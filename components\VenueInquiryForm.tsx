"use client"

import React, { useState } from 'react'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import {
  MessageCircle,
  Loader2,
  CheckCircle,
  AlertCircle,
  Users,
  MapPin,
  DollarSign,
  Clock,
  Building,
  Utensils,
  Music
} from 'lucide-react'
import { SearchDatePicker } from '@/components/ui/date-picker'
import { inquiryService, InquiryInput } from '@/lib/services/inquiryService'
import { showToast } from '@/lib/toast'
import { useAuth } from '@/contexts/AuthContext'
import { useSafeTranslation } from '@/hooks/use-safe-translation'

interface VenueInquiryFormProps {
  venueUserId: string
  venueId: string
  venueName: string
  venueType?: string
  venueCapacity?: string
  venueLocation?: string
}

export function VenueInquiryForm({ 
  venueUserId, 
  venueId, 
  venueName, 
  venueType, 
  venueCapacity,
  venueLocation 
}: VenueInquiryFormProps) {
  const { user } = useAuth()
  const { t } = useSafeTranslation()
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isSubmitted, setIsSubmitted] = useState(false)
  const [formData, setFormData] = useState({
    customerName: '',
    customerEmail: '',
    customerPhone: '',
    eventDate: '',
    eventTime: '',
    message: '',
    inquiryType: 'VENUE_INQUIRY' as const,
    budget: '',
    guestCount: '',
    eventType: '',
    cateringRequired: '',
    decorationRequired: '',
    musicRequired: '',
    additionalServices: [] as string[],
    preferredContactTime: ''
  })

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleServiceToggle = (service: string) => {
    setFormData(prev => ({
      ...prev,
      additionalServices: prev.additionalServices.includes(service)
        ? prev.additionalServices.filter(s => s !== service)
        : [...prev.additionalServices, service]
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    // Validation
    if (!formData.customerName.trim()) {
      showToast.error('Please enter your name')
      return
    }
    
    if (!formData.customerEmail.trim()) {
      showToast.error('Please enter your email')
      return
    }
    
    if (!formData.eventDate) {
      showToast.error('Please select your event date')
      return
    }
    
    if (!formData.guestCount.trim()) {
      showToast.error('Please enter the number of guests')
      return
    }
    
    if (!formData.message.trim()) {
      showToast.error('Please enter your message')
      return
    }

    setIsSubmitting(true)

    try {
      // Debug: Log form data before submission
      console.log('Venue inquiry form data before submission:', {
        formData,
        inquiryType: formData.inquiryType,
        inquiryTypeType: typeof formData.inquiryType
      });

      const inquiryData: InquiryInput = {
        vendorUserId: venueUserId,
        vendorId: venueId,
        vendorName: venueName,
        customerUserId: user?.userId || undefined,
        customerName: formData.customerName.trim(),
        customerEmail: formData.customerEmail.trim(),
        customerPhone: formData.customerPhone.trim() || undefined,
        eventDate: formData.eventDate || undefined,
        message: `Event Type: ${formData.eventType || 'Not specified'}\nEvent Time: ${formData.eventTime || 'Not specified'}\nCatering Required: ${formData.cateringRequired || 'Not specified'}\nDecoration Required: ${formData.decorationRequired || 'Not specified'}\nMusic/DJ Required: ${formData.musicRequired || 'Not specified'}\nAdditional Services: ${formData.additionalServices.length > 0 ? formData.additionalServices.join(', ') : 'None'}\n\nMessage:\n${formData.message.trim()}`,
        inquiryType: 'BOOKING_REQUEST',
        budget: formData.budget.trim() || undefined,
        guestCount: formData.guestCount.trim() || undefined,
        venue: venueLocation || venueName,
        additionalServices: formData.additionalServices,
        preferredContactTime: formData.preferredContactTime.trim() || undefined
      }

      console.log('Venue inquiry data to be submitted:', inquiryData);

      await inquiryService.createInquiry(inquiryData)
      
      setIsSubmitted(true)
      showToast.success('Venue inquiry sent successfully! The venue will contact you soon.')
      
      // Reset form after 3 seconds
      setTimeout(() => {
        setIsSubmitted(false)
        setFormData({
          customerName: '',
          customerEmail: '',
          customerPhone: '',
          eventDate: '',
          eventTime: '',
          message: '',
          inquiryType: 'VENUE_INQUIRY',
          budget: '',
          guestCount: '',
          eventType: '',
          cateringRequired: '',
          decorationRequired: '',
          musicRequired: '',
          additionalServices: [],
          preferredContactTime: ''
        })
      }, 3000)

    } catch (error) {
      console.error('Error submitting venue inquiry:', error)
      showToast.error('Failed to send inquiry. Please try again.')
    } finally {
      setIsSubmitting(false)
    }
  }

  if (isSubmitted) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center">
            <CheckCircle className="w-16 h-16 text-green-600 mx-auto mb-4" />
            <h3 className="text-xl font-semibold mb-2 text-green-800">Venue Inquiry Sent!</h3>
            <p className="text-gray-600 mb-4">
              Thank you for your inquiry. {venueName} will contact you soon to discuss availability and pricing.
            </p>
            <Badge className="bg-green-100 text-green-800">
              Response expected within 24 hours
            </Badge>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardContent className="p-6">
        <div className="flex items-center mb-4">
          <Building className="w-5 h-5 text-primary mr-2" />
          <h3 className="text-xl font-semibold">Check Availability & Send Inquiry</h3>
        </div>
        
        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Basic Information */}
          <div className="space-y-3">
            <Input
              placeholder="Your Name *"
              value={formData.customerName}
              onChange={(e) => handleInputChange('customerName', e.target.value)}
              required
              className="border-gray-300 focus:border-primary"
            />
            
            <Input
              placeholder="Your Email *"
              type="email"
              value={formData.customerEmail}
              onChange={(e) => handleInputChange('customerEmail', e.target.value)}
              required
              className="border-gray-300 focus:border-primary"
            />
            
            <Input
              placeholder="Your Phone"
              type="tel"
              value={formData.customerPhone}
              onChange={(e) => handleInputChange('customerPhone', e.target.value)}
              className="border-gray-300 focus:border-primary"
            />
          </div>

          {/* Event Details */}
          <div className="space-y-3">
          <SearchDatePicker
                date={formData.eventDate ? new Date(formData.eventDate) : undefined}
                onSelect={(date) => handleInputChange('eventDate', date ? date.toISOString().split('T')[0] : '')}
                placeholder={t('hero.datePlaceholder', 'Select Date')}
                disabled={(date) =>
                  date < new Date(new Date().setHours(0, 0, 0, 0))
                }
              />

            <div className="grid grid-cols-2 gap-3">
              <div className="relative">
                <Clock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  placeholder="Event Time"
                  type="time"
                  value={formData.eventTime}
                  onChange={(e) => handleInputChange('eventTime', e.target.value)}
                  className="pl-10 border-gray-300 focus:border-primary"
                />
              </div>
              
              <div className="relative">
                <Users className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  placeholder="Guest Count *"
                  value={formData.guestCount}
                  onChange={(e) => handleInputChange('guestCount', e.target.value)}
                  required
                  className="pl-10 border-gray-300 focus:border-primary"
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-3">
              <Select 
                value={formData.eventType} 
                onValueChange={(value) => handleInputChange('eventType', value)}
              >
                <SelectTrigger className="border-gray-300 focus:border-primary">
                  <SelectValue placeholder="Event Type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="wedding">Wedding</SelectItem>
                  <SelectItem value="engagement">Engagement</SelectItem>
                  <SelectItem value="reception">Reception</SelectItem>
                  <SelectItem value="birthday">Birthday Party</SelectItem>
                  <SelectItem value="anniversary">Anniversary</SelectItem>
                  <SelectItem value="corporate">Corporate Event</SelectItem>
                  <SelectItem value="conference">Conference</SelectItem>
                  <SelectItem value="other">Other</SelectItem>
                </SelectContent>
              </Select>
              
              <div className="relative">
                <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  placeholder="Budget Range"
                  value={formData.budget}
                  onChange={(e) => handleInputChange('budget', e.target.value)}
                  className="pl-10 border-gray-300 focus:border-primary"
                />
              </div>
            </div>
          </div>

          {/* Service Requirements */}
          <div className="space-y-3">
            <h4 className="font-medium text-gray-700">Service Requirements</h4>
            
            <div className="grid grid-cols-1 gap-3">
              <Select 
                value={formData.cateringRequired} 
                onValueChange={(value) => handleInputChange('cateringRequired', value)}
              >
                <SelectTrigger className="border-gray-300 focus:border-primary">
                  <Utensils className="w-4 h-4 mr-2" />
                  <SelectValue placeholder="Catering Required?" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="yes">Yes, catering required</SelectItem>
                  <SelectItem value="no">No, external catering</SelectItem>
                  <SelectItem value="partial">Partial catering</SelectItem>
                  <SelectItem value="discuss">Discuss with venue</SelectItem>
                </SelectContent>
              </Select>

              <Select 
                value={formData.decorationRequired} 
                onValueChange={(value) => handleInputChange('decorationRequired', value)}
              >
                <SelectTrigger className="border-gray-300 focus:border-primary">
                  <SelectValue placeholder="Decoration Required?" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="yes">Yes, decoration required</SelectItem>
                  <SelectItem value="no">No, external decoration</SelectItem>
                  <SelectItem value="partial">Basic decoration</SelectItem>
                  <SelectItem value="discuss">Discuss with venue</SelectItem>
                </SelectContent>
              </Select>

              <Select 
                value={formData.musicRequired} 
                onValueChange={(value) => handleInputChange('musicRequired', value)}
              >
                <SelectTrigger className="border-gray-300 focus:border-primary">
                  <Music className="w-4 h-4 mr-2" />
                  <SelectValue placeholder="Music/DJ Required?" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="yes">Yes, music/DJ required</SelectItem>
                  <SelectItem value="no">No, external music</SelectItem>
                  <SelectItem value="sound-only">Sound system only</SelectItem>
                  <SelectItem value="discuss">Discuss with venue</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Preferred Contact Time */}
          <div className="relative">
            <Clock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input
              placeholder="Preferred Contact Time (e.g., Morning, Evening)"
              value={formData.preferredContactTime}
              onChange={(e) => handleInputChange('preferredContactTime', e.target.value)}
              className="pl-10 border-gray-300 focus:border-primary"
            />
          </div>

          {/* Message */}
          <Textarea
            placeholder="Additional Requirements or Message *"
            value={formData.message}
            onChange={(e) => handleInputChange('message', e.target.value)}
            rows={4}
            required
            className="border-gray-300 focus:border-primary resize-none"
          />

          {/* Submit Button */}
          <Button 
            type="submit"
            className="w-full bg-primary hover:bg-primary/90 text-white font-semibold py-3"
            disabled={isSubmitting}
          >
            {isSubmitting ? (
              <>
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                Sending Inquiry...
              </>
            ) : (
              <>
                <Building className="w-4 h-4 mr-2" />
                Check Availability & Send Inquiry
              </>
            )}
          </Button>

          {/* Help Text */}
          <div className="flex items-start gap-2 text-sm text-gray-600 bg-blue-50 p-3 rounded-lg">
            <AlertCircle className="w-4 h-4 text-blue-600 mt-0.5 flex-shrink-0" />
            <div>
              <p className="font-medium text-blue-800">Quick Response Guaranteed</p>
              <p>The venue typically responds within 2-4 hours with availability and pricing details.</p>
            </div>
          </div>
        </form>
      </CardContent>
    </Card>
  )
}

export default VenueInquiryForm
