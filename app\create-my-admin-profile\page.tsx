'use client';

import React, { useState, useEffect } from 'react';
import { TopHeader } from '@/components/top-header';
import { Header } from '@/components/header';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  Shield, 
  Crown, 
  User,
  CheckCircle,
  XCircle,
  AlertCircle
} from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { generateClient } from 'aws-amplify/api';
import { createUserProfile, updateUserProfile } from '@/src/graphql/mutations';
import { userProfilesByUserId } from '@/src/graphql/queries';

const client = generateClient();

export default function CreateMyAdminProfilePage() {
  const { user, isAuthenticated } = useAuth();
  const [loading, setLoading] = useState(false);
  const [existingProfile, setExistingProfile] = useState<any>(null);
  const [message, setMessage] = useState<{ type: 'success' | 'error' | 'info'; text: string } | null>(null);
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    adminLevel: 'ADMIN' as 'ADMIN' | 'SUPER_ADMIN'
  });

  useEffect(() => {
    if (isAuthenticated && user) {
      checkExistingProfile();
      // Pre-fill form with user data if available
      setFormData(prev => ({
        ...prev,
        email: user.signInDetails?.loginId || ''
      }));
    }
  }, [isAuthenticated, user]);

  const checkExistingProfile = async () => {
    if (!user) return;

    try {
      const result = await client.graphql({
        query: userProfilesByUserId,
        variables: {
          userId: user.userId,
          limit: 1
        }
      });

      const profiles = result.data?.userProfilesByUserId?.items || [];
      if (profiles.length > 0) {
        setExistingProfile(profiles[0]);
        setFormData(prev => ({
          ...prev,
          firstName: profiles[0].firstName || '',
          lastName: profiles[0].lastName || '',
          email: profiles[0].email || '',
          phone: profiles[0].phone || ''
        }));

        if (profiles[0].isAdmin || profiles[0].isSuperAdmin) {
          setMessage({
            type: 'info',
            text: `You already have ${profiles[0].isSuperAdmin ? 'Super Admin' : 'Admin'} privileges!`
          });
        }
      }
    } catch (error) {
      console.error('Error checking existing profile:', error);
    }
  };

  const createAdminProfile = async () => {
    if (!user) {
      setMessage({ type: 'error', text: 'Please log in first' });
      return;
    }

    if (!formData.firstName.trim() || !formData.lastName.trim() || !formData.email.trim()) {
      setMessage({ type: 'error', text: 'Please fill in all required fields' });
      return;
    }

    setLoading(true);
    setMessage(null);

    try {
      const adminPermissions = formData.adminLevel === 'SUPER_ADMIN' 
        ? ['reviews:read', 'reviews:moderate', 'reviews:delete', 'users:read', 'users:update', 'users:delete', 'users:create_admin', 'system:manage', 'permissions:manage']
        : ['reviews:read', 'reviews:moderate', 'users:read', 'vendors:read', 'shops:read', 'venues:read'];

      if (existingProfile) {
        // Update existing profile
        const updateInput = {
          id: existingProfile.id,
          isAdmin: true,
          isSuperAdmin: formData.adminLevel === 'SUPER_ADMIN',
          role: formData.adminLevel,
          permissions: adminPermissions,
          firstName: formData.firstName,
          lastName: formData.lastName,
          email: formData.email,
          phone: formData.phone || null
        };

        const result = await client.graphql({
          query: updateUserProfile,
          variables: {
            input: updateInput
          }
        });

        setMessage({
          type: 'success',
          text: `Successfully updated your profile with ${formData.adminLevel} privileges!`
        });
        
        setExistingProfile(result.data?.updateUserProfile);
      } else {
        // Create new profile
        const createInput = {
          userId: user.userId,
          firstName: formData.firstName,
          lastName: formData.lastName,
          email: formData.email,
          phone: formData.phone || null,
          isAdmin: true,
          isSuperAdmin: formData.adminLevel === 'SUPER_ADMIN',
          role: formData.adminLevel,
          permissions: adminPermissions,
          isVerified: true,
          accountType: 'ADMIN_ACCOUNT',
          registrationSource: 'ADMIN_FORM'
        };

        const result = await client.graphql({
          query: createUserProfile,
          variables: {
            input: createInput
          }
        });

        setMessage({
          type: 'success',
          text: `Successfully created your ${formData.adminLevel} profile!`
        });
        
        setExistingProfile(result.data?.createUserProfile);
      }
    } catch (error: any) {
      console.error('Error creating/updating admin profile:', error);
      setMessage({
        type: 'error',
        text: error.message || 'Failed to create admin profile'
      });
    } finally {
      setLoading(false);
    }
  };

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <Card className="w-96">
          <CardContent className="p-6 text-center">
            <User className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h2 className="text-xl font-semibold mb-2">Please Log In</h2>
            <p className="text-gray-600 mb-4">You need to be logged in to create your admin profile.</p>
            <Button asChild>
              <a href="/login">Go to Login</a>
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <TopHeader />
      <Header />
      
      <div className="container mx-auto px-4 py-8 max-w-2xl">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Create My Admin Profile</h1>
          <p className="text-gray-600">Create or update your user profile with admin privileges</p>
        </div>

        {/* Current User Info */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle>Current User Information</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 text-sm">
              <p><strong>User ID:</strong> {user?.userId}</p>
              <p><strong>Login ID:</strong> {user?.signInDetails?.loginId}</p>
              <p><strong>Profile Status:</strong> {existingProfile ? 'Profile exists' : 'No profile yet'}</p>
              {existingProfile && (
                <p><strong>Current Role:</strong> {existingProfile.role || 'CUSTOMER'}</p>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Message */}
        {message && (
          <div className={`mb-6 p-4 rounded-lg ${
            message.type === 'success' 
              ? 'bg-green-50 border border-green-200 text-green-800'
              : message.type === 'error'
              ? 'bg-red-50 border border-red-200 text-red-800'
              : 'bg-blue-50 border border-blue-200 text-blue-800'
          }`}>
            <div className="flex items-center">
              {message.type === 'success' ? (
                <CheckCircle className="h-5 w-5 mr-2" />
              ) : message.type === 'error' ? (
                <XCircle className="h-5 w-5 mr-2" />
              ) : (
                <AlertCircle className="h-5 w-5 mr-2" />
              )}
              {message.text}
            </div>
          </div>
        )}

        {/* Admin Profile Form */}
        <Card>
          <CardHeader>
            <CardTitle>
              {existingProfile ? 'Update Admin Profile' : 'Create Admin Profile'}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="firstName">First Name *</Label>
                <Input
                  id="firstName"
                  value={formData.firstName}
                  onChange={(e) => setFormData(prev => ({ ...prev, firstName: e.target.value }))}
                  placeholder="Enter your first name"
                />
              </div>
              
              <div>
                <Label htmlFor="lastName">Last Name *</Label>
                <Input
                  id="lastName"
                  value={formData.lastName}
                  onChange={(e) => setFormData(prev => ({ ...prev, lastName: e.target.value }))}
                  placeholder="Enter your last name"
                />
              </div>
            </div>
            
            <div>
              <Label htmlFor="email">Email *</Label>
              <Input
                id="email"
                type="email"
                value={formData.email}
                onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
                placeholder="Enter your email"
              />
            </div>
            
            <div>
              <Label htmlFor="phone">Phone</Label>
              <Input
                id="phone"
                value={formData.phone}
                onChange={(e) => setFormData(prev => ({ ...prev, phone: e.target.value }))}
                placeholder="Enter your phone number (optional)"
              />
            </div>
            
            <div>
              <Label htmlFor="adminLevel">Admin Level</Label>
              <Select 
                value={formData.adminLevel} 
                onValueChange={(value: 'ADMIN' | 'SUPER_ADMIN') => 
                  setFormData(prev => ({ ...prev, adminLevel: value }))
                }
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="ADMIN">
                    <div className="flex items-center">
                      <Shield className="h-4 w-4 mr-2 text-blue-600" />
                      Admin
                    </div>
                  </SelectItem>
                  <SelectItem value="SUPER_ADMIN">
                    <div className="flex items-center">
                      <Crown className="h-4 w-4 mr-2 text-purple-600" />
                      Super Admin
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>

            <Button 
              onClick={createAdminProfile}
              disabled={loading}
              className="w-full bg-blue-600 hover:bg-blue-700"
            >
              {loading 
                ? 'Processing...' 
                : existingProfile 
                  ? 'Update Admin Profile' 
                  : 'Create Admin Profile'
              }
            </Button>
          </CardContent>
        </Card>

        {/* Success Actions */}
        {message?.type === 'success' && (
          <Card className="mt-6 border-green-200 bg-green-50">
            <CardContent className="p-4">
              <div className="text-green-800">
                <p className="font-semibold mb-2">✅ Admin Profile Created Successfully!</p>
                <p className="text-sm mb-2">You can now access:</p>
                <div className="flex flex-wrap gap-2">
                  <Button size="sm" asChild>
                    <a href="/admin">Admin Dashboard</a>
                  </Button>
                  <Button size="sm" variant="outline" asChild>
                    <a href="/dashboard/admin-reviews">Review Management</a>
                  </Button>
                  <Button size="sm" variant="outline" asChild>
                    <a href="/admin/users">User Management</a>
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Instructions */}
        <Card className="mt-6">
          <CardHeader>
            <CardTitle>How This Works</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 text-sm">
              <p>• This creates/updates YOUR user profile with admin privileges</p>
              <p>• You can only create admin profiles for yourself (AWS authorization)</p>
              <p>• Once created, you'll have access to admin dashboards</p>
              <p>• Required fields: First Name, Last Name, Email</p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
