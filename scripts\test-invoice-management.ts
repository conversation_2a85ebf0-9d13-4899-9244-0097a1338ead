#!/usr/bin/env ts-node

/**
 * Comprehensive Invoice Management Test Script
 * Tests all invoice types: shopping orders, vendor bookings, venue bookings, subscriptions
 */

import { invoiceService } from '../lib/services/invoiceService';

async function testInvoiceManagement() {
  console.log('🧪 Testing Comprehensive Invoice Management...\n');

  try {
    // Test 1: Shopping Order Invoice
    console.log('📦 Test 1: Shopping Order Invoice');
    const shoppingOrderResult = await invoiceService.generateShoppingOrderInvoice({
      orderId: 'ORD_001',
      customerId: 'customer_123',
      customerName: 'Priya Sharma',
      customerEmail: '<EMAIL>',
      customerPhone: '+91 **********',
      customerAddress: {
        street: '123 Wedding Lane',
        city: 'Chennai',
        state: 'Tamil Nadu',
        pincode: '600001',
        country: 'India'
      },
      items: [
        {
          productId: 'prod_001',
          productName: 'Wedding Saree',
          description: 'Traditional silk wedding saree',
          quantity: 1,
          unitPrice: 15000,
          taxRate: 18
        },
        {
          productId: 'prod_002',
          productName: 'Wedding Jewelry Set',
          description: 'Gold plated jewelry set',
          quantity: 1,
          unitPrice: 8000,
          taxRate: 18
        }
      ],
      subtotal: 23000,
      taxAmount: 4140,
      discountAmount: 1000,
      totalAmount: 26140,
      paymentStatus: 'paid',
      paymentMethod: 'UPI',
      transactionId: 'TXN_001',
      shippingAddress: {
        street: '123 Wedding Lane',
        city: 'Chennai',
        state: 'Tamil Nadu',
        pincode: '600001',
        country: 'India'
      }
    });

    console.log('Shopping Order Invoice Result:', shoppingOrderResult);
    console.log('✅ Shopping Order Invoice Test Complete\n');

    // Test 2: Vendor Service Invoice
    console.log('🎭 Test 2: Vendor Service Invoice');
    const vendorServiceResult = await invoiceService.generateVendorServiceInvoice({
      bookingId: 'VND_001',
      customerId: 'customer_123',
      customerName: 'Priya Sharma',
      customerEmail: '<EMAIL>',
      customerPhone: '+91 **********',
      customerAddress: {
        street: '123 Wedding Lane',
        city: 'Chennai',
        state: 'Tamil Nadu',
        pincode: '600001',
        country: 'India'
      },
      vendorId: 'vendor_456',
      vendorName: 'Dream Photographers',
      vendorEmail: '<EMAIL>',
      vendorBusinessName: 'Dream Photographers Studio',
      vendorGstNumber: 'GST123456789',
      vendorAddress: {
        street: '456 Photography Street',
        city: 'Chennai',
        state: 'Tamil Nadu',
        pincode: '600002',
        country: 'India'
      },
      serviceName: 'Wedding Photography Package',
      serviceDescription: 'Full day wedding photography with 500+ edited photos',
      eventDate: '2025-03-15',
      eventTime: '09:00 AM',
      eventLocation: 'Grand Palace, Chennai',
      amount: 25000,
      taxAmount: 4500,
      discountAmount: 2000,
      totalAmount: 27500,
      paymentStatus: 'pending',
      paymentMethod: 'Bank Transfer',
      transactionId: 'TXN_002',
      terms: '50% advance payment required'
    });

    console.log('Vendor Service Invoice Result:', vendorServiceResult);
    console.log('✅ Vendor Service Invoice Test Complete\n');

    // Test 3: Venue Booking Invoice
    console.log('🏰 Test 3: Venue Booking Invoice');
    const venueBookingResult = await invoiceService.generateVenueBookingInvoice({
      bookingId: 'VEN_001',
      customerId: 'customer_123',
      customerName: 'Priya Sharma',
      customerEmail: '<EMAIL>',
      customerPhone: '+91 **********',
      customerAddress: {
        street: '123 Wedding Lane',
        city: 'Chennai',
        state: 'Tamil Nadu',
        pincode: '600001',
        country: 'India'
      },
      venueId: 'venue_789',
      venueName: 'Grand Palace',
      venueEmail: '<EMAIL>',
      venueAddress: {
        street: '789 Palace Road',
        city: 'Chennai',
        state: 'Tamil Nadu',
        pincode: '600003',
        country: 'India'
      },
      packageName: 'Premium Wedding Package',
      packageDescription: 'Luxury wedding venue with catering and decoration',
      eventDate: '2025-03-15',
      eventTime: '06:00 PM',
      eventDuration: '8 hours',
      capacity: '200 guests',
      amount: 50000,
      taxAmount: 9000,
      discountAmount: 5000,
      totalAmount: 54000,
      paymentStatus: 'pending',
      paymentMethod: 'Cheque',
      transactionId: 'TXN_003',
      terms: '30% advance payment required'
    });

    console.log('Venue Booking Invoice Result:', venueBookingResult);
    console.log('✅ Venue Booking Invoice Test Complete\n');

    // Test 4: Subscription Invoice
    console.log('💳 Test 4: Subscription Invoice');
    const subscriptionResult = await invoiceService.generateSubscriptionInvoice({
      subscriptionId: 'SUB_001',
      vendorId: 'vendor_456',
      vendorName: 'Dream Photographers',
      vendorEmail: '<EMAIL>',
      planName: 'Premium Vendor Plan',
      planPrice: 5000,
      billingPeriod: 'Monthly',
      paymentStatus: 'paid',
      paymentMethod: 'Credit Card',
      transactionId: 'TXN_004'
    });

    console.log('Subscription Invoice Result:', subscriptionResult);
    console.log('✅ Subscription Invoice Test Complete\n');

    // Test 5: Get Invoice Statistics
    console.log('📊 Test 5: Invoice Statistics');
    const stats = await invoiceService.getInvoiceStatistics();
    console.log('Invoice Statistics:', stats);
    console.log('✅ Invoice Statistics Test Complete\n');

    // Test 6: Get Invoices for Admin
    console.log('👨‍💼 Test 6: Admin Invoice List');
    const adminInvoices = await invoiceService.getInvoicesForAdmin(10);
    console.log('Admin Invoices:', adminInvoices);
    console.log('✅ Admin Invoice List Test Complete\n');

    // Test 7: Get Invoices for Customer
    console.log('👤 Test 7: Customer Invoice List');
    const customerInvoices = await invoiceService.getInvoicesForUser('customer_123', 'customer');
    console.log('Customer Invoices:', customerInvoices);
    console.log('✅ Customer Invoice List Test Complete\n');

    // Test 8: Get Invoices for Vendor
    console.log('🏢 Test 8: Vendor Invoice List');
    const vendorInvoices = await invoiceService.getInvoicesForUser('vendor_456', 'vendor');
    console.log('Vendor Invoices:', vendorInvoices);
    console.log('✅ Vendor Invoice List Test Complete\n');

    console.log('🎉 All Invoice Management Tests Completed Successfully!');
    console.log('\n📋 Summary:');
    console.log('- ✅ Shopping Order Invoices');
    console.log('- ✅ Vendor Service Invoices');
    console.log('- ✅ Venue Booking Invoices');
    console.log('- ✅ Subscription Invoices');
    console.log('- ✅ Invoice Statistics');
    console.log('- ✅ Admin Dashboard');
    console.log('- ✅ Customer Dashboard');
    console.log('- ✅ Vendor Dashboard');

  } catch (error) {
    console.error('❌ Error in invoice management tests:', error);
  }
}

// Run the comprehensive test
testInvoiceManagement().catch(console.error); 