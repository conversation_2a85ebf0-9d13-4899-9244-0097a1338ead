'use client'

import { useState, useEffect } from 'react'
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { 
  Plus, 
  Search, 
  Edit, 
  Trash2, 
  Eye, 
  Heart, 
  MessageSquare, 
  Calendar,
  Filter,
  MoreHorizontal,
  Pin,
  Star,
  FileText,
  TrendingUp
} from 'lucide-react'
import { BlogService, BLOG_CATEGORIES, BlogStatus, BlogCategory, AuthorType } from '@/lib/services/blogService'
import { useAuth } from '@/contexts/AuthContext'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { showToast } from '@/lib/toast'

interface Blog {
  id: string;
  title: string;
  content: string;
  excerpt?: string;
  category: BlogCategory;
  authorId: string;
  authorName: string;
  authorType: AuthorType;
  featuredImage?: string;
  tags?: string[];
  status: BlogStatus;
  views?: number;
  likes?: number;
  comments?: number;
  isPinned?: boolean;
  isFeatured?: boolean;
  publishedAt?: string;
  createdAt: string;
  updatedAt: string;
}

export default function BlogsDashboard() {
  const { isAuthenticated } = useAuth()
  const router = useRouter()
  const [blogs, setBlogs] = useState<Blog[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState<BlogStatus | 'ALL'>('ALL')
  const [categoryFilter, setCategoryFilter] = useState<BlogCategory | 'ALL'>('ALL')

  useEffect(() => {
    if (isAuthenticated) {
      loadUserBlogs()
    }
  }, [isAuthenticated])

  const loadUserBlogs = async () => {
    try {
      setLoading(true)
      const result = await BlogService.getCurrentUserBlogs()
      setBlogs(result.blogs)
    } catch (error) {
      console.error('Error loading blogs:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleDeleteBlog = async (blogId: string) => {
    if (window.confirm('Are you sure you want to delete this blog post?')) {
      try {
        await BlogService.deleteBlog(blogId)
        setBlogs(blogs.filter(blog => blog.id !== blogId))
      } catch (error) {
        console.error('Error deleting blog:', error)
        showToast.error('Failed to delete blog post')
      }
    }
  }

  const handleTogglePin = async (blog: Blog) => {
    try {
      await BlogService.updateBlog({
        id: blog.id,
        isPinned: !blog.isPinned
      })
      setBlogs(blogs.map(b => 
        b.id === blog.id ? { ...b, isPinned: !b.isPinned } : b
      ))
    } catch (error) {
      console.error('Error updating blog:', error)
    }
  }

  const handleToggleFeatured = async (blog: Blog) => {
    try {
      await BlogService.updateBlog({
        id: blog.id,
        isFeatured: !blog.isFeatured
      })
      setBlogs(blogs.map(b => 
        b.id === blog.id ? { ...b, isFeatured: !b.isFeatured } : b
      ))
    } catch (error) {
      console.error('Error updating blog:', error)
    }
  }

  const filteredBlogs = blogs.filter(blog => {
    const matchesSearch = blog.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         blog.content.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = statusFilter === 'ALL' || blog.status === statusFilter
    const matchesCategory = categoryFilter === 'ALL' || blog.category === categoryFilter
    
    return matchesSearch && matchesStatus && matchesCategory
  })

  const getStatusColor = (status: BlogStatus) => {
    switch (status) {
      case BlogStatus.PUBLISHED:
        return 'bg-green-100 text-green-800'
      case BlogStatus.DRAFT:
        return 'bg-yellow-100 text-yellow-800'
      case BlogStatus.ARCHIVED:
        return 'bg-gray-100 text-gray-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  if (!isAuthenticated) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card>
          <CardContent className="p-6 text-center">
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Authentication Required</h3>
            <p className="text-gray-600">Please log in to manage your blog posts.</p>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 mb-1">Manage Blogs</h1>
          <p className="text-gray-500 text-base">{/* Add dynamic count here if available */}</p>
        </div>
        <div className="flex gap-2">
          <Button onClick={() => router.push(`/dashboard/blogs/create`)} className="bg-primary hover:bg-primary/90">+ Add Blog</Button>
        </div>
      </div>
      <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-4 mb-6">
        {/* Add search/filter controls if needed */}
      </div>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredBlogs.map((blog) => (
          <Card key={blog.id} className="overflow-hidden rounded-2xl shadow border border-gray-100 bg-white hover:shadow-md transition-shadow">
            <CardContent className="p-6 flex flex-col h-full">
              <div className="flex items-start justify-between mb-2">
                <div className="flex-1">
                  <h2 className="font-bold text-xl text-gray-900 mb-1 line-clamp-1">{blog.title}</h2>
                  <p className="text-primary font-medium text-xs uppercase tracking-wide mb-1">{blog.category}</p>
                </div>
                {/* Add status badges or action buttons here */}
              </div>
              <div className="text-gray-500 text-sm mb-2">{blog.authorName}</div>
              <p className="text-sm text-gray-600 mb-3 line-clamp-2">{blog.excerpt}</p>
              <div className="flex gap-2 mt-auto">
                <Button variant="outline" size="sm" className="flex-1" onClick={() => router.push(`/dashboard/blogs/edit/${blog.id}`)}>Edit</Button>
                <Button variant="outline" size="sm" className="flex-1" onClick={() => handleDeleteBlog(blog.id)}>Delete</Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  )
}
