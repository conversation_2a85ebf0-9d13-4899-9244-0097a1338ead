import { generateClient } from 'aws-amplify/api';
import { getCurrentUser } from 'aws-amplify/auth';
import { createVenue, updateVenue, deleteVenue } from '@/src/graphql/mutations';
import { listVenues, getVenue, venuesByUserId } from '@/src/graphql/queries';
import { onCreateVenue, onUpdateVenue, onDeleteVenue } from '@/src/graphql/subscriptions';
import { handleVenueOperation } from '@/lib/dynamodb-error-handler';

// Create client with user pool authentication for authenticated operations
const authenticatedClient = generateClient({
  authMode: 'userPool'
});

// Create client with API key for public operations
const publicClient = generateClient({
  authMode: 'apiKey'
});

// TypeScript interfaces
export interface VenueSpace {
  name: string;
  capacity: string;
  area?: string;
  price: string;
  description?: string;
  amenities?: string[];
  images?: string[];
}

export interface VenuePackage {
  name: string;
  price: string;
  duration?: string;
  description?: string;
  includes?: string[];
  excludes?: string[];
  terms?: string;
}

export interface VenuePolicies {
  cancellation?: string;
  advance?: string;
  catering?: string;
  decoration?: string;
  alcohol?: string;
  music?: string;
  parking?: string;
}

export interface VenueCoordinates {
  latitude?: number;
  longitude?: number;
}

export interface VenueOperatingHours {
  monday?: string;
  tuesday?: string;
  wednesday?: string;
  thursday?: string;
  friday?: string;
  saturday?: string;
  sunday?: string;
}

export interface SocialMedia {
  facebook?: string;
  instagram?: string;
  youtube?: string;
}

export interface VenueData {
  id?: string;
  name: string;
  type: string;
  capacity: string;
  location: string;
  city: string;
  state: string;
  fullAddress?: string;
  pincode?: string;
  contactPhone?: string;
  contactEmail?: string;
  website?: string;
  price: string;
  priceRange?: string;
  description?: string;
  images?: string[];
  amenities?: string[];
  spaces?: VenueSpace[];
  packages?: VenuePackage[];
  socialMedia?: SocialMedia;
  rating?: number;
  reviewCount?: number;
  bookings?: number;
  verified?: boolean;
  featured?: boolean;
  status: string;
  availability?: string;
  policies?: VenuePolicies;
  coordinates?: VenueCoordinates;
  operatingHours?: VenueOperatingHours;
}

export interface VenueResponse {
  id: string;
  userId: string;
  name: string;
  type: string;
  capacity: string;
  location: string;
  city: string;
  state: string;
  fullAddress?: string;
  pincode?: string;
  contactPhone?: string;
  contactEmail?: string;
  website?: string;
  price: string;
  priceRange?: string;
  description?: string;
  images?: string[];
  amenities?: string[];
  spaces?: VenueSpace[];
  packages?: VenuePackage[];
  socialMedia?: SocialMedia;
  rating?: number;
  reviewCount?: number;
  bookings?: number;
  verified?: boolean;
  featured?: boolean;
  status?: string;
  availability?: string;
  policies?: VenuePolicies;
  coordinates?: VenueCoordinates;
  operatingHours?: VenueOperatingHours;
  createdAt: string;
  updatedAt: string;
}

class VenueService {
  /**
   * Get current user ID
   */
  private async getCurrentUserId(): Promise<string> {
    try {
      const user = await getCurrentUser();
      return user.userId;
    } catch (error) {
      throw new Error('User not authenticated');
    }
  }

  /**
   * Create a new venue
   */
  async createVenue(venueData: VenueData): Promise<VenueResponse> {
    let input: any = null;

    try {
      const userId = await this.getCurrentUserId();

      input = {
        userId,
        name: venueData.name,
        type: venueData.type,
        capacity: venueData.capacity,
        location: venueData.location,
        city: venueData.city,
        state: venueData.state,
        fullAddress: venueData.fullAddress,
        pincode: venueData.pincode,
        contactPhone: venueData.contactPhone,
        contactEmail: venueData.contactEmail,
        website: venueData.website,
        price: venueData.price,
        priceRange: venueData.priceRange,
        description: venueData.description,
        images: venueData.images || [],
        amenities: venueData.amenities || [],
        spaces: venueData.spaces || [],
        packages: venueData.packages || [],
        socialMedia: venueData.socialMedia,
        rating: venueData.rating,
        reviewCount: venueData.reviewCount,
        bookings: venueData.bookings,
        verified: venueData.verified || false,
        featured: venueData.featured || false,
        status: venueData.status || 'active',
        availability: venueData.availability,
        policies: venueData.policies,
        coordinates: venueData.coordinates,
        operatingHours: venueData.operatingHours
      };

      console.log('Creating venue with input:', JSON.stringify(input, null, 2));

      return await handleVenueOperation(async (data) => {
        const result = await authenticatedClient.graphql({
          query: createVenue,
          variables: { input: data }
        });
        return result.data.createVenue;
      }, input);
    } catch (error) {
      console.error('Error creating venue:', error);
      if (input) {
        console.error('Input that caused error:', JSON.stringify(input, null, 2));
      }
      throw new Error('Failed to create venue');
    }
  }

  /**
   * Update an existing venue
   */
  async updateVenue(venueId: string, venueData: Partial<VenueData>): Promise<VenueResponse> {
    let input: any = null;

    try {
      const userId = await this.getCurrentUserId();

      // First verify that the venue belongs to the current user
      const existingVenue = await this.getVenue(venueId);
      if (existingVenue.userId !== userId) {
        throw new Error('Unauthorized: You can only update your own venues');
      }

      input = {
        id: venueId,
        ...venueData
      };

      console.log('Updating venue with input:', JSON.stringify(input, null, 2));

      return await handleVenueOperation(async (data) => {
        const result = await authenticatedClient.graphql({
          query: updateVenue,
          variables: { input: data }
        });
        return result.data.updateVenue;
      }, input);
    } catch (error) {
      console.error('Error updating venue:', error);
      if (input) {
        console.error('Input that caused error:', JSON.stringify(input, null, 2));
      }
      throw new Error('Failed to update venue');
    }
  }

  /**
   * Delete a venue
   */
  async deleteVenue(venueId: string): Promise<boolean> {
    try {
      const userId = await this.getCurrentUserId();
      
      // First verify that the venue belongs to the current user
      const existingVenue = await this.getVenue(venueId);
      if (existingVenue.userId !== userId) {
        throw new Error('Unauthorized: You can only delete your own venues');
      }

      await authenticatedClient.graphql({
        query: deleteVenue,
        variables: { input: { id: venueId } }
      });

      return true;
    } catch (error) {
      console.error('Error deleting venue:', error);
      throw new Error('Failed to delete venue');
    }
  }

  /**
   * Get a specific venue by ID
   */
  async getVenue(venueId: string): Promise<VenueResponse> {
    try {
      const result = await publicClient.graphql({
        query: getVenue,
        variables: { id: venueId }
      });

      return result.data.getVenue;
    } catch (error) {
      console.error('Error fetching venue:', error);
      throw new Error('Failed to fetch venue');
    }
  }

  /**
   * Get all venues for the current user
   */
  async getUserVenues(): Promise<VenueResponse[]> {
    try {
      const userId = await this.getCurrentUserId();

      const result = await authenticatedClient.graphql({
        query: venuesByUserId,
        variables: {
          userId: userId
        }
      });

      return result.data.venuesByUserId.items;
    } catch (error) {
      console.error('Error fetching user venues:', error);
      throw new Error('Failed to fetch venues');
    }
  }

  /**
   * Get all venues (public - for browsing)
   */
  async getAllVenues(limit: number = 10, nextToken?: string, dateFilter?: string): Promise<{
    venues: VenueResponse[];
    nextToken?: string;
  }> {
    try {
      const result = await publicClient.graphql({
        query: listVenues,
        variables: {
          limit,
          nextToken,
          filter: {
            status: { eq: 'active' }
          }
        }
      });

      let venues = result.data.listVenues.items;

      // Client-side date filtering if dateFilter is provided
      // In a real implementation, this would be done server-side with proper date availability fields
      if (dateFilter) {
        venues = venues.filter(venue => {
          // For now, we assume all venues are available unless they have specific unavailable dates
          // This logic can be enhanced when proper date availability fields are added to the schema
          return true; // Default to available
        });
      }

      return {
        venues,
        nextToken: result.data.listVenues.nextToken
      };
    } catch (error) {
      console.error('Error fetching all venues:', error);
      throw new Error('Failed to fetch venues');
    }
  }

  /**
   * List venues with pagination
   */
  async listVenues(filter?: any, limit: number = 10, nextToken?: string): Promise<{
    items: VenueResponse[];
    nextToken?: string;
  }> {
    try {
      const result = await publicClient.graphql({
        query: listVenues,
        variables: { filter, limit, nextToken }
      });
      return result.data.listVenues;
    } catch (error) {
      console.error('Error listing venues:', error);
      throw error;
    }
  }

  /**
   * Search venues by type
   */
  async getVenuesByType(type: string): Promise<VenueResponse[]> {
    try {
      const result = await publicClient.graphql({
        query: listVenues,
        variables: {
          filter: {
            type: { eq: type },
            status: { eq: 'active' }
          }
        }
      });

      return result.data.listVenues.items;
    } catch (error) {
      console.error('Error fetching venues by type:', error);
      throw new Error('Failed to fetch venues by type');
    }
  }

  /**
   * Search venues by city
   */
  async getVenuesByCity(city: string): Promise<VenueResponse[]> {
    try {
      const result = await publicClient.graphql({
        query: listVenues,
        variables: {
          filter: {
            city: { eq: city },
            status: { eq: 'active' }
          }
        }
      });

      return result.data.listVenues.items;
    } catch (error) {
      console.error('Error fetching venues by city:', error);
      throw new Error('Failed to fetch venues by city');
    }
  }

  /**
   * Bulk delete venues
   */
  async bulkDeleteVenues(venueIds: string[]): Promise<void> {
    try {
      const deletePromises = venueIds.map(id => this.deleteVenue(id));
      await Promise.all(deletePromises);
    } catch (error) {
      console.error('Error bulk deleting venues:', error);
      throw new Error('Failed to bulk delete venues');
    }
  }

  /**
   * Update venue status
   */
  async updateVenueStatus(venueId: string, status: string): Promise<VenueResponse> {
    try {
      return await this.updateVenue(venueId, { status });
    } catch (error) {
      console.error('Error updating venue status:', error);
      throw new Error('Failed to update venue status');
    }
  }

  /**
   * Get venues by user ID (for admin or specific user lookup)
   */
  async getVenuesByUserId(userId?: string, filter?: any, limit?: number, nextToken?: string): Promise<{ items: VenueResponse[], nextToken?: string }> {
    try {
      let userIdToUse = userId;

      if (!userIdToUse) {
        userIdToUse = await this.getCurrentUserId();
      }

      const result = await authenticatedClient.graphql({
        query: venuesByUserId,
        variables: {
          userId: userIdToUse,
          filter,
          limit,
          nextToken
        }
      });

      return result.data.venuesByUserId;
    } catch (error) {
      console.error('Error fetching venues by user ID:', error);
      throw new Error('Failed to fetch venues by user ID');
    }
  }

  /**
   * Subscribe to venue creation events
   */
  subscribeToVenueCreation(callback: (venue: VenueResponse) => void) {
    try {
      const subscription = authenticatedClient.graphql({
        query: onCreateVenue
      }).subscribe({
        next: ({ data }) => {
          if (data.onCreateVenue) {
            callback(data.onCreateVenue);
          }
        },
        error: (error) => {
          console.error('Venue creation subscription error:', error);
        }
      });

      return subscription;
    } catch (error) {
      console.error('Error setting up venue creation subscription:', error);
      throw new Error('Failed to subscribe to venue creation');
    }
  }

  /**
   * Subscribe to venue update events
   */
  subscribeToVenueUpdates(callback: (venue: VenueResponse) => void) {
    try {
      const subscription = authenticatedClient.graphql({
        query: onUpdateVenue
      }).subscribe({
        next: ({ data }) => {
          if (data.onUpdateVenue) {
            callback(data.onUpdateVenue);
          }
        },
        error: (error) => {
          console.error('Venue update subscription error:', error);
        }
      });

      return subscription;
    } catch (error) {
      console.error('Error setting up venue update subscription:', error);
      throw new Error('Failed to subscribe to venue updates');
    }
  }

  /**
   * Subscribe to venue deletion events
   */
  subscribeToVenueDeletion(callback: (venue: VenueResponse) => void) {
    try {
      const subscription = authenticatedClient.graphql({
        query: onDeleteVenue
      }).subscribe({
        next: ({ data }) => {
          if (data.onDeleteVenue) {
            callback(data.onDeleteVenue);
          }
        },
        error: (error) => {
          console.error('Venue deletion subscription error:', error);
        }
      });

      return subscription;
    } catch (error) {
      console.error('Error setting up venue deletion subscription:', error);
      throw new Error('Failed to subscribe to venue deletion');
    }
  }
}

export const venueService = new VenueService();

// Legacy exports for backward compatibility
export { venueService as default };
