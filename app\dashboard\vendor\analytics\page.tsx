'use client'
import { useState, useEffect } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { useRouter } from 'next/navigation'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { Button } from '@/components/ui/button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { 
  BarChart3, 
  TrendingUp, 
  Users, 
  Eye, 
  Phone, 
  Mail, 
  Calendar,
  MapPin,
  Star,
  RefreshCw,
  Download
} from 'lucide-react'

export default function VendorAnalyticsPage() {
  const { user, isAuthenticated, isLoading } = useAuth()
  const router = useRouter()
  
  const [analytics, setAnalytics] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const [dateRange, setDateRange] = useState('30d')
  const [refreshing, setRefreshing] = useState(false)

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push('/login')
    }
  }, [isAuthenticated, isLoading, router])

  useEffect(() => {
    if (user?.userId) {
      fetchAnalytics()
    }
  }, [user?.userId, dateRange])

  const fetchAnalytics = async () => {
    setLoading(true)
    try {
      const [vendorData, gaData, gmbData, gscData] = await Promise.all([
        fetch(`/api/vendor-analytics/${user.userId}?range=${dateRange}`).then(r => r.json()),
        fetch(`/api/google-analytics/${user.userId}?range=${dateRange}`).then(r => r.json()),
        fetch(`/api/google-my-business/${user.userId}?range=${dateRange}`).then(r => r.json()),
        fetch(`/api/search-console/${user.userId}?range=${dateRange}`).then(r => r.json())
      ])
      
      setAnalytics({
        vendor: vendorData,
        google: gaData,
        gmb: gmbData,
        searchConsole: gscData
      })
    } catch (error) {
      console.error('Error fetching analytics:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleRefresh = async () => {
    setRefreshing(true)
    await fetchAnalytics()
    setRefreshing(false)
  }

  if (isLoading || loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <RefreshCw className="w-8 h-8 animate-spin text-primary mx-auto mb-4" />
          <p className="text-gray-600">Loading analytics...</p>
        </div>
      </div>
    )
  }

  if (!isAuthenticated) return null

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Vendor Analytics</h1>
            <p className="text-gray-600 mt-2">Comprehensive insights into your vendor performance</p>
          </div>
          <div className="flex gap-3">
            <Select value={dateRange} onValueChange={setDateRange}>
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="7d">Last 7 days</SelectItem>
                <SelectItem value="30d">Last 30 days</SelectItem>
                <SelectItem value="90d">Last 90 days</SelectItem>
                <SelectItem value="1y">Last year</SelectItem>
              </SelectContent>
            </Select>
            <Button variant="outline" onClick={handleRefresh} disabled={refreshing}>
              <RefreshCw className={`w-4 h-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
          </div>
        </div>

        {/* Key Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Profile Views</p>
                  <p className="text-2xl font-bold">{analytics?.vendor?.profileViews || 0}</p>
                  <p className="text-xs text-green-600">+{analytics?.vendor?.profileViewsChange || 0}%</p>
                </div>
                <Eye className="w-8 h-8 text-blue-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Contact Clicks</p>
                  <p className="text-2xl font-bold">{analytics?.vendor?.contactClicks || 0}</p>
                  <p className="text-xs text-green-600">+{analytics?.vendor?.contactClicksChange || 0}%</p>
                </div>
                <Phone className="w-8 h-8 text-green-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Inquiries</p>
                  <p className="text-2xl font-bold">{analytics?.vendor?.inquiries || 0}</p>
                  <p className="text-xs text-green-600">+{analytics?.vendor?.inquiriesChange || 0}%</p>
                </div>
                <Mail className="w-8 h-8 text-purple-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Avg Rating</p>
                  <p className="text-2xl font-bold">{analytics?.vendor?.avgRating || 0}</p>
                  <p className="text-xs text-green-600">+{analytics?.vendor?.ratingChange || 0}</p>
                </div>
                <Star className="w-8 h-8 text-yellow-500" />
              </div>
            </CardContent>
          </Card>
        </div>

        <Tabs defaultValue="overview" className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="traffic">Traffic</TabsTrigger>
            <TabsTrigger value="engagement">Engagement</TabsTrigger>
            <TabsTrigger value="performance">Performance</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <TrendingUp className="w-5 h-5" />
                    Profile Views Trend
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="h-64 flex items-center justify-center bg-gray-50 rounded">
                    <p className="text-gray-500">Chart visualization would go here</p>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <BarChart3 className="w-5 h-5" />
                    Engagement Metrics
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {analytics?.vendor?.engagementData?.map((item: any, index: number) => (
                      <div key={index} className="flex justify-between items-center">
                        <span className="text-sm font-medium">{item.metric}</span>
                        <span className="text-lg font-bold">{item.value}</span>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="traffic" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Traffic Sources</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {analytics?.google?.trafficSources?.map((source: any, index: number) => (
                      <div key={index} className="flex justify-between items-center">
                        <span className="text-sm">{source.source}</span>
                        <span className="font-bold">{source.visitors}%</span>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Page Performance</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <span>Page Views</span>
                      <span className="font-bold">{analytics?.google?.pageViews || 0}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span>Unique Visitors</span>
                      <span className="font-bold">{analytics?.google?.uniqueVisitors || 0}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span>Bounce Rate</span>
                      <span className="font-bold">{analytics?.google?.bounceRate || 0}%</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span>Avg Session Duration</span>
                      <span className="font-bold">{analytics?.google?.avgSessionDuration || '0:00'}</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="engagement" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
              <Card>
                <CardHeader>
                  <CardTitle>Contact Engagement Trend</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="h-64 flex items-center justify-center bg-gray-50 rounded">
                    <p className="text-gray-500">Contact clicks over time chart</p>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>User Interaction Heatmap</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="flex justify-between items-center">
                      <span>Profile Header</span>
                      <div className="flex items-center gap-2">
                        <div className="w-16 bg-red-200 h-2 rounded"></div>
                        <span className="text-sm">85%</span>
                      </div>
                    </div>
                    <div className="flex justify-between items-center">
                      <span>Gallery Section</span>
                      <div className="flex items-center gap-2">
                        <div className="w-12 bg-orange-200 h-2 rounded"></div>
                        <span className="text-sm">65%</span>
                      </div>
                    </div>
                    <div className="flex justify-between items-center">
                      <span>Contact Info</span>
                      <div className="flex items-center gap-2">
                        <div className="w-20 bg-green-200 h-2 rounded"></div>
                        <span className="text-sm">92%</span>
                      </div>
                    </div>
                    <div className="flex justify-between items-center">
                      <span>Reviews Section</span>
                      <div className="flex items-center gap-2">
                        <div className="w-10 bg-yellow-200 h-2 rounded"></div>
                        <span className="text-sm">45%</span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Contact Methods</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="flex justify-between items-center">
                      <span className="flex items-center gap-2">
                        <Phone className="w-4 h-4 text-green-600" />
                        Phone
                      </span>
                      <span className="font-bold">{analytics?.vendor?.phoneClicks || 45}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="flex items-center gap-2">
                        <Mail className="w-4 h-4 text-blue-600" />
                        Email
                      </span>
                      <span className="font-bold">{analytics?.vendor?.emailClicks || 32}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="flex items-center gap-2">
                        <span className="w-4 h-4 bg-green-500 rounded text-white text-xs flex items-center justify-center">W</span>
                        WhatsApp
                      </span>
                      <span className="font-bold">{analytics?.vendor?.whatsappClicks || 12}</span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Gallery Stats</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span>Photo Views</span>
                      <span className="font-bold">{analytics?.vendor?.photoViews || 234}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Photo Clicks</span>
                      <span className="font-bold">{analytics?.vendor?.photoClicks || 89}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Gallery CTR</span>
                      <span className="font-bold text-green-600">{analytics?.vendor?.galleryCTR || 38}%</span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Social Proof</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span>Total Reviews</span>
                      <span className="font-bold">{analytics?.vendor?.totalReviews || 67}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Avg Rating</span>
                      <div className="flex items-center gap-1">
                        <Star className="w-4 h-4 text-yellow-500 fill-current" />
                        <span className="font-bold">{analytics?.vendor?.avgRating || 4.6}</span>
                      </div>
                    </div>
                    <div className="flex justify-between">
                      <span>Response Rate</span>
                      <span className="font-bold text-blue-600">{analytics?.vendor?.responseRate || 92}%</span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Engagement Score</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-green-600 mb-2">8.4</div>
                    <div className="text-sm text-gray-600 mb-3">Overall Score</div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div className="bg-green-600 h-2 rounded-full" style={{width: '84%'}}></div>
                    </div>
                    <div className="text-xs text-gray-500 mt-2">Above industry average</div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="performance" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
              <Card>
                <CardHeader>
                  <CardTitle>Conversion Funnel</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between p-3 bg-blue-50 rounded">
                      <div className="flex items-center gap-3">
                        <Eye className="w-5 h-5 text-blue-600" />
                        <span>Profile Views</span>
                      </div>
                      <span className="font-bold text-blue-600">{analytics?.vendor?.profileViews || 1250}</span>
                    </div>
                    <div className="flex items-center justify-center">
                      <div className="text-sm text-gray-500">↓ {analytics?.vendor?.viewToContactRate || 7.1}%</div>
                    </div>
                    <div className="flex items-center justify-between p-3 bg-green-50 rounded">
                      <div className="flex items-center gap-3">
                        <Phone className="w-5 h-5 text-green-600" />
                        <span>Contact Clicks</span>
                      </div>
                      <span className="font-bold text-green-600">{analytics?.vendor?.contactClicks || 89}</span>
                    </div>
                    <div className="flex items-center justify-center">
                      <div className="text-sm text-gray-500">↓ {analytics?.vendor?.contactToInquiryRate || 25.8}%</div>
                    </div>
                    <div className="flex items-center justify-between p-3 bg-purple-50 rounded">
                      <div className="flex items-center gap-3">
                        <Mail className="w-5 h-5 text-purple-600" />
                        <span>Inquiries</span>
                      </div>
                      <span className="font-bold text-purple-600">{analytics?.vendor?.inquiries || 23}</span>
                    </div>
                    <div className="flex items-center justify-center">
                      <div className="text-sm text-gray-500">↓ {analytics?.vendor?.inquiryToBookingRate || 34.8}%</div>
                    </div>
                    <div className="flex items-center justify-between p-3 bg-yellow-50 rounded">
                      <div className="flex items-center gap-3">
                        <Calendar className="w-5 h-5 text-yellow-600" />
                        <span>Bookings</span>
                      </div>
                      <span className="font-bold text-yellow-600">{analytics?.vendor?.totalBookings || 8}</span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Performance Trends</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="h-64 flex items-center justify-center bg-gray-50 rounded">
                    <p className="text-gray-500">Performance metrics over time</p>
                  </div>
                </CardContent>
              </Card>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Market Position</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-blue-600 mb-1">#{analytics?.vendor?.categoryRank || 3}</div>
                      <div className="text-sm text-gray-600">Category Ranking</div>
                    </div>
                    <div className="flex justify-between items-center">
                      <span>Above Category Avg</span>
                      <span className="font-bold text-green-600">+{analytics?.vendor?.aboveCategoryAvg || 15}%</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span>Market Share</span>
                      <span className="font-bold">{analytics?.vendor?.marketShare || 2.3}%</span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Response Metrics</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <span>Avg Response Time</span>
                      <span className="font-bold text-green-600">2.4 hrs</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span>Response Rate</span>
                      <span className="font-bold text-blue-600">{analytics?.vendor?.responseRate || 92}%</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span>Follow-up Rate</span>
                      <span className="font-bold text-purple-600">78%</span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Quality Score</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-green-600 mb-2">9.2</div>
                    <div className="text-sm text-gray-600 mb-3">Overall Quality</div>
                    <div className="grid grid-cols-2 gap-2 text-xs">
                      <div className="bg-green-100 p-2 rounded">
                        <div className="font-bold">Profile</div>
                        <div>9.5/10</div>
                      </div>
                      <div className="bg-blue-100 p-2 rounded">
                        <div className="font-bold">Service</div>
                        <div>8.9/10</div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}