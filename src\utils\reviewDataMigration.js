import { generateClient } from 'aws-amplify/api';
import { listReviews, updateReview } from '../graphql/queries';
import { updateReview as updateReviewMutation } from '../graphql/mutations';

const client = generateClient();

/**
 * Data migration utility to fix existing reviews with missing entityType
 */
export class ReviewDataMigration {
  
  /**
   * Fix reviews with missing entityType by setting them to 'PLATFORM'
   * @returns {Promise<Object>} Migration result
   */
  static async fixMissingEntityTypes() {
    try {
      console.log('Starting review data migration...');
      
      // Get all reviews
      const result = await client.graphql({
        query: listReviews,
        variables: {
          limit: 1000 // Adjust as needed
        }
      });

      const reviews = result.data.listReviews.items;
      console.log(`Found ${reviews.length} reviews to check`);

      const reviewsToUpdate = reviews.filter(review => 
        !review.entityType || 
        !review.entityId || 
        !review.userEntityComposite
      );

      console.log(`Found ${reviewsToUpdate.length} reviews that need updating`);

      const updateResults = [];
      const errors = [];

      for (const review of reviewsToUpdate) {
        try {
          const entityType = review.entityType || 'PLATFORM';
          const entityId = review.entityId || 'legacy';
          const userEntityComposite = review.userEntityComposite || 
            `${review.userId}#${entityType}#${entityId}`;

          const updateInput = {
            id: review.id,
            entityType: entityType,
            entityId: entityId,
            userEntityComposite: userEntityComposite,
            // Ensure other fields have defaults
            location: review.location || '',
            helpfulCount: review.helpfulCount || 0,
            reviewHelpfulUsers: review.reviewHelpfulUsers || [],
            verified: review.verified || false,
            purchaseVerified: review.purchaseVerified || false,
            images: review.images || []
          };

          console.log(`Updating review ${review.id}...`);
          
          const updateResult = await client.graphql({
            query: updateReviewMutation,
            variables: {
              input: updateInput
            }
          });

          updateResults.push({
            id: review.id,
            success: true,
            data: updateResult.data.updateReview
          });

          console.log(`✅ Updated review ${review.id}`);
          
        } catch (error) {
          console.error(`❌ Error updating review ${review.id}:`, error);
          errors.push({
            id: review.id,
            error: error.message
          });
        }
      }

      const summary = {
        totalReviews: reviews.length,
        reviewsNeedingUpdate: reviewsToUpdate.length,
        successfulUpdates: updateResults.filter(r => r.success).length,
        errors: errors.length,
        updateResults,
        errors
      };

      console.log('Migration Summary:', summary);
      
      return {
        success: errors.length === 0,
        summary,
        message: `Migration completed. Updated ${summary.successfulUpdates} of ${summary.reviewsNeedingUpdate} reviews.`
      };

    } catch (error) {
      console.error('Migration failed:', error);
      return {
        success: false,
        error: error.message,
        message: 'Migration failed. Check console for details.'
      };
    }
  }

  /**
   * Validate review data integrity
   * @returns {Promise<Object>} Validation result
   */
  static async validateReviewData() {
    try {
      console.log('Validating review data integrity...');
      
      const result = await client.graphql({
        query: listReviews,
        variables: {
          limit: 1000
        }
      });

      const reviews = result.data.listReviews.items;
      const issues = [];

      reviews.forEach(review => {
        const reviewIssues = [];
        
        if (!review.entityType) {
          reviewIssues.push('Missing entityType');
        }
        
        if (!review.entityId) {
          reviewIssues.push('Missing entityId');
        }
        
        if (!review.userEntityComposite) {
          reviewIssues.push('Missing userEntityComposite');
        }
        
        if (!review.userId) {
          reviewIssues.push('Missing userId');
        }
        
        if (!review.rating || review.rating < 1 || review.rating > 5) {
          reviewIssues.push('Invalid rating');
        }
        
        if (!review.title || !review.review) {
          reviewIssues.push('Missing title or review content');
        }

        if (reviewIssues.length > 0) {
          issues.push({
            id: review.id,
            issues: reviewIssues
          });
        }
      });

      const validation = {
        totalReviews: reviews.length,
        validReviews: reviews.length - issues.length,
        invalidReviews: issues.length,
        issues
      };

      console.log('Validation Results:', validation);
      
      return {
        success: true,
        validation,
        message: `Validation completed. ${validation.validReviews} valid, ${validation.invalidReviews} with issues.`
      };

    } catch (error) {
      console.error('Validation failed:', error);
      return {
        success: false,
        error: error.message,
        message: 'Validation failed. Check console for details.'
      };
    }
  }

  /**
   * Run complete data migration and validation
   * @returns {Promise<Object>} Complete migration result
   */
  static async runCompleteMigration() {
    try {
      console.log('🚀 Starting complete review data migration...');
      
      // Step 1: Validate current data
      console.log('📊 Step 1: Validating current data...');
      const validationBefore = await this.validateReviewData();
      
      // Step 2: Fix missing entity types
      console.log('🔧 Step 2: Fixing missing entity types...');
      const migrationResult = await this.fixMissingEntityTypes();
      
      // Step 3: Validate after migration
      console.log('✅ Step 3: Validating after migration...');
      const validationAfter = await this.validateReviewData();
      
      const completeMigration = {
        validationBefore: validationBefore.validation,
        migrationResult: migrationResult.summary,
        validationAfter: validationAfter.validation,
        success: migrationResult.success && validationAfter.success,
        message: 'Complete migration finished. Check results for details.'
      };

      console.log('🎉 Complete Migration Results:', completeMigration);
      
      return completeMigration;

    } catch (error) {
      console.error('Complete migration failed:', error);
      return {
        success: false,
        error: error.message,
        message: 'Complete migration failed. Check console for details.'
      };
    }
  }
}

// Export for use in browser console
if (typeof window !== 'undefined') {
  window.ReviewDataMigration = ReviewDataMigration;
}
