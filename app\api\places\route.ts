import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const input = searchParams.get('input');
  const sessionToken = searchParams.get('sessiontoken');
  
  if (!input) {
    return NextResponse.json({ error: 'Input required' }, { status: 400 });
  }

  const apiKey = process.env.NEXT_PUBLIC_GOOGLE_PLACES_API_KEY;
  if (!apiKey) {
    console.error('Google Places API key not found in environment variables');
    return NextResponse.json({ error: 'API key not configured' }, { status: 500 });
  }

  try {
    const response = await fetch(
      `https://maps.googleapis.com/maps/api/place/autocomplete/json?` +
      `input=${encodeURIComponent(input)}&` +
      `components=country:in&` +
      `types=(cities)&` +
      `sessiontoken=${sessionToken}&` +
      `key=${apiKey}`
    );

    const data = await response.json();
    console.log('Google Places API response:', data);
    return NextResponse.json(data);
  } catch (error) {
    console.error('Google Places API error:', error);
    return NextResponse.json({ error: 'API request failed' }, { status: 500 });
  }
}