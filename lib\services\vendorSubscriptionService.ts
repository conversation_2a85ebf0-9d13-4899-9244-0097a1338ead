"use client"

import { generateClient } from '@aws-amplify/api'
import { getCurrentUser } from 'aws-amplify/auth'
import { 
  createVendorSubscription, 
  createSubscriptionPayment,
  updateVendorSubscription 
} from '@/src/graphql/mutations'
import { 
  listPricingPlans, 
  getVendorSubscription,
  listVendorSubscriptions 
} from '@/src/graphql/queries'

const client = generateClient()

export interface VendorSubscriptionPlan {
  id: string
  name: string
  description: string
  price: number
  currency: string
  duration: 'MONTHLY' | 'QUARTERLY' | 'YEARLY'
  features: string[]
  isActive: boolean
  isPopular: boolean
  discountPercentage?: number
  discountValidUntil?: string
}

export interface CreateSubscriptionInput {
  vendorId: string
  planId: string
  paymentMethod: 'RAZORPAY' | 'UPI' | 'CREDIT_CARD' | 'DEBIT_CARD' | 'NET_BANKING'
  autoRenew?: boolean
  vendorEmail?: string
  vendorName?: string
}

export interface RazorpayVendorOptions {
  key: string
  amount: number
  currency: string
  name: string
  description: string
  image?: string
  order_id: string
  handler: (response: RazorpayVendorResponse) => void
  prefill?: {
    name?: string
    email?: string
    contact?: string
  }
  theme?: {
    color?: string
  }
  modal?: {
    ondismiss?: () => void
  }
}

export interface RazorpayVendorResponse {
  razorpay_payment_id: string
  razorpay_order_id: string
  razorpay_signature: string
}

declare global {
  interface Window {
    Razorpay: any
  }
}

export class VendorSubscriptionService {
  private static RAZORPAY_KEY = process.env.NEXT_PUBLIC_RAZORPAY_KEY || 'rzp_test_YourKeyHere'

  /**
   * Get all available pricing plans for vendors
   */
  static async getPricingPlans(): Promise<VendorSubscriptionPlan[]> {
    try {
      const result = await client.graphql({
        query: listPricingPlans,
        variables: {
          filter: { isActive: { eq: true } }
        }
      })

      return result.data.listPricingPlans.items.map(plan => ({
        id: plan.id,
        name: plan.name,
        description: plan.description,
        price: plan.price,
        currency: plan.currency,
        duration: plan.duration,
        features: plan.features,
        isActive: plan.isActive,
        isPopular: plan.isPopular,
        discountPercentage: plan.discountPercentage,
        discountValidUntil: plan.discountValidUntil
      }))
    } catch (error) {
      console.error('Error fetching pricing plans:', error)
      return []
    }
  }

  /**
   * Get vendor's current subscription
   */
  static async getVendorSubscription(vendorId: string) {
    try {
      const result = await client.graphql({
        query: listVendorSubscriptions,
        variables: {
          filter: {
            vendorId: { eq: vendorId },
            status: { eq: 'ACTIVE' }
          }
        },
        authMode: 'userPool'
      })

      return result.data.listVendorSubscriptions.items[0] || null
    } catch (error) {
      console.error('Error fetching vendor subscription:', error)
      return null
    }
  }

  /**
   * Get all subscriptions for a vendor (for dashboard)
   */
  static async getVendorSubscriptions(vendorId: string) {
    try {
      const result = await client.graphql({
        query: listVendorSubscriptions,
        variables: {
          filter: {
            vendorId: { eq: vendorId }
          }
        },
        authMode: 'userPool'
      })

      return result.data.listVendorSubscriptions.items || []
    } catch (error) {
      console.error('Error fetching vendor subscriptions:', error)
      return []
    }
  }

  /**
   * Create Razorpay order for vendor subscription
   */
  static async createRazorpayOrder(amount: number, currency: string = 'INR'): Promise<{ success: boolean; orderId?: string; message: string }> {
    try {
      // Use the same pattern as the existing payment service
      // In a real implementation, this would call your backend API
      // which would create a Razorpay order using their server-side API

      // For now, we'll simulate the order creation
      const orderId = `vendor_sub_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`

      return {
        success: true,
        orderId,
        message: 'Razorpay order created successfully'
      }
    } catch (error) {
      console.error('Create Razorpay order error:', error)
      return {
        success: false,
        message: 'Failed to create Razorpay order'
      }
    }
  }

  /**
   * Process vendor subscription payment with Razorpay
   */
  static async processSubscriptionPayment(
    subscriptionData: CreateSubscriptionInput,
    plan: VendorSubscriptionPlan,
    vendorInfo: { name: string; email: string; phone?: string }
  ): Promise<{ success: boolean; subscriptionId?: string; message: string }> {
    try {
      if (!window.Razorpay) {
        throw new Error('Razorpay SDK not loaded')
      }

      // Create Razorpay order
      const orderResult = await this.createRazorpayOrder(plan.price, plan.currency)
      if (!orderResult.success) {
        throw new Error(orderResult.message)
      }

      return new Promise((resolve) => {
        const options: RazorpayVendorOptions = {
          key: this.RAZORPAY_KEY,
          amount: plan.price * 100, // Amount in paise
          currency: plan.currency,
          name: 'BookmyFestive - Vendor Subscription',
          description: `${plan.name} Subscription`,
          image: '/logo.svg',
          order_id: orderResult.orderId!,
          handler: async (response: RazorpayVendorResponse) => {
            try {
              // Create subscription after successful payment
              const subscriptionResult = await this.createSubscription({
                ...subscriptionData,
                transactionId: response.razorpay_payment_id,
                razorpayOrderId: response.razorpay_order_id,
                razorpaySignature: response.razorpay_signature,
                plan
              })

              if (subscriptionResult.success) {
                resolve({
                  success: true,
                  subscriptionId: subscriptionResult.subscriptionId,
                  message: 'Subscription created successfully'
                })
              } else {
                resolve({
                  success: false,
                  message: subscriptionResult.message || 'Failed to create subscription'
                })
              }
            } catch (error) {
              console.error('Payment handler error:', error)
              resolve({
                success: false,
                message: 'Payment processing failed'
              })
            }
          },
          prefill: {
            name: vendorInfo.name,
            email: vendorInfo.email,
            contact: vendorInfo.phone || ''
          },
          theme: {
            color: '#610f13'
          },
          modal: {
            ondismiss: () => {
              resolve({
                success: false,
                message: 'Payment cancelled by user'
              })
            }
          }
        }

        const rzp = new window.Razorpay(options)
        rzp.open()
      })

    } catch (error) {
      console.error('Process subscription payment error:', error)
      return {
        success: false,
        message: error.message || 'Failed to process payment'
      }
    }
  }

  /**
   * Create vendor subscription after successful payment
   */
  private static async createSubscription(data: any): Promise<{ success: boolean; subscriptionId?: string; message: string }> {
    try {
      const user = await getCurrentUser()
      if (!user) {
        throw new Error('Authentication required')
      }

      // Calculate subscription dates
      const startDate = new Date()
      const endDate = this.calculateEndDate(startDate, data.plan.duration)

      // Create subscription
      const subscriptionId = `sub_${Date.now()}_${data.vendorId}`
      const subscriptionResult = await client.graphql({
        query: createVendorSubscription,
        variables: {
          input: {
            id: subscriptionId,
            vendorId: data.vendorId,
            planId: data.planId,
            status: 'ACTIVE',
            startDate: startDate.toISOString().split('T')[0],
            endDate: endDate.toISOString().split('T')[0],
            autoRenew: data.autoRenew !== false,
            paymentMethod: data.paymentMethod,
            lastPaymentDate: new Date().toISOString(),
            nextPaymentDate: endDate.toISOString(),
            amount: data.plan.price,
            currency: data.plan.currency,
            transactionId: data.transactionId
          }
        },
        authMode: 'userPool'
      })

      // Create payment record
      const paymentId = `pay_${Date.now()}_${data.vendorId}`
      await client.graphql({
        query: createSubscriptionPayment,
        variables: {
          input: {
            id: paymentId,
            subscriptionId,
            vendorId: data.vendorId,
            amount: data.plan.price,
            currency: data.plan.currency,
            paymentMethod: data.paymentMethod,
            transactionId: data.transactionId,
            status: 'PAID',
            paymentDate: new Date().toISOString()
          }
        },
        authMode: 'userPool'
      })

      return {
        success: true,
        subscriptionId,
        message: 'Subscription created successfully'
      }

    } catch (error) {
      console.error('Create subscription error:', error)
      return {
        success: false,
        message: error.message || 'Failed to create subscription'
      }
    }
  }

  /**
   * Calculate subscription end date based on duration
   */
  private static calculateEndDate(startDate: Date, duration: string): Date {
    const endDate = new Date(startDate)
    
    switch (duration) {
      case 'MONTHLY':
        endDate.setMonth(endDate.getMonth() + 1)
        break
      case 'QUARTERLY':
        endDate.setMonth(endDate.getMonth() + 3)
        break
      case 'YEARLY':
        endDate.setFullYear(endDate.getFullYear() + 1)
        break
      default:
        endDate.setMonth(endDate.getMonth() + 1) // Default to monthly
    }
    
    return endDate
  }

  /**
   * Load Razorpay SDK
   */
  static loadRazorpaySDK(): Promise<boolean> {
    return new Promise((resolve) => {
      if (window.Razorpay) {
        resolve(true)
        return
      }

      const script = document.createElement('script')
      script.src = 'https://checkout.razorpay.com/v1/checkout.js'
      script.async = true
      script.onload = () => resolve(true)
      script.onerror = () => resolve(false)
      document.body.appendChild(script)
    })
  }

  /**
   * Cancel a subscription (Direct GraphQL implementation)
   */
  static async cancelSubscription(subscriptionId: string, reason?: string): Promise<boolean> {
    try {
      const result = await client.graphql({
        query: updateVendorSubscription,
        variables: {
          input: {
            id: subscriptionId,
            status: 'CANCELLED',
            cancellationReason: reason,
            cancellationDate: new Date().toISOString()
          }
        },
        authMode: 'userPool'
      })

      return !!result.data.updateVendorSubscription
    } catch (error) {
      console.error('Error cancelling subscription:', error)
      throw new Error('Failed to cancel subscription')
    }
  }

  /**
   * Renew a subscription (Direct GraphQL implementation)
   */
  static async renewSubscription(subscriptionId: string, planId?: string): Promise<boolean> {
    try {
      const updateData: any = {
        id: subscriptionId,
        status: 'ACTIVE',
        startDate: new Date().toISOString().split('T')[0],
        lastPaymentDate: new Date().toISOString()
      }

      if (planId) {
        updateData.planId = planId
      }

      // Calculate new end date (assuming 1 year subscription)
      const endDate = new Date()
      endDate.setFullYear(endDate.getFullYear() + 1)
      updateData.endDate = endDate.toISOString().split('T')[0]
      updateData.nextPaymentDate = endDate.toISOString()

      const result = await client.graphql({
        query: updateVendorSubscription,
        variables: {
          input: updateData
        },
        authMode: 'userPool'
      })

      return !!result.data.updateVendorSubscription
    } catch (error) {
      console.error('Error renewing subscription:', error)
      throw new Error('Failed to renew subscription')
    }
  }

  /**
   * Skip pricing during signup (Direct GraphQL implementation)
   */
  static async skipPricingDuringSignup(vendorId: string, reason?: string): Promise<boolean> {
    try {
      // Create a basic free subscription record
      const result = await client.graphql({
        query: createVendorSubscription,
        variables: {
          input: {
            vendorId,
            planId: 'free-plan',
            status: 'ACTIVE',
            startDate: new Date().toISOString().split('T')[0],
            endDate: '2099-12-31', // Far future date for free plan
            autoRenew: false,
            amount: 0,
            currency: 'INR',
            paymentMethod: 'SKIPPED',
            cancellationReason: reason || 'Pricing skipped during signup'
          }
        },
        authMode: 'userPool'
      })

      return !!result.data.createVendorSubscription
    } catch (error) {
      console.error('Error skipping pricing:', error)
      throw new Error('Failed to skip pricing')
    }
  }
}
