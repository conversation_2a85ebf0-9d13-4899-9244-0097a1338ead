"use client"

import { TopHeader } from "@/components/top-header"
import { <PERSON><PERSON> } from "@/components/header"
import { Footer } from "@/components/footer"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { SimpleSEO } from "@/components/seo/SimpleSEO"
import { 
  Search, 
  BookOpen, 
  Users, 
  MapPin, 
  ShoppingBag, 
  Calendar,
  MessageCircle,
  Star,
  CreditCard,
  Settings,
  HelpCircle,
  Video,
  FileText,
  Phone
} from "lucide-react"
import Link from "next/link"
import { useState } from "react"
import { useHelpTranslations, useCommonTranslations } from '@/lib/universal-i18n'

export default function HelpPage() {
  const [searchQuery, setSearchQuery] = useState('')
  const helpT = useHelpTranslations()
  const commonT = useCommonTranslations()

  const helpCategories = [
    {
      icon: Users,
      title: "Getting Started",
      description: "Learn the basics of using BookmyFestive",
      articles: [
        "Creating Your Account",
        "Setting Up Your Profile", 
        "Understanding User Types",
        "First Steps Guide"
      ],
      link: "/help/getting-started"
    },
    {
      icon: Search,
      title: "Finding Vendors",
      description: "How to search and connect with wedding vendors",
      articles: [
        "Searching for Vendors",
        "Using Filters Effectively",
        "Reading Vendor Profiles",
        "Contacting Vendors"
      ],
      link: "/help/vendors"
    },
    {
      icon: MapPin,
      title: "Booking Venues",
      description: "Everything about finding and booking venues",
      articles: [
        "Venue Search Tips",
        "Comparing Venues",
        "Booking Process",
        "Venue Requirements"
      ],
      link: "/help/venues"
    },
    {
      icon: ShoppingBag,
      title: "Shopping",
      description: "Guide to shopping for wedding essentials",
      articles: [
        "Browsing Products",
        "Adding to Cart",
        "Checkout Process",
        "Order Tracking"
      ],
      link: "/help/shopping"
    },
    {
      icon: Calendar,
      title: "Wedding Planning",
      description: "Tools and tips for planning your wedding",
      articles: [
        "Wedding Checklist",
        "Budget Planning",
        "Guest List Management",
        "Timeline Creation"
      ],
      link: "/help/planning"
    },
    {
      icon: Star,
      title: "Reviews & Ratings",
      description: "How to leave and read reviews",
      articles: [
        "Writing Reviews",
        "Rating System",
        "Review Guidelines",
        "Managing Your Reviews"
      ],
      link: "/help/reviews"
    }
  ]

  const quickHelp = [
    {
      icon: Video,
      key: 'videoTutorials',
      link: "/help/videos"
    },
    {
      icon: FileText,
      key: 'userManual',
      link: "/help/manual"
    },
    {
      icon: MessageCircle,
      key: 'liveChat',
      action: "chat"
    },
    {
      icon: Phone,
      key: 'contactSupport',
      link: "/contact"
    }
  ]

  const popularArticles = [
    "How to create a vendor account",
    "Setting up your wedding budget",
    "Finding photographers in your city",
    "Understanding booking policies",
    "Managing your wedding checklist",
    "Payment and refund policies"
  ]

  return (
    <>
      <SimpleSEO
        title="Help Center - BookmyFestive"
        description="Get help with using BookmyFestive. Find guides, tutorials, and answers to common questions about wedding planning, vendors, venues, and more."
        keywords="help, support, guide, tutorial, wedding planning help, BookmyFestive help"
        url="/help"
      />
      
      <div className="min-h-screen bg-background">
        <TopHeader />
        <Header />
        
        {/* Hero Section */}
        <section className="bg-[#5d1417] text-primary-foreground py-20">
          <div className="container mx-auto px-4 text-center">
            <h1 className="text-4xl md:text-5xl font-bold mb-6 text-primary-foreground">
              {helpT.title}
            </h1>
            <p className="text-xl mb-8 max-w-2xl mx-auto text-primary-foreground/90">
              {helpT.subtitle}
            </p>
            
            {/* Search Bar */}
            <div className="max-w-2xl mx-auto relative">
              <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-muted-foreground w-5 h-5" />
              <Input
                type="text"
                placeholder={helpT.searchPlaceholder}
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-12 py-4 text-lg bg-background text-foreground"
              />
            </div>
          </div>
        </section>

        {/* Quick Help */}
        <section className="py-12 bg-muted/30">
          <div className="container mx-auto px-4">
            <h2 className="text-2xl font-bold text-center mb-8">{helpT.quickHelp.title}</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {quickHelp.map((item, index) => (
                <Card key={index} className="text-center hover:shadow-lg transition-shadow cursor-pointer">
                  <CardContent className="pt-6">
                    <item.icon className="w-12 h-12 mx-auto mb-4 text-primary" />
                    <h3 className="font-semibold mb-2">{helpT.quickHelp[item.key]?.title || item.title}</h3>
                    <p className="text-sm text-muted-foreground mb-4">{helpT.quickHelp[item.key]?.description || item.description}</p>
                    {item.link ? (
                      <Link href={item.link}>
                        <Button variant="outline" size="sm">{helpT.quickHelp.access}</Button>
                      </Link>
                    ) : (
                      <Button variant="outline" size="sm">{helpT.quickHelp.startChat}</Button>
                    )}
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Help Categories */}
        <section className="py-16">
          <div className="container mx-auto px-4">
            <h2 className="text-3xl font-bold text-center mb-12">{helpT.categories.title}</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {helpCategories.map((category, index) => (
                <Card key={index} className="hover:shadow-lg transition-shadow">
                  <CardHeader>
                    <div className="flex items-center gap-3">
                      <category.icon className="w-8 h-8 text-primary" />
                      <div>
                        <CardTitle className="text-lg">{category.title}</CardTitle>
                        <p className="text-sm text-muted-foreground">{category.description}</p>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <ul className="space-y-2 mb-4">
                      {category.articles.map((article, idx) => (
                        <li key={idx} className="text-sm text-muted-foreground hover:text-foreground cursor-pointer">
                          • {article}
                        </li>
                      ))}
                    </ul>
                    <Link href={category.link}>
                      <Button variant="outline" className="w-full">
                        {helpT.categories.viewAllArticles}
                      </Button>
                    </Link>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Popular Articles */}
        <section className="py-16 bg-muted/30">
          <div className="container mx-auto px-4">
            <h2 className="text-3xl font-bold text-center mb-12">{helpT.popularArticles.title}</h2>
            <div className="max-w-4xl mx-auto">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {popularArticles.map((article, index) => (
                  <Card key={index} className="hover:shadow-md transition-shadow cursor-pointer">
                    <CardContent className="p-4">
                      <div className="flex items-center gap-3">
                        <HelpCircle className="w-5 h-5 text-primary flex-shrink-0" />
                        <span className="text-sm">{article}</span>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          </div>
        </section>

        {/* Contact Support */}
        <section className="py-16">
          <div className="container mx-auto px-4 text-center">
            <h2 className="text-3xl font-bold mb-6">{helpT.stillNeedHelp.title}</h2>
            <p className="text-xl text-muted-foreground mb-8 max-w-2xl mx-auto">
              {helpT.stillNeedHelp.subtitle}
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/contact">
                <Button size="lg" className="px-8">
                  <MessageCircle className="w-5 h-5 mr-2" />
                  {helpT.stillNeedHelp.contactSupport}
                </Button>
              </Link>
              <Button variant="outline" size="lg" className="px-8">
                <Phone className="w-5 h-5 mr-2" />
                {helpT.stillNeedHelp.callUs}
              </Button>
            </div>
          </div>
        </section>

        <Footer />
      </div>
    </>
  )
}
