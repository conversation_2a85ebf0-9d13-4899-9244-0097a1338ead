import React, { useState } from 'react';
import { View, Text, TouchableOpacity, Linking, Alert } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Card, CardContent, CardHeader, CardTitle } from './ui/Card';
import { Button } from './ui/Button';
import { Badge } from './ui/Badge';
import { QuickInquiryForm } from './QuickInquiryForm';
import { useAuth } from '../providers/AuthProvider';
import { useTheme } from '../providers/ThemeProvider';
import { useNavigation } from '@react-navigation/native';

interface AuthenticatedContactVendorProps {
  vendor: {
    id: string;
    userId: string;
    name: string;
    category?: string;
    contact?: string;
    email?: string;
    website?: string;
    rating?: number;
    reviewCount?: number;
    priceRange?: string;
  };
  showInquiryForm?: boolean;
}

export function AuthenticatedContactVendor({ 
  vendor, 
  showInquiryForm = true 
}: AuthenticatedContactVendorProps) {
  const { isAuthenticated, isLoading, user } = useAuth();
  const { theme } = useTheme();
  const navigation = useNavigation();
  const [showLoginPrompt, setShowLoginPrompt] = useState(false);
  const [showQuickInquiry, setShowQuickInquiry] = useState(false);

  const handleContactAction = (action: string) => {
    if (!isAuthenticated) {
      setShowLoginPrompt(true);
      return;
    }

    switch (action) {
      case 'call':
        if (vendor.contact) {
          Linking.openURL(`tel:${vendor.contact}`);
        } else {
          Alert.alert('Contact Not Available', 'Phone number not provided by vendor');
        }
        break;
      case 'email':
        if (vendor.email) {
          Linking.openURL(`mailto:${vendor.email}`);
        } else {
          Alert.alert('Email Not Available', 'Email address not provided by vendor');
        }
        break;
      case 'website':
        if (vendor.website) {
          Linking.openURL(vendor.website);
        } else {
          Alert.alert('Website Not Available', 'Website not provided by vendor');
        }
        break;
      case 'inquiry':
        setShowQuickInquiry(true);
        break;
      case 'whatsapp':
        if (vendor.contact) {
          const message = `Hi ${vendor.name}, I'm interested in your Festive Services. Can you please provide more details?`;
          Linking.openURL(`whatsapp://send?phone=${vendor.contact}&text=${encodeURIComponent(message)}`);
        } else {
          Alert.alert('WhatsApp Not Available', 'Phone number not provided by vendor');
        }
        break;
    }
  };

  const handleLogin = () => {
    setShowLoginPrompt(false);
    navigation.navigate('Login' as never);
  };

  const handleSignup = () => {
    setShowLoginPrompt(false);
    navigation.navigate('Signup' as never);
  };

  if (isLoading) {
    return (
      <Card style={{ margin: 16 }}>
        <CardContent style={{ padding: 20, alignItems: 'center' }}>
          <Text style={{ color: theme.colors.textSecondary }}>Loading...</Text>
        </CardContent>
      </Card>
    );
  }

  if (showQuickInquiry) {
    return (
      <QuickInquiryForm
        vendorUserId={vendor.userId}
        vendorId={vendor.id}
        vendorName={vendor.name}
        vendorCategory={vendor.category}
        onClose={() => setShowQuickInquiry(false)}
      />
    );
  }

  if (showLoginPrompt) {
    return (
      <Card style={{ margin: 16 }}>
        <CardHeader>
          <View style={{ flexDirection: 'row', alignItems: 'center', gap: 8 }}>
            <Ionicons name="lock-closed" size={24} color={theme.colors.primary} />
            <CardTitle>Authentication Required</CardTitle>
          </View>
        </CardHeader>
        
        <CardContent>
          <View style={{ alignItems: 'center', gap: 16 }}>
            <Ionicons name="shield-checkmark" size={64} color={theme.colors.primary} />
            
            <Text style={{
              fontSize: 16,
              fontWeight: '600',
              color: theme.colors.text,
              textAlign: 'center',
            }}>
              Sign in to Contact Vendor
            </Text>
            
            <Text style={{
              fontSize: 14,
              color: theme.colors.textSecondary,
              textAlign: 'center',
              lineHeight: 20,
            }}>
              To protect our vendors from spam and ensure quality inquiries, 
              please sign in to access contact information and send messages.
            </Text>

            <View style={{ 
              backgroundColor: theme.colors.muted,
              padding: 12,
              borderRadius: 8,
              width: '100%',
            }}>
              <Text style={{
                fontSize: 12,
                color: theme.colors.textSecondary,
                textAlign: 'center',
                fontStyle: 'italic',
              }}>
                Your information is secure and will only be used to facilitate 
                communication with wedding vendors.
              </Text>
            </View>

            <View style={{ flexDirection: 'row', gap: 12, width: '100%' }}>
              <Button
                title="Sign In"
                onPress={handleLogin}
                style={{ flex: 1 }}
                icon="log-in"
              />
              <Button
                title="Sign Up"
                onPress={handleSignup}
                variant="outline"
                style={{ flex: 1 }}
                icon="person-add"
              />
            </View>

            <Button
              title="Cancel"
              onPress={() => setShowLoginPrompt(false)}
              variant="ghost"
              fullWidth
            />
          </View>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card style={{ margin: 16 }}>
      <CardHeader>
        <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between' }}>
          <View style={{ flexDirection: 'row', alignItems: 'center', gap: 8 }}>
            <Ionicons name="chatbubble-ellipses" size={24} color={theme.colors.primary} />
            <CardTitle>Contact Vendor</CardTitle>
          </View>
          
          {vendor.rating && (
            <View style={{ flexDirection: 'row', alignItems: 'center', gap: 4 }}>
              <Ionicons name="star" size={16} color="#F59E0B" />
              <Text style={{ fontSize: 14, fontWeight: '600', color: theme.colors.text }}>
                {vendor.rating.toFixed(1)}
              </Text>
              {vendor.reviewCount && (
                <Text style={{ fontSize: 12, color: theme.colors.textSecondary }}>
                  ({vendor.reviewCount})
                </Text>
              )}
            </View>
          )}
        </View>

        <View style={{ flexDirection: 'row', alignItems: 'center', gap: 8, marginTop: 8 }}>
          <Badge variant="default" size="sm">
            {vendor.name}
          </Badge>
          {vendor.category && (
            <Badge variant="outline" size="sm">
              {vendor.category}
            </Badge>
          )}
          {vendor.priceRange && (
            <Badge variant="secondary" size="sm">
              {vendor.priceRange}
            </Badge>
          )}
        </View>
      </CardHeader>

      <CardContent>
        <View style={{ gap: 12 }}>
          {/* Quick Actions */}
          <View style={{ flexDirection: 'row', gap: 8, flexWrap: 'wrap' }}>
            <Button
              title="Call"
              onPress={() => handleContactAction('call')}
              variant="default"
              size="sm"
              icon="call"
              style={{ flex: 1, minWidth: 80 }}
            />
            
            <Button
              title="Email"
              onPress={() => handleContactAction('email')}
              variant="outline"
              size="sm"
              icon="mail"
              style={{ flex: 1, minWidth: 80 }}
            />
            
            <Button
              title="WhatsApp"
              onPress={() => handleContactAction('whatsapp')}
              variant="secondary"
              size="sm"
              icon="logo-whatsapp"
              style={{ flex: 1, minWidth: 80 }}
            />
          </View>

          {/* Website Link */}
          {vendor.website && (
            <Button
              title="Visit Website"
              onPress={() => handleContactAction('website')}
              variant="outline"
              icon="globe"
              iconPosition="left"
              fullWidth
            />
          )}

          {/* Quick Inquiry */}
          {showInquiryForm && (
            <Button
              title="Send Quick Inquiry"
              onPress={() => handleContactAction('inquiry')}
              variant="default"
              icon="chatbubble"
              iconPosition="left"
              fullWidth
            />
          )}

          {/* Contact Information Display */}
          <View style={{ 
            backgroundColor: theme.colors.muted,
            padding: 12,
            borderRadius: 8,
            gap: 8,
          }}>
            <Text style={{
              fontSize: 14,
              fontWeight: '600',
              color: theme.colors.text,
              marginBottom: 4,
            }}>
              Contact Information
            </Text>
            
            {vendor.contact && (
              <View style={{ flexDirection: 'row', alignItems: 'center', gap: 8 }}>
                <Ionicons name="call" size={16} color={theme.colors.textSecondary} />
                <Text style={{ fontSize: 14, color: theme.colors.text }}>
                  {vendor.contact}
                </Text>
              </View>
            )}
            
            {vendor.email && (
              <View style={{ flexDirection: 'row', alignItems: 'center', gap: 8 }}>
                <Ionicons name="mail" size={16} color={theme.colors.textSecondary} />
                <Text style={{ fontSize: 14, color: theme.colors.text }}>
                  {vendor.email}
                </Text>
              </View>
            )}
            
            {vendor.website && (
              <View style={{ flexDirection: 'row', alignItems: 'center', gap: 8 }}>
                <Ionicons name="globe" size={16} color={theme.colors.textSecondary} />
                <Text style={{ fontSize: 14, color: theme.colors.text }}>
                  {vendor.website}
                </Text>
              </View>
            )}
          </View>

          {/* Security Notice */}
          <View style={{
            backgroundColor: '#F0F9FF',
            borderColor: '#0EA5E9',
            borderWidth: 1,
            borderRadius: 8,
            padding: 12,
            flexDirection: 'row',
            alignItems: 'center',
            gap: 8,
          }}>
            <Ionicons name="shield-checkmark" size={16} color="#0EA5E9" />
            <Text style={{ fontSize: 12, color: '#0369A1', flex: 1 }}>
              Your contact information is protected and will only be shared with this vendor.
            </Text>
          </View>
        </View>
      </CardContent>
    </Card>
  );
}
