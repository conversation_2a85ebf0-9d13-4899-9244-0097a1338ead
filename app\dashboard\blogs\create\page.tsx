'use client'

import { useState, useEffect } from 'react'
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { 
  ArrowLeft, 
  Save, 
  Eye, 
  Upload, 
  X,
  Plus,
  FileText,
  Image as ImageIcon
} from 'lucide-react'
import { BlogService, BLOG_CATEGORIES, BlogStatus, BlogCategory, AuthorType, CreateBlogInput } from '@/lib/services/blogService'
import { useAuth } from '@/contexts/AuthContext'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { navigateToBlogDashboard, getBlogDashboardLink, getBlogDashboardLabel } from '@/lib/utils/blogNavigation'
import { showToast } from '@/lib/toast'

export default function CreateBlog() {
  const { isAuthenticated, user, userProfile, isAdmin, isVendor } = useAuth()
  const router = useRouter()
  const [loading, setLoading] = useState(false)
  const [formData, setFormData] = useState<CreateBlogInput>({
    title: '',
    content: '',
    excerpt: '',
    category: BlogCategory.WEDDING_PLANNING,
    authorName: '',
    authorType: AuthorType.VENDOR,
    featuredImage: '',
    tags: [],
    status: BlogStatus.DRAFT
  })
  const [newTag, setNewTag] = useState('')
  const [previewMode, setPreviewMode] = useState(false)

  // Auto-populate author information based on user profile
  useEffect(() => {
    if (userProfile && isAuthenticated) {
      let authorType = AuthorType.EXPERT // Default for regular users
      let authorName = userProfile.fullName || userProfile.firstName || 'Anonymous'

      // Determine author type based on user role
      if (isAdmin || userProfile.isAdmin || userProfile.role === 'ADMIN') {
        authorType = AuthorType.ADMIN
      } else if (isVendor || userProfile.isVendor || userProfile.role === 'VENDOR' || userProfile.businessInfo?.businessName) {
        authorType = AuthorType.VENDOR
        // Use business name if available for vendors
        if (userProfile.businessInfo?.businessName) {
          authorName = userProfile.businessInfo.businessName
        }
      }

      setFormData(prev => ({
        ...prev,
        authorName,
        authorType
      }))
    }
  }, [userProfile, isAuthenticated, isAdmin, isVendor])

  const handleInputChange = (field: keyof CreateBlogInput, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleAddTag = () => {
    if (newTag.trim() && !formData.tags?.includes(newTag.trim())) {
      setFormData(prev => ({
        ...prev,
        tags: [...(prev.tags || []), newTag.trim()]
      }))
      setNewTag('')
    }
  }

  const handleRemoveTag = (tagToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags?.filter(tag => tag !== tagToRemove) || []
    }))
  }

  const handleSubmit = async (status: BlogStatus) => {
    if (!formData.title.trim() || !formData.content.trim()) {
      showToast.warning('Please fill in the title and content')
      return
    }

    try {
      setLoading(true)
      const blogData = {
        ...formData,
        status,
        excerpt: formData.excerpt || generateExcerpt(formData.content)
      }

      await BlogService.createBlog(blogData)
      navigateToBlogDashboard(router, { isAdmin, isVendor, userType: user?.userType, userProfile })
    } catch (error) {
      console.error('Error creating blog:', error)
      showToast.error('Failed to create blog post')
    } finally {
      setLoading(false)
    }
  }

  const generateExcerpt = (content: string, maxLength: number = 150): string => {
    const plainText = content.replace(/<[^>]*>/g, '')
    if (plainText.length <= maxLength) {
      return plainText
    }
    return plainText.substring(0, maxLength).trim() + '...'
  }

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      const reader = new FileReader()
      reader.onload = (e) => {
        const base64String = e.target?.result as string
        setFormData(prev => ({
          ...prev,
          featuredImage: base64String
        }))
      }
      reader.readAsDataURL(file)
    }
  }

  if (!isAuthenticated) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card>
          <CardContent className="p-6 text-center">
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Authentication Required</h3>
            <p className="text-gray-600">Please log in to create blog posts.</p>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="flex items-center justify-between mb-8">
        <div className="flex items-center gap-4">
          <Link href={getBlogDashboardLink({ isAdmin, isVendor, userType: user?.userType, userProfile })}>
            <Button variant="outline" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              {getBlogDashboardLabel({ isAdmin, isVendor, userType: user?.userType, userProfile })}
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Create New Blog Post</h1>
            <p className="text-gray-600">Share your expertise with the community</p>
          </div>
        </div>
        
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            onClick={() => setPreviewMode(!previewMode)}
          >
            <Eye className="h-4 w-4 mr-2" />
            {previewMode ? 'Edit' : 'Preview'}
          </Button>
          
          <Button
            onClick={() => handleSubmit(BlogStatus.DRAFT)}
            disabled={loading}
            variant="outline"
          >
            <Save className="h-4 w-4 mr-2" />
            Save Draft
          </Button>
          
          <Button
            onClick={() => handleSubmit(BlogStatus.PUBLISHED)}
            disabled={loading}
            className="bg-primary hover:bg-primary/90 text-white"
          >
            <FileText className="h-4 w-4 mr-2" />
            Publish
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Main Content */}
        <div className="lg:col-span-2">
          {previewMode ? (
            /* Preview Mode */
            <Card>
              <CardHeader>
                <CardTitle>Preview</CardTitle>
              </CardHeader>
              <CardContent className="p-6">
                {formData.featuredImage && (
                  <img
                    src={formData.featuredImage}
                    alt="Featured"
                    className="w-full h-64 object-cover rounded-lg mb-6"
                  />
                )}
                
                <div className="flex items-center gap-2 mb-4">
                  <Badge className={BLOG_CATEGORIES[formData.category]?.color}>
                    {BLOG_CATEGORIES[formData.category]?.title}
                  </Badge>
                  <span className="text-sm text-gray-500">by {formData.authorName}</span>
                </div>
                
                <h1 className="text-3xl font-bold text-gray-900 mb-4">{formData.title}</h1>
                
                {formData.excerpt && (
                  <p className="text-lg text-gray-600 mb-6">{formData.excerpt}</p>
                )}
                
                <div className="prose max-w-none">
                  <div dangerouslySetInnerHTML={{ __html: formData.content }} />
                </div>
                
                {formData.tags && formData.tags.length > 0 && (
                  <div className="mt-6 pt-6 border-t">
                    <h4 className="text-sm font-medium text-gray-900 mb-2">Tags:</h4>
                    <div className="flex flex-wrap gap-2">
                      {formData.tags.map((tag, index) => (
                        <Badge key={index} variant="outline">#{tag}</Badge>
                      ))}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          ) : (
            /* Edit Mode */
            <div className="space-y-6">
              {/* Title */}
              <Card>
                <CardContent className="p-6">
                  <Label htmlFor="title" className="text-base font-medium">Title *</Label>
                  <Input
                    id="title"
                    placeholder="Enter your blog post title..."
                    value={formData.title}
                    onChange={(e) => handleInputChange('title', e.target.value)}
                    className="mt-2 text-lg"
                  />
                </CardContent>
              </Card>

              {/* Excerpt */}
              <Card>
                <CardContent className="p-6">
                  <Label htmlFor="excerpt" className="text-base font-medium">Excerpt</Label>
                  <Textarea
                    id="excerpt"
                    placeholder="Brief description of your post (optional - will be auto-generated if left empty)"
                    value={formData.excerpt}
                    onChange={(e) => handleInputChange('excerpt', e.target.value)}
                    className="mt-2"
                    rows={3}
                  />
                </CardContent>
              </Card>

              {/* Content */}
              <Card>
                <CardContent className="p-6">
                  <Label htmlFor="content" className="text-base font-medium">Content *</Label>
                  <Textarea
                    id="content"
                    placeholder="Write your blog post content here..."
                    value={formData.content}
                    onChange={(e) => handleInputChange('content', e.target.value)}
                    className="mt-2"
                    rows={15}
                  />
                  <p className="text-sm text-gray-500 mt-2">
                    You can use HTML tags for formatting
                  </p>
                </CardContent>
              </Card>
            </div>
          )}
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Author Info */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Author Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="authorName">Author Name *</Label>
                <Input
                  id="authorName"
                  placeholder="Your name"
                  value={formData.authorName}
                  onChange={(e) => handleInputChange('authorName', e.target.value)}
                  className="mt-1"
                />
              </div>
              
              <div>
                <Label htmlFor="authorType">Author Type</Label>
                <select
                  id="authorType"
                  value={formData.authorType}
                  onChange={(e) => handleInputChange('authorType', e.target.value as AuthorType)}
                  className="mt-1 w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                >
                  <option value={AuthorType.VENDOR}>Vendor</option>
                  <option value={AuthorType.EXPERT}>Expert</option>
                  <option value={AuthorType.ADMIN}>Admin</option>
                </select>
              </div>
            </CardContent>
          </Card>

          {/* Category */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Category</CardTitle>
            </CardHeader>
            <CardContent>
              <select
                value={formData.category}
                onChange={(e) => handleInputChange('category', e.target.value as BlogCategory)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
              >
                {Object.entries(BLOG_CATEGORIES).map(([key, category]) => (
                  <option key={key} value={key}>
                    {category.icon} {category.title}
                  </option>
                ))}
              </select>
            </CardContent>
          </Card>

          {/* Featured Image */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Featured Image</CardTitle>
            </CardHeader>
            <CardContent>
              {formData.featuredImage ? (
                <div className="relative">
                  <img
                    src={formData.featuredImage}
                    alt="Featured"
                    className="w-full h-32 object-cover rounded-lg"
                  />
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleInputChange('featuredImage', '')}
                    className="absolute top-2 right-2"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              ) : (
                <div>
                  <input
                    type="file"
                    accept="image/*"
                    onChange={handleImageUpload}
                    className="hidden"
                    id="image-upload"
                  />
                  <label
                    htmlFor="image-upload"
                    className="flex flex-col items-center justify-center w-full h-32 border-2 border-dashed border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50"
                  >
                    <ImageIcon className="h-8 w-8 text-gray-400 mb-2" />
                    <span className="text-sm text-gray-500">Click to upload image</span>
                  </label>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Admin-only Features */}
          {isAdmin && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Admin Features</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="isFeatured"
                    checked={formData.isFeatured || false}
                    onChange={(e) => handleInputChange('isFeatured', e.target.checked)}
                    className="rounded border-gray-300 text-primary focus:ring-primary"
                  />
                  <Label htmlFor="isFeatured">Featured Blog</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="isPinned"
                    checked={formData.isPinned || false}
                    onChange={(e) => handleInputChange('isPinned', e.target.checked)}
                    className="rounded border-gray-300 text-primary focus:ring-primary"
                  />
                  <Label htmlFor="isPinned">Pinned Blog</Label>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Tags */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Tags</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex gap-2">
                <Input
                  placeholder="Add a tag"
                  value={newTag}
                  onChange={(e) => setNewTag(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && handleAddTag()}
                />
                <Button onClick={handleAddTag} size="sm">
                  <Plus className="h-4 w-4" />
                </Button>
              </div>
              
              {formData.tags && formData.tags.length > 0 && (
                <div className="flex flex-wrap gap-2">
                  {formData.tags.map((tag, index) => (
                    <Badge key={index} variant="outline" className="flex items-center gap-1">
                      #{tag}
                      <button
                        onClick={() => handleRemoveTag(tag)}
                        className="ml-1 hover:text-red-600"
                      >
                        <X className="h-3 w-3" />
                      </button>
                    </Badge>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
