'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { CheckCircle, XCircle, AlertCircle, ExternalLink } from 'lucide-react';
import EntityReviewService from '@/lib/services/entityReviewService';

interface TestResult {
  name: string;
  status: 'pass' | 'fail' | 'warning';
  message: string;
  data?: any;
}

export default function TestEntityReviews() {
  const [testResults, setTestResults] = useState<TestResult[]>([]);
  const [testing, setTesting] = useState(false);
  const [testEntityId, setTestEntityId] = useState('test-shop-1');

  const runTests = async () => {
    setTesting(true);
    const results: TestResult[] = [];

    // Test 1: Test getEntityReviews method
    try {
      console.log('Testing getEntityReviews...');
      const reviewsResult = await EntityReviewService.getEntityReviews(testEntityId, {
        limit: 10
      });
      
      results.push({
        name: 'Get Entity Reviews',
        status: 'pass',
        message: `Successfully fetched ${reviewsResult.reviews.length} reviews`,
        data: reviewsResult
      });
    } catch (error) {
      results.push({
        name: 'Get Entity Reviews',
        status: 'fail',
        message: `Error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        data: error
      });
    }

    // Test 2: Test getEntityReviewStats method
    try {
      console.log('Testing getEntityReviewStats...');
      const statsResult = await EntityReviewService.getEntityReviewStats(testEntityId);
      
      results.push({
        name: 'Get Entity Review Stats',
        status: 'pass',
        message: `Successfully calculated stats: ${statsResult.totalReviews} reviews, ${statsResult.averageRating} avg rating`,
        data: statsResult
      });
    } catch (error) {
      results.push({
        name: 'Get Entity Review Stats',
        status: 'fail',
        message: `Error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        data: error
      });
    }

    // Test 3: Test createEntityReview method (without actually creating)
    try {
      console.log('Testing createEntityReview method structure...');
      const testReviewData = {
        rating: 5,
        title: 'Test Review',
        review: 'This is a test review',
        entityType: 'SHOP',
        entityId: testEntityId,
        reviewTarget: 'VENDOR' as const
      };

      // Just test the method exists and can be called (will fail due to auth, but that's expected)
      try {
        await EntityReviewService.createEntityReview(testReviewData);
        results.push({
          name: 'Create Entity Review Method',
          status: 'pass',
          message: 'Method exists and is callable'
        });
      } catch (authError) {
        // Expected to fail due to authentication, but method exists
        results.push({
          name: 'Create Entity Review Method',
          status: 'warning',
          message: 'Method exists but requires authentication (expected)'
        });
      }
    } catch (error) {
      results.push({
        name: 'Create Entity Review Method',
        status: 'fail',
        message: `Method structure error: ${error instanceof Error ? error.message : 'Unknown error'}`
      });
    }

    // Test 4: Test GraphQL query availability
    try {
      console.log('Testing GraphQL imports...');
      const { listReviews } = await import('@/src/graphql/queries');
      if (listReviews) {
        results.push({
          name: 'GraphQL Queries Import',
          status: 'pass',
          message: 'listReviews query imported successfully'
        });
      } else {
        results.push({
          name: 'GraphQL Queries Import',
          status: 'fail',
          message: 'listReviews query not found'
        });
      }
    } catch (error) {
      results.push({
        name: 'GraphQL Queries Import',
        status: 'fail',
        message: `Import error: ${error instanceof Error ? error.message : 'Unknown error'}`
      });
    }

    setTestResults(results);
    setTesting(false);
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pass': return <CheckCircle className="w-5 h-5 text-green-600" />;
      case 'fail': return <XCircle className="w-5 h-5 text-red-600" />;
      case 'warning': return <AlertCircle className="w-5 h-5 text-yellow-600" />;
      default: return <AlertCircle className="w-5 h-5 text-gray-600" />;
    }
  };

  const getStatusBadge = (status: string) => {
    const colors = {
      pass: 'bg-green-100 text-green-800',
      fail: 'bg-red-100 text-red-800',
      warning: 'bg-yellow-100 text-yellow-800'
    };
    return colors[status as keyof typeof colors] || 'bg-gray-100 text-gray-800';
  };

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <span>Entity Review Service Test</span>
            <Badge variant="outline">GraphQL Query Fix Test</Badge>
          </CardTitle>
          <p className="text-gray-600">
            Test the EntityReviewService with the fixed listReviews query instead of reviewsByEntityId.
          </p>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center space-x-4">
            <div className="flex-1">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Test Entity ID
              </label>
              <Input
                value={testEntityId}
                onChange={(e) => setTestEntityId(e.target.value)}
                placeholder="Enter entity ID to test"
              />
            </div>
            <Button onClick={runTests} disabled={testing} className="mt-6">
              {testing ? 'Running Tests...' : 'Run Tests'}
            </Button>
          </div>

          {testResults.length > 0 && (
            <div className="space-y-3">
              <h3 className="text-lg font-semibold">Test Results</h3>
              {testResults.map((result, index) => (
                <div key={index} className="border rounded-lg p-4">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center space-x-3">
                      {getStatusIcon(result.status)}
                      <div>
                        <p className="font-medium">{result.name}</p>
                        <p className="text-sm text-gray-600">{result.message}</p>
                      </div>
                    </div>
                    <Badge className={getStatusBadge(result.status)}>
                      {result.status.toUpperCase()}
                    </Badge>
                  </div>
                  
                  {result.data && (
                    <details className="mt-2">
                      <summary className="text-sm text-blue-600 cursor-pointer hover:text-blue-800">
                        View Details
                      </summary>
                      <pre className="mt-2 p-2 bg-gray-100 rounded text-xs overflow-auto max-h-40">
                        {JSON.stringify(result.data, null, 2)}
                      </pre>
                    </details>
                  )}
                </div>
              ))}
            </div>
          )}

          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h4 className="font-semibold text-blue-900 mb-2">Fix Applied</h4>
            <div className="text-sm text-blue-800 space-y-1">
              <p>✅ <strong>EntityReviewService.getEntityReviews():</strong> Now uses listReviews with entityId filter</p>
              <p>✅ <strong>VendorReviewService.getReviewsForEntity():</strong> Now uses listReviews with entityId filter</p>
              <p>✅ <strong>Fallback Strategy:</strong> Uses working GraphQL queries instead of problematic reviewsByEntityId</p>
              <p>⚠️ <strong>Backend Status:</strong> reviewsByEntityId query exists but may not be deployed properly</p>
            </div>
          </div>

          <div className="bg-green-50 border border-green-200 rounded-lg p-4">
            <h4 className="font-semibold text-green-900 mb-2">Expected Results</h4>
            <div className="text-sm text-green-800 space-y-1">
              <p>• <strong>Get Entity Reviews:</strong> Should pass with 0 or more reviews</p>
              <p>• <strong>Get Entity Review Stats:</strong> Should pass with calculated statistics</p>
              <p>• <strong>Create Entity Review Method:</strong> Should show warning (auth required)</p>
              <p>• <strong>GraphQL Queries Import:</strong> Should pass with successful import</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
