import React, { useState, useEffect } from 'react';
import { Alert } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Button } from './ui/Button';
import { useAuth } from '../providers/AuthProvider';
import { useFavorites } from '../providers/FavoritesProvider';
import { useNavigation } from '@react-navigation/native';

interface FavoriteButtonProps {
  entityId: string;
  entityType: 'vendor' | 'venue' | 'product';
  entityData: {
    name: string;
    image?: string;
    price?: string;
    location?: string;
    city?: string;
    state?: string;
    rating?: number;
    reviewCount?: number;
    description?: string;
  };
  variant?: 'default' | 'outline' | 'ghost';
  size?: 'default' | 'sm' | 'lg' | 'icon';
  showText?: boolean;
  onToggle?: (isFavorited: boolean) => void;
}

export function FavoriteButton({
  entityId,
  entityType,
  entityData,
  variant = 'outline',
  size = 'icon',
  showText = false,
  onToggle,
}: FavoriteButtonProps) {
  const { isAuthenticated, user } = useAuth();
  const { isFavorite, addToFavorites, removeFromFavorites } = useFavorites();
  const navigation = useNavigation();
  const [loading, setLoading] = useState(false);

  const isFavorited = isFavorite(entityId, entityType);

  const handleToggleFavorite = async () => {
    if (!isAuthenticated) {
      Alert.alert(
        'Login Required',
        'Please login to add items to your favorites.',
        [
          { text: 'Cancel', style: 'cancel' },
          { 
            text: 'Login', 
            onPress: () => navigation.navigate('Login' as never)
          },
        ]
      );
      return;
    }

    try {
      setLoading(true);

      if (isFavorited) {
        await removeFromFavorites(entityId, entityType);
      } else {
        await addToFavorites({
          id: entityId,
          type: entityType,
          name: entityData.name,
          image: entityData.image,
          rating: entityData.rating,
          location: entityData.location || entityData.city,
          price: entityData.price,
        });
      }

      onToggle?.(!isFavorited);
    } catch (error) {
      console.error('Error toggling favorite:', error);
      Alert.alert('Error', 'Failed to update favorites. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const getIconName = () => {
    if (loading) return 'heart-outline';
    return isFavorited ? 'heart' : 'heart-outline';
  };

  const getIconColor = () => {
    if (isFavorited) return '#E53E3E';
    if (variant === 'outline' || variant === 'ghost') return undefined;
    return '#fff';
  };

  if (size === 'icon' && !showText) {
    return (
      <Button
        onPress={handleToggleFavorite}
        variant={variant}
        size={size}
        disabled={loading}
        loading={loading}
        icon={getIconName()}
      />
    );
  }

  return (
    <Button
      title={showText ? (isFavorited ? 'Remove from Favorites' : 'Add to Favorites') : undefined}
      onPress={handleToggleFavorite}
      variant={variant}
      size={size}
      disabled={loading}
      loading={loading}
      icon={getIconName()}
      iconPosition="left"
    />
  );
}
