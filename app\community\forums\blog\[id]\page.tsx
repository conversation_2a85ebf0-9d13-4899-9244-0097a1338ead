"use client";

import { useEffect, useState } from "react";
import { TopHeader } from "@/components/top-header";
import { Header } from "@/components/header";
import { Footer } from "@/components/footer";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Separator } from "@/components/ui/separator";
import {
  Calendar,
  Clock,
  Heart,
  Share2,
  Bookmark,
  MessageCircle,
  Eye,
  ArrowLeft,
  ArrowRight,
  Pin,
  Star,
} from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { useParams } from "next/navigation";
import {
  BlogService,
  BLOG_CATEGORIES,
  Blog,
  BlogCategory,
} from "@/lib/services/blogService";
import { useAuth } from "@/contexts/AuthContext";

export default function BlogDetailPage() {
  const params = useParams();
  const { user } = useAuth();
  const [blog, setBlog] = useState<Blog | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [relatedBlogs, setRelatedBlogs] = useState<Blog[]>([]);
  const [shareClicked, setShareClicked] = useState(false);
  const [isShared, setIsShared] = useState(false);

  useEffect(() => {
    if (params.id) {
      loadBlog(params.id as string);
    }
  }, [params.id]);

  const loadBlog = async (id: string) => {
    try {
      setIsLoading(true);
      const blogData = await BlogService.getBlog(id);
      setBlog(blogData);

      // Increment view count
      await BlogService.incrementViews(id);

      // Load related blogs from same category
      if (blogData.category) {
        const related = await BlogService.listBlogsByCategory(
          blogData.category as BlogCategory,
          3
        );
        setRelatedBlogs(related.blogs.filter((b) => b.id !== id));
      }
    } catch (error) {
      console.error("Error loading blog:", error);
    } finally {
      setIsLoading(false);
    }
  };
  // Handle share
  const handleShare = async () => {
    setShareClicked(true);
    setIsShared(true);
    try {
      if (navigator.share) {
        await navigator.share({
          title: blog?.title || "Check out this Blog",
          text: `Read this amazing Blog: ${blog?.title}`,
          url: window.location.href,
        });
      } else {
        // Fallback: copy to clipboard
        await navigator.clipboard.writeText(window.location.href);
        showToast.success("Link copied to clipboard!");
      }
    } catch (error) {
      console.error("Error sharing:", error);
      showToast.error("Failed to share");
    } finally {
      setTimeout(() => setIsShared(false), 1000);
      setTimeout(() => setShareClicked(false), 250);
    }
  };
  // Animation classes for creative effect
  const shareAnimClass = shareClicked ? "scale-125 -rotate-6 shadow-lg" : "";

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <TopHeader />
        <Header />
        <div className="container mx-auto px-4 py-12">
          <div className="max-w-4xl mx-auto">
            <div className="animate-pulse">
              <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
              <div className="h-12 bg-gray-200 rounded w-3/4 mb-6"></div>
              <div className="h-64 bg-gray-200 rounded mb-8"></div>
              <div className="space-y-4">
                <div className="h-4 bg-gray-200 rounded"></div>
                <div className="h-4 bg-gray-200 rounded w-5/6"></div>
                <div className="h-4 bg-gray-200 rounded w-4/6"></div>
              </div>
            </div>
          </div>
        </div>
        <Footer />
      </div>
    );
  }

  if (!blog) {
    return (
      <div className="min-h-screen bg-gray-50">
        <TopHeader />
        <Header />
        <div className="container mx-auto px-4 py-12">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-2xl font-bold mb-4">Blog Post Not Found</h1>
            <p className="text-gray-600 mb-6">
              The blog post you're looking for doesn't exist or has been
              removed.
            </p>
            <Link href="/community/forums">
              <Button>
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Forums
              </Button>
            </Link>
          </div>
        </div>
        <Footer />
      </div>
    );
  }

  const category = BLOG_CATEGORIES[blog.category as BlogCategory];
  const publishedDate = blog.publishedAt
    ? new Date(blog.publishedAt).toLocaleDateString("en-US", {
        year: "numeric",
        month: "2-digit",
        day: "2-digit",
      })
    : "Draft";

  const getReadTime = (content: string) => {
    const wordsPerMinute = 200;
    const words = content.split(" ").length;
    const minutes = Math.ceil(words / wordsPerMinute);
    return `${minutes} min read`;
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <TopHeader />
      <Header />

      <article className="py-8">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            {/* Back Button */}
            <div className="mb-6 mt-6">
              <Link href="/community/forums">
                <Button variant="outline" size="sm">
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Back to Forums
                </Button>
              </Link>
            </div>

            {/* Article Header */}
            <header className="mb-6 md:mb-8">
              <div className="flex flex-wrap items-center gap-2 mb-3 md:mb-4">
                <Badge className={category?.color}>
                  {category?.icon} {category?.title}
                </Badge>
                {blog.isPinned && <Pin className="w-4 h-4 text-primary" />}
                {blog.isFeatured && (
                  <Star className="w-4 h-4 text-yellow-500" />
                )}
              </div>

              <h1 className="text-3xl md:text-4xl font-bold mb-4">
                {blog.title}
              </h1>

              {blog.excerpt && (
                <p className="text-lg md:text-xl text-gray-600 mb-4 md:mb-6 leading-relaxed">{blog.excerpt}</p>
              )}

              {/* Author and Meta Info */}
              <div className="flex flex-col gap-4 mb-4 md:mb-6">
                <div className="flex items-center space-x-3 md:space-x-4">
                  <Avatar className="w-10 h-10 md:w-12 md:h-12">
                    <AvatarFallback>{blog.authorName.charAt(0)}</AvatarFallback>
                  </Avatar>
                  <div>
                    <div className="flex items-center space-x-2">
                      <h3 className="font-semibold">{blog.authorName}</h3>
                      {blog.authorType === "VENDOR" && (
                        <Badge variant="outline" className="text-xs">
                          Vendor
                        </Badge>
                      )}
                    </div>
                    <p className="text-xs md:text-sm text-gray-600">Wedding Expert</p>
                  </div>
                </div>

                <div className="flex flex-wrap items-center gap-3 md:gap-4 text-xs md:text-sm text-gray-600">
                  <div className="flex items-center">
                    <Calendar className="h-3 w-3 md:h-4 md:w-4 mr-1" />
                    {publishedDate}
                  </div>
                  <div className="flex items-center">
                    <Clock className="h-3 w-3 md:h-4 md:w-4 mr-1" />
                    {getReadTime(blog.content)}
                  </div>
                  <div className="flex items-center">
                    <Eye className="h-3 w-3 md:h-4 md:w-4 mr-1" />
                    {blog.views || 0}
                  </div>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex items-center gap-3 mb-4">
                <Button variant="outline" size="sm">
                  <Heart className="h-4 w-4 mr-2" />
                  {blog.likes || 0}
                </Button>
                <Button
                  size="sm"
                  variant="secondary"
                  className={`bg-white/90 hover:bg-white transition-all duration-300 ${
                    isShared ? "text-[#8B0000]" : "text-[#CD5C5C]"
                  } ${shareAnimClass}`}
                  onClick={handleShare}
                >
                  <Share2
                    className={`h-10 w-10 ${
                      isShared ? "fill-current" : ""
                    } transition-transform duration-300`}
                  />
                </Button>
                <Button variant="outline" size="sm">
                  <Bookmark className="h-4 w-4 mr-2" />
                  Save
                </Button>
              </div>

              {/* Featured Image */}
              {blog.featuredImage && (
                <div className="relative mb-6 md:mb-8">
                  <Image
                    src={blog.featuredImage}
                    alt={blog.title}
                    width={800}
                    height={400}
                    className="w-full h-48 md:h-64 lg:h-96 object-cover rounded-lg"
                  />
                </div>
              )}
            </header>

            {/* Article Content */}
            <div className="prose prose-lg max-w-none mb-12">
              <div
                className="text-gray-700 leading-relaxed"
                dangerouslySetInnerHTML={{
                  __html: blog.content
                    .replace(/\\n/g, "<br>")
                    .replace(/\n/g, "<br>"),
                }}
              />
            </div>

            {/* Tags */}
            {blog.tags && blog.tags.length > 0 && (
              <div className="mb-6 md:mb-8">
                <h3 className="font-semibold mb-2 md:mb-3 text-sm md:text-base">Tags</h3>
                <div className="flex flex-wrap gap-1 md:gap-2">
                  {blog.tags.map((tag, index) => (
                    <Badge key={index} variant="outline" className="text-xs">
                      #{tag}
                    </Badge>
                  ))}
                </div>
              </div>
            )}

            <Separator className="my-8" />

            {/* Author Bio */}
            <div className="mb-6 md:mb-8">
              <div className="flex flex-col md:flex-row items-start space-y-3 md:space-y-0 md:space-x-4">
                <Avatar className="w-12 h-12 md:w-16 md:h-16 mx-auto md:mx-0">
                  <AvatarFallback>{blog.authorName.charAt(0)}</AvatarFallback>
                </Avatar>
                <div className="flex-1">
                  <div className="flex items-center space-x-2 mb-2">
                    <h3 className="text-xl font-semibold">{blog.authorName}</h3>
                    {blog.authorType === "VENDOR" && (
                      <Badge variant="outline">Vendor</Badge>
                    )}
                  </div>
                  <p className="text-gray-600 mb-4">
                    Experienced wedding professional with expertise in{" "}
                    {category?.title.toLowerCase()}. Dedicated to helping
                    couples create their perfect wedding day.
                  </p>
                  <div className="flex flex-wrap justify-center md:justify-start gap-2 md:gap-3">
                    <Button variant="outline" size="sm" className="text-xs md:text-sm">
                      Follow
                    </Button>
                    <Button variant="outline" size="sm" className="text-xs md:text-sm">
                      View Profile
                    </Button>
                  </div>
                </div>
              </div>
            </div>

            {/* Comments Section */}
            <div className="mb-6 md:mb-8">
              <h3 className="text-xl md:text-2xl font-bold mb-4 md:mb-6 flex items-center">
                <MessageCircle className="h-5 w-5 md:h-6 md:w-6 mr-2" />
                Comments ({blog.comments || 0})
              </h3>

              <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 text-center">
                <p className="text-blue-800 mb-4">
                  Comments feature coming soon! Share your thoughts and
                  questions about this article.
                </p>
                <Button
                  variant="outline"
                  className="border-blue-300 text-blue-700 hover:bg-blue-100"
                >
                  <MessageCircle className="h-4 w-4 mr-2" />
                  Leave a Comment
                </Button>
              </div>
            </div>

            {/* Related Posts */}
            {relatedBlogs.length > 0 && (
              <div>
                <h3 className="text-xl md:text-2xl font-bold mb-4 md:mb-6">Related Articles</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6">
                  {relatedBlogs.map((relatedBlog) => (
                    <Card
                      key={relatedBlog.id}
                      className="overflow-hidden hover:shadow-lg transition-shadow"
                    >
                      {relatedBlog.featuredImage && (
                        <div className="relative">
                          <Image
                            src={relatedBlog.featuredImage}
                            alt={relatedBlog.title}
                            width={300}
                            height={200}
                            className="w-full h-32 md:h-48 object-cover"
                          />
                        </div>
                      )}
                      <CardContent className="p-4">
                        <Badge variant="secondary" className="mb-2 text-xs">
                          {
                            BLOG_CATEGORIES[
                              relatedBlog.category as BlogCategory
                            ]?.title
                          }
                        </Badge>
                        <h4 className="font-bold text-lg mb-2 line-clamp-2">
                          {relatedBlog.title}
                        </h4>
                        <p className="text-gray-600 text-sm mb-3 line-clamp-2">
                          {relatedBlog.excerpt}
                        </p>
                        <div className="flex items-center justify-between">
                          <span className="text-xs text-gray-500">
                            {getReadTime(relatedBlog.content)}
                          </span>
                          <Link
                            href={`/community/forums/blog/${relatedBlog.id}`}
                          >
                            <Button
                              variant="ghost"
                              size="sm"
                              className="text-primary hover:text-primary/90"
                            >
                              Read More <ArrowRight className="h-4 w-4 ml-1" />
                            </Button>
                          </Link>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      </article>

      <Footer />
    </div>
  );
}
