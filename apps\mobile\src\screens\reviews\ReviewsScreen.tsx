import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  RefreshControl,
} from 'react-native';
import { useNavigation, useRoute } from '@react-navigation/native';
import { useTheme } from '../../providers/ThemeProvider';
import { Card } from '../../components/ui/Card';
import { Badge } from '../../components/ui/Badge';
import { Button } from '../../components/ui/Button';

export default function ReviewsScreen() {
  const navigation = useNavigation();
  const route = useRoute();
  const { theme } = useTheme();
  const { entityType, entityId } = (route.params as any) || {};
  
  const [refreshing, setRefreshing] = useState(false);
  const [reviews, setReviews] = useState([
    {
      id: '1',
      userName: 'Priya Sharma',
      userAvatar: 'PS',
      rating: 5,
      date: '2024-02-15',
      title: 'Excellent Photography Service',
      comment: 'Amazing work by the team! They captured every moment beautifully. The photos came out stunning and the service was very professional. Highly recommended for wedding photography.',
      helpful: 12,
      images: ['photo1.jpg', 'photo2.jpg'],
    },
    {
      id: '2',
      userName: '<PERSON>esh <PERSON>',
      userAvatar: 'RK',
      rating: 4,
      date: '2024-02-10',
      title: 'Good Service, Minor Issues',
      comment: 'Overall good experience. The team was punctual and professional. However, there were some minor issues with the lighting setup. But the final results were satisfactory.',
      helpful: 8,
      images: [],
    },
    {
      id: '3',
      userName: 'Meera Patel',
      userAvatar: 'MP',
      rating: 5,
      date: '2024-02-08',
      title: 'Perfect Wedding Memories',
      comment: 'They made our special day even more memorable! The photographers were very friendly and made us feel comfortable. The album quality is excellent.',
      helpful: 15,
      images: ['photo3.jpg'],
    },
  ]);

  const onRefresh = async () => {
    setRefreshing(true);
    // TODO: Fetch latest reviews
    setTimeout(() => setRefreshing(false), 1000);
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, index) => (
      <Text
        key={index}
        style={[
          styles.star,
          { color: index < rating ? theme.colors.accent : theme.colors.border }
        ]}
      >
        ★
      </Text>
    ));
  };

  const averageRating = reviews.reduce((sum, review) => sum + review.rating, 0) / reviews.length;
  const ratingDistribution = [5, 4, 3, 2, 1].map(rating => ({
    rating,
    count: reviews.filter(review => review.rating === rating).length,
    percentage: (reviews.filter(review => review.rating === rating).length / reviews.length) * 100,
  }));

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    scrollContainer: {
      padding: 16,
      gap: 16,
    },
    header: {
      marginBottom: 8,
    },
    title: {
      fontSize: 24,
      fontWeight: 'bold',
      color: theme.colors.text,
      marginBottom: 4,
    },
    subtitle: {
      fontSize: 16,
      color: theme.colors.textSecondary,
    },
    summaryCard: {
      padding: 20,
      alignItems: 'center',
    },
    averageRating: {
      fontSize: 48,
      fontWeight: 'bold',
      color: theme.colors.primary,
      marginBottom: 8,
    },
    starsContainer: {
      flexDirection: 'row',
      marginBottom: 8,
    },
    star: {
      fontSize: 20,
      marginHorizontal: 2,
    },
    totalReviews: {
      fontSize: 14,
      color: theme.colors.textSecondary,
    },
    distributionContainer: {
      marginTop: 16,
      width: '100%',
    },
    distributionRow: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: 8,
    },
    distributionRating: {
      fontSize: 14,
      color: theme.colors.text,
      width: 20,
    },
    distributionBar: {
      flex: 1,
      height: 8,
      backgroundColor: theme.colors.muted,
      borderRadius: 4,
      marginHorizontal: 8,
      overflow: 'hidden',
    },
    distributionFill: {
      height: '100%',
      backgroundColor: theme.colors.accent,
    },
    distributionCount: {
      fontSize: 14,
      color: theme.colors.textSecondary,
      width: 30,
      textAlign: 'right',
    },
    reviewCard: {
      padding: 16,
      marginBottom: 12,
    },
    reviewHeader: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: 12,
    },
    userAvatar: {
      width: 40,
      height: 40,
      borderRadius: 20,
      backgroundColor: theme.colors.primary,
      alignItems: 'center',
      justifyContent: 'center',
      marginRight: 12,
    },
    avatarText: {
      fontSize: 16,
      fontWeight: 'bold',
      color: theme.colors.primaryForeground,
    },
    reviewUserInfo: {
      flex: 1,
    },
    userName: {
      fontSize: 16,
      fontWeight: '600',
      color: theme.colors.text,
      marginBottom: 2,
    },
    reviewDate: {
      fontSize: 12,
      color: theme.colors.textSecondary,
    },
    reviewRating: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    reviewTitle: {
      fontSize: 16,
      fontWeight: '600',
      color: theme.colors.text,
      marginBottom: 8,
    },
    reviewComment: {
      fontSize: 14,
      color: theme.colors.textSecondary,
      lineHeight: 20,
      marginBottom: 12,
    },
    reviewFooter: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingTop: 12,
      borderTopWidth: 1,
      borderTopColor: theme.colors.border,
    },
    helpfulContainer: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    helpfulText: {
      fontSize: 12,
      color: theme.colors.textSecondary,
      marginLeft: 4,
    },
    writeReviewButton: {
      marginTop: 8,
    },
    emptyContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      padding: 40,
    },
    emptyIcon: {
      fontSize: 64,
      color: theme.colors.textSecondary,
      marginBottom: 16,
    },
    emptyTitle: {
      fontSize: 20,
      fontWeight: 'bold',
      color: theme.colors.text,
      marginBottom: 8,
      textAlign: 'center',
    },
    emptyText: {
      fontSize: 16,
      color: theme.colors.textSecondary,
      textAlign: 'center',
      lineHeight: 24,
    },
  });

  if (reviews.length === 0) {
    return (
      <View style={styles.container}>
        <View style={styles.emptyContainer}>
          <Text style={styles.emptyIcon}>⭐</Text>
          <Text style={styles.emptyTitle}>No Reviews Yet</Text>
          <Text style={styles.emptyText}>
            Be the first to share your experience
          </Text>
          <Button
            onPress={() =>
              navigation.navigate('WriteReview' as never, {
                entityType,
                entityId,
              })
            }
            style={styles.writeReviewButton}
          >
            Write First Review
          </Button>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <ScrollView
        contentContainerStyle={styles.scrollContainer}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        <View style={styles.header}>
          <Text style={styles.title}>Reviews</Text>
          <Text style={styles.subtitle}>
            {reviews.length} review{reviews.length !== 1 ? 's' : ''}
          </Text>
        </View>

        <Card style={styles.summaryCard}>
          <Text style={styles.averageRating}>
            {averageRating.toFixed(1)}
          </Text>
          <View style={styles.starsContainer}>
            {renderStars(Math.round(averageRating))}
          </View>
          <Text style={styles.totalReviews}>
            Based on {reviews.length} reviews
          </Text>

          <View style={styles.distributionContainer}>
            {ratingDistribution.map((dist) => (
              <View key={dist.rating} style={styles.distributionRow}>
                <Text style={styles.distributionRating}>{dist.rating}</Text>
                <View style={styles.distributionBar}>
                  <View
                    style={[
                      styles.distributionFill,
                      { width: `${dist.percentage}%` },
                    ]}
                  />
                </View>
                <Text style={styles.distributionCount}>{dist.count}</Text>
              </View>
            ))}
          </View>
        </Card>

        <Button
          onPress={() =>
            navigation.navigate('WriteReview' as never, {
              entityType,
              entityId,
            })
          }
          style={styles.writeReviewButton}
        >
          Write a Review
        </Button>

        {reviews.map((review) => (
          <Card key={review.id} style={styles.reviewCard}>
            <View style={styles.reviewHeader}>
              <View style={styles.userAvatar}>
                <Text style={styles.avatarText}>{review.userAvatar}</Text>
              </View>
              <View style={styles.reviewUserInfo}>
                <Text style={styles.userName}>{review.userName}</Text>
                <Text style={styles.reviewDate}>{review.date}</Text>
              </View>
              <View style={styles.reviewRating}>
                {renderStars(review.rating)}
              </View>
            </View>

            <Text style={styles.reviewTitle}>{review.title}</Text>
            <Text style={styles.reviewComment}>{review.comment}</Text>

            <View style={styles.reviewFooter}>
              <View style={styles.helpfulContainer}>
                <Text>👍</Text>
                <Text style={styles.helpfulText}>
                  {review.helpful} found this helpful
                </Text>
              </View>
              <Button
                variant="ghost"
                size="sm"
                onPress={() =>
                  navigation.navigate('ReviewDetail' as never, {
                    reviewId: review.id,
                  })
                }
              >
                View Details
              </Button>
            </View>
          </Card>
        ))}
      </ScrollView>
    </View>
  );
}
