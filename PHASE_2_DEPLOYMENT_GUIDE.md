# 🚀 Phase 2 Deployment Guide - Custom Mutations & Lambda Functions

## ✅ **PHASE 2 READY FOR DEPLOYMENT**

### **📋 What's Added in Phase 2:**
- ✅ **Custom Mutations** - sendEmail, generateInvoice, createSubscription, etc.
- ✅ **Custom Queries** - getEmailStats, getVendorSubscription, etc.
- ✅ **Lambda Function Integrations** - All functions already implemented
- ✅ **Real-time Subscriptions** - Email status updates, invoice generation, subscription changes
- ✅ **Input/Output Types** - Complete type definitions for all operations

### **🎯 Custom Operations Added:**

#### **📧 Email Mutations:**
- `sendEmail(input: SendEmailInput!)` - Send individual emails with 10 template types
- `sendBulkEmail(input: SendBulkEmailInput!)` - Send bulk emails for marketing
- `subscribeToNewsletter(email: String!, preferences: EmailPreferences)` - Newsletter signup
- `unsubscribeFromNewsletter(email: String!)` - Newsletter unsubscribe

#### **📄 Invoice Mutations:**
- `generateInvoice(input: GenerateInvoiceInput!)` - Generate PDF invoices with company branding
- `downloadInvoice(invoiceId: ID!)` - Download invoice PDF from S3

#### **💰 Pricing Mutations:**
- `createSubscription(input: CreateSubscriptionInput!)` - Create vendor subscriptions
- `cancelSubscription(input: CancelSubscriptionInput!)` - Cancel subscriptions
- `skipPricingDuringSignup(vendorId: ID!)` - Skip pricing during vendor signup
- `renewSubscription(subscriptionId: ID!)` - Renew expired subscriptions

#### **📊 Custom Queries:**
- `getEmailStats(startDate, endDate, emailType)` - Email delivery analytics
- `getUserEmailHistory(userId, limit, nextToken)` - User email history
- `getInvoicesByCustomer(customerId, limit, nextToken)` - Customer invoices
- `getInvoicesByVendor(vendorId, limit, nextToken)` - Vendor invoices
- `getVendorSubscription(vendorId)` - Current vendor subscription
- `getSubscriptionPayments(subscriptionId, limit, nextToken)` - Payment history
- `getSubscriptionStats(startDate, endDate)` - Subscription analytics

#### **🔔 Real-time Subscriptions:**
- `onEmailStatusUpdate(userId)` - Real-time email delivery status
- `onInvoiceGenerated(customerId)` - Real-time invoice generation
- `onSubscriptionStatusChange(vendorId)` - Real-time subscription updates

## 🚀 **Deployment Instructions**

### **1. Deploy Phase 2 Schema**
```bash
# Navigate to project root
cd /Users/<USER>/Documents/Thiru/Thirumanam

# Deploy the updated schema with custom operations
amplify push

# When prompted, confirm the changes:
# - New Lambda functions will be created
# - Custom mutations and queries will be added
# - Real-time subscriptions will be enabled
```

### **2. Lambda Function Dependencies**
After deployment, install required dependencies for each Lambda function:

```bash
# sendEmail Lambda dependencies
cd amplify/backend/function/sendEmail-dev/src
npm init -y
npm install aws-sdk uuid

# generateInvoice Lambda dependencies  
cd ../generateInvoice-dev/src
npm init -y
npm install aws-sdk pdfkit uuid

# createSubscription Lambda dependencies
cd ../createSubscription-dev/src
npm init -y
npm install aws-sdk uuid

# Deploy Lambda functions
cd /Users/<USER>/Documents/Thiru/Thirumanam
amplify push function
```

### **3. Environment Variables**
Set these environment variables in AWS Lambda console for each function:

**sendEmail-dev:**
```env
FROM_EMAIL=<EMAIL>
AWS_REGION=ap-south-1
EMAILLOG_TABLE=EmailLog-dev-XXXXXXXXXXXX
EMAILTEMPLATE_TABLE=EmailTemplate-dev-XXXXXXXXXXXX
```

**generateInvoice-dev:**
```env
STORAGE_BUCKET=BookmyFestive-storage
AWS_REGION=ap-south-1
INVOICE_TABLE=Invoice-dev-XXXXXXXXXXXX
```

**createSubscription-dev:**
```env
VENDORSUBSCRIPTION_TABLE=VendorSubscription-dev-XXXXXXXXXXXX
PRICINGPLAN_TABLE=PricingPlan-dev-XXXXXXXXXXXX
SUBSCRIPTIONPAYMENT_TABLE=SubscriptionPayment-dev-XXXXXXXXXXXX
AWS_REGION=ap-south-1
```

### **4. Test Custom Operations**
After deployment, test the custom operations:

```typescript
// Test sendEmail mutation
import { generateClient } from '@aws-amplify/api';
const client = generateClient();

const emailResult = await client.graphql({
  query: `
    mutation SendEmail($input: SendEmailInput!) {
      sendEmail(input: $input) {
        success
        messageId
        emailLogId
        error
      }
    }
  `,
  variables: {
    input: {
      emailType: 'LOGIN_OTP',
      recipient: '<EMAIL>',
      templateData: JSON.stringify({
        userName: 'Test User',
        otp: '123456'
      })
    }
  }
});

// Test generateInvoice mutation
const invoiceResult = await client.graphql({
  query: `
    mutation GenerateInvoice($input: GenerateInvoiceInput!) {
      generateInvoice(input: $input) {
        success
        invoiceId
        invoiceNumber
        pdfUrl
        error
      }
    }
  `,
  variables: {
    input: {
      type: 'PRODUCT_ORDER',
      customerId: 'customer-123',
      customerName: 'There',
      customerEmail: '<EMAIL>',
      items: [{
        id: 'item-1',
        name: 'Wedding Decoration',
        quantity: 1,
        unitPrice: 50000,
        totalPrice: 50000
      }],
      subtotal: 50000,
      taxAmount: 9000,
      totalAmount: 59000,
      paymentStatus: 'PAID'
    }
  }
});

// Test getVendorSubscription query
const subscriptionResult = await client.graphql({
  query: `
    query GetVendorSubscription($vendorId: ID!) {
      getVendorSubscription(vendorId: $vendorId) {
        id
        status
        startDate
        endDate
        amount
      }
    }
  `,
  variables: {
    vendorId: 'vendor-123'
  }
});
```

## 📊 **Verification Checklist**

### **After Phase 2 Deployment, Verify:**
- [ ] All Lambda functions deployed successfully
- [ ] Custom mutations available in GraphQL API
- [ ] Custom queries working correctly
- [ ] Real-time subscriptions functioning
- [ ] Email sending working (test with sample data)
- [ ] Invoice generation creating PDFs
- [ ] Subscription management working
- [ ] Environment variables set correctly
- [ ] Dependencies installed in Lambda functions
- [ ] Error handling and logging working

## 🎉 **What Phase 2 Enables**

### **📧 Complete Email System:**
- Professional email templates for all 10 use cases
- Real-time email delivery tracking and analytics
- Bulk email capabilities for marketing campaigns
- Newsletter subscription management

### **📄 Invoice System:**
- Automatic PDF invoice generation with company branding
- Secure invoice storage and download from S3
- Support for all business types (products, venues, vendors)
- Payment status tracking and updates

### **💰 Subscription System:**
- Complete vendor subscription lifecycle management
- Payment processing integration (ready for Razorpay/Stripe)
- Subscription analytics and reporting
- Automatic billing and renewal handling

### **🔔 Real-time Features:**
- Live email delivery status updates
- Instant invoice generation notifications
- Real-time subscription status changes
- Enhanced user experience with live updates

## 🚨 **Important Notes**

1. **Lambda Function Names** will include environment suffix (e.g., `sendEmail-dev`)
2. **Table Names** will include random suffix - update environment variables accordingly
3. **Test All Operations** thoroughly before production use
4. **Monitor CloudWatch Logs** for any deployment or runtime issues
5. **Set Up Proper IAM Permissions** for Lambda functions to access DynamoDB, SES, and S3

**🎊 Phase 2 deployment will complete your email system with full functionality!**

## 📞 **Next Steps After Phase 2**

1. **Populate Email Templates** - Add default templates to EmailTemplate table
2. **Configure Pricing Plans** - Add subscription plans to PricingPlan table
3. **Test Email Delivery** - Verify all 10 email types work correctly
4. **Set Up Monitoring** - Configure CloudWatch alarms for Lambda functions
5. **Production Deployment** - Deploy to production environment when ready

**Your complete email system with Lambda functions is ready for deployment! 🚀**
