"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Package, Search, Calendar, CreditCard, Truck, Eye, Edit, RefreshCw, Filter, MapPin, Phone, Mail, AlertCircle } from "lucide-react"
import { OrderService, OrderData } from "@/lib/services/orderService"
import { useAuth } from "@/contexts/AuthContext"
import { showToast } from "@/lib/toast"

export default function VendorOrdersPage() {
  const { isAuthenticated, user, isVendor, userType, userProfile } = useAuth()
  const router = useRouter()
  const [orders, setOrders] = useState<OrderData[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState('all')
  const [dateFilter, setDateFilter] = useState('all')
  const [filteredOrders, setFilteredOrders] = useState<OrderData[]>([])
  const [selectedOrder, setSelectedOrder] = useState<OrderData | null>(null)
  const [isUpdateDialogOpen, setIsUpdateDialogOpen] = useState(false)
  const [updateData, setUpdateData] = useState({
    status: '',
    trackingNumber: '',
    courierPartner: '',
    notes: ''
  })
  const [currentPage, setCurrentPage] = useState(1)
  const [itemsPerPage] = useState(10)

  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/login?redirect=/dashboard/vendor-orders')
      return
    }

    // Check if user is a vendor
    if (!isVendor) {
      router.push('/dashboard')
      showToast.error('Access denied. Vendor account required.')
      return
    }

    loadVendorOrders()
  }, [isAuthenticated, user])

  useEffect(() => {
    filterOrders()
  }, [searchTerm, statusFilter, dateFilter, orders])

  const loadVendorOrders = async () => {
    try {
      setLoading(true)
      // This would be a new method to get orders for vendor's products
      const result = await OrderService.getVendorOrders(user?.userId || '', 100)

      if (result.success) {
        setOrders(result.orders || [])
      } else {
        showToast.error('Failed to load orders')
        setOrders([]) // Set empty array as fallback
      }
    } catch (error) {
      console.error('Error loading vendor orders:', error)
      showToast.error('Failed to load orders')
      setOrders([]) // Set empty array as fallback
    } finally {
      setLoading(false)
    }
  }

  const filterOrders = () => {
    let filtered = orders

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter(order =>
        order.orderNumber?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        order.customerName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (order.items && order.items.some(item => item.productName?.toLowerCase().includes(searchTerm.toLowerCase())))
      )
    }

    // Status filter
    if (statusFilter !== 'all') {
      filtered = filtered.filter(order => order.status === statusFilter)
    }

    // Date filter
    if (dateFilter !== 'all') {
      const now = new Date()
      const filterDate = new Date()
      
      switch (dateFilter) {
        case 'week':
          filterDate.setDate(now.getDate() - 7)
          break
        case 'month':
          filterDate.setMonth(now.getMonth() - 1)
          break
        case 'quarter':
          filterDate.setMonth(now.getMonth() - 3)
          break
      }
      
      filtered = filtered.filter(order => new Date(order.orderDate) >= filterDate)
    }

    setFilteredOrders(filtered)
  }

  const handleUpdateOrder = async () => {
    if (!selectedOrder) return

    try {
      let result
      try {
        // Try the main update method first
        result = await OrderService.updateOrderStatus(
          selectedOrder.id,
          updateData.status as any,
          {
            trackingNumber: updateData.trackingNumber,
            courierPartner: updateData.courierPartner,
            vendorNotes: updateData.notes
          }
        )
      } catch (updateError) {
        console.log('Main update failed, using simple update:', updateError.message)
        // Fallback to simple update method
        result = await OrderService.updateOrderStatusSimple(
          selectedOrder.id,
          updateData.status as any,
          {
            trackingNumber: updateData.trackingNumber,
            courierPartner: updateData.courierPartner,
            vendorNotes: updateData.notes
          }
        )
      }

      if (result.success) {
        showToast.success(result.message || 'Order updated successfully')
        setIsUpdateDialogOpen(false)
        loadVendorOrders()
      } else {
        showToast.error(result.message || 'Failed to update order')
      }
    } catch (error) {
      console.error('Error updating order:', error)
      showToast.error('Failed to update order')
    }
  }

  const openUpdateDialog = (order: OrderData) => {
    setSelectedOrder(order)
    setUpdateData({
      status: order.status,
      trackingNumber: order.trackingNumber || '',
      courierPartner: order.courierPartner || '',
      notes: ''
    })
    setIsUpdateDialogOpen(true)
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'PENDING': return 'bg-yellow-100 text-yellow-800'
      case 'CONFIRMED': return 'bg-green-100 text-green-800'
      case 'PROCESSING': return 'bg-blue-100 text-blue-800'
      case 'SHIPPED': return 'bg-purple-100 text-purple-800'
      case 'OUT_FOR_DELIVERY': return 'bg-indigo-100 text-indigo-800'
      case 'DELIVERED': return 'bg-green-100 text-green-800'
      case 'CANCELLED': return 'bg-red-100 text-red-800'
      case 'RETURNED': return 'bg-orange-100 text-orange-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getPaymentStatusColor = (status: string) => {
    switch (status) {
      case 'PAID': return 'bg-green-100 text-green-800'
      case 'COD_PENDING': return 'bg-yellow-100 text-yellow-800'
      case 'COD_COLLECTED': return 'bg-green-100 text-green-800'
      case 'FAILED': return 'bg-red-100 text-red-800'
      case 'PENDING': return 'bg-yellow-100 text-yellow-800'
      case 'REFUNDED': return 'bg-blue-100 text-blue-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getActiveOrders = () => filteredOrders.filter(order =>
    ['PENDING', 'CONFIRMED', 'PROCESSING', 'SHIPPED', 'OUT_FOR_DELIVERY'].includes(order.status)
  )

  const getCompletedOrders = () => filteredOrders.filter(order =>
    ['DELIVERED', 'CANCELLED', 'RETURNED'].includes(order.status)
  )

  // Pagination logic
  const getPaginatedOrders = (orders: OrderData[]) => {
    const startIndex = (currentPage - 1) * itemsPerPage
    const endIndex = startIndex + itemsPerPage
    return orders.slice(startIndex, endIndex)
  }

  const getTotalPages = (orders: OrderData[]) => Math.ceil(orders.length / itemsPerPage)

  const goToPage = (page: number) => {
    setCurrentPage(page)
    window.scrollTo({ top: 0, behavior: 'smooth' })
  }

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#F6C244]"></div>
        </div>
      </div>
    )
  }

  return (
    <div>
      <div className="space-y-8">
        {/* Header */}
        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-4 sm:p-6 border border-blue-100">
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-3 sm:gap-4">
            <div>
              <h1 className="text-2xl sm:text-3xl font-bold text-gray-900 mb-1 flex items-center gap-2">
                <Package className="h-6 w-6 sm:h-8 sm:w-8 text-blue-600" />
                Order Management
              </h1>
              <p className="text-gray-600 text-sm sm:text-base">Monitor and manage all customer orders across the platform</p>
            </div>
            <div className="flex gap-2">
              <Button onClick={loadVendorOrders} variant="outline" className="flex items-center gap-2 text-sm hover:bg-blue-50">
                <RefreshCw className="h-4 w-4" />
                <span className="hidden sm:inline">Refresh Data</span>
                <span className="sm:hidden">Refresh</span>
              </Button>
            </div>
          </div>
        </div>

        {/* Filters */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-4 mb-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="Search orders..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger>
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="PENDING">Pending</SelectItem>
                <SelectItem value="CONFIRMED">Confirmed</SelectItem>
                <SelectItem value="PROCESSING">Processing</SelectItem>
                <SelectItem value="SHIPPED">Shipped</SelectItem>
                <SelectItem value="OUT_FOR_DELIVERY">Out for Delivery</SelectItem>
                <SelectItem value="DELIVERED">Delivered</SelectItem>
                <SelectItem value="CANCELLED">Cancelled</SelectItem>
                <SelectItem value="RETURNED">Returned</SelectItem>
              </SelectContent>
            </Select>
            <Select value={dateFilter} onValueChange={setDateFilter}>
              <SelectTrigger>
                <SelectValue placeholder="Filter by date" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Time</SelectItem>
                <SelectItem value="week">Last Week</SelectItem>
                <SelectItem value="month">Last Month</SelectItem>
                <SelectItem value="quarter">Last 3 Months</SelectItem>
              </SelectContent>
            </Select>
            <div className="flex items-center gap-2 text-sm text-gray-600">
              <Filter className="h-4 w-4" />
              <span>{filteredOrders.length} orders found</span>
            </div>
          </div>
        </div>

        {/* Orders Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredOrders.length === 0 ? (
            <Card>
              <CardContent className="text-center py-12">
                <Package className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No orders found</h3>
                <p className="text-gray-600">No orders for your products at the moment.</p>
              </CardContent>
            </Card>
          ) : (
            <>
              {getPaginatedOrders(filteredOrders).map((order) => (
                <VendorOrderCard
                  key={order.id}
                  order={order}
                  getStatusColor={getStatusColor}
                  getPaymentStatusColor={getPaymentStatusColor}
                  onUpdate={openUpdateDialog}
                />
              ))}

              {/* Pagination Controls */}
              {filteredOrders.length > itemsPerPage && (
                <div className="flex justify-center items-center gap-4 pt-6">
                  <Button
                    variant="outline"
                    onClick={() => goToPage(currentPage - 1)}
                    disabled={currentPage === 1}
                  >
                    Previous
                  </Button>
                  <span className="text-sm text-gray-600">
                    Page {currentPage} of {getTotalPages(filteredOrders)} • {filteredOrders.length} total orders
                  </span>
                  <Button
                    variant="outline"
                    onClick={() => goToPage(currentPage + 1)}
                    disabled={currentPage >= getTotalPages(filteredOrders)}
                  >
                    Next
                  </Button>
                </div>
              )}
            </>
          )}
        </div>
      </div>

      {/* Update Order Dialog */}
      <Dialog open={isUpdateDialogOpen} onOpenChange={setIsUpdateDialogOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Update Order Status</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="status">Order Status</Label>
              <Select value={updateData.status} onValueChange={(value) => setUpdateData(prev => ({ ...prev, status: value }))}>
                <SelectTrigger>
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="CONFIRMED">Confirmed</SelectItem>
                  <SelectItem value="PROCESSING">Processing</SelectItem>
                  <SelectItem value="SHIPPED">Shipped</SelectItem>
                  <SelectItem value="OUT_FOR_DELIVERY">Out for Delivery</SelectItem>
                  <SelectItem value="DELIVERED">Delivered</SelectItem>
                  <SelectItem value="CANCELLED">Cancelled</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="trackingNumber">Tracking Number</Label>
              <Input
                id="trackingNumber"
                value={updateData.trackingNumber}
                onChange={(e) => setUpdateData(prev => ({ ...prev, trackingNumber: e.target.value }))}
                placeholder="Enter tracking number"
              />
            </div>

            <div>
              <Label htmlFor="courierPartner">Courier Partner</Label>
              <Input
                id="courierPartner"
                value={updateData.courierPartner}
                onChange={(e) => setUpdateData(prev => ({ ...prev, courierPartner: e.target.value }))}
                placeholder="e.g., BlueDart, FedEx"
              />
            </div>

            <div>
              <Label htmlFor="notes">Notes</Label>
              <Textarea
                id="notes"
                value={updateData.notes}
                onChange={(e) => setUpdateData(prev => ({ ...prev, notes: e.target.value }))}
                placeholder="Add any notes for the customer"
                rows={3}
              />
            </div>

            <div className="flex gap-3">
              <Button onClick={handleUpdateOrder} className="flex-1">
                Update Order
              </Button>
              <Button variant="outline" onClick={() => setIsUpdateDialogOpen(false)}>
                Cancel
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
}

// Vendor Order Card Component
function VendorOrderCard({ order, getStatusColor, getPaymentStatusColor, onUpdate }: {
  order: OrderData,
  getStatusColor: (status: string) => string,
  getPaymentStatusColor: (status: string) => string,
  onUpdate: (order: OrderData) => void
}) {
  const router = useRouter()

  return (
    <Card className="hover:shadow-lg transition-shadow">
      <CardHeader>
        <div className="flex justify-between items-start">
          <div>
            <CardTitle className="text-lg">Order #{order.orderNumber}</CardTitle>
            <div className="flex items-center gap-4 text-sm text-gray-600 mt-1">
              <span className="flex items-center gap-1">
                <Calendar className="h-4 w-4" />
                {new Date(order.orderDate).toLocaleDateString()}
              </span>
              <span className="flex items-center gap-1 min-w-0">
                <CreditCard className="h-4 w-4 flex-shrink-0" />
                <span className="truncate">{order.paymentMethod}</span>
              </span>
            </div>
          </div>
          <div className="text-right">
            <div className="text-lg font-bold">₹{order.total.toLocaleString()}</div>
            <div className="flex gap-2 mt-1">
              <Badge className={getStatusColor(order.status)}>{order.status}</Badge>
              <Badge className={getPaymentStatusColor(order.paymentStatus)}>
                {order.paymentStatus.replace('_', ' ')}
              </Badge>
            </div>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {/* Customer Information */}
        <div className="bg-gray-50 rounded-lg p-4 mb-4">
          <h4 className="font-semibold text-sm mb-2">Customer Information</h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
            <div className="flex items-center gap-2">
              <span className="font-medium">Name:</span>
              <span>{order.customerName || 'N/A'}</span>
            </div>
            <div className="flex items-center gap-2">
              <Mail className="h-4 w-4 text-gray-400" />
              <span>{order.customerEmail || 'N/A'}</span>
            </div>
            <div className="flex items-center gap-2">
              <Phone className="h-4 w-4 text-gray-400" />
              <span>{order.customerPhone || 'N/A'}</span>
            </div>
            <div className="flex items-center gap-2">
              <MapPin className="h-4 w-4 text-gray-400" />
              <span>
                {order.shippingAddress?.city || 'N/A'}, {order.shippingAddress?.state || 'N/A'}
              </span>
            </div>
          </div>
        </div>

        {/* Order Items */}
        <div className="space-y-3 mb-4">
          <h4 className="font-semibold text-sm">Order Items</h4>
          {order.items && order.items.length > 0 ? (
            order.items.map((item, index) => (
              <div key={index} className="flex items-center space-x-3 p-3 bg-white border rounded-lg">
                {item.productImage && (
                  <img
                    src={item.productImage}
                    alt={item.productName}
                    className="w-12 h-12 object-cover rounded"
                  />
                )}
                <div className="flex-1">
                  <p className="font-medium text-sm">{item.productName}</p>
                  <p className="text-xs text-gray-600">
                    Qty: {item.quantity} • ₹{item.productPrice?.toLocaleString() || '0'} each
                  </p>
                  {item.selectedSize && (
                    <p className="text-xs text-gray-500">Size: {item.selectedSize}</p>
                  )}
                  {item.selectedColor && (
                    <p className="text-xs text-gray-500">Color: {item.selectedColor}</p>
                  )}
                </div>
                <div className="text-sm font-medium">₹{item.subtotal?.toLocaleString() || '0'}</div>
              </div>
            ))
          ) : (
            <p className="text-sm text-gray-500">No items found</p>
          )}
        </div>

        {/* Shipping Information */}
        {order.shippingAddress && (
          <div className="bg-blue-50 rounded-lg p-4 mb-4">
            <h4 className="font-semibold text-sm mb-2">Shipping Address</h4>
            <div className="text-sm text-gray-700">
              <p>{order.shippingAddress.fullName || 'N/A'}</p>
              <p>{order.shippingAddress.addressLine1 || 'N/A'}</p>
              {order.shippingAddress.addressLine2 && <p>{order.shippingAddress.addressLine2}</p>}
              <p>
                {order.shippingAddress.city || 'N/A'}, {order.shippingAddress.state || 'N/A'} {order.shippingAddress.pincode || ''}
              </p>
              <p>{order.shippingAddress.country || 'N/A'}</p>
            </div>
          </div>
        )}

        {/* Tracking Information */}
        {(order.trackingNumber || order.courierPartner) && (
          <div className="bg-purple-50 rounded-lg p-4 mb-4">
            <h4 className="font-semibold text-sm mb-2">Tracking Information</h4>
            <div className="text-sm">
              {order.trackingNumber && (
                <p><span className="font-medium">Tracking Number:</span> {order.trackingNumber}</p>
              )}
              {order.courierPartner && (
                <p><span className="font-medium">Courier Partner:</span> {order.courierPartner}</p>
              )}
            </div>
          </div>
        )}

        {/* Actions */}
        <div className="flex gap-3">
          <Button
            variant="outline"
            size="sm"
            onClick={() => router.push(`/dashboard/order-confirmation?orderId=${order.id}`)}
            className="flex items-center gap-2"
          >
            <Eye className="h-4 w-4" />
            View Details
          </Button>

          {['PENDING', 'CONFIRMED', 'PROCESSING', 'SHIPPED'].includes(order.status) && (
            <Button
              size="sm"
              onClick={() => onUpdate(order)}
              className="flex items-center gap-2 bg-[#F6C244] hover:bg-[#F6C244]/90 text-black"
            >
              <Edit className="h-4 w-4" />
              Update Status
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
