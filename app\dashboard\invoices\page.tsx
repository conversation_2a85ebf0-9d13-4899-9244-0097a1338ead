'use client';

import React from 'react';
import { AuthenticatedRoute } from '@/components/RouteProtection';
import InvoiceViewer from '@/components/dashboard/InvoiceViewer';
import { useAuth } from '@/contexts/AuthContext';

export default function InvoicesPage() {
  const { userProfile } = useAuth();

  // Determine user type for invoice viewing
  const getUserType = () => {
    if (userProfile?.isSuperAdmin || userProfile?.isAdmin) {
      return 'admin';
    } else if (userProfile?.isVendor) {
      return 'vendor';
    } else {
      return 'customer';
    }
  };

  return (
    <AuthenticatedRoute>
      <div className="container mx-auto px-3 sm:px-4 py-4 sm:py-8">
        <InvoiceViewer userType={getUserType()} />
      </div>
    </AuthenticatedRoute>
  );
}
