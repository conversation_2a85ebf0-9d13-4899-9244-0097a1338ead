'use client';

import React, { useState, useEffect } from 'react';
import { AuthenticatedRoute } from '@/components/RouteProtection';
import { useAuth } from '@/contexts/AuthContext';

// Dynamic import to avoid SSR issues
let InvoiceViewer: React.ComponentType<any> | null = null;

export default function InvoicesPage() {
  const { userProfile } = useAuth();
  const [isComponentLoaded, setIsComponentLoaded] = useState(false);

  // Determine user type for invoice viewing
  const getUserType = () => {
    if (userProfile?.isSuperAdmin || userProfile?.isAdmin) {
      return 'admin';
    } else if (userProfile?.isVendor) {
      return 'vendor';
    } else {
      return 'customer';
    }
  };

  useEffect(() => {
    // Dynamically import the InvoiceViewer component
    const loadComponent = async () => {
      try {
        const module = await import('@/components/dashboard/InvoiceViewer');
        InvoiceViewer = module.default;
        setIsComponentLoaded(true);
      } catch (error) {
        console.error('Error loading InvoiceViewer component:', error);
      }
    };

    loadComponent();
  }, []);

  if (!isComponentLoaded) {
    return (
      <AuthenticatedRoute>
        <div className="container mx-auto px-3 sm:px-4 py-4 sm:py-8">
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
          </div>
          <p className="text-center text-gray-600">Loading invoice viewer...</p>
        </div>
      </AuthenticatedRoute>
    );
  }

  return (
    <AuthenticatedRoute>
      <div className="container mx-auto px-3 sm:px-4 py-4 sm:py-8">
        {InvoiceViewer && <InvoiceViewer userType={getUserType()} />}
      </div>
    </AuthenticatedRoute>
  );
}
