import React, { useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
} from 'react-native';
import { useNavigation, useRoute } from '@react-navigation/native';
import { useTheme } from '../../providers/ThemeProvider';
import { Button } from '../../components/ui/Button';
import { Card } from '../../components/ui/Card';

export default function OrderConfirmationScreen() {
  const navigation = useNavigation();
  const route = useRoute();
  const { theme } = useTheme();
  
  const { orderId } = (route.params as any) || {};

  useEffect(() => {
    // Set navigation options to prevent going back
    navigation.setOptions({
      headerLeft: () => null,
      gestureEnabled: false,
    });
  }, [navigation]);

  const orderDetails = {
    orderId: orderId || 'TM360-' + Date.now(),
    orderDate: new Date().toLocaleDateString(),
    estimatedDelivery: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toLocaleDateString(),
    total: 25000,
    items: [
      { name: 'Wedding Photography Package', price: 15000 },
      { name: 'Bridal Makeup Kit', price: 8000 },
      { name: 'Decoration Items', price: 2000 },
    ],
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    scrollContainer: {
      padding: 16,
      gap: 20,
    },
    header: {
      alignItems: 'center',
      marginBottom: 8,
    },
    successIcon: {
      fontSize: 64,
      color: theme.colors.secondary,
      marginBottom: 16,
    },
    title: {
      fontSize: 24,
      fontWeight: 'bold',
      color: theme.colors.text,
      textAlign: 'center',
      marginBottom: 8,
    },
    subtitle: {
      fontSize: 16,
      color: theme.colors.textSecondary,
      textAlign: 'center',
    },
    orderIdContainer: {
      alignItems: 'center',
      padding: 16,
      backgroundColor: theme.colors.primaryForeground,
      borderRadius: 8,
      marginBottom: 8,
    },
    orderIdLabel: {
      fontSize: 14,
      color: theme.colors.textSecondary,
      marginBottom: 4,
    },
    orderId: {
      fontSize: 18,
      fontWeight: 'bold',
      color: theme.colors.primary,
    },
    sectionTitle: {
      fontSize: 18,
      fontWeight: '600',
      color: theme.colors.text,
      marginBottom: 12,
    },
    detailRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingVertical: 8,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border,
    },
    detailLabel: {
      fontSize: 14,
      color: theme.colors.textSecondary,
    },
    detailValue: {
      fontSize: 14,
      fontWeight: '500',
      color: theme.colors.text,
    },
    itemRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingVertical: 12,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border,
    },
    itemName: {
      fontSize: 14,
      color: theme.colors.text,
      flex: 1,
    },
    itemPrice: {
      fontSize: 14,
      fontWeight: '500',
      color: theme.colors.text,
    },
    totalRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingVertical: 12,
      borderTopWidth: 2,
      borderTopColor: theme.colors.border,
      marginTop: 8,
    },
    totalLabel: {
      fontSize: 16,
      fontWeight: 'bold',
      color: theme.colors.text,
    },
    totalValue: {
      fontSize: 18,
      fontWeight: 'bold',
      color: theme.colors.primary,
    },
    nextSteps: {
      padding: 16,
      backgroundColor: theme.colors.secondary + '20',
      borderRadius: 8,
    },
    nextStepsTitle: {
      fontSize: 16,
      fontWeight: '600',
      color: theme.colors.text,
      marginBottom: 8,
    },
    nextStepItem: {
      fontSize: 14,
      color: theme.colors.textSecondary,
      marginBottom: 4,
      paddingLeft: 8,
    },
    buttonContainer: {
      gap: 12,
      marginTop: 24,
    },
  });

  return (
    <View style={styles.container}>
      <ScrollView contentContainerStyle={styles.scrollContainer}>
        <View style={styles.header}>
          <Text style={styles.successIcon}>✅</Text>
          <Text style={styles.title}>Order Confirmed!</Text>
          <Text style={styles.subtitle}>
            Thank you for your order. We'll send you updates via email.
          </Text>
        </View>

        <View style={styles.orderIdContainer}>
          <Text style={styles.orderIdLabel}>Order ID</Text>
          <Text style={styles.orderId}>{orderDetails.orderId}</Text>
        </View>

        <Card>
          <Text style={styles.sectionTitle}>Order Details</Text>
          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Order Date</Text>
            <Text style={styles.detailValue}>{orderDetails.orderDate}</Text>
          </View>
          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Estimated Delivery</Text>
            <Text style={styles.detailValue}>{orderDetails.estimatedDelivery}</Text>
          </View>
        </Card>

        <Card>
          <Text style={styles.sectionTitle}>Order Summary</Text>
          {orderDetails.items.map((item, index) => (
            <View key={index} style={styles.itemRow}>
              <Text style={styles.itemName}>{item.name}</Text>
              <Text style={styles.itemPrice}>₹{item.price.toLocaleString()}</Text>
            </View>
          ))}
          <View style={styles.totalRow}>
            <Text style={styles.totalLabel}>Total</Text>
            <Text style={styles.totalValue}>
              ₹{orderDetails.total.toLocaleString()}
            </Text>
          </View>
        </Card>

        <View style={styles.nextSteps}>
          <Text style={styles.nextStepsTitle}>What's Next?</Text>
          <Text style={styles.nextStepItem}>
            • You'll receive an email confirmation shortly
          </Text>
          <Text style={styles.nextStepItem}>
            • Track your order in the "My Orders" section
          </Text>
          <Text style={styles.nextStepItem}>
            • We'll notify you when your order is ready
          </Text>
        </View>

        <View style={styles.buttonContainer}>
          <Button
            onPress={() => navigation.navigate('Orders' as never)}
            variant="default"
          >
            View My Orders
          </Button>
          
          <Button
            onPress={() => navigation.navigate('MainTabs' as never)}
            variant="outline"
          >
            Continue Shopping
          </Button>
        </View>
      </ScrollView>
    </View>
  );
}
