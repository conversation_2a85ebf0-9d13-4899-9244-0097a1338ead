import React, { useState, useRef, useEffect } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Modal } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useAuth } from '../providers/AuthProvider';
import { useNavigation } from '@react-navigation/native';

export default function UserProfileDropdown() {
  const { user, logout } = useAuth();
  const navigation = useNavigation();
  const [dropdownVisible, setDropdownVisible] = useState(false);
  const [profileDropdownOpen, setProfileDropdownOpen] = useState(false);
  const profileDropdownTimeout = useRef<NodeJS.Timeout | null>(null);

  const handleProfileMouseEnter = () => {
    if (profileDropdownTimeout.current) {
      clearTimeout(profileDropdownTimeout.current);
    }
    setProfileDropdownOpen(true);
  };

  const handleProfileMouseLeave = () => {
    profileDropdownTimeout.current = setTimeout(() => {
      setProfileDropdownOpen(false);
    }, 150);
  };

  const handleLogout = async () => {
    try {
      await logout();
      setDropdownVisible(false);
      (navigation as any).navigate('Home');
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  const handleDashboardPress = () => {
    setDropdownVisible(false);
    (navigation as any).navigate('Dashboard');
  };

  if (!user) {
    return (
      <TouchableOpacity
        style={styles.loginButton}
        onPress={() => (navigation as any).navigate('Login')}
      >
        <Text style={styles.loginButtonText}>Login</Text>
      </TouchableOpacity>
    );
  }

  return (
    <View style={styles.container}>
      <TouchableOpacity
        style={styles.profileButton}
        onPress={() => setDropdownVisible(true)}
        onPressIn={handleProfileMouseEnter}
        onPressOut={handleProfileMouseLeave}
      >
        <View style={styles.profileAvatar}>
          <Text style={styles.profileInitial}>
            {user.fullName?.charAt(0).toUpperCase() || 'U'}
          </Text>
        </View>
      </TouchableOpacity>

      <Modal
        visible={dropdownVisible}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setDropdownVisible(false)}
      >
        <TouchableOpacity
          style={styles.modalOverlay}
          activeOpacity={1}
          onPress={() => setDropdownVisible(false)}
        >
          <View style={styles.dropdownContainer}>
            <View style={styles.dropdownContent}>
              <TouchableOpacity
                style={styles.dropdownItem}
                onPress={handleDashboardPress}
              >
                <Ionicons name="person" size={16} color="#374151" />
                <Text style={styles.dropdownText}>Dashboard</Text>
              </TouchableOpacity>
              
              <TouchableOpacity
                style={styles.dropdownItem}
                onPress={handleLogout}
              >
                <Ionicons name="log-out" size={16} color="#ef4444" />
                <Text style={[styles.dropdownText, { color: '#ef4444' }]}>Logout</Text>
              </TouchableOpacity>
            </View>
          </View>
        </TouchableOpacity>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    position: 'relative',
  },
  profileButton: {
    padding: 8,
    borderRadius: 8,
  },
  profileAvatar: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#610f13',
    alignItems: 'center',
    justifyContent: 'center',
  },
  profileInitial: {
    fontSize: 14,
    fontWeight: '600',
    color: '#ffffff',
  },
  loginButton: {
    backgroundColor: '#610f13',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
  },
  loginButtonText: {
    color: '#ffffff',
    fontSize: 14,
    fontWeight: '600',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-start',
    alignItems: 'flex-end',
    paddingTop: 60,
    paddingRight: 16,
  },
  dropdownContainer: {
    backgroundColor: '#ffffff',
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
    minWidth: 160,
  },
  dropdownContent: {
    padding: 8,
  },
  dropdownItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 4,
  },
  dropdownText: {
    fontSize: 14,
    color: '#374151',
    marginLeft: 12,
  },
}); 