import { generateClient } from 'aws-amplify/api';
import {
  createChecklistCategory,
  updateChecklistCategory,
  deleteChecklistCategory,
  createChecklistItem,
  updateChecklistItem,
  deleteChecklistItem
} from '@/src/graphql/mutations';
import {
  listChecklistCategories,
  checklistCategoriesByUserId,
  checklistItemsByCategoryId,
  checklistItemsByUserId,
  getChecklistItem
} from '@/src/graphql/queries';

const client = generateClient();

export interface ChecklistItem {
  id: string;
  userId?: string | null;
  categoryId: string;
  text: string;
  completed: boolean;
  dueDate?: string | null;
  priority: 'LOW' | 'MEDIUM' | 'HIGH';
  order?: number | null;
  isDefault: boolean;
  createdAt?: string;
  updatedAt?: string;
  __typename?: string;
}

export interface ChecklistCategory {
  id: string;
  userId?: string | null;
  name: string;
  icon: string;
  expanded: boolean;
  order?: number | null;
  isDefault: boolean;
  items?: ChecklistItem[];
  createdAt?: string;
  updatedAt?: string;
  __typename?: string;
}

// Default checklist data for non-logged-in users
export const DEFAULT_CHECKLIST_DATA: ChecklistCategory[] = [
  {
    id: 'default-venue',
    name: 'Venue & Location',
    icon: '🏛️',
    expanded: true,
    isDefault: true,
    order: 1,
    items: [
      { id: 'default-item-1', categoryId: 'default-venue', text: 'Book ceremony venue', completed: false, priority: 'HIGH', isDefault: true, order: 1 },
      { id: 'default-item-2', categoryId: 'default-venue', text: 'Book reception venue', completed: false, priority: 'HIGH', isDefault: true, order: 2 },
      { id: 'default-item-3', categoryId: 'default-venue', text: 'Visit venues in person', completed: false, priority: 'MEDIUM', isDefault: true, order: 3 },
      { id: 'default-item-4', categoryId: 'default-venue', text: 'Sign venue contracts', completed: false, priority: 'HIGH', isDefault: true, order: 4 },
      { id: 'default-item-5', categoryId: 'default-venue', text: 'Arrange parking for guests', completed: false, priority: 'MEDIUM', isDefault: true, order: 5 },
    ]
  },
  {
    id: 'default-vendors',
    name: 'Vendors',
    icon: '👥',
    expanded: true,
    isDefault: true,
    order: 2,
    items: [
      { id: 'default-item-6', categoryId: 'default-vendors', text: 'Hire photographer', completed: false, priority: 'HIGH', isDefault: true, order: 1 },
      { id: 'default-item-7', categoryId: 'default-vendors', text: 'Hire videographer', completed: false, priority: 'MEDIUM', isDefault: true, order: 2 },
      { id: 'default-item-8', categoryId: 'default-vendors', text: 'Book caterer', completed: false, priority: 'HIGH', isDefault: true, order: 3 },
      { id: 'default-item-9', categoryId: 'default-vendors', text: 'Hire DJ/band', completed: false, priority: 'MEDIUM', isDefault: true, order: 4 },
      { id: 'default-item-10', categoryId: 'default-vendors', text: 'Book florist', completed: false, priority: 'MEDIUM', isDefault: true, order: 5 },
      { id: 'default-item-11', categoryId: 'default-vendors', text: 'Hire wedding planner', completed: false, priority: 'LOW', isDefault: true, order: 6 },
    ]
  },
  {
    id: 'default-attire',
    name: 'Attire & Beauty',
    icon: '👗',
    expanded: true,
    isDefault: true,
    order: 3,
    items: [
      { id: 'default-item-12', categoryId: 'default-attire', text: 'Choose and order wedding dress', completed: false, priority: 'HIGH', isDefault: true, order: 1 },
      { id: 'default-item-13', categoryId: 'default-attire', text: 'Book dress fittings', completed: false, priority: 'MEDIUM', isDefault: true, order: 2 },
      { id: 'default-item-14', categoryId: 'default-attire', text: 'Choose groom\'s attire', completed: false, priority: 'MEDIUM', isDefault: true, order: 3 },
      { id: 'default-item-15', categoryId: 'default-attire', text: 'Book hair and makeup artist', completed: false, priority: 'MEDIUM', isDefault: true, order: 4 },
      { id: 'default-item-16', categoryId: 'default-attire', text: 'Choose bridesmaid dresses', completed: false, priority: 'LOW', isDefault: true, order: 5 },
    ]
  },
  {
    id: 'default-ceremony',
    name: 'Ceremony',
    icon: '💒',
    expanded: true,
    isDefault: true,
    order: 4,
    items: [
      { id: 'default-item-17', categoryId: 'default-ceremony', text: 'Choose officiant', completed: false, priority: 'HIGH', isDefault: true, order: 1 },
      { id: 'default-item-18', categoryId: 'default-ceremony', text: 'Write vows', completed: false, priority: 'MEDIUM', isDefault: true, order: 2 },
      { id: 'default-item-19', categoryId: 'default-ceremony', text: 'Choose ceremony music', completed: false, priority: 'MEDIUM', isDefault: true, order: 3 },
      { id: 'default-item-20', categoryId: 'default-ceremony', text: 'Arrange marriage license', completed: false, priority: 'HIGH', isDefault: true, order: 4 },
      { id: 'default-item-21', categoryId: 'default-ceremony', text: 'Plan ceremony timeline', completed: false, priority: 'MEDIUM', isDefault: true, order: 5 },
    ]
  },
  {
    id: 'default-reception',
    name: 'Reception',
    icon: '🎉',
    expanded: true,
    isDefault: true,
    order: 5,
    items: [
      { id: 'default-item-22', categoryId: 'default-reception', text: 'Choose reception menu', completed: false, priority: 'MEDIUM', isDefault: true, order: 1 },
      { id: 'default-item-23', categoryId: 'default-reception', text: 'Order wedding cake', completed: false, priority: 'MEDIUM', isDefault: true, order: 2 },
      { id: 'default-item-24', categoryId: 'default-reception', text: 'Plan reception timeline', completed: false, priority: 'MEDIUM', isDefault: true, order: 3 },
      { id: 'default-item-25', categoryId: 'default-reception', text: 'Choose reception music', completed: false, priority: 'LOW', isDefault: true, order: 4 },
      { id: 'default-item-26', categoryId: 'default-reception', text: 'Arrange guest book', completed: false, priority: 'LOW', isDefault: true, order: 5 },
    ]
  },
  {
    id: 'default-guests',
    name: 'Guests & Invitations',
    icon: '📧',
    expanded: true,
    isDefault: true,
    order: 6,
    items: [
      { id: 'default-item-27', categoryId: 'default-guests', text: 'Create guest list', completed: false, priority: 'HIGH', isDefault: true, order: 1 },
      { id: 'default-item-28', categoryId: 'default-guests', text: 'Design and order invitations', completed: false, priority: 'HIGH', isDefault: true, order: 2 },
      { id: 'default-item-29', categoryId: 'default-guests', text: 'Send save-the-dates', completed: false, priority: 'MEDIUM', isDefault: true, order: 3 },
      { id: 'default-item-30', categoryId: 'default-guests', text: 'Send formal invitations', completed: false, priority: 'MEDIUM', isDefault: true, order: 4 },
      { id: 'default-item-31', categoryId: 'default-guests', text: 'Track RSVPs', completed: false, priority: 'MEDIUM', isDefault: true, order: 5 },
      { id: 'default-item-32', categoryId: 'default-guests', text: 'Arrange guest accommodations', completed: false, priority: 'LOW', isDefault: true, order: 6 },
    ]
  }
];

export class ChecklistService {
  
  /**
   * Get checklist data for user (or default data if not logged in)
   */
  static async getChecklistData(userId?: string): Promise<ChecklistCategory[]> {
    try {
      if (!userId) {
        // Return default data for non-logged-in users
        return DEFAULT_CHECKLIST_DATA;
      }

      // Get user's custom checklist data
      const response = await client.graphql({
        query: checklistCategoriesByUserId,
        variables: {
          userId
        }
      });

      const userCategories = response.data.checklistCategoriesByUserId.items;

      if (userCategories.length === 0) {
        // User has no custom data, initialize with default data
        try {
          await this.initializeUserChecklist(userId);
          return await this.getChecklistData(userId);
        } catch (error) {
          console.error('Error initializing user checklist:', error);
          // If initialization fails, return default data
          return DEFAULT_CHECKLIST_DATA;
        }
      }

      // Get items for each category
      const categoriesWithItems = await Promise.all(
        userCategories.map(async (category: any) => {
          const itemsResponse = await client.graphql({
            query: checklistItemsByCategoryId,
            variables: {
              categoryId: category.id
            }
          });

          return {
            ...category,
            items: itemsResponse.data.checklistItemsByCategoryId.items || []
          };
        })
      );

      return categoriesWithItems.sort((a, b) => (a.order || 0) - (b.order || 0));
    } catch (error) {
      console.error('Error fetching checklist data:', error);
      return DEFAULT_CHECKLIST_DATA;
    }
  }

  /**
   * Initialize user checklist with default data
   */
  static async initializeUserChecklist(userId: string): Promise<void> {
    try {
      // Check if user already has categories to avoid duplicates
      const existingResponse = await client.graphql({
        query: checklistCategoriesByUserId,
        variables: { userId }
      });

      if (existingResponse.data.checklistCategoriesByUserId.items.length > 0) {
        console.log('User already has checklist data, skipping initialization');
        return;
      }

      // Create categories first
      const categoryPromises = DEFAULT_CHECKLIST_DATA.map(async (category) => {
        try {
          const categoryResponse = await client.graphql({
            query: createChecklistCategory,
            variables: {
              input: {
                userId,
                name: category.name,
                icon: category.icon,
                expanded: category.expanded,
                order: category.order,
                isDefault: true
              }
            }
          });

          const createdCategory = categoryResponse.data.createChecklistCategory;

          // Create items for this category
          if (category.items) {
            const itemPromises = category.items.map(async (item) => {
              try {
                return await client.graphql({
                  query: createChecklistItem,
                  variables: {
                    input: {
                      userId,
                      categoryId: createdCategory.id,
                      text: item.text,
                      completed: item.completed,
                      priority: item.priority,
                      order: item.order,
                      isDefault: true
                    }
                  }
                });
              } catch (itemError) {
                console.error('Error creating checklist item:', itemError);
                return null;
              }
            });

            await Promise.all(itemPromises);
          }

          return createdCategory;
        } catch (categoryError) {
          console.error('Error creating checklist category:', categoryError);
          return null;
        }
      });

      await Promise.all(categoryPromises);
      console.log('User checklist initialized successfully');
    } catch (error) {
      console.error('Error initializing user checklist:', error);
      throw error;
    }
  }

  /**
   * Update checklist item
   */
  static async updateChecklistItem(
    itemId: string,
    updates: Partial<ChecklistItem>,
    userId?: string
  ): Promise<ChecklistItem> {
    try {
      // If userId is provided, include it in the update to ensure ownership
      const updateInput: any = {
        id: itemId,
        ...updates
      };

      if (userId) {
        updateInput.userId = userId;
      }

      const response = await client.graphql({
        query: updateChecklistItem,
        variables: {
          input: updateInput
        }
      });

      return response.data.updateChecklistItem;
    } catch (error) {
      console.error('Error updating checklist item:', error);

      // If authorization fails and we have userId, try to recreate the item with user ownership
      if (error.message?.includes('Not Authorized') && userId) {
        console.log('Authorization failed, attempting to recreate item with user ownership...');
        try {
          // Get the current item details first
          const currentItem = await this.getChecklistItemById(itemId);
          if (currentItem) {
            // Delete the old item (if possible)
            try {
              await this.deleteChecklistItem(itemId);
            } catch (deleteError) {
              console.warn('Could not delete old item:', deleteError);
            }

            // Create a new item with the updates and proper userId
            const newItem = await this.createChecklistItem(userId, currentItem.categoryId, {
              ...currentItem,
              ...updates,
              userId
            });

            return newItem;
          }
        } catch (recreateError) {
          console.error('Failed to recreate item:', recreateError);
        }
      }

      throw error;
    }
  }

  /**
   * Get checklist item by ID
   */
  static async getChecklistItemById(itemId: string): Promise<ChecklistItem | null> {
    try {
      const response = await client.graphql({
        query: getChecklistItem,
        variables: { id: itemId }
      });

      return response.data.getChecklistItem;
    } catch (error) {
      console.error('Error getting checklist item:', error);
      return null;
    }
  }

  /**
   * Delete checklist item
   */
  static async deleteChecklistItem(itemId: string): Promise<boolean> {
    try {
      await client.graphql({
        query: deleteChecklistItem,
        variables: {
          input: { id: itemId }
        }
      });
      return true;
    } catch (error) {
      console.error('Error deleting checklist item:', error);
      return false;
    }
  }

  /**
   * Create new checklist item
   */
  static async createChecklistItem(
    userId: string,
    categoryId: string,
    itemData: Partial<ChecklistItem>
  ): Promise<ChecklistItem> {
    try {
      const response = await client.graphql({
        query: createChecklistItem,
        variables: {
          input: {
            userId,
            categoryId, // This should be the ID of the category
            text: itemData.text || '',
            completed: itemData.completed || false,
            priority: itemData.priority || 'MEDIUM',
            order: itemData.order || 0,
            isDefault: false,
            dueDate: itemData.dueDate || null
          }
        }
      });

      return response.data.createChecklistItem;
    } catch (error) {
      console.error('Error creating checklist item:', error);
      throw error;
    }
  }

  /**
   * Delete checklist item
   */
  static async deleteChecklistItem(itemId: string): Promise<void> {
    try {
      await client.graphql({
        query: deleteChecklistItem,
        variables: {
          input: {
            id: itemId
          }
        }
      });
    } catch (error) {
      console.error('Error deleting checklist item:', error);
      throw error;
    }
  }

  /**
   * Update checklist category
   */
  static async updateChecklistCategory(
    categoryId: string,
    updates: Partial<ChecklistCategory>
  ): Promise<ChecklistCategory> {
    try {
      const response = await client.graphql({
        query: updateChecklistCategory,
        variables: {
          input: {
            id: categoryId,
            ...updates
          }
        }
      });

      return response.data.updateChecklistCategory;
    } catch (error) {
      console.error('Error updating checklist category:', error);
      throw error;
    }
  }

  /**
   * Create new checklist category
   */
  static async createChecklistCategory(
    userId: string,
    categoryData: Partial<ChecklistCategory>
  ): Promise<ChecklistCategory> {
    try {
      const response = await client.graphql({
        query: createChecklistCategory,
        variables: {
          input: {
            userId,
            name: categoryData.name || '',
            icon: categoryData.icon || '📝',
            expanded: categoryData.expanded !== undefined ? categoryData.expanded : true,
            order: categoryData.order || 0,
            isDefault: false
          }
        }
      });

      return response.data.createChecklistCategory;
    } catch (error) {
      console.error('Error creating checklist category:', error);
      throw error;
    }
  }

  /**
   * Delete checklist category
   */
  static async deleteChecklistCategory(categoryId: string): Promise<void> {
    try {
      await client.graphql({
        query: deleteChecklistCategory,
        variables: {
          input: {
            id: categoryId
          }
        }
      });
    } catch (error) {
      console.error('Error deleting checklist category:', error);
      throw error;
    }
  }

  /**
   * Get checklist statistics
   */
  static getChecklistStats(categories: ChecklistCategory[]): {
    totalItems: number;
    completedItems: number;
    completionPercentage: number;
    highPriorityPending: number;
  } {
    let totalItems = 0;
    let completedItems = 0;
    let highPriorityPending = 0;

    categories.forEach(category => {
      if (category.items) {
        totalItems += category.items.length;
        category.items.forEach(item => {
          if (item.completed) {
            completedItems++;
          } else if (item.priority === 'HIGH') {
            highPriorityPending++;
          }
        });
      }
    });

    const completionPercentage = totalItems > 0 ? Math.round((completedItems / totalItems) * 100) : 0;

    return {
      totalItems,
      completedItems,
      completionPercentage,
      highPriorityPending
    };
  }
}

export default ChecklistService;
