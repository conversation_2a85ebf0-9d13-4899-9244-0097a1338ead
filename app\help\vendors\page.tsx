"use client"

import { <PERSON>Header } from "@/components/top-header"
import { <PERSON><PERSON> } from "@/components/header"
import { Footer } from "@/components/footer"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { SimpleSEO } from "@/components/seo/SimpleSEO"
import { 
  ArrowLeft,
  Search,
  Filter,
  Star,
  MapPin,
  Phone,
  Mail,
  Calendar,
  Camera,
  Music,
  Utensils,
  Car,
  Flower,
  Palette,
  Users,
  CheckCircle,
  AlertCircle,
  Heart,
  MessageCircle,
  Eye,
  Bookmark
} from "lucide-react"
import Link from "next/link"
import { useHelpTranslations } from '@/lib/universal-i18n'

export default function VendorsHelpPage() {
  const helpT = useHelpTranslations()
  const vendorCategories = [
    {
      icon: Camera,
      title: "Photographers",
      description: "Capture your special moments",
      tips: [
        "Check their portfolio style matches your vision",
        "Ask about engagement shoot packages",
        "Understand delivery timeline for photos",
        "Discuss backup photographer availability"
      ]
    },
    {
      icon: Music,
      title: "Entertainment",
      description: "DJs, bands, and performers",
      tips: [
        "Listen to sample mixes or live performances",
        "Discuss music preferences and restrictions",
        "Confirm equipment and setup requirements",
        "Ask about backup plans for technical issues"
      ]
    },
    {
      icon: Utensils,
      title: "Caterers",
      description: "Food and beverage services",
      tips: [
        "Schedule tasting sessions before booking",
        "Discuss dietary restrictions and preferences",
        "Understand service style and staff requirements",
        "Clarify what's included in the package"
      ]
    },
    {
      icon: Car,
      title: "Transportation",
      description: "Wedding cars and logistics",
      tips: [
        "Book decorated cars well in advance",
        "Confirm pickup and drop-off locations",
        "Discuss backup vehicle availability",
        "Understand decoration and timing policies"
      ]
    },
    {
      icon: Flower,
      title: "Decorators",
      description: "Floral and event decoration",
      tips: [
        "Share your color scheme and theme",
        "Discuss seasonal flower availability",
        "Understand setup and breakdown timing",
        "Ask for detailed decoration proposals"
      ]
    },
    {
      icon: Palette,
      title: "Makeup Artists",
      description: "Bridal and family makeup",
      tips: [
        "Schedule trial sessions before the wedding",
        "Discuss skin type and preferences",
        "Confirm availability for the entire day",
        "Ask about touch-up services during events"
      ]
    }
  ]

  const searchTips = [
    {
      icon: Search,
      title: "Use Specific Keywords",
      description: "Search for specific services like 'wedding photographer Chennai' or 'mehendi artist Mumbai'"
    },
    {
      icon: Filter,
      title: "Apply Smart Filters",
      description: "Use location, budget, and service type filters to narrow down your options"
    },
    {
      icon: Star,
      title: "Check Ratings & Reviews",
      description: "Read recent reviews and check overall ratings to gauge vendor quality"
    },
    {
      icon: MapPin,
      title: "Consider Location",
      description: "Choose vendors familiar with your venue or willing to travel to your location"
    }
  ]

  const evaluationCriteria = [
    {
      title: "Portfolio Quality",
      description: "Review their previous work to ensure it matches your style and quality expectations",
      weight: "High Priority"
    },
    {
      title: "Customer Reviews",
      description: "Read recent reviews focusing on professionalism, quality, and reliability",
      weight: "High Priority"
    },
    {
      title: "Pricing Transparency",
      description: "Ensure clear pricing with no hidden costs and detailed package breakdowns",
      weight: "Medium Priority"
    },
    {
      title: "Communication Style",
      description: "Assess their responsiveness and willingness to understand your requirements",
      weight: "Medium Priority"
    },
    {
      title: "Availability",
      description: "Confirm they're available for your wedding date and any related events",
      weight: "High Priority"
    },
    {
      title: "Backup Plans",
      description: "Understand their contingency plans for emergencies or equipment failures",
      weight: "Medium Priority"
    }
  ]

  const contactingVendors = [
    {
      step: "1. Initial Contact",
      description: "Send a detailed inquiry with your wedding date, venue, and specific requirements",
      tips: [
        "Include your budget range",
        "Mention any special requests",
        "Ask for availability confirmation",
        "Request portfolio samples"
      ]
    },
    {
      step: "2. Compare Responses",
      description: "Evaluate vendor responses based on professionalism and detail",
      tips: [
        "Note response time and quality",
        "Compare pricing and packages",
        "Check if they understood your requirements",
        "Assess their enthusiasm for your project"
      ]
    },
    {
      step: "3. Schedule Meetings",
      description: "Meet shortlisted vendors in person or via video call",
      tips: [
        "Prepare a list of questions",
        "Discuss your vision in detail",
        "Ask for references from recent clients",
        "Understand their working style"
      ]
    },
    {
      step: "4. Make Your Decision",
      description: "Choose based on quality, compatibility, and value for money",
      tips: [
        "Trust your instincts about compatibility",
        "Don't compromise on must-have requirements",
        "Consider the overall package value",
        "Get everything in writing before booking"
      ]
    }
  ]

  const redFlags = [
    "No portfolio or poor quality samples",
    "Unwillingness to provide references",
    "Vague pricing or hidden costs",
    "Poor communication or delayed responses",
    "No written contracts or agreements",
    "Pressure to book immediately",
    "Negative reviews about reliability",
    "No backup plans for emergencies"
  ]

  return (
    <>
      <SimpleSEO
        title="Finding Wedding Vendors Guide - BookmyFestive Help"
        description="Complete guide to finding and hiring the best wedding vendors. Learn how to search, evaluate, and book photographers, caterers, decorators, and more."
        keywords="wedding vendors, wedding photographer, wedding caterer, wedding decorator, vendor booking"
        url="/help/vendors"
      />
      
      <div className="min-h-screen bg-background">
        <TopHeader />
        <Header />

        {/* Header */}
        <section className="py-12">
          <div className="container mx-auto px-4">
            <Link href="/help" className="inline-flex items-center gap-2 text-primary hover:underline mb-6">
              <ArrowLeft className="w-4 h-4" />
              Back to Help Center
            </Link>
            
            <div className="text-center mb-12">
              <Search className="w-16 h-16 mx-auto mb-6 text-primary" />
              <h1 className="text-4xl font-bold mb-4">{helpT.vendors?.title || 'Finding Wedding Vendors'}</h1>
              <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
                {helpT.vendors?.subtitle || 'Your complete guide to finding, evaluating, and hiring the perfect wedding vendors. From photographers to caterers, learn how to make the right choices for your special day.'}
              </p>
            </div>
          </div>
        </section>

        {/* Search Tips */}
        <section className="py-16">
          <div className="container mx-auto px-4">
            <h2 className="text-3xl font-bold text-center mb-12">Smart Search Strategies</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {searchTips.map((tip, index) => (
                <Card key={index} className="text-center hover:shadow-lg transition-shadow">
                  <CardHeader>
                    <tip.icon className="w-12 h-12 mx-auto mb-4 text-primary" />
                    <CardTitle className="text-lg">{tip.title}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-sm text-muted-foreground">{tip.description}</p>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Vendor Categories */}
        <section className="py-16 bg-muted/30">
          <div className="container mx-auto px-4">
            <h2 className="text-3xl font-bold text-center mb-12">Vendor Categories</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {vendorCategories.map((category, index) => (
                <Card key={index} className="hover:shadow-lg transition-shadow">
                  <CardHeader>
                    <div className="flex items-center gap-3">
                      <category.icon className="w-8 h-8 text-primary" />
                      <div>
                        <CardTitle className="text-lg">{category.title}</CardTitle>
                        <p className="text-sm text-muted-foreground">{category.description}</p>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <ul className="space-y-2">
                      {category.tips.map((tip, idx) => (
                        <li key={idx} className="flex items-start gap-2">
                          <CheckCircle className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                          <span className="text-sm">{tip}</span>
                        </li>
                      ))}
                    </ul>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Evaluation Criteria */}
        <section className="py-16">
          <div className="container mx-auto px-4">
            <h2 className="text-3xl font-bold text-center mb-12">How to Evaluate Vendors</h2>
            <div className="max-w-4xl mx-auto space-y-6">
              {evaluationCriteria.map((criteria, index) => (
                <Card key={index}>
                  <CardContent className="pt-6">
                    <div className="flex items-start justify-between gap-4">
                      <div className="flex-1">
                        <h3 className="font-semibold mb-2">{criteria.title}</h3>
                        <p className="text-sm text-muted-foreground">{criteria.description}</p>
                      </div>
                      <Badge variant={criteria.weight === "High Priority" ? "default" : "secondary"}>
                        {criteria.weight}
                      </Badge>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Contacting Process */}
        <section className="py-16 bg-muted/30">
          <div className="container mx-auto px-4">
            <h2 className="text-3xl font-bold text-center mb-12">Contacting Vendors</h2>
            <div className="max-w-4xl mx-auto space-y-8">
              {contactingVendors.map((process, index) => (
                <Card key={index}>
                  <CardHeader>
                    <CardTitle className="text-xl text-primary">{process.step}</CardTitle>
                    <p className="text-muted-foreground">{process.description}</p>
                  </CardHeader>
                  <CardContent>
                    <ul className="space-y-2">
                      {process.tips.map((tip, idx) => (
                        <li key={idx} className="flex items-start gap-2">
                          <CheckCircle className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                          <span className="text-sm">{tip}</span>
                        </li>
                      ))}
                    </ul>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Red Flags */}
        <section className="py-16">
          <div className="container mx-auto px-4">
            <h2 className="text-3xl font-bold text-center mb-12">Red Flags to Avoid</h2>
            <div className="max-w-4xl mx-auto">
              <Card className="border-red-200">
                <CardHeader>
                  <div className="flex items-center gap-3">
                    <AlertCircle className="w-8 h-8 text-red-500" />
                    <div>
                      <CardTitle className="text-red-700">Warning Signs</CardTitle>
                      <p className="text-sm text-muted-foreground">Be cautious if you encounter any of these issues</p>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {redFlags.map((flag, index) => (
                      <div key={index} className="flex items-start gap-2">
                        <AlertCircle className="w-4 h-4 text-red-500 mt-0.5 flex-shrink-0" />
                        <span className="text-sm">{flag}</span>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </section>

        {/* Quick Actions */}
        <section className="py-16 bg-muted/30">
          <div className="container mx-auto px-4 text-center">
            <h2 className="text-3xl font-bold mb-8">Start Finding Vendors</h2>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/vendors">
                <Button size="lg" className="px-8">
                  <Search className="w-5 h-5 mr-2" />
                  Browse All Vendors
                </Button>
              </Link>
              <Link href="/vendors?category=photography">
                <Button variant="outline" size="lg" className="px-8">
                  <Camera className="w-5 h-5 mr-2" />
                  Find Photographers
                </Button>
              </Link>
              <Link href="/vendors?category=catering">
                <Button variant="outline" size="lg" className="px-8">
                  <Utensils className="w-5 h-5 mr-2" />
                  Find Caterers
                </Button>
              </Link>
            </div>
          </div>
        </section>

        <Footer />
      </div>
    </>
  )
}
