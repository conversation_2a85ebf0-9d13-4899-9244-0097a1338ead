import React, { useState, useEffect } from 'react';
import { View, Text, ScrollView, Alert, TouchableOpacity } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Card, CardContent, CardHeader, CardTitle } from './ui/Card';
import { Button } from './ui/Button';
import { Input } from './ui/Input';
import { Badge } from './ui/Badge';
import { useAuth } from '../providers/AuthProvider';
import { useTheme } from '../providers/ThemeProvider';

interface EntityReviewFormProps {
  entityType: 'SHOP' | 'VENUE' | 'VENDOR';
  entityId: string;
  entityName: string;
  onClose: () => void;
  onSubmitted: () => void;
}

interface StarRatingProps {
  rating: number;
  onRatingChange: (rating: number) => void;
  label: string;
  size?: number;
}

function StarRating({ rating, onRatingChange, label, size = 24 }: StarRatingProps) {
  const { theme } = useTheme();
  
  return (
    <View style={{ marginBottom: 16 }}>
      <Text style={{ 
        fontSize: 14, 
        fontWeight: '600', 
        color: theme.colors.text,
        marginBottom: 8 
      }}>
        {label}
      </Text>
      <View style={{ flexDirection: 'row', gap: 4 }}>
        {[1, 2, 3, 4, 5].map((star) => (
          <TouchableOpacity
            key={star}
            onPress={() => onRatingChange(star)}
            style={{ padding: 2 }}
          >
            <Ionicons
              name={star <= rating ? "star" : "star-outline"}
              size={size}
              color={star <= rating ? "#F59E0B" : "#D1D5DB"}
            />
          </TouchableOpacity>
        ))}
        <Text style={{ 
          marginLeft: 8, 
          fontSize: 14, 
          color: theme.colors.textSecondary 
        }}>
          {rating}/5
        </Text>
      </View>
    </View>
  );
}

export function EntityReviewForm({ 
  entityType, 
  entityId, 
  entityName, 
  onClose, 
  onSubmitted 
}: EntityReviewFormProps) {
  const { user, isAuthenticated, userProfile } = useAuth();
  const { theme } = useTheme();
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    location: '',
    weddingDate: '',
    rating: 5,
    serviceRating: 5,
    valueRating: 5,
    communicationRating: 5,
    professionalismRating: 5,
    title: '',
    review: '',
    wouldRecommend: true
  });

  // Pre-fill form with user profile data when authenticated
  useEffect(() => {
    if (isAuthenticated && userProfile) {
      setFormData(prev => ({
        ...prev,
        name: userProfile.fullName || userProfile.firstName || '',
        email: userProfile.email || '',
        location: userProfile.city || userProfile.state || '',
      }));
    }
  }, [isAuthenticated, userProfile]);

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleRatingChange = (field: string, rating: number) => {
    setFormData(prev => ({ ...prev, [field]: rating }));
  };

  const validateForm = () => {
    const required = ['name', 'email', 'title', 'review'];
    const missing = required.filter(field => !formData[field as keyof typeof formData]);
    
    if (missing.length > 0) {
      Alert.alert('Validation Error', `Please fill in: ${missing.join(', ')}`);
      return false;
    }

    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(formData.email)) {
      Alert.alert('Validation Error', 'Please enter a valid email address');
      return false;
    }

    if (formData.review.length < 10) {
      Alert.alert('Validation Error', 'Review must be at least 10 characters long');
      return false;
    }

    return true;
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;

    try {
      setLoading(true);

      const reviewData = {
        ...formData,
        entityType,
        entityId,
        entityName,
        userId: user?.id || null,
        status: 'PENDING',
        submittedAt: new Date().toISOString(),
        source: 'MOBILE_APP',
        overallRating: Math.round((
          formData.rating + 
          formData.serviceRating + 
          formData.valueRating + 
          formData.communicationRating + 
          formData.professionalismRating
        ) / 5),
        metadata: {
          platform: 'React Native',
          userAgent: 'Mobile App'
        }
      };

      // Here you would typically call your review service
      // await EntityReviewService.createReview(reviewData);
      
      onSubmitted();
      
      Alert.alert(
        'Success!', 
        'Your review has been submitted successfully. It will be published after moderation.',
        [
          { text: 'OK', onPress: onClose }
        ]
      );

    } catch (error) {
      console.error('Error submitting review:', error);
      Alert.alert('Error', 'Failed to submit review. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const getEntityIcon = () => {
    switch (entityType) {
      case 'SHOP': return 'storefront';
      case 'VENUE': return 'business';
      case 'VENDOR': return 'person';
      default: return 'star';
    }
  };

  return (
    <ScrollView style={{ flex: 1 }}>
      <Card style={{ margin: 16 }}>
        <CardHeader>
          <View style={{ flexDirection: 'row', alignItems: 'center', gap: 8 }}>
            <Ionicons name={getEntityIcon()} size={24} color={theme.colors.primary} />
            <CardTitle>Write a Review</CardTitle>
          </View>
          <View style={{ flexDirection: 'row', alignItems: 'center', gap: 8, marginTop: 8 }}>
            <Text style={{ fontSize: 14, color: theme.colors.textSecondary }}>
              Review for:
            </Text>
            <Badge variant="default" size="sm">
              {entityName}
            </Badge>
            <Badge variant="outline" size="sm">
              {entityType}
            </Badge>
          </View>
        </CardHeader>

        <CardContent>
          <View style={{ gap: 16 }}>
            {/* Personal Information */}
            <Text style={{ 
              fontSize: 16, 
              fontWeight: '600', 
              color: theme.colors.text,
              marginBottom: 8 
            }}>
              Personal Information
            </Text>

            <Input
              label="Your Name *"
              value={formData.name}
              onChangeText={(value) => handleInputChange('name', value)}
              placeholder="Enter your full name"
              leftIcon="person"
            />

            <Input
              label="Email Address *"
              value={formData.email}
              onChangeText={(value) => handleInputChange('email', value)}
              placeholder="Enter your email"
              keyboardType="email-address"
              autoCapitalize="none"
              leftIcon="mail"
            />

            <Input
              label="Location"
              value={formData.location}
              onChangeText={(value) => handleInputChange('location', value)}
              placeholder="Your city/state"
              leftIcon="location"
            />

            <Input
              label="Wedding Date"
              value={formData.weddingDate}
              onChangeText={(value) => handleInputChange('weddingDate', value)}
              placeholder="DD/MM/YYYY"
              leftIcon="calendar"
            />

            {/* Ratings */}
            <Text style={{ 
              fontSize: 16, 
              fontWeight: '600', 
              color: theme.colors.text,
              marginTop: 16,
              marginBottom: 8 
            }}>
              Rate Your Experience
            </Text>

            <StarRating
              label="Overall Rating"
              rating={formData.rating}
              onRatingChange={(rating) => handleRatingChange('rating', rating)}
            />

            <StarRating
              label="Service Quality"
              rating={formData.serviceRating}
              onRatingChange={(rating) => handleRatingChange('serviceRating', rating)}
            />

            <StarRating
              label="Value for Money"
              rating={formData.valueRating}
              onRatingChange={(rating) => handleRatingChange('valueRating', rating)}
            />

            <StarRating
              label="Communication"
              rating={formData.communicationRating}
              onRatingChange={(rating) => handleRatingChange('communicationRating', rating)}
            />

            <StarRating
              label="Professionalism"
              rating={formData.professionalismRating}
              onRatingChange={(rating) => handleRatingChange('professionalismRating', rating)}
            />

            {/* Review Content */}
            <Text style={{ 
              fontSize: 16, 
              fontWeight: '600', 
              color: theme.colors.text,
              marginTop: 16,
              marginBottom: 8 
            }}>
              Your Review
            </Text>

            <Input
              label="Review Title *"
              value={formData.title}
              onChangeText={(value) => handleInputChange('title', value)}
              placeholder="Brief title for your review"
              leftIcon="create"
            />

            <Input
              label="Detailed Review *"
              value={formData.review}
              onChangeText={(value) => handleInputChange('review', value)}
              placeholder="Share your detailed experience, what you liked, areas for improvement, etc."
              multiline
              numberOfLines={6}
              style={{ height: 120, textAlignVertical: 'top' }}
              leftIcon="chatbubble"
            />

            {/* Recommendation */}
            <View style={{ 
              flexDirection: 'row', 
              alignItems: 'center', 
              justifyContent: 'space-between',
              backgroundColor: theme.colors.muted,
              padding: 12,
              borderRadius: 8,
              marginTop: 8
            }}>
              <Text style={{ 
                fontSize: 14, 
                fontWeight: '600', 
                color: theme.colors.text 
              }}>
                Would you recommend this {entityType.toLowerCase()}?
              </Text>
              <TouchableOpacity
                onPress={() => handleInputChange('wouldRecommend', (!formData.wouldRecommend).toString())}
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  gap: 8,
                  backgroundColor: formData.wouldRecommend ? '#10B981' : '#6B7280',
                  paddingHorizontal: 12,
                  paddingVertical: 6,
                  borderRadius: 16,
                }}
              >
                <Ionicons 
                  name={formData.wouldRecommend ? "thumbs-up" : "thumbs-down"} 
                  size={16} 
                  color="#fff" 
                />
                <Text style={{ color: '#fff', fontSize: 12, fontWeight: '600' }}>
                  {formData.wouldRecommend ? 'Yes' : 'No'}
                </Text>
              </TouchableOpacity>
            </View>

            {/* Submit Buttons */}
            <View style={{ flexDirection: 'row', gap: 12, marginTop: 24 }}>
              <Button
                title="Cancel"
                onPress={onClose}
                variant="outline"
                style={{ flex: 1 }}
              />
              <Button
                title={loading ? "Submitting..." : "Submit Review"}
                onPress={handleSubmit}
                disabled={loading}
                loading={loading}
                style={{ flex: 1 }}
                icon="send"
                iconPosition="right"
              />
            </View>
          </View>
        </CardContent>
      </Card>
    </ScrollView>
  );
}
