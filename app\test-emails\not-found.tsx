import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import Link from 'next/link'

export default function NotFound() {
  return (
    <div className="container mx-auto p-6 max-w-2xl">
      <Card>
        <CardHeader>
          <CardTitle className="text-orange-600">Page Not Found</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="p-4 bg-orange-50 border border-orange-200 rounded-lg">
            <p className="text-orange-800 mb-2">
              The email testing page you're looking for doesn't exist.
            </p>
            <p className="text-sm text-orange-600">
              This might be due to:
            </p>
            <ul className="text-sm text-orange-600 mt-2 ml-4 list-disc">
              <li>Incorrect URL</li>
              <li>Page has been moved or removed</li>
              <li><PERSON><PERSON> in the address</li>
            </ul>
          </div>

          <div className="flex gap-3">
            <Link href="/test-emails">
              <Button variant="default">
                Go to Email Testing
              </Button>
            </Link>
            <Link href="/">
              <Button variant="outline">
                Go to Home
              </Button>
            </Link>
          </div>

          <div className="text-sm text-gray-600">
            <p>You can also:</p>
            <ul className="mt-2 ml-4 list-disc">
              <li>Use the command-line test scripts instead</li>
              <li>Check the navigation menu</li>
              <li>Contact support if you need help</li>
            </ul>
          </div>

          <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <h3 className="font-medium text-blue-800 mb-2">Alternative Testing Methods:</h3>
            <p className="text-sm text-blue-600 mb-2">
              Instead of the web interface, you can test email services using the command line:
            </p>
            <div className="bg-gray-100 p-3 rounded font-mono text-sm">
              <div>npm run test:email</div>
              <div>npm run test:booking</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
} 