"use client"

import { TopHeader } from "@/components/top-header"
import { Header } from "@/components/header"
import { Footer } from "@/components/footer"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { SimpleSEO } from "@/components/seo/SimpleSEO"
import { 
  ArrowLeft,
  ShoppingBag,
  Search,
  Filter,
  Star,
  Heart,
  ShoppingCart,
  CreditCard,
  Truck,
  Shield,
  CheckCircle,
  AlertCircle,
  Package,
  RefreshCw,
  Phone,
  Mail,
  Eye,
  Bookmark,
  Gift,
  Tag
} from "lucide-react"
import Link from "next/link"
import { useHelpTranslations } from '@/lib/universal-i18n'

export default function ShoppingHelpPage() {
  const helpT = useHelpTranslations()
  const productCategories = [
    {
      icon: Gift,
      title: "Wedding Attire",
      description: "Bridal wear, groom wear, and family outfits",
      items: [
        "Bridal lehengas and sarees",
        "Groom sherwanis and suits",
        "Family traditional wear",
        "Accessories and jewelry"
      ]
    },
    {
      icon: Package,
      title: "Jewelry & Accessories",
      description: "Wedding jewelry and fashion accessories",
      items: [
        "Bridal jewelry sets",
        "Engagement rings",
        "Hair accessories",
        "Footwear and bags"
      ]
    },
    {
      icon: Heart,
      title: "Decoration Items",
      description: "Wedding decoration and setup items",
      items: [
        "Flower arrangements",
        "Lighting decorations",
        "Backdrop materials",
        "Table decorations"
      ]
    },
    {
      icon: Tag,
      title: "Wedding Favors",
      description: "Gifts and favors for guests",
      items: [
        "Return gifts",
        "Wedding favors",
        "Customized items",
        "Gift packaging"
      ]
    }
  ]

  const shoppingSteps = [
    {
      step: "1. Browse Products",
      description: "Explore our extensive collection of wedding essentials",
      tips: [
        "Use search filters to narrow down options",
        "Check product ratings and reviews",
        "Compare prices from different sellers",
        "Save favorites for later comparison"
      ]
    },
    {
      step: "2. Add to Cart",
      description: "Select your preferred items and add them to cart",
      tips: [
        "Choose correct size and color options",
        "Check product availability and delivery time",
        "Review product details and specifications",
        "Apply available discount coupons"
      ]
    },
    {
      step: "3. Checkout Process",
      description: "Complete your purchase with secure payment",
      tips: [
        "Verify shipping address details",
        "Choose preferred delivery option",
        "Select secure payment method",
        "Review order summary before confirming"
      ]
    },
    {
      step: "4. Track Your Order",
      description: "Monitor your order status and delivery",
      tips: [
        "Check order confirmation email",
        "Track shipment using tracking ID",
        "Prepare for delivery at specified time",
        "Inspect items upon delivery"
      ]
    }
  ]

  const paymentMethods = [
    {
      icon: CreditCard,
      title: "Credit/Debit Cards",
      description: "Secure card payments with instant confirmation",
      features: ["Instant payment", "Secure encryption", "EMI options available"]
    },
    {
      icon: Shield,
      title: "Net Banking",
      description: "Direct bank transfers with high security",
      features: ["Bank-level security", "No additional charges", "Instant confirmation"]
    },
    {
      icon: Package,
      title: "Digital Wallets",
      description: "Quick payments through popular wallet apps",
      features: ["One-click payments", "Cashback offers", "Quick refunds"]
    },
    {
      icon: Truck,
      title: "Cash on Delivery",
      description: "Pay when you receive your order",
      features: ["No advance payment", "Inspect before paying", "Available in select areas"]
    }
  ]

  const shoppingTips = [
    {
      tip: "Read Product Reviews",
      description: "Check recent customer reviews to understand product quality and seller reliability"
    },
    {
      tip: "Compare Prices",
      description: "Compare prices from different sellers and look for the best deals and offers"
    },
    {
      tip: "Check Return Policy",
      description: "Understand the return and exchange policy before making a purchase"
    },
    {
      tip: "Verify Seller Ratings",
      description: "Choose sellers with high ratings and positive customer feedback"
    },
    {
      tip: "Use Filters Effectively",
      description: "Use price, brand, and feature filters to find exactly what you need"
    },
    {
      tip: "Look for Offers",
      description: "Check for discount coupons, seasonal sales, and bundle offers"
    }
  ]

  const deliveryInfo = [
    {
      title: "Standard Delivery",
      timeframe: "5-7 business days",
      description: "Regular delivery for most products",
      cost: "Free for orders above ₹500"
    },
    {
      title: "Express Delivery",
      timeframe: "2-3 business days",
      description: "Faster delivery for urgent orders",
      cost: "Additional charges apply"
    },
    {
      title: "Same Day Delivery",
      timeframe: "Within 24 hours",
      description: "Available in select cities",
      cost: "Premium charges apply"
    },
    {
      title: "Scheduled Delivery",
      timeframe: "Choose your date",
      description: "Deliver on your preferred date",
      cost: "May include additional charges"
    }
  ]

  const returnPolicy = [
    "7-day return policy for most products",
    "Items must be in original condition",
    "Original packaging and tags required",
    "Refund processed within 5-7 business days",
    "Exchange available for size/color issues",
    "Custom-made items are non-returnable",
    "Damaged items can be returned immediately",
    "Return shipping may be charged to customer"
  ]

  return (
    <>
      <SimpleSEO
        title="Festive Shopping Guide - BookmyFestive Help"
        description="Complete guide to shopping for wedding essentials. Learn about product categories, payment methods, delivery options, and return policies."
        keywords="Festive Shopping, wedding products, bridal wear, wedding jewelry, wedding accessories"
        url="/help/shopping"
      />
      
      <div className="min-h-screen bg-background">
        <TopHeader />
        <Header />

        {/* Header */}
        <section className="py-12">
          <div className="container mx-auto px-4">
            <Link href="/help" className="inline-flex items-center gap-2 text-primary hover:underline mb-6">
              <ArrowLeft className="w-4 h-4" />
              Back to Help Center
            </Link>
            
            <div className="text-center mb-12">
              <ShoppingBag className="w-16 h-16 mx-auto mb-6 text-primary" />
              <h1 className="text-4xl font-bold mb-4">{helpT.shopping?.title || 'Festive Shopping Guide'}</h1>
              <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
                {helpT.shopping?.subtitle || 'Your complete guide to shopping for wedding essentials on BookmyFestive. From bridal wear to decorations, learn how to find, compare, and purchase everything you need.'}
              </p>
            </div>
          </div>
        </section>

        {/* Product Categories */}
        <section className="py-16">
          <div className="container mx-auto px-4">
            <h2 className="text-3xl font-bold text-center mb-12">Product Categories</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {productCategories.map((category, index) => (
                <Card key={index} className="hover:shadow-lg transition-shadow">
                  <CardHeader className="text-center">
                    <category.icon className="w-12 h-12 mx-auto mb-4 text-primary" />
                    <CardTitle className="text-lg">{category.title}</CardTitle>
                    <p className="text-sm text-muted-foreground">{category.description}</p>
                  </CardHeader>
                  <CardContent>
                    <ul className="space-y-2">
                      {category.items.map((item, idx) => (
                        <li key={idx} className="flex items-start gap-2">
                          <CheckCircle className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                          <span className="text-sm">{item}</span>
                        </li>
                      ))}
                    </ul>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Shopping Process */}
        <section className="py-16 bg-muted/30">
          <div className="container mx-auto px-4">
            <h2 className="text-3xl font-bold text-center mb-12">How to Shop</h2>
            <div className="max-w-4xl mx-auto space-y-8">
              {shoppingSteps.map((process, index) => (
                <Card key={index}>
                  <CardHeader>
                    <CardTitle className="text-xl text-primary">{process.step}</CardTitle>
                    <p className="text-muted-foreground">{process.description}</p>
                  </CardHeader>
                  <CardContent>
                    <ul className="space-y-2">
                      {process.tips.map((tip, idx) => (
                        <li key={idx} className="flex items-start gap-2">
                          <CheckCircle className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                          <span className="text-sm">{tip}</span>
                        </li>
                      ))}
                    </ul>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Payment Methods */}
        <section className="py-16">
          <div className="container mx-auto px-4">
            <h2 className="text-3xl font-bold text-center mb-12">Payment Options</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {paymentMethods.map((method, index) => (
                <Card key={index} className="text-center hover:shadow-lg transition-shadow">
                  <CardHeader>
                    <method.icon className="w-12 h-12 mx-auto mb-4 text-primary" />
                    <CardTitle className="text-lg">{method.title}</CardTitle>
                    <p className="text-sm text-muted-foreground">{method.description}</p>
                  </CardHeader>
                  <CardContent>
                    <ul className="space-y-2">
                      {method.features.map((feature, idx) => (
                        <li key={idx} className="flex items-center gap-2 justify-center">
                          <CheckCircle className="w-4 h-4 text-green-500" />
                          <span className="text-sm">{feature}</span>
                        </li>
                      ))}
                    </ul>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Shopping Tips */}
        <section className="py-16 bg-muted/30">
          <div className="container mx-auto px-4">
            <h2 className="text-3xl font-bold text-center mb-12">Smart Shopping Tips</h2>
            <div className="max-w-4xl mx-auto space-y-6">
              {shoppingTips.map((tip, index) => (
                <Card key={index}>
                  <CardContent className="pt-6">
                    <div className="flex items-start gap-4">
                      <div className="w-8 h-8 bg-primary/10 rounded-lg flex items-center justify-center flex-shrink-0">
                        <span className="text-primary font-bold">{index + 1}</span>
                      </div>
                      <div>
                        <h3 className="font-semibold mb-2">{tip.tip}</h3>
                        <p className="text-sm text-muted-foreground">{tip.description}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Delivery Information */}
        <section className="py-16">
          <div className="container mx-auto px-4">
            <h2 className="text-3xl font-bold text-center mb-12">Delivery Options</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {deliveryInfo.map((delivery, index) => (
                <Card key={index}>
                  <CardHeader>
                    <CardTitle className="text-lg text-primary">{delivery.title}</CardTitle>
                    <Badge variant="outline">{delivery.timeframe}</Badge>
                  </CardHeader>
                  <CardContent>
                    <p className="text-sm text-muted-foreground mb-3">{delivery.description}</p>
                    <p className="text-sm font-medium">{delivery.cost}</p>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Return Policy */}
        <section className="py-16 bg-muted/30">
          <div className="container mx-auto px-4">
            <h2 className="text-3xl font-bold text-center mb-12">Return & Exchange Policy</h2>
            <div className="max-w-4xl mx-auto">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-3">
                    <RefreshCw className="w-6 h-6 text-primary" />
                    Return Policy Guidelines
                  </CardTitle>
                  <p className="text-muted-foreground">Important information about returns and exchanges</p>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {returnPolicy.map((policy, index) => (
                      <div key={index} className="flex items-start gap-2">
                        <CheckCircle className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                        <span className="text-sm">{policy}</span>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </section>

        {/* Quick Actions */}
        <section className="py-16">
          <div className="container mx-auto px-4 text-center">
            <h2 className="text-3xl font-bold mb-8">Start Shopping</h2>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/shop">
                <Button size="lg" className="px-8">
                  <ShoppingBag className="w-5 h-5 mr-2" />
                  Browse All Products
                </Button>
              </Link>
              <Link href="/shop?category=bridal-wear">
                <Button variant="outline" size="lg" className="px-8">
                  <Gift className="w-5 h-5 mr-2" />
                  Bridal Wear
                </Button>
              </Link>
              <Link href="/shop?category=jewelry">
                <Button variant="outline" size="lg" className="px-8">
                  <Heart className="w-5 h-5 mr-2" />
                  Jewelry
                </Button>
              </Link>
            </div>
          </div>
        </section>

        <Footer />
      </div>
    </>
  )
}
