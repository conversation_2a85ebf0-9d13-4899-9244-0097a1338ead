'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { 
  Search, 
  Filter, 
  Edit, 
  Trash2, 
  Eye, 
  Calendar, 
  User, 
  Mail, 
  Phone, 
  MessageSquare,
  Clock,
  AlertCircle,
  CheckCircle,
  XCircle,
  Star,
  MoreHorizontal,
  Plus,
  RefreshCw,
  Download,
  Loader2
} from 'lucide-react'
import { toast } from 'sonner'
import { inquiryService } from '@/lib/services/inquiryService'
import { useAuth } from '@/contexts/AuthContext'

interface Inquiry {
  id: string
  vendorUserId: string
  vendorId: string
  vendorName: string
  customerUserId?: string
  customerName: string
  customerEmail: string
  customerPhone?: string
  eventDate?: string
  message: string
  inquiryType: 'VENDOR_INQUIRY' | 'VENUE_INQUIRY' | 'SERVICE_QUOTE' | 'AVAILABILITY_CHECK' | 'GENERAL_QUESTION' | 'BOOKING_REQUEST'
  status: 'NEW' | 'CONTACTED' | 'QUOTED' | 'NEGOTIATING' | 'CONFIRMED' | 'COMPLETED' | 'CANCELLED' | 'EXPIRED'
  priority?: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT'
  budget?: string
  guestCount?: string
  venue?: string
  additionalServices?: string[]
  preferredContactTime?: string
  responseMessage?: string
  respondedAt?: string
  assignedTo?: string
  followUpDate?: string
  notes?: string
  createdAt: string
  updatedAt: string
}

export default function AdminInquiryManagement() {
  const { user } = useAuth()
  const [inquiries, setInquiries] = useState<Inquiry[]>([])
  const [filteredInquiries, setFilteredInquiries] = useState<Inquiry[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState('ALL')
  const [typeFilter, setTypeFilter] = useState('ALL')
  const [priorityFilter, setPriorityFilter] = useState('ALL')
  const [selectedInquiry, setSelectedInquiry] = useState<Inquiry | null>(null)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [editFormData, setEditFormData] = useState<Partial<Inquiry>>({})
  const [updating, setUpdating] = useState(false)
  const [deleting, setDeleting] = useState(false)

  // Load inquiries
  const loadInquiries = async () => {
    try {
      setLoading(true)
      const result = await inquiryService.listInquiries({ limit: 100 })

      console.log('Admin inquiry loading result:', result)

      if (result && result.items) {
        setInquiries(result.items)
        setFilteredInquiries(result.items)
        console.log('Loaded inquiries:', result.items.length)
      } else {
        console.error('Invalid result format:', result)
        toast.error('Failed to load inquiries', {
          description: 'Invalid response format'
        })
      }
    } catch (error) {
      console.error('Error loading inquiries:', error)
      toast.error('Error loading inquiries', {
        description: error instanceof Error ? error.message : 'An unexpected error occurred'
      })
    } finally {
      setLoading(false)
    }
  }

  // Filter inquiries
  useEffect(() => {
    let filtered = inquiries

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter(inquiry =>
        inquiry.customerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        inquiry.customerEmail.toLowerCase().includes(searchTerm.toLowerCase()) ||
        inquiry.vendorName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        inquiry.message.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    // Status filter
    if (statusFilter !== 'ALL') {
      filtered = filtered.filter(inquiry => inquiry.status === statusFilter)
    }

    // Type filter
    if (typeFilter !== 'ALL') {
      filtered = filtered.filter(inquiry => inquiry.inquiryType === typeFilter)
    }

    // Priority filter
    if (priorityFilter !== 'ALL') {
      filtered = filtered.filter(inquiry => inquiry.priority === priorityFilter)
    }

    setFilteredInquiries(filtered)
  }, [inquiries, searchTerm, statusFilter, typeFilter, priorityFilter])

  // Load inquiries on component mount
  useEffect(() => {
    loadInquiries()
  }, [])

  // Handle edit inquiry
  const handleEditInquiry = (inquiry: Inquiry) => {
    setSelectedInquiry(inquiry)
    setEditFormData({
      status: inquiry.status,
      priority: inquiry.priority,
      responseMessage: inquiry.responseMessage,
      assignedTo: inquiry.assignedTo,
      followUpDate: inquiry.followUpDate,
      notes: inquiry.notes
    })
    setIsEditDialogOpen(true)
  }

  // Handle view inquiry
  const handleViewInquiry = (inquiry: Inquiry) => {
    setSelectedInquiry(inquiry)
    setIsViewDialogOpen(true)
  }

  // Handle delete inquiry
  const handleDeleteInquiry = (inquiry: Inquiry) => {
    setSelectedInquiry(inquiry)
    setIsDeleteDialogOpen(true)
  }

  // Update inquiry
  const updateInquiry = async () => {
    if (!selectedInquiry) return

    try {
      setUpdating(true)

      // Try basic update first (status and priority only)
      console.log('Attempting basic inquiry update...')

      const basicUpdateData = {
        id: selectedInquiry.id,
        status: editFormData.status,
        priority: editFormData.priority
      }

      let result = await inquiryService.updateInquiryBasic(basicUpdateData)

      // If basic update succeeded, try full update with all fields
      if (result && (editFormData.responseMessage || editFormData.assignedTo ||
                     editFormData.followUpDate || editFormData.notes)) {

        console.log('Basic update succeeded, attempting full update...')

        try {
          result = await inquiryService.updateInquiry({
            id: selectedInquiry.id,
            status: editFormData.status,
            priority: editFormData.priority,
            responseMessage: editFormData.responseMessage,
            assignedTo: editFormData.assignedTo,
            followUpDate: editFormData.followUpDate,
            notes: editFormData.notes
          })

          toast.success('Inquiry updated successfully (all fields)')
        } catch (fullUpdateError) {
          console.warn('Full update failed, but basic update succeeded:', fullUpdateError)
          toast.success('Inquiry updated successfully (basic fields only)', {
            description: 'Some admin fields could not be updated due to permissions'
          })
        }
      } else {
        toast.success('Inquiry updated successfully')
      }

      setIsEditDialogOpen(false)
      await loadInquiries() // Reload inquiries

    } catch (error) {
      console.error('Error updating inquiry:', error)
      toast.error('Error updating inquiry', {
        description: error instanceof Error ? error.message : 'Unknown error occurred'
      })
    } finally {
      setUpdating(false)
    }
  }

  // Delete inquiry
  const deleteInquiry = async () => {
    if (!selectedInquiry) return

    try {
      setDeleting(true)
      
      const result = await inquiryService.deleteInquiry(selectedInquiry.id)

      if (result.success) {
        toast.success('Inquiry deleted successfully')
        setIsDeleteDialogOpen(false)
        await loadInquiries() // Reload inquiries
      } else {
        toast.error('Failed to delete inquiry', {
          description: result.error || 'Unknown error occurred'
        })
      }
    } catch (error) {
      console.error('Error deleting inquiry:', error)
      toast.error('Error deleting inquiry')
    } finally {
      setDeleting(false)
    }
  }



  // Get status badge color
  const getStatusBadgeColor = (status: string) => {
    switch (status) {
      case 'NEW': return 'bg-blue-100 text-blue-800'
      case 'CONTACTED': return 'bg-yellow-100 text-yellow-800'
      case 'QUOTED': return 'bg-purple-100 text-purple-800'
      case 'NEGOTIATING': return 'bg-orange-100 text-orange-800'
      case 'CONFIRMED': return 'bg-green-100 text-green-800'
      case 'COMPLETED': return 'bg-gray-100 text-gray-800'
      case 'CANCELLED': return 'bg-red-100 text-red-800'
      case 'EXPIRED': return 'bg-gray-100 text-gray-600'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  // Get priority badge color
  const getPriorityBadgeColor = (priority?: string) => {
    switch (priority) {
      case 'URGENT': return 'bg-red-100 text-red-800'
      case 'HIGH': return 'bg-orange-100 text-orange-800'
      case 'MEDIUM': return 'bg-yellow-100 text-yellow-800'
      case 'LOW': return 'bg-green-100 text-green-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  // Calculate stats
  const stats = {
    total: inquiries.length,
    new: inquiries.filter(i => i.status === 'NEW').length,
    inProgress: inquiries.filter(i => ['CONTACTED', 'QUOTED', 'NEGOTIATING'].includes(i.status)).length,
    completed: inquiries.filter(i => i.status === 'COMPLETED').length,
    urgent: inquiries.filter(i => i.priority === 'URGENT').length
  }

  if (!user) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Authentication Required</h2>
          <p className="text-gray-600">Please log in to access the admin inquiry management.</p>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8 space-y-8">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Inquiry Management</h1>
          <p className="text-gray-600 mt-1">Manage all customer inquiries and vendor communications</p>
        </div>
        <div className="mt-4 sm:mt-0 flex items-center gap-3">
          <Button
            onClick={loadInquiries}
            disabled={loading}
            variant="outline"
            size="sm"
            className="flex items-center gap-2"
          >
            <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>

          <Badge variant="outline" className="text-sm">
            {filteredInquiries.length} of {inquiries.length} inquiries
          </Badge>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Inquiries</CardTitle>
            <MessageSquare className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.total}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">New</CardTitle>
            <AlertCircle className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">{stats.new}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">In Progress</CardTitle>
            <Clock className="h-4 w-4 text-yellow-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-600">{stats.inProgress}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Completed</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{stats.completed}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Urgent</CardTitle>
            <Star className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{stats.urgent}</div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Filters</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <Label htmlFor="search">Search</Label>
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  id="search"
                  placeholder="Search inquiries..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div>
              <Label htmlFor="status">Status</Label>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="All Statuses" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="ALL">All Statuses</SelectItem>
                  <SelectItem value="NEW">New</SelectItem>
                  <SelectItem value="CONTACTED">Contacted</SelectItem>
                  <SelectItem value="QUOTED">Quoted</SelectItem>
                  <SelectItem value="NEGOTIATING">Negotiating</SelectItem>
                  <SelectItem value="CONFIRMED">Confirmed</SelectItem>
                  <SelectItem value="COMPLETED">Completed</SelectItem>
                  <SelectItem value="CANCELLED">Cancelled</SelectItem>
                  <SelectItem value="EXPIRED">Expired</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="type">Type</Label>
              <Select value={typeFilter} onValueChange={setTypeFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="All Types" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="ALL">All Types</SelectItem>
                  <SelectItem value="VENDOR_INQUIRY">Vendor Inquiry</SelectItem>
                  <SelectItem value="VENUE_INQUIRY">Venue Inquiry</SelectItem>
                  <SelectItem value="SERVICE_QUOTE">Service Quote</SelectItem>
                  <SelectItem value="AVAILABILITY_CHECK">Availability Check</SelectItem>
                  <SelectItem value="GENERAL_QUESTION">General Question</SelectItem>
                  <SelectItem value="BOOKING_REQUEST">Booking Request</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="priority">Priority</Label>
              <Select value={priorityFilter} onValueChange={setPriorityFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="All Priorities" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="ALL">All Priorities</SelectItem>
                  <SelectItem value="URGENT">Urgent</SelectItem>
                  <SelectItem value="HIGH">High</SelectItem>
                  <SelectItem value="MEDIUM">Medium</SelectItem>
                  <SelectItem value="LOW">Low</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Inquiries Table */}
      <Card>
        <CardHeader>
          <CardTitle>Inquiries</CardTitle>
          <CardDescription>
            Manage all customer inquiries and vendor communications
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="h-8 w-8 animate-spin" />
              <span className="ml-2">Loading inquiries...</span>
            </div>
          ) : filteredInquiries.length === 0 ? (
            <div className="text-center py-8">
              <MessageSquare className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No inquiries found</h3>
              <p className="text-gray-600">
                {searchTerm || statusFilter !== 'ALL' || typeFilter !== 'ALL' || priorityFilter !== 'ALL'
                  ? 'Try adjusting your filters to see more results.'
                  : 'No inquiries have been submitted yet.'}
              </p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Customer</TableHead>
                    <TableHead>Vendor</TableHead>
                    <TableHead>Type</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Priority</TableHead>
                    <TableHead>Event Date</TableHead>
                    <TableHead>Created</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredInquiries.map((inquiry) => (
                    <TableRow key={inquiry.id}>
                      <TableCell>
                        <div>
                          <div className="font-medium">{inquiry.customerName}</div>
                          <div className="text-sm text-gray-600">{inquiry.customerEmail}</div>
                          {inquiry.customerPhone && (
                            <div className="text-sm text-gray-600">{inquiry.customerPhone}</div>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="font-medium">{inquiry.vendorName}</div>
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline" className="text-xs">
                          {inquiry.inquiryType.replace('_', ' ')}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <Badge className={`text-xs ${getStatusBadgeColor(inquiry.status)}`}>
                          {inquiry.status}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        {inquiry.priority && (
                          <Badge className={`text-xs ${getPriorityBadgeColor(inquiry.priority)}`}>
                            {inquiry.priority}
                          </Badge>
                        )}
                      </TableCell>
                      <TableCell>
                        {inquiry.eventDate ? (
                          <div className="flex items-center text-sm">
                            <Calendar className="h-4 w-4 mr-1" />
                            {new Date(inquiry.eventDate).toLocaleDateString()}
                          </div>
                        ) : (
                          <span className="text-gray-400">-</span>
                        )}
                      </TableCell>
                      <TableCell>
                        <div className="text-sm">
                          {new Date(inquiry.createdAt).toLocaleDateString()}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleViewInquiry(inquiry)}
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleEditInquiry(inquiry)}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDeleteInquiry(inquiry)}
                            className="text-red-600 hover:text-red-700"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* View Inquiry Dialog */}
      <Dialog open={isViewDialogOpen} onOpenChange={setIsViewDialogOpen}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Inquiry Details</DialogTitle>
            <DialogDescription>
              View complete inquiry information
            </DialogDescription>
          </DialogHeader>
          {selectedInquiry && (
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h3 className="text-lg font-semibold mb-3">Customer Information</h3>
                  <div className="space-y-2">
                    <div className="flex items-center">
                      <User className="h-4 w-4 mr-2 text-gray-500" />
                      <span className="font-medium">{selectedInquiry.customerName}</span>
                    </div>
                    <div className="flex items-center">
                      <Mail className="h-4 w-4 mr-2 text-gray-500" />
                      <span>{selectedInquiry.customerEmail}</span>
                    </div>
                    {selectedInquiry.customerPhone && (
                      <div className="flex items-center">
                        <Phone className="h-4 w-4 mr-2 text-gray-500" />
                        <span>{selectedInquiry.customerPhone}</span>
                      </div>
                    )}
                  </div>
                </div>
                <div>
                  <h3 className="text-lg font-semibold mb-3">Vendor Information</h3>
                  <div className="space-y-2">
                    <div className="flex items-center">
                      <User className="h-4 w-4 mr-2 text-gray-500" />
                      <span className="font-medium">{selectedInquiry.vendorName}</span>
                    </div>
                    <div>
                      <span className="text-sm text-gray-600">Vendor ID: {selectedInquiry.vendorId}</span>
                    </div>
                  </div>
                </div>
              </div>

              <div>
                <h3 className="text-lg font-semibold mb-3">Inquiry Details</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label>Type</Label>
                    <Badge variant="outline" className="mt-1">
                      {selectedInquiry.inquiryType.replace('_', ' ')}
                    </Badge>
                  </div>
                  <div>
                    <Label>Status</Label>
                    <Badge className={`mt-1 ${getStatusBadgeColor(selectedInquiry.status)}`}>
                      {selectedInquiry.status}
                    </Badge>
                  </div>
                  {selectedInquiry.priority && (
                    <div>
                      <Label>Priority</Label>
                      <Badge className={`mt-1 ${getPriorityBadgeColor(selectedInquiry.priority)}`}>
                        {selectedInquiry.priority}
                      </Badge>
                    </div>
                  )}
                  {selectedInquiry.eventDate && (
                    <div>
                      <Label>Event Date</Label>
                      <div className="flex items-center mt-1">
                        <Calendar className="h-4 w-4 mr-2" />
                        {new Date(selectedInquiry.eventDate).toLocaleDateString()}
                      </div>
                    </div>
                  )}
                  {selectedInquiry.budget && (
                    <div>
                      <Label>Budget</Label>
                      <p className="mt-1">{selectedInquiry.budget}</p>
                    </div>
                  )}
                  {selectedInquiry.guestCount && (
                    <div>
                      <Label>Guest Count</Label>
                      <p className="mt-1">{selectedInquiry.guestCount}</p>
                    </div>
                  )}
                  {selectedInquiry.venue && (
                    <div>
                      <Label>Venue</Label>
                      <p className="mt-1">{selectedInquiry.venue}</p>
                    </div>
                  )}
                  {selectedInquiry.preferredContactTime && (
                    <div>
                      <Label>Preferred Contact Time</Label>
                      <p className="mt-1">{selectedInquiry.preferredContactTime}</p>
                    </div>
                  )}
                </div>
              </div>

              <div>
                <Label>Message</Label>
                <div className="mt-2 p-3 bg-gray-50 rounded-lg">
                  <p className="whitespace-pre-wrap">{selectedInquiry.message}</p>
                </div>
              </div>

              {selectedInquiry.additionalServices && selectedInquiry.additionalServices.length > 0 && (
                <div>
                  <Label>Additional Services</Label>
                  <div className="mt-2 flex flex-wrap gap-2">
                    {selectedInquiry.additionalServices.map((service, index) => (
                      <Badge key={index} variant="outline">{service}</Badge>
                    ))}
                  </div>
                </div>
              )}

              {selectedInquiry.responseMessage && (
                <div>
                  <Label>Response Message</Label>
                  <div className="mt-2 p-3 bg-blue-50 rounded-lg">
                    <p className="whitespace-pre-wrap">{selectedInquiry.responseMessage}</p>
                    {selectedInquiry.respondedAt && (
                      <p className="text-sm text-gray-600 mt-2">
                        Responded on: {new Date(selectedInquiry.respondedAt).toLocaleString()}
                      </p>
                    )}
                  </div>
                </div>
              )}

              {selectedInquiry.notes && (
                <div>
                  <Label>Notes</Label>
                  <div className="mt-2 p-3 bg-yellow-50 rounded-lg">
                    <p className="whitespace-pre-wrap">{selectedInquiry.notes}</p>
                  </div>
                </div>
              )}

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-600">
                <div>
                  <Label>Created At</Label>
                  <p className="mt-1">{new Date(selectedInquiry.createdAt).toLocaleString()}</p>
                </div>
                <div>
                  <Label>Last Updated</Label>
                  <p className="mt-1">{new Date(selectedInquiry.updatedAt).toLocaleString()}</p>
                </div>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Edit Inquiry Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Edit Inquiry</DialogTitle>
            <DialogDescription>
              Update inquiry status, priority, and response
            </DialogDescription>
          </DialogHeader>
          {selectedInquiry && (
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="edit-status">Status</Label>
                  <Select
                    value={editFormData.status || ''}
                    onValueChange={(value) => setEditFormData(prev => ({ ...prev, status: value as any }))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="NEW">New</SelectItem>
                      <SelectItem value="CONTACTED">Contacted</SelectItem>
                      <SelectItem value="QUOTED">Quoted</SelectItem>
                      <SelectItem value="NEGOTIATING">Negotiating</SelectItem>
                      <SelectItem value="CONFIRMED">Confirmed</SelectItem>
                      <SelectItem value="COMPLETED">Completed</SelectItem>
                      <SelectItem value="CANCELLED">Cancelled</SelectItem>
                      <SelectItem value="EXPIRED">Expired</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="edit-priority">Priority</Label>
                  <Select
                    value={editFormData.priority || ''}
                    onValueChange={(value) => setEditFormData(prev => ({ ...prev, priority: value as any }))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select priority" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="LOW">Low</SelectItem>
                      <SelectItem value="MEDIUM">Medium</SelectItem>
                      <SelectItem value="HIGH">High</SelectItem>
                      <SelectItem value="URGENT">Urgent</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div>
                <Label htmlFor="edit-assigned-to">Assigned To</Label>
                <Input
                  id="edit-assigned-to"
                  value={editFormData.assignedTo || ''}
                  onChange={(e) => setEditFormData(prev => ({ ...prev, assignedTo: e.target.value }))}
                  placeholder="Assign to team member"
                />
              </div>

              <div>
                <Label htmlFor="edit-follow-up-date">Follow Up Date</Label>
                <Input
                  id="edit-follow-up-date"
                  type="date"
                  value={editFormData.followUpDate || ''}
                  onChange={(e) => setEditFormData(prev => ({ ...prev, followUpDate: e.target.value }))}
                />
              </div>

              <div>
                <Label htmlFor="edit-response-message">Response Message</Label>
                <Textarea
                  id="edit-response-message"
                  value={editFormData.responseMessage || ''}
                  onChange={(e) => setEditFormData(prev => ({ ...prev, responseMessage: e.target.value }))}
                  placeholder="Enter response message..."
                  rows={4}
                />
              </div>

              <div>
                <Label htmlFor="edit-notes">Internal Notes</Label>
                <Textarea
                  id="edit-notes"
                  value={editFormData.notes || ''}
                  onChange={(e) => setEditFormData(prev => ({ ...prev, notes: e.target.value }))}
                  placeholder="Add internal notes..."
                  rows={3}
                />
              </div>
            </div>
          )}
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsEditDialogOpen(false)}
              disabled={updating}
            >
              Cancel
            </Button>
            <Button
              onClick={updateInquiry}
              disabled={updating}
            >
              {updating ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Updating...
                </>
              ) : (
                'Update Inquiry'
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Inquiry Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Inquiry</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this inquiry? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          {selectedInquiry && (
            <div className="py-4">
              <div className="bg-gray-50 p-4 rounded-lg">
                <h4 className="font-medium">{selectedInquiry.customerName}</h4>
                <p className="text-sm text-gray-600">{selectedInquiry.customerEmail}</p>
                <p className="text-sm text-gray-600 mt-1">
                  Inquiry to: {selectedInquiry.vendorName}
                </p>
                <p className="text-sm text-gray-600">
                  Created: {new Date(selectedInquiry.createdAt).toLocaleDateString()}
                </p>
              </div>
            </div>
          )}
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsDeleteDialogOpen(false)}
              disabled={deleting}
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={deleteInquiry}
              disabled={deleting}
            >
              {deleting ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Deleting...
                </>
              ) : (
                'Delete Inquiry'
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
