import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  FlatList,
  Alert,
  RefreshControl,
  Image,
  Dimensions,
  SafeAreaView,
  Modal,
  ActivityIndicator
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation, useRoute } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { AppStackParamList } from '../navigation/AppNavigator';
import { useTheme } from '../providers/ThemeProvider';
import { useCart } from '../providers/CartProvider';
import { useFavorites } from '../providers/FavoritesProvider';
import { GraphQLService } from '../services/graphqlService';
import { Card, CardContent, Button, Badge } from '../components/ui';
import LoadingSpinner from '../components/LoadingSpinner';

type NavigationProp = StackNavigationProp<AppStackParamList>;

interface Product {
  id: string;
  name: string;
  price: string;
  images?: string[];
  category: string;
  brand?: string;
  rating?: number;
  inStock: boolean;
  description?: string;
  vendorId?: string;
  discount?: number;
  originalPrice?: string;
  reviewCount?: number;
  tags?: string[];
}

const categories = [
  { name: 'All Categories', value: 'all' },
  { name: 'Bridal Wear', value: 'bridal-wear' },
  { name: 'Groom Wear', value: 'groom-wear' },
  { name: 'Jewelry', value: 'jewelry' },
  { name: 'Footwear', value: 'footwear' },
  { name: 'Accessories', value: 'accessories' },
  { name: 'Decor', value: 'decor' },
  { name: 'Invitations', value: 'invitations' },
  { name: 'Gifts', value: 'gifts' },
];

const sortOptions = [
  { label: 'Popular', value: 'popular' },
  { label: 'Price: Low to High', value: 'price-low' },
  { label: 'Price: High to Low', value: 'price-high' },
  { label: 'Rating', value: 'rating' },
  { label: 'Newest', value: 'newest' },
];

const graphqlService = new GraphQLService();

export default function ShopScreen() {
  const { theme } = useTheme();
  const { addItem, isItemInCart } = useCart();
  const { isFavorite, addToFavorites, removeFromFavorites } = useFavorites();
  const navigation = useNavigation<NavigationProp>();
  const route = useRoute();

  // Search and filter state
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [priceRange, setPriceRange] = useState('all');
  const [selectedBrand, setSelectedBrand] = useState('all');
  const [selectedRating, setSelectedRating] = useState('all');
  const [selectedAvailability, setSelectedAvailability] = useState('all');
  const [selectedDiscount, setSelectedDiscount] = useState('all');
  const [sortBy, setSortBy] = useState('popular');

  // Data and UI state
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [showFilters, setShowFilters] = useState(false);
  const [imageErrors, setImageErrors] = useState<Set<string>>(new Set());

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(12);

  const { width } = Dimensions.get('window');

  // Handle route parameters
  useEffect(() => {
    const params = route.params as any;
    if (params) {
      if (params.search) {
        setSearchTerm(params.search);
      }
      if (params.category) {
        setSelectedCategory(params.category);
      }
      if (params.location) {
        // Handle location-based filtering if needed
        console.log('Location filter:', params.location);
      }
    }
  }, [route.params]);

  useEffect(() => {
    loadProducts();
  }, []);

  // Reset page when filters change
  useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm, selectedCategory, priceRange, selectedBrand, selectedRating, selectedAvailability, selectedDiscount]);

  const loadProducts = async () => {
    try {
      setLoading(true);

      // Fetch products from GraphQL API
      const result = await graphqlService.listProducts({}, 100);

      // Transform API data to match our Product interface
      const transformedProducts: Product[] = result.items.map((item: any) => ({
        id: item.id,
        name: item.name,
        price: item.price || '0',
        images: item.images || [],
        category: item.category,
        brand: item.brand,
        rating: item.rating,
        inStock: item.inStock !== false,
        description: item.description,
        vendorId: item.userId,
        discount: item.discount,
        originalPrice: item.originalPrice,
        reviewCount: item.reviewCount,
        tags: item.tags,
      }));

      setProducts(transformedProducts);
    } catch (error) {
      console.error('Failed to load products:', error);
      Alert.alert('Error', 'Failed to load products. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Helper functions
  const clearAllFilters = () => {
    setSearchTerm('');
    setSelectedCategory('all');
    setPriceRange('all');
    setSelectedBrand('all');
    setSelectedRating('all');
    setSelectedAvailability('all');
    setSelectedDiscount('all');
    setSortBy('popular');
  };

  const hasActiveFilters = () => {
    return searchTerm || selectedCategory !== 'all' || priceRange !== 'all' ||
           selectedBrand !== 'all' || selectedRating !== 'all' ||
           selectedAvailability !== 'all' || selectedDiscount !== 'all';
  };

  // Get product image with fallback
  const getProductImage = (product: Product) => {
    if (imageErrors.has(product.id)) {
      return '/placeholder-image.jpg
    }

    const firstImage = product.images?.[0];
    if (firstImage && firstImage.trim() !== '' && firstImage !== 'undefined' && firstImage !== 'null') {
      return firstImage;
    }

    return '/placeholder-image.jpg
  };

  // Handle image load errors
  const handleImageError = (productId: string) => {
    setImageErrors(prev => new Set([...prev, productId]));
  };

  // Get unique values for filter options
  const getUniqueCategories = () => {
    const cats = products.map(p => p.category).filter(Boolean);
    return [...new Set(cats)];
  };

  const getUniqueBrands = () => {
    const brands = products.map(p => p.brand).filter(Boolean);
    return [...new Set(brands)];
  };

  // Filter and sort products
  const filteredProducts = products.filter(product => {
    const matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         product.category.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (product.brand && product.brand.toLowerCase().includes(searchTerm.toLowerCase()));

    const matchesCategory = selectedCategory === 'all' || product.category.toLowerCase() === selectedCategory.toLowerCase();

    const matchesBrand = selectedBrand === 'all' || (product.brand && product.brand.toLowerCase() === selectedBrand.toLowerCase());

    let matchesPrice = true;
    if (priceRange !== 'all') {
      const price = parseInt(product.price.replace(/[^\d]/g, ''));
      switch (priceRange) {
        case '0-5000':
          matchesPrice = price <= 5000;
          break;
        case '5000-25000':
          matchesPrice = price > 5000 && price <= 25000;
          break;
        case '25000-50000':
          matchesPrice = price > 25000 && price <= 50000;
          break;
        case '50000+':
          matchesPrice = price > 50000;
          break;
      }
    }

    const matchesRating = selectedRating === 'all' || (() => {
      const rating = product.rating || 0;
      switch (selectedRating) {
        case '4-plus': return rating >= 4;
        case '3-plus': return rating >= 3;
        case '2-plus': return rating >= 2;
        default: return true;
      }
    })();

    const matchesAvailability = selectedAvailability === 'all' || (() => {
      switch (selectedAvailability) {
        case 'in-stock': return product.inStock;
        case 'out-of-stock': return !product.inStock;
        default: return true;
      }
    })();

    const matchesDiscount = selectedDiscount === 'all' || (() => {
      const discount = product.discount || 0;
      switch (selectedDiscount) {
        case 'on-sale': return discount > 0;
        case '10-plus': return discount >= 10;
        case '20-plus': return discount >= 20;
        case '50-plus': return discount >= 50;
        default: return true;
      }
    })();

    return matchesSearch && matchesCategory && matchesBrand && matchesPrice &&
           matchesRating && matchesAvailability && matchesDiscount;
  }).sort((a, b) => {
    switch (sortBy) {
      case 'price-low':
        return parseInt(a.price.replace(/[^\d]/g, '')) - parseInt(b.price.replace(/[^\d]/g, ''));
      case 'price-high':
        return parseInt(b.price.replace(/[^\d]/g, '')) - parseInt(a.price.replace(/[^\d]/g, ''));
      case 'rating':
        return (b.rating || 0) - (a.rating || 0);
      case 'newest':
        return 0; // Would need createdAt field for proper sorting
      default:
        return 0;
    }
  });

  // Pagination logic
  const totalPages = Math.ceil(filteredProducts.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const paginatedProducts = filteredProducts.slice(startIndex, endIndex);

  // Pagination functions
  const goToPage = (page: number) => {
    setCurrentPage(page);
  };

  const goToPrevious = () => {
    if (currentPage > 1) {
      goToPage(currentPage - 1);
    }
  };

  const goToNext = () => {
    if (currentPage < totalPages) {
      goToPage(currentPage + 1);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadProducts();
    setRefreshing(false);
  };

  // Handle favorite toggle
  const handleFavoriteToggle = async (product: Product) => {
    if (isFavorite(product.id, 'product')) {
      await removeFromFavorites(product.id, 'product');
    } else {
      await addToFavorites({
        id: product.id,
        type: 'product',
        name: product.name,
        image: getProductImage(product),
        rating: product.rating,
        location: product.brand,
        price: product.price,
      });
    }
  };

  if (loading) {
    return <LoadingSpinner />;
  }

  const handleAddToCart = async (product: Product) => {
    try {
      const price = typeof product.price === 'string' ?
        parseFloat(product.price.replace(/[^\d.]/g, '')) : product.price;

      await addItem({
        id: product.id,
        name: product.name,
        price: price,
        quantity: 1,
        image: getProductImage(product),
        vendorName: product.brand || 'Unknown Brand',
        category: product.category,
      });

      Alert.alert('Success', 'Product added to cart!');
    } catch (error) {
      console.error('Failed to add to cart:', error);
      Alert.alert('Error', 'Failed to add product to cart');
    }
  };
  // Helper functions

  const renderProduct = ({ item }: { item: Product }) => {
    const price = typeof item.price === 'string' ?
      parseFloat(item.price.replace(/[^\d.]/g, '')) : item.price;
    const originalPrice = item.originalPrice ?
      parseFloat(item.originalPrice.replace(/[^\d.]/g, '')) : null;
    const hasDiscount = item.discount && item.discount > 0;

    return (
      <TouchableOpacity
        style={styles.productCard}
        onPress={() => navigation.navigate('ProductDetail', { productId: item.id })}
      >
        <Card style={styles.productCardInner}>
          <View style={styles.productImageContainer}>
            <Image
              source={{ uri: getProductImage(item) }}
              style={styles.productImage}
              resizeMode="cover"
              onError={() => handleImageError(item.id)}
            />

            {/* Discount Badge */}
            {hasDiscount && (
              <View style={[styles.discountBadge, { backgroundColor: theme.colors.error }]}>
                <Text style={styles.discountText}>{item.discount}% OFF</Text>
              </View>
            )}

            {/* Stock Badge */}
            {!item.inStock && (
              <View style={[styles.stockBadge, { backgroundColor: 'rgba(0,0,0,0.7)' }]}>
                <Text style={styles.stockText}>Out of Stock</Text>
              </View>
            )}

            {/* Favorite Button */}
            <TouchableOpacity
              style={styles.favoriteButton}
              onPress={(e) => {
                e.stopPropagation();
                handleFavoriteToggle(item);
              }}
            >
              <Ionicons
                name={isFavorite(item.id, 'product') ? 'heart' : 'heart-outline'}
                size={20}
                color={isFavorite(item.id, 'product') ? theme.colors.error : '#fff'}
              />
            </TouchableOpacity>
          </View>

          <CardContent style={styles.productInfo}>
            <Text style={[styles.productName, { color: theme.colors.text }]} numberOfLines={2}>
              {item.name}
            </Text>

            {item.brand && (
              <Text style={[styles.brandName, { color: theme.colors.textSecondary }]} numberOfLines={1}>
                by {item.brand}
              </Text>
            )}

            {/* Rating */}
            {item.rating && (
              <View style={styles.ratingContainer}>
                <Ionicons name="star" size={14} color={theme.colors.accent} />
                <Text style={[styles.rating, { color: theme.colors.text }]}>
                  {item.rating.toFixed(1)}
                </Text>
                {item.reviewCount && (
                  <Text style={[styles.reviewCount, { color: theme.colors.textSecondary }]}>
                    ({item.reviewCount})
                  </Text>
                )}
              </View>
            )}

            {/* Price */}
            <View style={styles.priceContainer}>
              <Text style={[styles.price, { color: theme.colors.primary }]}>
                ₹{price.toLocaleString()}
              </Text>
              {originalPrice && originalPrice > price && (
                <Text style={[styles.originalPrice, { color: theme.colors.textSecondary }]}>
                  ₹{originalPrice.toLocaleString()}
                </Text>
              )}
            </View>

            {/* Action Buttons */}
            <View style={styles.productActions}>
              <TouchableOpacity
                style={[
                  styles.addToCartButton,
                  {
                    backgroundColor: item.inStock ? theme.colors.primary : theme.colors.border,
                    flex: isItemInCart(item.id) ? 0 : 1,
                  }
                ]}
                onPress={(e) => {
                  e.stopPropagation();
                  handleAddToCart(item);
                }}
                disabled={!item.inStock || isItemInCart(item.id)}
              >
                <Ionicons
                  name={isItemInCart(item.id) ? "checkmark" : "bag-add"}
                  size={16}
                  color={item.inStock ? 'white' : theme.colors.textSecondary}
                />
                <Text style={[
                  styles.addToCartText,
                  { color: item.inStock ? 'white' : theme.colors.textSecondary }
                ]}>
                  {!item.inStock ? 'Out of Stock' :
                   isItemInCart(item.id) ? 'In Cart' : 'Add to Cart'}
                </Text>
              </TouchableOpacity>

              {isItemInCart(item.id) && (
                <TouchableOpacity
                  style={[styles.viewCartButton, { borderColor: theme.colors.primary }]}
                  onPress={(e) => {
                    e.stopPropagation();
                    navigation.navigate('Cart');
                  }}
                >
                  <Text style={[styles.viewCartText, { color: theme.colors.primary }]}>
                    View Cart
                  </Text>
                </TouchableOpacity>
              )}
            </View>
          </CardContent>
        </Card>
      </TouchableOpacity>
    );
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {/* Header */}
      <View style={[styles.header, { backgroundColor: theme.colors.primary }]}>
        <View style={styles.searchContainer}>
          <View style={[styles.searchInputContainer, { backgroundColor: theme.colors.surface }]}>
            <Ionicons name="search" size={20} color={theme.colors.textSecondary} />
            <TextInput
              style={[styles.searchInput, { color: theme.colors.text }]}
              placeholder="Search products, brands..."
              placeholderTextColor={theme.colors.textSecondary}
              value={searchTerm}
              onChangeText={setSearchTerm}
            />
          </View>

          <TouchableOpacity
            style={[styles.filterButton, { backgroundColor: theme.colors.surface }]}
            onPress={() => setShowFilters(!showFilters)}
          >
            <Ionicons name="filter" size={20} color={theme.colors.primary} />
            {hasActiveFilters() && <View style={styles.filterIndicator} />}
          </TouchableOpacity>
        </View>
      </View>

      {/* Categories Filter */}
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        style={styles.categoriesContainer}
        contentContainerStyle={styles.categoriesContent}
      >
        {categories.map((category) => (
          <TouchableOpacity
            key={category.value}
            style={[
              styles.categoryChip,
              {
                backgroundColor: selectedCategory === category.value
                  ? theme.colors.primary
                  : theme.colors.surface,
              }
            ]}
            onPress={() => setSelectedCategory(category.value)}
          >
            <Text style={[
              styles.categoryText,
              {
                color: selectedCategory === category.value
                  ? 'white'
                  : theme.colors.text,
              }
            ]}>
              {category.name}
            </Text>
          </TouchableOpacity>
        ))}
      </ScrollView>

      {/* Results Header */}
      <View style={[styles.resultsHeader, { backgroundColor: theme.colors.surface }]}>
        <View style={styles.resultsInfo}>
          <Text style={[styles.resultsCount, { color: theme.colors.text }]}>
            {filteredProducts.length} products found
          </Text>
          {hasActiveFilters() && (
            <TouchableOpacity onPress={clearAllFilters} style={styles.clearFilters}>
              <Text style={[styles.clearFiltersText, { color: theme.colors.primary }]}>
                Clear all filters
              </Text>
            </TouchableOpacity>
          )}
        </View>

        <View style={styles.sortContainer}>
          <Text style={[styles.sortLabel, { color: theme.colors.textSecondary }]}>Sort:</Text>
          <TouchableOpacity
            style={[styles.sortButton, { borderColor: theme.colors.border }]}
            onPress={() => {
              // Show sort options modal or picker
            }}
          >
            <Text style={[styles.sortButtonText, { color: theme.colors.text }]}>
              {sortOptions.find(opt => opt.value === sortBy)?.label || 'Popular'}
            </Text>
            <Ionicons name="chevron-down" size={16} color={theme.colors.textSecondary} />
          </TouchableOpacity>
        </View>
      </View>

      {/* Products Grid */}
      <ScrollView
        style={styles.productsContainer}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        showsVerticalScrollIndicator={false}
      >
        {paginatedProducts.length > 0 ? (
          <>
            <View style={styles.productsGrid}>
              {paginatedProducts.map((product, index) => (
                <View key={product.id} style={styles.productItem}>
                  {renderProduct({ item: product })}
                </View>
              ))}
            </View>

            {/* Pagination */}
            {totalPages > 1 && (
              <View style={styles.paginationContainer}>
                <TouchableOpacity
                  style={[
                    styles.paginationButton,
                    { backgroundColor: currentPage === 1 ? theme.colors.border : theme.colors.primary }
                  ]}
                  onPress={goToPrevious}
                  disabled={currentPage === 1}
                >
                  <Ionicons
                    name="chevron-back"
                    size={20}
                    color={currentPage === 1 ? theme.colors.textSecondary : '#fff'}
                  />
                </TouchableOpacity>

                <View style={styles.pageNumbers}>
                  <Text style={[styles.pageInfo, { color: theme.colors.text }]}>
                    Page {currentPage} of {totalPages}
                  </Text>
                </View>

                <TouchableOpacity
                  style={[
                    styles.paginationButton,
                    { backgroundColor: currentPage === totalPages ? theme.colors.border : theme.colors.primary }
                  ]}
                  onPress={goToNext}
                  disabled={currentPage === totalPages}
                >
                  <Ionicons
                    name="chevron-forward"
                    size={20}
                    color={currentPage === totalPages ? theme.colors.textSecondary : '#fff'}
                  />
                </TouchableOpacity>
              </View>
            )}
          </>
        ) : (
          <View style={styles.emptyContainer}>
            <Ionicons name="bag-outline" size={64} color={theme.colors.textSecondary} />
            <Text style={[styles.emptyText, { color: theme.colors.text }]}>
              No products found
            </Text>
            <Text style={[styles.emptySubtext, { color: theme.colors.textSecondary }]}>
              Try adjusting your search or filters
            </Text>
            {hasActiveFilters() && (
              <TouchableOpacity onPress={clearAllFilters} style={styles.clearFiltersButton}>
                <Text style={[styles.clearFiltersButtonText, { color: theme.colors.primary }]}>
                  Clear all filters
                </Text>
              </TouchableOpacity>
            )}
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    paddingTop: 16,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  searchInputContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 10,
    borderRadius: 8,
    gap: 8,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    paddingVertical: 0,
  },
  filterButton: {
    padding: 10,
    borderRadius: 8,
    position: 'relative',
  },
  filterIndicator: {
    position: 'absolute',
    top: 6,
    right: 6,
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#F6C244',
  },
  categoriesContainer: {
    paddingVertical: 12,
  },
  categoriesContent: {
    paddingHorizontal: 16,
    gap: 8,
  },
  categoryChip: {
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderRadius: 20,
    marginRight: 8,
  },
  categoryText: {
    fontSize: 14,
    fontWeight: '500',
  },
  resultsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  resultsInfo: {
    flex: 1,
  },
  resultsCount: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  clearFilters: {
    alignSelf: 'flex-start',
  },
  clearFiltersText: {
    fontSize: 14,
    fontWeight: '500',
  },
  sortContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  sortLabel: {
    fontSize: 14,
  },
  sortButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderWidth: 1,
    borderRadius: 6,
    gap: 6,
  },
  sortButtonText: {
    fontSize: 14,
    fontWeight: '500',
  },
  productsContainer: {
    flex: 1,
  },
  productsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    paddingHorizontal: 16,
    paddingTop: 16,
    justifyContent: 'space-between',
  },
  productItem: {
    width: '48%',
    marginBottom: 16,
  },
  productCard: {
    width: '100%',
  },
  productCardInner: {
    overflow: 'hidden',
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  productImageContainer: {
    position: 'relative',
    height: 160,
  },
  productImage: {
    width: '100%',
    height: '100%',
  },
  discountBadge: {
    position: 'absolute',
    top: 8,
    left: 8,
    paddingHorizontal: 6,
    paddingVertical: 3,
    borderRadius: 8,
  },
  discountText: {
    color: '#fff',
    fontSize: 10,
    fontWeight: '700',
  },
  stockBadge: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 12,
  },
  stockText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '600',
  },
  favoriteButton: {
    position: 'absolute',
    top: 8,
    right: 8,
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  productInfo: {
    padding: 12,
  },
  productName: {
    fontSize: 14,
    fontWeight: '700',
    lineHeight: 20,
    marginBottom: 4,
  },
  brandName: {
    fontSize: 12,
    marginBottom: 6,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
    marginBottom: 8,
  },
  rating: {
    fontSize: 12,
    fontWeight: '600',
  },
  reviewCount: {
    fontSize: 11,
  },
  priceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginBottom: 12,
  },
  price: {
    fontSize: 16,
    fontWeight: '700',
  },
  originalPrice: {
    fontSize: 14,
    textDecorationLine: 'line-through',
  },
  productActions: {
    flexDirection: 'row',
    gap: 8,
  },
  addToCartButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 6,
    gap: 4,
  },
  addToCartText: {
    fontSize: 12,
    fontWeight: '600',
  },
  viewCartButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 6,
    borderWidth: 1,
  },
  viewCartText: {
    fontSize: 12,
    fontWeight: '600',
  },
  paginationContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 20,
    paddingHorizontal: 16,
    gap: 16,
  },
  paginationButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  pageNumbers: {
    flex: 1,
    alignItems: 'center',
  },
  pageInfo: {
    fontSize: 14,
    fontWeight: '500',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 64,
    paddingHorizontal: 32,
    gap: 16,
  },
  emptyText: {
    fontSize: 20,
    fontWeight: '600',
    textAlign: 'center',
  },
  emptySubtext: {
    fontSize: 16,
    textAlign: 'center',
    lineHeight: 24,
  },
  clearFiltersButton: {
    marginTop: 16,
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
    backgroundColor: 'rgba(97, 15, 19, 0.1)',
  },
  clearFiltersButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
});
