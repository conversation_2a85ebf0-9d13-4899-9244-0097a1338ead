import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../../../providers/ThemeProvider';
import { useAuth } from '../../../providers/AuthProvider';
import { Header } from '../../../components/Header';
import { Card, Input, Button, Badge } from '../../../components/ui';
import type { StackNavigationProp } from '@react-navigation/stack';
import type { AppStackParamList } from '../../../navigation/AppNavigator';

type NavigationProp = StackNavigationProp<AppStackParamList>;

interface Inquiry {
  id: string;
  customerName: string;
  customerEmail: string;
  customerPhone: string;
  subject: string;
  message: string;
  inquiryType: 'general' | 'booking' | 'pricing' | 'availability' | 'custom';
  status: 'new' | 'replied' | 'in_progress' | 'resolved' | 'closed';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  eventDate?: string;
  eventType?: string;
  guestCount?: number;
  budget?: number;
  createdAt: string;
  updatedAt: string;
  lastReply?: string;
}

export default function VendorInquiryManagementScreen() {
  const { theme } = useTheme();
  const navigation = useNavigation<NavigationProp>();
  const { user } = useAuth();
  const [inquiries, setInquiries] = useState<Inquiry[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedStatus, setSelectedStatus] = useState('All');

  const statuses = ['All', 'new', 'replied', 'in_progress', 'resolved', 'closed'];

  useEffect(() => {
    loadInquiries();
  }, []);

  const loadInquiries = async () => {
    try {
      setLoading(true);
      // TODO: Replace with actual API call
      const mockInquiries: Inquiry[] = [
        {
          id: '1',
          customerName: 'Priya Sharma',
          customerEmail: '<EMAIL>',
          customerPhone: '+91 8148376909',
          subject: 'Wedding Decoration Inquiry',
          message: 'Hi, I am planning my wedding for March 2024 and would like to know about your decoration packages for 300 guests.',
          inquiryType: 'pricing',
          status: 'new',
          priority: 'high',
          eventDate: '2024-03-15',
          eventType: 'Wedding',
          guestCount: 300,
          budget: 150000,
          createdAt: '2024-01-25T10:30:00Z',
          updatedAt: '2024-01-25T10:30:00Z',
        },
        {
          id: '2',
          customerName: 'Rahul Kumar',
          customerEmail: '<EMAIL>',
          customerPhone: '+91 9876543211',
          subject: 'Availability Check',
          message: 'Can you please check availability for February 20th, 2024? We are planning an engagement ceremony.',
          inquiryType: 'availability',
          status: 'replied',
          priority: 'medium',
          eventDate: '2024-02-20',
          eventType: 'Engagement',
          guestCount: 150,
          createdAt: '2024-01-24T14:15:00Z',
          updatedAt: '2024-01-24T16:30:00Z',
          lastReply: '2024-01-24T16:30:00Z',
        },
        {
          id: '3',
          customerName: 'Anita Patel',
          customerEmail: '<EMAIL>',
          customerPhone: '+91 9876543212',
          subject: 'Custom Package Request',
          message: 'I need a custom decoration package for a destination wedding in Goa. Please provide details.',
          inquiryType: 'custom',
          status: 'in_progress',
          priority: 'high',
          eventDate: '2024-04-10',
          eventType: 'Destination Wedding',
          guestCount: 200,
          budget: 250000,
          createdAt: '2024-01-23T09:45:00Z',
          updatedAt: '2024-01-25T11:20:00Z',
        },
      ];
      
      setInquiries(mockInquiries);
    } catch (error) {
      console.error('Error loading inquiries:', error);
      Alert.alert('Error', 'Failed to load inquiries');
    } finally {
      setLoading(false);
    }
  };

  const filteredInquiries = inquiries.filter(inquiry => {
    const matchesSearch = inquiry.customerName.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         inquiry.subject.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         inquiry.customerEmail.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesStatus = selectedStatus === 'All' || inquiry.status === selectedStatus;
    return matchesSearch && matchesStatus;
  });

  const handleInquiryPress = (inquiry: Inquiry) => {
    // TODO: Navigate to inquiry detail screen
    Alert.alert('Inquiry Details', `View details for inquiry from ${inquiry.customerName}`);
  };

  const handleReplyInquiry = (inquiry: Inquiry) => {
    // TODO: Navigate to reply screen or show reply modal
    Alert.alert('Reply to Inquiry', `Reply to ${inquiry.customerName}`);
  };

  const handleUpdateStatus = (inquiryId: string, newStatus: Inquiry['status']) => {
    setInquiries(prevInquiries =>
      prevInquiries.map(inquiry =>
        inquiry.id === inquiryId
          ? { ...inquiry, status: newStatus, updatedAt: new Date().toISOString() }
          : inquiry
      )
    );
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-IN', {
      day: 'numeric',
      month: 'short',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const formatCurrency = (amount: number) => {
    return `₹${amount.toLocaleString('en-IN')}`;
  };

  const getStatusColor = (status: Inquiry['status']) => {
    switch (status) {
      case 'new': return theme.colors.info;
      case 'replied': return theme.colors.success;
      case 'in_progress': return theme.colors.warning;
      case 'resolved': return theme.colors.success;
      case 'closed': return theme.colors.textSecondary;
      default: return theme.colors.textSecondary;
    }
  };

  const getPriorityColor = (priority: Inquiry['priority']) => {
    switch (priority) {
      case 'urgent': return theme.colors.destructive;
      case 'high': return theme.colors.warning;
      case 'medium': return theme.colors.info;
      case 'low': return theme.colors.textSecondary;
      default: return theme.colors.textSecondary;
    }
  };

  const renderStatusFilter = () => (
    <View style={styles.filterContainer}>
      <FlatList
        data={statuses}
        horizontal
        showsHorizontalScrollIndicator={false}
        keyExtractor={(item) => item}
        renderItem={({ item }) => (
          <TouchableOpacity
            style={[
              styles.filterButton,
              selectedStatus === item && styles.filterButtonActive,
              { borderColor: theme.colors.border }
            ]}
            onPress={() => setSelectedStatus(item)}
          >
            <Text
              style={[
                styles.filterButtonText,
                { color: selectedStatus === item ? theme.colors.primary : theme.colors.textSecondary }
              ]}
            >
              {item}
            </Text>
          </TouchableOpacity>
        )}
      />
    </View>
  );

  const renderInquiry = ({ item }: { item: Inquiry }) => (
    <Card style={styles.inquiryCard}>
      <TouchableOpacity onPress={() => handleInquiryPress(item)}>
        <View style={styles.inquiryHeader}>
          <View style={styles.inquiryTitleRow}>
            <Text style={[styles.inquirySubject, { color: theme.colors.text }]} numberOfLines={1}>
              {item.subject}
            </Text>
            <View style={styles.badgeContainer}>
              <Badge 
                style={[styles.priorityBadge, { backgroundColor: getPriorityColor(item.priority) }]}
              >
                <Text style={styles.badgeText}>{item.priority}</Text>
              </Badge>
              <Badge 
                style={[styles.statusBadge, { backgroundColor: getStatusColor(item.status) }]}
              >
                <Text style={styles.badgeText}>{item.status}</Text>
              </Badge>
            </View>
          </View>
          
          <View style={styles.customerInfo}>
            <Text style={[styles.customerName, { color: theme.colors.primary }]}>
              {item.customerName}
            </Text>
            <Text style={[styles.customerContact, { color: theme.colors.textSecondary }]}>
              {item.customerEmail} • {item.customerPhone}
            </Text>
          </View>
        </View>
        
        <Text style={[styles.inquiryMessage, { color: theme.colors.textSecondary }]} numberOfLines={2}>
          {item.message}
        </Text>
        
        {(item.eventDate || item.guestCount || item.budget) && (
          <View style={styles.eventDetails}>
            {item.eventDate && (
              <View style={styles.eventDetailItem}>
                <Ionicons name="calendar" size={14} color={theme.colors.textSecondary} />
                <Text style={[styles.eventDetailText, { color: theme.colors.textSecondary }]}>
                  {new Date(item.eventDate).toLocaleDateString('en-IN')}
                </Text>
              </View>
            )}
            
            {item.guestCount && (
              <View style={styles.eventDetailItem}>
                <Ionicons name="people" size={14} color={theme.colors.textSecondary} />
                <Text style={[styles.eventDetailText, { color: theme.colors.textSecondary }]}>
                  {item.guestCount} guests
                </Text>
              </View>
            )}
            
            {item.budget && (
              <View style={styles.eventDetailItem}>
                <Ionicons name="wallet" size={14} color={theme.colors.textSecondary} />
                <Text style={[styles.eventDetailText, { color: theme.colors.textSecondary }]}>
                  {formatCurrency(item.budget)}
                </Text>
              </View>
            )}
          </View>
        )}
        
        <View style={styles.inquiryMeta}>
          <Text style={[styles.metaText, { color: theme.colors.textSecondary }]}>
            Created: {formatDate(item.createdAt)}
          </Text>
          {item.lastReply && (
            <Text style={[styles.metaText, { color: theme.colors.textSecondary }]}>
              Last reply: {formatDate(item.lastReply)}
            </Text>
          )}
        </View>
      </TouchableOpacity>
      
      <View style={styles.inquiryActions}>
        <TouchableOpacity
          style={[styles.actionButton, { backgroundColor: theme.colors.primary }]}
          onPress={() => handleReplyInquiry(item)}
        >
          <Ionicons name="mail" size={16} color="white" />
          <Text style={styles.actionButtonText}>Reply</Text>
        </TouchableOpacity>
        
        {item.status === 'new' && (
          <TouchableOpacity
            style={[styles.actionButton, { backgroundColor: theme.colors.warning }]}
            onPress={() => handleUpdateStatus(item.id, 'in_progress')}
          >
            <Ionicons name="time" size={16} color="white" />
            <Text style={styles.actionButtonText}>In Progress</Text>
          </TouchableOpacity>
        )}
        
        {(item.status === 'in_progress' || item.status === 'replied') && (
          <TouchableOpacity
            style={[styles.actionButton, { backgroundColor: theme.colors.success }]}
            onPress={() => handleUpdateStatus(item.id, 'resolved')}
          >
            <Ionicons name="checkmark" size={16} color="white" />
            <Text style={styles.actionButtonText}>Resolve</Text>
          </TouchableOpacity>
        )}
        
        <TouchableOpacity
          style={[styles.actionButton, { backgroundColor: theme.colors.textSecondary }]}
          onPress={() => handleUpdateStatus(item.id, 'closed')}
        >
          <Ionicons name="close" size={16} color="white" />
          <Text style={styles.actionButtonText}>Close</Text>
        </TouchableOpacity>
      </View>
    </Card>
  );

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <Header title="Inquiry Management" showBack />
      
      {/* Search Bar */}
      <View style={styles.searchContainer}>
        <Input
          placeholder="Search inquiries..."
          value={searchQuery}
          onChangeText={setSearchQuery}
          leftIcon={<Ionicons name="search" size={20} color={theme.colors.textSecondary} />}
        />
      </View>

      {/* Status Filter */}
      {renderStatusFilter()}

      {/* Inquiries List */}
      <FlatList
        data={filteredInquiries}
        renderItem={renderInquiry}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.inquiriesList}
        showsVerticalScrollIndicator={false}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  searchContainer: {
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  filterContainer: {
    paddingHorizontal: 16,
    marginBottom: 16,
  },
  filterButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    borderWidth: 1,
    marginRight: 8,
  },
  filterButtonActive: {
    backgroundColor: 'rgba(97, 15, 19, 0.1)',
  },
  filterButtonText: {
    fontSize: 12,
    fontWeight: '500',
  },
  inquiriesList: {
    paddingHorizontal: 16,
    paddingBottom: 20,
  },
  inquiryCard: {
    marginBottom: 16,
    padding: 16,
  },
  inquiryHeader: {
    marginBottom: 12,
  },
  inquiryTitleRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  inquirySubject: {
    fontSize: 16,
    fontWeight: '600',
    flex: 1,
    marginRight: 8,
  },
  badgeContainer: {
    flexDirection: 'row',
    gap: 4,
  },
  priorityBadge: {
    paddingHorizontal: 6,
    paddingVertical: 2,
  },
  statusBadge: {
    paddingHorizontal: 6,
    paddingVertical: 2,
  },
  badgeText: {
    color: 'white',
    fontSize: 10,
    fontWeight: '500',
  },
  customerInfo: {
    gap: 2,
  },
  customerName: {
    fontSize: 14,
    fontWeight: '500',
  },
  customerContact: {
    fontSize: 12,
  },
  inquiryMessage: {
    fontSize: 14,
    lineHeight: 18,
    marginBottom: 12,
  },
  eventDetails: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
    marginBottom: 12,
  },
  eventDetailItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  eventDetailText: {
    fontSize: 12,
  },
  inquiryMeta: {
    gap: 2,
    marginBottom: 12,
  },
  metaText: {
    fontSize: 11,
  },
  inquiryActions: {
    flexDirection: 'row',
    gap: 8,
  },
  actionButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 6,
    gap: 4,
  },
  actionButtonText: {
    color: 'white',
    fontSize: 12,
    fontWeight: '500',
  },
});
