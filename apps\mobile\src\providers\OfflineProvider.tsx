import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { offlineService, SyncResult, OfflineData } from '../services/offlineService';
import { networkService, NetworkState } from '../services/networkService';
import { cacheService, CacheStats } from '../services/cacheService';

interface OfflineContextType {
  // Network state
  isOffline: boolean;
  networkState: NetworkState;
  connectionQuality: 'excellent' | 'good' | 'fair' | 'poor' | 'offline';
  
  // Sync state
  isSyncing: boolean;
  lastSyncResult: SyncResult | null;
  pendingActionsCount: number;
  
  // Cache state
  cacheStats: CacheStats | null;
  
  // Offline data
  offlineData: OfflineData | null;
  isDataStale: boolean;
  
  // Actions
  forceSync: () => Promise<SyncResult>;
  clearOfflineData: () => Promise<void>;
  refreshNetworkState: () => Promise<NetworkState>;
  cleanExpiredCache: () => Promise<number>;
  
  // Queue offline actions
  queueOfflineAction: (action: {
    type: 'CREATE' | 'UPDATE' | 'DELETE';
    entity: string;
    data: any;
    priority?: 'low' | 'normal' | 'high';
    maxRetries?: number;
  }) => Promise<void>;
}

const OfflineContext = createContext<OfflineContextType | undefined>(undefined);

interface OfflineProviderProps {
  children: ReactNode;
}

export function OfflineProvider({ children }: OfflineProviderProps) {
  const [isOffline, setIsOffline] = useState(false);
  const [networkState, setNetworkState] = useState<NetworkState>(networkService.getCurrentState());
  const [isSyncing, setIsSyncing] = useState(false);
  const [lastSyncResult, setLastSyncResult] = useState<SyncResult | null>(null);
  const [pendingActionsCount, setPendingActionsCount] = useState(0);
  const [cacheStats, setCacheStats] = useState<CacheStats | null>(null);
  const [offlineData, setOfflineData] = useState<OfflineData | null>(null);
  const [isDataStale, setIsDataStale] = useState(false);

  useEffect(() => {
    initializeOfflineProvider();
    
    // Cleanup on unmount
    return () => {
      // Any cleanup if needed
    };
  }, []);

  const initializeOfflineProvider = async () => {
    try {
      // Initialize network state
      const currentNetworkState = await networkService.refreshNetworkState();
      setNetworkState(currentNetworkState);
      setIsOffline(!currentNetworkState.isConnected || !currentNetworkState.isInternetReachable);

      // Load initial data
      await loadOfflineData();
      await loadCacheStats();
      await loadPendingActionsCount();
      await checkDataStaleness();

      // Set up listeners
      setupNetworkListener();
      setupOfflineListener();
      setupSyncMonitoring();
    } catch (error) {
      console.error('Error initializing offline provider:', error);
    }
  };

  const setupNetworkListener = () => {
    networkService.addListener((state: NetworkState) => {
      setNetworkState(state);
      const offline = !state.isConnected || !state.isInternetReachable;
      setIsOffline(offline);
    });
  };

  const setupOfflineListener = () => {
    offlineService.addOfflineListener((offline: boolean) => {
      setIsOffline(offline);
    });
  };

  const setupSyncMonitoring = () => {
    // Monitor sync status
    const checkSyncStatus = () => {
      setIsSyncing(offlineService.isSyncInProgress());
    };

    // Check sync status periodically
    const syncStatusInterval = setInterval(checkSyncStatus, 1000);
    
    // Cleanup interval on unmount
    return () => clearInterval(syncStatusInterval);
  };

  const loadOfflineData = async () => {
    try {
      const data = await offlineService.getOfflineData();
      setOfflineData(data);
    } catch (error) {
      console.error('Error loading offline data:', error);
    }
  };

  const loadCacheStats = async () => {
    try {
      const stats = await cacheService.getStats();
      setCacheStats(stats);
    } catch (error) {
      console.error('Error loading cache stats:', error);
    }
  };

  const loadPendingActionsCount = async () => {
    try {
      const actions = await offlineService.getPendingActions();
      setPendingActionsCount(actions.length);
    } catch (error) {
      console.error('Error loading pending actions count:', error);
    }
  };

  const checkDataStaleness = async () => {
    try {
      const stale = await offlineService.isDataStale();
      setIsDataStale(stale);
    } catch (error) {
      console.error('Error checking data staleness:', error);
    }
  };

  const forceSync = async (): Promise<SyncResult> => {
    try {
      setIsSyncing(true);
      const result = await offlineService.forceSync();
      setLastSyncResult(result);
      
      // Refresh counts after sync
      await loadPendingActionsCount();
      await checkDataStaleness();
      
      return result;
    } catch (error) {
      console.error('Error during force sync:', error);
      const errorResult: SyncResult = {
        success: false,
        syncedActions: 0,
        failedActions: 0,
        errors: [{ actionId: 'force_sync', error: error instanceof Error ? error.message : 'Unknown error' }],
      };
      setLastSyncResult(errorResult);
      return errorResult;
    } finally {
      setIsSyncing(false);
    }
  };

  const clearOfflineData = async (): Promise<void> => {
    try {
      await offlineService.clearOfflineData();
      await loadOfflineData();
      await loadCacheStats();
      await loadPendingActionsCount();
      await checkDataStaleness();
    } catch (error) {
      console.error('Error clearing offline data:', error);
      throw error;
    }
  };

  const refreshNetworkState = async (): Promise<NetworkState> => {
    try {
      const state = await networkService.refreshNetworkState();
      setNetworkState(state);
      setIsOffline(!state.isConnected || !state.isInternetReachable);
      return state;
    } catch (error) {
      console.error('Error refreshing network state:', error);
      throw error;
    }
  };

  const cleanExpiredCache = async (): Promise<number> => {
    try {
      const cleanedCount = await cacheService.cleanExpired();
      await loadCacheStats();
      return cleanedCount;
    } catch (error) {
      console.error('Error cleaning expired cache:', error);
      throw error;
    }
  };

  const queueOfflineAction = async (action: {
    type: 'CREATE' | 'UPDATE' | 'DELETE';
    entity: string;
    data: any;
    priority?: 'low' | 'normal' | 'high';
    maxRetries?: number;
  }): Promise<void> => {
    try {
      await offlineService.queueAction({
        type: action.type,
        entity: action.entity,
        data: action.data,
        priority: action.priority || 'normal',
        maxRetries: action.maxRetries || 3,
      });
      
      // Refresh pending actions count
      await loadPendingActionsCount();
    } catch (error) {
      console.error('Error queueing offline action:', error);
      throw error;
    }
  };

  const getConnectionQuality = (): 'excellent' | 'good' | 'fair' | 'poor' | 'offline' => {
    return networkService.getConnectionQuality();
  };

  const contextValue: OfflineContextType = {
    // Network state
    isOffline,
    networkState,
    connectionQuality: getConnectionQuality(),
    
    // Sync state
    isSyncing,
    lastSyncResult,
    pendingActionsCount,
    
    // Cache state
    cacheStats,
    
    // Offline data
    offlineData,
    isDataStale,
    
    // Actions
    forceSync,
    clearOfflineData,
    refreshNetworkState,
    cleanExpiredCache,
    queueOfflineAction,
  };

  return (
    <OfflineContext.Provider value={contextValue}>
      {children}
    </OfflineContext.Provider>
  );
}

export function useOffline(): OfflineContextType {
  const context = useContext(OfflineContext);
  if (context === undefined) {
    throw new Error('useOffline must be used within an OfflineProvider');
  }
  return context;
}

// Hook for network state only
export function useNetworkState(): NetworkState {
  const { networkState } = useOffline();
  return networkState;
}

// Hook for offline status only
export function useOfflineStatus(): boolean {
  const { isOffline } = useOffline();
  return isOffline;
}

// Hook for sync status only
export function useSyncStatus(): { isSyncing: boolean; lastSyncResult: SyncResult | null; pendingActionsCount: number } {
  const { isSyncing, lastSyncResult, pendingActionsCount } = useOffline();
  return { isSyncing, lastSyncResult, pendingActionsCount };
}
