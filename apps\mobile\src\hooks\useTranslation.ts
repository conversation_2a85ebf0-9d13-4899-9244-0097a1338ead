import { useTranslation as useI18nTranslation } from 'react-i18next';
import {
  tCommon,
  tAuth,
  tNavigation,
  tWedding,
  tVendor,
  tShop,
  tBooking,
  tPayment,
  tNotification,
  tError,
  tSuccess,
  formatCurrency,
  formatNumber,
  formatDate,
  formatDateTime,
  getCurrentLanguage,
  changeLanguage,
  isRTL,
} from '../i18n';

/**
 * Enhanced translation hook with namespace shortcuts and formatting utilities
 */
export const useTranslation = () => {
  const { t, i18n } = useI18nTranslation();

  return {
    // Core translation function
    t,
    
    // Namespace shortcuts
    tCommon,
    tAuth,
    tNavigation,
    tWedding,
    tVendor,
    tShop,
    tBooking,
    tPayment,
    tNotification,
    tError,
    tSuccess,
    
    // Formatting utilities
    formatCurrency,
    formatNumber,
    formatDate,
    formatDateTime,
    
    // Language utilities
    getCurrentLanguage,
    changeLanguage,
    isRTL,
    
    // i18n instance
    i18n,
    
    // Current language info
    language: i18n.language,
    isReady: i18n.isInitialized,
  };
};

/**
 * Hook for common translations
 */
export const useCommonTranslations = () => {
  return {
    yes: tCommon('yes'),
    no: tCommon('no'),
    ok: tCommon('ok'),
    cancel: tCommon('cancel'),
    save: tCommon('save'),
    edit: tCommon('edit'),
    delete: tCommon('delete'),
    add: tCommon('add'),
    remove: tCommon('remove'),
    search: tCommon('search'),
    filter: tCommon('filter'),
    loading: tCommon('loading'),
    error: tCommon('error'),
    success: tCommon('success'),
    retry: tCommon('retry'),
    back: tCommon('back'),
    next: tCommon('next'),
    continue: tCommon('continue'),
    submit: tCommon('submit'),
    close: tCommon('close'),
    view: tCommon('view'),
    share: tCommon('share'),
    more: tCommon('more'),
    settings: tCommon('settings'),
    help: tCommon('help'),
    support: tCommon('support'),
    contact: tCommon('contact'),
    about: tCommon('about'),
    language: tCommon('language'),
  };
};

/**
 * Hook for navigation translations
 */
export const useNavigationTranslations = () => {
  return {
    home: tNavigation('home'),
    search: tNavigation('search'),
    favorites: tNavigation('favorites'),
    shop: tNavigation('shop'),
    planning: tNavigation('planning'),
    profile: tNavigation('profile'),
    vendors: tNavigation('vendors'),
    venues: tNavigation('venues'),
    cart: tNavigation('cart'),
    orders: tNavigation('orders'),
    bookings: tNavigation('bookings'),
    notifications: tNavigation('notifications'),
    dashboard: tNavigation('dashboard'),
    settings: tNavigation('settings'),
    logout: tNavigation('logout'),
  };
};

/**
 * Hook for authentication translations
 */
export const useAuthTranslations = () => {
  return {
    login: tAuth('login'),
    signup: tAuth('signup'),
    logout: tAuth('logout'),
    forgotPassword: tAuth('forgotPassword'),
    resetPassword: tAuth('resetPassword'),
    changePassword: tAuth('changePassword'),
    verifyEmail: tAuth('verifyEmail'),
    verifyPhone: tAuth('verifyPhone'),
    enterOTP: tAuth('enterOTP'),
    resendOTP: tAuth('resendOTP'),
    accountType: tAuth('accountType'),
    customer: tAuth('customer'),
    vendor: tAuth('vendor'),
    selectAccountType: tAuth('selectAccountType'),
    customerDescription: tAuth('customerDescription'),
    vendorDescription: tAuth('vendorDescription'),
    alreadyHaveAccount: tAuth('alreadyHaveAccount'),
    dontHaveAccount: tAuth('dontHaveAccount'),
    agreeToTerms: tAuth('agreeToTerms'),
    privacyPolicy: tAuth('privacyPolicy'),
  };
};

/**
 * Hook for wedding planning translations
 */
export const useWeddingTranslations = () => {
  return {
    planning: tWedding('planning'),
    budget: tWedding('budget'),
    budgetTracker: tWedding('budgetTracker'),
    guestList: tWedding('guestList'),
    guestListManager: tWedding('guestListManager'),
    timeline: tWedding('timeline'),
    checklist: tWedding('checklist'),
    vendors: tWedding('vendors'),
    venues: tWedding('venues'),
    weddingDate: tWedding('weddingDate'),
    venue: tWedding('venue'),
    guestCount: tWedding('guestCount'),
    totalBudget: tWedding('totalBudget'),
    spent: tWedding('spent'),
    remaining: tWedding('remaining'),
    overBudget: tWedding('overBudget'),
    addExpense: tWedding('addExpense'),
    expense: tWedding('expense'),
    expenses: tWedding('expenses'),
    guest: tWedding('guest'),
    guests: tWedding('guests'),
    addGuest: tWedding('addGuest'),
    attending: tWedding('attending'),
    notAttending: tWedding('notAttending'),
    maybe: tWedding('maybe'),
    rsvp: tWedding('rsvp'),
    overallProgress: tWedding('overallProgress'),
    daysToGo: tWedding('daysToGo'),
    quickActions: tWedding('quickActions'),
    planningTools: tWedding('planningTools'),
  };
};

/**
 * Hook for error translations
 */
export const useErrorTranslations = () => {
  return {
    general: tError('general'),
    network: tError('network'),
    timeout: tError('timeout'),
    notFound: tError('notFound'),
    unauthorized: tError('unauthorized'),
    forbidden: tError('forbidden'),
    serverError: tError('serverError'),
    validationError: tError('validationError'),
    requiredField: tError('requiredField'),
    invalidEmail: tError('invalidEmail'),
    invalidPhone: tError('invalidPhone'),
    passwordTooShort: tError('passwordTooShort'),
    passwordMismatch: tError('passwordMismatch'),
    tryAgain: tError('tryAgain'),
  };
};

/**
 * Hook for success translations
 */
export const useSuccessTranslations = () => {
  return {
    saved: tSuccess('saved'),
    updated: tSuccess('updated'),
    deleted: tSuccess('deleted'),
    sent: tSuccess('sent'),
    uploaded: tSuccess('uploaded'),
    downloaded: tSuccess('downloaded'),
    copied: tSuccess('copied'),
    shared: tSuccess('shared'),
  };
};

export default useTranslation;
