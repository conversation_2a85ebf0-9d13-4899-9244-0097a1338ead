import { Page, expect } from '@playwright/test';

export class TestHelpers {
  constructor(private page: Page) {}

  /**
   * Wait for page to load completely
   */
  async waitForPageLoad() {
    await this.page.waitForLoadState('networkidle');
    await this.page.waitForLoadState('domcontentloaded');
  }

  /**
   * Navigate to a specific route and wait for load
   */
  async navigateTo(path: string) {
    await this.page.goto(path);
    await this.waitForPageLoad();
  }

  /**
   * Wait for element to be visible and clickable
   */
  async waitForElement(selector: string, timeout = 10000) {
    await this.page.waitForSelector(selector, { state: 'visible', timeout });
  }

  /**
   * Click element with retry logic
   */
  async clickElement(selector: string, timeout = 10000) {
    await this.waitForElement(selector, timeout);
    await this.page.click(selector);
  }

  /**
   * Fill input field with validation
   */
  async fillInput(selector: string, value: string, timeout = 10000) {
    await this.waitForElement(selector, timeout);
    await this.page.fill(selector, value);
    
    // Verify the value was filled
    const inputValue = await this.page.inputValue(selector);
    expect(inputValue).toBe(value);
  }

  /**
   * Wait for toast notification
   */
  async waitForToast(message?: string, timeout = 5000) {
    const toastSelector = '[data-testid="toast"], .toast, [role="alert"]';
    await this.page.waitForSelector(toastSelector, { timeout });
    
    if (message) {
      await expect(this.page.locator(toastSelector)).toContainText(message);
    }
  }

  /**
   * Wait for loading to complete
   */
  async waitForLoadingToComplete() {
    // Wait for common loading indicators to disappear
    const loadingSelectors = [
      '[data-testid="loading"]',
      '.loading',
      '.spinner',
      '[aria-label="Loading"]'
    ];

    for (const selector of loadingSelectors) {
      try {
        await this.page.waitForSelector(selector, { state: 'hidden', timeout: 1000 });
      } catch {
        // Ignore if selector doesn't exist
      }
    }
  }

  /**
   * Check if user is logged in by looking for user menu or profile
   */
  async isUserLoggedIn(): Promise<boolean> {
    try {
      // Look for user menu, profile dropdown, or logout button
      const userIndicators = [
        '[data-testid="user-menu"]',
        '[data-testid="profile-dropdown"]',
        'text=Logout',
        'text=Profile',
        'text=Dashboard'
      ];

      for (const selector of userIndicators) {
        const element = await this.page.locator(selector).first();
        if (await element.isVisible()) {
          return true;
        }
      }
      return false;
    } catch {
      return false;
    }
  }

  /**
   * Logout user if logged in
   */
  async logoutIfLoggedIn() {
    if (await this.isUserLoggedIn()) {
      try {
        // Try different logout methods
        const logoutSelectors = [
          'text=Logout',
          '[data-testid="logout-button"]',
          'button:has-text("Logout")'
        ];

        for (const selector of logoutSelectors) {
          const element = this.page.locator(selector).first();
          if (await element.isVisible()) {
            await element.click();
            await this.waitForPageLoad();
            break;
          }
        }
      } catch {
        // If logout fails, clear storage
        await this.page.context().clearCookies();
        await this.page.evaluate(() => {
          localStorage.clear();
          sessionStorage.clear();
        });
      }
    }
  }

  /**
   * Generate random test data
   */
  generateTestData() {
    const timestamp = Date.now();
    const random = Math.floor(Math.random() * 1000);
    
    return {
      email: `test.user.${timestamp}.${random}@example.com`,
      password: 'TestPassword123!',
      firstName: `Test${random}`,
      lastName: `User${timestamp}`,
      phone: `+91${Math.floor(Math.random() * 9000000000) + 1000000000}`,
      city: 'Chennai',
      state: 'Tamil Nadu'
    };
  }

  /**
   * Take screenshot with timestamp
   */
  async takeScreenshot(name: string) {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    await this.page.screenshot({ 
      path: `test-results/screenshots/${name}-${timestamp}.png`,
      fullPage: true 
    });
  }

  /**
   * Wait for network requests to complete
   */
  async waitForNetworkIdle(timeout = 5000) {
    await this.page.waitForLoadState('networkidle', { timeout });
  }

  /**
   * Check if element exists without waiting
   */
  async elementExists(selector: string): Promise<boolean> {
    try {
      const element = await this.page.locator(selector).first();
      return await element.isVisible();
    } catch {
      return false;
    }
  }

  /**
   * Scroll element into view
   */
  async scrollToElement(selector: string) {
    await this.page.locator(selector).scrollIntoViewIfNeeded();
  }

  /**
   * Wait for URL to change
   */
  async waitForUrlChange(expectedUrl: string, timeout = 10000) {
    await this.page.waitForURL(expectedUrl, { timeout });
  }

  /**
   * Clear all form inputs on page
   */
  async clearAllInputs() {
    const inputs = await this.page.locator('input[type="text"], input[type="email"], input[type="password"], textarea').all();
    for (const input of inputs) {
      await input.clear();
    }
  }
}
