import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  Image,
  TouchableOpacity,
  Dimensions
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../../providers/ThemeProvider';
import { Header } from '../../components/Header';
import { Card, Input } from '../../components/ui';
import type { StackNavigationProp } from '@react-navigation/stack';
import type { AppStackParamList } from '../../navigation/AppNavigator';

type NavigationProp = StackNavigationProp<AppStackParamList>;

const { width: screenWidth } = Dimensions.get('window');
const cardWidth = (screenWidth - 48) / 2;

interface RealWedding {
  id: string;
  title: string;
  coupleNames: string;
  location: string;
  date: string;
  coverImage: string;
  images: string[];
  description: string;
  budget: string;
  guestCount: number;
  style: string;
  vendors: {
    photographer: string;
    decorator: string;
    venue: string;
    caterer: string;
  };
  likes: number;
  isLiked: boolean;
  tags: string[];
}

export default function RealWeddingsScreen() {
  const { theme } = useTheme();
  const navigation = useNavigation<NavigationProp>();
  const [weddings, setWeddings] = useState<RealWedding[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadRealWeddings();
  }, []);

  const loadRealWeddings = async () => {
    try {
      setLoading(true);
      // TODO: Replace with actual API call
      const mockWeddings: RealWedding[] = [
        {
          id: '1',
          title: 'A Royal South Indian Wedding',
          coupleNames: 'Priya & Arjun',
          location: 'Chennai, Tamil Nadu',
          date: '2024-01-15',
          coverImage: '/placeholder-image.jpg
          images: [
            '/placeholder-image.jpg
            '/placeholder-image.jpg
            '/placeholder-image.jpg
          ],
          description: 'A beautiful traditional South Indian wedding with all the customs and rituals.',
          budget: '₹15-20 Lakhs',
          guestCount: 500,
          style: 'Traditional',
          vendors: {
            photographer: 'Rajesh Photography',
            decorator: 'Elegant Decorations',
            venue: 'Grand Palace Hotel',
            caterer: 'Royal Caterers'
          },
          likes: 245,
          isLiked: false,
          tags: ['traditional', 'south indian', 'royal', 'grand']
        },
        {
          id: '2',
          title: 'Intimate Beach Wedding',
          coupleNames: 'Kavya & Vikram',
          location: 'Goa',
          date: '2024-02-20',
          coverImage: '/placeholder-image.jpg
          images: [
            '/placeholder-image.jpg
            '/placeholder-image.jpg
          ],
          description: 'A romantic beach wedding with close family and friends.',
          budget: '₹8-12 Lakhs',
          guestCount: 150,
          style: 'Beach/Destination',
          vendors: {
            photographer: 'Coastal Clicks',
            decorator: 'Beach Vibes Decor',
            venue: 'Sunset Beach Resort',
            caterer: 'Seaside Catering'
          },
          likes: 189,
          isLiked: true,
          tags: ['beach', 'intimate', 'destination', 'romantic']
        },
        {
          id: '3',
          title: 'Modern Punjabi Wedding',
          coupleNames: 'Simran & Karan',
          location: 'Delhi',
          date: '2024-03-10',
          coverImage: '/placeholder-image.jpg
          images: [
            '/placeholder-image.jpg
            '/placeholder-image.jpg
          ],
          description: 'A vibrant Punjabi wedding with modern touches and traditional celebrations.',
          budget: '₹25-30 Lakhs',
          guestCount: 800,
          style: 'Modern Traditional',
          vendors: {
            photographer: 'Delhi Wedding Films',
            decorator: 'Marigold Decorations',
            venue: 'Heritage Banquet Hall',
            caterer: 'Punjabi Flavors'
          },
          likes: 312,
          isLiked: false,
          tags: ['punjabi', 'modern', 'vibrant', 'traditional']
        }
      ];

      setWeddings(mockWeddings);
    } catch (error) {
      console.error('Error loading real weddings:', error);
    } finally {
      setLoading(false);
    }
  };

  const filteredWeddings = weddings.filter(wedding =>
    wedding.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
    wedding.coupleNames.toLowerCase().includes(searchQuery.toLowerCase()) ||
    wedding.location.toLowerCase().includes(searchQuery.toLowerCase()) ||
    wedding.style.toLowerCase().includes(searchQuery.toLowerCase()) ||
    wedding.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))
  );

  const handleWeddingPress = (wedding: RealWedding) => {
    // TODO: Navigate to wedding detail screen
    navigation.navigate('BlogDetail', { blogId: wedding.id });
  };

  const handleLike = (weddingId: string) => {
    setWeddings(prevWeddings =>
      prevWeddings.map(wedding =>
        wedding.id === weddingId
          ? { ...wedding, isLiked: !wedding.isLiked, likes: wedding.isLiked ? wedding.likes - 1 : wedding.likes + 1 }
          : wedding
      )
    );
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-IN', {
      day: 'numeric',
      month: 'short',
      year: 'numeric'
    });
  };

  const renderWedding = ({ item }: { item: RealWedding }) => (
    <TouchableOpacity
      style={styles.weddingCard}
      onPress={() => handleWeddingPress(item)}
    >
      <View style={styles.imageContainer}>
        <Image source={{ uri: item.coverImage }} style={styles.weddingImage} />
        <TouchableOpacity
          style={styles.likeButton}
          onPress={() => handleLike(item.id)}
        >
          <Ionicons
            name={item.isLiked ? "heart" : "heart-outline"}
            size={20}
            color={item.isLiked ? theme.colors.destructive : "white"}
          />
        </TouchableOpacity>
        <View style={styles.imageOverlay}>
          <View style={styles.imageCount}>
            <Ionicons name="images" size={12} color="white" />
            <Text style={styles.imageCountText}>{item.images.length}</Text>
          </View>
        </View>
      </View>

      <View style={styles.weddingInfo}>
        <Text style={[styles.weddingTitle, { color: theme.colors.text }]} numberOfLines={2}>
          {item.title}
        </Text>
        <Text style={[styles.coupleNames, { color: theme.colors.primary }]}>
          {item.coupleNames}
        </Text>

        <View style={styles.weddingMeta}>
          <View style={styles.metaRow}>
            <Ionicons name="location" size={12} color={theme.colors.textSecondary} />
            <Text style={[styles.metaText, { color: theme.colors.textSecondary }]}>
              {item.location}
            </Text>
          </View>
          <View style={styles.metaRow}>
            <Ionicons name="calendar" size={12} color={theme.colors.textSecondary} />
            <Text style={[styles.metaText, { color: theme.colors.textSecondary }]}>
              {formatDate(item.date)}
            </Text>
          </View>
        </View>

        <View style={styles.weddingDetails}>
          <View style={styles.detailItem}>
            <Text style={[styles.detailLabel, { color: theme.colors.textSecondary }]}>Style</Text>
            <Text style={[styles.detailValue, { color: theme.colors.text }]}>{item.style}</Text>
          </View>
          <View style={styles.detailItem}>
            <Text style={[styles.detailLabel, { color: theme.colors.textSecondary }]}>Guests</Text>
            <Text style={[styles.detailValue, { color: theme.colors.text }]}>{item.guestCount}</Text>
          </View>
        </View>

        <View style={styles.likesContainer}>
          <Ionicons name="heart" size={14} color={theme.colors.destructive} />
          <Text style={[styles.likesCount, { color: theme.colors.textSecondary }]}>
            {item.likes} likes
          </Text>
        </View>
      </View>
    </TouchableOpacity>
  );

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <Header title="Real Weddings" showBack showSearch={false} />

      {/* Search Bar */}
      <View style={styles.searchContainer}>
        <Input
          placeholder="Search weddings..."
          value={searchQuery}
          onChangeText={setSearchQuery}
          leftIcon={<Ionicons name="search" size={20} color={theme.colors.textSecondary} />}
        />
      </View>

      {/* Weddings Grid */}
      <FlatList
        data={filteredWeddings}
        renderItem={renderWedding}
        keyExtractor={(item) => item.id}
        numColumns={2}
        contentContainerStyle={styles.weddingsList}
        showsVerticalScrollIndicator={false}
        columnWrapperStyle={styles.row}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  searchContainer: {
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  weddingsList: {
    paddingHorizontal: 16,
    paddingBottom: 20,
  },
  row: {
    justifyContent: 'space-between',
  },
  weddingCard: {
    width: cardWidth,
    backgroundColor: 'white',
    borderRadius: 12,
    marginBottom: 16,
    overflow: 'hidden',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  imageContainer: {
    position: 'relative',
  },
  weddingImage: {
    width: '100%',
    height: cardWidth * 0.75,
    resizeMode: 'cover',
  },
  likeButton: {
    position: 'absolute',
    top: 8,
    right: 8,
    backgroundColor: 'rgba(0,0,0,0.5)',
    borderRadius: 20,
    padding: 6,
  },
  imageOverlay: {
    position: 'absolute',
    bottom: 8,
    right: 8,
  },
  imageCount: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.7)',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 10,
  },
  imageCountText: {
    color: 'white',
    fontSize: 10,
    marginLeft: 2,
  },
  weddingInfo: {
    padding: 12,
  },
  weddingTitle: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 4,
    lineHeight: 18,
  },
  coupleNames: {
    fontSize: 12,
    fontWeight: '500',
    marginBottom: 8,
  },
  weddingMeta: {
    gap: 4,
    marginBottom: 8,
  },
  metaRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  metaText: {
    fontSize: 11,
    marginLeft: 4,
  },
  weddingDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  detailItem: {
    flex: 1,
  },
  detailLabel: {
    fontSize: 10,
    marginBottom: 2,
  },
  detailValue: {
    fontSize: 11,
    fontWeight: '500',
  },
  likesContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  likesCount: {
    fontSize: 11,
    marginLeft: 4,
  },
});
