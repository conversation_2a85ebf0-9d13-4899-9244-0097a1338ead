/**
 * Test script to verify availability checking functionality
 * This can be used to debug GraphQL filter issues
 */

import { AvailabilityService } from './services/availabilityService'
import { BookingService } from './services/bookingService'

export async function testAvailabilityCheck() {
  console.log('🧪 Testing Availability Check...')
  
  try {
    // Test with a sample vendor/venue
    const testParams = {
      entityId: 'test-vendor-123',
      entityType: 'VENDOR' as const,
      eventDate: '2024-12-25',
      eventTime: '14:00',
      duration: '4 hours'
    }

    console.log('📅 Testing availability for:', testParams)

    // Test the availability service directly
    const availabilityResult = await AvailabilityService.checkAvailability(testParams)
    console.log('✅ Availability Result:', availabilityResult)

    // Test the booking service wrapper
    const bookingResult = await BookingService.checkAvailability(
      testParams.entityId,
      testParams.entityType,
      testParams.eventDate,
      testParams.eventTime,
      testParams.duration
    )
    console.log('✅ Booking Service Result:', bookingResult)

    return {
      success: true,
      availabilityResult,
      bookingResult
    }

  } catch (error) {
    console.error('❌ Test failed:', error)
    return {
      success: false,
      error: error.message
    }
  }
}

export async function testUserBookingCheck() {
  console.log('🧪 Testing User Booking Check...')
  
  try {
    const result = await BookingService.canUserBook(
      'test-vendor-123',
      'VENDOR',
      '2024-12-25',
      'test-user-123'
    )
    
    console.log('✅ User Booking Check Result:', result)
    return { success: true, result }
    
  } catch (error) {
    console.error('❌ User booking check failed:', error)
    return { success: false, error: error.message }
  }
}

// Helper function to test GraphQL filter syntax
export function validateFilterSyntax() {
  console.log('🧪 Validating GraphQL Filter Syntax...')
  
  const validFilters = [
    // Simple filter
    {
      entityId: { eq: 'test-123' }
    },
    
    // AND filter with OR nested
    {
      and: [
        { entityId: { eq: 'test-123' } },
        { eventDate: { eq: '2024-12-25' } },
        {
          or: [
            { status: { eq: 'CONFIRMED' } },
            { status: { eq: 'PENDING' } },
            { status: { eq: 'IN_PROGRESS' } }
          ]
        }
      ]
    },
    
    // Date range filter
    {
      and: [
        { entityId: { eq: 'test-123' } },
        { eventDate: { between: ['2024-12-01', '2024-12-31'] } }
      ]
    }
  ]
  
  console.log('✅ Valid filter examples:')
  validFilters.forEach((filter, index) => {
    console.log(`Filter ${index + 1}:`, JSON.stringify(filter, null, 2))
  })
  
  return validFilters
}

// Export for use in components or debugging
export const AvailabilityTestUtils = {
  testAvailabilityCheck,
  testUserBookingCheck,
  validateFilterSyntax
}
