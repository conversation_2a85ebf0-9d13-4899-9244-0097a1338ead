'use client';

import React, { useState, useEffect } from 'react';
import { Mail, Bell, CheckCircle, AlertCircle, Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useNewsletter } from '@/hooks/useNewsletter';
import { useAuth } from '@/contexts/AuthContext';
import { useEmailIntegration } from '@/hooks/useEmailIntegration';

interface NewsletterSubscriptionProps {
  source: string;
  className?: string;
  variant?: 'default' | 'compact' | 'detailed';
  showInterests?: boolean;
  defaultEmail?: string;
  onSuccess?: () => void;
}

const INTEREST_OPTIONS = [
  { value: 'PHOTOGRAPHY', label: 'Photography' },
  { value: 'VIDEOGRAPHY', label: 'Videography' },
  { value: 'CATERING', label: 'Catering' },
  { value: 'DECORATION', label: 'Decoration' },
  { value: 'MAKEUP', label: 'Makeup & Beauty' },
  { value: 'VENUES', label: 'Venues' },
  { value: 'SHOPPING', label: 'Festive Shopping' },
  { value: 'PLANNING', label: 'Wedding Planning' },
  { value: 'HONEYMOON', label: 'Honeymoon' },
  { value: 'JEWELRY', label: 'Jewelry' },
  { value: 'INVITATIONS', label: 'Invitations' },
  { value: 'MUSIC', label: 'Music & Entertainment' }
];

export default function NewsletterSubscription({
  source,
  className = '',
  variant = 'default',
  showInterests = false,
  defaultEmail = '',
  onSuccess
}: NewsletterSubscriptionProps) {
  const { user, userProfile } = useAuth();
  const { loading, error, success, subscribe, clearMessages } = useNewsletter();
  const emailIntegration = useEmailIntegration();
  
  const [formData, setFormData] = useState({
    email: defaultEmail || userProfile?.email || '',
    firstName: userProfile?.firstName || '',
    lastName: userProfile?.lastName || '',
    phone: userProfile?.phone || '',
    city: userProfile?.city || '',
    state: userProfile?.state || '',
    weddingDate: '',
    interests: [] as string[],
    preferences: {
      weddingTips: true,
      vendorRecommendations: true,
      specialOffers: true,
      eventUpdates: false,
      blogUpdates: false,
      frequency: 'WEEKLY'
    }
  });

  // Auto-fill user data when user logs in
  useEffect(() => {
    if (userProfile) {
      setFormData(prev => ({
        ...prev,
        email: prev.email || userProfile.email || '',
        firstName: prev.firstName || userProfile.firstName || '',
        lastName: prev.lastName || userProfile.lastName || '',
        phone: prev.phone || userProfile.phone || '',
        city: prev.city || userProfile.city || '',
        state: prev.state || userProfile.state || ''
      }));
    }
  }, [userProfile]);

  // Clear messages when component unmounts or success changes
  useEffect(() => {
    if (success && onSuccess) {
      onSuccess();
    }
  }, [success, onSuccess]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleInterestChange = (interest: string, checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      interests: checked 
        ? [...prev.interests, interest]
        : prev.interests.filter(i => i !== interest)
    }));
  };

  const handlePreferenceChange = (preference: string, checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      preferences: {
        ...prev.preferences,
        [preference]: checked
      }
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    clearMessages();

    if (!formData.email) {
      return;
    }

    // Subscribe to newsletter in database
    const subscriptionResult = await subscribe({
      email: formData.email,
      firstName: formData.firstName || undefined,
      lastName: formData.lastName || undefined,
      phone: formData.phone || undefined,
      city: formData.city || undefined,
      state: formData.state || undefined,
      weddingDate: formData.weddingDate || undefined,
      interests: formData.interests.length > 0 ? formData.interests : undefined,
      source,
      preferences: formData.preferences
    });

    // Send welcome email if subscription was successful
    if (subscriptionResult && !error) {
      try {
        await emailIntegration.sendNewsletterWelcome({
          email: formData.email,
          firstName: formData.firstName,
          lastName: formData.lastName,
          interests: formData.interests,
          preferences: formData.preferences
        });
      } catch (emailError) {
        console.error('Error sending newsletter welcome email:', emailError);
        // Don't fail the subscription if email fails
      }
    }

    if (onSuccess && !error) {
      onSuccess();
    }
  };

  if (variant === 'compact') {
    return (
      <div className={`space-y-4 ${className}`}>
        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}
        
        {success && (
          <Alert>
            <CheckCircle className="h-4 w-4" />
            <AlertDescription>
              Successfully subscribed! Check your email for confirmation.
            </AlertDescription>
          </Alert>
        )}

        <form onSubmit={handleSubmit} className="flex flex-col md:flex-row gap-2 md:gap-2">
          <div className="flex-1 relative">
            <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            <Input
              type="email"
              name="email"
              value={formData.email}
              onChange={handleInputChange}
              placeholder="Enter your email"
              className="pl-10"
              required
              disabled={loading}
            />
          </div>
          <Button
            type="submit"
            disabled={loading || !formData.email}
            className="w-full md:w-auto bg-[#FFD700] text-black hover:bg-[#e6c200] focus-visible:ring-yellow-400"
          >
            {loading ? (
              <>
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                Subscribing...
              </>
            ) : (
              <>
                <Bell className="w-4 h-4 mr-2" />
                Subscribe
              </>
            )}
          </Button>
        </form>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}
      
      {success && (
        <Alert>
          <CheckCircle className="h-4 w-4" />
          <AlertDescription>
            Successfully subscribed! Check your email for confirmation.
          </AlertDescription>
        </Alert>
      )}

      <form onSubmit={handleSubmit} className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <Label htmlFor="email">Email Address *</Label>
            <Input
              id="email"
              type="email"
              name="email"
              value={formData.email}
              onChange={handleInputChange}
              placeholder="Enter your email"
              required
              disabled={loading}
            />
          </div>
          
          {variant === 'detailed' && (
            <>
              <div>
                <Label htmlFor="firstName">First Name</Label>
                <Input
                  id="firstName"
                  type="text"
                  name="firstName"
                  value={formData.firstName}
                  onChange={handleInputChange}
                  placeholder="Your first name"
                  disabled={loading}
                />
              </div>
              
              <div>
                <Label htmlFor="lastName">Last Name</Label>
                <Input
                  id="lastName"
                  type="text"
                  name="lastName"
                  value={formData.lastName}
                  onChange={handleInputChange}
                  placeholder="Your last name"
                  disabled={loading}
                />
              </div>
              
              <div>
                <Label htmlFor="phone">Phone Number</Label>
                <Input
                  id="phone"
                  type="tel"
                  name="phone"
                  value={formData.phone}
                  onChange={handleInputChange}
                  placeholder="Your phone number"
                  disabled={loading}
                />
              </div>
              
              <div>
                <Label htmlFor="city">City</Label>
                <Input
                  id="city"
                  type="text"
                  name="city"
                  value={formData.city}
                  onChange={handleInputChange}
                  placeholder="Your city"
                  disabled={loading}
                />
              </div>
              
              <div>
                <Label htmlFor="weddingDate">Wedding Date</Label>
                <Input
                  id="weddingDate"
                  type="date"
                  name="weddingDate"
                  value={formData.weddingDate}
                  onChange={handleInputChange}
                  disabled={loading}
                />
              </div>
            </>
          )}
        </div>

        {showInterests && (
          <div>
            <Label className="text-base font-medium">Interests</Label>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-2 mt-2">
              {INTEREST_OPTIONS.map((interest) => (
                <div key={interest.value} className="flex items-center space-x-2">
                  <Checkbox
                    id={interest.value}
                    checked={formData.interests.includes(interest.value)}
                    onCheckedChange={(checked) => 
                      handleInterestChange(interest.value, checked as boolean)
                    }
                    disabled={loading}
                  />
                  <Label htmlFor={interest.value} className="text-sm">
                    {interest.label}
                  </Label>
                </div>
              ))}
            </div>
          </div>
        )}

        <div>
          <Label className="text-base font-medium">Email Preferences</Label>
          <div className="space-y-2 mt-2">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="weddingTips"
                checked={formData.preferences.weddingTips}
                onCheckedChange={(checked) => 
                  handlePreferenceChange('weddingTips', checked as boolean)
                }
                disabled={loading}
              />
              <Label htmlFor="weddingTips" className="text-sm">
                Wedding tips and planning advice
              </Label>
            </div>
            
            <div className="flex items-center space-x-2">
              <Checkbox
                id="vendorRecommendations"
                checked={formData.preferences.vendorRecommendations}
                onCheckedChange={(checked) => 
                  handlePreferenceChange('vendorRecommendations', checked as boolean)
                }
                disabled={loading}
              />
              <Label htmlFor="vendorRecommendations" className="text-sm">
                Vendor recommendations and reviews
              </Label>
            </div>
            
            <div className="flex items-center space-x-2">
              <Checkbox
                id="specialOffers"
                checked={formData.preferences.specialOffers}
                onCheckedChange={(checked) => 
                  handlePreferenceChange('specialOffers', checked as boolean)
                }
                disabled={loading}
              />
              <Label htmlFor="specialOffers" className="text-sm">
                Special offers and discounts
              </Label>
            </div>
          </div>
        </div>

        <Button 
          type="submit" 
          className="w-full bg-[#FFD700] text-black hover:bg-[#e6c200] focus-visible:ring-yellow-400" 
          disabled={loading || !formData.email}
        >
          {loading ? (
            <>
              <Loader2 className="w-4 h-4 mr-2 animate-spin" />
              Subscribing...
            </>
          ) : (
            <>
              <Bell className="w-4 h-4 mr-2" />
              Subscribe to Newsletter
            </>
          )}
        </Button>
      </form>
    </div>
  );
}
