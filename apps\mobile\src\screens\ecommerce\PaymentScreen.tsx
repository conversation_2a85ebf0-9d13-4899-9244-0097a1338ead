import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Alert,
} from 'react-native';
import { useNavigation, useRoute } from '@react-navigation/native';
import { useTheme } from '../../providers/ThemeProvider';
import { Button } from '../../components/ui/Button';
import { Card } from '../../components/ui/Card';
import { Input } from '../../components/ui/Input';

export default function PaymentScreen() {
  const navigation = useNavigation();
  const route = useRoute();
  const { theme } = useTheme();
  
  const { orderId, amount } = (route.params as any) || {};
  const [loading, setLoading] = useState(false);
  const [paymentMethod, setPaymentMethod] = useState('card');

  const handlePayment = async () => {
    setLoading(true);
    try {
      // TODO: Integrate with Ra<PERSON>pay
      await new Promise(resolve => setTimeout(resolve, 2000)); // Simulate payment
      
      navigation.navigate('OrderConfirmation' as never, { orderId });
    } catch (error) {
      Alert.alert('Payment Failed', 'Please try again');
    } finally {
      setLoading(false);
    }
  };

  const paymentMethods = [
    { id: 'card', name: 'Credit/Debit Card', icon: '💳' },
    { id: 'upi', name: 'UPI', icon: '📱' },
    { id: 'netbanking', name: 'Net Banking', icon: '🏦' },
    { id: 'wallet', name: 'Digital Wallet', icon: '👛' },
  ];

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    scrollContainer: {
      padding: 16,
      gap: 20,
    },
    header: {
      alignItems: 'center',
      marginBottom: 8,
    },
    title: {
      fontSize: 24,
      fontWeight: 'bold',
      color: theme.colors.text,
      marginBottom: 8,
    },
    amount: {
      fontSize: 32,
      fontWeight: 'bold',
      color: theme.colors.primary,
    },
    sectionTitle: {
      fontSize: 18,
      fontWeight: '600',
      color: theme.colors.text,
      marginBottom: 12,
    },
    paymentMethodCard: {
      flexDirection: 'row',
      alignItems: 'center',
      padding: 16,
      marginBottom: 8,
      borderWidth: 2,
      borderColor: 'transparent',
    },
    selectedPaymentMethod: {
      borderColor: theme.colors.primary,
      backgroundColor: theme.colors.primaryForeground,
    },
    methodIcon: {
      fontSize: 24,
      marginRight: 12,
    },
    methodName: {
      fontSize: 16,
      fontWeight: '500',
      color: theme.colors.text,
      flex: 1,
    },
    radioButton: {
      width: 20,
      height: 20,
      borderRadius: 10,
      borderWidth: 2,
      borderColor: theme.colors.border,
      alignItems: 'center',
      justifyContent: 'center',
    },
    radioButtonSelected: {
      borderColor: theme.colors.primary,
    },
    radioButtonInner: {
      width: 10,
      height: 10,
      borderRadius: 5,
      backgroundColor: theme.colors.primary,
    },
    cardForm: {
      gap: 16,
    },
    inputRow: {
      flexDirection: 'row',
      gap: 12,
    },
    inputHalf: {
      flex: 1,
    },
    securityInfo: {
      flexDirection: 'row',
      alignItems: 'center',
      padding: 12,
      backgroundColor: theme.colors.secondary + '20',
      borderRadius: 8,
      marginTop: 16,
    },
    securityIcon: {
      fontSize: 20,
      marginRight: 8,
    },
    securityText: {
      fontSize: 14,
      color: theme.colors.textSecondary,
      flex: 1,
    },
    payButton: {
      marginTop: 24,
    },
  });

  return (
    <View style={styles.container}>
      <ScrollView contentContainerStyle={styles.scrollContainer}>
        <View style={styles.header}>
          <Text style={styles.title}>Payment</Text>
          <Text style={styles.amount}>₹{amount?.toLocaleString()}</Text>
        </View>

        <Card>
          <Text style={styles.sectionTitle}>Payment Method</Text>
          {paymentMethods.map((method) => (
            <Card
              key={method.id}
              style={[
                styles.paymentMethodCard,
                paymentMethod === method.id && styles.selectedPaymentMethod,
              ]}
              onPress={() => setPaymentMethod(method.id)}
            >
              <Text style={styles.methodIcon}>{method.icon}</Text>
              <Text style={styles.methodName}>{method.name}</Text>
              <View
                style={[
                  styles.radioButton,
                  paymentMethod === method.id && styles.radioButtonSelected,
                ]}
              >
                {paymentMethod === method.id && (
                  <View style={styles.radioButtonInner} />
                )}
              </View>
            </Card>
          ))}
        </Card>

        {paymentMethod === 'card' && (
          <Card>
            <Text style={styles.sectionTitle}>Card Details</Text>
            <View style={styles.cardForm}>
              <Input placeholder="Card Number" keyboardType="numeric" />
              <View style={styles.inputRow}>
                <Input
                  placeholder="MM/YY"
                  style={styles.inputHalf}
                  keyboardType="numeric"
                />
                <Input
                  placeholder="CVV"
                  style={styles.inputHalf}
                  keyboardType="numeric"
                  secureTextEntry
                />
              </View>
              <Input placeholder="Cardholder Name" />
            </View>
          </Card>
        )}

        <View style={styles.securityInfo}>
          <Text style={styles.securityIcon}>🔒</Text>
          <Text style={styles.securityText}>
            Your payment information is secure and encrypted
          </Text>
        </View>

        <Button
          onPress={handlePayment}
          loading={loading}
          style={styles.payButton}
        >
          Pay ₹{amount?.toLocaleString()}
        </Button>
      </ScrollView>
    </View>
  );
}
