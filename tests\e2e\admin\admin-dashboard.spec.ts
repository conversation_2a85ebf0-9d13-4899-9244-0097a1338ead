import { test, expect } from '@playwright/test';
import { AuthHelpers } from '../utils/auth-helpers';

test.describe('Admin Dashboard', () => {
  let authHelpers: AuthHelpers;

  test.beforeEach(async ({ page }) => {
    authHelpers = new AuthHelpers(page);
    
    // Login as admin
    try {
      await authHelpers.loginAsAdmin();
    } catch {
      test.skip(true, 'Admin user not available');
    }
  });

  test('should display admin dashboard', async ({ page }) => {
    await authHelpers.navigateTo('/dashboard');
    
    // Check for admin-specific elements
    const adminElements = [
      'text=Admin Dashboard',
      'text=User Management',
      'text=Vendor Management',
      'text=Admin Panel',
      '[data-testid="admin-dashboard"]'
    ];

    let adminElementFound = false;
    for (const selector of adminElements) {
      if (await authHelpers.elementExists(selector)) {
        adminElementFound = true;
        break;
      }
    }

    expect(adminElementFound).toBe(true);
  });

  test('should have user management section', async ({ page }) => {
    await authHelpers.navigateTo('/dashboard');
    
    // Look for user management
    const userManagementElements = [
      'text=User Management',
      'text=Users',
      'a[href*="users"]',
      '[data-testid="user-management"]'
    ];

    let userManagementFound = false;
    for (const selector of userManagementElements) {
      if (await authHelpers.elementExists(selector)) {
        await authHelpers.clickElement(selector);
        userManagementFound = true;
        break;
      }
    }

    expect(userManagementFound).toBe(true);
  });

  test('should have vendor management section', async ({ page }) => {
    await authHelpers.navigateTo('/dashboard');
    
    // Look for vendor management
    const vendorManagementElements = [
      'text=Vendor Management',
      'text=Vendors',
      'a[href*="vendors"]',
      '[data-testid="vendor-management"]'
    ];

    let vendorManagementFound = false;
    for (const selector of vendorManagementElements) {
      if (await authHelpers.elementExists(selector)) {
        await authHelpers.clickElement(selector);
        vendorManagementFound = true;
        break;
      }
    }

    expect(vendorManagementFound).toBe(true);
  });

  test('should display analytics/statistics', async ({ page }) => {
    await authHelpers.navigateTo('/dashboard');
    
    // Look for analytics elements
    const analyticsElements = [
      '.stats',
      '.analytics',
      '.dashboard-stats',
      'text=Total Users',
      'text=Total Vendors',
      'text=Total Bookings',
      '[data-testid="analytics"]'
    ];

    let analyticsFound = false;
    for (const selector of analyticsElements) {
      if (await authHelpers.elementExists(selector)) {
        analyticsFound = true;
        break;
      }
    }

    expect(analyticsFound).toBe(true);
  });
});

test.describe('Admin User Management', () => {
  let authHelpers: AuthHelpers;

  test.beforeEach(async ({ page }) => {
    authHelpers = new AuthHelpers(page);
    
    try {
      await authHelpers.loginAsAdmin();
    } catch {
      test.skip(true, 'Admin user not available');
    }
  });

  test('should list all users', async ({ page }) => {
    await authHelpers.navigateTo('/dashboard/users');
    
    // Check for user list
    const userListElements = [
      '.user-list',
      '.users-table',
      'table',
      '[data-testid="users-list"]'
    ];

    let userListFound = false;
    for (const selector of userListElements) {
      if (await authHelpers.elementExists(selector)) {
        userListFound = true;
        break;
      }
    }

    expect(userListFound).toBe(true);
  });

  test('should allow user search', async ({ page }) => {
    await authHelpers.navigateTo('/dashboard/users');
    
    // Look for search functionality
    const searchElements = [
      'input[placeholder*="Search"]',
      'input[name="search"]',
      '[data-testid="user-search"]'
    ];

    let searchFound = false;
    for (const selector of searchElements) {
      if (await authHelpers.elementExists(selector)) {
        await authHelpers.fillInput(selector, 'test');
        searchFound = true;
        break;
      }
    }

    expect(searchFound).toBe(true);
  });
});
