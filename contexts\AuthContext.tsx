"use client"

import React, { createContext, useContext, useEffect, useState } from 'react';
import { getCurrentUser, signIn, signOut, signUp, confirmSignUp, AuthUser, resendSignUpCode, resetPassword, confirmResetPassword } from 'aws-amplify/auth';
import { profileService } from '@/lib/services/profileService';
import { HybridMobileAuthService } from '@/lib/services/hybridMobileAuth';
import { useEmailIntegration } from '@/hooks/useEmailIntegration';

interface BusinessSignUpData {
  businessName: string;
  businessType: string;
  fullName: string;
  emailOrPhone: string;
  password: string;
  isPhone?: boolean;
}

type UserRole = 'customer' | 'vendor' | 'admin' | 'super_admin';

interface AuthContextType {
  user: AuthUser | null;
  userProfile: any | null;
  userType: UserRole | null;
  userRole: UserRole | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  isVendor: boolean;
  isAdmin: boolean;
  isSuperAdmin: boolean;
  signInWithPassword: (emailOrPhone: string, password: string) => Promise<any>;
  signUp: (emailOrPhone: string, fullName: string, password: string, isPhone?: boolean) => Promise<any>;
  businessSignUp: (businessData: BusinessSignUpData) => Promise<any>;
  signOut: () => Promise<void>;
  confirmSignUp: (emailOrPhone: string, code: string) => Promise<any>;
  resendConfirmationCode: (emailOrPhone: string) => Promise<any>;
  requestPasswordReset: (emailOrPhone: string) => Promise<any>;
  confirmPasswordReset: (emailOrPhone: string, code: string, newPassword: string) => Promise<any>;
  refreshUserProfile: () => Promise<void>;
  checkAuthState: () => Promise<void>;
  sendOTP: (phone: string, countryCode?: string) => Promise<{ success: boolean; message: string; session?: string; }>;
  verifyOTP: (sessionId: string, otp: string) => Promise<{ success: boolean; message: string; user?: any; isNewUser?: boolean }>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<AuthUser | null>(null);
  const [userProfile, setUserProfile] = useState<any | null>(null);
  const [userType, setUserType] = useState<UserRole | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [pendingUser, setPendingUser] = useState<string | null>(null);

  useEffect(() => {
    checkAuthState();
  }, []);

  const checkAuthState = async () => {
    try {
      // Clear any stale cached data first
      const cachedAuth = sessionStorage.getItem('auth-state');
      if (cachedAuth) {
        const { timestamp } = JSON.parse(cachedAuth);
        if (Date.now() - timestamp > 5 * 60 * 1000) {
          sessionStorage.removeItem('auth-state');
        }
      }

      // Check for Google auth session
      const googleAuth = localStorage.getItem('authSession');
      if (googleAuth) {
        const authData = JSON.parse(googleAuth);
        if (authData.isAuthenticated) {
          setUser(authData.user);
          setUserProfile(authData.userProfile);
          let userType: UserRole = 'customer';
          const profile = authData.userProfile;
          if (profile?.isSuperAdmin || profile?.role === 'super_admin') {
            userType = 'super_admin';
          } else if (profile?.isAdmin || profile?.role === 'admin') {
            userType = 'admin';
          } else if (profile?.isVendor) {
            userType = 'vendor';
          }
          setUserType(userType);
          setIsAuthenticated(true);
          setIsLoading(false);
          return;
        }
      }

      // Check fresh cached Cognito auth state
      const freshCachedAuth = sessionStorage.getItem('auth-state');
      if (freshCachedAuth) {
        const { user, userProfile, userType } = JSON.parse(freshCachedAuth);
        setUser(user);
        setUserProfile(userProfile);
        setUserType(userType);
        setIsAuthenticated(true);
        setIsLoading(false);
        return;
      }

      // Fallback to Cognito auth
      const currentUser = await getCurrentUser();
      setUser(currentUser);
      setIsAuthenticated(true);

      const profileData = await fetchUserProfile(currentUser.userId);

      sessionStorage.setItem('auth-state', JSON.stringify({
        user: currentUser,
        userProfile: profileData.profile,
        userType: profileData.userType,
        timestamp: Date.now()
      }));
    } catch (error) {
      // Clear all auth data on error
      localStorage.removeItem('authSession');
      localStorage.removeItem('currentUser');
      sessionStorage.removeItem('auth-state');
      
      setUser(null);
      setUserProfile(null);
      setUserType(null);
      setIsAuthenticated(false);
    } finally {
      setIsLoading(false);
    }
  };

  const fetchUserProfile = async (userId?: string): Promise<{ profile: any; userType: UserRole }> => {
    try {
      if (process.env.NODE_ENV === 'development') {
        console.log('fetchUserProfile called with userId:', userId);
      }
      const profile = await profileService.getProfile(userId);
      if (process.env.NODE_ENV === 'development') {
        console.log('Profile fetched from service:', profile);
      }

      if (profile) {
        setUserProfile(profile);

        // Determine user type based on profile data with priority order
        let determinedUserType: UserRole = 'customer'; // Default

        if (process.env.NODE_ENV === 'development') {
          console.log('Profile data for user type detection:', {
            isSuperAdmin: profile.isSuperAdmin,
            isAdmin: profile.isAdmin,
            isVendor: profile.isVendor,
            role: profile.role,
            businessInfo: profile.businessInfo
          });
        }

        // Check for super admin (highest priority)
        if (profile.isSuperAdmin || profile.role === 'super_admin') {
          determinedUserType = 'super_admin';
        }
        // Check for admin
        else if (profile.isAdmin || profile.role === 'admin') {
          determinedUserType = 'admin';
        }
        // Check for vendor
        else if (profile.isVendor || (profile.businessInfo && profile.businessInfo.businessName)) {
          determinedUserType = 'vendor';
        }
        // Default to customer
        else {
          determinedUserType = 'customer';
        }

        if (process.env.NODE_ENV === 'development') {
          console.log('Determined user type:', determinedUserType);
        }

        setUserType(determinedUserType);
        return { profile, userType: determinedUserType };
      } else {
        // No profile exists - check if there's pending profile creation data
        console.log('No profile found, checking for pending profile data...');

        // Check if there's pending business or user data that should be used for profile creation
        const pendingBusinessData = localStorage.getItem('pendingBusinessData');
        const pendingUserData = localStorage.getItem('pendingUserData');
        const skipAutoCreation = localStorage.getItem('skipAutoProfileCreation');

        if (pendingBusinessData || pendingUserData || skipAutoCreation) {
          console.log('Found pending profile data or skip flag, skipping automatic creation to allow manual creation');
          setUserProfile(null);
          setUserType(null);
          return { profile: null, userType: 'customer' };
        } else {
          // No pending data, try to create profile automatically from Cognito user
          try {
            if (user) {
              console.log('Creating profile automatically from Cognito user data');
              const newProfile = await profileService.createProfileFromCognitoUser(user);
              const newUserType = newProfile.isVendor ? 'vendor' : 'customer';
              setUserProfile(newProfile);
              setUserType(newUserType);
              console.log('Profile created successfully');
              return { profile: newProfile, userType: newUserType };
            } else {
              setUserProfile(null);
              setUserType(null);
              return { profile: null, userType: 'customer' };
            }
          } catch (createError) {
            console.error('Failed to create profile automatically:', createError);
            setUserProfile(null);
            setUserType('customer'); // Default to customer if profile creation fails
            return { profile: null, userType: 'customer' };
          }
        }
      }
    } catch (error) {
      console.error('Error fetching user profile:', error);
      setUserProfile(null);
      setUserType('customer'); // Default to customer if profile fetch fails
      return { profile: null, userType: 'customer' };
    }
  };

  const refreshUserProfile = async () => {
    if (user?.userId) {
      await fetchUserProfile(user.userId);
    }
  };

  const handleSignInWithPassword = async (emailOrPhone: string, password: string) => {
    try {
      const result = await signIn({
        username: emailOrPhone,
        password: password
      });

      // Check if user is signed in successfully
      if (result.isSignedIn) {
        // Get the current user and fetch their profile
        const currentUser = await getCurrentUser();
        setUser(currentUser);
        setIsAuthenticated(true);
        
        // Fetch user profile to get userType and other data
        const profileData = await fetchUserProfile(currentUser.userId);
        
        // Cache the complete auth state
        sessionStorage.setItem('auth-state', JSON.stringify({
          user: currentUser,
          userProfile: profileData.profile,
          userType: profileData.userType,
          timestamp: Date.now()
        }));
        
        return result;
      } else if (result.nextStep?.signInStep === 'CONFIRM_SIGN_UP') {
        // User exists but is not confirmed - throw specific error for UI to handle
        const error = new Error('Please verify your email address first. Check your inbox for the verification code.');
        (error as any).name = 'UserNotConfirmedException';
        (error as any).username = emailOrPhone;
        throw error;
      } else {
        // Handle other sign-in steps if needed
        throw new Error('Sign in requires additional steps. Please try again.');
      }
    } catch (error: any) {
      // If user doesn't exist, we'll handle it in the UI
      if (error.name === 'UserNotFoundException') {
        throw new Error('User not found. Please sign up first.');
      }
      if (error.name === 'NotAuthorizedException') {
        throw new Error('Incorrect password. Please try again.');
      }
      if (error.name === 'UserNotConfirmedException') {
        // Re-throw the unconfirmed user error as-is
        throw error;
      }
      throw error;
    }
  };

  const handleRequestPasswordReset = async (emailOrPhone: string) => {
    try {
      const result = await resetPassword({ username: emailOrPhone });
      setPendingUser(emailOrPhone);
      return result;
    } catch (error: any) {
      if (error.name === 'UserNotFoundException') {
        throw new Error('User not found. Please sign up first.');
      }
      throw error;
    }
  };

  const handleConfirmPasswordReset = async (emailOrPhone: string, code: string, newPassword: string) => {
    try {
      const result = await confirmResetPassword({
        username: emailOrPhone,
        confirmationCode: code,
        newPassword: newPassword
      });

      setPendingUser(null);
      return result;
    } catch (error) {
      throw error;
    }
  };

  const handleSignUp = async (emailOrPhone: string, fullName: string, password: string, isPhone: boolean = false) => {
    try {
      const userAttributes: any = {
        name: fullName,
      };

      // Determine if it's email or phone and set appropriate attributes
      if (isPhone) {
        userAttributes.phone_number = emailOrPhone.startsWith('+') ? emailOrPhone : `+91${emailOrPhone}`;
      } else {
        userAttributes.email = emailOrPhone;
      }

      const result = await signUp({
        username: emailOrPhone,
        password: password,
        options: {
          userAttributes,
        },
      });

      setPendingUser(emailOrPhone);
      return result;
    } catch (error) {
      throw error;
    }
  };

  const handleBusinessSignUp = async (businessData: BusinessSignUpData) => {
    try {
      // Use name field to store business name temporarily, or combine with full name
      const displayName = `${businessData.fullName} (${businessData.businessName})`;

      const userAttributes: any = {
        name: displayName,
      };

      // Determine if it's email or phone and set appropriate attributes
      if (businessData.isPhone) {
        userAttributes.phone_number = businessData.emailOrPhone.startsWith('+')
          ? businessData.emailOrPhone
          : `+91${businessData.emailOrPhone}`;
      } else {
        userAttributes.email = businessData.emailOrPhone;
      }

      const result = await signUp({
        username: businessData.emailOrPhone,
        password: businessData.password,
        options: {
          userAttributes,
        },
      });

      setPendingUser(businessData.emailOrPhone);

      // Store business data in localStorage temporarily until we can save to database
      if (typeof window !== 'undefined') {
        localStorage.setItem('pendingBusinessData', JSON.stringify({
          businessName: businessData.businessName,
          businessType: businessData.businessType,
          fullName: businessData.fullName,
          emailOrPhone: businessData.emailOrPhone
        }));
      }

      return result;
    } catch (error) {
      throw error;
    }
  };

  const handleSignOut = async () => {
    try {
      // Clear all authentication data
      localStorage.removeItem('authSession');
      localStorage.removeItem('googleAuthSession');
      localStorage.removeItem('currentUser');
      localStorage.removeItem('registeredUsers');
      localStorage.removeItem('pendingUserData');
      localStorage.removeItem('pendingBusinessData');
      
      // Clear session storage
      sessionStorage.removeItem('auth-state');
      
      // Try Cognito signout
      try {
        await signOut();
      } catch (cognitoError) {
        console.log('Cognito signout not applicable');
      }
      
      // Reset all state
      setUser(null);
      setUserProfile(null);
      setUserType(null);
      setIsAuthenticated(false);
      setPendingUser(null);
      
      // Notify service worker to clear auth caches
      if ('serviceWorker' in navigator && navigator.serviceWorker.controller) {
        navigator.serviceWorker.controller.postMessage({ type: 'LOGOUT' });
      }
      
      // Force page reload to clear any cached state
      window.location.href = '/';
    } catch (error) {
      console.error('Error signing out:', error);
      // Clear data and notify service worker even on error
      if ('serviceWorker' in navigator && navigator.serviceWorker.controller) {
        navigator.serviceWorker.controller.postMessage({ type: 'LOGOUT' });
      }
      // Force redirect even if there's an error
      window.location.href = '/';
    }
  };

  const handleResendConfirmationCode = async (emailOrPhone: string) => {
    try {
      const result = await resendSignUpCode({ username: emailOrPhone });
      return result;
    } catch (error) {
      throw error;
    }
  };

  const handleConfirmSignUp = async (emailOrPhone: string, code: string) => {
    try {
      const result = await confirmSignUp({ username: emailOrPhone, confirmationCode: code });

      // Profile creation will be handled automatically by the Lambda trigger
      // when the user confirms their signup

      // Send welcome email after successful signup confirmation
      try {
        const isEmail = emailOrPhone.includes('@');
        
        if (isEmail) {
          // Import emailService directly to avoid hook usage in non-component function
          const { emailService } = await import('@/lib/services/emailService');
          
          // Use the user welcome email method instead
          await emailService.sendUserWelcomeEmail({
            email: emailOrPhone,
            firstName: result.user?.attributes?.name?.split(' ')[0] || 'User',
            lastName: result.user?.attributes?.name?.split(' ').slice(1).join(' ') || '',
            type: 'signup',
            isVendor: false // Will be updated based on profile
          });
          
          console.log('Welcome email sent successfully to:', emailOrPhone);
        }
      } catch (emailError) {
        console.error('Error sending welcome email:', emailError);
        // Don't fail signup if email fails
      }

      return result;
    } catch (error) {
      throw error;
    }
  };

  // Phone-based OTP methods
  const sendOTP = async (phone: string, countryCode: string = '+91') => {
    return HybridMobileAuthService.sendMobileOTP(phone, countryCode);
  };
  const verifyOTP = async (sessionId: string, otp: string) => {
    return HybridMobileAuthService.verifyMobileOTP(sessionId, otp);
  };

  const value = {
    user,
    userProfile,
    userType,
    userRole: userType, // Alias for backward compatibility
    isLoading,
    isAuthenticated,
    isVendor: userType === 'vendor',
    isAdmin: userType === 'admin',
    isSuperAdmin: userType === 'super_admin',
    signInWithPassword: handleSignInWithPassword,
    signUp: handleSignUp,
    businessSignUp: handleBusinessSignUp,
    signOut: handleSignOut,
    confirmSignUp: handleConfirmSignUp,
    resendConfirmationCode: handleResendConfirmationCode,
    requestPasswordReset: handleRequestPasswordReset,
    confirmPasswordReset: handleConfirmPasswordReset,
    refreshUserProfile,
    checkAuthState,
    sendOTP,
    verifyOTP,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};
