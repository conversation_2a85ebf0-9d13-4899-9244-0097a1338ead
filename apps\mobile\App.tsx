// Import polyfills first
import 'react-native-get-random-values';
import 'react-native-url-polyfill/auto';
import { Buffer } from '@craftzdog/react-native-buffer';

// Set global Buffer
(global as any).Buffer = Buffer;

// Fix UUID import issues for AWS Amplify
import 'uuid';

import React from 'react';
import { StatusBar } from 'expo-status-bar';
import { NavigationContainer } from '@react-navigation/native';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { StyleSheet, LogBox } from 'react-native';
import Toast from 'react-native-toast-message';


// Import navigation
import RootNavigator from './src/navigation/RootNavigator';

// Import providers
import { AuthProvider } from './src/providers/AuthProvider';
import { CartProvider } from './src/providers/CartProvider';
import { ThemeProvider } from './src/providers/ThemeProvider';
import { FavoritesProvider } from './src/providers/FavoritesProvider';
import { SearchProvider } from './src/providers/SearchProvider';
import { NotificationProvider } from './src/providers/NotificationProvider';
import { OfflineProvider } from './src/providers/OfflineProvider';

// Import notification setup
import { setupNotifications } from './src/services/notifications';

// Import i18n configuration
import './src/i18n';

// Ignore specific warnings
LogBox.ignoreLogs([
  'Non-serializable values were found in the navigation state',
  'VirtualizedLists should never be nested',
]);

export default function App() {
  React.useEffect(() => {
    // Setup push notifications
    setupNotifications();
  }, []);

  return (
    <GestureHandlerRootView style={styles.container}>
      <SafeAreaProvider>
        <ThemeProvider>
          <AuthProvider>
            <OfflineProvider>
              <NotificationProvider>
                <SearchProvider>
                  <FavoritesProvider>
                    <CartProvider>
                      <NavigationContainer>
                        <RootNavigator />
                        <StatusBar style="auto" />
                      </NavigationContainer>
                      <Toast />
                    </CartProvider>
                  </FavoritesProvider>
                </SearchProvider>
              </NotificationProvider>
            </OfflineProvider>
          </AuthProvider>
        </ThemeProvider>
      </SafeAreaProvider>
    </GestureHandlerRootView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});
