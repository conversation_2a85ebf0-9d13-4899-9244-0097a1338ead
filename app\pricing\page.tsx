'use client';

import React, { useState, useEffect } from 'react';
import { Check, Star, CreditCard, Smartphone, Building, Wallet, ArrowRight, Users, TrendingUp, Shield, Zap } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { pricingService, type PricingPlan } from '@/lib/services/pricingService';
import { useAuth } from '@/contexts/AuthContext';
import { useRouter } from 'next/navigation';
import VendorPricingModal from '@/components/pricing/VendorPricingModal';
import { showToast } from '@/lib/toast';

export default function PricingPage() {
  const [plans, setPlans] = useState<PricingPlan[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedPlan, setSelectedPlan] = useState<PricingPlan | null>(null);
  const [showPricingModal, setShowPricingModal] = useState(false);
  const [billingCycle, setBillingCycle] = useState<'MONTHLY' | 'YEARLY'>('MONTHLY');
  const { user, userProfile } = useAuth();
  const router = useRouter();

  useEffect(() => {
    loadPricingPlans();
  }, []);

  const loadPricingPlans = async () => {
    setLoading(true);
    try {
      const allPlans = await pricingService.getPricingPlans();
      setPlans(allPlans.filter(plan => plan.isActive));
    } catch (error) {
      console.error('Error loading pricing plans:', error);
    } finally {
      setLoading(false);
    }
  };

  const handlePlanSelect = (plan: PricingPlan) => {
    if (!user) {
      router.push('/login?redirect=/pricing');
      return;
    }

    if (!userProfile?.isVendor) {
      showToast.warning('Subscription plans are only available for vendor accounts. Please update your profile to become a vendor.');
      return;
    }

    setSelectedPlan(plan);
    setShowPricingModal(true);
  };

  const getFilteredPlans = () => {
    return plans.filter(plan => plan.duration === billingCycle);
  };

  const getYearlySavings = (monthlyPrice: number) => {
    const yearlyPrice = monthlyPrice * 10; // 2 months free
    const monthlyCost = monthlyPrice * 12;
    return Math.round(((monthlyCost - yearlyPrice) / monthlyCost) * 100);
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-50 via-white to-blue-50">
        <div className="flex justify-center items-center py-20">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 via-white to-blue-50">
      {/* Hero Section */}
      <div className="relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-purple-600/10 to-blue-600/10"></div>
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
          <div className="text-center">
            <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
              Choose Your Perfect
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-purple-600 to-blue-600"> Plan</span>
            </h1>
            <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
              Grow your wedding business with our comprehensive vendor subscription plans. 
              Get more bookings, manage your business efficiently, and reach more customers.
            </p>
            
            {/* Billing Toggle */}
            <div className="flex items-center justify-center mb-12">
              <div className="bg-white rounded-full p-1 shadow-lg border">
                <button
                  onClick={() => setBillingCycle('MONTHLY')}
                  className={`px-6 py-2 rounded-full text-sm font-medium transition-all ${
                    billingCycle === 'MONTHLY'
                      ? 'bg-primary text-white shadow-md'
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                >
                  Monthly
                </button>
                <button
                  onClick={() => setBillingCycle('YEARLY')}
                  className={`px-6 py-2 rounded-full text-sm font-medium transition-all relative ${
                    billingCycle === 'YEARLY'
                      ? 'bg-primary text-white shadow-md'
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                >
                  Yearly
                  <Badge className="absolute -top-2 -right-2 bg-green-500 text-white text-xs px-2 py-0.5">
                    Save 20%
                  </Badge>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Pricing Plans */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-20">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {getFilteredPlans().map((plan, index) => (
            <Card
              key={plan.id}
              className={`relative overflow-hidden transition-all duration-300 hover:shadow-2xl hover:scale-105 ${
                plan.isPopular
                  ? 'border-2 border-primary shadow-xl ring-4 ring-primary/20'
                  : 'border border-gray-200 hover:border-primary/50'
              }`}
            >
              {plan.isPopular && (
                <div className="absolute top-0 left-0 right-0 bg-gradient-to-r from-purple-600 to-blue-600 text-white text-center py-2 text-sm font-medium">
                  <Star className="inline h-4 w-4 mr-1" />
                  Most Popular
                </div>
              )}

              <CardHeader className={`text-center ${plan.isPopular ? 'pt-12' : 'pt-6'}`}>
                <CardTitle className="text-2xl font-bold text-gray-900">{plan.name}</CardTitle>
                <CardDescription className="text-gray-600 mt-2">{plan.description}</CardDescription>
                
                <div className="mt-6">
                  <div className="flex items-baseline justify-center">
                    <span className="text-4xl font-bold text-gray-900">₹{plan.price.toLocaleString()}</span>
                    <span className="text-gray-600 ml-2">/{plan.duration.toLowerCase()}</span>
                  </div>
                  
                  {billingCycle === 'YEARLY' && plan.duration === 'YEARLY' && (
                    <div className="mt-2">
                      <Badge className="bg-green-100 text-green-800">
                        Save {getYearlySavings(plan.price / 10)}% annually
                      </Badge>
                    </div>
                  )}

                  {plan.discountPercentage && (
                    <div className="mt-2">
                      <Badge className="bg-red-100 text-red-800">
                        {plan.discountPercentage}% OFF Limited Time
                      </Badge>
                    </div>
                  )}
                </div>
              </CardHeader>

              <CardContent className="px-6 pb-8">
                <ul className="space-y-4 mb-8">
                  {plan.features.map((feature, featureIndex) => (
                    <li key={featureIndex} className="flex items-start">
                      <Check className="h-5 w-5 text-green-500 mt-0.5 mr-3 flex-shrink-0" />
                      <span className="text-gray-700">{feature}</span>
                    </li>
                  ))}
                </ul>

                <Button
                  onClick={() => handlePlanSelect(plan)}
                  className={`w-full py-3 text-lg font-medium transition-all ${
                    plan.isPopular
                      ? 'bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white shadow-lg'
                      : 'bg-white border-2 border-primary text-primary hover:bg-primary hover:text-white'
                  }`}
                >
                  {user ? 'Choose Plan' : 'Sign Up to Choose'}
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Features Comparison */}
        <div className="mt-20">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Why Choose Our Platform?</h2>
            <p className="text-xl text-gray-600">Everything you need to grow your wedding business</p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <div className="text-center">
              <div className="bg-purple-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                <Users className="h-8 w-8 text-purple-600" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">More Customers</h3>
              <p className="text-gray-600">Reach thousands of couples planning their dream wedding</p>
            </div>

            <div className="text-center">
              <div className="bg-blue-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                <TrendingUp className="h-8 w-8 text-blue-600" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Grow Revenue</h3>
              <p className="text-gray-600">Increase bookings and grow your business with our tools</p>
            </div>

            <div className="text-center">
              <div className="bg-green-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                <Shield className="h-8 w-8 text-green-600" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Trusted Platform</h3>
              <p className="text-gray-600">Join thousands of verified vendors on our platform</p>
            </div>

            <div className="text-center">
              <div className="bg-yellow-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                <Zap className="h-8 w-8 text-yellow-600" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Easy Management</h3>
              <p className="text-gray-600">Manage bookings, payments, and customers effortlessly</p>
            </div>
          </div>
        </div>

        {/* CTA Section */}
        <div className="mt-20 text-center">
          <div className="bg-gradient-to-r from-purple-600 to-blue-600 rounded-2xl p-12 text-white">
            <h2 className="text-3xl font-bold mb-4">Ready to Grow Your Business?</h2>
            <p className="text-xl mb-8 opacity-90">
              Join thousands of successful wedding vendors on BookmyFestive
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button
                onClick={() => router.push('/vendor-signup')}
                className="bg-white text-purple-600 hover:bg-gray-100 px-8 py-3 text-lg font-medium"
              >
                Become a Vendor
              </Button>
              <Button
                onClick={() => router.push('/contact')}
                variant="outline"
                className="border-white text-white hover:bg-white hover:text-purple-600 px-8 py-3 text-lg font-medium"
              >
                Contact Sales
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Pricing Modal */}
      {selectedPlan && (
        <VendorPricingModal
          isOpen={showPricingModal}
          onClose={() => {
            setShowPricingModal(false);
            setSelectedPlan(null);
          }}
          vendorId={user?.id || ''}
          showSkipOption={false}
          title="Complete Your Subscription"
          subtitle={`You've selected the ${selectedPlan.name} plan. Complete your payment to get started.`}
        />
      )}
    </div>
  );
}
