'use client';

import React, { useState } from 'react';
import { TopHeader } from '@/components/top-header';
import { Header } from '@/components/header';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { 
  Shield, 
  Crown, 
  CheckCircle,
  XCircle,
  AlertCircle
} from 'lucide-react';
import AdminUserService from '@/lib/services/adminUserService';

export default function TestAdminCreationPage() {
  const [testUserId, setTestUserId] = useState('test-user-123');
  const [loading, setLoading] = useState(false);
  const [results, setResults] = useState<any[]>([]);

  const addResult = (result: any) => {
    setResults(prev => [...prev, { ...result, timestamp: new Date().toLocaleTimeString() }]);
  };

  const testAdminCreation = async (adminLevel: 'ADMIN' | 'SUPER_ADMIN') => {
    setLoading(true);
    
    try {
      addResult({
        type: 'info',
        message: `Testing ${adminLevel} creation for user: ${testUserId}`,
        action: 'Starting test'
      });

      const result = await AdminUserService.makeUserAdmin(testUserId, adminLevel);
      
      if (result.success) {
        addResult({
          type: 'success',
          message: `Successfully created ${adminLevel}`,
          action: 'Admin creation',
          data: result.data
        });
      } else {
        addResult({
          type: 'error',
          message: result.error || 'Failed to create admin',
          action: 'Admin creation',
          data: result
        });
      }
    } catch (error) {
      addResult({
        type: 'error',
        message: error instanceof Error ? error.message : 'Unknown error',
        action: 'Admin creation',
        data: error
      });
    } finally {
      setLoading(false);
    }
  };

  const testUserProfileRetrieval = async () => {
    setLoading(true);
    
    try {
      addResult({
        type: 'info',
        message: `Testing user profile retrieval for: ${testUserId}`,
        action: 'Profile retrieval'
      });

      const profile = await AdminUserService.getUserProfile(testUserId);
      
      if (profile) {
        addResult({
          type: 'success',
          message: 'User profile found',
          action: 'Profile retrieval',
          data: profile
        });
      } else {
        addResult({
          type: 'warning',
          message: 'No user profile found (this is normal for new users)',
          action: 'Profile retrieval'
        });
      }
    } catch (error) {
      addResult({
        type: 'error',
        message: error instanceof Error ? error.message : 'Unknown error',
        action: 'Profile retrieval',
        data: error
      });
    } finally {
      setLoading(false);
    }
  };

  const clearResults = () => {
    setResults([]);
  };

  const getIcon = (type: string) => {
    switch (type) {
      case 'success': return <CheckCircle className="h-5 w-5 text-green-600" />;
      case 'error': return <XCircle className="h-5 w-5 text-red-600" />;
      case 'warning': return <AlertCircle className="h-5 w-5 text-yellow-600" />;
      default: return <AlertCircle className="h-5 w-5 text-blue-600" />;
    }
  };

  const getColorClass = (type: string) => {
    switch (type) {
      case 'success': return 'bg-green-50 border-green-200 text-green-800';
      case 'error': return 'bg-red-50 border-red-200 text-red-800';
      case 'warning': return 'bg-yellow-50 border-yellow-200 text-yellow-800';
      default: return 'bg-blue-50 border-blue-200 text-blue-800';
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <TopHeader />
      <Header />
      
      <div className="container mx-auto px-4 py-8 max-w-4xl">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Admin Creation Test</h1>
          <p className="text-gray-600">Test the admin user creation functionality</p>
        </div>

        {/* Test Controls */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle>Test Controls</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="testUserId">Test User ID</Label>
              <Input
                id="testUserId"
                value={testUserId}
                onChange={(e) => setTestUserId(e.target.value)}
                placeholder="Enter a test user ID"
              />
              <p className="text-xs text-gray-500 mt-1">
                Use any string as a test user ID (e.g., test-user-123)
              </p>
            </div>
            
            <div className="flex flex-wrap gap-2">
              <Button 
                onClick={() => testAdminCreation('ADMIN')}
                disabled={loading || !testUserId.trim()}
                className="bg-blue-600 hover:bg-blue-700"
              >
                <Shield className="h-4 w-4 mr-2" />
                Test Admin Creation
              </Button>
              
              <Button 
                onClick={() => testAdminCreation('SUPER_ADMIN')}
                disabled={loading || !testUserId.trim()}
                className="bg-purple-600 hover:bg-purple-700"
              >
                <Crown className="h-4 w-4 mr-2" />
                Test Super Admin Creation
              </Button>
              
              <Button 
                onClick={testUserProfileRetrieval}
                disabled={loading || !testUserId.trim()}
                variant="outline"
              >
                Test Profile Retrieval
              </Button>
              
              <Button 
                onClick={clearResults}
                variant="outline"
                className="text-gray-600"
              >
                Clear Results
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Test Results */}
        {results.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle>Test Results</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3 max-h-96 overflow-y-auto">
                {results.map((result, index) => (
                  <div key={index} className={`p-3 rounded-lg border ${getColorClass(result.type)}`}>
                    <div className="flex items-start space-x-2">
                      {getIcon(result.type)}
                      <div className="flex-1">
                        <div className="flex items-center justify-between mb-1">
                          <p className="font-medium">{result.action}</p>
                          <span className="text-xs opacity-75">{result.timestamp}</span>
                        </div>
                        <p className="text-sm">{result.message}</p>
                        {result.data && (
                          <details className="mt-2">
                            <summary className="text-xs cursor-pointer hover:underline">
                              View Details
                            </summary>
                            <pre className="mt-1 p-2 bg-white bg-opacity-50 rounded text-xs overflow-auto max-h-32">
                              {JSON.stringify(result.data, null, 2)}
                            </pre>
                          </details>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Instructions */}
        <Card className="mt-6">
          <CardHeader>
            <CardTitle>Test Instructions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3 text-sm">
              <p><strong>1.</strong> Enter a test User ID (can be any string)</p>
              <p><strong>2.</strong> Click "Test Admin Creation" or "Test Super Admin Creation"</p>
              <p><strong>3.</strong> Check the results to see if admin creation works</p>
              <p><strong>4.</strong> If successful, the user profile will be created with admin privileges</p>
              
              <div className="mt-4 p-3 bg-blue-50 rounded-lg">
                <p className="text-blue-800">
                  <strong>Note:</strong> This test creates actual user profiles in the database. 
                  Use test user IDs that you can easily identify and clean up later if needed.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
