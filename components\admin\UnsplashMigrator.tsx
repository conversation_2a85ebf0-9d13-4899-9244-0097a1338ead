"use client"

import React, { useState } from 'react'
import { generateClient } from 'aws-amplify/api'
import { listVendors, listVenues, listShops } from '@/src/graphql/queries'
import { updateVendor, updateVenue, updateShop } from '@/src/graphql/mutations'
import { showToast } from '@/lib/toast'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'
import { Database, Play, CheckCircle, XCircle, Loader2 } from 'lucide-react'

const client = generateClient({ authMode: 'userPool' })

export function UnsplashMigrator() {
  const [isMigrating, setIsMigrating] = useState(false)
  const [progress, setProgress] = useState(0)
  const [results, setResults] = useState<string[]>([])

  const replaceUnsplashUrls = (obj: any): any => {
    if (typeof obj === 'string') {
      return obj.replace(/https:\/\/images\.unsplash\.com\/[^"']*/g, '/placeholder-image.jpg')
    }
    if (Array.isArray(obj)) {
      return obj.map(replaceUnsplashUrls)
    }
    if (obj && typeof obj === 'object') {
      const newObj: any = {}
      for (const [key, value] of Object.entries(obj)) {
        newObj[key] = replaceUnsplashUrls(value)
      }
      return newObj
    }
    return obj
  }

  const runMigration = async () => {
    setIsMigrating(true)
    setProgress(0)
    setResults([])
    
    try {
      // Get all data
      const [vendorsResult, venuesResult, shopsResult] = await Promise.all([
        client.graphql({ query: listVendors }),
        client.graphql({ query: listVenues }),
        client.graphql({ query: listShops })
      ])

      const vendors = vendorsResult.data.listVendors.items
      const venues = venuesResult.data.listVenues.items
      const shops = shopsResult.data.listShops.items

      const totalItems = vendors.length + venues.length + shops.length
      let completed = 0
      const newResults: string[] = []

      // Migrate vendors
      for (const vendor of vendors) {
        const hasUnsplash = JSON.stringify(vendor).includes('unsplash')
        if (hasUnsplash) {
          try {
            const updatedVendor = replaceUnsplashUrls(vendor)
            delete updatedVendor.createdAt
            delete updatedVendor.updatedAt
            
            await client.graphql({
              query: updateVendor,
              variables: { input: updatedVendor }
            })
            newResults.push(`✅ Updated vendor: ${vendor.name}`)
          } catch (error) {
            newResults.push(`❌ Failed vendor: ${vendor.name}`)
          }
        }
        completed++
        setProgress((completed / totalItems) * 100)
        setResults([...newResults])
      }

      // Migrate venues
      for (const venue of venues) {
        const hasUnsplash = JSON.stringify(venue).includes('unsplash')
        if (hasUnsplash) {
          try {
            const updatedVenue = replaceUnsplashUrls(venue)
            delete updatedVenue.createdAt
            delete updatedVenue.updatedAt
            
            await client.graphql({
              query: updateVenue,
              variables: { input: updatedVenue }
            })
            newResults.push(`✅ Updated venue: ${venue.name}`)
          } catch (error) {
            newResults.push(`❌ Failed venue: ${venue.name}`)
          }
        }
        completed++
        setProgress((completed / totalItems) * 100)
        setResults([...newResults])
      }

      // Migrate shops
      for (const shop of shops) {
        const hasUnsplash = JSON.stringify(shop).includes('unsplash')
        if (hasUnsplash) {
          try {
            const updatedShop = replaceUnsplashUrls(shop)
            delete updatedShop.createdAt
            delete updatedShop.updatedAt
            
            await client.graphql({
              query: updateShop,
              variables: { input: updatedShop }
            })
            newResults.push(`✅ Updated shop: ${shop.name}`)
          } catch (error) {
            newResults.push(`❌ Failed shop: ${shop.name}`)
          }
        }
        completed++
        setProgress((completed / totalItems) * 100)
        setResults([...newResults])
      }

      showToast.success('Migration completed successfully!')
    } catch (error) {
      showToast.error('Migration failed: ' + (error as Error).message)
    } finally {
      setIsMigrating(false)
    }
  }

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Database className="w-5 h-5" />
          Unsplash URL Migration
        </CardTitle>
        <p className="text-gray-600 text-sm">
          Replace all Unsplash image URLs in database with placeholder images.
        </p>
      </CardHeader>
      
      <CardContent className="space-y-6">
        <div className="flex items-center justify-between">
          <Button 
            onClick={runMigration} 
            disabled={isMigrating}
            className="flex items-center gap-2"
          >
            {isMigrating ? (
              <Loader2 className="w-4 h-4 animate-spin" />
            ) : (
              <Play className="w-4 h-4" />
            )}
            {isMigrating ? 'Migrating...' : 'Start Migration'}
          </Button>
          
          {isMigrating && (
            <div className="flex-1 ml-4">
              <Progress value={progress} className="w-full" />
            </div>
          )}
        </div>

        {results.length > 0 && (
          <div className="space-y-2">
            <h3 className="font-semibold">Migration Results:</h3>
            <div className="max-h-60 overflow-y-auto space-y-1">
              {results.map((result, index) => (
                <div key={index} className="text-sm p-2 bg-gray-50 rounded">
                  {result}
                </div>
              ))}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}