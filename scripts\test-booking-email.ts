#!/usr/bin/env ts-node

/**
 * Test script for booking confirmation email
 */

import { emailService } from '../lib/services/emailService';

async function testBookingEmail() {
  console.log('🧪 Testing Booking Confirmation Email...\n');

  try {
    const result = await emailService.sendBookingConfirmationEmail({
      email: '<EMAIL>',
      userName: 'Test User',
      bookingId: 'TEST_BOOKING_123',
      entityName: 'Test Vendor',
      entityType: 'vendor',
      eventDate: new Date().toLocaleDateString(),
      status: 'confirmed'
    });

    if (result) {
      console.log('✅ Booking confirmation email test successful!');
      console.log('📧 Email was sent/logged successfully');
    } else {
      console.log('❌ Booking confirmation email test failed');
    }
  } catch (error) {
    console.error('❌ Error testing booking email:', error);
  }
}

// Run the test
testBookingEmail().catch(console.error); 