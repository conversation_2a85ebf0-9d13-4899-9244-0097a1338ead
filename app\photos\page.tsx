'use client'
import { TopHeader } from "@/components/top-header"
import { <PERSON><PERSON> } from "@/components/header"
import { Foot<PERSON> } from "@/components/footer"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Search, Heart, Eye, Download, Filter } from "lucide-react"
import Image from "next/image"
import Link from "next/link"

export default function PhotosPage() {
  const photoCategories = [
    "All Photos",
    "Bridal Portraits",
    "Couple Shots",
    "Ceremony",
    "Reception",
    "Mehendi",
    "Sangeet",
    "Decor",
    "Food",
    "Candid Moments",
  ]

  const photos = [
    {
      id: 1,
      title: "Elegant Bridal Portrait",
      category: "Bridal Portraits",
      photographer: "Elegant Photography",
      location: "Mumbai",
      likes: 234,
      views: 1200,
      image: "/placeholder.svg?height=400&width=300",
      featured: true,
    },
    {
      id: 2,
      title: "Beautiful Couple Moment",
      category: "Couple Shots",
      photographer: "Romantic Captures",
      location: "Goa",
      likes: 189,
      views: 890,
      image: "/placeholder.svg?height=400&width=300",
      featured: false,
    },
    {
      id: 3,
      title: "Traditional Ceremony",
      category: "Ceremony",
      photographer: "Cultural Frames",
      location: "Jaipur",
      likes: 156,
      views: 756,
      image: "/placeholder.svg?height=400&width=300",
      featured: true,
    },
    {
      id: 4,
      title: "Mehendi Celebration",
      category: "Mehendi",
      photographer: "Festive Moments",
      location: "Delhi",
      likes: 298,
      views: 1456,
      image: "/placeholder.svg?height=400&width=300",
      featured: false,
    },
    {
      id: 5,
      title: "Reception Decor",
      category: "Decor",
      photographer: "Decor Dreams",
      location: "Bangalore",
      likes: 167,
      views: 623,
      image: "/placeholder.svg?height=400&width=300",
      featured: false,
    },
    {
      id: 6,
      title: "Sangeet Night",
      category: "Sangeet",
      photographer: "Dance Captures",
      location: "Pune",
      likes: 245,
      views: 1089,
      image: "/placeholder.svg?height=400&width=300",
      featured: true,
    },
  ]

  return (
    <div className="min-h-screen bg-white">
      <Header />

      {/* Hero Section */}
      <section className="bg-gradient-to-r from-[#F8F5F0] to-[#F6C244] py-16">
        <div className="container mx-auto px-4">
          <div className="text-center mb-8">
            <h1 className="text-4xl font-bold text-gray-900 mb-4">Wedding Photo Gallery</h1>
            <p className="text-xl text-gray-600">Get inspired by beautiful wedding moments and ideas</p>
          </div>

          {/* Search and Filters */}
          <div className="bg-white rounded-lg shadow-lg p-6 max-w-3xl mx-auto">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input placeholder="Search photos..." className="pl-10" />
              </div>
              <Select>
                <SelectTrigger>
                  <SelectValue placeholder="Category" />
                </SelectTrigger>
                <SelectContent>
                  {photoCategories.map((category) => (
                    <SelectItem key={category} value={category.toLowerCase().replace(" ", "-")}>
                      {category}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <Button className="bg-primary hover:bg-primary/90">Search</Button>
            </div>
            <div className="flex items-center space-x-4">
              <Button variant="outline" size="sm">
                <Filter className="h-4 w-4 mr-2" />
                More Filters
              </Button>
              <Select>
                <SelectTrigger className="w-40">
                  <SelectValue placeholder="Sort by" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="popular">Most Popular</SelectItem>
                  <SelectItem value="recent">Most Recent</SelectItem>
                  <SelectItem value="liked">Most Liked</SelectItem>
                  <SelectItem value="viewed">Most Viewed</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>
      </section>

      {/* Category Tabs */}
      <section className="py-8 border-b">
        <div className="container mx-auto px-4">
          <div className="flex flex-wrap gap-2 justify-center">
            {photoCategories.map((category, index) => (
              <Button
                key={index}
                variant={index === 0 ? "default" : "outline"}
                size="sm"
                className={index === 0 ? "bg-primary hover:bg-primary/90" : ""}
              >
                {category}
              </Button>
            ))}
          </div>
        </div>
      </section>

      {/* Photos Grid */}
      <section className="py-12">
        <div className="container mx-auto px-4">
          <div className="flex justify-between items-center mb-8">
            <h2 className="text-2xl font-bold">Wedding Photos ({photos.length})</h2>
            <div className="flex items-center space-x-2">
              <span className="text-sm text-gray-600">View:</span>
              <Button variant="outline" size="sm">
                Grid
              </Button>
              <Button variant="ghost" size="sm">
                Masonry
              </Button>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {photos.map((photo) => (
              <Card key={photo.id} className="overflow-hidden hover:shadow-lg transition-shadow group">
                <div className="relative">
                  <Image
                    src={photo.image || "/placeholder.svg"}
                    alt={photo.title}
                    width={300}
                    height={400}
                    className="w-full h-80 object-cover"
                  />
                  {photo.featured && <Badge className="absolute top-4 left-4 bg-primary">Featured</Badge>}

                  {/* Overlay on hover */}
                  <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center space-x-4">
                    <Button size="sm" variant="secondary" className="bg-white/90 hover:bg-white">
                      <Eye className="h-4 w-4" />
                    </Button>
                    <Button size="sm" variant="secondary" className="bg-white/90 hover:bg-white">
                      <Heart className="h-4 w-4" />
                    </Button>
                    <Button size="sm" variant="secondary" className="bg-white/90 hover:bg-white">
                      <Download className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
                <CardContent className="p-4">
                  <h3 className="font-semibold text-lg mb-1">{photo.title}</h3>
                  <p className="text-sm text-gray-600 mb-2">{photo.category}</p>
                  <p className="text-sm text-gray-500 mb-3">
                    by {photo.photographer} • {photo.location}
                  </p>
                  <div className="flex items-center justify-between text-sm text-gray-500">
                    <div className="flex items-center space-x-4">
                      <span className="flex items-center">
                        <Heart className="h-4 w-4 mr-1" />
                        {photo.likes}
                      </span>
                      <span className="flex items-center">
                        <Eye className="h-4 w-4 mr-1" />
                        {photo.views}
                      </span>
                    </div>
                    <Link href={`/photos/${photo.id}`}>
                      <Button size="sm" variant="ghost" className="text-primary hover:text-primary/90">
                        View Full
                      </Button>
                    </Link>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Load More */}
          <div className="text-center mt-12">
            <Button variant="outline" size="lg">
              Load More Photos
            </Button>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  )
}
