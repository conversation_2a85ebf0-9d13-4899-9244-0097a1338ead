# Shared Package for Web and Mobile

This package contains shared utilities, types, and services that work across both web and mobile platforms.

## Platform-Safe Storage

The shared storage utility provides a consistent API that works on both web (using sessionStorage/localStorage) and mobile (using AsyncStorage).

### Basic Usage

```typescript
import { storage, SafeAPI } from '@shared/types';

// Session storage (temporary, cleared when app closes)
await storage.session.set('key', 'value');
const value = await storage.session.get('key');
await storage.session.remove('key');

// Local storage (persistent)
await storage.local.set('key', 'value');
const value = await storage.local.get('key');
await storage.local.remove('key');

// JSON storage
await storage.json.set('user', { name: '<PERSON>', age: 30 }, false); // local storage
await storage.json.set('temp', { data: 'temp' }, true); // session storage
const user = await storage.json.get<User>('user');
```

### Safe API Usage (for web components)

```typescript
import { SafeAPI } from '@shared/types';

// Safe sessionStorage that won't crash in React Native
const dismissed = SafeAPI.sessionStorage.getItem('pwa-install-dismissed');
SafeAPI.sessionStorage.setItem('pwa-install-dismissed', 'true');

// Safe localStorage
const theme = SafeAPI.localStorage.getItem('theme');
SafeAPI.localStorage.setItem('theme', 'dark');
```

## Platform Detection

```typescript
import { Platform, PlatformComponent } from '@shared/types';

// Platform checks
if (Platform.isWeb) {
  // Web-specific code
}

if (Platform.isMobile) {
  // Mobile-specific code
}

// Feature detection
if (Platform.hasSessionStorage) {
  // Use sessionStorage
}

// Component rendering
const shouldRender = PlatformComponent.shouldRender(['web']); // Only render on web
const styles = PlatformComponent.getStyles(webStyles, mobileStyles);
```

## Shared GraphQL Client

```typescript
import { sharedGraphQLClient } from '@shared/services/graphqlClient';
import { Vendor, Venue, Shop } from '@shared/types';

// Use the same GraphQL operations on both platforms
const vendors = await sharedGraphQLClient.listVendors();
const vendor = await sharedGraphQLClient.getVendor(id);
const newVendor = await sharedGraphQLClient.createVendor(input);
```

## Shared Types

All GraphQL types are shared between web and mobile:

```typescript
import { 
  Vendor, 
  Venue, 
  Shop, 
  UserProfile, 
  Review, 
  Inquiry,
  ModelVendorFilterInput,
  CreateVendorInput,
  UpdateVendorInput 
} from '@shared/types';
```

## Platform-Aware Components

For React components that need to work on both platforms:

```typescript
import { Platform } from '@shared/types';

const MyComponent = () => {
  // Only render PWA prompt on web
  if (!Platform.isWeb) {
    return null;
  }
  
  return <PWAInstallPrompt />;
};
```

## Error Handling

The shared utilities include built-in error handling:

```typescript
// Storage operations won't crash if the platform doesn't support them
await storage.session.set('key', 'value'); // Safe on both platforms

// Safe API calls return null/false instead of throwing
const value = SafeAPI.sessionStorage.getItem('key'); // Returns null in React Native
```

## Best Practices

1. **Always use shared storage utilities** instead of direct sessionStorage/localStorage calls
2. **Use platform detection** for platform-specific features
3. **Use shared GraphQL client** for consistent data operations
4. **Use shared types** for type safety across platforms
5. **Handle platform differences gracefully** with fallbacks

## Migration Guide

### From Direct Storage Usage

```typescript
// Before (web-only)
sessionStorage.setItem('key', 'value');
const value = sessionStorage.getItem('key');

// After (cross-platform)
import { SafeAPI } from '@shared/types';
SafeAPI.sessionStorage.setItem('key', 'value');
const value = SafeAPI.sessionStorage.getItem('key');
```

### From Platform-Specific GraphQL

```typescript
// Before (separate implementations)
// Web: import { getVendor } from '../graphql/queries';
// Mobile: import { getVendor } from '../graphql/queries';

// After (shared)
import { sharedGraphQLClient } from '@shared/services/graphqlClient';
const vendor = await sharedGraphQLClient.getVendor(id);
```

This shared package ensures consistency and reduces code duplication between your web and mobile applications while providing safe fallbacks for platform-specific features.
