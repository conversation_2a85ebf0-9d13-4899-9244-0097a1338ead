import React, { useRef, useState } from 'react';
import {
  View,
  TouchableOpacity,
  StyleSheet,
  Animated,
  Dimensions,
  Text,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import * as Haptics from 'expo-haptics';
import { useTheme } from '../../providers/ThemeProvider';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

interface FABAction {
  icon: string;
  label: string;
  onPress: () => void;
  color?: string;
}

interface FloatingActionButtonProps {
  icon?: string;
  onPress?: () => void;
  actions?: FABAction[];
  position?: 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left';
  size?: 'small' | 'medium' | 'large';
  color?: string;
  backgroundColor?: string;
  style?: any;
  hapticFeedback?: boolean;
  animationType?: 'scale' | 'rotate' | 'bounce';
}

export default function FloatingActionButton({
  icon = 'add',
  onPress,
  actions = [],
  position = 'bottom-right',
  size = 'medium',
  color,
  backgroundColor,
  style,
  hapticFeedback = true,
  animationType = 'scale',
}: FloatingActionButtonProps) {
  const { theme } = useTheme();
  const [isExpanded, setIsExpanded] = useState(false);
  
  // Animation values
  const scaleAnim = useRef(new Animated.Value(1)).current;
  const rotateAnim = useRef(new Animated.Value(0)).current;
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const actionAnimations = useRef(
    actions.map(() => new Animated.Value(0))
  ).current;

  const getSizeValues = () => {
    switch (size) {
      case 'small':
        return { size: 40, iconSize: 20 };
      case 'large':
        return { size: 64, iconSize: 32 };
      default: // medium
        return { size: 56, iconSize: 24 };
    }
  };

  const getPositionStyle = () => {
    const { size: fabSize } = getSizeValues();
    const margin = 16;

    switch (position) {
      case 'bottom-left':
        return {
          position: 'absolute' as const,
          bottom: margin,
          left: margin,
        };
      case 'top-right':
        return {
          position: 'absolute' as const,
          top: margin + 50, // Account for status bar
          right: margin,
        };
      case 'top-left':
        return {
          position: 'absolute' as const,
          top: margin + 50, // Account for status bar
          left: margin,
        };
      default: // bottom-right
        return {
          position: 'absolute' as const,
          bottom: margin,
          right: margin,
        };
    }
  };

  const handlePress = () => {
    if (hapticFeedback) {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    }

    if (actions.length > 0) {
      toggleActions();
    } else if (onPress) {
      onPress();
    }
  };

  const toggleActions = () => {
    const toValue = isExpanded ? 0 : 1;
    setIsExpanded(!isExpanded);

    // Animate main button
    Animated.parallel([
      Animated.spring(rotateAnim, {
        toValue: toValue * 0.75, // 270 degrees
        useNativeDriver: true,
      }),
      Animated.timing(fadeAnim, {
        toValue,
        duration: 200,
        useNativeDriver: true,
      }),
    ]).start();

    // Animate action buttons with stagger
    const animations = actionAnimations.map((anim, index) =>
      Animated.timing(anim, {
        toValue,
        duration: 200,
        delay: index * 50,
        useNativeDriver: true,
      })
    );

    Animated.parallel(animations).start();
  };

  const handlePressIn = () => {
    if (hapticFeedback) {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }

    switch (animationType) {
      case 'scale':
        Animated.spring(scaleAnim, {
          toValue: 0.9,
          useNativeDriver: true,
        }).start();
        break;
      case 'bounce':
        Animated.spring(scaleAnim, {
          toValue: 0.85,
          tension: 300,
          friction: 10,
          useNativeDriver: true,
        }).start();
        break;
    }
  };

  const handlePressOut = () => {
    switch (animationType) {
      case 'scale':
      case 'bounce':
        Animated.spring(scaleAnim, {
          toValue: 1,
          useNativeDriver: true,
        }).start();
        break;
    }
  };

  const getMainButtonStyle = () => {
    const { size: fabSize } = getSizeValues();
    
    return {
      width: fabSize,
      height: fabSize,
      borderRadius: fabSize / 2,
      backgroundColor: backgroundColor || theme.colors.primary,
      alignItems: 'center' as const,
      justifyContent: 'center' as const,
      elevation: 8,
      shadowColor: theme.colors.text,
      shadowOffset: {
        width: 0,
        height: 4,
      },
      shadowOpacity: 0.3,
      shadowRadius: 8,
    };
  };

  const getActionButtonStyle = (index: number) => {
    const { size: fabSize } = getSizeValues();
    const actionSize = fabSize * 0.8;
    const spacing = fabSize + 16;
    
    const translateY = actionAnimations[index].interpolate({
      inputRange: [0, 1],
      outputRange: [0, -(spacing * (index + 1))],
    });

    const scale = actionAnimations[index].interpolate({
      inputRange: [0, 1],
      outputRange: [0, 1],
    });

    return {
      position: 'absolute' as const,
      width: actionSize,
      height: actionSize,
      borderRadius: actionSize / 2,
      backgroundColor: theme.colors.surface,
      alignItems: 'center' as const,
      justifyContent: 'center' as const,
      elevation: 4,
      shadowColor: theme.colors.text,
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowOpacity: 0.2,
      shadowRadius: 4,
      transform: [
        { translateY },
        { scale },
      ],
    };
  };

  const getMainButtonAnimatedStyle = () => {
    const rotate = rotateAnim.interpolate({
      inputRange: [0, 1],
      outputRange: ['0deg', '225deg'],
    });

    return {
      transform: [
        { scale: scaleAnim },
        { rotate },
      ],
    };
  };

  const renderActionButton = (action: FABAction, index: number) => {
    const { iconSize } = getSizeValues();
    
    return (
      <Animated.View key={index} style={getActionButtonStyle(index)}>
        <TouchableOpacity
          style={styles.actionButton}
          onPress={() => {
            if (hapticFeedback) {
              Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
            }
            action.onPress();
            toggleActions();
          }}
          activeOpacity={0.8}
        >
          <Ionicons
            name={action.icon as any}
            size={iconSize * 0.8}
            color={action.color || theme.colors.text}
          />
        </TouchableOpacity>
        
        {/* Action label */}
        <Animated.View
          style={[
            styles.actionLabel,
            {
              opacity: fadeAnim,
              backgroundColor: theme.colors.text,
            },
          ]}
        >
          <Text style={[styles.actionLabelText, { color: theme.colors.background }]}>
            {action.label}
          </Text>
        </Animated.View>
      </Animated.View>
    );
  };

  const renderBackdrop = () => {
    if (!isExpanded) return null;

    return (
      <Animated.View
        style={[
          styles.backdrop,
          {
            opacity: fadeAnim.interpolate({
              inputRange: [0, 1],
              outputRange: [0, 0.5],
            }),
          },
        ]}
        pointerEvents={isExpanded ? 'auto' : 'none'}
      >
        <TouchableOpacity
          style={styles.backdropTouchable}
          onPress={toggleActions}
          activeOpacity={1}
        />
      </Animated.View>
    );
  };

  const { iconSize } = getSizeValues();

  return (
    <>
      {renderBackdrop()}
      
      <View style={[getPositionStyle(), style]}>
        {/* Action buttons */}
        {actions.map((action, index) => renderActionButton(action, index))}
        
        {/* Main FAB */}
        <Animated.View style={getMainButtonAnimatedStyle()}>
          <TouchableOpacity
            style={getMainButtonStyle()}
            onPress={handlePress}
            onPressIn={handlePressIn}
            onPressOut={handlePressOut}
            activeOpacity={0.8}
          >
            <Ionicons
              name={icon as any}
              size={iconSize}
              color={color || '#FFFFFF'}
            />
          </TouchableOpacity>
        </Animated.View>
      </View>
    </>
  );
}

const styles = StyleSheet.create({
  backdrop: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'black',
    zIndex: 999,
  },
  backdropTouchable: {
    flex: 1,
  },
  actionButton: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  actionLabel: {
    position: 'absolute',
    right: 60,
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    minWidth: 80,
    alignItems: 'center',
  },
  actionLabelText: {
    fontSize: 12,
    fontWeight: '600',
  },
});
