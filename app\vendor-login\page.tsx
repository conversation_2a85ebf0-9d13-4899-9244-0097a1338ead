"use client"
import { TopHeader } from "@/components/top-header"
import { <PERSON><PERSON> } from "@/components/header"
import { <PERSON><PERSON> } from "@/components/footer"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Checkbox } from "@/components/ui/checkbox"
import { Separator } from "@/components/ui/separator"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Eye, EyeOff, Mail, Lock, User, AlertCircle, Building2, Store, Camera, Utensils, Palette, Music } from "lucide-react"
import Link from "next/link"
import { useState } from "react"
import { useAuth } from "@/contexts/AuthContext"
import { useRouter } from "next/navigation"
import { profileService } from "@/lib/services/profileService"
import toast from 'react-hot-toast'
import AuthRoutingService from "@/lib/services/authRouting"

export default function VendorLoginPage() {
  const [isSignUp, setIsSignUp] = useState(false)
  const [showPassword, setShowPassword] = useState(false)
  const [isPhone, setIsPhone] = useState(false)
  const [isConfirmation, setIsConfirmation] = useState(false)
  const [isForgotPassword, setIsForgotPassword] = useState(false)
  const [isResetPassword, setIsResetPassword] = useState(false)
  const [formData, setFormData] = useState({
    businessName: '',
    fullName: '',
    emailOrPhone: '',
    password: '',
    confirmationCode: '',
    resetCode: '',
    newPassword: '',
    businessType: '',
    agreeToTerms: false
  })
  const [error, setError] = useState('')
  const [loading, setLoading] = useState(false)

  const { signInWithPassword, signUp, businessSignUp, confirmSignUp, resendConfirmationCode, requestPasswordReset, confirmPasswordReset, userType, userProfile, refreshUserProfile } = useAuth()
  const router = useRouter()

  const businessTypes = [
    { value: 'photographer', label: 'Wedding Photographer', icon: Camera },
    { value: 'venue', label: 'Wedding Venue', icon: Building2 },
    { value: 'catering', label: 'Catering Service', icon: Utensils },
    { value: 'decoration', label: 'Decoration & Design', icon: Palette },
    { value: 'music', label: 'Music & Entertainment', icon: Music },
    { value: 'shop', label: 'Wedding Shop', icon: Store },
    { value: 'other', label: 'Other Services', icon: Building2 }
  ]

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    })
  }

  const validateInput = (input: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    const phoneRegex = /^(\+91|91)?[6-9]\d{9}$/ // Indian phone number format

    if (emailRegex.test(input)) {
      setIsPhone(false)
      return true
    } else if (phoneRegex.test(input.replace(/\s|-|\(|\)/g, ''))) {
      setIsPhone(true)
      return true
    }
    return false
  }

  const handleSignIn = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError('')

    if (!validateInput(formData.emailOrPhone)) {
      setError('Please enter a valid email address or phone number')
      setLoading(false)
      return
    }

    try {
      await signInWithPassword(formData.emailOrPhone, formData.password)
      toast.success('Welcome back! Redirecting to your dashboard...')

      // Wait a moment for user profile to be fetched, then route appropriately
      setTimeout(() => {
        const dashboardRoute = AuthRoutingService.getDashboardRoute(userType, userProfile)
        router.push(dashboardRoute)
      }, 1000)
    } catch (error: any) {
      // Handle unconfirmed user - redirect to OTP verification
      if (error.name === 'UserNotConfirmedException') {
        setIsConfirmation(true)
        toast.info('Please verify your email address first.')
        // Automatically resend confirmation code
        try {
          await resendConfirmationCode(formData.emailOrPhone)
        } catch (resendError) {
          console.error('Failed to resend confirmation code:', resendError)
        }
        setLoading(false)
        return
      }

      toast.error(error.message || 'Failed to sign in')
      setError(error.message || 'Failed to sign in')
    } finally {
      setLoading(false)
    }
  }

  const handleSignUp = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError('')

    if (!validateInput(formData.emailOrPhone)) {
      setError('Please enter a valid email address or phone number')
      setLoading(false)
      return
    }

    if (formData.password.length < 8) {
      setError('Password must be at least 8 characters long')
      setLoading(false)
      return
    }

    if (!formData.businessName.trim()) {
      setError('Business name is required')
      setLoading(false)
      return
    }

    if (!formData.businessType) {
      setError('Please select your business type')
      setLoading(false)
      return
    }

    if (!formData.agreeToTerms) {
      setError('Please agree to the terms and conditions')
      setLoading(false)
      return
    }

    try {
      // Use business sign up with additional business attributes
      await businessSignUp({
        businessName: formData.businessName,
        businessType: formData.businessType,
        fullName: formData.fullName,
        emailOrPhone: formData.emailOrPhone,
        password: formData.password,
        isPhone: isPhone
      })
      toast.success('Verification code sent! Please check your email/phone.')
      setIsConfirmation(true)
    } catch (error: any) {
      toast.error(error.message || 'Failed to create business account')
      setError(error.message || 'Failed to create business account')
    } finally {
      setLoading(false)
    }
  }

  const handleConfirmSignUp = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError('')

    try {
      await confirmSignUp(formData.emailOrPhone, formData.confirmationCode)
      // After confirming sign up, automatically sign in
      await signInWithPassword(formData.emailOrPhone, formData.password)

      // Create business profile after successful authentication
      try {
        // Get business data from localStorage
        const pendingBusinessData = localStorage.getItem('pendingBusinessData')
        if (pendingBusinessData) {
          const businessData = JSON.parse(pendingBusinessData)

          // Create user profile with business information
          await profileService.createProfile({
            firstName: businessData.fullName.split(' ')[0] || businessData.fullName,
            lastName: businessData.fullName.split(' ').slice(1).join(' ') || '',
            email: businessData.emailOrPhone.includes('@') ? businessData.emailOrPhone : '',
            phone: !businessData.emailOrPhone.includes('@') ? businessData.emailOrPhone : '',
            businessInfo: {
              businessName: businessData.businessName,
              businessType: businessData.businessType,
              businessEmail: businessData.emailOrPhone.includes('@') ? businessData.emailOrPhone : '',
              businessPhone: !businessData.emailOrPhone.includes('@') ? businessData.emailOrPhone : ''
            },
            isVendor: true
          })

          // Force refresh the auth context to pick up the new vendor profile
          await refreshUserProfile()

          // Clear the temporary data
          localStorage.removeItem('pendingBusinessData')
          toast.success('Business account created successfully! Welcome to BookmyFestive.')
        }
      } catch (profileError) {
        console.error('Error creating business profile:', profileError)
        toast.error('Account verified, but failed to create business profile. You can complete it later.')
        // Don't block the user from proceeding even if profile creation fails
      }

      router.push('/dashboard')
    } catch (error: any) {
      toast.error(error.message || 'Failed to confirm sign up')
      setError(error.message || 'Failed to confirm sign up')
    } finally {
      setLoading(false)
    }
  }

  const handleForgotPassword = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError('')

    if (!validateInput(formData.emailOrPhone)) {
      setError('Please enter a valid email address or phone number')
      setLoading(false)
      return
    }

    try {
      await requestPasswordReset(formData.emailOrPhone)
      setIsResetPassword(true)
    } catch (error: any) {
      setError(error.message || 'Failed to send reset code')
    } finally {
      setLoading(false)
    }
  }

  const handleResetPassword = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError('')

    if (formData.newPassword.length < 8) {
      setError('Password must be at least 8 characters long')
      setLoading(false)
      return
    }

    try {
      await confirmPasswordReset(formData.emailOrPhone, formData.resetCode, formData.newPassword)
      setIsForgotPassword(false)
      setIsResetPassword(false)
      setFormData({ ...formData, password: formData.newPassword, resetCode: '', newPassword: '' })
    } catch (error: any) {
      setError(error.message || 'Failed to reset password')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-white flex flex-col">
      <TopHeader />
      <Header />
      <section className="flex flex-1 items-center justify-center bg-gradient-to-br from-primary/5 to-secondary/5" style={{paddingTop: 80, paddingBottom: 40}}>
        <div className="w-full max-w-6xl mx-auto bg-white rounded-xl shadow-lg overflow-hidden flex flex-col lg:flex-row">
          {/* Login Form - First on mobile, second on desktop */}
          <div className="w-full lg:w-1/2 flex flex-col justify-center p-8 lg:p-12 lg:order-2">
            <div className="mb-6 text-center">
              <h2 className="text-2xl lg:text-3xl font-bold text-gray-900 mb-2">
                {isConfirmation ? 'Verify Your Account' : 
                 isForgotPassword ? (isResetPassword ? 'Reset Password' : 'Forgot Password') :
                 isSignUp ? 'Create Business Account' : 'Business Sign In'}
              </h2>
              <p className="text-gray-600">
                {isConfirmation ? 'Enter the verification code sent to your email/phone' :
                 isForgotPassword ? (isResetPassword ? 'Enter the code and your new password' : 'Enter your email or phone to reset password') :
                 isSignUp ? 'Start growing your wedding business today' : 'Welcome back! Sign in to your business account'}
              </p>
            </div>

            {error && (
              <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg flex items-center gap-2 text-red-700">
                <AlertCircle className="h-4 w-4" />
                <span className="text-sm">{error}</span>
              </div>
            )}

            {/* Confirmation Form */}
            {isConfirmation ? (
              <form onSubmit={handleConfirmSignUp} className="space-y-4">
                <div className="relative">
                  <Input
                    name="confirmationCode"
                    type="text"
                    placeholder="Enter verification code"
                    className="pl-12"
                    value={formData.confirmationCode}
                    onChange={handleInputChange}
                    required
                  />
                  <span className="absolute left-4 top-1/2 -translate-y-1/2 text-primary">
                    <AlertCircle className="h-5 w-5" />
                  </span>
                </div>
                <Button type="submit" className="w-full bg-primary hover:bg-primary/90" disabled={loading}>
                  {loading ? 'Verifying...' : 'Verify Account'}
                </Button>
                <div className="text-center">
                  <button
                    type="button"
                    className="text-sm text-primary hover:underline"
                    onClick={async () => {
                      try {
                        await resendConfirmationCode(formData.emailOrPhone)
                        toast.success('Verification code resent successfully!')
                      } catch (error: any) {
                        toast.error(error.message || 'Failed to resend code')
                        setError(error.message || 'Failed to resend code')
                      }
                    }}
                  >
                    Resend verification code
                  </button>
                </div>
              </form>
            ) : isForgotPassword ? (
              /* Forgot Password Forms */
              isResetPassword ? (
                <form onSubmit={handleResetPassword} className="space-y-4">
                  <div className="relative">
                    <Input
                      name="resetCode"
                      type="text"
                      placeholder="Enter reset code"
                      className="pl-12"
                      value={formData.resetCode}
                      onChange={handleInputChange}
                      required
                    />
                    <span className="absolute left-4 top-1/2 -translate-y-1/2 text-primary">
                      <AlertCircle className="h-5 w-5" />
                    </span>
                  </div>
                  <div className="relative">
                    <Input
                      name="newPassword"
                      type={showPassword ? "text" : "password"}
                      placeholder="New Password*"
                      className="pl-12 pr-12"
                      value={formData.newPassword}
                      onChange={handleInputChange}
                      required
                    />
                    <span className="absolute left-4 top-1/2 -translate-y-1/2 text-primary">
                      <Lock className="h-5 w-5" />
                    </span>
                    <button
                      type="button"
                      className="absolute right-4 top-1/2 -translate-y-1/2 text-gray-500 hover:text-gray-700"
                      onClick={() => setShowPassword(!showPassword)}
                    >
                      {showPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
                    </button>
                  </div>
                  <Button type="submit" className="w-full bg-primary hover:bg-primary/90" disabled={loading}>
                    {loading ? 'Resetting...' : 'Reset Password'}
                  </Button>
                  <Button
                    type="button"
                    variant="outline"
                    className="w-full"
                    onClick={() => {
                      setIsForgotPassword(false)
                      setIsResetPassword(false)
                    }}
                  >
                    Back to Sign In
                  </Button>
                </form>
              ) : (
                <form onSubmit={handleForgotPassword} className="space-y-4">
                  <div className="relative">
                    <Input
                      name="emailOrPhone"
                      type="text"
                      placeholder="Enter email or phone number"
                      className="pl-12"
                      value={formData.emailOrPhone}
                      onChange={handleInputChange}
                      required
                    />
                    <span className="absolute left-4 top-1/2 -translate-y-1/2 text-primary">
                      <Mail className="h-5 w-5" />
                    </span>
                  </div>
                  <Button type="submit" className="w-full bg-primary hover:bg-primary/90" disabled={loading}>
                    {loading ? 'Sending...' : 'Send Reset Code'}
                  </Button>
                  <Button
                    type="button"
                    variant="outline"
                    className="w-full"
                    onClick={() => setIsForgotPassword(false)}
                  >
                    Back to Sign In
                  </Button>
                </form>
              )
            ) : (
              /* Main Sign In/Sign Up Form */
              <form onSubmit={isSignUp ? handleSignUp : handleSignIn} className="space-y-4">
                {isSignUp && (
                  <>
                    <div className="relative">
                      <Input
                        name="businessName"
                        type="text"
                        placeholder="Business Name*"
                        className="pl-12"
                        value={formData.businessName}
                        onChange={handleInputChange}
                        required
                      />
                      <span className="absolute left-4 top-1/2 -translate-y-1/2 text-primary">
                        <Building2 className="h-5 w-5" />
                      </span>
                    </div>
                    <div className="relative">
                      <Input
                        name="fullName"
                        type="text"
                        placeholder="Your Full Name*"
                        className="pl-12"
                        value={formData.fullName}
                        onChange={handleInputChange}
                        required
                      />
                      <span className="absolute left-4 top-1/2 -translate-y-1/2 text-primary">
                        <User className="h-5 w-5" />
                      </span>
                    </div>
                    <div className="space-y-2">
                      <label className="text-sm font-medium text-gray-700">Business Type*</label>
                      <Select
                        value={formData.businessType}
                        onValueChange={(value) => setFormData({ ...formData, businessType: value })}
                      >
                        <SelectTrigger className="w-full">
                          <SelectValue placeholder="Select your business type" />
                        </SelectTrigger>
                        <SelectContent>
                          {businessTypes.map((type) => {
                            const IconComponent = type.icon
                            return (
                              <SelectItem key={type.value} value={type.value}>
                                <div className="flex items-center gap-2">
                                  <IconComponent className="h-4 w-4" />
                                  <span>{type.label}</span>
                                </div>
                              </SelectItem>
                            )
                          })}
                        </SelectContent>
                      </Select>
                    </div>
                  </>
                )}
                <div className="relative">
                  <Input
                    name="emailOrPhone"
                    type="text"
                    placeholder="Business Email or Phone Number*"
                    className="pl-12"
                    value={formData.emailOrPhone}
                    onChange={handleInputChange}
                    required
                  />
                  <span className="absolute left-4 top-1/2 -translate-y-1/2 text-primary">
                    <Mail className="h-5 w-5" />
                  </span>
                </div>
                <div className="relative">
                  <Input
                    name="password"
                    type={showPassword ? "text" : "password"}
                    placeholder="Password*"
                    className="pl-12 pr-12"
                    value={formData.password}
                    onChange={handleInputChange}
                    required
                  />
                  <span className="absolute left-4 top-1/2 -translate-y-1/2 text-primary">
                    <Lock className="h-5 w-5" />
                  </span>
                  <button
                    type="button"
                    className="absolute right-4 top-1/2 -translate-y-1/2 text-gray-500 hover:text-gray-700"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
                  </button>
                </div>

                {isSignUp && (
                  <div className="flex items-start space-x-2">
                    <Checkbox
                      id="terms"
                      checked={formData.agreeToTerms}
                      onCheckedChange={(checked) => setFormData({ ...formData, agreeToTerms: checked as boolean })}
                      className="mt-1"
                    />
                    <label htmlFor="terms" className="text-sm text-gray-600">
                      I agree to the{" "}
                      <Link href="/terms" className="text-primary hover:text-primary/90">
                        Terms of Service
                      </Link>{" "}
                      and{" "}
                      <Link href="/privacy" className="text-primary hover:text-primary/90">
                        Privacy Policy
                      </Link>
                    </label>
                  </div>
                )}

                {!isSignUp && (
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <Checkbox id="remember" />
                      <label htmlFor="remember" className="text-sm text-gray-600">
                        Remember me
                      </label>
                    </div>
                    <button
                      type="button"
                      className="text-sm text-primary hover:underline"
                      onClick={() => setIsForgotPassword(true)}
                    >
                      Forgot password?
                    </button>
                  </div>
                )}

                <Button type="submit" className="w-full bg-primary hover:bg-primary/90" disabled={loading}>
                  {loading ? (isSignUp ? 'Creating Account...' : 'Signing In...') : (isSignUp ? 'Create Business Account' : 'Sign In to Dashboard')}
                </Button>

                <div className="text-center">
                  <button
                    type="button"
                    className="text-sm text-primary hover:underline"
                    onClick={() => setIsSignUp(!isSignUp)}
                  >
                    {isSignUp ? 'Already have a business account? Sign In' : "Don't have a business account? Sign Up"}
                  </button>
                </div>
              </form>
            )}

            <div className="mt-6">
              <Separator className="my-4" />
              <div className="text-center">
                <span className="text-gray-600 text-sm">Looking for personal account?</span>
                <Link href="/login" className="ml-2 text-primary hover:underline text-sm font-medium">
                  Customer Sign In
                </Link>
              </div>
            </div>
          </div>

          {/* Business Benefits - Second on mobile, first on desktop */}
          <div className="w-full lg:w-1/2 bg-gradient-to-br from-primary to-pink-600 p-8 lg:p-12 text-white lg:order-1">
            <div className="h-full flex flex-col justify-center">
              <div className="mb-8">
                <h1 className="text-3xl lg:text-4xl font-bold mb-4">
                  Grow Your Wedding Business
                </h1>
                <p className="text-lg opacity-90 mb-6">
                  Join thousands of wedding professionals who trust BookmyFestive to connect with couples planning their dream wedding.
                </p>
              </div>

              <div className="space-y-4 mb-8">
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center">
                    <span className="text-sm font-bold">✓</span>
                  </div>
                  <span>Reach thousands of engaged couples</span>
                </div>
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center">
                    <span className="text-sm font-bold">✓</span>
                  </div>
                  <span>Manage bookings and inquiries</span>
                </div>
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center">
                    <span className="text-sm font-bold">✓</span>
                  </div>
                  <span>Showcase your portfolio</span>
                </div>
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center">
                    <span className="text-sm font-bold">✓</span>
                  </div>
                  <span>Get verified business badge</span>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4 text-center">
                <div>
                  <div className="text-2xl font-bold">10,000+</div>
                  <div className="text-sm opacity-80">Active Couples</div>
                </div>
                <div>
                  <div className="text-2xl font-bold">5,000+</div>
                  <div className="text-sm opacity-80">Trusted Vendors</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
      <Footer />
    </div>
  )
}