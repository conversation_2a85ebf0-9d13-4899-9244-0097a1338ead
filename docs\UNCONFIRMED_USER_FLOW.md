# Unconfirmed User Flow Implementation

## Overview

This document describes the implementation of proper handling for unconfirmed users during login attempts. When a user tries to login but their email address is not yet verified, the system now automatically redirects them to the OTP verification flow.

## Changes Made

### 1. AuthContext.tsx Updates

**File**: `contexts/AuthContext.tsx`

Updated the `handleSignInWithPassword` function to properly handle AWS Amplify v6 response structure:

- Added check for `result.isSignedIn` status
- Added detection of `result.nextStep?.signInStep === 'CONFIRM_SIGN_UP'`
- Throws a specific `UserNotConfirmedException` error with the username for UI handling

```typescript
if (result.nextStep?.signInStep === 'CONFIRM_SIGN_UP') {
  const error = new Error('Please verify your email address first. Check your inbox for the verification code.');
  (error as any).name = 'UserNotConfirmedException';
  (error as any).username = emailOrPhone;
  throw error;
}
```

### 2. Login Page Updates

**File**: `app/login/page.tsx`

Enhanced the `handleSignIn` function to catch `UserNotConfirmedException` and automatically:

- Switch to confirmation mode (`setIsConfirmation(true)`)
- Display helpful message to user
- Automatically resend confirmation code
- Start resend timer

Updated the `handleOTPVerification` function to:

- Handle both signup and login confirmation flows
- Use the correct email address for confirmation
- Maintain password for auto-login after verification

### 3. Vendor Login Page Updates

**File**: `app/vendor-login/page.tsx`

Added similar unconfirmed user handling:

- Catches `UserNotConfirmedException`
- Switches to confirmation mode
- Shows toast notification
- Automatically resends confirmation code

### 4. Admin Login Page Updates

**File**: `app/admin-login/page.tsx`

Added basic unconfirmed user handling:

- Shows appropriate error message for unconfirmed admin accounts
- Directs users to contact system administrator

### 5. Test Page

**File**: `app/test-unconfirmed-user/page.tsx`

Created a test page to verify the unconfirmed user flow:

- Step 1: Create an unconfirmed user
- Step 2: Try to login (should fail with proper error)
- Step 3: Test confirmation flow

## User Flow

### Before Changes

1. User tries to login with unconfirmed account
2. Generic error message displayed
3. User confused about what to do next

### After Changes

1. User tries to login with unconfirmed account
2. System detects `UserNotConfirmedException`
3. Automatically switches to OTP verification mode
4. Shows clear message: "Please verify your email address first"
5. Automatically resends confirmation code
6. User enters OTP and gets verified
7. System automatically logs user in after verification

## Error Handling

The system now properly handles these AWS Amplify authentication errors:

- `UserNotFoundException` - User doesn't exist
- `NotAuthorizedException` - Wrong password
- `UserNotConfirmedException` - User exists but email not verified

## Testing

To test the unconfirmed user flow:

1. Visit `/test-unconfirmed-user`
2. Create a test account (Step 1)
3. Try to login without confirming (Step 2)
4. Verify the error is caught and handled properly

## Benefits

1. **Better User Experience**: Users are automatically guided to verify their email
2. **Reduced Support Requests**: Clear messaging about what users need to do
3. **Seamless Flow**: Automatic transition from login attempt to verification
4. **Consistent Behavior**: Same handling across all login pages

## Technical Notes

- Uses AWS Amplify v6 `signIn` response structure
- Maintains backward compatibility with existing flows
- Preserves user's password for auto-login after verification
- Handles both email and phone number login attempts
- Automatically resends confirmation codes when needed

## Future Enhancements

1. Add rate limiting for confirmation code resends
2. Implement phone number verification for mobile users
3. Add email template customization for verification codes
4. Consider implementing magic link verification as alternative
