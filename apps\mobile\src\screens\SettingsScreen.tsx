import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
  Switch,
  Alert,
  RefreshControl,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTranslation } from 'react-i18next';
import { useNavigation } from '@react-navigation/native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useOffline } from '../providers/OfflineProvider';
import { useTheme } from '../providers/ThemeProvider';

import LanguageSelector from '../components/LanguageSelector';
import { getCurrentLanguage, SUPPORTED_LANGUAGES } from '../i18n';

interface SettingItemProps {
  icon: string;
  title: string;
  subtitle?: string;
  value?: string;
  showArrow?: boolean;
  showSwitch?: boolean;
  switchValue?: boolean;
  onPress?: () => void;
  onSwitchChange?: (value: boolean) => void;
}

const SettingItem: React.FC<SettingItemProps> = ({
  icon,
  title,
  subtitle,
  value,
  showArrow = true,
  showSwitch = false,
  switchValue = false,
  onPress,
  onSwitchChange,
}) => {
  return (
    <TouchableOpacity
      style={styles.settingItem}
      onPress={onPress}
      disabled={showSwitch}
      activeOpacity={0.7}
    >
      <View style={styles.settingItemLeft}>
        <View style={styles.iconContainer}>
          <Ionicons name={icon as any} size={20} color="#8B0000" />
        </View>
        <View style={styles.settingItemContent}>
          <Text style={styles.settingItemTitle}>{title}</Text>
          {subtitle && (
            <Text style={styles.settingItemSubtitle}>{subtitle}</Text>
          )}
        </View>
      </View>
      <View style={styles.settingItemRight}>
        {value && (
          <Text style={styles.settingItemValue}>{value}</Text>
        )}
        {showSwitch ? (
          <Switch
            value={switchValue}
            onValueChange={onSwitchChange}
            trackColor={{ false: '#e0e0e0', true: '#8B0000' }}
            thumbColor="#fff"
          />
        ) : (
          showArrow && (
            <Ionicons name="chevron-forward" size={20} color="#ccc" />
          )
        )}
      </View>
    </TouchableOpacity>
  );
};

const SettingsScreen: React.FC = () => {
  const { t } = useTranslation();
  const navigation = useNavigation();
  const { theme } = useTheme();
  const {
    isOffline,
    networkState,
    connectionQuality,
    isSyncing,
    pendingActionsCount,
    cacheStats,
    forceSync,
    clearOfflineData,
    cleanExpiredCache,
  } = useOffline();

  const [languageSelectorVisible, setLanguageSelectorVisible] = useState(false);
  const [currentLanguage, setCurrentLanguage] = useState('en');
  const [notificationsEnabled, setNotificationsEnabled] = useState(true);
  const [emailNotifications, setEmailNotifications] = useState(true);
  const [pushNotifications, setPushNotifications] = useState(true);
  const [darkMode, setDarkMode] = useState(false);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    loadSettings();
  }, []);

  const loadSettings = async () => {
    try {
      // Load current language
      const lang = getCurrentLanguage();
      setCurrentLanguage(lang);

      // Load other settings from AsyncStorage
      const notifications = await AsyncStorage.getItem('notifications-enabled');
      const emailNotifs = await AsyncStorage.getItem('email-notifications');
      const pushNotifs = await AsyncStorage.getItem('push-notifications');
      const theme = await AsyncStorage.getItem('dark-mode');

      if (notifications !== null) setNotificationsEnabled(JSON.parse(notifications));
      if (emailNotifs !== null) setEmailNotifications(JSON.parse(emailNotifs));
      if (pushNotifs !== null) setPushNotifications(JSON.parse(pushNotifs));
      if (theme !== null) setDarkMode(JSON.parse(theme));
    } catch (error) {
      console.error('Error loading settings:', error);
    }
  };

  const saveSettingToStorage = async (key: string, value: boolean) => {
    try {
      await AsyncStorage.setItem(key, JSON.stringify(value));
    } catch (error) {
      console.error(`Error saving ${key}:`, error);
    }
  };

  const getCurrentLanguageName = () => {
    const language = SUPPORTED_LANGUAGES.find(lang => lang.code === currentLanguage);
    return language ? language.nativeName : 'English';
  };

  const handleLanguageChange = (languageCode: string) => {
    setCurrentLanguage(languageCode);
  };

  const handleNotificationsToggle = (value: boolean) => {
    setNotificationsEnabled(value);
    saveSettingToStorage('notifications-enabled', value);
  };

  const handleEmailNotificationsToggle = (value: boolean) => {
    setEmailNotifications(value);
    saveSettingToStorage('email-notifications', value);
  };

  const handlePushNotificationsToggle = (value: boolean) => {
    setPushNotifications(value);
    saveSettingToStorage('push-notifications', value);
  };

  const handleDarkModeToggle = (value: boolean) => {
    setDarkMode(value);
    saveSettingToStorage('dark-mode', value);
    // TODO: Implement theme switching
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    try {
      await forceSync();
      await loadSettings();
    } catch (error) {
      console.error('Error during refresh:', error);
    } finally {
      setRefreshing(false);
    }
  };

  const handleClearOfflineData = () => {
    Alert.alert(
      'Clear Offline Data',
      'This will remove all cached data and pending offline actions. Are you sure?',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Clear',
          style: 'destructive',
          onPress: async () => {
            try {
              await clearOfflineData();
              Alert.alert('Success', 'Offline data cleared successfully');
            } catch (error) {
              Alert.alert('Error', 'Failed to clear offline data');
            }
          },
        },
      ]
    );
  };

  const handleCleanCache = async () => {
    try {
      const cleanedCount = await cleanExpiredCache();
      Alert.alert('Success', `Cleaned ${cleanedCount} expired cache entries`);
    } catch (error) {
      Alert.alert('Error', 'Failed to clean cache');
    }
  };

  const formatBytes = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getConnectionStatusText = (): string => {
    if (isOffline) return 'Offline';
    return `${connectionQuality.charAt(0).toUpperCase() + connectionQuality.slice(1)} (${networkState.strength}%)`;
  };

  const handleLogout = () => {
    Alert.alert(
      t('auth.logout'),
      'Are you sure you want to logout?',
      [
        {
          text: t('common.cancel'),
          style: 'cancel',
        },
        {
          text: t('auth.logout'),
          style: 'destructive',
          onPress: async () => {
            try {
              // Clear offline data and logout
              await clearOfflineData();
              // Note: Add logout functionality when auth provider is available
              console.log('Logout pressed - implement with auth provider');
            } catch (error) {
              console.error('Logout error:', error);
              Alert.alert('Error', 'Failed to logout. Please try again.');
            }
          },
        },
      ]
    );
  };

  return (
    <View style={styles.container}>
      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            tintColor={theme.colors.primary}
          />
        }
      >
        {/* Account Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Account</Text>
          <SettingItem
            icon="person-outline"
            title="Profile"
            subtitle="Manage your profile information"
            onPress={() => console.log('Profile pressed')}
          />
          <SettingItem
            icon="card-outline"
            title="Payment Methods"
            subtitle="Manage your payment options"
            onPress={() => console.log('Payment methods pressed')}
          />
          <SettingItem
            icon="location-outline"
            title="Addresses"
            subtitle="Manage your saved addresses"
            onPress={() => console.log('Addresses pressed')}
          />
        </View>

        {/* Preferences Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Preferences</Text>
          <SettingItem
            icon="language-outline"
            title={t('common.language')}
            subtitle="Choose your preferred language"
            value={getCurrentLanguageName()}
            onPress={() => setLanguageSelectorVisible(true)}
          />
          <SettingItem
            icon="moon-outline"
            title={t('common.theme')}
            subtitle="Switch between light and dark mode"
            showSwitch={true}
            switchValue={darkMode}
            onSwitchChange={handleDarkModeToggle}
            showArrow={false}
          />
        </View>

        {/* Notifications Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>{t('common.notifications')}</Text>
          <SettingItem
            icon="notifications-outline"
            title="Push Notifications"
            subtitle="Receive notifications on your device"
            showSwitch={true}
            switchValue={pushNotifications}
            onSwitchChange={handlePushNotificationsToggle}
            showArrow={false}
          />
          <SettingItem
            icon="mail-outline"
            title="Email Notifications"
            subtitle="Receive notifications via email"
            showSwitch={true}
            switchValue={emailNotifications}
            onSwitchChange={handleEmailNotificationsToggle}
            showArrow={false}
          />
        </View>

        {/* Offline & Sync Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Offline & Sync</Text>
          <SettingItem
            icon={isOffline ? "cloud-offline-outline" : "cloud-done-outline"}
            title="Connection Status"
            subtitle={getConnectionStatusText()}
            value={isSyncing ? "Syncing..." : ""}
            showArrow={false}
          />
          {pendingActionsCount > 0 && (
            <SettingItem
              icon="sync-outline"
              title="Pending Actions"
              subtitle={`${pendingActionsCount} actions waiting to sync`}
              onPress={handleRefresh}
            />
          )}
          {cacheStats && (
            <SettingItem
              icon="archive-outline"
              title="Cache Storage"
              subtitle={`${cacheStats.totalItems} items, ${formatBytes(cacheStats.totalSize)}`}
              onPress={handleCleanCache}
            />
          )}
          <SettingItem
            icon="refresh-outline"
            title="Force Sync"
            subtitle="Sync all pending data now"
            onPress={handleRefresh}
          />
          <SettingItem
            icon="trash-outline"
            title="Clear Offline Data"
            subtitle="Remove all cached data and pending actions"
            onPress={handleClearOfflineData}
          />
        </View>

        {/* Support Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Support</Text>
          <SettingItem
            icon="help-circle-outline"
            title={t('common.help')}
            subtitle="Get help and support"
            onPress={() => console.log('Help pressed')}
          />
          <SettingItem
            icon="chatbubble-outline"
            title="Contact Us"
            subtitle="Send us your feedback"
            onPress={() => console.log('Contact pressed')}
          />
          <SettingItem
            icon="document-text-outline"
            title={t('common.terms')}
            subtitle="Terms and conditions"
            onPress={() => console.log('Terms pressed')}
          />
          <SettingItem
            icon="shield-outline"
            title={t('common.privacy')}
            subtitle="Privacy policy"
            onPress={() => console.log('Privacy pressed')}
          />
        </View>

        {/* Account Actions */}
        <View style={styles.section}>
          <SettingItem
            icon="log-out-outline"
            title={t('auth.logout')}
            subtitle="Sign out of your account"
            onPress={handleLogout}
            showArrow={false}
          />
        </View>

        {/* App Info */}
        <View style={styles.appInfo}>
          <Text style={styles.appInfoText}>BookmyFestive</Text>
          <Text style={styles.appInfoVersion}>Version 1.0.0</Text>
        </View>
      </ScrollView>

      {/* Language Selector Modal */}
      <LanguageSelector
        visible={languageSelectorVisible}
        onClose={() => setLanguageSelectorVisible(false)}
        onLanguageChange={handleLanguageChange}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f8f8',
  },
  scrollView: {
    flex: 1,
  },
  section: {
    backgroundColor: '#fff',
    marginTop: 20,
    marginHorizontal: 16,
    borderRadius: 12,
    overflow: 'hidden',
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    paddingHorizontal: 16,
    paddingTop: 16,
    paddingBottom: 8,
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  settingItemLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  iconContainer: {
    width: 32,
    height: 32,
    borderRadius: 8,
    backgroundColor: '#f8f8f8',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  settingItemContent: {
    flex: 1,
  },
  settingItemTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
    marginBottom: 2,
  },
  settingItemSubtitle: {
    fontSize: 14,
    color: '#666',
  },
  settingItemRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  settingItemValue: {
    fontSize: 14,
    color: '#8B0000',
    marginRight: 8,
    fontWeight: '500',
  },
  appInfo: {
    alignItems: 'center',
    paddingVertical: 32,
  },
  appInfoText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#8B0000',
    marginBottom: 4,
  },
  appInfoVersion: {
    fontSize: 14,
    color: '#666',
  },
});

export default SettingsScreen;
