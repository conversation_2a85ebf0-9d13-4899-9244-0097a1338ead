'use client'

import { useState } from 'react'
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { RefreshCw, Trash2, Download, AlertCircle, X } from "lucide-react"
import { clearAllCaches, forceServiceWorkerUpdate, forceAppRefresh } from '@/lib/pwa-utils'
import { showToast } from '@/lib/toast'
export function CacheManager() {
  const [isClearing, setIsClearing] = useState(false)
  const [isUpdating, setIsUpdating] = useState(false)
  const [isRefreshing, setIsRefreshing] = useState(false)
  const [cacheInfo, setCacheInfo] = useState<string[]>([])
  const [isVisible, setIsVisible] = useState(true)

  // Only show in development
  if (process.env.NODE_ENV !== 'development') {
    return null
  }

  // Don't render if hidden
  if (!isVisible) {
    return null
  }

  const handleClearCache = async () => {
    setIsClearing(true)
    try {
      const success = await clearAllCaches()
      if (success) {
        showToast.success('All caches cleared successfully!')
        await getCacheInfo()
      } else {
        showToast.error('Failed to clear caches')
      }
    } catch (error) {
      console.error('Error clearing cache:', error)
      showToast.error('Error clearing cache')
    } finally {
      setIsClearing(false)
    }
  }

  const handleForceUpdate = async () => {
    setIsUpdating(true)
    try {
      const success = await forceServiceWorkerUpdate()
      if (success) {
        showToast.success('Service worker update forced!')
      } else {
        showToast.error('Failed to force service worker update')
      }
    } catch (error) {
      console.error('Error forcing update:', error)
      showToast.error('Error forcing update')
    } finally {
      setIsUpdating(false)
    }
  }

  const handleForceRefresh = async () => {
    setIsRefreshing(true)
    try {
      await forceAppRefresh()
    } catch (error) {
      console.error('Error forcing refresh:', error)
      showToast.error('Error forcing refresh')
      setIsRefreshing(false)
    }
  }

  const getCacheInfo = async () => {
    if ('caches' in window) {
      try {
        const cacheNames = await caches.keys()
        setCacheInfo(cacheNames)
      } catch (error) {
        console.error('Error getting cache info:', error)
      }
    }
  }

  return (
    <div className="fixed bottom-4 right-4 z-50">
      <Card className="w-80 bg-white/95 backdrop-blur-sm border-orange-200">
        <CardHeader className="pb-3 relative">
          <button
            onClick={() => setIsVisible(false)}
            className="absolute top-2 right-2 p-1 hover:bg-gray-200 rounded-full transition-colors"
            title="Close cache manager"
          >
            <X className="h-4 w-4 text-gray-500 hover:text-gray-700" />
          </button>
          <CardTitle className="text-sm flex items-center gap-2 pr-8">
            <AlertCircle className="h-4 w-4 text-orange-500" />
            Development Cache Manager
          </CardTitle>
          <CardDescription className="text-xs">
            PWA cache management tools for development
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="space-y-2">
            <Button
              onClick={getCacheInfo}
              variant="outline"
              size="sm"
              className="w-full text-xs"
            >
              <Download className="h-3 w-3 mr-1" />
              Check Cache Status
            </Button>
            
            {cacheInfo.length > 0 && (
              <div className="text-xs space-y-1">
                <p className="font-medium">Active Caches:</p>
                {cacheInfo.map((cacheName, index) => (
                  <Badge key={index} variant="secondary" className="text-xs mr-1">
                    {cacheName}
                  </Badge>
                ))}
              </div>
            )}
          </div>

          <div className="grid grid-cols-1 gap-2">
            <Button
              onClick={handleClearCache}
              disabled={isClearing}
              variant="destructive"
              size="sm"
              className="text-xs"
            >
              <Trash2 className="h-3 w-3 mr-1" />
              {isClearing ? 'Clearing...' : 'Clear All Caches'}
            </Button>

            <Button
              onClick={handleForceUpdate}
              disabled={isUpdating}
              variant="outline"
              size="sm"
              className="text-xs"
            >
              <RefreshCw className="h-3 w-3 mr-1" />
              {isUpdating ? 'Updating...' : 'Force SW Update'}
            </Button>

            <Button
              onClick={handleForceRefresh}
              disabled={isRefreshing}
              variant="default"
              size="sm"
              className="text-xs"
            >
              <RefreshCw className="h-3 w-3 mr-1" />
              {isRefreshing ? 'Refreshing...' : 'Force App Refresh'}
            </Button>
          </div>

          <div className="text-xs text-gray-500 pt-2 border-t">
            <p>• Clear Caches: Removes all cached data</p>
            <p>• Force SW Update: Updates service worker</p>
            <p>• Force Refresh: Clears cache + reloads app</p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
