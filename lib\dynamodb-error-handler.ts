'use client';

import { compressImagesForDB, checkImagesSizeLimit } from './image-optimization';

/**
 * DynamoDB Error Handler
 * 
 * Handles common DynamoDB errors, especially item size limit errors,
 * and provides automatic image compression solutions.
 */

export interface DynamoDBErrorInfo {
  isItemSizeError: boolean;
  isImageSizeError: boolean;
  errorMessage: string;
  suggestedAction?: string;
}

/**
 * Parse DynamoDB error to determine if it's a size-related issue
 */
export function parseDynamoDBError(error: any): DynamoDBErrorInfo {
  const errorMessage = error?.message || error?.toString() || 'Unknown error';
  
  const isItemSizeError = errorMessage.includes('Item size has exceeded the maximum allowed size') ||
                         errorMessage.includes('ValidationException') ||
                         errorMessage.includes('maximum allowed size');
  
  const isImageSizeError = isItemSizeError && (
    errorMessage.includes(':images') ||
    errorMessage.includes(':gallery') ||
    errorMessage.includes('image')
  );

  let suggestedAction = '';
  
  if (isImageSizeError) {
    suggestedAction = 'Images are too large for database storage. They will be automatically compressed.';
  } else if (isItemSizeError) {
    suggestedAction = 'Data size exceeds DynamoDB limits. Please reduce the amount of data or split into multiple records.';
  }

  return {
    isItemSizeError,
    isImageSizeError,
    errorMessage,
    suggestedAction
  };
}

/**
 * Handle DynamoDB operations with automatic image compression retry
 */
export async function handleDynamoDBOperation<T>(
  operation: () => Promise<T>,
  data: any,
  imageFields: string[] = ['images', 'gallery']
): Promise<T> {
  try {
    // First attempt - try the operation as-is
    return await operation();
  } catch (error) {
    const errorInfo = parseDynamoDBError(error);
    
    if (errorInfo.isImageSizeError) {
      console.warn('DynamoDB size limit exceeded, attempting image compression...');
      
      // Create a copy of the data to modify
      const compressedData = { ...data };
      
      // Compress images in specified fields
      for (const field of imageFields) {
        if (compressedData[field] && Array.isArray(compressedData[field])) {
          try {
            const originalImages = compressedData[field];
            const sizeCheck = checkImagesSizeLimit(originalImages);
            
            if (!sizeCheck.isValid) {
              console.log(`Compressing ${field}: ${sizeCheck.totalSizeKB.toFixed(1)}KB -> target: ${sizeCheck.maxAllowedKB.toFixed(1)}KB`);
              
              const compressedImages = await compressImagesForDB(originalImages, {
                maxTotalSizeKB: sizeCheck.maxAllowedKB * 0.8, // Use 80% of limit for safety
                maxSizePerImageKB: 30, // Smaller per-image limit
                quality: 40 // Lower quality for better compression
              });
              
              compressedData[field] = compressedImages;
              
              const newSizeCheck = checkImagesSizeLimit(compressedImages);
              console.log(`Compression result: ${newSizeCheck.totalSizeKB.toFixed(1)}KB (${compressedImages.length} images)`);
            }
          } catch (compressionError) {
            console.error(`Error compressing ${field}:`, compressionError);
            // Continue with original data if compression fails
          }
        }
      }
      
      // Create new operation with compressed data
      const retryOperation = () => {
        // This is a bit tricky - we need to modify the original operation
        // For now, we'll throw a specific error that the caller can catch
        throw new Error('COMPRESSION_RETRY_NEEDED');
      };
      
      // Return the compressed data so the caller can retry
      return { compressedData } as any;
    }
    
    // Re-throw non-size-related errors
    throw error;
  }
}

/**
 * Wrapper for shop service operations
 */
export async function handleShopOperation<T>(
  operation: (data: any) => Promise<T>,
  data: any
): Promise<T> {
  try {
    return await operation(data);
  } catch (error) {
    const errorInfo = parseDynamoDBError(error);
    
    if (errorInfo.isImageSizeError && data.images) {
      console.warn('Shop images too large, compressing...');
      
      try {
        const compressedImages = await compressImagesForDB(data.images, {
          maxTotalSizeKB: 300, // 300KB total for images
          maxSizePerImageKB: 30, // 30KB per image
          quality: 40
        });
        
        const compressedData = { ...data, images: compressedImages };
        return await operation(compressedData);
      } catch (compressionError) {
        console.error('Failed to compress shop images:', compressionError);
        throw new Error('Failed to save shop: Images too large and compression failed');
      }
    }
    
    throw error;
  }
}

/**
 * Wrapper for vendor service operations
 */
export async function handleVendorOperation<T>(
  operation: (data: any) => Promise<T>,
  data: any
): Promise<T> {
  try {
    return await operation(data);
  } catch (error) {
    const errorInfo = parseDynamoDBError(error);
    
    if (errorInfo.isImageSizeError && data.gallery) {
      console.warn('Vendor gallery too large, compressing...');
      
      try {
        const compressedGallery = await compressImagesForDB(data.gallery, {
          maxTotalSizeKB: 300, // 300KB total for gallery
          maxSizePerImageKB: 25, // 25KB per image (vendors may have more images)
          quality: 35
        });
        
        const compressedData = { 
          ...data, 
          gallery: compressedGallery,
          profilePhoto: compressedGallery[0] || data.profilePhoto
        };
        
        return await operation(compressedData);
      } catch (compressionError) {
        console.error('Failed to compress vendor gallery:', compressionError);
        throw new Error('Failed to save vendor: Gallery too large and compression failed');
      }
    }
    
    throw error;
  }
}

/**
 * Wrapper for venue service operations
 */
export async function handleVenueOperation<T>(
  operation: (data: any) => Promise<T>,
  data: any
): Promise<T> {
  try {
    return await operation(data);
  } catch (error) {
    const errorInfo = parseDynamoDBError(error);
    
    if (errorInfo.isImageSizeError && data.images) {
      console.warn('Venue images too large, compressing...');
      
      try {
        const compressedImages = await compressImagesForDB(data.images, {
          maxTotalSizeKB: 300, // 300KB total for images
          maxSizePerImageKB: 20, // 20KB per image (venues may have many images)
          quality: 35
        });
        
        const compressedData = { 
          ...data, 
          images: compressedImages,
          mainImage: compressedImages[0] || data.mainImage
        };
        
        return await operation(compressedData);
      } catch (compressionError) {
        console.error('Failed to compress venue images:', compressionError);
        throw new Error('Failed to save venue: Images too large and compression failed');
      }
    }
    
    throw error;
  }
}

/**
 * Generic error handler for any DynamoDB operation with images
 */
export async function withImageCompression<T>(
  operation: () => Promise<T>,
  fallbackOperation?: () => Promise<T>
): Promise<T> {
  try {
    return await operation();
  } catch (error) {
    const errorInfo = parseDynamoDBError(error);
    
    if (errorInfo.isImageSizeError && fallbackOperation) {
      console.warn('Retrying operation with image compression...');
      return await fallbackOperation();
    }
    
    throw error;
  }
}
