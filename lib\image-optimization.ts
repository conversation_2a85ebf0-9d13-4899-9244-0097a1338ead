'use client';

/**
 * Image Optimization Utilities
 *
 * Provides utilities for optimizing images across the application
 * to improve Lighthouse performance scores and user experience.
 * Includes compression utilities to handle DynamoDB size limits.
 */

// Image quality settings for different use cases
export const ImageQuality = {
  HERO: 90,        // High quality for hero images
  PRODUCT: 80,     // Good quality for product images
  THUMBNAIL: 70,   // Optimized for thumbnails
  AVATAR: 75,      // Good for profile images
  PLACEHOLDER: 60, // Low quality for placeholders
  COMPRESSED: 50   // Heavily compressed for database storage
} as const;

// DynamoDB limits
export const DB_LIMITS = {
  MAX_ITEM_SIZE: 400 * 1024, // 400KB (DynamoDB limit)
  MAX_ATTRIBUTE_SIZE: 350 * 1024, // 350KB (safe limit for images attribute)
  TARGET_IMAGE_SIZE: 50 * 1024, // 50KB target per image for database storage
} as const;

// Common image sizes used in the app
export const ImageSizes = {
  HERO: { width: 1920, height: 1080 },
  PRODUCT_LARGE: { width: 800, height: 600 },
  PRODUCT_MEDIUM: { width: 400, height: 300 },
  PRODUCT_SMALL: { width: 200, height: 150 },
  THUMBNAIL: { width: 150, height: 150 },
  AVATAR: { width: 96, height: 96 },
  CARD: { width: 320, height: 240 }
} as const;

// Responsive sizes for different breakpoints
export const ResponsiveSizes = {
  FULL_WIDTH: '100vw',
  HALF_WIDTH: '(max-width: 768px) 100vw, 50vw',
  THIRD_WIDTH: '(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw',
  QUARTER_WIDTH: '(max-width: 768px) 50vw, 25vw',
  FIXED_SMALL: '150px',
  FIXED_MEDIUM: '320px',
  FIXED_LARGE: '800px'
} as const;

/**
 * Generate optimized image URL with fallback
 */
export function getOptimizedImageUrl(
  src: string, 
  options: {
    width?: number;
    height?: number;
    quality?: number;
    format?: 'webp' | 'avif' | 'jpeg' | 'png';
  } = {}
): string {
  // If it's already a placeholder, return as-is
  if (src.includes('placeholder') || src.includes('via.placeholder')) {
    return src;
  }

  // For external URLs, we rely on Next.js Image component optimization
  // For internal URLs, we could add additional processing here
  return src;
}

/**
 * Generate fallback image URL
 */
export function getFallbackImageUrl(
  width: number = 400,
  height: number = 300,
  text: string = 'Image Not Available',
  bgColor: string = 'f3f4f6',
  textColor: string = '6b7280'
): string {
  return `/placeholder.svg`;
}

/**
 * Generate blur data URL for placeholder
 */
export function generateBlurDataURL(
  width: number = 10,
  height: number = 10,
  color: string = '#f3f4f6'
): string {
  // Create a simple SVG blur placeholder
  const svg = `
    <svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">
      <defs>
        <linearGradient id="grad" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" style="stop-color:${color};stop-opacity:1" />
          <stop offset="100%" style="stop-color:#e5e7eb;stop-opacity:1" />
        </linearGradient>
      </defs>
      <rect width="100%" height="100%" fill="url(#grad)" />
    </svg>
  `;
  
  const base64 = Buffer.from(svg).toString('base64');
  return `data:image/svg+xml;base64,${base64}`;
}

/**
 * Check if image URL is valid and accessible
 */
export async function validateImageUrl(url: string): Promise<boolean> {
  if (typeof window === 'undefined') return true; // Skip validation on server
  
  try {
    const response = await fetch(url, { method: 'HEAD' });
    return response.ok;
  } catch {
    return false;
  }
}

/**
 * Preload critical images for better performance
 */
export function preloadImage(src: string, priority: boolean = false): void {
  if (typeof window === 'undefined') return;
  
  const link = document.createElement('link');
  link.rel = priority ? 'preload' : 'prefetch';
  link.as = 'image';
  link.href = src;
  
  // Add to document head
  document.head.appendChild(link);
}

/**
 * Preload multiple images
 */
export function preloadImages(urls: string[], priority: boolean = false): void {
  urls.forEach(url => preloadImage(url, priority));
}

/**
 * Get optimized image props for common use cases
 */
export function getImageProps(
  type: 'hero' | 'product' | 'thumbnail' | 'avatar' | 'card',
  src: string,
  alt: string,
  customProps: Record<string, any> = {}
) {
  const baseProps = {
    src,
    alt,
    placeholder: 'blur' as const,
    blurDataURL: generateBlurDataURL()
  };

  switch (type) {
    case 'hero':
      return {
        ...baseProps,
        ...ImageSizes.HERO,
        quality: ImageQuality.HERO,
        priority: true,
        sizes: ResponsiveSizes.FULL_WIDTH,
        ...customProps
      };

    case 'product':
      return {
        ...baseProps,
        ...ImageSizes.PRODUCT_MEDIUM,
        quality: ImageQuality.PRODUCT,
        sizes: ResponsiveSizes.THIRD_WIDTH,
        ...customProps
      };

    case 'thumbnail':
      return {
        ...baseProps,
        ...ImageSizes.THUMBNAIL,
        quality: ImageQuality.THUMBNAIL,
        sizes: ResponsiveSizes.FIXED_SMALL,
        ...customProps
      };

    case 'avatar':
      return {
        ...baseProps,
        ...ImageSizes.AVATAR,
        quality: ImageQuality.AVATAR,
        sizes: ResponsiveSizes.FIXED_SMALL,
        ...customProps
      };

    case 'card':
      return {
        ...baseProps,
        ...ImageSizes.CARD,
        quality: ImageQuality.PRODUCT,
        sizes: ResponsiveSizes.FIXED_MEDIUM,
        ...customProps
      };

    default:
      return {
        ...baseProps,
        ...customProps
      };
  }
}

/**
 * Image error handler with fallback logic
 */
export class ImageErrorHandler {
  private static fallbackAttempts = new Map<string, number>();
  private static maxAttempts = 2;

  static handleError(
    originalSrc: string,
    fallbackSrc?: string,
    onFallback?: (newSrc: string) => void
  ): void {
    const attempts = this.fallbackAttempts.get(originalSrc) || 0;
    
    if (attempts < this.maxAttempts) {
      this.fallbackAttempts.set(originalSrc, attempts + 1);
      
      // Try fallback image
      if (fallbackSrc && onFallback) {
        onFallback(fallbackSrc);
        return;
      }
      
      // Generate placeholder
      const placeholder = getFallbackImageUrl(400, 300, 'Image Not Available');
      if (onFallback) {
        onFallback(placeholder);
      }
    }
  }

  static resetAttempts(src: string): void {
    this.fallbackAttempts.delete(src);
  }

  static clearAllAttempts(): void {
    this.fallbackAttempts.clear();
  }
}

/**
 * Performance monitoring for images
 */
export class ImagePerformanceMonitor {
  private static loadTimes = new Map<string, number>();
  private static errorCount = new Map<string, number>();

  static recordLoadTime(src: string, loadTime: number): void {
    this.loadTimes.set(src, loadTime);
  }

  static recordError(src: string): void {
    const count = this.errorCount.get(src) || 0;
    this.errorCount.set(src, count + 1);
  }

  static getStats(): {
    averageLoadTime: number;
    totalErrors: number;
    slowestImage: string | null;
  } {
    const loadTimes = Array.from(this.loadTimes.values());
    const averageLoadTime = loadTimes.length > 0 
      ? loadTimes.reduce((a, b) => a + b, 0) / loadTimes.length 
      : 0;

    const totalErrors = Array.from(this.errorCount.values())
      .reduce((a, b) => a + b, 0);

    const slowestImage = loadTimes.length > 0
      ? Array.from(this.loadTimes.entries())
          .sort(([,a], [,b]) => b - a)[0][0]
      : null;

    return {
      averageLoadTime,
      totalErrors,
      slowestImage
    };
  }

  static logStats(): void {
    if (process.env.NODE_ENV === 'development') {
      console.log('📊 Image Performance Stats:', this.getStats());
    }
  }
}

/**
 * Compress image to fit within DynamoDB size limits
 */
export async function compressImageForDB(
  file: File | string,
  options: {
    maxSizeKB?: number;
    quality?: number;
    maxWidth?: number;
    maxHeight?: number;
  } = {}
): Promise<string> {
  const {
    maxSizeKB = DB_LIMITS.TARGET_IMAGE_SIZE / 1024,
    quality = ImageQuality.COMPRESSED,
    maxWidth = 800,
    maxHeight = 600
  } = options;

  try {
    // Convert file to canvas for processing
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    if (!ctx) throw new Error('Canvas context not available');

    // Load image
    const img = new Image();
    const imageDataUrl = typeof file === 'string' ? file : await fileToDataURL(file);

    await new Promise((resolve, reject) => {
      img.onload = resolve;
      img.onerror = reject;
      img.src = imageDataUrl;
    });

    // Calculate new dimensions
    const { width, height } = calculateOptimalDimensions(
      img.width,
      img.height,
      maxWidth,
      maxHeight
    );

    // Set canvas size
    canvas.width = width;
    canvas.height = height;

    // Draw and compress image
    ctx.drawImage(img, 0, 0, width, height);

    // Try different quality levels until size is acceptable
    let currentQuality = quality;
    let compressedDataUrl = '';
    let attempts = 0;
    const maxAttempts = 10;

    do {
      compressedDataUrl = canvas.toDataURL('image/jpeg', currentQuality / 100);
      const sizeKB = getBase64SizeKB(compressedDataUrl);

      if (sizeKB <= maxSizeKB || attempts >= maxAttempts) {
        break;
      }

      currentQuality = Math.max(10, currentQuality - 10);
      attempts++;
    } while (attempts < maxAttempts);

    return compressedDataUrl;
  } catch (error) {
    console.error('Error compressing image:', error);
    throw error;
  }
}

/**
 * Compress multiple images for database storage
 */
export async function compressImagesForDB(
  images: (File | string)[],
  options: {
    maxTotalSizeKB?: number;
    maxSizePerImageKB?: number;
    quality?: number;
  } = {}
): Promise<string[]> {
  const {
    maxTotalSizeKB = DB_LIMITS.MAX_ATTRIBUTE_SIZE / 1024,
    maxSizePerImageKB = DB_LIMITS.TARGET_IMAGE_SIZE / 1024,
    quality = ImageQuality.COMPRESSED
  } = options;

  const compressedImages: string[] = [];
  let totalSizeKB = 0;

  for (const image of images) {
    try {
      const compressed = await compressImageForDB(image, {
        maxSizeKB: maxSizePerImageKB,
        quality
      });

      const sizeKB = getBase64SizeKB(compressed);

      // Check if adding this image would exceed total size limit
      if (totalSizeKB + sizeKB > maxTotalSizeKB) {
        console.warn(`Skipping image - would exceed total size limit. Current: ${totalSizeKB}KB, Image: ${sizeKB}KB, Limit: ${maxTotalSizeKB}KB`);
        break;
      }

      compressedImages.push(compressed);
      totalSizeKB += sizeKB;
    } catch (error) {
      console.error('Error compressing image:', error);
      // Continue with other images
    }
  }

  return compressedImages;
}

/**
 * Calculate optimal dimensions while maintaining aspect ratio
 */
function calculateOptimalDimensions(
  originalWidth: number,
  originalHeight: number,
  maxWidth: number,
  maxHeight: number
): { width: number; height: number } {
  const aspectRatio = originalWidth / originalHeight;

  let width = originalWidth;
  let height = originalHeight;

  // Scale down if necessary
  if (width > maxWidth) {
    width = maxWidth;
    height = width / aspectRatio;
  }

  if (height > maxHeight) {
    height = maxHeight;
    width = height * aspectRatio;
  }

  return {
    width: Math.round(width),
    height: Math.round(height)
  };
}

/**
 * Convert file to data URL
 */
function fileToDataURL(file: File): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => resolve(reader.result as string);
    reader.onerror = reject;
    reader.readAsDataURL(file);
  });
}

/**
 * Get size of base64 string in KB
 */
function getBase64SizeKB(base64String: string): number {
  // Remove data URL prefix if present
  const base64Data = base64String.split(',')[1] || base64String;

  // Calculate size: each base64 character represents 6 bits
  // 4 base64 characters = 3 bytes
  const sizeBytes = (base64Data.length * 3) / 4;

  // Account for padding
  const padding = (base64Data.match(/=/g) || []).length;
  const actualSizeBytes = sizeBytes - padding;

  return actualSizeBytes / 1024;
}

/**
 * Check if images array exceeds DynamoDB size limits
 */
export function checkImagesSizeLimit(images: string[]): {
  isValid: boolean;
  totalSizeKB: number;
  maxAllowedKB: number;
  exceedsBy?: number;
} {
  const totalSizeKB = images.reduce((total, image) => {
    return total + getBase64SizeKB(image);
  }, 0);

  const maxAllowedKB = DB_LIMITS.MAX_ATTRIBUTE_SIZE / 1024;
  const isValid = totalSizeKB <= maxAllowedKB;

  return {
    isValid,
    totalSizeKB,
    maxAllowedKB,
    exceedsBy: isValid ? undefined : totalSizeKB - maxAllowedKB
  };
}
