import React, { useState, useEffect } from 'react';
import { View, Text, ScrollView, StyleSheet, TouchableOpacity, Image, FlatList, Alert } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../../providers/ThemeProvider';
import { Card, CardContent, Badge } from '../../components/ui';
import { AvailabilityChecker } from '../../components/booking/AvailabilityChecker';

interface Venue {
  id: string;
  name: string;
  description: string;
  address: string;
  city: string;
  state: string;
  contact: string;
  email: string;
  website?: string;
  venueType: string;
  capacity: { min: number; max: number };
  priceRange: string;
  amenities: string[];
  gallery: string[];
  rating: number;
  reviewCount: number;
  verified: boolean;
  featured: boolean;
  policies: string[];
  catering: boolean;
  decoration: boolean;
  parking: boolean;
  accommodation: boolean;
}

interface Props {
  navigation: any;
  route: any;
}

const RenderImageWithFallback = ({ uri, style }: { uri?: string; style?: any }) => {
  if (uri) {
    return <Image source={{ uri }} style={style} resizeMode="cover" />;
  }
  return (
    <View style={[style, { backgroundColor: '#E5E7EB', justifyContent: 'center', alignItems: 'center' }]}>
      <Text style={{ color: '#9CA3AF', fontSize: 16 }}>No Image</Text>
    </View>
  );
};

export default function VenueDetailScreen({ navigation, route }: Props) {
  const { theme } = useTheme();
  const { venueId } = route.params;
  const [venue, setVenue] = useState<Venue | null>(null);
  const [loading, setLoading] = useState(true);
  const [isFavorite, setIsFavorite] = useState(false);
  const [activeTab, setActiveTab] = useState<'Overview' | 'Gallery' | 'Reviews'>('Overview');

  // Availability checker state
  const [selectedDate, setSelectedDate] = useState('');
  const [selectedTime, setSelectedTime] = useState('');
  const [isAvailable, setIsAvailable] = useState<boolean | null>(null);

  useEffect(() => {
    loadVenueDetails();
  }, [venueId]);

  const loadVenueDetails = async () => {
    // Replace with your API call
    const mockVenue: Venue = {
      id: venueId,
      name: 'Grand Palace Wedding Hall',
      description: 'Elegant and spacious wedding hall perfect for grand celebrations. Our venue combines traditional architecture with modern amenities to create the perfect setting for your special day.',
      address: '456 Wedding Avenue, Anna Nagar',
      city: 'Chennai',
      state: 'Tamil Nadu',
      contact: '+91 98765 43211',
      email: '<EMAIL>',
      website: 'https://grandpalace.com',
      venueType: 'Banquet Hall',
      capacity: { min: 100, max: 1000 },
      priceRange: '₹50,000 - ₹2,00,000',
      amenities: [
        'Air Conditioning', 'Sound System', 'Stage Setup', 'Bridal Room',
        'Parking', 'Catering Kitchen', 'Generator Backup', 'Security',
      ],
      gallery: [
        '/placeholder-image.jpg
        '/placeholder-image.jpg
        '/placeholder-image.jpg
      ],
      rating: 4.6,
      reviewCount: 89,
      verified: true,
      featured: true,
      policies: [
        'No outside alcohol allowed',
        'Decoration setup 4 hours before event',
        'Music must stop by 11 PM',
        'Security deposit required',
      ],
      catering: true,
      decoration: true,
      parking: true,
      accommodation: false,
    };
    setVenue(mockVenue);
    setLoading(false);
  };

  const handleCall = () => venue?.contact && Alert.alert('Call', venue.contact);
  const handleEmail = () => venue?.email && Alert.alert('Email', venue.email);
  const handleWebsite = () => venue?.website && Alert.alert('Website', venue.website);
  const toggleFavorite = () => setIsFavorite(!isFavorite);
  const handleBooking = () => Alert.alert('Booking', 'Booking functionality will be implemented soon');
  const handleWriteReview = () => Alert.alert('Review', 'Review functionality coming soon!');
  const handleInquiry = () => Alert.alert('Inquiry', 'Inquiry submitted!');

  const handleAvailabilityChange = (available: boolean, conflicts?: any[]) => {
    setIsAvailable(available);
    console.log('Venue availability changed:', { available, conflicts });
  };

  if (loading) {
    return (
      <View style={[styles.container, styles.centered, { backgroundColor: theme.colors.background }]}>
        <Text style={[styles.loadingText, { color: theme.colors.text }]}>Loading...</Text>
      </View>
    );
  }
  if (!venue) {
    return (
      <View style={[styles.container, styles.centered, { backgroundColor: theme.colors.background }]}>
        <Text style={[styles.errorText, { color: theme.colors.error }]}>Venue not found</Text>
      </View>
    );
  }

  return (
    <View style={{ flex: 1, backgroundColor: theme.colors.background }}>
      {/* Hero Section - Matching Web App */}
      <View style={styles.heroContainer}>
        <RenderImageWithFallback uri={venue.gallery[0]} style={styles.heroImage} />
        <View style={styles.heroOverlay}>
          {/* Venue Info Overlay - Matching Web App */}
          <View style={styles.heroContent}>
            <View style={styles.heroHeader}>
              <View style={styles.heroTitleSection}>
                <Text style={[styles.heroName, { color: theme.colors.primaryForeground }]}>
                  {venue.name}
                </Text>
                {venue.featured && (
                  <Badge variant="secondary" style={styles.featuredBadge}>
                    Featured
                  </Badge>
                )}
              </View>
              <Text style={[styles.heroCategory, { color: theme.colors.primaryForeground }]}>
                {venue.venueType}
              </Text>
              <View style={styles.heroLocationRow}>
                <Ionicons name="location-outline" size={20} color={theme.colors.primaryForeground} />
                <Text style={[styles.heroLocation, { color: theme.colors.primaryForeground }]}>
                  {venue.city}, {venue.state}
                </Text>
              </View>
            </View>
            <View style={styles.heroPriceSection}>
              <Text style={[styles.heroPrice, { color: theme.colors.primaryForeground }]}>
                {venue.priceRange}
              </Text>
              <View style={styles.heroRatingRow}>
                <Ionicons name="star" size={16} color="#FFD700" />
                <Text style={[styles.heroRating, { color: theme.colors.primaryForeground }]}>
                  {venue.rating}
                </Text>
                <Text style={[styles.heroReviewCount, { color: theme.colors.primaryForeground }]}>
                  ({venue.reviewCount || 0} reviews)
                </Text>
              </View>
            </View>
          </View>
        </View>

        {/* Floating Action Buttons - Matching Web App */}
        <View style={styles.floatingActions}>
          <TouchableOpacity style={styles.actionButton} onPress={toggleFavorite}>
            <Ionicons name={isFavorite ? 'heart' : 'heart-outline'} size={24} color={isFavorite ? '#EF4444' : theme.colors.foreground} />
          </TouchableOpacity>
          <TouchableOpacity style={styles.actionButton} onPress={() => {/* Share functionality */}}>
            <Ionicons name="share-outline" size={24} color={theme.colors.foreground} />
          </TouchableOpacity>
        </View>
      </View>

      {/* Tab Bar */}
      <View style={{ flexDirection: 'row', justifyContent: 'space-around', marginVertical: 12 }}>
        {['Overview', 'Gallery', 'Reviews'].map(tab => (
          <TouchableOpacity
            key={tab}
            onPress={() => setActiveTab(tab as any)}
            style={{
              borderBottomWidth: activeTab === tab ? 2 : 0,
              borderBottomColor: activeTab === tab ? theme.colors.primary : 'transparent',
              paddingVertical: 8,
              flex: 1,
              alignItems: 'center',
            }}
          >
            <Text style={{ color: activeTab === tab ? theme.colors.primary : theme.colors.textSecondary, fontWeight: 'bold' }}>{tab}</Text>
          </TouchableOpacity>
        ))}
      </View>

      {/* Tab Content */}
      <ScrollView style={{ flex: 1 }}>
        {activeTab === 'Overview' && (
          <View>
            {/* About Section - Moved to Top like Web App */}
            <Card style={[styles.section, { marginBottom: 24 }]}>
              <CardContent style={styles.cardContent}>
                <View style={styles.aboutHeader}>
                  <Text style={[styles.sectionTitle, { color: theme.colors.foreground }]}>
                    About {venue.name}
                  </Text>
                  <View style={styles.aboutActions}>
                    <TouchableOpacity onPress={toggleFavorite} style={styles.actionIconButton}>
                      <Ionicons
                        name={isFavorite ? 'heart' : 'heart-outline'}
                        size={24}
                        color={isFavorite ? '#EF4444' : theme.colors.foreground}
                      />
                    </TouchableOpacity>
                    <TouchableOpacity onPress={() => {/* Share functionality */}} style={styles.actionIconButton}>
                      <Ionicons name="share-outline" size={24} color={theme.colors.foreground} />
                    </TouchableOpacity>
                  </View>
                </View>

                <Text style={[styles.description, { color: theme.colors.mutedForeground }]}>
                  {venue.description}
                </Text>

                {/* Venue Stats - Matching Web App */}
                <View style={styles.statsContainer}>
                  <View style={styles.statItem}>
                    <Ionicons name="star" size={20} color="#FFD700" />
                    <Text style={[styles.statValue, { color: theme.colors.foreground }]}>
                      {venue.rating}
                    </Text>
                    <Text style={[styles.statLabel, { color: theme.colors.mutedForeground }]}>
                      Rating
                    </Text>
                  </View>
                  <View style={styles.statItem}>
                    <Ionicons name="people" size={20} color={theme.colors.primary} />
                    <Text style={[styles.statValue, { color: theme.colors.foreground }]}>
                      {venue.capacity.max}
                    </Text>
                    <Text style={[styles.statLabel, { color: theme.colors.mutedForeground }]}>
                      Capacity
                    </Text>
                  </View>
                  <View style={styles.statItem}>
                    <Ionicons name="location" size={20} color={theme.colors.secondary} />
                    <Text style={[styles.statValue, { color: theme.colors.foreground }]}>
                      {venue.venueType}
                    </Text>
                    <Text style={[styles.statLabel, { color: theme.colors.mutedForeground }]}>
                      Type
                    </Text>
                  </View>
                  <View style={styles.statItem}>
                    <Ionicons name="chatbubbles" size={20} color={theme.colors.accent} />
                    <Text style={[styles.statValue, { color: theme.colors.foreground }]}>
                      {venue.reviewCount || 0}
                    </Text>
                    <Text style={[styles.statLabel, { color: theme.colors.mutedForeground }]}>
                      Reviews
                    </Text>
                  </View>
                </View>
              </CardContent>
            </Card>

            {/* Availability Checker */}
            <Card style={[styles.section, { marginBottom: 24 }]}>
              <CardContent style={styles.cardContent}>
                <Text style={[styles.sectionTitle, { color: theme.colors.foreground }]}>
                  Check Availability
                </Text>
                <Text style={[styles.sectionSubtitle, { color: theme.colors.mutedForeground, marginBottom: 16 }]}>
                  Select your preferred date and time to check if {venue?.name} is available
                </Text>

                <AvailabilityChecker
                  entityId={venue?.id || ''}
                  entityType="VENUE"
                  entityName={venue?.name || ''}
                  selectedDate={selectedDate}
                  selectedTime={selectedTime}
                  duration="8 hours"
                  onAvailabilityChange={handleAvailabilityChange}
                />
              </CardContent>
            </Card>

            {/* Amenities */}
            <View style={[styles.section, { backgroundColor: theme.colors.surface }]}>
              <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>Amenities</Text>
              <View style={styles.amenitiesGrid}>
                {venue.amenities.map((amenity, index) => (
                  <View key={index} style={styles.amenityItem}>
                    <Ionicons name="checkmark-circle" size={16} color={theme.colors.success} />
                    <Text style={[styles.amenityText, { color: theme.colors.text }]}>{amenity}</Text>
                  </View>
                ))}
              </View>
            </View>

            {/* Services */}
            <View style={[styles.section, { backgroundColor: theme.colors.surface }]}>
              <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>Services Available</Text>
              <View style={styles.servicesContainer}>
                <View style={styles.serviceItem}>
                  <Ionicons name={venue.catering ? 'checkmark-circle' : 'close-circle'} size={20} color={venue.catering ? theme.colors.success : theme.colors.error} />
                  <Text style={[styles.serviceText, { color: theme.colors.text }]}>In-house Catering</Text>
                </View>
                <View style={styles.serviceItem}>
                  <Ionicons name={venue.decoration ? 'checkmark-circle' : 'close-circle'} size={20} color={venue.decoration ? theme.colors.success : theme.colors.error} />
                  <Text style={[styles.serviceText, { color: theme.colors.text }]}>Decoration Services</Text>
                </View>
                <View style={styles.serviceItem}>
                  <Ionicons name={venue.parking ? 'checkmark-circle' : 'close-circle'} size={20} color={venue.parking ? theme.colors.success : theme.colors.error} />
                  <Text style={[styles.serviceText, { color: theme.colors.text }]}>Parking Available</Text>
                </View>
                <View style={styles.serviceItem}>
                  <Ionicons name={venue.accommodation ? 'checkmark-circle' : 'close-circle'} size={20} color={venue.accommodation ? theme.colors.success : theme.colors.error} />
                  <Text style={[styles.serviceText, { color: theme.colors.text }]}>Guest Accommodation</Text>
                </View>
              </View>
            </View>

            {/* Policies */}
            <View style={[styles.section, { backgroundColor: theme.colors.surface }]}>
              <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>Venue Policies</Text>
              {venue.policies.map((policy, index) => (
                <View key={index} style={styles.policyItem}>
                  <Ionicons name="information-circle-outline" size={16} color={theme.colors.textSecondary} />
                  <Text style={[styles.policyText, { color: theme.colors.textSecondary }]}>{policy}</Text>
                </View>
              ))}
            </View>

            {/* Contact Info */}
            <View style={[styles.section, { backgroundColor: theme.colors.surface }]}>
              <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>Contact Information</Text>
              <View style={styles.contactItem}>
                <Ionicons name="location-outline" size={20} color={theme.colors.textSecondary} />
                <Text style={[styles.contactText, { color: theme.colors.textSecondary }]}>{venue.address}, {venue.city}, {venue.state}</Text>
              </View>
              <TouchableOpacity style={styles.contactItem} onPress={handleCall}>
                <Ionicons name="call-outline" size={20} color={theme.colors.primary} />
                <Text style={[styles.contactText, styles.contactLink, { color: theme.colors.primary }]}>{venue.contact}</Text>
              </TouchableOpacity>
              <TouchableOpacity style={styles.contactItem} onPress={handleEmail}>
                <Ionicons name="mail-outline" size={20} color={theme.colors.primary} />
                <Text style={[styles.contactText, styles.contactLink, { color: theme.colors.primary }]}>{venue.email}</Text>
              </TouchableOpacity>
              {venue.website && (
                <TouchableOpacity style={styles.contactItem} onPress={handleWebsite}>
                  <Ionicons name="globe-outline" size={20} color={theme.colors.primary} />
                  <Text style={[styles.contactText, styles.contactLink, { color: theme.colors.primary }]}>Visit Website</Text>
                </TouchableOpacity>
              )}
            </View>

            {/* Action Buttons */}
            <View style={styles.actionContainer}>
              <TouchableOpacity style={[styles.actionButton, { backgroundColor: theme.colors.primary }]} onPress={handleBooking}>
                <Ionicons name="calendar-outline" size={20} color="white" />
                <Text style={styles.actionButtonText}>Book Venue</Text>
              </TouchableOpacity>
            </View>

            {/* Quick Inquiry Form */}
            <View style={[styles.section, { backgroundColor: theme.colors.surface, marginTop: 16 }]}>
              <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>Quick Inquiry</Text>
              <View style={{ gap: 10 }}>
                <View>
                  <Text style={{ color: theme.colors.textSecondary, fontSize: 13 }}>Your Name *</Text>
                  <View style={{ backgroundColor: '#F3F4F6', borderRadius: 8, padding: 8 }}>
                    <Text style={{ color: theme.colors.text }}>John Doe</Text>
                  </View>
                </View>
                <View>
                  <Text style={{ color: theme.colors.textSecondary, fontSize: 13 }}>Email *</Text>
                  <View style={{ backgroundColor: '#F3F4F6', borderRadius: 8, padding: 8 }}>
                    <Text style={{ color: theme.colors.text }}><EMAIL></Text>
                  </View>
                </View>
                <View>
                  <Text style={{ color: theme.colors.textSecondary, fontSize: 13 }}>Your Message *</Text>
                  <View style={{ backgroundColor: '#F3F4F6', borderRadius: 8, padding: 8, minHeight: 60 }}>
                    <Text style={{ color: theme.colors.text }}>I am interested in your venue. Please contact me.</Text>
                  </View>
                </View>
                <TouchableOpacity style={{ backgroundColor: theme.colors.primary, borderRadius: 8, paddingVertical: 14, alignItems: 'center', marginTop: 8 }} onPress={handleInquiry}>
                  <Text style={{ color: 'white', fontWeight: 'bold', fontSize: 16 }}>Send Inquiry</Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        )}

        {activeTab === 'Gallery' && (
          <View style={[styles.section, { backgroundColor: theme.colors.surface }]}>
            <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>Photo Gallery</Text>
            <FlatList
              data={venue.gallery}
              keyExtractor={(item, idx) => idx.toString()}
              horizontal
              showsHorizontalScrollIndicator={false}
              style={{ marginTop: 12 }}
              contentContainerStyle={{ gap: 12 }}
              renderItem={({ item }) => (
                <RenderImageWithFallback uri={item} style={{ width: 220, height: 180, borderRadius: 12, backgroundColor: '#E5E7EB' }} />
              )}
            />
          </View>
        )}

        {activeTab === 'Reviews' && (
          <View style={[styles.section, { backgroundColor: theme.colors.surface }]}>
            <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>Reviews</Text>
            <Text style={{ color: theme.colors.textSecondary, marginBottom: 8 }}>No reviews yet. Be the first to write a review!</Text>
            <TouchableOpacity style={{ backgroundColor: theme.colors.primary, borderRadius: 8, paddingVertical: 12, alignItems: 'center' }} onPress={handleWriteReview}>
              <Text style={{ color: 'white', fontWeight: 'bold', fontSize: 16 }}>Write a Review</Text>
            </TouchableOpacity>
          </View>
        )}
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1 },
  centered: { justifyContent: 'center', alignItems: 'center' },
  loadingText: { fontSize: 16 },
  errorText: { fontSize: 16 },
  heroContainer: { position: 'relative', height: 250 },
  heroImage: { width: '100%', height: '100%' },
  heroOverlay: { position: 'absolute', left: 0, right: 0, bottom: 0, padding: 16, backgroundColor: 'rgba(0,0,0,0.4)' },
  heroName: { color: '#fff', fontSize: 28, fontWeight: 'bold' },
  heroPrice: { color: '#fff', fontSize: 18, fontWeight: '600', marginTop: 4 },
  heroCategory: { color: '#fff', fontSize: 16, marginTop: 2 },
  heroLocationRow: { flexDirection: 'row', alignItems: 'center', marginTop: 4 },
  heroLocation: { color: '#fff', fontSize: 14, marginLeft: 4 },
  heroRatingRow: { flexDirection: 'row', alignItems: 'center', marginTop: 4 },
  heroRating: { color: '#FFD700', fontSize: 16, marginLeft: 4 },
  favoriteButton: {
    position: 'absolute', top: 16, right: 16, width: 44, height: 44, borderRadius: 22,
    justifyContent: 'center', alignItems: 'center', backgroundColor: 'rgba(0,0,0,0.5)',
    shadowColor: '#000', shadowOffset: { width: 0, height: 2 }, shadowOpacity: 0.1, shadowRadius: 4, elevation: 3,
  },
  section: { margin: 16, padding: 16, borderRadius: 12 },
  sectionTitle: { fontSize: 18, fontWeight: 'bold', marginBottom: 12 },
  description: { fontSize: 14, lineHeight: 20 },
  amenitiesGrid: { flexDirection: 'row', flexWrap: 'wrap', gap: 12 },
  amenityItem: { flexDirection: 'row', alignItems: 'center', gap: 8, width: '48%', marginBottom: 8 },
  amenityText: { fontSize: 14, flex: 1 },
  servicesContainer: { gap: 12 },
  serviceItem: { flexDirection: 'row', alignItems: 'center', gap: 12 },
  serviceText: { fontSize: 16 },
  policyItem: { flexDirection: 'row', alignItems: 'flex-start', gap: 8, marginBottom: 8 },
  policyText: { fontSize: 14, flex: 1, lineHeight: 20 },
  contactItem: { flexDirection: 'row', alignItems: 'center', gap: 12, paddingVertical: 8 },
  contactText: { fontSize: 14, flex: 1 },
  contactLink: { textDecorationLine: 'underline' },
  actionContainer: { padding: 16, paddingBottom: 32 },
  actionButton: { flexDirection: 'row', alignItems: 'center', justifyContent: 'center', paddingVertical: 16, borderRadius: 12, gap: 8 },
  actionButtonText: { color: 'white', fontSize: 16, fontWeight: '600' },
});
