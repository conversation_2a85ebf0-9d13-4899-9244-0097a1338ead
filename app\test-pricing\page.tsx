"use client"

import React, { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { VendorSubscriptionService } from '@/lib/services/vendorSubscriptionService'
import VendorPricingStep from '@/components/vendor/VendorPricingStep'
import toast from 'react-hot-toast'

export default function TestPricingPage() {
  const [showPricing, setShowPricing] = useState(false)
  const [plans, setPlans] = useState([])

  useEffect(() => {
    loadPlans()
  }, [])

  const loadPlans = async () => {
    try {
      const pricingPlans = await VendorSubscriptionService.getPricingPlans()
      setPlans(pricingPlans)
      console.log('Loaded pricing plans:', pricingPlans)
    } catch (error) {
      console.error('Error loading plans:', error)
    }
  }

  const handleTestPricing = () => {
    setShowPricing(true)
  }

  const handleSubscriptionComplete = (subscriptionId: string) => {
    toast.success(`Subscription created: ${subscriptionId}`)
    setShowPricing(false)
  }

  const handleSkip = () => {
    toast.info('Pricing skipped')
    setShowPricing(false)
  }

  if (showPricing) {
    return (
      <VendorPricingStep
        vendorId="test_vendor_123"
        vendorInfo={{
          name: "Test Vendor",
          email: "<EMAIL>",
          phone: "+91 8148376909"
        }}
        onSubscriptionComplete={handleSubscriptionComplete}
        onSkip={handleSkip}
      />
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-8">Pricing System Test</h1>
        
        <div className="bg-white rounded-lg p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">Available Plans</h2>
          <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto">
            {JSON.stringify(plans, null, 2)}
          </pre>
        </div>

        <div className="space-y-4">
          <Button onClick={loadPlans} variant="outline">
            Reload Plans
          </Button>
          
          <Button onClick={handleTestPricing} className="bg-red-600 hover:bg-red-700">
            Test Pricing Component
          </Button>
        </div>

        <div className="mt-8 bg-blue-50 p-4 rounded-lg">
          <h3 className="font-semibold text-blue-900 mb-2">Test Instructions:</h3>
          <ol className="list-decimal list-inside text-blue-800 space-y-1">
            <li>Click "Reload Plans" to fetch pricing plans from the database</li>
            <li>Click "Test Pricing Component" to see the pricing selection UI</li>
            <li>Test the payment flow (will use test Razorpay keys)</li>
            <li>Check console for detailed logs</li>
          </ol>
        </div>
      </div>
    </div>
  )
}
