import React from 'react';
import { View, Text, StyleSheet, ViewStyle, TextStyle } from 'react-native';
import { useTheme } from '../../providers/ThemeProvider';

export interface BadgeProps {
  children: React.ReactNode;
  variant?: 'default' | 'secondary' | 'destructive' | 'outline' | 'success' | 'warning';
  size?: 'default' | 'sm' | 'lg';
  style?: ViewStyle;
  textStyle?: TextStyle;
}

export function Badge({
  children,
  variant = 'default',
  size = 'default',
  style,
  textStyle,
}: BadgeProps) {
  const { theme } = useTheme();

  const getBadgeStyle = (): ViewStyle => {
    const baseStyle: ViewStyle = {
      paddingHorizontal: theme.spacing.sm,
      paddingVertical: theme.spacing.xs,
      borderRadius: theme.borderRadius.sm,
      alignSelf: 'flex-start',
    };

    const sizeStyles: Record<string, ViewStyle> = {
      default: {
        paddingHorizontal: theme.spacing.sm,
        paddingVertical: theme.spacing.xs,
      },
      sm: {
        paddingHorizontal: 6,
        paddingVertical: 2,
      },
      lg: {
        paddingHorizontal: 12,
        paddingVertical: 6,
      },
    };

    const variantStyles: Record<string, ViewStyle> = {
      default: {
        backgroundColor: theme.colors.primary,
      },
      secondary: {
        backgroundColor: theme.colors.secondary,
      },
      destructive: {
        backgroundColor: theme.colors.error || '#EF4444',
      },
      success: {
        backgroundColor: '#10B981',
      },
      warning: {
        backgroundColor: '#F59E0B',
      },
      outline: {
        backgroundColor: 'transparent',
        borderWidth: 1,
        borderColor: theme.colors.border,
      },
    };

    return {
      ...baseStyle,
      ...sizeStyles[size],
      ...variantStyles[variant],
    };
  };

  const getTextStyle = (): TextStyle => {
    const baseStyle: TextStyle = {
      fontWeight: '500',
      textAlign: 'center',
    };

    const sizeStyles: Record<string, TextStyle> = {
      default: {
        fontSize: theme.typography.caption.fontSize, // text-xs
        lineHeight: theme.typography.caption.lineHeight
      },
      sm: {
        fontSize: 10, // smaller than caption
        lineHeight: 14
      },
      lg: {
        fontSize: theme.typography.bodySmall.fontSize, // text-sm
        lineHeight: theme.typography.bodySmall.lineHeight
      },
    };

    const variantStyles: Record<string, TextStyle> = {
      default: { color: theme.colors.primaryForeground },
      secondary: { color: theme.colors.secondaryForeground },
      destructive: { color: theme.colors.destructiveForeground },
      success: { color: '#fff' },
      warning: { color: '#fff' },
      outline: { color: theme.colors.foreground },
    };

    return {
      ...baseStyle,
      ...sizeStyles[size],
      ...variantStyles[variant],
    };
  };

  return (
    <View style={[getBadgeStyle(), style]}>
      <Text style={[getTextStyle(), textStyle]}>
        {children}
      </Text>
    </View>
  );
}

const styles = StyleSheet.create({
  // Additional styles can be added here if needed
});
