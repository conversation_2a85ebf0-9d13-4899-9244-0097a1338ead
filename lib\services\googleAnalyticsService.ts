declare global {
  interface Window {
    gtag: (...args: any[]) => void
    dataLayer: any[]
  }
}

export class GoogleAnalyticsService {
  private static GA_MEASUREMENT_ID = process.env.NEXT_PUBLIC_GA_MEASUREMENT_ID || 'G-QWDCH24FT0'

  static initialize() {
    if (typeof window === 'undefined') return

    // Load Google Analytics script
    const script = document.createElement('script')
    script.src = `https://www.googletagmanager.com/gtag/js?id=${this.GA_MEASUREMENT_ID}`
    script.async = true
    document.head.appendChild(script)

    // Initialize gtag
    window.dataLayer = window.dataLayer || []
    window.gtag = function gtag() {
      window.dataLayer.push(arguments)
    }
    window.gtag('js', new Date())
    window.gtag('config', this.GA_MEASUREMENT_ID, {
      page_title: document.title,
      page_location: window.location.href
    })
  }

  static trackEvent(eventName: string, parameters: Record<string, any> = {}) {
    if (typeof window === 'undefined' || !window.gtag) return

    window.gtag('event', eventName, {
      event_category: 'vendor_interaction',
      event_label: parameters.label || '',
      value: parameters.value || 0,
      ...parameters
    })
  }

  static trackVendorView(vendorId: string, vendorName: string) {
    this.trackEvent('vendor_profile_view', {
      vendor_id: vendorId,
      vendor_name: vendorName,
      event_category: 'vendor_engagement'
    })
  }

  static trackContactClick(vendorId: string, contactType: 'phone' | 'email' | 'whatsapp') {
    this.trackEvent('vendor_contact_click', {
      vendor_id: vendorId,
      contact_type: contactType,
      event_category: 'vendor_conversion'
    })
  }

  static trackGalleryView(vendorId: string, photoIndex: number) {
    this.trackEvent('vendor_gallery_view', {
      vendor_id: vendorId,
      photo_index: photoIndex,
      event_category: 'vendor_engagement'
    })
  }

  static trackInquirySubmit(vendorId: string) {
    this.trackEvent('vendor_inquiry_submit', {
      vendor_id: vendorId,
      event_category: 'vendor_conversion',
      value: 1
    })
  }

  static async getVendorPageAnalytics(vendorId: string, dateRange: string) {
    try {
      // In a real implementation, you would use Google Analytics Reporting API
      // For now, returning mock data
      return {
        pageViews: this.getMockPageViews(dateRange),
        uniqueVisitors: this.getMockUniqueVisitors(dateRange),
        bounceRate: this.getMockBounceRate(),
        avgSessionDuration: this.getMockSessionDuration(),
        trafficSources: this.getMockTrafficSources(),
        geographic: this.getMockGeographicData(),
        deviceTypes: this.getMockDeviceTypes(),
        topPages: this.getMockTopPages(vendorId)
      }
    } catch (error) {
      console.error('Error fetching Google Analytics data:', error)
      return this.getEmptyAnalytics()
    }
  }

  private static getMockPageViews(dateRange: string): number {
    const multiplier = dateRange === '7d' ? 0.25 : dateRange === '30d' ? 1 : dateRange === '90d' ? 3 : 12
    return Math.floor(500 * multiplier * (1 + Math.random() * 0.4))
  }

  private static getMockUniqueVisitors(dateRange: string): number {
    const multiplier = dateRange === '7d' ? 0.25 : dateRange === '30d' ? 1 : dateRange === '90d' ? 3 : 12
    return Math.floor(380 * multiplier * (1 + Math.random() * 0.3))
  }

  private static getMockBounceRate(): number {
    return Math.round((25 + Math.random() * 20) * 10) / 10
  }

  private static getMockSessionDuration(): string {
    const minutes = Math.floor(Math.random() * 3) + 1
    const seconds = Math.floor(Math.random() * 60)
    return `${minutes}:${seconds.toString().padStart(2, '0')}`
  }

  private static getMockTrafficSources() {
    return [
      { source: 'Organic Search', visitors: 45, sessions: 1250 },
      { source: 'Direct', visitors: 30, sessions: 850 },
      { source: 'Social Media', visitors: 15, sessions: 420 },
      { source: 'Referral', visitors: 10, sessions: 280 }
    ]
  }

  private static getMockGeographicData() {
    return [
      { region: 'Tamil Nadu', visitors: 35, sessions: 980 },
      { region: 'Karnataka', visitors: 20, sessions: 560 },
      { region: 'Andhra Pradesh', visitors: 15, sessions: 420 },
      { region: 'Kerala', visitors: 12, sessions: 336 },
      { region: 'Telangana', visitors: 10, sessions: 280 },
      { region: 'Others', visitors: 8, sessions: 224 }
    ]
  }

  private static getMockDeviceTypes() {
    return [
      { device: 'Mobile', percentage: 65, sessions: 1820 },
      { device: 'Desktop', percentage: 25, sessions: 700 },
      { device: 'Tablet', percentage: 10, sessions: 280 }
    ]
  }

  private static getMockTopPages(vendorId: string) {
    return [
      { page: `/vendors/${vendorId}`, views: 1250, uniqueViews: 980 },
      { page: `/vendors/${vendorId}/gallery`, views: 450, uniqueViews: 380 },
      { page: `/vendors/${vendorId}/reviews`, views: 280, uniqueViews: 240 },
      { page: `/vendors/${vendorId}/contact`, views: 150, uniqueViews: 130 }
    ]
  }

  private static getEmptyAnalytics() {
    return {
      pageViews: 0,
      uniqueVisitors: 0,
      bounceRate: 0,
      avgSessionDuration: '0:00',
      trafficSources: [],
      geographic: [],
      deviceTypes: [],
      topPages: []
    }
  }

  // Real-time analytics methods
  static async getRealTimeData(vendorId: string) {
    // Mock real-time data
    return {
      activeUsers: Math.floor(Math.random() * 20) + 5,
      currentPageViews: Math.floor(Math.random() * 50) + 10,
      topActivePages: [
        { page: `/vendors/${vendorId}`, activeUsers: 8 },
        { page: `/vendors/${vendorId}/gallery`, activeUsers: 3 },
        { page: `/vendors/${vendorId}/reviews`, activeUsers: 2 }
      ],
      trafficSources: [
        { source: 'Organic Search', activeUsers: 12 },
        { source: 'Direct', activeUsers: 8 },
        { source: 'Social Media', activeUsers: 5 }
      ]
    }
  }

  // Conversion tracking
  static trackConversion(vendorId: string, conversionType: string, value?: number) {
    this.trackEvent('conversion', {
      vendor_id: vendorId,
      conversion_type: conversionType,
      value: value || 1,
      event_category: 'conversions'
    })
  }

  // Enhanced ecommerce tracking for bookings
  static trackBooking(vendorId: string, bookingData: {
    bookingId: string
    value: number
    currency: string
    serviceType: string
  }) {
    if (typeof window === 'undefined' || !window.gtag) return

    window.gtag('event', 'purchase', {
      transaction_id: bookingData.bookingId,
      value: bookingData.value,
      currency: bookingData.currency,
      items: [{
        item_id: vendorId,
        item_name: bookingData.serviceType,
        category: 'wedding_service',
        quantity: 1,
        price: bookingData.value
      }]
    })
  }
}