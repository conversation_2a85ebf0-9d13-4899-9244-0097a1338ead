'use client'

import { useAuth } from '@/contexts/AuthContext'
import { useState } from 'react'

export const AuthTest = () => {
  const { 
    user, 
    userProfile, 
    userType, 
    isLoading, 
    isAuthenticated,
    signInWithPassword,
    signUp,
    signOut 
  } = useAuth()
  
  const [testEmail, setTestEmail] = useState('')
  const [testPassword, setTestPassword] = useState('')
  const [testName, setTestName] = useState('')
  const [message, setMessage] = useState('')
  const [isTestLoading, setIsTestLoading] = useState(false)

  const handleTestLogin = async () => {
    if (!testEmail || !testPassword) {
      setMessage('Please enter email and password')
      return
    }

    setIsTestLoading(true)
    setMessage('')
    
    try {
      await signInWithPassword(testEmail, testPassword)
      setMessage('Login successful!')
    } catch (error: any) {
      setMessage(`Login failed: ${error.message}`)
    } finally {
      setIsTestLoading(false)
    }
  }

  const handleTestSignUp = async () => {
    if (!testEmail || !testPassword || !testName) {
      setMessage('Please enter name, email and password')
      return
    }

    setIsTestLoading(true)
    setMessage('')
    
    try {
      await signUp(testEmail, testName, testPassword)
      setMessage('Sign up successful! Check your email for verification.')
    } catch (error: any) {
      setMessage(`Sign up failed: ${error.message}`)
    } finally {
      setIsTestLoading(false)
    }
  }

  const handleTestLogout = async () => {
    setIsTestLoading(true)
    try {
      await signOut()
      setMessage('Logout successful!')
    } catch (error: any) {
      setMessage(`Logout failed: ${error.message}`)
    } finally {
      setIsTestLoading(false)
    }
  }

  if (isLoading) {
    return (
      <div className="p-6 bg-blue-50 rounded-lg">
        <h3 className="text-lg font-semibold text-blue-800 mb-2">
          Authentication Status
        </h3>
        <p className="text-blue-600">Loading authentication state...</p>
      </div>
    )
  }

  return (
    <div className="p-6 bg-white rounded-lg shadow-md">
      <h3 className="text-lg font-semibold text-gray-800 mb-4">
        🔐 Cognito Authentication Test
      </h3>
      
      {/* Authentication Status */}
      <div className="mb-6 p-4 bg-gray-50 rounded">
        <h4 className="font-medium text-gray-700 mb-2">Current Status:</h4>
        <div className="space-y-1 text-sm">
          <p><strong>Authenticated:</strong> {isAuthenticated ? '✅ Yes' : '❌ No'}</p>
          <p><strong>User Type:</strong> {userType || 'Not determined'}</p>
          <p><strong>User ID:</strong> {user?.userId || 'N/A'}</p>
          <p><strong>Username:</strong> {user?.username || 'N/A'}</p>
          <p><strong>Profile Loaded:</strong> {userProfile ? '✅ Yes' : '❌ No'}</p>
        </div>
      </div>

      {/* Test Actions */}
      {!isAuthenticated ? (
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Full Name
            </label>
            <input
              type="text"
              value={testName}
              onChange={(e) => setTestName(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Enter full name"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Email
            </label>
            <input
              type="email"
              value={testEmail}
              onChange={(e) => setTestEmail(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Enter email"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Password
            </label>
            <input
              type="password"
              value={testPassword}
              onChange={(e) => setTestPassword(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Enter password"
            />
          </div>
          
          <div className="flex space-x-3">
            <button
              onClick={handleTestLogin}
              disabled={isTestLoading}
              className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:opacity-50"
            >
              {isTestLoading ? 'Testing...' : 'Test Login'}
            </button>
            
            <button
              onClick={handleTestSignUp}
              disabled={isTestLoading}
              className="px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 disabled:opacity-50"
            >
              {isTestLoading ? 'Testing...' : 'Test Sign Up'}
            </button>
          </div>
        </div>
      ) : (
        <div className="space-y-4">
          <div className="p-4 bg-green-50 rounded">
            <h4 className="font-medium text-green-800 mb-2">✅ Authentication Successful!</h4>
            <p className="text-green-700 text-sm">
              You are successfully authenticated with Cognito User Pool: {user?.username}
            </p>
          </div>
          
          <button
            onClick={handleTestLogout}
            disabled={isTestLoading}
            className="px-4 py-2 bg-red-500 text-white rounded-md hover:bg-red-600 disabled:opacity-50"
          >
            {isTestLoading ? 'Logging out...' : 'Test Logout'}
          </button>
        </div>
      )}
      
      {/* Message Display */}
      {message && (
        <div className={`mt-4 p-3 rounded ${
          message.includes('successful') 
            ? 'bg-green-100 text-green-700' 
            : 'bg-red-100 text-red-700'
        }`}>
          {message}
        </div>
      )}
      
      {/* Raw User Data */}
      {user && (
        <div className="mt-6">
          <h4 className="font-medium text-gray-700 mb-2">Raw User Data:</h4>
          <pre className="text-xs bg-gray-100 p-3 rounded overflow-auto">
            {JSON.stringify(user, null, 2)}
          </pre>
        </div>
      )}
    </div>
  )
}
