import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { AuthState, LoginCredentials, SignupData } from '../shared/types';
import { amplifyAuthService } from '../services/amplifyService';

interface AuthContextType extends AuthState {
  login: (credentials: LoginCredentials) => Promise<void>;
  signup: (data: SignupData) => Promise<void>;
  logout: () => Promise<void>;
  refreshAuth: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [authState, setAuthState] = useState<AuthState>({
    isAuthenticated: false,
    isLoading: true,
    user: null,
    userProfile: null,
    userType: null,
    error: null,
  });

  // Initialize auth on app start
  useEffect(() => {
    initializeAuth();
  }, []);

  const initializeAuth = async () => {
    try {
      const initialState = await amplifyAuthService.getCurrentAuthState();
      setAuthState(initialState);
    } catch (error) {
      console.error('Auth initialization failed:', error);
      setAuthState(prev => ({
        ...prev,
        isLoading: false,
        error: 'Failed to initialize authentication',
      }));
    }
  };

  const login = async (credentials: LoginCredentials) => {
    try {
      setAuthState(prev => ({ ...prev, isLoading: true, error: null }));
      const newAuthState = await amplifyAuthService.login(credentials);
      setAuthState(newAuthState);
    } catch (error) {
      setAuthState(prev => ({
        ...prev,
        isLoading: false,
        error: error instanceof Error ? error.message : 'Login failed',
      }));
      throw error;
    }
  };

  const signup = async (data: SignupData) => {
    try {
      setAuthState(prev => ({ ...prev, isLoading: true, error: null }));
      const result = await amplifyAuthService.signup(data);

      if (result.needsConfirmation) {
        // Handle email confirmation flow
        setAuthState(prev => ({
          ...prev,
          isLoading: false,
          error: null,
        }));
        // You might want to navigate to confirmation screen here
      } else if (result.authState) {
        setAuthState(result.authState);
      }
    } catch (error) {
      setAuthState(prev => ({
        ...prev,
        isLoading: false,
        error: error instanceof Error ? error.message : 'Signup failed',
      }));
      throw error;
    }
  };

  const logout = async () => {
    try {
      setAuthState(prev => ({ ...prev, isLoading: true }));
      await amplifyAuthService.logout();
      setAuthState({
        isAuthenticated: false,
        isLoading: false,
        user: null,
        userProfile: null,
        userType: null,
        error: null,
      });
    } catch (error) {
      console.error('Logout failed:', error);
      // Still clear auth state even if API call fails
      setAuthState({
        isAuthenticated: false,
        isLoading: false,
        user: null,
        userProfile: null,
        userType: null,
        error: null,
      });
    }
  };

  const refreshAuth = async () => {
    try {
      setAuthState(prev => ({ ...prev, isLoading: true }));
      const newAuthState = await amplifyAuthService.getCurrentAuthState();
      setAuthState(newAuthState);
    } catch (error) {
      console.error('Auth refresh failed:', error);
      setAuthState({
        isAuthenticated: false,
        isLoading: false,
        user: null,
        userProfile: null,
        userType: null,
        error: 'Session expired',
      });
    }
  };

  const contextValue: AuthContextType = {
    ...authState,
    login,
    signup,
    logout,
    refreshAuth,
  };

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth(): AuthContextType {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
