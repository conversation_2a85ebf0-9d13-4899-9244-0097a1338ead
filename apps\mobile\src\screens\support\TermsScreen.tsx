import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
} from 'react-native';
import { useTheme } from '../../providers/ThemeProvider';
import { Header } from '../../components/Header';
import { Card } from '../../components/ui';

export default function TermsScreen() {
  const { theme } = useTheme();

  const termsSections = [
    {
      title: 'Acceptance of Terms',
      content: `By accessing and using BookmyFestive, you accept and agree to be bound by the terms and provision of this agreement. If you do not agree to abide by the above, please do not use this service.`
    },
    {
      title: 'Use License',
      content: `Permission is granted to temporarily download one copy of BookmyFestive for personal, non-commercial transitory viewing only. This is the grant of a license, not a transfer of title.`
    },
    {
      title: 'User Accounts',
      content: `When you create an account with us, you must provide information that is accurate, complete, and current at all times. You are responsible for safeguarding the password and for all activities that occur under your account.`
    },
    {
      title: 'Vendor Services',
      content: `Vendors on our platform are independent service providers. BookmyFestive acts as a marketplace connecting customers with vendors. We do not guarantee the quality of services provided by vendors.`
    },
    {
      title: 'Payment Terms',
      content: `All payments are processed securely through our payment partners. Refund policies vary by vendor and service type. Please review individual vendor policies before making a purchase.`
    },
    {
      title: 'Content Guidelines',
      content: `Users are responsible for the content they post on our platform. Content must be appropriate, legal, and not infringe on the rights of others. We reserve the right to remove inappropriate content.`
    },
    {
      title: 'Limitation of Liability',
      content: `BookmyFestive shall not be liable for any indirect, incidental, special, consequential, or punitive damages, including without limitation, loss of profits, data, use, goodwill, or other intangible losses.`
    },
    {
      title: 'Termination',
      content: `We may terminate or suspend your account immediately, without prior notice or liability, for any reason whatsoever, including without limitation if you breach the Terms.`
    },
    {
      title: 'Changes to Terms',
      content: `We reserve the right, at our sole discretion, to modify or replace these Terms at any time. If a revision is material, we will try to provide at least 30 days notice prior to any new terms taking effect.`
    }
  ];

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <Header title="Terms of Service" showBack />
      
      <ScrollView 
        style={styles.content}
        showsVerticalScrollIndicator={false}
      >
        <Card style={styles.headerCard}>
          <Text style={[styles.title, { color: theme.colors.text }]}>
            Terms of Service
          </Text>
          <Text style={[styles.lastUpdated, { color: theme.colors.textSecondary }]}>
            Last Updated: January 25, 2024
          </Text>
          <Text style={[styles.intro, { color: theme.colors.textSecondary }]}>
            Welcome to BookmyFestive. These terms and conditions outline the rules and regulations for the use of our wedding planning platform. By using our service, you agree to comply with these terms.
          </Text>
        </Card>

        {termsSections.map((section, index) => (
          <Card key={index} style={styles.sectionCard}>
            <Text style={[styles.sectionTitle, { color: theme.colors.primary }]}>
              {index + 1}. {section.title}
            </Text>
            <Text style={[styles.sectionContent, { color: theme.colors.text }]}>
              {section.content}
            </Text>
          </Card>
        ))}

        <Card style={styles.footerCard}>
          <Text style={[styles.footerTitle, { color: theme.colors.primary }]}>
            Contact Information
          </Text>
          <Text style={[styles.footerContent, { color: theme.colors.textSecondary }]}>
            If you have any questions about these Terms of Service, please contact us at:
          </Text>
          <Text style={[styles.contactInfo, { color: theme.colors.text }]}>
            Email: <EMAIL>{'\n'}
            Phone: +91 80 1234 5678{'\n'}
            Address: Bangalore, Karnataka, India
          </Text>
        </Card>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    paddingHorizontal: 16,
  },
  headerCard: {
    marginVertical: 16,
    padding: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  lastUpdated: {
    fontSize: 14,
    marginBottom: 16,
    fontStyle: 'italic',
  },
  intro: {
    fontSize: 16,
    lineHeight: 24,
  },
  sectionCard: {
    marginBottom: 16,
    padding: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 12,
  },
  sectionContent: {
    fontSize: 15,
    lineHeight: 22,
  },
  footerCard: {
    marginBottom: 32,
    padding: 20,
  },
  footerTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 12,
  },
  footerContent: {
    fontSize: 15,
    lineHeight: 22,
    marginBottom: 12,
  },
  contactInfo: {
    fontSize: 14,
    lineHeight: 20,
    fontFamily: 'monospace',
  },
});
