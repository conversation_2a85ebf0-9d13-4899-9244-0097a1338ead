# 📧 Email Service Setup Guide

This guide will help you configure the email service to send actual emails using AWS SES directly.

## 🚀 Quick Start (AWS SES - Recommended)

### **Step 1: Configure AWS SES in AWS Console**

1. **Go to AWS SES Console:**
   - Navigate to [AWS SES Console](https://console.aws.amazon.com/ses/)
   - Select your region (e.g., us-east-1)

2. **Verify Your Domain or Email:**
   ```bash
   # Option A: Verify Domain (Recommended for production)
   - Click "Verified identities"
   - Click "Create identity"
   - Choose "Domain" and enter your domain
   - Follow DNS verification steps
   
   # Option B: Verify Email (For testing)
   - Click "Verified identities"
   - Click "Create identity"
   - Choose "Email address" and enter your email
   - Check your inbox and click verification link
   ```

3. **Move Out of Sandbox (Production):**
   - By default, AWS SES is in sandbox mode
   - You can only send to verified emails/domains
   - To send to any email, request production access
   - Go to "Account dashboard" → "Request production access"

### **Step 2: Create IAM User for SES**

1. **Go to IAM Console:**
   - Navigate to [IAM Console](https://console.aws.amazon.com/iam/)
   - Click "Users" → "Create user"

2. **Attach SES Policy:**
   ```json
   {
     "Version": "2012-10-17",
     "Statement": [
       {
         "Effect": "Allow",
         "Action": [
           "ses:SendEmail",
           "ses:SendRawEmail"
         ],
         "Resource": "*"
       }
     ]
   }
   ```

3. **Generate Access Keys:**
   - Click on your user → "Security credentials"
   - Create access key
   - Save the Access Key ID and Secret Access Key

### **Step 3: Set Environment Variables**

Create a `.env.local` file in your project root:

```bash
# Email Service Configuration
NEXT_PUBLIC_EMAIL_PROVIDER=aws-ses
NEXT_PUBLIC_FROM_EMAIL=<EMAIL>
NEXT_PUBLIC_AWS_REGION=us-east-1

# AWS Credentials (Never commit these to git!)
AWS_ACCESS_KEY_ID=your_access_key_here
AWS_SECRET_ACCESS_KEY=your_secret_key_here
```

## 🔧 Environment Variables Reference

| Variable | Description | Required | Example |
|----------|-------------|----------|---------|
| `NEXT_PUBLIC_EMAIL_PROVIDER` | Email service provider | Yes | `aws-ses` |
| `NEXT_PUBLIC_FROM_EMAIL` | Sender email address | Yes | `<EMAIL>` |
| `NEXT_PUBLIC_AWS_REGION` | AWS region for SES | Yes | `us-east-1` |
| `AWS_ACCESS_KEY_ID` | AWS access key | Yes | `AKIA...` |
| `AWS_SECRET_ACCESS_KEY` | AWS secret key | Yes | `...` |

## 📋 Email Types Supported

The email service supports these email types:

1. **Welcome Emails:**
   - User signup welcome
   - User login welcome
   - Newsletter subscription welcome
   - Vendor signup welcome

2. **Transaction Emails:**
   - Booking confirmation
   - Payment success
   - Order tracking

3. **Marketing Emails:**
   - Weekly newsletter
   - Special offers
   - Vendor launches
   - Favorites notifications

4. **System Emails:**
   - Login OTP
   - Availability checks

## 🧪 Testing

### **Test via Web Interface**
1. Visit `/test-emails` (requires login)
2. Enter your email address
3. Click test buttons for different email types
4. Check console for results

### **Test via Command Line**
```bash
# Test all email services
npm run test:email

# Test booking confirmation flow
npm run test:booking

# Test actual email sending
npm run test:email:sending
```

## 🔍 Troubleshooting

### **Common Issues**

1. **"Email service not configured"**
   - Check environment variables
   - Ensure AWS credentials are set
   - Verify region is correct

2. **"AWS SES error: MessageRejected"**
   - Check if recipient email is verified (in sandbox mode)
   - Verify your domain/email is verified
   - Check if you're still in sandbox mode

3. **"AWS SES error: AccessDenied"**
   - Check IAM permissions
   - Verify access keys are correct
   - Ensure SES policy is attached

### **Debug Mode**

Enable debug logging by setting:
```bash
NODE_ENV=development
```

### **Check Email Logs**

All emails are logged to the database with status:
- `SENT`: Email sent successfully
- `FAILED`: Email sending failed
- `PENDING`: Email queued for sending

## 🚀 Production Deployment

### **AWS SES Production Setup**
1. **Request Production Access:**
   - Go to SES Console → Account dashboard
   - Click "Request production access"
   - Fill out the form with your use case

2. **Domain Verification:**
   - Verify your domain ownership
   - Set up SPF and DKIM records
   - Monitor bounce and complaint rates

3. **Monitoring:**
   - Set up CloudWatch alarms
   - Monitor delivery rates
   - Track bounce and complaint rates

## 📊 Monitoring

### **Email Metrics to Track**
- Delivery rate
- Bounce rate
- Complaint rate
- Open rate (if using tracking)
- Click rate (if using tracking)

### **AWS CloudWatch Metrics**
- `Send` - Number of emails sent
- `Bounce` - Number of bounces
- `Complaint` - Number of complaints
- `Delivery` - Number of successful deliveries

## 🔐 Security Best Practices

1. **Never commit credentials to git**
2. **Use environment variables**
3. **Rotate access keys regularly**
4. **Use least privilege IAM policies**
5. **Monitor for suspicious activity**

## 📞 Support

If you encounter issues:

1. Check the troubleshooting section
2. Verify AWS credentials and permissions
3. Check AWS SES console for errors
4. Review CloudWatch logs
5. Test with command-line tools

## 🎯 Next Steps

After configuring AWS SES:

1. **Test all email types** via `/test-emails`
2. **Verify delivery** in your email inbox
3. **Monitor AWS SES metrics** in CloudWatch
4. **Request production access** if needed
5. **Set up monitoring and alerts**

## 🌟 Benefits of AWS SES Direct Integration

- **No API routes needed** - Direct AWS SDK calls
- **Better performance** - No additional HTTP layer
- **Cost effective** - Pay only for emails sent
- **Native integration** - Works seamlessly with Amplify
- **Better security** - Direct AWS authentication
- **Scalable** - Handles high volume automatically

---

**Happy Email Sending with AWS SES! 📧✨** 