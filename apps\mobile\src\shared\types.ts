// Temporary shared types for mobile app
export interface User {
  id: string;
  email: string;
  fullName: string;
  phone?: string;
  isAdmin?: boolean;
  role?: string;
  createdAt: string;
  updatedAt: string;
}

export interface UserProfile extends User {
  avatar?: string;
  city?: string;
  state?: string;
  weddingDate?: string;
  partnerName?: string;
  budget?: number;
}

export interface CartItem {
  id: number | string;
  name: string;
  price: number;
  quantity: number;
  image?: string;
  vendorId?: string;
  vendorName?: string;
  category?: string;
}

export interface Cart {
  items: CartItem[];
  total: number;
  itemCount: number;
  lastUpdated: string;
}

export interface AuthState {
  isAuthenticated: boolean;
  isLoading: boolean;
  user: User | null;
  userProfile: UserProfile | null;
  userType: 'customer' | 'vendor' | 'admin' | null;
  error: string | null;
}

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface SignupData {
  fullName: string;
  email: string;
  phone?: string;
  password: string;
  userType?: 'customer' | 'vendor';
}
