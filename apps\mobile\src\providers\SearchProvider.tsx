import React, { createContext, useContext, useState, useEffect } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';

interface SearchFilters {
  category?: string;
  location?: string;
  priceRange?: {
    min: number;
    max: number;
  };
  rating?: number;
  sortBy?: 'rating' | 'price' | 'popularity' | 'distance';
  sortOrder?: 'asc' | 'desc';
}

interface SearchHistory {
  id: string;
  query: string;
  type: 'vendor' | 'venue' | 'product';
  timestamp: string;
}

interface SearchContextType {
  searchQuery: string;
  setSearchQuery: (query: string) => void;
  searchFilters: SearchFilters;
  setSearchFilters: (filters: SearchFilters) => void;
  searchHistory: SearchHistory[];
  addToSearchHistory: (query: string, type: 'vendor' | 'venue' | 'product') => void;
  clearSearchHistory: () => void;
  recentSearches: string[];
  popularSearches: string[];
  isSearching: boolean;
  setIsSearching: (searching: boolean) => void;
}

const SearchContext = createContext<SearchContextType | undefined>(undefined);

export const useSearch = () => {
  const context = useContext(SearchContext);
  if (!context) {
    throw new Error('useSearch must be used within a SearchProvider');
  }
  return context;
};

const STORAGE_KEY = 'search_history';
const MAX_HISTORY_ITEMS = 20;

export const SearchProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [searchFilters, setSearchFilters] = useState<SearchFilters>({});
  const [searchHistory, setSearchHistory] = useState<SearchHistory[]>([]);
  const [isSearching, setIsSearching] = useState(false);

  // Popular searches - could be fetched from API
  const popularSearches = [
    'Wedding Photography',
    'Bridal Makeup',
    'Wedding Venues',
    'Catering Services',
    'Wedding Decoration',
    'DJ Services',
    'Wedding Cards',
    'Bridal Wear',
  ];

  useEffect(() => {
    loadSearchHistory();
  }, []);

  const loadSearchHistory = async () => {
    try {
      const storedHistory = await AsyncStorage.getItem(STORAGE_KEY);
      if (storedHistory) {
        setSearchHistory(JSON.parse(storedHistory));
      }
    } catch (error) {
      console.error('Error loading search history:', error);
    }
  };

  const saveSearchHistory = async (history: SearchHistory[]) => {
    try {
      await AsyncStorage.setItem(STORAGE_KEY, JSON.stringify(history));
    } catch (error) {
      console.error('Error saving search history:', error);
    }
  };

  const addToSearchHistory = (query: string, type: 'vendor' | 'venue' | 'product') => {
    if (!query.trim()) return;

    const newSearch: SearchHistory = {
      id: Date.now().toString(),
      query: query.trim(),
      type,
      timestamp: new Date().toISOString(),
    };

    // Remove duplicate if exists
    const filteredHistory = searchHistory.filter(
      item => item.query.toLowerCase() !== query.toLowerCase()
    );

    // Add new search at the beginning
    const updatedHistory = [newSearch, ...filteredHistory].slice(0, MAX_HISTORY_ITEMS);
    
    setSearchHistory(updatedHistory);
    saveSearchHistory(updatedHistory);
  };

  const clearSearchHistory = async () => {
    try {
      setSearchHistory([]);
      await AsyncStorage.removeItem(STORAGE_KEY);
    } catch (error) {
      console.error('Error clearing search history:', error);
    }
  };

  // Get recent unique search queries
  const recentSearches = searchHistory
    .slice(0, 10)
    .map(item => item.query)
    .filter((query, index, array) => array.indexOf(query) === index);

  const value: SearchContextType = {
    searchQuery,
    setSearchQuery,
    searchFilters,
    setSearchFilters,
    searchHistory,
    addToSearchHistory,
    clearSearchHistory,
    recentSearches,
    popularSearches,
    isSearching,
    setIsSearching,
  };

  return (
    <SearchContext.Provider value={value}>
      {children}
    </SearchContext.Provider>
  );
};
