"use client"

import React from 'react'
import { signOut, getCurrentUser, fetchAuthSession, signInWithCustomAuth, signUp, confirmSignUp, signIn } from 'aws-amplify/auth'
import { generateClient } from 'aws-amplify/api'
import { Hub } from 'aws-amplify/utils'
import { profileService } from './profileService'
import { createUserProfile, updateUserProfile } from '@/src/graphql/mutations'
import { listUserProfiles, getUserProfile } from '@/src/graphql/queries'

export interface GoogleAuthResult {
  success: boolean
  user?: any
  userProfile?: any
  error?: string
  isNewUser?: boolean
}

// Extend window object for Google APIs
declare global {
  interface Window {
    google: any
  }
}

/**
 * Google Authentication Service for BookmyFestive
 * Handles Google OAuth integration with popup-based authentication
 */
export class GoogleAuthService {
  private static googleAuth: any = null
  private static isInitialized = false

  /**
   * Initialize Google Auth API
   */
  static async initializeGoogleAuth(): Promise<void> {
    if (this.isInitialized) return

    try {
      // Load Google Identity Services
      await this.loadGoogleScript()

      // Initialize Google Auth
      if (typeof window !== 'undefined' && window.google) {
        this.googleAuth = window.google.accounts.oauth2
        this.isInitialized = true
        console.log('Google Auth initialized successfully')
      }
    } catch (error) {
      console.error('Failed to initialize Google Auth:', error)
      throw new Error('Google Auth initialization failed')
    }
  }

  /**
   * Load Google Identity Services script
   */
  private static loadGoogleScript(): Promise<void> {
    return new Promise((resolve, reject) => {
      if (typeof window === 'undefined') {
        reject(new Error('Window object not available'))
        return
      }

      // Check if script is already loaded
      if (window.google && window.google.accounts) {
        resolve()
        return
      }

      const script = document.createElement('script')
      script.src = 'https://accounts.google.com/gsi/client'
      script.async = true
      script.defer = true

      script.onload = () => resolve()
      script.onerror = () => reject(new Error('Failed to load Google script'))

      document.head.appendChild(script)
    })
  }

  /**
   * Sign in with Google using popup
   */
  static async signInWithGooglePopup(): Promise<GoogleAuthResult> {
    try {
      await this.initializeGoogleAuth()

      return new Promise((resolve) => {
        if (!window.google) {
          resolve({
            success: false,
            error: 'Google Auth not available'
          })
          return
        }

        const client = window.google.accounts.oauth2.initTokenClient({
          client_id: process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID || '*************-pnl7jismo4sqd50odsjqjb3sn97hdqvq.apps.googleusercontent.com',
          scope: 'openid email profile',
          callback: async (response: any) => {
            clearTimeout(timeoutId)
            
            if (response.error) {
              // Handle different types of errors
              let errorMessage = response.error
              if (response.error === 'popup_closed_by_user' || response.error === 'access_denied') {
                errorMessage = 'popup_closed'
              }
              
              resolve({
                success: false,
                error: errorMessage
              })
              return
            }

            try {
              // Get user info from Google
              const userInfo = await this.getUserInfo(response.access_token)

              // Create user profile in our system
              const result = await this.handleGoogleUser(userInfo, response.access_token)
              resolve(result)
            } catch (error: any) {
              resolve({
                success: false,
                error: error.message || 'Failed to process Google authentication'
              })
            }
          },
          error_callback: (error: any) => {
            clearTimeout(timeoutId)
            // Handle popup closure and other errors
            console.log('Google OAuth error:', error)
            resolve({
              success: false,
              error: 'popup_closed'
            })
          }
        })

        // Add timeout to handle cases where popup is closed without callback
        const timeoutId = setTimeout(() => {
          resolve({
            success: false,
            error: 'popup_closed'
          })
        }, 60000) // 60 second timeout

        try {
          client.requestAccessToken()
        } catch (requestError: any) {
          clearTimeout(timeoutId)
          resolve({
            success: false,
            error: 'popup_closed'
          })
        }
      })
    } catch (error: any) {
      return {
        success: false,
        error: error.message || 'Google sign-in failed'
      }
    }
  }

  /**
   * Get user information from Google API
   */
  private static async getUserInfo(accessToken: string): Promise<any> {
    const response = await fetch('https://www.googleapis.com/oauth2/v2/userinfo', {
      headers: {
        Authorization: `Bearer ${accessToken}`
      }
    })

    if (!response.ok) {
      throw new Error('Failed to get user information from Google')
    }

    return response.json()
  }

  /**
   * Handle Google user authentication and profile creation
   */
  private static async handleGoogleUser(googleUser: any, accessToken: string): Promise<GoogleAuthResult> {
    try {
      console.log('🔄 Processing Google user:', googleUser.email)

      // Check if user already exists in our system (using public API access)
      const existingProfile = await this.findUserByEmail(googleUser.email)

      if (existingProfile) {
        console.log('✅ Existing user found:', existingProfile.email)
        console.log('🔄 Attempting Cognito integration for existing user...')

        // For existing users, we need to sign in with Cognito
        // First, try to sign in with the email (assuming they have a Cognito account)
        try {
          // Try to get current user first (in case already signed in)
          let currentUser
          let cognitoIntegrated = false

          try {
            currentUser = await getCurrentUser()
            console.log('✅ User already signed in to Cognito:', currentUser.userId)
            cognitoIntegrated = true
          } catch {
            // User not signed in, need to sign in
            console.log('🔄 User not signed in to Cognito, attempting sign in...')

            // Try to sign in existing Google user with Cognito
            try {
              currentUser = await this.signInExistingGoogleUser(googleUser, existingProfile)
              console.log('✅ Successfully signed in existing Google user to Cognito')
              cognitoIntegrated = true
            } catch (signInError: any) {
              console.log('⚠️ Cognito sign-in failed for existing user:', signInError.message)
              console.log('🔄 Continuing with profile-only authentication...')
              cognitoIntegrated = false
              // Continue without Cognito authentication - user can still access the app
            }
          }

          const authSession = {
            user: {
              userId: currentUser?.userId || existingProfile.userId || existingProfile.id,
              username: currentUser?.username || googleUser.email,
              email: googleUser.email,
              signInDetails: {
                loginId: googleUser.email,
                authFlowType: 'GOOGLE_OAUTH'
              }
            },
            userProfile: existingProfile,
            isAuthenticated: true,
            authProvider: 'google',
            accessToken,
            cognitoIntegrated, // Track whether Cognito integration was successful
            authMode: cognitoIntegrated ? 'cognito-integrated' : 'profile-only'
          }

          if (typeof window !== 'undefined') {
            localStorage.setItem('googleAuthSession', JSON.stringify(authSession))
            localStorage.setItem('amplify-google-auth', JSON.stringify({
              isAuthenticated: true,
              user: authSession.user,
              userProfile: authSession.userProfile,
              accessToken: accessToken,
              cognitoIntegrated,
              authMode: authSession.authMode,
              timestamp: Date.now()
            }))
          }

          console.log(`✅ Existing user authenticated successfully (${authSession.authMode})`)

          return {
            success: true,
            user: authSession.user,
            userProfile: existingProfile,
            isNewUser: false
          }
        } catch (error: any) {
          console.error('❌ Error signing in existing user:', error)
          throw error
        }
      } else {
        console.log('🔄 New user, starting complete signup flow')

        // For new users, we need a complete flow: Google auth -> Cognito signup -> OTP -> Password -> Profile -> Dashboard
        const signupResult = await this.createNewGoogleUserWithCompleteFlow(googleUser, accessToken)
        return {
          success: true,
          user: signupResult.user,
          userProfile: signupResult.userProfile,
          isNewUser: true,
          requiresOTP: signupResult.requiresOTP,
          requiresPassword: signupResult.requiresPassword,
          signupStep: signupResult.signupStep,
          tempPassword: signupResult.tempPassword,
          cognitoUserId: signupResult.cognitoUserId
        }
      }
    } catch (error: any) {
      console.error('❌ Error handling Google user:', error)
      return {
        success: false,
        error: error.message || 'Failed to process Google user'
      }
    }
  }

  /**
   * Find user by email in our system
   */
  private static async findUserByEmail(email: string): Promise<any> {
    try {
      // Use imported GraphQL query with public API access
      const publicClient = generateClient({
        authMode: 'apiKey'
      });

      const result = await publicClient.graphql({
        query: listUserProfiles,
        variables: {
          filter: {
            email: { eq: email }
          }
        }
      });

      const profiles = result.data?.listUserProfiles?.items || [];
      return profiles.length > 0 ? profiles[0] : null;
    } catch (error) {
      console.log('User not found by email:', email)
      return null
    }
  }

  /**
   * Sign in existing user
   */
  private static async signInExistingUser(userProfile: any, googleUser: any): Promise<any> {
    try {
      // Create a custom auth session for Google users
      const authSession = {
        user: {
          userId: userProfile.id,
          username: userProfile.email,
          email: googleUser.email,
          signInDetails: {
            loginId: googleUser.email,
            authFlowType: 'GOOGLE_OAUTH'
          }
        }
      }

      // Store auth session in localStorage for persistence
      localStorage.setItem('googleAuthSession', JSON.stringify(authSession))

      return authSession
    } catch (error) {
      console.error('Error signing in existing user:', error)
      throw new Error('Failed to sign in existing user')
    }
  }

  /**
   * Create new Google user with complete signup flow (OTP + Password + Profile)
   */
  private static async createNewGoogleUserWithCompleteFlow(googleUser: any, accessToken: string): Promise<any> {
    try {
      console.log('🔄 Starting complete Google user signup flow:', googleUser.email)

      // Step 1: Create Cognito user (this will require OTP confirmation)
      const tempPassword = this.generateTemporaryPassword()

      console.log('🔄 Creating Cognito user for complete flow...')
      const signUpResult = await signUp({
        username: googleUser.email,
        password: tempPassword,
        options: {
          userAttributes: {
            email: googleUser.email,
            given_name: googleUser.given_name || 'Google',
            family_name: googleUser.family_name || 'User',
            picture: googleUser.picture || ''
            // Don't mark email as verified - let the normal OTP flow handle it
          }
        }
      })

      console.log('✅ Cognito user created for complete flow:', {
        userId: signUpResult.userId,
        isSignUpComplete: signUpResult.isSignUpComplete,
        nextStep: signUpResult.nextStep
      })

      // Step 2: Check if OTP confirmation is required
      let requiresOTP = false
      let requiresPassword = false
      let signupStep = 'profile_creation'

      if (!signUpResult.isSignUpComplete && signUpResult.nextStep?.signUpStep === 'CONFIRM_SIGN_UP') {
        console.log('✅ OTP confirmation required - this is expected for new users')
        requiresOTP = true
        signupStep = 'otp_verification'
      }

      // Step 3: Prepare user profile data (we'll create it after authentication is complete)
      console.log('🔄 Preparing user profile data for later creation...')
      const profileData = {
        userId: signUpResult.userId || googleUser.id,
        firstName: googleUser.given_name || 'Google',
        lastName: googleUser.family_name || 'User',
        email: googleUser.email,
        profilePhoto: googleUser.picture,
        country: 'India',
        isVendor: false,
        isAdmin: false,
        isSuperAdmin: false,
        role: 'CUSTOMER',
        registrationSource: 'SOCIAL_LOGIN',
        accountType: 'PERSONAL',
        isVerified: false // Will be set to true after OTP verification
      }

      console.log('✅ User profile data prepared - will create after authentication')

      // Step 4: Create temporary auth session
      const tempAuthSession = {
        user: {
          userId: signUpResult.userId || googleUser.id,
          username: googleUser.email,
          email: googleUser.email,
          signInDetails: {
            loginId: googleUser.email,
            authFlowType: 'GOOGLE_OAUTH'
          }
        },
        userProfile: null, // Will be created after authentication is complete
        isAuthenticated: false, // Not fully authenticated until OTP/password setup
        authProvider: 'google',
        accessToken,
        signupInProgress: true
      }

      // Store temporary session
      if (typeof window !== 'undefined') {
        localStorage.setItem('googleSignupSession', JSON.stringify(tempAuthSession))
        localStorage.setItem('googleSignupData', JSON.stringify({
          googleUser,
          accessToken,
          cognitoUserId: signUpResult.userId,
          tempPassword,
          signupStep,
          requiresOTP,
          requiresPassword: true, // Always require password setup for new users
          profileData, // Store profile data to create later
          otpVerified: false,
          passwordSet: false
        }))
      }

      console.log('✅ Complete signup flow initiated - user needs to complete OTP and password setup')

      return {
        user: tempAuthSession.user,
        userProfile: null, // Will be created after authentication is complete
        requiresOTP,
        requiresPassword: true,
        signupStep,
        tempPassword,
        cognitoUserId: signUpResult.userId
      }
    } catch (error: any) {
      console.error('❌ Error in complete Google signup flow:', error)
      throw new Error('Failed to start complete signup flow: ' + (error.message || 'Unknown error'))
    }
  }

  /**
   * Create new Google user with both Cognito and database profile (legacy method)
   *
   * Note: This method attempts to create a user in Cognito and integrate with the app's user profile.
   * If Cognito requires email confirmation (which it shouldn't for Google OAuth users),
   * this will throw an error and the system will fall back to profile-only creation.
   *
   * To fix the Cognito confirmation issue permanently:
   * 1. In AWS Cognito User Pool settings, set "Email verification" to "Optional"
   * 2. Or configure a Lambda trigger to auto-confirm Google OAuth users
   * 3. Or use the AWS CLI to set the user pool to not require email verification
   */
  private static async createNewGoogleUserSimple(googleUser: any, accessToken: string): Promise<any> {
    try {
      console.log('🔄 Creating new Google user with Cognito and profile:', googleUser.email)

      // Step 1: Create Cognito user first
      const tempPassword = this.generateTemporaryPassword()

      console.log('🔄 Creating Cognito user...')
      let signUpResult
      try {
        signUpResult = await signUp({
          username: googleUser.email,
          password: tempPassword,
          options: {
            userAttributes: {
              email: googleUser.email,
              given_name: googleUser.given_name || 'Google',
              family_name: googleUser.family_name || 'User',
              picture: googleUser.picture || '',
              email_verified: 'true' // Mark email as verified since Google has already verified it
              // Note: Only using standard attributes that are available in Cognito
            },
            autoSignIn: true // Try auto sign-in for Google users
          }
        })

        console.log('✅ Cognito user created:', {
          userId: signUpResult.userId,
          isSignUpComplete: signUpResult.isSignUpComplete,
          nextStep: signUpResult.nextStep
        })

        // Check if sign-up was completed automatically
        if (signUpResult.isSignUpComplete) {
          console.log('✅ Sign-up completed automatically - user is ready to use')
        }
      } catch (signUpError: any) {
        console.error('❌ Sign-up failed:', signUpError)

        if (signUpError.name === 'UsernameExistsException') {
          console.log('⚠️ User already exists in Cognito, this might be a retry')
          // For existing users, we'll try to sign in directly
          throw new Error('User already exists in Cognito. Please try signing in instead.')
        }

        throw signUpError
      }

      // Step 2: Handle confirmation for Google users
      console.log('🔄 Checking user confirmation status...')

      // For Google users, we need to handle confirmation automatically since Google has already verified the email
      if (!signUpResult.isSignUpComplete && signUpResult.nextStep?.signUpStep === 'CONFIRM_SIGN_UP') {
        console.log('🔄 User needs confirmation - auto-confirming for Google OAuth user...')

        try {
          // For Google users, we'll use a dummy confirmation code since the email is already verified by Google
          // This is a workaround for Cognito requiring confirmation even for OAuth users
          await confirmSignUp({
            username: googleUser.email,
            confirmationCode: '000000' // Dummy code - this will fail but trigger admin confirmation
          })
        } catch (confirmError: any) {
          console.log('⚠️ Expected confirmation error for Google user:', confirmError.name)

          // If confirmation fails (expected), we'll try to sign in anyway
          // The admin should have auto-confirmed the user or we'll handle it in sign-in
        }
      }

      // Step 3: Sign in the user to get proper authentication
      console.log('🔄 Signing in user...')

      // Check if user is already signed in due to autoSignIn
      let signInResult
      try {
        const currentUser = await getCurrentUser()
        console.log('✅ User already signed in via autoSignIn:', currentUser.userId)
        signInResult = { isSignedIn: true, nextStep: null }
      } catch {
        // User not signed in, proceed with manual sign-in
        try {
          signInResult = await signIn({
            username: googleUser.email,
            password: tempPassword
          })
        } catch (signInError: any) {
          console.log('⚠️ Sign-in failed, checking if it\'s due to unconfirmed status:', signInError.name)

          // If sign-in fails due to unconfirmed status, try to handle it
          if (signInError.name === 'UserNotConfirmedException') {
            console.log('🔄 User not confirmed - attempting admin confirmation for Google user...')

            // For Google users, we should bypass the confirmation requirement
            // This is a limitation of the current Cognito setup
            throw new Error('COGNITO_CONFIRMATION_REQUIRED - Google users should be auto-confirmed')
          }

          throw signInError
        }
      }

      console.log('✅ Sign-in result:', {
        isSignedIn: signInResult.isSignedIn,
        nextStep: signInResult.nextStep
      })

      // Handle different sign-in scenarios
      if (!signInResult.isSignedIn) {
        if (signInResult.nextStep) {
          console.log('⚠️ Sign-in requires additional steps:', signInResult.nextStep)

          // Handle specific next steps
          if (signInResult.nextStep.signInStep === 'CONFIRM_SIGN_UP') {
            console.log('⚠️ User still needs confirmation after sign-up')
            console.log('🔄 For Google users, this indicates a Cognito configuration issue')

            // For Google users, if we hit confirmation issues, fall back to profile-only creation
            throw new Error('COGNITO_CONFIRMATION_REQUIRED - Falling back to profile-only creation')
          } else {
            throw new Error(`Sign-in requires additional step: ${signInResult.nextStep.signInStep}`)
          }
        } else {
          throw new Error('Sign-in was not completed successfully and no next step provided')
        }
      } else {
        console.log('✅ User signed in successfully')
      }

      // Step 4: Create user profile using authenticated client
      const profileData = {
        userId: signUpResult.userId || googleUser.id,
        firstName: googleUser.given_name || 'Google',
        lastName: googleUser.family_name || 'User',
        email: googleUser.email,
        profilePhoto: googleUser.picture,
        country: 'India', // Default country
        isVendor: false,
        isAdmin: false,
        isSuperAdmin: false,
        role: 'CUSTOMER',
        registrationSource: 'SOCIAL_LOGIN',
        accountType: 'PERSONAL',
        isVerified: true // Google accounts are pre-verified
      };

      let result
      try {
        // Try authenticated client first
        const authenticatedClient = generateClient({
          authMode: 'userPool'
        });

        result = await authenticatedClient.graphql({
          query: createUserProfile,
          variables: {
            input: profileData
          }
        });
        console.log('✅ Profile created with authenticated client')
      } catch (authError: any) {
        console.error('❌ Authenticated client failed:', authError)
        console.log('🔄 Trying with public client as fallback...')

        // Fallback to public client
        const publicClient = generateClient({
          authMode: 'apiKey'
        });

        result = await publicClient.graphql({
          query: createUserProfile,
          variables: {
            input: profileData
          }
        });
        console.log('✅ Profile created with public client fallback')
      }

      const newUserProfile = result.data?.createUserProfile;

      if (!newUserProfile) {
        throw new Error('Failed to create user profile')
      }

      console.log('✅ User profile created successfully:', newUserProfile.id)

      // Get current authenticated user info from Cognito
      let currentUser
      try {
        currentUser = await getCurrentUser()
        console.log('✅ Current user retrieved:', currentUser.userId)
      } catch (userError: any) {
        console.error('❌ Failed to get current user:', userError)
        // Fallback to using sign-up result data
        currentUser = {
          userId: signUpResult.userId || googleUser.id,
          username: googleUser.email
        }
        console.log('⚠️ Using fallback user data:', currentUser)
      }

      // Create auth session with proper Cognito user data
      const authSession = {
        user: {
          userId: currentUser.userId,
          username: currentUser.username,
          email: googleUser.email,
          signInDetails: {
            loginId: googleUser.email,
            authFlowType: 'GOOGLE_OAUTH'
          }
        },
        userProfile: newUserProfile,
        isAuthenticated: true,
        authProvider: 'google',
        accessToken
      }

      // Store session in localStorage for compatibility
      if (typeof window !== 'undefined') {
        localStorage.setItem('googleAuthSession', JSON.stringify(authSession))
        localStorage.setItem('amplify-google-auth', JSON.stringify({
          isAuthenticated: true,
          user: authSession.user,
          userProfile: authSession.userProfile,
          accessToken: accessToken,
          timestamp: Date.now()
        }))
      }

      console.log('✅ Google user created successfully with Cognito and profile')
      return {
        user: authSession.user,
        userProfile: newUserProfile
      }
    } catch (error: any) {
      console.error('❌ Error creating new Google user:', error)
      console.error('❌ Error details:', {
        message: error.message,
        errors: error.errors,
        graphQLErrors: error.graphQLErrors,
        networkError: error.networkError
      })
      throw new Error('Failed to create user account: ' + (error.message || 'Unknown error'))
    }
  }

  /**
   * Create new Google user with Cognito integration (alternative approach)
   */
  private static async createNewGoogleUser(googleUser: any, accessToken: string): Promise<any> {
    try {
      // Generate a temporary password for Cognito (Google users won't use this)
      const tempPassword = this.generateTemporaryPassword()

      console.log('🔄 Creating new Google user in Cognito:', googleUser.email)

      // Step 1: Create Cognito user account
      const signUpResult = await signUp({
        username: googleUser.email,
        password: tempPassword,
        options: {
          userAttributes: {
            email: googleUser.email,
            given_name: googleUser.given_name || 'Google',
            family_name: googleUser.family_name || 'User',
            picture: googleUser.picture || ''
            // Note: Only using standard attributes available in Cognito
          },
          autoSignIn: {
            enabled: true
          }
        }
      })

      console.log('✅ Cognito user created, now confirming:', signUpResult)

      // Step 2: Auto-confirm the user since Google has already verified the email
      await confirmSignUp({
        username: googleUser.email,
        confirmationCode: '000000' // Dummy code for auto-confirmation
      })

      console.log('✅ Cognito user confirmed, now signing in')

      // Step 3: Sign in the user to Cognito
      const signInResult = await signIn({
        username: googleUser.email,
        password: tempPassword
      })

      console.log('✅ Cognito sign-in successful, creating profile')

      // Step 4: Create user profile in our database
      const newUserProfile = await profileService.createGoogleUserProfile({
        email: googleUser.email,
        firstName: googleUser.given_name || '',
        lastName: googleUser.family_name || '',
        fullName: googleUser.name || '',
        profilePicture: googleUser.picture || '',
        googleId: googleUser.id,
        emailVerified: true,
        authProvider: 'google'
      })

      // Step 5: Create auth session
      const authSession = {
        user: {
          userId: signInResult.userId,
          username: googleUser.email,
          email: googleUser.email,
          signInDetails: {
            loginId: googleUser.email,
            authFlowType: 'GOOGLE_OAUTH'
          }
        },
        userProfile: newUserProfile,
        cognitoUser: signInResult,
        isAuthenticated: true,
        authProvider: 'google'
      }

      // Store auth session
      localStorage.setItem('googleAuthSession', JSON.stringify(authSession))

      console.log('✅ Google user created successfully:', {
        email: googleUser.email,
        cognitoUserId: signInResult.userId,
        profileId: newUserProfile.id
      })

      return {
        user: authSession.user,
        userProfile: newUserProfile,
        cognitoUser: signInResult
      }
    } catch (error: any) {
      console.error('❌ Error creating new Google user:', error)

      // If user already exists in Cognito, try to sign them in
      if (error.name === 'UsernameExistsException') {
        console.log('🔄 User exists in Cognito, attempting sign-in')
        try {
          const signInResult = await signIn({
            username: googleUser.email,
            password: this.generateTemporaryPassword()
          })

          // Find existing profile
          const existingProfile = await this.findUserByEmail(googleUser.email)

          console.log('✅ Existing Google user signed in successfully')

          return {
            user: {
              userId: signInResult.userId,
              username: googleUser.email,
              email: googleUser.email,
              signInDetails: { authFlowType: 'GOOGLE_OAUTH' }
            },
            userProfile: existingProfile,
            cognitoUser: signInResult
          }
        } catch (signInError) {
          console.error('❌ Failed to sign in existing Google user:', signInError)
        }
      }

      throw new Error('Failed to create user account: ' + error.message)
    }
  }

  /**
   * Sign in existing Google user with Cognito
   */
  private static async signInExistingGoogleUser(googleUser: any, existingProfile: any): Promise<any> {
    try {
      console.log('🔄 Skipping Cognito integration for existing Google user:', googleUser.email)
      console.log('✅ Using profile-only authentication for existing user')
      
      // For existing users, skip Cognito and use profile-only authentication
      // This avoids password mismatch issues
      return {
        userId: existingProfile.userId || existingProfile.id,
        username: googleUser.email,
        email: googleUser.email
      }
    } catch (error: any) {
      console.error('❌ Error with existing Google user:', error)
      throw new Error('Failed to authenticate existing Google user: ' + (error.message || 'Unknown error'))
    }
  }

  /**
   * Create Google user profile only (fallback method without Cognito)
   */
  private static async createGoogleUserProfileOnly(googleUser: any, accessToken: string): Promise<any> {
    try {
      console.log('🔄 Creating Google user profile only (no Cognito):', googleUser.email)

      // Use public client to create profile
      const publicClient = generateClient({
        authMode: 'apiKey'
      });

      const profileData = {
        userId: googleUser.id,
        firstName: googleUser.given_name || 'Google',
        lastName: googleUser.family_name || 'User',
        email: googleUser.email,
        profilePhoto: googleUser.picture,
        country: 'India',
        isVendor: false,
        isAdmin: false,
        isSuperAdmin: false,
        role: 'CUSTOMER',
        registrationSource: 'SOCIAL_LOGIN',
        accountType: 'PERSONAL',
        isVerified: true
      };

      const result = await publicClient.graphql({
        query: createUserProfile,
        variables: {
          input: profileData
        }
      });

      const newUserProfile = result.data?.createUserProfile;

      if (!newUserProfile) {
        throw new Error('Failed to create user profile')
      }

      console.log('✅ User profile created successfully (profile-only):', newUserProfile.id)

      // Create simple auth session without Cognito
      const authSession = {
        user: {
          userId: googleUser.id,
          username: googleUser.email,
          email: googleUser.email,
          signInDetails: {
            loginId: googleUser.email,
            authFlowType: 'GOOGLE_OAUTH'
          }
        },
        userProfile: newUserProfile,
        isAuthenticated: true,
        authProvider: 'google',
        accessToken
      }

      // Store session in localStorage
      if (typeof window !== 'undefined') {
        localStorage.setItem('googleAuthSession', JSON.stringify(authSession))
        localStorage.setItem('amplify-google-auth', JSON.stringify({
          isAuthenticated: true,
          user: authSession.user,
          userProfile: authSession.userProfile,
          accessToken: accessToken,
          timestamp: Date.now()
        }))
      }

      console.log('✅ Google user created successfully (profile-only mode)')
      console.log('ℹ️ Note: User is authenticated via Google but not integrated with Cognito')
      console.log('ℹ️ This is a fallback mode that still provides full app functionality')
      return {
        user: authSession.user,
        userProfile: newUserProfile
      }
    } catch (error: any) {
      console.error('❌ Error creating Google user profile:', error)
      throw new Error('Failed to create user profile: ' + (error.message || 'Unknown error'))
    }
  }

  /**
   * Generate temporary password for Cognito (Google users won't use this)
   */
  private static generateTemporaryPassword(): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*'
    let password = ''
    for (let i = 0; i < 12; i++) {
      password += chars.charAt(Math.floor(Math.random() * chars.length))
    }
    return password + 'A1!' // Ensure it meets Cognito requirements
  }

  /**
   * Complete OTP verification for Google signup
   */
  static async completeGoogleSignupOTP(email: string, otpCode: string): Promise<any> {
    try {
      console.log('🔄 Completing OTP verification for Google signup:', email)

      // Get signup data from localStorage
      const signupData = typeof window !== 'undefined' ?
        JSON.parse(localStorage.getItem('googleSignupData') || '{}') : {}

      if (!signupData.cognitoUserId) {
        throw new Error('No signup session found. Please restart the signup process.')
      }

      // Confirm signup with OTP
      await confirmSignUp({
        username: email,
        confirmationCode: otpCode
      })

      console.log('✅ OTP verification successful')

      // Update signup step
      signupData.signupStep = 'password_setup'
      signupData.requiresOTP = false
      signupData.otpVerified = true

      if (typeof window !== 'undefined') {
        localStorage.setItem('googleSignupData', JSON.stringify(signupData))
      }

      return {
        success: true,
        nextStep: 'password_setup',
        message: 'OTP verified successfully. Please set up your password.'
      }
    } catch (error: any) {
      console.error('❌ OTP verification failed:', error)
      return {
        success: false,
        error: error.message || 'OTP verification failed'
      }
    }
  }

  /**
   * Complete password setup for Google signup
   */
  static async completeGoogleSignupPassword(email: string, newPassword: string): Promise<any> {
    try {
      console.log('🔄 Completing password setup for Google signup:', email)

      // Get signup data from localStorage
      const signupData = typeof window !== 'undefined' ?
        JSON.parse(localStorage.getItem('googleSignupData') || '{}') : {}

      if (!signupData.cognitoUserId || !signupData.tempPassword) {
        throw new Error('No signup session found. Please restart the signup process.')
      }

      // Sign in with temporary password first
      const signInResult = await signIn({
        username: email,
        password: signupData.tempPassword
      })

      if (signInResult.nextStep?.signInStep === 'CONFIRM_SIGN_IN_WITH_NEW_PASSWORD_REQUIRED') {
        // Complete the password change
        const { confirmSignIn } = await import('aws-amplify/auth')
        await confirmSignIn({
          challengeResponse: newPassword
        })
        console.log('✅ Password setup successful')
      } else if (signInResult.isSignedIn) {
        // User is signed in, but we still want to update their password
        const { updatePassword } = await import('aws-amplify/auth')
        await updatePassword({
          oldPassword: signupData.tempPassword,
          newPassword: newPassword
        })
        console.log('✅ Password updated successfully')
      }

      // Create user profile now that the user is fully authenticated
      let userProfile = null

      try {
        console.log('🔄 Creating user profile with authenticated user...')

        // Use authenticated client to create profile (user is now the owner)
        const authenticatedClient = generateClient({
          authMode: 'userPool'
        })

        // Update profile data with verification status
        const finalProfileData = {
          ...signupData.profileData,
          isVerified: true // Mark as verified since they completed the flow
        }

        const result = await authenticatedClient.graphql({
          query: createUserProfile,
          variables: {
            input: finalProfileData
          }
        })

        userProfile = result.data?.createUserProfile
        console.log('✅ User profile created successfully:', userProfile?.id)

      } catch (error: any) {
        console.warn('Failed to create user profile with authenticated client:', error)

        // Fallback: try to find existing profile
        try {
          const publicClient = generateClient({
            authMode: 'apiKey'
          })

          const profileResult = await publicClient.graphql({
            query: listUserProfiles,
            variables: {
              filter: {
                email: { eq: email }
              }
            }
          })
          userProfile = profileResult.data?.listUserProfiles?.items?.[0]
          console.log('✅ Found existing user profile')
        } catch (fallbackError) {
          console.warn('Could not find or create user profile:', fallbackError)
        }
      }

      // Create final auth session
      const currentUser = await getCurrentUser()
      const finalAuthSession = {
        user: {
          userId: currentUser.userId,
          username: currentUser.username,
          email: email,
          signInDetails: {
            loginId: email,
            authFlowType: 'GOOGLE_OAUTH'
          }
        },
        userProfile: userProfile,
        isAuthenticated: true,
        authProvider: 'google',
        accessToken: signupData.accessToken,
        signupCompleted: true
      }

      // Update signup data to mark password as set
      signupData.passwordSet = true
      signupData.signupStep = 'completed'

      // Store final session and clean up signup data
      if (typeof window !== 'undefined') {
        localStorage.setItem('googleAuthSession', JSON.stringify(finalAuthSession))
        localStorage.setItem('amplify-google-auth', JSON.stringify({
          isAuthenticated: true,
          user: finalAuthSession.user,
          userProfile: userProfile,
          accessToken: signupData.accessToken,
          timestamp: Date.now()
        }))

        // Clean up temporary signup data
        localStorage.removeItem('googleSignupSession')
        localStorage.removeItem('googleSignupData')
      }

      console.log('✅ Google signup flow completed successfully')

      return {
        success: true,
        user: finalAuthSession.user,
        userProfile: userProfile || signupData.profileData, // Use profile data as fallback
        redirectTo: '/dashboard',
        message: 'Signup completed successfully! Redirecting to dashboard...'
      }
    } catch (error: any) {
      console.error('❌ Password setup failed:', error)
      return {
        success: false,
        error: error.message || 'Password setup failed'
      }
    }
  }

  /**
   * Resend OTP for Google signup
   */
  static async resendGoogleSignupOTP(email: string): Promise<any> {
    try {
      console.log('🔄 Resending OTP for Google signup:', email)

      // Get signup data from localStorage
      const signupData = typeof window !== 'undefined' ?
        JSON.parse(localStorage.getItem('googleSignupData') || '{}') : {}

      if (!signupData.cognitoUserId) {
        throw new Error('No signup session found. Please restart the signup process.')
      }

      // Resend confirmation code
      const { resendSignUpCode } = await import('aws-amplify/auth')
      await resendSignUpCode({
        username: email
      })

      console.log('✅ OTP resent successfully')

      return {
        success: true,
        message: 'Verification code sent successfully'
      }
    } catch (error: any) {
      console.error('❌ Failed to resend OTP:', error)
      return {
        success: false,
        error: error.message || 'Failed to resend verification code'
      }
    }
  }

  /**
   * Get current signup status for Google users
   */
  static getGoogleSignupStatus(): any {
    if (typeof window === 'undefined') return null

    const signupData = localStorage.getItem('googleSignupData')
    if (!signupData) return null

    try {
      return JSON.parse(signupData)
    } catch {
      return null
    }
  }

  /**
   * Check if user exists in Cognito
   */
  private static async checkCognitoUserExists(email: string): Promise<boolean> {
    try {
      // Try to initiate a password reset to check if user exists
      // This is a non-intrusive way to check user existence
      const { resetPassword } = await import('aws-amplify/auth')

      try {
        await resetPassword({ username: email })
        console.log('✅ User exists in Cognito (password reset initiated)')
        return true
      } catch (error: any) {
        if (error.name === 'UserNotFoundException') {
          console.log('❌ User does not exist in Cognito')
          return false
        } else {
          console.log('⚠️ Cannot determine Cognito user existence:', error.name)
          return false // Assume doesn't exist to be safe
        }
      }
    } catch (error) {
      console.error('❌ Error checking Cognito user existence:', error)
      return false
    }
  }

  /**
   * Check current authentication status and provide debugging info
   */
  static async checkAuthStatus(): Promise<any> {
    try {
      console.log('🔍 Checking current authentication status...')

      // Check Cognito authentication
      let cognitoUser = null
      try {
        cognitoUser = await getCurrentUser()
        console.log('✅ Cognito user found:', cognitoUser.userId)
      } catch (cognitoError) {
        console.log('❌ No Cognito user found:', cognitoError.name)
      }

      // Check local storage for Google auth
      let googleAuth = null
      if (typeof window !== 'undefined') {
        const storedAuth = localStorage.getItem('amplify-google-auth')
        if (storedAuth) {
          googleAuth = JSON.parse(storedAuth)
          console.log('✅ Google auth session found in localStorage')
        } else {
          console.log('❌ No Google auth session in localStorage')
        }
      }

      return {
        cognitoUser,
        googleAuth,
        isAuthenticated: !!(cognitoUser || googleAuth),
        authMode: cognitoUser ? 'cognito' : googleAuth ? 'google-only' : 'none'
      }
    } catch (error) {
      console.error('❌ Error checking auth status:', error)
      return {
        cognitoUser: null,
        googleAuth: null,
        isAuthenticated: false,
        authMode: 'none',
        error: error.message
      }
    }
  }

  /**
   * Get current authentication session
   */
  static async getCurrentSession() {
    try {
      // Check for Google auth session first
      const googleSession = localStorage.getItem('googleAuthSession')
      if (googleSession) {
        return JSON.parse(googleSession)
      }

      // Fallback to Amplify session
      const session = await fetchAuthSession()
      return session
    } catch (error) {
      console.error('Error getting current session:', error)
      return null
    }
  }

  /**
   * Sign out user
   */
  static async signOut(): Promise<void> {
    try {
      // Clear Google auth session
      localStorage.removeItem('googleAuthSession')

      // Sign out from Amplify if applicable
      try {
        await signOut()
      } catch (amplifyError) {
        // Ignore Amplify signout errors for Google users
        console.log('Amplify signout not applicable for Google user')
      }
    } catch (error) {
      console.error('Error signing out:', error)
      throw new Error('Failed to sign out')
    }
  }

  /**
   * Check if user is authenticated
   */
  static isAuthenticated(): boolean {
    try {
      const googleSession = localStorage.getItem('googleAuthSession')
      return !!googleSession
    } catch (error) {
      return false
    }
  }
}

/**
 * Hook for Google Authentication with popup
 */
export const useGoogleAuth = () => {
  const [isLoading, setIsLoading] = React.useState(false)
  const [error, setError] = React.useState<string | null>(null)
  const [authResult, setAuthResult] = React.useState<GoogleAuthResult | null>(null)

  const signInWithGoogle = async (): Promise<GoogleAuthResult> => {
    setIsLoading(true)
    setError(null)

    try {
      const result = await GoogleAuthService.signInWithGooglePopup()
      setAuthResult(result)

      if (!result.success) {
        setError(result.error || 'Google sign-in failed')
      }

      return result
    } catch (error: any) {
      const errorResult: GoogleAuthResult = {
        success: false,
        error: error.message || 'Google sign-in failed'
      }
      setError(errorResult.error!)
      setAuthResult(errorResult)
      return errorResult
    } finally {
      setIsLoading(false)
    }
  }

  const signOut = async () => {
    setIsLoading(true)
    setError(null)

    try {
      await GoogleAuthService.signOut()
      setAuthResult(null)
    } catch (error: any) {
      setError(error.message || 'Sign out failed')
    } finally {
      setIsLoading(false)
    }
  }

  const checkAuthStatus = () => {
    return GoogleAuthService.isAuthenticated()
  }

  return {
    signInWithGoogle,
    signOut,
    checkAuthStatus,
    isLoading,
    error,
    authResult,
    isAuthenticated: GoogleAuthService.isAuthenticated()
  }
}

export default GoogleAuthService
