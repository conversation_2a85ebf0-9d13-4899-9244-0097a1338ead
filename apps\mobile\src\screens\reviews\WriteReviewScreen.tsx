import React, { useState } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  ScrollView, 
  TouchableOpacity, 
  Alert,
  Image 
} from 'react-native';
import { RouteProp, useRoute, useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../../providers/ThemeProvider';
import { Header } from '../../components/Header';
import { Card, Input, Button } from '../../components/ui';
import type { AppStackParamList } from '../../navigation/AppNavigator';

type WriteReviewRouteProp = RouteProp<AppStackParamList, 'WriteReview'>;

interface ReviewData {
  rating: number;
  title: string;
  review: string;
  photos: string[];
  recommend: boolean;
}

export default function WriteReviewScreen() {
  const { theme } = useTheme();
  const route = useRoute<WriteReviewRouteProp>();
  const navigation = useNavigation();
  const { vendorId, vendorName, vendorType } = route.params;
  
  const [reviewData, setReviewData] = useState<ReviewData>({
    rating: 0,
    title: '',
    review: '',
    photos: [],
    recommend: true
  });
  const [loading, setLoading] = useState(false);

  const handleRatingPress = (rating: number) => {
    setReviewData(prev => ({ ...prev, rating }));
  };

  const handleInputChange = (field: keyof ReviewData, value: any) => {
    setReviewData(prev => ({ ...prev, [field]: value }));
  };

  const handleSubmitReview = async () => {
    if (reviewData.rating === 0) {
      Alert.alert('Error', 'Please select a rating');
      return;
    }
    
    if (!reviewData.title.trim() || !reviewData.review.trim()) {
      Alert.alert('Error', 'Please fill in all required fields');
      return;
    }

    try {
      setLoading(true);
      // TODO: Replace with actual API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      Alert.alert(
        'Review Submitted!', 
        'Thank you for your review. It will be published after moderation.',
        [{ text: 'OK', onPress: () => navigation.goBack() }]
      );
    } catch (error) {
      Alert.alert('Error', 'Failed to submit review. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const renderStarRating = () => {
    return (
      <View style={styles.starContainer}>
        {[1, 2, 3, 4, 5].map((star) => (
          <TouchableOpacity
            key={star}
            onPress={() => handleRatingPress(star)}
            style={styles.starButton}
          >
            <Ionicons
              name={star <= reviewData.rating ? "star" : "star-outline"}
              size={32}
              color={star <= reviewData.rating ? theme.colors.accent : theme.colors.border}
            />
          </TouchableOpacity>
        ))}
      </View>
    );
  };

  const getRatingText = (rating: number) => {
    switch (rating) {
      case 1: return 'Poor';
      case 2: return 'Fair';
      case 3: return 'Good';
      case 4: return 'Very Good';
      case 5: return 'Excellent';
      default: return 'Select Rating';
    }
  };

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <Header title="Write Review" showBack />
      
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Vendor Info */}
        <Card style={styles.vendorCard}>
          <View style={styles.vendorInfo}>
            <View style={styles.vendorDetails}>
              <Text style={[styles.vendorName, { color: theme.colors.text }]}>
                {vendorName}
              </Text>
              <Text style={[styles.vendorType, { color: theme.colors.textSecondary }]}>
                {vendorType}
              </Text>
            </View>
          </View>
        </Card>

        {/* Rating Section */}
        <Card style={styles.ratingCard}>
          <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
            Overall Rating *
          </Text>
          <Text style={[styles.sectionSubtitle, { color: theme.colors.textSecondary }]}>
            How would you rate your overall experience?
          </Text>
          
          {renderStarRating()}
          
          <Text style={[styles.ratingText, { color: theme.colors.primary }]}>
            {getRatingText(reviewData.rating)}
          </Text>
        </Card>

        {/* Review Form */}
        <Card style={styles.formCard}>
          <View style={styles.formContainer}>
            <Input
              label="Review Title *"
              placeholder="Summarize your experience"
              value={reviewData.title}
              onChangeText={(value) => handleInputChange('title', value)}
              maxLength={100}
            />
            
            <View style={styles.inputContainer}>
              <Text style={[styles.inputLabel, { color: theme.colors.text }]}>
                Your Review *
              </Text>
              <Input
                placeholder="Share details about your experience..."
                value={reviewData.review}
                onChangeText={(value) => handleInputChange('review', value)}
                multiline
                numberOfLines={6}
                style={styles.reviewInput}
                maxLength={1000}
              />
              <Text style={[styles.characterCount, { color: theme.colors.textSecondary }]}>
                {reviewData.review.length}/1000 characters
              </Text>
            </View>
          </View>
        </Card>

        {/* Recommendation */}
        <Card style={styles.recommendCard}>
          <View style={styles.recommendContainer}>
            <View style={styles.recommendInfo}>
              <Text style={[styles.recommendTitle, { color: theme.colors.text }]}>
                Would you recommend this vendor?
              </Text>
              <Text style={[styles.recommendSubtitle, { color: theme.colors.textSecondary }]}>
                Help other couples make informed decisions
              </Text>
            </View>
            
            <View style={styles.recommendButtons}>
              <TouchableOpacity
                style={[
                  styles.recommendButton,
                  reviewData.recommend && styles.recommendButtonActive,
                  { borderColor: theme.colors.border }
                ]}
                onPress={() => handleInputChange('recommend', true)}
              >
                <Ionicons 
                  name="thumbs-up" 
                  size={20} 
                  color={reviewData.recommend ? theme.colors.primary : theme.colors.textSecondary} 
                />
                <Text style={[
                  styles.recommendButtonText,
                  { color: reviewData.recommend ? theme.colors.primary : theme.colors.textSecondary }
                ]}>
                  Yes
                </Text>
              </TouchableOpacity>
              
              <TouchableOpacity
                style={[
                  styles.recommendButton,
                  !reviewData.recommend && styles.recommendButtonActive,
                  { borderColor: theme.colors.border }
                ]}
                onPress={() => handleInputChange('recommend', false)}
              >
                <Ionicons 
                  name="thumbs-down" 
                  size={20} 
                  color={!reviewData.recommend ? theme.colors.destructive : theme.colors.textSecondary} 
                />
                <Text style={[
                  styles.recommendButtonText,
                  { color: !reviewData.recommend ? theme.colors.destructive : theme.colors.textSecondary }
                ]}>
                  No
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </Card>

        {/* Submit Button */}
        <View style={styles.submitContainer}>
          <Button 
            onPress={handleSubmitReview}
            loading={loading}
            style={styles.submitButton}
          >
            Submit Review
          </Button>
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  vendorCard: {
    margin: 16,
    padding: 16,
  },
  vendorInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  vendorDetails: {
    flex: 1,
  },
  vendorName: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  vendorType: {
    fontSize: 14,
    textTransform: 'capitalize',
  },
  ratingCard: {
    marginHorizontal: 16,
    marginBottom: 16,
    padding: 20,
    alignItems: 'center',
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  sectionSubtitle: {
    fontSize: 14,
    textAlign: 'center',
    marginBottom: 20,
  },
  starContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginBottom: 16,
  },
  starButton: {
    padding: 4,
    marginHorizontal: 4,
  },
  ratingText: {
    fontSize: 16,
    fontWeight: '600',
  },
  formCard: {
    marginHorizontal: 16,
    marginBottom: 16,
    padding: 0,
  },
  formContainer: {
    padding: 20,
    gap: 16,
  },
  inputContainer: {
    gap: 8,
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: '500',
  },
  reviewInput: {
    height: 120,
    textAlignVertical: 'top',
  },
  characterCount: {
    fontSize: 12,
    textAlign: 'right',
  },
  recommendCard: {
    marginHorizontal: 16,
    marginBottom: 16,
    padding: 20,
  },
  recommendContainer: {
    gap: 16,
  },
  recommendInfo: {
    alignItems: 'center',
  },
  recommendTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  recommendSubtitle: {
    fontSize: 14,
    textAlign: 'center',
  },
  recommendButtons: {
    flexDirection: 'row',
    justifyContent: 'center',
    gap: 16,
  },
  recommendButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 25,
    borderWidth: 1,
    gap: 8,
  },
  recommendButtonActive: {
    backgroundColor: 'rgba(97, 15, 19, 0.1)',
  },
  recommendButtonText: {
    fontSize: 14,
    fontWeight: '500',
  },
  submitContainer: {
    padding: 16,
    paddingBottom: 32,
  },
  submitButton: {
    paddingVertical: 16,
  },
});
