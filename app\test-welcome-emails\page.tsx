'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Checkbox } from '@/components/ui/checkbox';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  Mail, 
  Send, 
  CheckCircle, 
  AlertCircle, 
  Loader2,
  User,
  Heart
} from 'lucide-react';
import { Header } from "@/components/header";
import { Footer } from "@/components/footer";
import { TopHeader } from "@/components/top-header";
import { useWelcomeEmail } from '@/hooks/useWelcomeEmail';
import { useAuth } from '@/contexts/AuthContext';

export default function TestWelcomeEmailsPage() {
  const { user, userProfile } = useAuth();
  const { loading, error, success, sendNewsletterWelcome, sendUserWelcome, clearMessages } = useWelcomeEmail();
  
  const [emailData, setEmailData] = useState({
    email: userProfile?.email || '',
    firstName: userProfile?.firstName || '',
    lastName: userProfile?.lastName || '',
    type: 'newsletter' as 'newsletter' | 'login' | 'signup',
    isVendor: userProfile?.isVendor || false,
    businessName: userProfile?.businessInfo?.businessName || '',
    interests: [] as string[],
    preferences: {
      weddingTips: true,
      vendorRecommendations: true,
      specialOffers: true,
      eventUpdates: false,
      blogUpdates: false,
      frequency: 'WEEKLY'
    }
  });

  const interestOptions = [
    'PHOTOGRAPHY', 'VIDEOGRAPHY', 'CATERING', 'DECORATION', 'MAKEUP',
    'VENUES', 'SHOPPING', 'PLANNING', 'HONEYMOON', 'JEWELRY',
    'INVITATIONS', 'MUSIC', 'TRANSPORTATION'
  ];

  const handleInputChange = (field: string, value: any) => {
    setEmailData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleInterestToggle = (interest: string) => {
    setEmailData(prev => ({
      ...prev,
      interests: prev.interests.includes(interest)
        ? prev.interests.filter(i => i !== interest)
        : [...prev.interests, interest]
    }));
  };

  const handlePreferenceChange = (preference: string, value: boolean) => {
    setEmailData(prev => ({
      ...prev,
      preferences: {
        ...prev.preferences,
        [preference]: value
      }
    }));
  };

  const handleSendEmail = async () => {
    clearMessages();

    if (!emailData.email) {
      return;
    }

    const emailPayload = {
      email: emailData.email,
      firstName: emailData.firstName,
      lastName: emailData.lastName,
      type: emailData.type,
      isVendor: emailData.isVendor,
      businessName: emailData.businessName,
      interests: emailData.interests,
      preferences: emailData.preferences
    };

    if (emailData.type === 'newsletter') {
      await sendNewsletterWelcome(emailPayload);
    } else {
      await sendUserWelcome(emailPayload);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <TopHeader />
      <Header />
      
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-2xl mx-auto">
          <div className="text-center mb-8">
            <div className="flex items-center justify-center mb-4">
              <Mail className="w-8 h-8 text-primary mr-2" />
              <h1 className="text-3xl font-bold">Welcome Email Test</h1>
            </div>
            <p className="text-gray-600">
              Test the welcome email functionality for newsletter subscriptions and user logins
            </p>
          </div>

          {error && (
            <Alert variant="destructive" className="mb-6">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}
          
          {success && (
            <Alert className="mb-6">
              <CheckCircle className="h-4 w-4" />
              <AlertDescription>
                Welcome email sent successfully! Check the console for details.
              </AlertDescription>
            </Alert>
          )}

          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="w-5 h-5" />
                Email Configuration
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="email">Email Address *</Label>
                  <Input
                    id="email"
                    type="email"
                    value={emailData.email}
                    onChange={(e) => handleInputChange('email', e.target.value)}
                    placeholder="Enter email address"
                    required
                  />
                </div>
                
                <div>
                  <Label htmlFor="type">Email Type</Label>
                  <Select value={emailData.type} onValueChange={(value) => handleInputChange('type', value)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="newsletter">Newsletter Welcome</SelectItem>
                      <SelectItem value="signup">User Signup Welcome</SelectItem>
                      <SelectItem value="login">User Login Welcome</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                <div>
                  <Label htmlFor="firstName">First Name</Label>
                  <Input
                    id="firstName"
                    value={emailData.firstName}
                    onChange={(e) => handleInputChange('firstName', e.target.value)}
                    placeholder="First name"
                  />
                </div>
                
                <div>
                  <Label htmlFor="lastName">Last Name</Label>
                  <Input
                    id="lastName"
                    value={emailData.lastName}
                    onChange={(e) => handleInputChange('lastName', e.target.value)}
                    placeholder="Last name"
                  />
                </div>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="isVendor"
                  checked={emailData.isVendor}
                  onCheckedChange={(checked) => handleInputChange('isVendor', checked as boolean)}
                />
                <Label htmlFor="isVendor">Is Vendor/Business User</Label>
              </div>

              {emailData.isVendor && (
                <div>
                  <Label htmlFor="businessName">Business Name</Label>
                  <Input
                    id="businessName"
                    value={emailData.businessName}
                    onChange={(e) => handleInputChange('businessName', e.target.value)}
                    placeholder="Business name"
                  />
                </div>
              )}
            </CardContent>
          </Card>

          {emailData.type === 'newsletter' && (
            <Card className="mb-6">
              <CardHeader>
                <CardTitle>Newsletter Preferences</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label className="text-base font-medium">Interests</Label>
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-2 mt-2">
                    {interestOptions.map((interest) => (
                      <div key={interest} className="flex items-center space-x-2">
                        <Checkbox
                          id={interest}
                          checked={emailData.interests.includes(interest)}
                          onCheckedChange={() => handleInterestToggle(interest)}
                        />
                        <Label htmlFor={interest} className="text-sm capitalize">
                          {interest.toLowerCase().replace('_', ' ')}
                        </Label>
                      </div>
                    ))}
                  </div>
                </div>

                <div>
                  <Label className="text-base font-medium">Email Preferences</Label>
                  <div className="space-y-2 mt-2">
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="weddingTips"
                        checked={emailData.preferences.weddingTips}
                        onCheckedChange={(checked) => 
                          handlePreferenceChange('weddingTips', checked as boolean)
                        }
                      />
                      <Label htmlFor="weddingTips" className="text-sm">
                        Wedding tips and planning advice
                      </Label>
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="vendorRecommendations"
                        checked={emailData.preferences.vendorRecommendations}
                        onCheckedChange={(checked) => 
                          handlePreferenceChange('vendorRecommendations', checked as boolean)
                        }
                      />
                      <Label htmlFor="vendorRecommendations" className="text-sm">
                        Vendor recommendations and reviews
                      </Label>
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="specialOffers"
                        checked={emailData.preferences.specialOffers}
                        onCheckedChange={(checked) => 
                          handlePreferenceChange('specialOffers', checked as boolean)
                        }
                      />
                      <Label htmlFor="specialOffers" className="text-sm">
                        Special offers and discounts
                      </Label>
                    </div>
                  </div>
                </div>

                <div>
                  <Label htmlFor="frequency">Email Frequency</Label>
                  <Select 
                    value={emailData.preferences.frequency} 
                    onValueChange={(value) => handlePreferenceChange('frequency', value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="DAILY">Daily</SelectItem>
                      <SelectItem value="WEEKLY">Weekly</SelectItem>
                      <SelectItem value="BIWEEKLY">Bi-weekly</SelectItem>
                      <SelectItem value="MONTHLY">Monthly</SelectItem>
                      <SelectItem value="SPECIAL_ONLY">Special offers only</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </CardContent>
            </Card>
          )}

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Send className="w-5 h-5" />
                Send Test Email
              </CardTitle>
            </CardHeader>
            <CardContent>
              <Button 
                onClick={handleSendEmail}
                disabled={loading || !emailData.email}
                className="w-full"
              >
                {loading ? (
                  <>
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    Sending Email...
                  </>
                ) : (
                  <>
                    <Send className="w-4 h-4 mr-2" />
                    Send {emailData.type === 'newsletter' ? 'Newsletter' : 'User'} Welcome Email
                  </>
                )}
              </Button>
              
              <div className="mt-4 p-4 bg-blue-50 rounded-lg">
                <h4 className="font-medium text-blue-900 mb-2">📧 Email Preview</h4>
                <div className="text-sm text-blue-700 space-y-1">
                  <p><strong>To:</strong> {emailData.email}</p>
                  <p><strong>Type:</strong> {emailData.type} welcome email</p>
                  <p><strong>Recipient:</strong> {emailData.firstName} {emailData.lastName} {emailData.isVendor ? '(Vendor)' : '(Customer)'}</p>
                  {emailData.type === 'newsletter' && emailData.interests.length > 0 && (
                    <p><strong>Interests:</strong> {emailData.interests.join(', ')}</p>
                  )}
                </div>
              </div>

              <div className="mt-4 p-4 bg-yellow-50 rounded-lg">
                <h4 className="font-medium text-yellow-900 mb-2">⚠️ Development Note</h4>
                <p className="text-sm text-yellow-700">
                  In development mode, emails are logged to the console instead of being sent. 
                  Configure email providers (AWS SES, SendGrid, or SMTP) in environment variables to send actual emails.
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      <Footer />
    </div>
  );
}
