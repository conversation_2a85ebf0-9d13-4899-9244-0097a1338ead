'use client';

import React, { useState, useEffect } from 'react';

export default function TestWelcomeEmailsPage() {
  const [emailData, setEmailData] = useState({
    email: '',
    firstName: '',
    lastName: '',
    type: 'newsletter' as 'newsletter' | 'login' | 'signup',
    isVendor: false,
    businessName: '',
    interests: [] as string[],
    preferences: {
      weddingTips: true,
      vendorRecommendations: true,
      specialOffers: true,
      eventUpdates: false,
      blogUpdates: false,
      frequency: 'WEEKLY'
    }
  });
  
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [useWelcomeEmail, setUseWelcomeEmail] = useState<any>(null);
  const [useAuth, setUseAuth] = useState<any>(null);

  // Dynamically import hooks to avoid SSR issues
  useEffect(() => {
    const loadHooks = async () => {
      try {
        const [welcomeEmailHook, authHook] = await Promise.all([
          import('@/hooks/useWelcomeEmail'),
          import('@/contexts/AuthContext')
        ]);
        
        setUseWelcomeEmail(welcomeEmailHook.useWelcomeEmail);
        setUseAuth(authHook.useAuth);
      } catch (err) {
        console.error('Failed to load hooks:', err);
        setError('Failed to load page components. Please refresh.');
      }
    };
    
    loadHooks();
  }, []);

  const interestOptions = [
    'PHOTOGRAPHY', 'VIDEOGRAPHY', 'CATERING', 'DECORATION', 'MAKEUP',
    'VENUES', 'SHOPPING', 'PLANNING', 'HONEYMOON', 'JEWELRY',
    'INVITATIONS', 'MUSIC', 'TRANSPORTATION'
  ];

  const handleInputChange = (field: string, value: any) => {
    setEmailData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleInterestToggle = (interest: string) => {
    setEmailData(prev => ({
      ...prev,
      interests: prev.interests.includes(interest)
        ? prev.interests.filter(i => i !== interest)
        : [...prev.interests, interest]
    }));
  };

  const handlePreferenceChange = (preference: string, value: any) => {
    setEmailData(prev => ({
      ...prev,
      preferences: {
        ...prev.preferences,
        [preference]: value
      }
    }));
  };

  const handleSendEmail = async () => {
    setLoading(true);
    setError(null);
    setSuccess(null);

    if (!emailData.email) {
      setLoading(false);
      setError('Email address is required.');
      return;
    }

    const emailPayload = {
      email: emailData.email,
      firstName: emailData.firstName,
      lastName: emailData.lastName,
      type: emailData.type,
      isVendor: emailData.isVendor,
      businessName: emailData.businessName,
      interests: emailData.interests,
      preferences: emailData.preferences
    };

    try {
      if (emailData.type === 'newsletter') {
        await useWelcomeEmail?.sendNewsletterWelcome(emailPayload);
      } else {
        await useWelcomeEmail?.sendUserWelcome(emailPayload);
      }
      setSuccess('Welcome email sent successfully!');
    } catch (err: any) {
      // Handle specific error types
      if (err.message?.includes('Not Authorized')) {
        setError('Authorization error: You need to be logged in to send emails. Please log in and try again.');
      } else if (err.message?.includes('Variable') && err.message?.includes('invalid value')) {
        setError('Invalid data error: Please check your email format and form data. Make sure all required fields are filled.');
      } else {
        setError(err.message || 'Failed to send welcome email. Please check your connection and try again.');
      }
    } finally {
      setLoading(false);
    }
  };

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="text-red-500 text-6xl mb-4">⚠️</div>
          <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
            <p className="text-red-800">{error}</p>
          </div>
          <button 
            onClick={() => window.location.reload()} 
            className="w-full px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
          >
            Refresh Page
          </button>
        </div>
      </div>
    );
  }

  if (!useWelcomeEmail || !useAuth) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="text-blue-500 text-6xl mb-4 animate-spin">⏳</div>
          <p className="text-gray-600">Loading welcome email test page...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-2xl mx-auto">
          <div className="text-center mb-8">
            <div className="flex items-center justify-center mb-4">
              <div className="text-blue-600 text-4xl mr-2">📧</div>
              <h1 className="text-3xl font-bold">Welcome Email Test</h1>
            </div>
            <p className="text-gray-600">
              Test the welcome email functionality for newsletter subscriptions and user logins
            </p>
          </div>

          {error && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
              <div className="flex items-center">
                <span className="text-red-600 mr-2">⚠️</span>
                <p className="text-red-800">{error}</p>
              </div>
            </div>
          )}
          
          {success && (
            <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
              <div className="flex items-center">
                <span className="text-green-600 mr-2">✅</span>
                <p className="text-green-800">{success}</p>
              </div>
            </div>
          )}

          <div className="bg-white rounded-lg shadow-lg p-6 mb-6">
            <h2 className="text-xl font-semibold mb-4">Email Configuration</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
              <div>
                <label className="block text-sm font-medium mb-2">Email Address *</label>
                <input
                  type="email"
                  value={emailData.email}
                  onChange={(e) => handleInputChange('email', e.target.value)}
                  placeholder="Enter email address"
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-2">Email Type</label>
                <select 
                  value={emailData.type} 
                  onChange={(e) => handleInputChange('type', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="newsletter">Newsletter Welcome</option>
                  <option value="signup">User Signup Welcome</option>
                  <option value="login">User Login Welcome</option>
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-2">First Name</label>
                <input
                  type="text"
                  value={emailData.firstName}
                  onChange={(e) => handleInputChange('firstName', e.target.value)}
                  placeholder="First name"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-2">Last Name</label>
                <input
                  type="text"
                  value={emailData.lastName}
                  onChange={(e) => handleInputChange('lastName', e.target.value)}
                  placeholder="Last name"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </div>

            <div className="flex items-center space-x-2 mb-4">
              <input
                type="checkbox"
                id="isVendor"
                checked={emailData.isVendor}
                onChange={(e) => handleInputChange('isVendor', e.target.checked)}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <label htmlFor="isVendor">Is Vendor/Business User</label>
            </div>

            {emailData.isVendor && (
              <div className="mb-4">
                <label className="block text-sm font-medium mb-2">Business Name</label>
                <input
                  type="text"
                  value={emailData.businessName}
                  onChange={(e) => handleInputChange('businessName', e.target.value)}
                  placeholder="Business name"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            )}
          </div>

          {emailData.type === 'newsletter' && (
            <div className="bg-white rounded-lg shadow-lg p-6 mb-6">
              <h2 className="text-xl font-semibold mb-4">Newsletter Preferences</h2>
              <div className="mb-6">
                <label className="block text-base font-medium mb-2">Interests</label>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                  {interestOptions.map((interest) => (
                    <div key={interest} className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        id={interest}
                        checked={emailData.interests.includes(interest)}
                        onChange={() => handleInterestToggle(interest)}
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                      <label htmlFor={interest} className="text-sm capitalize">
                        {interest.toLowerCase().replace('_', ' ')}
                      </label>
                    </div>
                  ))}
                </div>
              </div>

              <div className="mb-6">
                <label className="block text-base font-medium mb-2">Email Preferences</label>
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="weddingTips"
                      checked={emailData.preferences.weddingTips}
                      onChange={(e) => handlePreferenceChange('weddingTips', e.target.checked)}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <label htmlFor="weddingTips" className="text-sm">
                      Wedding tips and planning advice
                    </label>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="vendorRecommendations"
                      checked={emailData.preferences.vendorRecommendations}
                      onChange={(e) => handlePreferenceChange('vendorRecommendations', e.target.checked)}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <label htmlFor="vendorRecommendations" className="text-sm">
                      Vendor recommendations and reviews
                    </label>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="specialOffers"
                      checked={emailData.preferences.specialOffers}
                      onChange={(e) => handlePreferenceChange('specialOffers', e.target.checked)}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <label htmlFor="specialOffers" className="text-sm">
                      Special offers and discounts
                    </label>
                  </div>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">Email Frequency</label>
                <select 
                  value={emailData.preferences.frequency} 
                  onChange={(e) => handlePreferenceChange('frequency', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="DAILY">Daily</option>
                  <option value="WEEKLY">Weekly</option>
                  <option value="BIWEEKLY">Bi-weekly</option>
                  <option value="MONTHLY">Monthly</option>
                  <option value="SPECIAL_ONLY">Special offers only</option>
                </select>
              </div>
            </div>
          )}

          <div className="bg-white rounded-lg shadow-lg p-6">
            <h2 className="text-xl font-semibold mb-4">Send Test Email</h2>
            <button 
              onClick={handleSendEmail}
              disabled={loading || !emailData.email}
              className="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {loading ? (
                <>
                  <span className="animate-spin mr-2">⏳</span>
                  Sending Email...
                </>
              ) : (
                <>
                  <span className="mr-2">📧</span>
                  Send {emailData.type === 'newsletter' ? 'Newsletter' : 'User'} Welcome Email
                </>
              )}
            </button>
            
            <div className="mt-4 p-4 bg-blue-50 rounded-lg">
              <h4 className="font-medium text-blue-900 mb-2">📧 Email Preview</h4>
              <div className="text-sm text-blue-700 space-y-1">
                <p><strong>To:</strong> {emailData.email}</p>
                <p><strong>Type:</strong> {emailData.type} welcome email</p>
                <p><strong>Recipient:</strong> {emailData.firstName} {emailData.lastName} {emailData.isVendor ? '(Vendor)' : '(Customer)'}</p>
                {emailData.type === 'newsletter' && emailData.interests.length > 0 && (
                  <p><strong>Interests:</strong> {emailData.interests.join(', ')}</p>
                )}
              </div>
            </div>

            <div className="mt-4 p-4 bg-yellow-50 rounded-lg">
              <h4 className="font-medium text-yellow-900 mb-2">⚠️ Development Note</h4>
              <p className="text-sm text-yellow-700">
                In development mode, emails are logged to the console instead of being sent. 
                Configure email providers (AWS SES, SendGrid, or SMTP) in environment variables to send actual emails.
              </p>
              
              <div className="mt-3 p-3 bg-blue-50 border border-blue-200 rounded">
                <p className="text-sm text-blue-800">
                  <strong>Authentication Required:</strong> You must be logged in to test emails. 
                  If you see authorization errors, please log in first or use the command-line test scripts.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
