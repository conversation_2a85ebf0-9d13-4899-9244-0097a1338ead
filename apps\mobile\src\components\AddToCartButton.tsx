import React, { useState, useEffect } from 'react';
import { View, Text, Alert } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Button } from './ui/Button';
import { useAuth } from '../providers/AuthProvider';
import { useCart } from '../providers/CartProvider';
import { useNavigation } from '@react-navigation/native';
import { useTheme } from '../providers/ThemeProvider';

interface AddToCartButtonProps {
  productId: string;
  productData: {
    name: string;
    image?: string;
    price: number;
    originalPrice?: number;
    discount?: number;
    brand?: string;
    category?: string;
    description?: string;
  };
  variant?: 'default' | 'outline' | 'secondary';
  size?: 'default' | 'sm' | 'lg';
  showText?: boolean;
  showQuantityControls?: boolean;
  initialQuantity?: number;
  onAddToCart?: (quantity: number) => void;
}

export function AddToCartButton({
  productId,
  productData,
  variant = 'default',
  size = 'default',
  showText = true,
  showQuantityControls = false,
  initialQuantity = 1,
  onAddToCart,
}: AddToCartButtonProps) {
  const { isAuthenticated } = useAuth();
  const { addItem, updateQuantity, removeItem, isInCart, getItemQuantity } = useCart();
  const navigation = useNavigation();
  const { theme } = useTheme();
  const [loading, setLoading] = useState(false);
  const [quantity, setQuantity] = useState(initialQuantity);
  const [justAdded, setJustAdded] = useState(false);

  const isProductInCart = isInCart(productId);
  const currentQuantity = getItemQuantity(productId);

  useEffect(() => {
    if (isProductInCart) {
      setQuantity(currentQuantity);
    }
  }, [isProductInCart, currentQuantity]);

  const handleAddToCart = async () => {
    if (!isAuthenticated) {
      Alert.alert(
        'Login Required',
        'Please login to add items to your cart.',
        [
          { text: 'Cancel', style: 'cancel' },
          { 
            text: 'Login', 
            onPress: () => navigation.navigate('Login' as never)
          },
        ]
      );
      return;
    }

    try {
      setLoading(true);

      await addItem({
        id: productId,
        name: productData.name,
        price: productData.price,
        quantity: quantity,
        image: productData.image || '',
        vendorName: productData.brand || 'Unknown Brand',
        category: productData.category || 'General',
      });

      setJustAdded(true);
      setTimeout(() => setJustAdded(false), 2000);
      
      onAddToCart?.(quantity);
      
      Alert.alert('Success', 'Product added to cart!');
    } catch (error) {
      console.error('Error adding to cart:', error);
      Alert.alert('Error', 'Failed to add product to cart. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleUpdateQuantity = async (newQuantity: number) => {
    if (newQuantity <= 0) {
      await removeItem(productId);
      setQuantity(initialQuantity);
      return;
    }

    try {
      await updateQuantity(productId, newQuantity);
      setQuantity(newQuantity);
    } catch (error) {
      console.error('Error updating quantity:', error);
      Alert.alert('Error', 'Failed to update quantity. Please try again.');
    }
  };

  const handleIncrement = () => {
    const newQuantity = quantity + 1;
    if (isProductInCart) {
      handleUpdateQuantity(newQuantity);
    } else {
      setQuantity(newQuantity);
    }
  };

  const handleDecrement = () => {
    const newQuantity = quantity - 1;
    if (isProductInCart) {
      handleUpdateQuantity(newQuantity);
    } else if (newQuantity > 0) {
      setQuantity(newQuantity);
    }
  };

  const getButtonText = () => {
    if (justAdded) return 'Added!';
    if (isProductInCart) return 'In Cart';
    return 'Add to Cart';
  };

  const getButtonIcon = () => {
    if (justAdded) return 'checkmark';
    if (isProductInCart) return 'bag-check';
    return 'bag-add';
  };

  if (showQuantityControls && (isProductInCart || quantity > 1)) {
    return (
      <View style={{ flexDirection: 'row', alignItems: 'center', gap: 8 }}>
        <Button
          onPress={handleDecrement}
          variant="outline"
          size="icon"
          icon="remove"
        />
        
        <Text style={{ 
          fontSize: 16, 
          fontWeight: '600', 
          color: theme.colors.text,
          minWidth: 30,
          textAlign: 'center'
        }}>
          {isProductInCart ? currentQuantity : quantity}
        </Text>
        
        <Button
          onPress={handleIncrement}
          variant="outline"
          size="icon"
          icon="add"
        />
        
        {!isProductInCart && (
          <Button
            title="Add to Cart"
            onPress={handleAddToCart}
            variant={variant}
            size={size}
            disabled={loading}
            loading={loading}
            icon="bag-add"
            iconPosition="left"
          />
        )}
      </View>
    );
  }

  return (
    <Button
      title={showText ? getButtonText() : undefined}
      onPress={isProductInCart ? () => navigation.navigate('Cart' as never) : handleAddToCart}
      variant={isProductInCart ? 'outline' : variant}
      size={size}
      disabled={loading}
      loading={loading}
      icon={getButtonIcon()}
      iconPosition="left"
    />
  );
}
