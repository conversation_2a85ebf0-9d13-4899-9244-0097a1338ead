@tailwind base;
@tailwind components;
@tailwind utilities;

@layer utilities {
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  .animate-spin-slow {
    animation: spin 3s linear infinite;
  }

  @keyframes spin {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 357 70% 21%; /* maroon #610f13 */
    --primary-foreground: 36 53% 98%;
    --secondary: 90 34% 45%; /* leaf green */
    --secondary-foreground: 36 53% 98%;
    --muted: 36 53% 85%;
    --muted-foreground: 25 60% 40%;
    --accent: 44 89% 62%; /* gold #F6C244 */
    --accent-foreground: 25 60% 20%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 36 53% 98%;
    --border: 36 53% 80%;
    --input: 36 53% 80%;
    --ring: 347 69% 34%;
    --radius: 0.5rem;
    --chart-1: 347 69% 34%; /* maroon */
    --chart-2: 90 34% 45%;
    --chart-3: 25 60% 40%;
    --chart-4: 36 53% 91%;
    --chart-5: 44 89% 62%; /* gold */
  }

  .dark {
    --background: 25 60% 20%;
    --foreground: 36 53% 98%;
    --card: 25 60% 25%;
    --card-foreground: 36 53% 98%;
    --popover: 25 60% 25%;
    --popover-foreground: 36 53% 98%;
    --primary: 347 69% 34%; /* maroon #9B1C31 */
    --primary-foreground: 36 53% 98%;
    --secondary: 90 34% 30%;
    --secondary-foreground: 36 53% 98%;
    --muted: 25 60% 25%;
    --muted-foreground: 36 53% 91%;
    --accent: 44 89% 62%; /* gold #F6C244 */
    --accent-foreground: 25 60% 20%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 36 53% 98%;
    --border: 25 60% 25%;
    --input: 25 60% 25%;
    --ring: 347 69% 34%;
    --chart-1: 347 69% 34%; /* maroon */
    --chart-2: 90 34% 45%;
    --chart-3: 25 60% 40%;
    --chart-4: 36 53% 91%;
    --chart-5: 44 89% 62%; /* gold */
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

@layer utilities {
  .line-clamp-2 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
  }

  .line-clamp-3 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
  }

  /* Add smooth scale and shadow for card images on hover */
  .group:hover .group-hover\:scale-105 {
    transform: scale(1.05);
  }
  .group:hover .group-hover\:-translate-y-1 {
    transform: translateY(-0.25rem);
  }
  .group:hover .group-hover\:shadow-lg {
    box-shadow: 0 10px 15px -3px rgba(0,0,0,0.1), 0 4px 6px -4px rgba(0,0,0,0.1);
  }

  /* PWA and Mobile Optimizations */
  .safe-area-pb {
    padding-bottom: env(safe-area-inset-bottom);
  }

  .safe-area-pt {
    padding-top: env(safe-area-inset-top);
  }

  .safe-area-pl {
    padding-left: env(safe-area-inset-left);
  }

  .safe-area-pr {
    padding-right: env(safe-area-inset-right);
  }

  /* Mobile-first responsive design */
  @media (max-width: 768px) {
    body {
      padding-bottom: 80px; /* Space for bottom navigation */
    }

    /* Optimize touch targets */
    button, a, input, select, textarea {
      min-height: 44px;
    }

    /* Improve scrolling on iOS */
    * {
      -webkit-overflow-scrolling: touch;
    }

    /* Prevent zoom on input focus */
    input, select, textarea {
      font-size: 16px;
    }

    /* Mobile hamburger menu positioning */
    .mobile-hamburger {
      position: fixed;
      left: 16px;
      top: 50%;
      transform: translateY(-50%);
      z-index: 1000;
    }

    /* Mobile header viewport containment */
    .MuiToolbar-root {
      max-width: 100vw;
      overflow: hidden;
      box-sizing: border-box;
    }

    /* Ensure mobile header elements stay within viewport */
    @media (max-width: 1024px) {
      .MuiToolbar-root {
        padding-left: 8px !important;
        padding-right: 8px !important;
        gap: 4px;
      }

      /* Mobile header layout fixes */
      .mobile-header-container {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
        max-width: 100vw;
        overflow: hidden;
        box-sizing: border-box;
      }

      /* Hamburger button container */
      .mobile-hamburger-container {
        flex-shrink: 0;
        min-width: 48px;
        max-width: 48px;
      }

      /* Logo container with proper constraints */
      .mobile-logo-container {
        flex: 1;
        min-width: 0;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        overflow: hidden;
      }

      /* Login button container */
      .mobile-login-container {
        flex-shrink: 0;
        min-width: 60px;
        max-width: 80px;
        display: flex;
        justify-content: flex-end;
      }
    }

    /* Extra small screens (iPhone SE, etc.) */
    @media (max-width: 375px) {
      .MuiToolbar-root {
        padding-left: 4px !important;
        padding-right: 4px !important;
        gap: 2px;
      }

      /* .mobile-logo-container img {
        max-width: 120px !important;
      } */

      .mobile-login-container {
        min-width: 50px;
        max-width: 70px;
      }

      .mobile-login-container a {
        padding-left: 6px !important;
        padding-right: 6px !important;
        font-size: 10px !important;
      }
    }
  }

  /* Hamburger menu slide animation */
  @keyframes slide-in-left {
    from {
      transform: translateX(-100%);
    }
    to {
      transform: translateX(0);
    }
  }

  @keyframes slide-out-left {
    from {
      transform: translateX(0);
    }
    to {
      transform: translateX(-100%);
    }
  }

  .animate-slide-in-left {
    animation: slide-in-left 0.3s ease-out;
  }

  .animate-slide-out-left {
    animation: slide-out-left 0.3s ease-in;
  }

  /* PWA display mode styles */
  @media (display-mode: standalone) {
    body {
      user-select: none;
      -webkit-user-select: none;
      padding-top: env(safe-area-inset-top);
      padding-bottom: env(safe-area-inset-bottom);
    }

    /* Hide browser UI elements in standalone mode */
    .browser-only {
      display: none;
    }

    /* Better scrollbar for PWA */
    ::-webkit-scrollbar {
      width: 0px;
      background: transparent;
    }

    /* Improve touch responsiveness */
    button, a, input, select, textarea {
      touch-action: manipulation;
    }

    /* PWA-specific bottom navigation spacing */
    .pwa-bottom-nav {
      padding-bottom: max(1rem, env(safe-area-inset-bottom));
    }
  }

  /* Additional PWA utilities */
  .pb-safe {
    padding-bottom: max(1rem, env(safe-area-inset-bottom));
  }

  .pt-safe {
    padding-top: max(1rem, env(safe-area-inset-top));
  }

  /* Hide footer in PWA mode */
  @media (display-mode: standalone) {
    .pwa-hide {
      display: none !important;
    }
  }
}
