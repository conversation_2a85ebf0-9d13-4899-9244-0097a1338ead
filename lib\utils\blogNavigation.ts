/**
 * Smart blog navigation utility
 * Redirects users to the appropriate blog management page based on their role
 */

export interface UserContext {
  isAdmin?: boolean
  isSuperAdmin?: boolean
  isVendor?: boolean
  userType?: string
  userProfile?: any
}

/**
 * Get the appropriate blog dashboard URL based on user type
 */
export function getBlogDashboardUrl(userContext: UserContext): string {
  const { isAdmin, isSuperAdmin, userType, userProfile } = userContext

  // Check for super admin (highest priority)
  if (isSuperAdmin || userType === 'super_admin' || userProfile?.isSuperAdmin) {
    return '/dashboard/admin-blogs'
  }

  // Check for admin
  if (isAdmin || userType === 'admin' || userProfile?.isAdmin) {
    return '/dashboard/admin-blogs'
  }

  // For vendors and regular users, use the standard blog dashboard
  return '/dashboard/blogs'
}

/**
 * Navigate to the appropriate blog dashboard based on user type
 */
export function navigateToBlogDashboard(router: any, userContext: UserContext): void {
  const url = getBlogDashboardUrl(userContext)
  router.push(url)
}

/**
 * Get the appropriate blog dashboard link for static navigation
 */
export function getBlogDashboardLink(userContext: UserContext): string {
  return getBlogDashboardUrl(userContext)
}

/**
 * Get user-friendly label for the blog dashboard
 */
export function getBlogDashboardLabel(userContext: UserContext): string {
  const { isAdmin, isSuperAdmin, userType, userProfile } = userContext

  // Check for super admin or admin
  if (isSuperAdmin || userType === 'super_admin' || userProfile?.isSuperAdmin ||
      isAdmin || userType === 'admin' || userProfile?.isAdmin) {
    return 'Back to Blog Management'
  }

  // For vendors and regular users
  return 'Back to My Blogs'
}
