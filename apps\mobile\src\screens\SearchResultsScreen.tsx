import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Image,
  TextInput,
  ActivityIndicator,
  Alert,
} from 'react-native';
import { RouteProp, useRoute, useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../providers/ThemeProvider';
import { useSearch } from '../providers/SearchProvider';
import { useFavorites } from '../providers/FavoritesProvider';
import { graphqlService } from '../services/graphqlService';
import { AppStackParamList } from '../navigation/AppNavigator';

type SearchResultsRouteProp = RouteProp<AppStackParamList, 'SearchResults'>;

interface SearchResult {
  id: string;
  name: string;
  type: 'vendor' | 'venue' | 'product';
  image?: string;
  rating?: number;
  reviewCount?: number;
  price?: string;
  location?: string;
  category?: string;
  description?: string;
}

export default function SearchResultsScreen() {
  const route = useRoute<SearchResultsRouteProp>();
  const navigation = useNavigation();
  const { theme } = useTheme();
  const { searchQuery, setSearchQuery, searchFilters, setSearchFilters } = useSearch();
  const { isFavorite, addToFavorites, removeFromFavorites } = useFavorites();
  
  const { query, type } = route.params;
  
  const [results, setResults] = useState<SearchResult[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchText, setSearchText] = useState(query);
  const [selectedType, setSelectedType] = useState<'all' | 'vendor' | 'venue' | 'product'>(type || 'all');
  const [sortBy, setSortBy] = useState<'relevance' | 'rating' | 'price'>('relevance');

  useEffect(() => {
    performSearch();
  }, [query, selectedType, sortBy]);

  const performSearch = async () => {
    try {
      setLoading(true);
      const searchResults: SearchResult[] = [];

      // Search vendors
      if (selectedType === 'all' || selectedType === 'vendor') {
        const vendorFilter = {
          or: [
            { name: { contains: query } },
            { description: { contains: query } },
            { category: { contains: query } },
            { services: { contains: query } }
          ]
        };
        
        const vendorsResult = await graphqlService.listVendors(vendorFilter, 20);
        const vendorResults = vendorsResult.items.map(vendor => ({
          id: vendor.id,
          name: vendor.name,
          type: 'vendor' as const,
          image: vendor.profilePhoto,
          rating: vendor.rating,
          reviewCount: vendor.reviewCount,
          price: vendor.priceRange,
          location: `${vendor.city}, ${vendor.state}`,
          category: vendor.category,
          description: vendor.description,
        }));
        searchResults.push(...vendorResults);
      }

      // Search venues
      if (selectedType === 'all' || selectedType === 'venue') {
        const venueFilter = {
          or: [
            { name: { contains: query } },
            { description: { contains: query } },
            { type: { contains: query } },
            { city: { contains: query } }
          ]
        };
        
        const venuesResult = await graphqlService.listVenues(venueFilter, 20);
        const venueResults = venuesResult.items.map(venue => ({
          id: venue.id,
          name: venue.name,
          type: 'venue' as const,
          image: venue.images?.[0],
          rating: venue.rating,
          reviewCount: venue.reviewCount,
          price: venue.priceRange,
          location: `${venue.city}, ${venue.state}`,
          category: venue.type,
          description: venue.description,
        }));
        searchResults.push(...venueResults);
      }

      // Search products
      if (selectedType === 'all' || selectedType === 'product') {
        const productFilter = {
          or: [
            { name: { contains: query } },
            { description: { contains: query } },
            { category: { contains: query } },
            { brand: { contains: query } }
          ]
        };
        
        const productsResult = await graphqlService.listShops(productFilter, 20);
        const productResults = productsResult.items.map(product => ({
          id: product.id,
          name: product.name,
          type: 'product' as const,
          image: product.images?.[0],
          rating: product.rating,
          reviewCount: product.reviewCount,
          price: `₹${product.price}`,
          category: product.category,
          description: product.description,
        }));
        searchResults.push(...productResults);
      }

      // Sort results
      const sortedResults = sortResults(searchResults);
      setResults(sortedResults);

    } catch (error) {
      console.error('Error performing search:', error);
      Alert.alert('Error', 'Failed to search. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const sortResults = (results: SearchResult[]) => {
    switch (sortBy) {
      case 'rating':
        return results.sort((a, b) => (b.rating || 0) - (a.rating || 0));
      case 'price':
        return results.sort((a, b) => {
          const priceA = extractPrice(a.price);
          const priceB = extractPrice(b.price);
          return priceA - priceB;
        });
      default:
        return results; // Keep original order for relevance
    }
  };

  const extractPrice = (priceString?: string): number => {
    if (!priceString) return 0;
    const match = priceString.match(/[\d,]+/);
    return match ? parseInt(match[0].replace(/,/g, '')) : 0;
  };

  const handleSearch = () => {
    if (searchText.trim()) {
      setSearchQuery(searchText);
      navigation.setParams({ query: searchText });
    }
  };

  const handleResultPress = (result: SearchResult) => {
    switch (result.type) {
      case 'vendor':
        navigation.navigate('VendorDetail', { vendorId: result.id });
        break;
      case 'venue':
        navigation.navigate('VenueDetail', { venueId: result.id });
        break;
      case 'product':
        navigation.navigate('ProductDetail', { productId: result.id });
        break;
    }
  };

  const handleFavoriteToggle = async (result: SearchResult) => {
    if (isFavorite(result.id, result.type)) {
      await removeFromFavorites(result.id, result.type);
    } else {
      await addToFavorites({
        id: result.id,
        type: result.type,
        name: result.name,
        image: result.image,
        rating: result.rating,
        price: result.price,
        location: result.location,
      });
    }
  };

  const renderResult = ({ item }: { item: SearchResult }) => (
    <TouchableOpacity
      style={[styles.resultCard, { backgroundColor: theme.colors.surface }]}
      onPress={() => handleResultPress(item)}
    >
      <Image
        source={{ uri: item.image || '/placeholder-image.jpg
        style={styles.resultImage}
        resizeMode="cover"
      />
      
      <View style={styles.resultContent}>
        <View style={styles.resultHeader}>
          <Text style={[styles.resultName, { color: theme.colors.text }]} numberOfLines={1}>
            {item.name}
          </Text>
          <TouchableOpacity onPress={() => handleFavoriteToggle(item)}>
            <Ionicons
              name={isFavorite(item.id, item.type) ? 'heart' : 'heart-outline'}
              size={20}
              color={isFavorite(item.id, item.type) ? theme.colors.error : theme.colors.textSecondary}
            />
          </TouchableOpacity>
        </View>
        
        <Text style={[styles.resultCategory, { color: theme.colors.textSecondary }]}>
          {item.category}
        </Text>
        
        {item.location && (
          <Text style={[styles.resultLocation, { color: theme.colors.textSecondary }]}>
            {item.location}
          </Text>
        )}
        
        <View style={styles.resultFooter}>
          <View style={styles.ratingContainer}>
            {item.rating && (
              <>
                <Ionicons name="star" size={14} color="#F59E0B" />
                <Text style={[styles.rating, { color: theme.colors.text }]}>
                  {item.rating.toFixed(1)}
                </Text>
                {item.reviewCount && (
                  <Text style={[styles.reviewCount, { color: theme.colors.textSecondary }]}>
                    ({item.reviewCount})
                  </Text>
                )}
              </>
            )}
          </View>
          
          {item.price && (
            <Text style={[styles.price, { color: theme.colors.primary }]}>
              {item.price}
            </Text>
          )}
        </View>
      </View>
    </TouchableOpacity>
  );

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {/* Search Header */}
      <View style={[styles.searchHeader, { backgroundColor: theme.colors.surface }]}>
        <View style={styles.searchBar}>
          <Ionicons name="search-outline" size={20} color={theme.colors.textSecondary} />
          <TextInput
            style={[styles.searchInput, { color: theme.colors.text }]}
            placeholder="Search..."
            placeholderTextColor={theme.colors.textSecondary}
            value={searchText}
            onChangeText={setSearchText}
            onSubmitEditing={handleSearch}
            returnKeyType="search"
          />
        </View>
      </View>

      {/* Filter Tabs */}
      <View style={styles.filterTabs}>
        {['all', 'vendor', 'venue', 'product'].map((filterType) => (
          <TouchableOpacity
            key={filterType}
            style={[
              styles.filterTab,
              {
                backgroundColor: selectedType === filterType
                  ? theme.colors.primary
                  : theme.colors.surface
              }
            ]}
            onPress={() => setSelectedType(filterType as any)}
          >
            <Text style={[
              styles.filterTabText,
              {
                color: selectedType === filterType
                  ? '#fff'
                  : theme.colors.text
              }
            ]}>
              {filterType.charAt(0).toUpperCase() + filterType.slice(1)}
            </Text>
          </TouchableOpacity>
        ))}
      </View>

      {/* Sort Options */}
      <View style={styles.sortContainer}>
        <Text style={[styles.sortLabel, { color: theme.colors.text }]}>Sort by:</Text>
        <View style={styles.sortOptions}>
          {['relevance', 'rating', 'price'].map((option) => (
            <TouchableOpacity
              key={option}
              style={[
                styles.sortOption,
                {
                  backgroundColor: sortBy === option
                    ? theme.colors.primary
                    : theme.colors.surface
                }
              ]}
              onPress={() => setSortBy(option as any)}
            >
              <Text style={[
                styles.sortOptionText,
                {
                  color: sortBy === option
                    ? '#fff'
                    : theme.colors.text
                }
              ]}>
                {option.charAt(0).toUpperCase() + option.slice(1)}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>

      {/* Results */}
      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={theme.colors.primary} />
          <Text style={[styles.loadingText, { color: theme.colors.textSecondary }]}>
            Searching...
          </Text>
        </View>
      ) : (
        <FlatList
          data={results}
          renderItem={renderResult}
          keyExtractor={(item) => `${item.type}-${item.id}`}
          contentContainerStyle={styles.resultsList}
          showsVerticalScrollIndicator={false}
          ListEmptyComponent={
            <View style={styles.emptyContainer}>
              <Ionicons name="search-outline" size={48} color={theme.colors.textSecondary} />
              <Text style={[styles.emptyText, { color: theme.colors.textSecondary }]}>
                No results found for "{query}"
              </Text>
              <Text style={[styles.emptySubtext, { color: theme.colors.textSecondary }]}>
                Try adjusting your search terms or filters
              </Text>
            </View>
          }
        />
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  searchHeader: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  searchBar: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F3F4F6',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
    gap: 8,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
  },
  filterTabs: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    paddingVertical: 8,
    gap: 8,
  },
  filterTab: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
  },
  filterTabText: {
    fontSize: 14,
    fontWeight: '500',
  },
  sortContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    gap: 12,
  },
  sortLabel: {
    fontSize: 14,
    fontWeight: '500',
  },
  sortOptions: {
    flexDirection: 'row',
    gap: 8,
  },
  sortOption: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  sortOptionText: {
    fontSize: 12,
    fontWeight: '500',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    gap: 16,
  },
  loadingText: {
    fontSize: 16,
  },
  resultsList: {
    padding: 16,
  },
  resultCard: {
    flexDirection: 'row',
    padding: 12,
    borderRadius: 12,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  resultImage: {
    width: 80,
    height: 80,
    borderRadius: 8,
  },
  resultContent: {
    flex: 1,
    marginLeft: 12,
  },
  resultHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 4,
  },
  resultName: {
    fontSize: 16,
    fontWeight: '600',
    flex: 1,
  },
  resultCategory: {
    fontSize: 14,
    marginBottom: 2,
  },
  resultLocation: {
    fontSize: 12,
    marginBottom: 8,
  },
  resultFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  rating: {
    fontSize: 14,
    fontWeight: '500',
  },
  reviewCount: {
    fontSize: 12,
  },
  price: {
    fontSize: 14,
    fontWeight: '600',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 64,
    gap: 16,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: '600',
    textAlign: 'center',
  },
  emptySubtext: {
    fontSize: 14,
    textAlign: 'center',
  },
});
