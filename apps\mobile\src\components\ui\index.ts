// Animated Components
export { default as AnimatedButton } from './AnimatedButton';
export { 
  default as AnimatedCard,
  FadeInCard,
  SlideUpCard,
  SlideLeftCard,
  ScaleCard,
  FlipCard,
  createStaggeredCards,
} from './AnimatedCard';
export { default as FloatingActionButton } from './FloatingActionButton';
export { default as EnhancedLoading } from './EnhancedLoading';
export { 
  default as PageTransition,
  FadeTransition,
  SlideLeftTransition,
  SlideRightTransition,
  SlideUpTransition,
  SlideDownTransition,
  ScaleTransition,
  FlipTransition,
  ZoomTransition,
  BlurTransition,
  createStaggeredTransitions,
} from './PageTransition';
export { default as AnimatedInput } from './AnimatedInput';
export {
  default as PullToRefresh,
  SpinnerPullToRefresh,
  ArrowPullToRefresh,
  CustomPullToRefresh,
} from './PullToRefresh';
export {
  default as SwipeActions,
  createDeleteAction,
  createEditAction,
  createArchiveAction,
  createShareAction,
  createFavoriteAction,
} from './SwipeActions';
export { default as OnboardingCarousel } from './OnboardingCarousel';
export {
  default as Toast,
  GlobalToast,
  toastManager,
  type ToastType,
} from './Toast';

// Accessibility Components
export {
  default as AccessibilityWrapper,
  AccessibleButton,
  AccessibleLink,
  AccessibleImage,
  AccessibleText,
  AccessibleHeader,
  AccessibleList,
  AccessibleListItem,
  AccessibleSwitch,
  AccessibleCheckbox,
  AccessibleRadio,
  announceForAccessibility,
  setAccessibilityFocus,
  isScreenReaderEnabled,
  isReduceMotionEnabled,
  isReduceTransparencyEnabled,
  AccessibilityGuidelines,
  checkAccessibilityCompliance,
} from './AccessibilityWrapper';

// Re-export existing components for convenience
export { default as LoadingSpinner } from '../LoadingSpinner';
export { default as ErrorMessage } from '../ErrorMessage';
export { default as EmptyState } from '../EmptyState';
export { default as OfflineIndicator } from '../OfflineIndicator';
export { default as NotificationBell } from '../NotificationBell';
export { default as LanguageSelector } from '../LanguageSelector';
export { default as RoleBasedAccess } from '../RoleBasedAccess';
export { default as WebSafeWrapper } from '../WebSafeWrapper';

// Chart components
export { default as BarChart } from '../charts/BarChart';
export { default as LineChart } from '../charts/LineChart';
export { default as PieChart } from '../charts/PieChart';

// New UI Components matching web app design
export { Button } from './Button';
export type { ButtonProps } from './Button';

export { Card, CardHeader, CardTitle, CardDescription, CardContent, CardFooter } from './Card';
export type { CardProps, CardHeaderProps, CardTitleProps, CardDescriptionProps, CardContentProps, CardFooterProps } from './Card';

export { Input } from './Input';
export type { InputProps } from './Input';

export { Badge } from './Badge';
export type { BadgeProps } from './Badge';
