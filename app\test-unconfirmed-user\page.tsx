"use client"

import { useState } from 'react'
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { useAuth } from "@/contexts/AuthContext"
import { AlertCircle, CheckCircle, Mail, Lock } from "lucide-react"

export default function TestUnconfirmedUserPage() {
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    fullName: ''
  })
  const [testStep, setTestStep] = useState<'signup' | 'login' | 'confirm'>('signup')
  const [error, setError] = useState('')
  const [success, setSuccess] = useState('')
  const [loading, setLoading] = useState(false)

  const { signUp, signInWithPassword, confirmSignUp, resendConfirmationCode } = useAuth()

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    })
  }

  // Step 1: Create an unconfirmed user
  const handleSignUp = async () => {
    setLoading(true)
    setError('')
    setSuccess('')

    try {
      await signUp(formData.email, formData.fullName, formData.password, false)
      setSuccess('User created successfully! Now try to login without confirming.')
      setTestStep('login')
    } catch (error: any) {
      setError(error.message || 'Signup failed')
    } finally {
      setLoading(false)
    }
  }

  // Step 2: Try to login with unconfirmed user
  const handleLogin = async () => {
    setLoading(true)
    setError('')
    setSuccess('')

    try {
      await signInWithPassword(formData.email, formData.password)
      setSuccess('Login successful! (This should not happen for unconfirmed users)')
    } catch (error: any) {
      if (error.name === 'UserNotConfirmedException') {
        setSuccess('✅ Correct! Unconfirmed user error detected. Now you can test the confirmation flow.')
        setTestStep('confirm')
      } else {
        setError(`Unexpected error: ${error.message}`)
      }
    } finally {
      setLoading(false)
    }
  }

  // Step 3: Confirm the user
  const handleConfirm = async () => {
    setLoading(true)
    setError('')
    setSuccess('')

    try {
      // For testing, we'll use a dummy code since we can't receive real emails
      await confirmSignUp(formData.email, '123456')
      setSuccess('User confirmed successfully!')
    } catch (error: any) {
      setError(`Confirmation failed: ${error.message}. This is expected since we used a dummy code.`)
    } finally {
      setLoading(false)
    }
  }

  const handleResendCode = async () => {
    setLoading(true)
    setError('')

    try {
      await resendConfirmationCode(formData.email)
      setSuccess('Confirmation code resent!')
    } catch (error: any) {
      setError(`Failed to resend code: ${error.message}`)
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle className="text-center">
            Test Unconfirmed User Flow
          </CardTitle>
          <p className="text-sm text-gray-600 text-center">
            Step {testStep === 'signup' ? '1' : testStep === 'login' ? '2' : '3'} of 3
          </p>
        </CardHeader>
        <CardContent className="space-y-4">
          {error && (
            <div className="p-3 bg-red-50 border border-red-200 rounded-lg flex items-start gap-2 text-red-700">
              <AlertCircle className="w-5 h-5 mt-0.5 flex-shrink-0" />
              <p className="text-sm">{error}</p>
            </div>
          )}

          {success && (
            <div className="p-3 bg-green-50 border border-green-200 rounded-lg flex items-start gap-2 text-green-700">
              <CheckCircle className="w-5 h-5 mt-0.5 flex-shrink-0" />
              <p className="text-sm">{success}</p>
            </div>
          )}

          {testStep === 'signup' && (
            <>
              <div className="space-y-3">
                <div className="relative">
                  <Input
                    name="fullName"
                    type="text"
                    placeholder="Full Name"
                    className="pl-10"
                    value={formData.fullName}
                    onChange={handleInputChange}
                    required
                  />
                  <Mail className="absolute left-3 top-1/2 -translate-y-1/2 w-4 h-4 text-gray-400" />
                </div>
                <div className="relative">
                  <Input
                    name="email"
                    type="email"
                    placeholder="Email Address"
                    className="pl-10"
                    value={formData.email}
                    onChange={handleInputChange}
                    required
                  />
                  <Mail className="absolute left-3 top-1/2 -translate-y-1/2 w-4 h-4 text-gray-400" />
                </div>
                <div className="relative">
                  <Input
                    name="password"
                    type="password"
                    placeholder="Password"
                    className="pl-10"
                    value={formData.password}
                    onChange={handleInputChange}
                    required
                  />
                  <Lock className="absolute left-3 top-1/2 -translate-y-1/2 w-4 h-4 text-gray-400" />
                </div>
              </div>
              <Button onClick={handleSignUp} className="w-full" disabled={loading}>
                {loading ? 'Creating Account...' : 'Step 1: Create Unconfirmed User'}
              </Button>
            </>
          )}

          {testStep === 'login' && (
            <>
              <p className="text-sm text-gray-600">
                Now try to login with the unconfirmed user. This should trigger the UserNotConfirmedException.
              </p>
              <Button onClick={handleLogin} className="w-full" disabled={loading}>
                {loading ? 'Signing In...' : 'Step 2: Try Login (Should Fail)'}
              </Button>
            </>
          )}

          {testStep === 'confirm' && (
            <>
              <p className="text-sm text-gray-600">
                The unconfirmed user flow is working correctly! In a real scenario, the user would be redirected to the OTP confirmation page.
              </p>
              <div className="space-y-2">
                <Button onClick={handleConfirm} className="w-full" disabled={loading}>
                  {loading ? 'Confirming...' : 'Step 3: Test Confirm (Will Fail - Demo Only)'}
                </Button>
                <Button onClick={handleResendCode} variant="outline" className="w-full" disabled={loading}>
                  Test Resend Code
                </Button>
              </div>
            </>
          )}

          <div className="pt-4 border-t">
            <p className="text-xs text-gray-500 text-center">
              This is a test page to verify the unconfirmed user flow works correctly.
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
