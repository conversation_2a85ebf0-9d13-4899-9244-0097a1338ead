'use client'

import { useState, useEffect, useRef, useCallback, memo } from 'react'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Star, Heart, ShoppingCart, Loader2, ChevronLeft, ChevronRight } from '@/lib/icon-imports'
import Link from 'next/link'
import OptimizedImage, { ProductImage } from '@/components/OptimizedImage'
import { shopService, ShopResponse } from '@/lib/services/shopService'
import { ClientRoot, useCart } from '@/components/ClientRoot'
import '@/lib/amplify-singleton' // Auto-configures Amplify
import toast from 'react-hot-toast';

const FeaturedShopProductsContent = memo(function FeaturedShopProductsContent() {
  const [products, setProducts] = useState<ShopResponse[]>([])
  const [loading, setLoading] = useState(true)
  const [imageErrors, setImageErrors] = useState<Set<string>>(new Set())
  const { addToCart } = useCart()

  // Carousel functionality
  const carouselRef = useRef<HTMLDivElement>(null)
  const [canScrollLeft, setCanScrollLeft] = useState(false)
  const [canScrollRight, setCanScrollRight] = useState(true)

  // Load featured products - memoized to prevent recreating function on each render
  const loadFeaturedProducts = useCallback(async () => {
    try {
      setLoading(true)
      // Get featured products or latest products
      const response = await shopService.listShops(undefined, 6)

      // Filter for featured products first, then take latest if not enough featured
      const featuredProducts = response.items.filter(product => product.featured)
      const latestProducts = response.items.slice(0, 6)

      const displayProducts = featuredProducts.length >= 4
        ? featuredProducts.slice(0, 6)
        : latestProducts

      setProducts(displayProducts)
    } catch (error) {
      console.error('Error loading featured products:', error)
    } finally {
      setLoading(false)
    }
  }, []);

  useEffect(() => {
    loadFeaturedProducts()
  }, [loadFeaturedProducts])

  // Helper function to get product image with fallback - memoized to prevent unnecessary recalculations
  const getProductImage = useCallback((product: ShopResponse) => {
    if (imageErrors.has(product.id)) {
      return "/placeholder.svg"
    }

    // Check if product has images and the first image is valid
    const firstImage = product.images?.[0];
    if (firstImage && firstImage.trim() !== '' && firstImage !== 'undefined' && firstImage !== 'null') {
      return firstImage;
    }

    return "/placeholder.svg"
  }, [imageErrors]);

  // Handle image load errors - memoized to prevent recreating function on each render
  const handleImageError = useCallback((productId: string) => {
    setImageErrors(prev => new Set(Array.from(prev).concat(productId)))
  }, []);

  // Carousel scroll functions - memoized to prevent recreating on each render
  const checkScrollPosition = useCallback(() => {
    if (carouselRef.current) {
      const { scrollLeft, scrollWidth, clientWidth } = carouselRef.current
      setCanScrollLeft(scrollLeft > 5)
      setCanScrollRight(scrollLeft < scrollWidth - clientWidth - 5)
    }
  }, []);

  const scrollLeft = useCallback(() => {
    if (carouselRef.current) {
      const scrollAmount = 336 // Width of one card (320px) + gap (16px)
      carouselRef.current.scrollBy({
        left: -scrollAmount,
        behavior: 'smooth'
      })
      setTimeout(checkScrollPosition, 100)
    }
  }, [checkScrollPosition]);

  const scrollRight = useCallback(() => {
    if (carouselRef.current) {
      const scrollAmount = 336 // Width of one card (320px) + gap (16px)
      carouselRef.current.scrollBy({
        left: scrollAmount,
        behavior: 'smooth'
      })
      setTimeout(checkScrollPosition, 100)
    }
  }, [checkScrollPosition]);

  // Set up scroll event listener
  useEffect(() => {
    const carousel = carouselRef.current
    if (carousel) {
      setTimeout(checkScrollPosition, 100)
      carousel.addEventListener('scroll', checkScrollPosition)
      window.addEventListener('resize', checkScrollPosition)

      return () => {
        carousel.removeEventListener('scroll', checkScrollPosition)
        window.removeEventListener('resize', checkScrollPosition)
      }
    }
  }, [products]) // Re-run when products change

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
        <span className="ml-2 text-gray-600">Loading products...</span>
      </div>
    )
  }

  if (products.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="w-16 h-16 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
          <ShoppingCart className="w-8 h-8 text-gray-400" />
        </div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">No Products Available</h3>
        <p className="text-gray-500 mb-4">We're working on adding amazing products for you.</p>
        <Link href="/shop">
          <Button variant="outline" className="text-primary border-primary hover:bg-primary hover:text-white">
            Browse All Products
          </Button>
        </Link>
      </div>
    )
  }

  // Render as horizontal scrolling carousel like Festive Services section
  return (
    <div className="relative">
      {/* Navigation Controls */}
      <div className="flex items-center justify-end mb-6">
        <div className="flex gap-2">
          <button
            onClick={scrollLeft}
            className={`p-2 rounded-full bg-gray-100 hover:bg-gray-200 transition-all duration-200 hover:shadow-md active:scale-95 cursor-pointer ${
              !canScrollLeft ? 'opacity-50 cursor-not-allowed' : ''
            }`}
            aria-label="Scroll left"
            type="button"
            disabled={!canScrollLeft}
          >
            <ChevronLeft className="w-5 h-5 text-gray-600" />
          </button>
          <button
            onClick={scrollRight}
            className={`p-2 rounded-full bg-gray-100 hover:bg-gray-200 transition-all duration-200 hover:shadow-md active:scale-95 cursor-pointer ${
              !canScrollRight ? 'opacity-50 cursor-not-allowed' : ''
            }`}
            aria-label="Scroll right"
            type="button"
            disabled={!canScrollRight}
          >
            <ChevronRight className="w-5 h-5 text-gray-600" />
          </button>
        </div>
      </div>

      {/* Carousel Container */}
      <div
        ref={carouselRef}
        className="flex gap-4 overflow-x-auto scrollbar-hide scroll-smooth pb-2"
        style={{
          scrollbarWidth: 'none',
          msOverflowStyle: 'none',
          scrollBehavior: 'smooth'
        }}
      >
      {products.map((product) => (
          <div key={product.id} className="flex-none w-48 md:w-80">
            <Card className="overflow-hidden hover:shadow-lg transition-all duration-300 group scale-100 mx-auto h-[300px] md:h-[500px] flex flex-col">
              <div className="relative flex-shrink-0">
            <Link href={`/shop/${product.id}`}>
              <ProductImage
                src={getProductImage(product)}
                alt={product.name || 'Product Image'}
                width={192}
                height={128}
                className="w-full h-32 md:h-80 object-cover transition-transform duration-300 group-hover:scale-105 group-hover:-translate-y-1 group-hover:shadow-lg"
                onError={() => handleImageError(product.id)}
                quality={80}
                sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 192px"
              />
            </Link>
          </div>
              <CardContent className="p-2 md:p-4 flex-1 flex flex-col">
                                <Badge className="text-xs mb-2 bg-[#F6C244]/20 text-[#7c5a13] border-none w-fit hover:bg-[#F6C244]/20">
                  {product.category}
                </Badge>
            <Link href={`/shop/${product.id}`}>
                  <h3 className="font-medium text-base mb-2 line-clamp-2 hover:text-primary transition-colors min-h-[3rem]">
                {product.name}
              </h3>
            </Link>
                            {/* Price and Rating Row */}
                <div className="flex items-center justify-between mb-4 mt-auto">
                  <div className="flex items-center gap-2 flex-wrap">
                {product.discount && product.discount > 0 ? (
                  <>
                    <span className="text-2xl font-extrabold text-primary">{product.price}</span>
                    <span className="text-base text-gray-400 line-through font-medium">{product.originalPrice || ''}</span>
                    <span className="text-sm font-bold text-red-600 ml-1">{product.discount}% OFF</span>
                  </>
                ) : (
                  <span className="text-2xl font-extrabold text-primary">{product.price}</span>
                )}
              </div>
                  <div className="flex items-center ml-2 flex-shrink-0">
                <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                <span className="text-sm font-medium ml-1">{product.rating || 0}</span>
              </div>
            </div>
            {/* Add to Cart Button */}
            {/*
            <Button
              className="w-full mt-2 bg-primary text-white hover:bg-primary/90"
            >
              <ShoppingCart className="h-4 w-4 mr-2" />
              Add to Cart
            </Button>
            */}
          </CardContent>
        </Card>
          </div>
      ))}
      </div>
    </div>
  )
});

export function FeaturedShopProducts() {
  return (
    <ClientRoot>
      <FeaturedShopProductsContent />
    </ClientRoot>
  )
}
