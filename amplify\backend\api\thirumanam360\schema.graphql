# This "input" configures a global authorization rule to enable public access to
# all models in this schema. Learn more about authorization rules here: https://docs.amplify.aws/cli/graphql/authorization-rules
# input AMPLIFY { globalAuthRule: AuthRule = { allow: public } } # DISABLED FOR PRODUCTION

type Vendor @model @auth(rules: [
  { allow: private, provider: userPools },
  { allow: public, provider: apiKey, operations: [read] }
]) {
  id: ID!
  userId: String! @index(name: "byUserId")
  name: String!
  description: String
  contact: String!
  email: String
  address: String
  city: String!
  state: String!
  pincode: String
  website: String
  category: String!
  profilePhoto: String
  gallery: [String]
  services: [Service]
  socialMedia: SocialMedia
  experience: String
  events: String
  responseTime: String
  rating: Float
  reviewCount: Int
  verified: Boolean
  featured: Boolean
  availability: String
  priceRange: String
  specializations: [String]
  awards: [String]
  languages: [String]
  coverage: [String]
  equipment: [String]
  status: String
}

type Service {
  name: String
  price: String
  description: String
}

type SocialMedia {
  facebook: String
  instagram: String
  youtube: String
}

type Venue @model @auth(rules: [
  { allow: private, provider: userPools },
  { allow: public, provider: apiKey, operations: [read] }
]) {
  id: ID!
  userId: String! @index(name: "byUserId")
  name: String!
  description: String
  type: String!
  capacity: String!
  location: String!
  city: String!
  state: String!
  fullAddress: String
  pincode: String
  contactPhone: String
  contactEmail: String
  website: String
  price: String!
  priceRange: String
  images: [String]
  amenities: [String]
  spaces: [VenueSpace]
  packages: [VenuePackage]
  socialMedia: SocialMedia
  rating: Float
  reviewCount: Int
  bookings: Int
  verified: Boolean
  featured: Boolean
  status: String!
  availability: String
  policies: VenuePolicies
  coordinates: VenueCoordinates
  operatingHours: VenueOperatingHours
  createdAt: AWSDateTime!
  updatedAt: AWSDateTime!
}

type VenueSpace {
  name: String!
  capacity: String!
  area: String
  price: String!
  description: String
  amenities: [String]
  images: [String]
}

type VenuePackage {
  name: String!
  price: String!
  duration: String
  description: String
  includes: [String]
  excludes: [String]
  terms: String
}

type VenuePolicies {
  cancellation: String
  advance: String
  catering: String
  decoration: String
  alcohol: String
  music: String
  parking: String
}

type VenueCoordinates {
  latitude: Float
  longitude: Float
}

type VenueOperatingHours {
  monday: String
  tuesday: String
  wednesday: String
  thursday: String
  friday: String
  saturday: String
  sunday: String
}

type Shop @model @auth(rules: [
  { allow: owner, ownerField: "userId", provider: userPools },
  { allow: private, provider: userPools, operations: [read, update, delete] },
  { allow: public, provider: apiKey, operations: [read, create, update, delete] }
]) {
  id: ID!
  userId: String! @index(name: "byUserId")
  name: String!
  category: String!
  price: String!
  originalPrice: String
  discount: Int
  stock: Int!
  sku: String
  brand: String
  featured: Boolean
  description: String
  features: [String]
  sizes: [String]
  colors: [String]
  images: [String]
  specifications: ShopSpecifications
  rating: Float
  reviewCount: Int
  inStock: Boolean!
  status: String!
  createdAt: AWSDateTime!
  updatedAt: AWSDateTime!
}

type ShopSpecifications {
  fabric: String
  work: String
  occasion: String
  care: String
  delivery: String
  returnPolicy: String
}

type Review @model @auth(rules: [
  { allow: owner, ownerField: "userId", provider: userPools },
  { allow: private, provider: userPools, operations: [create, read, update] },
  { allow: public, provider: apiKey, operations: [create, read, update] }
]) {
  id: ID!
  userId: String! @index(name: "byUserId")
  name: String!
  email: String!
  location: String
  weddingDate: AWSDate
  category: ReviewCategory!
  rating: Int!
  title: String!
  review: String!
  wouldRecommend: Boolean!
  verified: Boolean
  status: ReviewStatus!
  # Entity-specific fields for shop/venue/vendor reviews
  entityType: EntityType
  entityId: String @index(name: "byEntityId", sortKeyFields: ["createdAt"])
  # Composite index for user-entity uniqueness
  userEntityComposite: String @index(name: "byUserEntity")
  serviceRating: Int # Rating for specific service quality
  valueRating: Int # Rating for value for money
  communicationRating: Int # Rating for communication
  professionalismRating: Int # Rating for professionalism
  images: [String] # Review images
  helpfulCount: Int # Number of users who found this review helpful
  # Additional metadata
  purchaseVerified: Boolean # Whether the purchase/booking is verified
  reviewHelpfulUsers: [String] # List of user IDs who marked this helpful
  # Vendor response fields
  vendorResponse: String # Vendor's response to the review
  responseDate: AWSDateTime # Date when vendor responded
  # Review routing fields
  reviewTarget: ReviewTarget! # Determines if review goes to admin or vendor
  adminNotes: String # Admin notes for moderation
  moderatedBy: String # Admin who moderated the review
  moderatedAt: AWSDateTime # When the review was moderated
  createdAt: AWSDateTime!
  updatedAt: AWSDateTime!
}

enum ReviewCategory {
  PLATFORM      # Reviews about the overall platform experience - goes to admin
  PRODUCT       # Reviews about specific products/services - goes to vendor
  VENDOR        # Reviews about vendor services - goes to vendor
  VENUE         # Reviews about venue services - goes to vendor
  SHOP          # Reviews about shop products - goes to vendor
}

enum ReviewStatus {
  PENDING
  APPROVED
  REJECTED
}

enum ReviewTarget {
  ADMIN     # Platform reviews go to admin dashboard
  VENDOR    # Product/service reviews go to vendor dashboard
}

enum EntityType {
  SHOP
  VENUE
  VENDOR
  PLATFORM
}

type Contact @model @auth(rules: [
  # Allow admins full access to all contact submissions
  { allow: private, provider: userPools, operations: [create, read, update, delete] },
  # Allow public API key access for creating contacts (contact forms) and reading (for admin dashboard)
  { allow: public, provider: apiKey, operations: [create, read, update] }
]) {
  id: ID!
  name: String!
  email: String!
  phone: String
  subject: String!
  message: String!
  inquiryType: ContactInquiryType!
  status: ContactStatus!
  priority: ContactPriority
  assignedTo: String
  responseMessage: String
  respondedAt: AWSDateTime
  createdAt: AWSDateTime!
  updatedAt: AWSDateTime!
}

enum ContactInquiryType {
  GENERAL
  VENDOR
  SUPPORT
  FEEDBACK
  BUSINESS
}

enum ContactStatus {
  NEW
  IN_PROGRESS
  RESOLVED
  CLOSED
}

enum ContactPriority {
  LOW
  MEDIUM
  HIGH
  URGENT
}

type Inquiry @model @auth(rules: [
  # Allow customers to create inquiries and read their own inquiries
  { allow: owner, ownerField: "customerUserId", provider: userPools, operations: [create, read] },
  # Allow vendors to read inquiries sent to them and update those inquiries
  { allow: owner, ownerField: "vendorUserId", provider: userPools, operations: [read, update] },
  # Allow admins full access (requires admin role check in resolvers)
  { allow: private, provider: userPools, operations: [create, read, update, delete] },
  # Allow public API key access for creating inquiries (non-authenticated users) and reading
  { allow: public, provider: apiKey, operations: [create, read, update] }
]) {
  id: ID!
  vendorUserId: String! @index(name: "byVendorUserId")
  vendorId: String! @index(name: "byVendorId")
  vendorName: String!
  customerUserId: String @index(name: "byCustomerUserId")
  customerName: String!
  customerEmail: String!
  customerPhone: String
  eventDate: AWSDate
  message: String!
  inquiryType: InquiryType!
  status: InquiryStatus!
  priority: InquiryPriority
  budget: String
  guestCount: String
  venue: String
  additionalServices: [String]
  preferredContactTime: String
  responseMessage: String @auth(rules: [
    { allow: owner, ownerField: "vendorUserId", provider: userPools },
    { allow: private, provider: userPools },
    { allow: public, provider: apiKey }
  ])
  respondedAt: AWSDateTime @auth(rules: [
    { allow: owner, ownerField: "vendorUserId", provider: userPools },
    { allow: private, provider: userPools },
    { allow: public, provider: apiKey }
  ])
  assignedTo: String @auth(rules: [
    { allow: private, provider: userPools },
    { allow: public, provider: apiKey }
  ])
  followUpDate: AWSDate @auth(rules: [
    { allow: private, provider: userPools },
    { allow: public, provider: apiKey }
  ])
  notes: String @auth(rules: [
    { allow: private, provider: userPools },
    { allow: public, provider: apiKey }
  ])
  createdAt: AWSDateTime!
  updatedAt: AWSDateTime!
}

type UserProfile @model @auth(rules: [
  { allow: owner, provider: userPools },
  { allow: private, provider: userPools, operations: [read] },
  { allow: public, provider: apiKey, operations: [read, create] }
]) {
  id: ID!
  userId: String! @index(name: "byUserId")
  firstName: String!
  lastName: String
  email: String!
  phone: String
  dateOfBirth: AWSDate
  gender: Gender
  address: String
  city: String
  state: String
  pincode: String
  country: String
  profilePhoto: String
  bio: String
  website: String
  socialMedia: ProfileSocialMedia
  preferences: ProfilePreferences
  businessInfo: BusinessInfo
  isVendor: Boolean
  isAdmin: Boolean
  isSuperAdmin: Boolean
  role: UserRole
  permissions: [String]
  registrationSource: RegistrationSource
  accountType: AccountType
  userTypeForm: UserTypeFormData
  isVerified: Boolean
  lastLoginAt: AWSDateTime
  createdAt: AWSDateTime!
  updatedAt: AWSDateTime!
}

type ProfileSocialMedia {
  facebook: String
  instagram: String
  twitter: String
  linkedin: String
  youtube: String
}

type ProfilePreferences {
  language: String
  currency: String
  timezone: String
  notifications: NotificationSettings
  privacy: PrivacySettings
}

type NotificationSettings {
  email: Boolean
  sms: Boolean
  push: Boolean
  marketing: Boolean
}

type PrivacySettings {
  profileVisibility: ProfileVisibility
  contactVisibility: ContactVisibility
  showOnlineStatus: Boolean
}

type BusinessInfo {
  businessName: String
  businessType: String
  businessAddress: String
  businessPhone: String
  businessEmail: String
  businessWebsite: String
  gstNumber: String
  panNumber: String
  businessLicense: String
}

enum Gender {
  MALE
  FEMALE
  OTHER
  PREFER_NOT_TO_SAY
}

enum UserRole {
  CUSTOMER
  VENDOR
  ADMIN
  SUPER_ADMIN
}

enum FavoriteEntityType {
  VENDOR
  VENUE
  SHOP_ITEM
}

enum CartItemStatus {
  ACTIVE
  SAVED_FOR_LATER
  REMOVED
}

enum RegistrationSource {
  CUSTOMER_FORM
  VENDOR_FORM
  ADMIN_FORM
  BULK_IMPORT
  SOCIAL_LOGIN
  INVITATION
  MIGRATION
}

enum AccountType {
  PERSONAL
  BUSINESS
  ORGANIZATION
  ADMIN_ACCOUNT
}

type UserTypeFormData {
  formType: String
  submissionDate: AWSDateTime
  formVersion: String
  businessDetails: BusinessFormDetails
  personalDetails: PersonalFormDetails
  adminDetails: AdminFormDetails
  verificationStatus: String
  approvalStatus: String
  submittedBy: String
  reviewedBy: String
  reviewDate: AWSDateTime
  reviewNotes: String
}

type BusinessFormDetails {
  businessName: String
  businessType: String
  businessCategory: String
  businessDescription: String
  servicesOffered: [String]
  experienceYears: Int
  teamSize: Int
  businessLicense: String
  gstNumber: String
  panNumber: String
  businessAddress: String
  businessPhone: String
  businessEmail: String
  businessWebsite: String
  portfolioLinks: [String]
  socialMediaLinks: [String]
  operatingHours: String
  serviceAreas: [String]
  priceRange: String
  specializations: [String]
}

type PersonalFormDetails {
  firstName: String
  lastName: String
  dateOfBirth: AWSDate
  gender: String
  weddingDate: AWSDate
  partnerName: String
  weddingLocation: String
  budgetRange: String
  weddingStyle: String
  guestCount: Int
  interests: [String]
  preferences: [String]
  referralSource: String
}

type AdminFormDetails {
  adminLevel: String
  department: String
  responsibilities: [String]
  accessLevel: String
  reportingTo: String
  startDate: AWSDate
  employeeId: String
  designation: String
}

enum ProfileVisibility {
  PUBLIC
  PRIVATE
  FRIENDS_ONLY
}

enum ContactVisibility {
  PUBLIC
  PRIVATE
  VERIFIED_ONLY
}

enum InquiryType {
  VENDOR_INQUIRY
  VENUE_INQUIRY
  SERVICE_QUOTE
  AVAILABILITY_CHECK
  GENERAL_QUESTION
  BOOKING_REQUEST
}

enum InquiryStatus {
  NEW
  CONTACTED
  QUOTED
  NEGOTIATING
  CONFIRMED
  COMPLETED
  CANCELLED
  EXPIRED
}

enum InquiryPriority {
  LOW
  MEDIUM
  HIGH
  URGENT
}

input ShopSpecificationsInput {
  fabric: String
  work: String
  occasion: String
  care: String
  delivery: String
  returnPolicy: String
}

type Blog @model @auth(rules: [
  { allow: owner, ownerField: "authorId", provider: userPools },
  { allow: groups, groups: ["admin", "super_admin"], provider: userPools },
  { allow: private, provider: userPools, operations: [read, update, delete] },
  { allow: public, provider: apiKey, operations: [read] }
]) {
  id: ID!
  title: String!
  content: String!
  excerpt: String
  category: BlogCategory!
  authorId: String! @index(name: "byAuthor")
  authorName: String!
  authorType: AuthorType!
  featuredImage: String
  tags: [String]
  status: BlogStatus!
  views: Int
  likes: Int
  comments: Int
  isPinned: Boolean
  isFeatured: Boolean
  publishedAt: AWSDateTime
  createdAt: AWSDateTime!
  updatedAt: AWSDateTime!
}

type Booking @model @auth(rules: [
  { allow: owner, provider: userPools },
  { allow: private, provider: userPools, operations: [read, create, update] },
  { allow: public, provider: apiKey, operations: [read] }
]) {
  id: ID!
  customerId: String! @index(name: "byCustomer")
  customerName: String!
  customerEmail: String!
  customerPhone: String
  entityId: String! @index(name: "byEntity")
  entityType: BookingEntityType!
  entityName: String!
  vendorId: String @index(name: "byVendor")
  eventDate: AWSDate!
  eventTime: String!
  guestCount: Int!
  eventType: String!
  duration: String
  specialRequests: String
  budget: String
  contactPreference: ContactPreference!
  status: BookingStatus!
  priority: BookingPriority
  notes: String
  vendorNotes: String
  estimatedCost: String
  finalCost: String
  advanceAmount: String
  balanceAmount: String
  paymentStatus: PaymentStatus
  paymentMethod: String
  transactionId: String
  contractSigned: Boolean
  contractUrl: String
  cancellationReason: String
  cancellationDate: AWSDateTime
  refundAmount: String
  refundStatus: RefundStatus
  followUpDate: AWSDate
  reminderSent: Boolean
  customerRating: Int
  customerReview: String
  vendorRating: Int
  vendorReview: String
  communicationLog: [CommunicationEntry]
  attachments: [String]
  metadata: AWSJSON
  createdAt: AWSDateTime!
  updatedAt: AWSDateTime!
}

type CommunicationEntry {
  timestamp: AWSDateTime!
  type: CommunicationType!
  from: String!
  to: String!
  subject: String
  message: String!
  attachments: [String]
  status: String
}

type NewsletterSubscription @model @auth(rules: [
  { allow: private, provider: userPools },
  { allow: public, provider: apiKey, operations: [create, read] }
]) {
  id: ID!
  email: String! @index(name: "byEmail")
  firstName: String
  lastName: String
  phone: String
  city: String
  state: String
  weddingDate: AWSDate
  interests: [NewsletterInterest]
  source: SubscriptionSource!
  status: NewsletterSubscriptionStatus!
  preferences: NewsletterPreferences
  userId: String @index(name: "byUserId")
  subscribedAt: AWSDateTime!
  unsubscribedAt: AWSDateTime
  lastEmailSent: AWSDateTime
  emailsSent: Int
  emailsOpened: Int
  emailsClicked: Int
  createdAt: AWSDateTime!
  updatedAt: AWSDateTime!
}

type NewsletterPreferences {
  weddingTips: Boolean
  vendorRecommendations: Boolean
  specialOffers: Boolean
  eventUpdates: Boolean
  blogUpdates: Boolean
  frequency: NewsletterEmailFrequency
}

enum NewsletterInterest {
  PHOTOGRAPHY
  VIDEOGRAPHY
  CATERING
  DECORATION
  MAKEUP
  VENUES
  SHOPPING
  PLANNING
  HONEYMOON
  JEWELRY
  INVITATIONS
  MUSIC
  TRANSPORTATION
}

enum SubscriptionSource {
  HOMEPAGE
  SIGNUP_FORM
  VENDOR_PAGE
  VENUE_PAGE
  SHOP_PAGE
  BLOG_PAGE
  OFFERS_PAGE
  CONTACT_FORM
  MOBILE_APP
  SOCIAL_MEDIA
  REFERRAL
  OTHER
}

enum NewsletterSubscriptionStatus {
  ACTIVE
  UNSUBSCRIBED
  BOUNCED
  COMPLAINED
  PENDING_CONFIRMATION
}

enum NewsletterEmailFrequency {
  DAILY
  WEEKLY
  BIWEEKLY
  MONTHLY
  SPECIAL_ONLY
}

enum BlogCategory {
  WEDDING_PLANNING
  VENUE_SELECTION
  PHOTOGRAPHY_VIDEOGRAPHY
  CATERING_FOOD
  DECORATIONS_THEMES
  BUDGET_FINANCE
  FASHION_STYLE
  REAL_WEDDINGS
  EXPERT_TIPS
  VENDOR_SPOTLIGHT
}

enum BlogStatus {
  DRAFT
  PUBLISHED
  ARCHIVED
}

enum AuthorType {
  VENDOR
  ADMIN
  EXPERT
}

enum BookingEntityType {
  VENDOR
  VENUE
}

enum BookingStatus {
  PENDING
  CONFIRMED
  IN_PROGRESS
  COMPLETED
  CANCELLED
  REJECTED
}

enum BookingPriority {
  LOW
  MEDIUM
  HIGH
  URGENT
}

enum ContactPreference {
  PHONE
  EMAIL
  WHATSAPP
}

enum PaymentStatus {
  PENDING
  PARTIAL
  PAID
  REFUNDED
  FAILED
}

enum RefundStatus {
  NOT_APPLICABLE
  REQUESTED
  PROCESSING
  COMPLETED
  REJECTED
}

enum CommunicationType {
  EMAIL
  PHONE
  SMS
  WHATSAPP
  IN_PERSON
  VIDEO_CALL
  SYSTEM
}

type ChecklistItem @model @auth(rules: [
  { allow: owner, ownerField: "userId", provider: userPools },
  { allow: private, provider: userPools },
  { allow: public, provider: apiKey }
]) {
  id: ID!
  userId: String @index(name: "byUserId")
  categoryId: ID! @index(name: "byCategory")
  text: String!
  completed: Boolean!
  dueDate: AWSDate
  priority: ChecklistPriority!
  order: Int
  isDefault: Boolean!
  createdAt: AWSDateTime!
  updatedAt: AWSDateTime!
}

type ChecklistCategory @model @auth(rules: [
  { allow: owner, ownerField: "userId", provider: userPools },
  { allow: private, provider: userPools },
  { allow: public, provider: apiKey }
]) {
  id: ID!
  userId: String @index(name: "byUserId")
  name: String!
  icon: String!
  expanded: Boolean!
  order: Int
  isDefault: Boolean!
  items: [ChecklistItem] @hasMany(indexName: "byCategory", fields: ["id"])
  createdAt: AWSDateTime!
  updatedAt: AWSDateTime!
}

enum ChecklistPriority {
  LOW
  MEDIUM
  HIGH
}

type Favorite @model @auth(rules: [
  { allow: owner, provider: userPools },
  { allow: private, provider: userPools, operations: [read] }
]) {
  id: ID!
  userId: String! @index(name: "byUser")
  entityId: String! @index(name: "byEntity")
  entityType: FavoriteEntityType!
  entityName: String!
  entityImage: String
  entityPrice: String
  entityLocation: String
  entityCity: String
  entityState: String
  entityRating: Float
  entityReviewCount: Int
  entityDescription: String
  dateAdded: AWSDateTime!
  notes: String
  createdAt: AWSDateTime!
  updatedAt: AWSDateTime!
}

type CartItem @model @auth(rules: [
  { allow: owner, provider: userPools },
  { allow: private, provider: userPools, operations: [read] }
]) {
  id: ID!
  userId: String! @index(name: "byUser")
  productId: String! @index(name: "byProduct")
  productName: String!
  productImage: String
  productPrice: Float!
  originalPrice: Float
  discount: Float
  quantity: Int!
  selectedVariant: String
  selectedSize: String
  selectedColor: String
  productBrand: String
  productCategory: String
  productDescription: String
  status: CartItemStatus!
  dateAdded: AWSDateTime!
  dateUpdated: AWSDateTime!
  notes: String
  createdAt: AWSDateTime!
  updatedAt: AWSDateTime!
}

type Order @model @auth(rules: [
  { allow: owner, provider: userPools },
  { allow: private, provider: userPools, operations: [read, create, update] },
  { allow: public, provider: apiKey, operations: [read] }
]) {
  id: ID!
  userId: String! @index(name: "byUser")
  orderNumber: String! @index(name: "byOrderNumber")
  status: OrderStatus!
  paymentStatus: OrderPaymentStatus!
  paymentMethod: PaymentMethod!

  # Customer Information
  customerName: String!
  customerEmail: String!
  customerPhone: String!

  # Shipping Address
  shippingAddress: ShippingAddress!

  # Billing Address (optional, defaults to shipping)
  billingAddress: ShippingAddress

  # Order Items
  items: [OrderItem!]!

  # Pricing
  subtotal: Float!
  shippingCost: Float!
  tax: Float!
  discount: Float!
  total: Float!

  # Payment Information
  razorpayOrderId: String
  razorpayPaymentId: String
  razorpaySignature: String
  transactionId: String

  # Order Tracking
  estimatedDeliveryDate: AWSDate
  actualDeliveryDate: AWSDate
  trackingNumber: String
  courierPartner: String

  # Additional Information
  specialInstructions: String
  giftMessage: String
  isGift: Boolean!

  # Timestamps
  orderDate: AWSDateTime!
  shippedDate: AWSDateTime
  deliveredDate: AWSDateTime
  cancelledDate: AWSDateTime

  # Metadata
  metadata: AWSJSON
  notes: String

  createdAt: AWSDateTime!
  updatedAt: AWSDateTime!
}

type OrderItem {
  productId: String!
  productName: String!
  productImage: String
  productPrice: Float!
  originalPrice: Float
  discount: Float
  quantity: Int!
  selectedVariant: String
  selectedSize: String
  selectedColor: String
  productBrand: String
  productCategory: String
  subtotal: Float!
}

type ShippingAddress {
  fullName: String!
  addressLine1: String!
  addressLine2: String
  city: String!
  state: String!
  pincode: String!
  country: String!
  phone: String
  landmark: String
  addressType: AddressType
}

type Payment @model @auth(rules: [
  { allow: owner, provider: userPools },
  { allow: private, provider: userPools, operations: [read, create, update] }
]) {
  id: ID!
  orderId: String! @index(name: "byOrder")
  userId: String! @index(name: "byUser")

  # Payment Details
  amount: Float!
  currency: String!
  paymentMethod: PaymentMethod!
  status: PaymentTransactionStatus!

  # Razorpay Integration
  razorpayOrderId: String
  razorpayPaymentId: String
  razorpaySignature: String

  # COD Details
  codAmount: Float
  codCollected: Boolean
  codCollectedDate: AWSDateTime

  # Transaction Information
  transactionId: String
  gatewayResponse: AWSJSON
  failureReason: String

  # Refund Information
  refundAmount: Float
  refundStatus: RefundStatus
  refundDate: AWSDateTime
  refundTransactionId: String

  # Timestamps
  initiatedAt: AWSDateTime!
  completedAt: AWSDateTime

  # Metadata
  metadata: AWSJSON
  notes: String

  createdAt: AWSDateTime!
  updatedAt: AWSDateTime!
}

# Order and Payment Enums
enum OrderStatus {
  PENDING
  CONFIRMED
  PROCESSING
  SHIPPED
  OUT_FOR_DELIVERY
  DELIVERED
  CANCELLED
  RETURNED
  REFUNDED
}

enum OrderPaymentStatus {
  PENDING
  PAID
  FAILED
  REFUNDED
  PARTIALLY_REFUNDED
  COD_PENDING
  COD_COLLECTED
}

enum PaymentMethod {
  RAZORPAY
  COD
  UPI
  CREDIT_CARD
  DEBIT_CARD
  NET_BANKING
  WALLET
}

enum PaymentTransactionStatus {
  INITIATED
  PENDING
  SUCCESS
  FAILED
  CANCELLED
  REFUNDED
  PARTIALLY_REFUNDED
}

enum AddressType {
  HOME
  OFFICE
  OTHER
}

# Planning Tools Models - Simplified for initial deployment
type WeddingPlan @model @auth(rules: [
  { allow: owner, ownerField: "userId", provider: userPools },
  { allow: private, provider: userPools },
  { allow: public, provider: apiKey }
]) {
  id: ID!
  userId: String! @index(name: "byUserId", sortKeyFields: ["createdAt"])
  weddingDate: AWSDate
  venue: String
  budget: Float
  guestCount: Int
  theme: String
  status: WeddingPlanStatus
  notes: String
  createdAt: AWSDateTime
  updatedAt: AWSDateTime
}

enum WeddingPlanStatus {
  PLANNING
  CONFIRMED
  COMPLETED
  CANCELLED
}

type Budget @model @auth(rules: [
  { allow: owner, ownerField: "userId", provider: userPools },
  { allow: private, provider: userPools },
  { allow: public, provider: apiKey }
]) {
  id: ID!
  userId: String! @index(name: "byUserId", sortKeyFields: ["createdAt"])
  weddingPlanId: String
  name: String!
  totalBudget: Float!
  spentAmount: Float
  isTemplate: Boolean
  templateType: String
  createdAt: AWSDateTime
  updatedAt: AWSDateTime
}

# Simplified Budget Storage - JSON format for now
type BudgetData @model @auth(rules: [
  { allow: owner, ownerField: "userId", provider: userPools },
  { allow: private, provider: userPools },
  { allow: public, provider: apiKey }
]) {
  id: ID!
  userId: String! @index(name: "byUserId", sortKeyFields: ["createdAt"])
  budgetId: String! @index(name: "byBudget")
  name: String!
  data: AWSJSON!
  createdAt: AWSDateTime
  updatedAt: AWSDateTime
}

# Guest List Storage - JSON format
type GuestListData @model @auth(rules: [
  { allow: owner, ownerField: "userId", provider: userPools },
  { allow: private, provider: userPools },
  { allow: public, provider: apiKey }
]) {
  id: ID!
  userId: String! @index(name: "byUserId", sortKeyFields: ["createdAt"])
  name: String!
  data: AWSJSON!
  totalGuests: Int
  confirmedGuests: Int
  createdAt: AWSDateTime
  updatedAt: AWSDateTime
}

# Planning Tools Data Storage - Simplified JSON approach
type PlanningToolsData @model @auth(rules: [
  { allow: owner, ownerField: "userId", provider: userPools },
  { allow: private, provider: userPools },
  { allow: public, provider: apiKey }
]) {
  id: ID!
  userId: String! @index(name: "byUserId", sortKeyFields: ["createdAt"])
  toolType: PlanningToolType!
  name: String!
  data: AWSJSON!
  metadata: AWSJSON
  createdAt: AWSDateTime
  updatedAt: AWSDateTime
}

enum PlanningToolType {
  BUDGET
  GUEST_LIST
  TIMELINE
  CHECKLIST
  IDEAS
  WEDDING_PLAN
}

# Email Management Schema for BookmyFestive

type EmailTemplate @model @auth(rules: [
  { allow: public, operations: [read] },
  { allow: private, operations: [read, create, update, delete] }
]) {
  id: ID!
  name: String! @index(name: "byName")
  type: EmailType!
  subject: String!
  htmlContent: String!
  textContent: String!
  isActive: Boolean!
  variables: [String!]
  createdAt: AWSDateTime!
  updatedAt: AWSDateTime!
}

type EmailLog @model @auth(rules: [
  { allow: private, operations: [read, create] },
  { allow: owner, operations: [read] }
]) {
  id: ID!
  messageId: String
  emailType: EmailType!
  recipient: String! @index(name: "byRecipient")
  subject: String!
  status: EmailStatus!
  errorMessage: String
  templateData: AWSJSON
  sentAt: AWSDateTime!
  userId: String @index(name: "byUserId")
  vendorId: String @index(name: "byVendorId")
  bookingId: String @index(name: "byBookingId")
  orderId: String @index(name: "byOrderId")
  createdAt: AWSDateTime!
  updatedAt: AWSDateTime!
}

type EmailSubscription @model @auth(rules: [
  { allow: owner, operations: [read, create, update, delete] },
  { allow: private, operations: [read] }
]) {
  id: ID!
  email: String! @index(name: "byEmail")
  userId: String @index(name: "byUserId")
  subscriptionType: EmailSubscriptionType!
  isActive: Boolean!
  preferences: EmailPreferences
  subscribedAt: AWSDateTime!
  unsubscribedAt: AWSDateTime
  createdAt: AWSDateTime!
  updatedAt: AWSDateTime!
}

type EmailPreferences {
  weddingTips: Boolean
  vendorRecommendations: Boolean
  specialOffers: Boolean
  eventUpdates: Boolean
  blogUpdates: Boolean
  weeklyNews: Boolean
  favoritesNotifications: Boolean
  vendorLaunches: Boolean
  frequency: NewsletterEmailFrequency
}

input EmailPreferencesInput {
  weddingTips: Boolean
  vendorRecommendations: Boolean
  specialOffers: Boolean
  eventUpdates: Boolean
  blogUpdates: Boolean
  weeklyNews: Boolean
  favoritesNotifications: Boolean
  vendorLaunches: Boolean
  frequency: NewsletterEmailFrequency
}

enum EmailType {
  LOGIN_OTP
  WELCOME_SIGNUP
  NEWSLETTER_SUBSCRIPTION
  BOOKING_CONFIRMATION
  PAYMENT_SUCCESS
  WEEKLY_NEWS
  OFFERS_MAIL
  FAVORITES_NOTIFICATION
  VENDOR_LAUNCH
  AVAILABILITY_CHECK
}

enum EmailStatus {
  SENT
  FAILED
  PENDING
  DELIVERED
  BOUNCED
  COMPLAINED
}

enum EmailSubscriptionType {
  NEWSLETTER
  MARKETING
  TRANSACTIONAL
  ALL
}

# Invoice Management Schema for BookmyFestive

type Invoice @model @auth(rules: [
  { allow: owner, operations: [read] },
  { allow: private, operations: [read, create, update, delete] }
]) {
  id: ID!
  invoiceNumber: String! @index(name: "byInvoiceNumber")
  invoiceDate: AWSDate!
  dueDate: AWSDate
  type: InvoiceType!

  # Customer Information
  customerId: ID! @index(name: "byCustomerId")
  customerName: String!
  customerEmail: String!
  customerPhone: String
  customerAddress: Address

  # Vendor Information (for bookings)
  vendorId: ID @index(name: "byVendorId")
  vendorName: String
  vendorEmail: String
  vendorBusinessName: String
  vendorAddress: Address
  vendorGstNumber: String

  # Invoice Items
  items: [InvoiceItem!]!

  # Pricing Details
  subtotal: Float!
  taxAmount: Float!
  discountAmount: Float
  totalAmount: Float!

  # Payment Information
  paymentStatus: InvoicePaymentStatus!
  paymentMethod: String
  paymentDate: AWSDateTime
  transactionId: String

  # Additional Information
  notes: String
  terms: String

  # Booking specific (for venue/vendor bookings)
  eventDate: AWSDate
  eventTime: String
  eventLocation: String

  # PDF URL
  pdfUrl: String

  # Metadata
  createdAt: AWSDateTime!
  updatedAt: AWSDateTime!
}

type InvoiceItem {
  id: ID!
  name: String!
  description: String
  quantity: Int!
  unitPrice: Float!
  totalPrice: Float!
  taxRate: Float
  taxAmount: Float
}

type Address {
  street: String!
  city: String!
  state: String!
  pincode: String!
  country: String!
}

enum InvoiceType {
  PRODUCT_ORDER
  VENUE_BOOKING
  VENDOR_BOOKING
  SUBSCRIPTION
}

enum InvoicePaymentStatus {
  PAID
  PENDING
  OVERDUE
  CANCELLED
}

# Pricing and Subscription Management Schema for BookmyFestive

type PricingPlan @model @auth(rules: [
  { allow: public, operations: [read] },
  { allow: private, operations: [read, create, update, delete] }
]) {
  id: ID!
  name: String! @index(name: "byName")
  description: String!
  price: Float!
  currency: String!
  duration: PlanDuration!
  features: [String!]!
  isActive: Boolean!
  isPopular: Boolean!
  discountPercentage: Float
  discountValidUntil: AWSDate
  createdAt: AWSDateTime!
  updatedAt: AWSDateTime!
}

type VendorSubscription @model @auth(rules: [
  { allow: owner, ownerField: "vendorId", operations: [read, create, update, delete] },
  { allow: private, operations: [read, create, update, delete] }
]) {
  id: ID!
  vendorId: ID! @index(name: "byVendorId")
  planId: ID! @index(name: "byPlanId")
  status: SubscriptionStatus!
  startDate: AWSDate!
  endDate: AWSDate!
  autoRenew: Boolean!
  paymentMethod: String
  lastPaymentDate: AWSDateTime
  nextPaymentDate: AWSDateTime
  amount: Float!
  currency: String!
  transactionId: String
  cancellationReason: String
  cancellationDate: AWSDateTime
  createdAt: AWSDateTime!
  updatedAt: AWSDateTime!
}

type SubscriptionPayment @model @auth(rules: [
  { allow: owner, ownerField: "vendorId", operations: [read] },
  { allow: private, operations: [read, create, update, delete] }
]) {
  id: ID!
  subscriptionId: ID! @index(name: "bySubscriptionId")
  vendorId: ID! @index(name: "byVendorId")
  amount: Float!
  currency: String!
  paymentMethod: String!
  transactionId: String
  status: SubscriptionPaymentStatus!
  paymentDate: AWSDateTime!
  invoiceId: ID
  createdAt: AWSDateTime!
  updatedAt: AWSDateTime!
}

enum PlanDuration {
  MONTHLY
  QUARTERLY
  YEARLY
}

enum SubscriptionStatus {
  ACTIVE
  PENDING
  CANCELLED
  EXPIRED
  SUSPENDED
}

enum SubscriptionPaymentStatus {
  PAID
  PENDING
  FAILED
  REFUNDED
}

# Phase 2: Custom Mutations, Queries, and Lambda Integrations

# Email Input Types
input SendEmailInput {
  emailType: EmailType!
  recipient: String!
  templateData: AWSJSON!
  userId: String
  vendorId: String
  bookingId: String
  orderId: String
}

input SendBulkEmailInput {
  emailType: EmailType!
  recipients: [String!]!
  templateData: AWSJSON!
}

# Invoice Input Types
input GenerateInvoiceInput {
  type: InvoiceType!
  customerId: ID!
  customerName: String!
  customerEmail: String!
  customerPhone: String
  customerAddress: AddressInput
  vendorId: ID
  vendorName: String
  vendorEmail: String
  vendorBusinessName: String
  vendorAddress: AddressInput
  vendorGstNumber: String
  items: [InvoiceItemInput!]!
  subtotal: Float!
  taxAmount: Float!
  discountAmount: Float
  totalAmount: Float!
  paymentStatus: InvoicePaymentStatus!
  paymentMethod: String
  paymentDate: AWSDateTime
  transactionId: String
  notes: String
  terms: String
  eventDate: AWSDate
  eventTime: String
  eventLocation: String
}

input InvoiceItemInput {
  id: ID!
  name: String!
  description: String
  quantity: Int!
  unitPrice: Float!
  totalPrice: Float!
  taxRate: Float
  taxAmount: Float
}

input AddressInput {
  street: String!
  city: String!
  state: String!
  pincode: String!
  country: String!
}

# Pricing Input Types
input CreateSubscriptionInput {
  vendorId: ID!
  planId: ID!
  paymentMethod: String!
  autoRenew: Boolean
}

input CancelSubscriptionInput {
  vendorId: ID!
  reason: String!
}

# Response Types
type SendEmailResponse {
  success: Boolean!
  messageId: String
  error: String
  emailLogId: String
}

type SendBulkEmailResponse {
  success: Boolean!
  sentCount: Int!
  failedCount: Int!
  errors: [String!]
  emailLogIds: [String!]
}

type GenerateInvoiceResponse {
  success: Boolean!
  invoiceId: ID
  invoiceNumber: String
  pdfUrl: String
  error: String
}

type CreateSubscriptionResponse {
  success: Boolean!
  subscriptionId: ID
  paymentUrl: String
  error: String
}

type CancelSubscriptionResponse {
  success: Boolean!
  message: String
  error: String
}

# Additional Response Types for Queries
type EmailStats {
  totalSent: Int!
  totalDelivered: Int!
  totalFailed: Int!
  totalBounced: Int!
  deliveryRate: Float!
  bounceRate: Float!
  byType: [EmailTypeStats!]!
}

type EmailTypeStats {
  emailType: EmailType!
  sent: Int!
  delivered: Int!
  failed: Int!
  bounced: Int!
}

type EmailLogConnection {
  items: [EmailLog!]!
  nextToken: String
}

type InvoiceConnection {
  items: [Invoice!]!
  nextToken: String
}

type SubscriptionPaymentConnection {
  items: [SubscriptionPayment!]!
  nextToken: String
}

type SubscriptionStatsResponse {
  totalSubscriptions: Int!
  activeSubscriptions: Int!
  cancelledSubscriptions: Int!
  revenue: Float!
  byPlan: [PlanStatsResponse!]!
}

type PlanStatsResponse {
  planId: ID!
  planName: String!
  subscriptions: Int!
  revenue: Float!
}

# Custom Mutations
type Mutation {
  # Invoice Mutations (Keep only working functions)
  generateInvoice(input: GenerateInvoiceInput!): GenerateInvoiceResponse
    @function(name: "generateInvoice-${env}")
    @auth(rules: [{ allow: private }])

  # Pricing Mutations (Keep only working functions)
  createSubscription(input: CreateSubscriptionInput!): CreateSubscriptionResponse
    @function(name: "createSubscription-${env}")
    @auth(rules: [{ allow: private }])
}

# Additional Input Types for Mutations
input SkipPricingInput {
  vendorId: ID!
  reason: String
}

type SkipPricingResponse {
  success: Boolean!
  message: String
}

input RenewSubscriptionInput {
  subscriptionId: ID!
  planId: ID
}

type RenewSubscriptionResponse {
  success: Boolean!
  subscriptionId: ID
  message: String
}

# Real-time Subscriptions
# Note: Subscriptions are commented out to avoid deployment issues
# They can be enabled later when Lambda functions are properly configured
# type Subscription {
#   # Email Status Updates
#   onEmailStatusUpdate(userId: String!): EmailLog
#     @aws_subscribe(mutations: ["sendEmail"])
#     @auth(rules: [{ allow: private }])
#
#   # Invoice Updates
#   onInvoiceGenerated(customerId: ID!): Invoice
#     @aws_subscribe(mutations: ["generateInvoice"])
#     @auth(rules: [{ allow: private }])
#
#   # Subscription Updates
#   onSubscriptionStatusChange(vendorId: ID!): VendorSubscription
#     @aws_subscribe(mutations: ["createSubscription", "cancelSubscription"])
#     @auth(rules: [{ allow: private }])
# }


