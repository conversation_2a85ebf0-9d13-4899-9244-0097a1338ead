import React, { useState } from 'react';
import { View, Text, TextInput, TouchableOpacity, StyleSheet, Alert, ScrollView } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { StackNavigationProp } from '@react-navigation/stack';
import { RouteProp } from '@react-navigation/native';
import { AppStackParamList } from '../navigation/AppNavigator';
import { useTheme } from '../providers/ThemeProvider';
import { useAuth } from '../providers/AuthProvider';
import { graphqlService } from '../services/graphqlService';
import LoadingSpinner from '../components/LoadingSpinner';
import ErrorMessage from '../components/ErrorMessage';

type ReviewScreenNavigationProp = StackNavigationProp<AppStackParamList, 'Review'>;
type ReviewScreenRouteProp = RouteProp<AppStackParamList, 'Review'>;

interface Props {
  navigation: ReviewScreenNavigationProp;
  route: ReviewScreenRouteProp;
}

interface ReviewData {
  rating: number;
  title: string;
  comment: string;
  serviceQuality: number;
  valueForMoney: number;
  professionalism: number;
  communication: number;
  wouldRecommend: boolean;
}

export default function ReviewScreen({ navigation, route }: Props) {
  const { theme } = useTheme();
  const { user } = useAuth();
  const { vendorId, venueId, shopId, serviceName } = route.params;
  
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const [reviewData, setReviewData] = useState<ReviewData>({
    rating: 0,
    title: '',
    comment: '',
    serviceQuality: 0,
    valueForMoney: 0,
    professionalism: 0,
    communication: 0,
    wouldRecommend: true,
  });

  const handleRatingChange = (field: keyof ReviewData, rating: number) => {
    setReviewData(prev => ({ ...prev, [field]: rating }));
  };

  const handleInputChange = (field: keyof ReviewData, value: string | boolean) => {
    setReviewData(prev => ({ ...prev, [field]: value }));
  };

  const validateReview = (): boolean => {
    if (reviewData.rating === 0) {
      Alert.alert('Error', 'Please provide an overall rating');
      return false;
    }
    if (!reviewData.title.trim()) {
      Alert.alert('Error', 'Please provide a review title');
      return false;
    }
    if (!reviewData.comment.trim()) {
      Alert.alert('Error', 'Please write a review comment');
      return false;
    }
    if (reviewData.serviceQuality === 0 || reviewData.valueForMoney === 0 || 
        reviewData.professionalism === 0 || reviewData.communication === 0) {
      Alert.alert('Error', 'Please rate all aspects of the service');
      return false;
    }
    return true;
  };

  const handleSubmitReview = async () => {
    if (!validateReview()) return;

    try {
      setLoading(true);
      setError(null);

      const review = {
        userId: user?.id,
        vendorId,
        venueId,
        shopId,
        rating: reviewData.rating,
        title: reviewData.title,
        comment: reviewData.comment,
        serviceQuality: reviewData.serviceQuality,
        valueForMoney: reviewData.valueForMoney,
        professionalism: reviewData.professionalism,
        communication: reviewData.communication,
        wouldRecommend: reviewData.wouldRecommend,
        createdAt: new Date().toISOString(),
        status: 'published',
      };

      // TODO: Implement actual review creation via GraphQL
      // await graphqlService.createReview(review);
      console.log('Creating review:', review);
      
      Alert.alert(
        'Review Submitted',
        'Thank you for your review! It will help other couples make informed decisions.',
        [
          {
            text: 'OK',
            onPress: () => navigation.goBack(),
          },
        ]
      );
    } catch (err) {
      setError('Failed to submit review. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const renderStarRating = (
    label: string,
    value: number,
    onPress: (rating: number) => void
  ) => (
    <View style={styles.ratingSection}>
      <Text style={[styles.ratingLabel, { color: theme.colors.text }]}>
        {label}
      </Text>
      <View style={styles.starsContainer}>
        {[1, 2, 3, 4, 5].map((star) => (
          <TouchableOpacity
            key={star}
            onPress={() => onPress(star)}
            style={styles.starButton}
          >
            <Ionicons
              name={star <= value ? 'star' : 'star-outline'}
              size={28}
              color={star <= value ? '#F59E0B' : theme.colors.textSecondary}
            />
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );

  if (loading) {
    return <LoadingSpinner fullScreen text="Submitting review..." />;
  }

  return (
    <ScrollView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {error && (
        <ErrorMessage
          message={error}
          onRetry={() => setError(null)}
        />
      )}

      <View style={[styles.header, { backgroundColor: theme.colors.surface }]}>
        <Text style={[styles.headerTitle, { color: theme.colors.text }]}>
          Write a Review
        </Text>
        <Text style={[styles.serviceName, { color: theme.colors.primary }]}>
          {serviceName}
        </Text>
      </View>

      <View style={styles.content}>
        {/* Overall Rating */}
        {renderStarRating(
          'Overall Rating',
          reviewData.rating,
          (rating) => handleRatingChange('rating', rating)
        )}

        {/* Review Title */}
        <View style={styles.inputSection}>
          <Text style={[styles.inputLabel, { color: theme.colors.text }]}>
            Review Title
          </Text>
          <TextInput
            style={[styles.textInput, { backgroundColor: theme.colors.surface, color: theme.colors.text }]}
            value={reviewData.title}
            onChangeText={(value) => handleInputChange('title', value)}
            placeholder="Summarize your experience"
            placeholderTextColor={theme.colors.textSecondary}
            maxLength={100}
          />
        </View>

        {/* Review Comment */}
        <View style={styles.inputSection}>
          <Text style={[styles.inputLabel, { color: theme.colors.text }]}>
            Your Review
          </Text>
          <TextInput
            style={[styles.textArea, { backgroundColor: theme.colors.surface, color: theme.colors.text }]}
            value={reviewData.comment}
            onChangeText={(value) => handleInputChange('comment', value)}
            placeholder="Share details about your experience..."
            placeholderTextColor={theme.colors.textSecondary}
            multiline
            numberOfLines={4}
            maxLength={500}
          />
        </View>

        {/* Detailed Ratings */}
        <View style={styles.detailedRatings}>
          <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
            Rate Different Aspects
          </Text>
          
          {renderStarRating(
            'Service Quality',
            reviewData.serviceQuality,
            (rating) => handleRatingChange('serviceQuality', rating)
          )}
          
          {renderStarRating(
            'Value for Money',
            reviewData.valueForMoney,
            (rating) => handleRatingChange('valueForMoney', rating)
          )}
          
          {renderStarRating(
            'Professionalism',
            reviewData.professionalism,
            (rating) => handleRatingChange('professionalism', rating)
          )}
          
          {renderStarRating(
            'Communication',
            reviewData.communication,
            (rating) => handleRatingChange('communication', rating)
          )}
        </View>

        {/* Recommendation */}
        <View style={styles.recommendationSection}>
          <Text style={[styles.inputLabel, { color: theme.colors.text }]}>
            Would you recommend this service?
          </Text>
          <View style={styles.recommendationButtons}>
            <TouchableOpacity
              style={[
                styles.recommendButton,
                {
                  backgroundColor: reviewData.wouldRecommend ? theme.colors.primary : theme.colors.surface,
                  borderColor: theme.colors.border,
                }
              ]}
              onPress={() => handleInputChange('wouldRecommend', true)}
            >
              <Text style={[
                styles.recommendButtonText,
                { color: reviewData.wouldRecommend ? 'white' : theme.colors.text }
              ]}>
                Yes
              </Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[
                styles.recommendButton,
                {
                  backgroundColor: !reviewData.wouldRecommend ? theme.colors.error : theme.colors.surface,
                  borderColor: theme.colors.border,
                }
              ]}
              onPress={() => handleInputChange('wouldRecommend', false)}
            >
              <Text style={[
                styles.recommendButtonText,
                { color: !reviewData.wouldRecommend ? 'white' : theme.colors.text }
              ]}>
                No
              </Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Submit Button */}
        <TouchableOpacity
          style={[styles.submitButton, { backgroundColor: theme.colors.primary }]}
          onPress={handleSubmitReview}
        >
          <Text style={styles.submitButtonText}>Submit Review</Text>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    padding: 20,
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  serviceName: {
    fontSize: 18,
    fontWeight: '600',
  },
  content: {
    padding: 20,
  },
  ratingSection: {
    marginBottom: 24,
  },
  ratingLabel: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
  },
  starsContainer: {
    flexDirection: 'row',
    gap: 8,
  },
  starButton: {
    padding: 4,
  },
  inputSection: {
    marginBottom: 24,
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
  },
  textInput: {
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    borderWidth: 1,
    borderColor: 'transparent',
  },
  textArea: {
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    borderWidth: 1,
    borderColor: 'transparent',
    minHeight: 100,
    textAlignVertical: 'top',
  },
  detailedRatings: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  recommendationSection: {
    marginBottom: 32,
  },
  recommendationButtons: {
    flexDirection: 'row',
    gap: 12,
  },
  recommendButton: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    borderWidth: 1,
    alignItems: 'center',
  },
  recommendButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
  submitButton: {
    paddingVertical: 16,
    borderRadius: 8,
    alignItems: 'center',
  },
  submitButtonText: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
  },
});
