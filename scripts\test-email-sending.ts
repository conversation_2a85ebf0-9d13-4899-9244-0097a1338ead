#!/usr/bin/env ts-node

/**
 * Test Email Sending Functionality
 * This script tests the actual email sending via AWS SDK and other providers
 */

import { emailService } from '../lib/services/emailService';

const testEmail = '<EMAIL>'; // Change this to your email

async function testEmailSending() {
  console.log('🧪 Testing Email Sending Functionality\n');

  // Test 1: Welcome Signup Email
  console.log('📧 Test 1: Welcome Signup Email');
  try {
    const result1 = await emailService.sendUserWelcomeEmail({
      email: testEmail,
      firstName: 'John',
      lastName: 'Doe',
      type: 'signup',
      isVendor: false
    });
    console.log(`   Result: ${result1 ? '✅ Success' : '❌ Failed'}\n`);
  } catch (error) {
    console.log(`   Error: ${error}\n`);
  }

  // Test 2: Newsletter Welcome Email
  console.log('📧 Test 2: Newsletter Welcome Email');
  try {
    const result2 = await emailService.sendNewsletterWelcomeEmail({
      email: testEmail,
      firstName: 'Jane',
      lastName: 'Smith',
      type: 'newsletter',
      interests: ['PHOTOGRAPHY', 'VENUES'],
      preferences: {
        weddingTips: true,
        vendorRecommendations: true,
        frequency: 'WEEKLY'
      }
    });
    console.log(`   Result: ${result2 ? '✅ Success' : '❌ Failed'}\n`);
  } catch (error) {
    console.log(`   Error: ${error}\n`);
  }

  // Test 3: Booking Confirmation Email
  console.log('📧 Test 3: Booking Confirmation Email');
  try {
    const result3 = await emailService.sendBookingConfirmationEmail({
      email: testEmail,
      userName: 'There',
      bookingId: 'booking_test_123',
      entityName: 'Dream Venue',
      entityType: 'venue',
      eventDate: '2024-12-25',
      eventTime: '6:00 PM',
      amount: '₹50,000',
      status: 'confirmed'
    });
    console.log(`   Result: ${result3 ? '✅ Success' : '❌ Failed'}\n`);
  } catch (error) {
    console.log(`   Error: ${error}\n`);
  }

  console.log('🎯 Email Sending Tests Complete!');
  console.log('\n📋 Check your email inbox for test emails');
  console.log('📊 Check the database for email logs');
  console.log('🔍 Check console for detailed results');
}

// Main execution
async function main() {
  try {
    await testEmailSending();
  } catch (error) {
    console.error('❌ Test execution failed:', error);
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  main();
}

export { testEmailSending }; 