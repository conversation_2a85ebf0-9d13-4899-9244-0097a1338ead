const { generateClient } = require('aws-amplify/api');
const { listVendors, listVenues, listShops } = require('../src/graphql/queries');
const { updateVendor, updateVenue, updateShop } = require('../src/graphql/mutations');

const client = generateClient({ authMode: 'apiKey' });

// Function to replace Unsplash URLs with placeholders
function replaceUnsplashUrls(obj) {
  if (typeof obj === 'string') {
    return obj.replace(/https:\/\/images\.unsplash\.com\/[^"']*/g, '/placeholder-image.jpg');
  }
  if (Array.isArray(obj)) {
    return obj.map(replaceUnsplashUrls);
  }
  if (obj && typeof obj === 'object') {
    const newObj = {};
    for (const [key, value] of Object.entries(obj)) {
      newObj[key] = replaceUnsplashUrls(value);
    }
    return newObj;
  }
  return obj;
}

async function migrateVendors() {
  console.log('🔄 Migrating Vendor data...');
  try {
    const result = await client.graphql({ query: listVendors });
    const vendors = result.data.listVendors.items;
    
    for (const vendor of vendors) {
      const hasUnsplash = JSON.stringify(vendor).includes('unsplash');
      if (hasUnsplash) {
        const updatedVendor = replaceUnsplashUrls(vendor);
        delete updatedVendor.createdAt;
        delete updatedVendor.updatedAt;
        
        await client.graphql({
          query: updateVendor,
          variables: { input: updatedVendor }
        });
        console.log(`✅ Updated vendor: ${vendor.name}`);
      }
    }
  } catch (error) {
    console.error('❌ Error migrating vendors:', error);
  }
}

async function migrateVenues() {
  console.log('🔄 Migrating Venue data...');
  try {
    const result = await client.graphql({ query: listVenues });
    const venues = result.data.listVenues.items;
    
    for (const venue of venues) {
      const hasUnsplash = JSON.stringify(venue).includes('unsplash');
      if (hasUnsplash) {
        const updatedVenue = replaceUnsplashUrls(venue);
        delete updatedVenue.createdAt;
        delete updatedVenue.updatedAt;
        
        await client.graphql({
          query: updateVenue,
          variables: { input: updatedVenue }
        });
        console.log(`✅ Updated venue: ${venue.name}`);
      }
    }
  } catch (error) {
    console.error('❌ Error migrating venues:', error);
  }
}

async function migrateShops() {
  console.log('🔄 Migrating Shop data...');
  try {
    const result = await client.graphql({ query: listShops });
    const shops = result.data.listShops.items;
    
    for (const shop of shops) {
      const hasUnsplash = JSON.stringify(shop).includes('unsplash');
      if (hasUnsplash) {
        const updatedShop = replaceUnsplashUrls(shop);
        delete updatedShop.createdAt;
        delete updatedShop.updatedAt;
        
        await client.graphql({
          query: updateShop,
          variables: { input: updatedShop }
        });
        console.log(`✅ Updated shop: ${shop.name}`);
      }
    }
  } catch (error) {
    console.error('❌ Error migrating shops:', error);
  }
}

async function runMigration() {
  console.log('🚀 Starting Unsplash URL migration...');
  
  await migrateVendors();
  await migrateVenues();
  await migrateShops();
  
  console.log('✅ Migration completed!');
}

// Run migration if called directly
if (require.main === module) {
  runMigration().catch(console.error);
}

module.exports = { runMigration };