'use client'
import { useState, useRef, useEffect } from 'react';
import Image from 'next/image';
import { listVendors, getVendor } from '../../../src/graphql/queries'
import { createVendor, updateVendor, deleteVendor } from '../../../src/graphql/mutations';
import API from '@aws-amplify/api';
import { graphqlOperation } from '@aws-amplify/api-graphql';
import { generateClient } from '@aws-amplify/api';
import { useAuth } from '@/contexts/AuthContext';
import { useRouter } from 'next/navigation';

const client = generateClient();


const CATEGORIES = [
  'திருமண புகைப்படக்காரர்கள் (Photographers)',
  'நாதஸ்வரம் கலைஞர்கள் (Nadaswaram Artists)',
  'சமையல் (Catering)',
  'அலங்காரம் (Decoration)',
  'பட்டு சேலை கடைகள் (Saree Stores)',
  'மற்றவை (Others)'
]

export default function VendorDashboard() {
  const { user, isAuthenticated, isLoading } = useAuth()
  const router = useRouter()

  const [profile, setProfile] = useState({
    name: '',
    description: '',
    contact: '',
    address: '',
    city: '',
    category: '',
    profilePhoto: '',
  })
  const [gallery, setGallery] = useState<string[]>([])
  const [services, setServices] = useState([{ name: '', price: '' }])
  const fileInputRef = useRef<HTMLInputElement>(null)
  const galleryInputRef = useRef<HTMLInputElement>(null)
  const [vendors, setVendors] = useState<any[]>([])
  const [editingId, setEditingId] = useState<string | null>(null)
  const [loading, setLoading] = useState(true)
  const [saveLoading, setSaveLoading] = useState(false)
  const [error, setError] = useState('')
  const [success, setSuccess] = useState('')

  // Redirect if not authenticated
  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push('/login')
    }
  }, [isAuthenticated, isLoading, router])

  // Profile photo upload
  const handleProfilePhoto = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      const url = URL.createObjectURL(file)
      setProfile(p => ({ ...p, profilePhoto: url }))
    }
  }

  // Gallery upload
  const handleGalleryUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || [])
    const urls = files.map(file => URL.createObjectURL(file))
    setGallery(g => [...g, ...urls])
  }

  // Remove gallery image
  const removeGalleryImg = (idx: number) => {
    setGallery(g => g.filter((_, i) => i !== idx))
  }

  // Service change
  const handleServiceChange = (idx: number, field: 'name' | 'price', value: string) => {
    setServices(s => s.map((item, i) => i === idx ? { ...item, [field]: value } : item))
  }

  // Add service
  const addService = () => {
    setServices(s => [...s, { name: '', price: '' }])
  }

  // Remove service
  const removeService = (idx: number) => {
    setServices(s => s.filter((_, i) => i !== idx))
  }

  const fetchVendors = async () => {
    if (!user?.userId) return

    setLoading(true)
    setError('')
    try {
      const result: any = await client.graphql(graphqlOperation(listVendors))
      // Filter vendors by current user (temporary solution until schema is updated)
      const userVendors = result.data.listVendors.items.filter((vendor: any) =>
        vendor.userId === user.userId
      )
      setVendors(userVendors)
    } catch (err) {
      console.error('Error fetching vendors:', err)
      setError('Failed to fetch vendor data')
    }
    setLoading(false)
  }

  useEffect(() => {
    if (user?.userId) {
      fetchVendors()
    }
  }, [user?.userId])

  const handleSave = async () => {
    if (!user?.userId) {
      setError('User not authenticated')
      return
    }

    // Validation
    if (!profile.name.trim()) {
      setError('Vendor name is required')
      return
    }
    if (!profile.category) {
      setError('Category is required')
      return
    }
    if (!profile.contact.trim()) {
      setError('Contact information is required')
      return
    }

    setSaveLoading(true)
    setError('')
    setSuccess('')

    const vendorInput = {
      ...profile,
      gallery,
      services: services.filter(s => s.name.trim() && s.price.trim()), // Filter out empty services
      userId: user.userId, // Associate with current user
      createdAt: editingId ? undefined : new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    }

    try {
      if (editingId) {
        await client.graphql(graphqlOperation(updateVendor, {
          input: {
            id: editingId,
            ...vendorInput
          }
        }))
        setSuccess('Vendor updated successfully!')
      } else {
        await client.graphql(graphqlOperation(createVendor, {
          input: vendorInput
        }))
        setSuccess('Vendor created successfully!')
      }

      // Reset form
      setProfile({
        name: '',
        description: '',
        contact: '',
        address: '',
        city: '',
        category: '',
        profilePhoto: '',
      })
      setGallery([])
      setServices([{ name: '', price: '' }])
      setEditingId(null)

      // Refresh vendor list
      fetchVendors()

      // Clear success message after 3 seconds
      setTimeout(() => setSuccess(''), 3000)
    } catch (err: any) {
      console.error('Error saving vendor:', err)
      setError(err.message || 'Failed to save vendor data')
    } finally {
      setSaveLoading(false)
    }
  }

  const handleEdit = (vendor: any) => {
    setProfile({
      name: vendor.name || '',
      description: vendor.description || '',
      contact: vendor.contact || '',
      address: vendor.address || '',
      city: vendor.city || '',
      category: vendor.category || '',
      profilePhoto: vendor.profilePhoto || '',
    })
    setGallery(vendor.gallery || [])
    setServices(vendor.services && vendor.services.length > 0 ? vendor.services : [{ name: '', price: '' }])
    setEditingId(vendor.id)
  }

  const handleDelete = async (id: string) => {
    if (!window.confirm('Are you sure you want to delete this vendor? This action cannot be undone.')) {
      return
    }

    setError('')
    setSuccess('')

    try {
      await client.graphql(graphqlOperation(deleteVendor, { input: { id } }))
      setSuccess('Vendor deleted successfully!')
      fetchVendors()

      // Clear success message after 3 seconds
      setTimeout(() => setSuccess(''), 3000)
    } catch (err: any) {
      console.error('Error deleting vendor:', err)
      setError(err.message || 'Failed to delete vendor')
    }
  }

  // Show loading state while checking authentication
  if (isLoading) {
    return (
      <div className="min-h-screen bg-white flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading...</p>
        </div>
      </div>
    )
  }

  // Don't render if not authenticated (will redirect)
  if (!isAuthenticated) {
    return null
  }

  return (
    <div className="min-h-screen bg-white">
      <section className="py-10 md:py-16">
        <div className="container mx-auto px-4 max-w-3xl">
          <div className="flex justify-between items-center mb-6">
            <h1 className="text-2xl md:text-3xl font-bold">Vendor Dashboard</h1>
            <div className="flex gap-3">
              <a 
                href="/dashboard/vendor/analytics" 
                className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors"
              >
                📊 View Analytics
              </a>
            </div>
          </div>

          {/* User Info */}
          <div className="mb-6 p-4 bg-gray-50 rounded-lg">
            <p className="text-sm text-gray-600">
              Logged in as: <span className="font-semibold">{user?.signInDetails?.loginId}</span>
            </p>
          </div>

          {/* Error Message */}
          {error && (
            <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md text-red-700">
              {error}
            </div>
          )}

          {/* Success Message */}
          {success && (
            <div className="mb-4 p-3 bg-green-50 border border-green-200 rounded-md text-green-700">
              {success}
            </div>
          )}

          <form className="space-y-8">
            {/* Profile Photo */}
            <div className="flex flex-col items-center gap-3">
              <div className="relative w-28 h-28 rounded-full overflow-hidden border bg-gray-100">
                {profile.profilePhoto ? (
                  <Image src={profile.profilePhoto} alt="Profile" fill className="object-cover" />
                ) : (
                  <span className="flex items-center justify-center w-full h-full text-gray-400">No Photo</span>
                )}
              </div>
              <input
                type="file"
                accept="image/*"
                className="hidden"
                ref={fileInputRef}
                onChange={handleProfilePhoto}
              />
              <button
                type="button"
                className="bg-primary text-white px-4 py-2 rounded hover:bg-primary/90"
                onClick={() => fileInputRef.current?.click()}
              >
                Change Profile Photo
              </button>
            </div>
            {/* Vendor Info */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block font-semibold mb-1">Name</label>
                <input className="border rounded px-3 py-2 w-full" value={profile.name} onChange={e => setProfile(p => ({ ...p, name: e.target.value }))} />
              </div>
              <div>
                <label className="block font-semibold mb-1">Category</label>
                <select className="border rounded px-3 py-2 w-full" value={profile.category} onChange={e => setProfile(p => ({ ...p, category: e.target.value }))}>
                  <option value="">Select</option>
                  {CATEGORIES.map(cat => <option key={cat} value={cat}>{cat}</option>)}
                </select>
              </div>
              <div>
                <label className="block font-semibold mb-1">Contact</label>
                <input className="border rounded px-3 py-2 w-full" value={profile.contact} onChange={e => setProfile(p => ({ ...p, contact: e.target.value }))} />
              </div>
              <div>
                <label className="block font-semibold mb-1">City</label>
                <input className="border rounded px-3 py-2 w-full" value={profile.city} onChange={e => setProfile(p => ({ ...p, city: e.target.value }))} />
              </div>
              <div className="md:col-span-2">
                <label className="block font-semibold mb-1">Address</label>
                <input className="border rounded px-3 py-2 w-full" value={profile.address} onChange={e => setProfile(p => ({ ...p, address: e.target.value }))} />
              </div>
              <div className="md:col-span-2">
                <label className="block font-semibold mb-1">Description</label>
                <textarea className="border rounded px-3 py-2 w-full min-h-[80px]" value={profile.description} onChange={e => setProfile(p => ({ ...p, description: e.target.value }))} />
              </div>
            </div>
            {/* Gallery Upload */}
            <div>
              <label className="block font-semibold mb-2">Gallery Photos</label>
              <input
                type="file"
                accept="image/*"
                multiple
                className="hidden"
                ref={galleryInputRef}
                onChange={handleGalleryUpload}
              />
              <button
                type="button"
                className="bg-primary text-white px-4 py-2 rounded hover:bg-primary/90 mb-3"
                onClick={() => galleryInputRef.current?.click()}
              >
                Upload Photos
              </button>
              <div className="flex flex-wrap gap-3 mt-2">
                {gallery.length === 0 && <span className="text-gray-400">No photos uploaded</span>}
                {gallery.map((url, idx) => (
                  <div key={idx} className="relative w-24 h-24 rounded overflow-hidden border bg-gray-100">
                    <Image src={url} alt={`Gallery ${idx + 1}`} fill className="object-cover" />
                    <button
                      type="button"
                      className="absolute top-1 right-1 bg-white/80 rounded-full p-1 text-xs text-red-600 hover:bg-white"
                      onClick={() => removeGalleryImg(idx)}
                      title="Remove"
                    >✕</button>
                  </div>
                ))}
              </div>
            </div>
            {/* Services & Pricing */}
            <div>
              <label className="block font-semibold mb-2">Services & Pricing</label>
              <div className="space-y-3">
                {services.map((s, idx) => (
                  <div key={idx} className="flex gap-2 items-center">
                    <input
                      className="border rounded px-3 py-2 w-full"
                      placeholder="Service Name"
                      value={s.name}
                      onChange={e => handleServiceChange(idx, 'name', e.target.value)}
                    />
                    <input
                      className="border rounded px-3 py-2 w-32 text-right"
                      placeholder="Price (₹)"
                      type="number"
                      value={s.price}
                      onChange={e => handleServiceChange(idx, 'price', e.target.value)}
                    />
                    <button
                      type="button"
                      className="text-red-500 hover:underline text-xs"
                      onClick={() => removeService(idx)}
                      title="Remove"
                    >✕</button>
                  </div>
                ))}
                <button
                  type="button"
                  className="bg-primary text-white px-4 py-2 rounded hover:bg-primary/90"
                  onClick={addService}
                >
                  Add Service
                </button>
              </div>
            </div>
            {/* Save Button */}
            <div className="text-center pt-4">
              <button
                type="button"
                className="bg-primary text-white px-8 py-3 rounded font-bold text-lg disabled:opacity-50 disabled:cursor-not-allowed"
                onClick={handleSave}
                disabled={saveLoading}
              >
                {saveLoading ? 'Saving...' : (editingId ? 'Update Vendor' : 'Add Vendor')}
              </button>
              {editingId && (
                <button
                  type="button"
                  className="ml-2 bg-gray-200 px-4 py-2 rounded hover:bg-gray-300 disabled:opacity-50"
                  disabled={saveLoading}
                  onClick={() => {
                    setProfile({
                      name: '',
                      description: '',
                      contact: '',
                      address: '',
                      city: '',
                      category: '',
                      profilePhoto: '',
                    })
                    setGallery([])
                    setServices([{ name: '', price: '' }])
                    setEditingId(null)
                    setError('')
                    setSuccess('')
                  }}
                >
                  Cancel
                </button>
              )}
            </div>
          </form>
          <h2 className="text-xl font-semibold mb-4 mt-10">Your Vendors</h2>
          {loading ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
              <p className="mt-2 text-gray-600">Loading vendors...</p>
            </div>
          ) : vendors.length === 0 ? (
            <div className="text-center py-8 bg-gray-50 rounded-lg">
              <p className="text-gray-600">No vendors found. Create your first vendor profile above!</p>
            </div>
          ) : (
            <div className="grid gap-4">
              {vendors.map((vendor) => (
                <div key={vendor.id} className="border border-gray-200 p-6 rounded-lg shadow-sm hover:shadow-md transition-shadow">
                  <div className="flex justify-between items-start mb-4">
                    <div className="flex-1">
                      <h3 className="font-bold text-lg text-gray-900">{vendor.name}</h3>
                      <p className="text-sm text-gray-600 mb-2">{vendor.category}</p>
                      <p className="text-gray-700 mb-2">{vendor.description}</p>
                      <div className="text-sm text-gray-600">
                        <p><span className="font-medium">Contact:</span> {vendor.contact}</p>
                        {vendor.address && <p><span className="font-medium">Address:</span> {vendor.address}</p>}
                        {vendor.city && <p><span className="font-medium">City:</span> {vendor.city}</p>}
                      </div>
                      {vendor.services && vendor.services.length > 0 && (
                        <div className="mt-3">
                          <p className="font-medium text-sm text-gray-700 mb-1">Services:</p>
                          <div className="flex flex-wrap gap-2">
                            {vendor.services.map((service: any, index: number) => (
                              <span key={index} className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded">
                                {service.name} - ₹{service.price}
                              </span>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                  <div className="flex gap-2 pt-4 border-t border-gray-100">
                    <button
                      className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded text-sm font-medium transition-colors"
                      onClick={() => handleEdit(vendor)}
                    >
                      Edit
                    </button>
                    <button
                      className="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded text-sm font-medium transition-colors"
                      onClick={() => handleDelete(vendor.id)}
                    >
                      Delete
                    </button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </section>
    </div>
  )
} 