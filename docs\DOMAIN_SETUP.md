# Domain Setup Guide

## Option 1: Direct BigRock DNS Configuration

1. **Login to BigRock Control Panel**
2. **Go to DNS Management**
3. **Add these records:**

```
# Root domain
Type: A
Name: @
Value: [Get from AWS Amplify Console]
TTL: 300

# WWW subdomain  
Type: CNAME
Name: www
Value: [Your-App-ID].amplifyapp.com
TTL: 300
```

## Option 2: Use AWS Route 53 (Recommended)

1. **Create Route 53 Hosted Zone**
2. **Update BigRock Nameservers:**
   - ns-1234.awsdns-12.org
   - ns-5678.awsdns-34.net
   - ns-9012.awsdns-56.co.uk
   - ns-3456.awsdns-78.com

3. **Configure in Amplify:**
   - Add custom domain
   - Select Route 53 hosted zone
   - Auto-configure SSL certificate

## Verification
- Wait 24-48 hours for DNS propagation
- Check with: `nslookup BookmyFestive.com`
- Verify SSL certificate is active