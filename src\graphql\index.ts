// GraphQL Types for Blog System and Review System

// Review System Enums
export enum ReviewCategory {
  PLATFORM = 'PLATFORM',
  PRODUCT = 'PRODUCT',
  VENDOR = 'VENDOR',
  VENUE = 'VENUE',
  SHOP = 'SHOP'
}

export enum ReviewStatus {
  PENDING = 'PENDING',
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED'
}

export enum ReviewTarget {
  ADMIN = 'ADMIN',
  VENDOR = 'VENDOR'
}

export enum EntityType {
  SHOP = 'SHOP',
  VENUE = 'VENUE',
  VENDOR = 'VENDOR',
  PLATFORM = 'PLATFORM'
}

// Blog System Enums
export enum BlogCategory {
  WEDDING_PLANNING = 'WEDDING_PLANNING',
  VENUE_SELECTION = 'VENUE_SELECTION',
  PHOTOGRAPHY_VIDEOGRAPHY = 'PHOTOGRAPHY_VIDEOGRAPHY',
  CATERING_FOOD = 'CATERING_FOOD',
  DECORATIONS_THEMES = 'DECORATIONS_THEMES',
  BUDGET_FINANCE = 'BUDGET_FINANCE',
  FASHION_STYLE = 'FASHION_STYLE',
  REAL_WEDDINGS = 'REAL_WEDDINGS',
  EXPERT_TIPS = 'EXPERT_TIPS',
  VENDOR_SPOTLIGHT = 'VENDOR_SPOTLIGHT'
}

export enum BlogStatus {
  DRAFT = 'DRAFT',
  PUBLISHED = 'PUBLISHED',
  ARCHIVED = 'ARCHIVED'
}

export enum AuthorType {
  VENDOR = 'VENDOR',
  ADMIN = 'ADMIN',
  EXPERT = 'EXPERT'
}

export interface Blog {
  id: string;
  title: string;
  content: string;
  excerpt?: string;
  category: BlogCategory;
  authorId: string;
  authorName: string;
  authorType: AuthorType;
  featuredImage?: string;
  tags?: string[];
  status: BlogStatus;
  views?: number;
  likes?: number;
  comments?: number;
  isPinned?: boolean;
  isFeatured?: boolean;
  publishedAt?: string;
  createdAt: string;
  updatedAt: string;
}

export interface CreateBlogInput {
  title: string;
  content: string;
  excerpt?: string;
  category: BlogCategory;
  authorId: string;
  authorName: string;
  authorType: AuthorType;
  featuredImage?: string;
  tags?: string[];
  status?: BlogStatus;
  views?: number;
  likes?: number;
  comments?: number;
  isPinned?: boolean;
  isFeatured?: boolean;
  publishedAt?: string;
}

export interface UpdateBlogInput {
  id: string;
  title?: string;
  content?: string;
  excerpt?: string;
  category?: BlogCategory;
  authorId?: string;
  authorName?: string;
  authorType?: AuthorType;
  featuredImage?: string;
  tags?: string[];
  status?: BlogStatus;
  views?: number;
  likes?: number;
  comments?: number;
  isPinned?: boolean;
  isFeatured?: boolean;
  publishedAt?: string;
}

export interface DeleteBlogInput {
  id: string;
}

export interface ListBlogsQuery {
  items: Blog[];
  nextToken?: string;
}

// GraphQL Operations
export const createBlog = /* GraphQL */ `
  mutation CreateBlog($input: CreateBlogInput!) {
    createBlog(input: $input) {
      id
      title
      content
      excerpt
      category
      authorId
      authorName
      authorType
      featuredImage
      tags
      status
      views
      likes
      comments
      isPinned
      isFeatured
      publishedAt
      createdAt
      updatedAt
    }
  }
`;

export const updateBlog = /* GraphQL */ `
  mutation UpdateBlog($input: UpdateBlogInput!) {
    updateBlog(input: $input) {
      id
      title
      content
      excerpt
      category
      authorId
      authorName
      authorType
      featuredImage
      tags
      status
      views
      likes
      comments
      isPinned
      isFeatured
      publishedAt
      createdAt
      updatedAt
    }
  }
`;

export const deleteBlog = /* GraphQL */ `
  mutation DeleteBlog($input: DeleteBlogInput!) {
    deleteBlog(input: $input) {
      id
      title
      content
      excerpt
      category
      authorId
      authorName
      authorType
      featuredImage
      tags
      status
      views
      likes
      comments
      isPinned
      isFeatured
      publishedAt
      createdAt
      updatedAt
    }
  }
`;

export const getBlog = /* GraphQL */ `
  query GetBlog($id: ID!) {
    getBlog(id: $id) {
      id
      title
      content
      excerpt
      category
      authorId
      authorName
      authorType
      featuredImage
      tags
      status
      views
      likes
      comments
      isPinned
      isFeatured
      publishedAt
      createdAt
      updatedAt
    }
  }
`;

export const listBlogs = /* GraphQL */ `
  query ListBlogs(
    $filter: ModelBlogFilterInput
    $limit: Int
    $nextToken: String
  ) {
    listBlogs(filter: $filter, limit: $limit, nextToken: $nextToken) {
      items {
        id
        title
        content
        excerpt
        category
        authorId
        authorName
        authorType
        featuredImage
        tags
        status
        views
        likes
        comments
        isPinned
        isFeatured
        publishedAt
        createdAt
        updatedAt
      }
      nextToken
    }
  }
`;

// Review System Interfaces
export interface CreateReviewInput {
  userId: string;
  name: string;
  email: string;
  location?: string;
  weddingDate?: string;
  category: ReviewCategory;
  rating: number;
  title: string;
  review: string;
  wouldRecommend: boolean;
  verified?: boolean;
  status: ReviewStatus;
  entityType?: EntityType;
  entityId?: string;
  userEntityComposite?: string;
  serviceRating?: number;
  valueRating?: number;
  communicationRating?: number;
  professionalismRating?: number;
  images?: string[];
  helpfulCount?: number;
  purchaseVerified?: boolean;
  reviewHelpfulUsers?: string[];
  vendorResponse?: string;
  responseDate?: string;
  reviewTarget: ReviewTarget;
  adminNotes?: string;
  moderatedBy?: string;
  moderatedAt?: string;
}

export interface UpdateReviewInput {
  id: string;
  userId?: string;
  name?: string;
  email?: string;
  location?: string;
  weddingDate?: string;
  category?: ReviewCategory;
  rating?: number;
  title?: string;
  review?: string;
  wouldRecommend?: boolean;
  verified?: boolean;
  status?: ReviewStatus;
  entityType?: EntityType;
  entityId?: string;
  userEntityComposite?: string;
  serviceRating?: number;
  valueRating?: number;
  communicationRating?: number;
  professionalismRating?: number;
  images?: string[];
  helpfulCount?: number;
  purchaseVerified?: boolean;
  reviewHelpfulUsers?: string[];
  vendorResponse?: string;
  responseDate?: string;
  reviewTarget?: ReviewTarget;
  adminNotes?: string;
  moderatedBy?: string;
  moderatedAt?: string;
}