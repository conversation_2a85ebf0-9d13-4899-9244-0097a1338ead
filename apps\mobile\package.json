{"name": "mobile", "version": "1.0.0", "main": "index.ts", "scripts": {"start": "expo start", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web"}, "dependencies": {"@aws-amplify/react-native": "^1.1.10", "@craftzdog/react-native-buffer": "^6.1.0", "@expo/metro-runtime": "~5.0.4", "@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "2.1.2", "@react-native-community/datetimepicker": "8.4.1", "@react-native-community/netinfo": "^11.4.1", "@react-navigation/bottom-tabs": "^7.4.2", "@react-navigation/drawer": "^7.5.3", "@react-navigation/native": "^7.1.14", "@react-navigation/stack": "^7.4.2", "aws-amplify": "^6.15.4", "expo": "~53.0.20", "expo-camera": "^16.1.11", "expo-constants": "^17.1.7", "expo-device": "^7.1.4", "expo-image": "^2.4.0", "expo-location": "^18.1.6", "expo-notifications": "^0.31.4", "expo-sharing": "^13.1.5", "expo-status-bar": "~2.2.3", "i18next": "^25.3.2", "lottie-react-native": "7.2.2", "react": "^19.1.0", "react-dom": "^19.1.0", "react-i18next": "^15.6.1", "react-native": "0.79.5", "react-native-animatable": "^1.4.0", "react-native-chart-kit": "^6.12.0", "react-native-crypto": "^2.2.1", "react-native-gesture-handler": "~2.24.0", "react-native-get-random-values": "^1.11.0", "react-native-haptic-feedback": "^2.3.3", "react-native-mmkv": "^3.3.0", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-svg": "15.11.2", "react-native-toast-message": "^2.3.3", "react-native-url-polyfill": "^2.0.0", "react-native-web": "^0.20.0", "react-native-worklets": "^0.4.0", "stream-browserify": "^3.0.0", "uuid": "^8.3.2", "victory-native": "^41.17.4", "expo-haptics": "~14.1.4"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "typescript": "~5.8.3"}, "resolutions": {"uuid": "^8.3.2"}, "private": true}