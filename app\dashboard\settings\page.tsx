'use client'
import { useState } from 'react'

export default function DashboardSettings() {
  const [notifications, setNotifications] = useState({
    email: true,
    sms: false,
    push: false,
  })
  const [language, setLanguage] = useState('en')

  return (
    <div className="max-w-3xl mx-auto px-4 py-10">
      <h1 className="text-2xl md:text-3xl font-bold mb-8 text-center flex items-center justify-center gap-2 text-primary">Settings</h1>
      <div className="max-w-2xl mx-auto bg-white border border-gray-100 rounded-xl p-8 shadow-sm">
        <form className="space-y-8">
          <div>
            <label className="block font-semibold mb-3 text-gray-700">Notification Preferences</label>
            <div className="space-y-3">
              <label className="flex items-center gap-3 text-gray-600">
                <input type="checkbox" checked={notifications.email} onChange={e => setNotifications(n => ({ ...n, email: e.target.checked }))} className="accent-primary w-5 h-5 rounded" />
                Email Notifications
              </label>
              <label className="flex items-center gap-3 text-gray-600">
                <input type="checkbox" checked={notifications.sms} onChange={e => setNotifications(n => ({ ...n, sms: e.target.checked }))} className="accent-primary w-5 h-5 rounded" />
                SMS Notifications
              </label>
              <label className="flex items-center gap-3 text-gray-600">
                <input type="checkbox" checked={notifications.push} onChange={e => setNotifications(n => ({ ...n, push: e.target.checked }))} className="accent-primary w-5 h-5 rounded" />
                Push Notifications
              </label>
            </div>
          </div>
          <div>
            <label className="block font-semibold mb-3 text-gray-700">Language</label>
            <select className="border border-gray-200 rounded px-4 py-2 w-full focus:ring-2 focus:ring-primary focus:border-primary transition text-gray-700 bg-gray-50" value={language} onChange={e => setLanguage(e.target.value)}>
              <option value="en">English</option>
              <option value="ta">தமிழ் (Tamil)</option>
            </select>
          </div>
          <div className="text-center pt-2">
            <button type="button" className="bg-primary text-white px-8 py-3 rounded font-bold text-lg opacity-60 cursor-not-allowed" disabled>
              Save (Demo Only)
            </button>
          </div>
        </form>
      </div>
    </div>
  )
} 