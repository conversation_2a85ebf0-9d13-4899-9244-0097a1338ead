import { useState, useEffect, useCallback } from 'react';
import { enhancedGraphQLService, QueryOptions } from '../services/enhancedGraphQLService';
import { useOffline } from '../providers/OfflineProvider';

export interface UseOfflineQueryOptions extends QueryOptions {
  enabled?: boolean;
  refetchOnReconnect?: boolean;
  staleTime?: number;
  retryOnError?: boolean;
  maxRetries?: number;
}

export interface UseOfflineQueryResult<T> {
  data: T | null;
  loading: boolean;
  error: Error | null;
  refetch: () => Promise<void>;
  isStale: boolean;
  isCached: boolean;
  lastFetched: number | null;
}

export function useOfflineQuery<T>(
  query: string,
  variables: any = {},
  options: UseOfflineQueryOptions = {}
): UseOfflineQueryResult<T> {
  const {
    enabled = true,
    refetchOnReconnect = true,
    staleTime = 5 * 60 * 1000, // 5 minutes
    retryOnError = true,
    maxRetries = 3,
    ...queryOptions
  } = options;

  const { isOffline, networkState } = useOffline();
  
  const [data, setData] = useState<T | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const [lastFetched, setLastFetched] = useState<number | null>(null);
  const [isCached, setIsCached] = useState(false);
  const [retryCount, setRetryCount] = useState(0);

  const executeQuery = useCallback(async (forceRefresh = false) => {
    if (!enabled) return;

    setLoading(true);
    setError(null);

    try {
      const result = await enhancedGraphQLService.query<T>(
        query,
        variables,
        {
          ...queryOptions,
          forceRefresh,
        }
      );

      setData(result);
      setLastFetched(Date.now());
      setIsCached(false); // Fresh data
      setRetryCount(0);
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Unknown error');
      setError(error);

      // Retry logic
      if (retryOnError && retryCount < maxRetries && !isOffline) {
        setTimeout(() => {
          setRetryCount(prev => prev + 1);
          executeQuery(forceRefresh);
        }, Math.pow(2, retryCount) * 1000); // Exponential backoff
      } else {
        // Try to get cached data as fallback
        try {
          const cachedResult = await enhancedGraphQLService.query<T>(
            query,
            variables,
            {
              ...queryOptions,
              useCache: true,
              forceRefresh: false,
            }
          );
          
          if (cachedResult) {
            setData(cachedResult);
            setIsCached(true);
            setError(null); // Clear error if we have cached data
          }
        } catch (cacheError) {
          console.error('Failed to get cached data:', cacheError);
        }
      }
    } finally {
      setLoading(false);
    }
  }, [
    enabled,
    query,
    variables,
    queryOptions,
    retryOnError,
    maxRetries,
    retryCount,
    isOffline,
  ]);

  const refetch = useCallback(async () => {
    await executeQuery(true);
  }, [executeQuery]);

  // Initial fetch
  useEffect(() => {
    if (enabled) {
      executeQuery();
    }
  }, [executeQuery, enabled]);

  // Refetch when coming back online
  useEffect(() => {
    if (refetchOnReconnect && !isOffline && networkState.isConnected && data) {
      // Only refetch if data is stale
      const isStale = lastFetched ? Date.now() - lastFetched > staleTime : true;
      if (isStale) {
        executeQuery();
      }
    }
  }, [isOffline, networkState.isConnected, refetchOnReconnect, data, lastFetched, staleTime, executeQuery]);

  const isStale = lastFetched ? Date.now() - lastFetched > staleTime : false;

  return {
    data,
    loading,
    error,
    refetch,
    isStale,
    isCached,
    lastFetched,
  };
}

// Hook for mutations with offline support
export interface UseOfflineMutationOptions {
  onSuccess?: (data: any) => void;
  onError?: (error: Error) => void;
  optimistic?: boolean;
  queueOffline?: boolean;
  priority?: 'low' | 'normal' | 'high';
  maxRetries?: number;
}

export interface UseOfflineMutationResult<T> {
  mutate: (variables?: any) => Promise<T | null>;
  loading: boolean;
  error: Error | null;
  data: T | null;
  reset: () => void;
}

export function useOfflineMutation<T>(
  mutation: string,
  options: UseOfflineMutationOptions = {}
): UseOfflineMutationResult<T> {
  const {
    onSuccess,
    onError,
    optimistic = false,
    queueOffline = true,
    priority = 'normal',
    maxRetries = 3,
  } = options;

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const [data, setData] = useState<T | null>(null);

  const mutate = useCallback(async (variables: any = {}): Promise<T | null> => {
    setLoading(true);
    setError(null);

    try {
      const result = await enhancedGraphQLService.mutate<T>(
        mutation,
        variables,
        {
          optimistic,
          queueOffline,
          priority,
          maxRetries,
        }
      );

      setData(result);
      
      if (onSuccess) {
        onSuccess(result);
      }

      return result;
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Unknown error');
      setError(error);
      
      if (onError) {
        onError(error);
      }

      throw error;
    } finally {
      setLoading(false);
    }
  }, [mutation, optimistic, queueOffline, priority, maxRetries, onSuccess, onError]);

  const reset = useCallback(() => {
    setLoading(false);
    setError(null);
    setData(null);
  }, []);

  return {
    mutate,
    loading,
    error,
    data,
    reset,
  };
}

// Hook for infinite queries (pagination)
export interface UseOfflineInfiniteQueryOptions extends UseOfflineQueryOptions {
  getNextPageParam?: (lastPage: any, allPages: any[]) => any;
  initialPageParam?: any;
}

export interface UseOfflineInfiniteQueryResult<T> {
  data: T[];
  loading: boolean;
  error: Error | null;
  hasNextPage: boolean;
  fetchNextPage: () => Promise<void>;
  refetch: () => Promise<void>;
  isStale: boolean;
  isCached: boolean;
}

export function useOfflineInfiniteQuery<T>(
  query: string,
  baseVariables: any = {},
  options: UseOfflineInfiniteQueryOptions = {}
): UseOfflineInfiniteQueryResult<T> {
  const {
    getNextPageParam = (lastPage: any) => lastPage?.nextToken,
    initialPageParam = null,
    ...queryOptions
  } = options;

  const [pages, setPages] = useState<T[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const [nextPageParam, setNextPageParam] = useState(initialPageParam);
  const [isCached, setIsCached] = useState(false);

  const fetchPage = useCallback(async (pageParam: any = null, isRefetch = false) => {
    setLoading(true);
    setError(null);

    try {
      const variables = {
        ...baseVariables,
        nextToken: pageParam,
      };

      const result = await enhancedGraphQLService.query<any>(
        query,
        variables,
        queryOptions
      );

      if (isRefetch) {
        setPages([result]);
        setNextPageParam(getNextPageParam(result, [result]));
      } else {
        setPages(prev => [...prev, result]);
        setNextPageParam(getNextPageParam(result, [...pages, result]));
      }

      setIsCached(false);
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Unknown error');
      setError(error);
    } finally {
      setLoading(false);
    }
  }, [query, baseVariables, queryOptions, getNextPageParam, pages]);

  const fetchNextPage = useCallback(async () => {
    if (nextPageParam && !loading) {
      await fetchPage(nextPageParam);
    }
  }, [nextPageParam, loading, fetchPage]);

  const refetch = useCallback(async () => {
    setPages([]);
    setNextPageParam(initialPageParam);
    await fetchPage(initialPageParam, true);
  }, [fetchPage, initialPageParam]);

  // Initial fetch
  useEffect(() => {
    if (queryOptions.enabled !== false) {
      fetchPage(initialPageParam, true);
    }
  }, [fetchPage, initialPageParam, queryOptions.enabled]);

  const data = pages.flatMap(page => page?.items || []);
  const hasNextPage = !!nextPageParam;
  const isStale = false; // Simplified for now

  return {
    data,
    loading,
    error,
    hasNextPage,
    fetchNextPage,
    refetch,
    isStale,
    isCached,
  };
}
