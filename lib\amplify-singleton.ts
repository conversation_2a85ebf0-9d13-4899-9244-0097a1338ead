'use client';

import { Amplify } from 'aws-amplify';
import awsExports from '@/src/aws-exports';

// Singleton pattern to ensure Amplify is configured only once
let isConfigured = false;

export const configureAmplify = () => {
  if (!isConfigured && typeof window !== 'undefined') {
    Amplify.configure(awsExports);
    isConfigured = true;
  }
};

// Auto-configure on import
configureAmplify();

export default awsExports;
