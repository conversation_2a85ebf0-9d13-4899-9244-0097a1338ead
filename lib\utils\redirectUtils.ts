/**
 * Utility functions for handling redirects after authentication
 */

const REDIRECT_KEY = 'auth_redirect_url';

export class RedirectUtils {
  /**
   * Store the current URL to redirect back to after login
   */
  static storeRedirectUrl(url?: string): void {
    if (typeof window === 'undefined') return;
    
    const redirectUrl = url || window.location.pathname + window.location.search;
    
    // Don't store login/signup/auth pages as redirect URLs
    const authPages = ['/login', '/signup', '/register', '/forgot-password', '/reset-password'];
    const isAuthPage = authPages.some(page => redirectUrl.startsWith(page));
    
    if (!isAuthPage && redirectUrl !== '/') {
      localStorage.setItem(REDIRECT_KEY, redirectUrl);
    }
  }

  /**
   * Get the stored redirect URL and clear it
   */
  static getAndClearRedirectUrl(): string | null {
    if (typeof window === 'undefined') return null;
    
    const redirectUrl = localStorage.getItem(REDIRECT_KEY);
    if (redirectUrl) {
      localStorage.removeItem(REDIRECT_KEY);
      return redirectUrl;
    }
    return null;
  }

  /**
   * Clear the stored redirect URL without returning it
   */
  static clearRedirectUrl(): void {
    if (typeof window === 'undefined') return;
    localStorage.removeItem(REDIRECT_KEY);
  }

  /**
   * Check if there's a stored redirect URL
   */
  static hasRedirectUrl(): boolean {
    if (typeof window === 'undefined') return false;
    return localStorage.getItem(REDIRECT_KEY) !== null;
  }

  /**
   * Get the redirect URL from query parameters
   */
  static getRedirectFromQuery(): string | null {
    if (typeof window === 'undefined') return null;
    
    const urlParams = new URLSearchParams(window.location.search);
    return urlParams.get('redirect') || urlParams.get('returnTo');
  }

  /**
   * Store redirect URL from query parameter
   */
  static storeRedirectFromQuery(): void {
    const redirectFromQuery = this.getRedirectFromQuery();
    if (redirectFromQuery) {
      this.storeRedirectUrl(redirectFromQuery);
    }
  }
}
