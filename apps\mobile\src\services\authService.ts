import AsyncStorage from '@react-native-async-storage/async-storage';
import { AuthState, LoginCredentials, SignupData, User, UserProfile } from '../shared/types';

class AuthService {
  private storageKey = 'thirumanam-auth';

  async getAuthState(): Promise<AuthState> {
    try {
      const authData = await AsyncStorage.getItem(this.storageKey);
      if (authData) {
        const parsed = JSON.parse(authData);
        return {
          isAuthenticated: parsed.isAuthenticated || false,
          isLoading: false,
          user: parsed.user || null,
          userProfile: parsed.userProfile || null,
          userType: parsed.userType || null,
          error: null,
        };
      }
    } catch (error) {
      console.error('Error loading auth state:', error);
    }

    return {
      isAuthenticated: false,
      isLoading: false,
      user: null,
      userProfile: null,
      userType: null,
      error: null,
    };
  }

  async saveAuthState(authState: Partial<AuthState>): Promise<void> {
    try {
      const currentState = await this.getAuthState();
      const newState = { ...currentState, ...authState };
      await AsyncStorage.setItem(this.storageKey, JSON.stringify(newState));
    } catch (error) {
      console.error('Error saving auth state:', error);
    }
  }

  async clearAuthState(): Promise<void> {
    try {
      await AsyncStorage.removeItem(this.storageKey);
      await AsyncStorage.removeItem('thirumanam-token');
    } catch (error) {
      console.error('Error clearing auth state:', error);
    }
  }

  async login(credentials: LoginCredentials): Promise<AuthState> {
    try {
      // Mock login - replace with actual API call
      await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate API delay
      
      // Mock successful login
      const mockUser: User = {
        id: '1',
        email: credentials.email,
        fullName: 'John Doe',
        phone: '+91 9876543210',
        role: 'customer',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      const mockProfile: UserProfile = {
        ...mockUser,
        city: 'Chennai',
        state: 'Tamil Nadu',
        weddingDate: '2024-12-15',
        partnerName: 'Jane Doe',
        budget: 500000,
      };

      const authState: AuthState = {
        isAuthenticated: true,
        isLoading: false,
        user: mockUser,
        userProfile: mockProfile,
        userType: 'customer',
        error: null,
      };

      await this.saveAuthState(authState);
      await AsyncStorage.setItem('thirumanam-token', 'mock-jwt-token');
      
      return authState;
    } catch (error) {
      const errorState: AuthState = {
        isAuthenticated: false,
        isLoading: false,
        user: null,
        userProfile: null,
        userType: null,
        error: error instanceof Error ? error.message : 'Login failed',
      };
      
      throw error;
    }
  }

  async signup(signupData: SignupData): Promise<AuthState> {
    try {
      // Mock signup - replace with actual API call
      await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate API delay
      
      const mockUser: User = {
        id: '1',
        email: signupData.email,
        fullName: signupData.fullName,
        phone: signupData.phone,
        role: signupData.userType || 'customer',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      const mockProfile: UserProfile = {
        ...mockUser,
      };

      const authState: AuthState = {
        isAuthenticated: true,
        isLoading: false,
        user: mockUser,
        userProfile: mockProfile,
        userType: signupData.userType || 'customer',
        error: null,
      };

      await this.saveAuthState(authState);
      await AsyncStorage.setItem('thirumanam-token', 'mock-jwt-token');
      
      return authState;
    } catch (error) {
      const errorState: AuthState = {
        isAuthenticated: false,
        isLoading: false,
        user: null,
        userProfile: null,
        userType: null,
        error: error instanceof Error ? error.message : 'Signup failed',
      };
      
      throw error;
    }
  }

  async logout(): Promise<void> {
    await this.clearAuthState();
  }

  async initializeAuth(): Promise<AuthState> {
    try {
      const token = await AsyncStorage.getItem('thirumanam-token');
      
      if (token) {
        // Token exists, return stored auth state
        return await this.getAuthState();
      } else {
        // No token, return unauthenticated state
        return {
          isAuthenticated: false,
          isLoading: false,
          user: null,
          userProfile: null,
          userType: null,
          error: null,
        };
      }
    } catch (error) {
      console.error('Error initializing auth:', error);
      return {
        isAuthenticated: false,
        isLoading: false,
        user: null,
        userProfile: null,
        userType: null,
        error: null,
      };
    }
  }
}

export const authService = new AuthService();
export default AuthService;
