/* eslint-disable */
// this is an auto generated file. This will be overwritten

export const getVendor = /* GraphQL */ `
  query GetVendor($id: ID!) {
    getVendor(id: $id) {
      id
      userId
      name
      description
      contact
      email
      address
      city
      state
      pincode
      website
      category
      profilePhoto
      gallery
      services {
        name
        price
        description
        __typename
      }
      socialMedia {
        facebook
        instagram
        youtube
        __typename
      }
      experience
      events
      responseTime
      rating
      reviewCount
      verified
      featured
      availability
      priceRange
      specializations
      awards
      languages
      coverage
      equipment
      status
      createdAt
      updatedAt
      __typename
    }
  }
`;
export const listVendors = /* GraphQL */ `
  query ListVendors(
    $filter: ModelVendorFilterInput
    $limit: Int
    $nextToken: String
  ) {
    listVendors(filter: $filter, limit: $limit, nextToken: $nextToken) {
      items {
        id
        userId
        name
        description
        contact
        email
        address
        city
        state
        pincode
        website
        category
        profilePhoto
        gallery
        experience
        events
        responseTime
        rating
        reviewCount
        verified
        featured
        availability
        priceRange
        specializations
        awards
        languages
        coverage
        equipment
        status
        createdAt
        updatedAt
        __typename
      }
      nextToken
      __typename
    }
  }
`;
export const vendorsByUserId = /* GraphQL */ `
  query VendorsByUserId(
    $userId: String!
    $sortDirection: ModelSortDirection
    $filter: ModelVendorFilterInput
    $limit: Int
    $nextToken: String
  ) {
    vendorsByUserId(
      userId: $userId
      sortDirection: $sortDirection
      filter: $filter
      limit: $limit
      nextToken: $nextToken
    ) {
      items {
        id
        userId
        name
        description
        contact
        email
        address
        city
        state
        pincode
        website
        category
        profilePhoto
        gallery
        experience
        events
        responseTime
        rating
        reviewCount
        verified
        featured
        availability
        priceRange
        specializations
        awards
        languages
        coverage
        equipment
        status
        createdAt
        updatedAt
        __typename
      }
      nextToken
      __typename
    }
  }
`;
export const getVenue = /* GraphQL */ `
  query GetVenue($id: ID!) {
    getVenue(id: $id) {
      id
      userId
      name
      description
      type
      capacity
      location
      city
      state
      fullAddress
      pincode
      contactPhone
      contactEmail
      website
      price
      priceRange
      images
      amenities
      spaces {
        name
        capacity
        area
        price
        description
        amenities
        images
        __typename
      }
      packages {
        name
        price
        duration
        description
        includes
        excludes
        terms
        __typename
      }
      socialMedia {
        facebook
        instagram
        youtube
        __typename
      }
      rating
      reviewCount
      bookings
      verified
      featured
      status
      availability
      policies {
        cancellation
        advance
        catering
        decoration
        alcohol
        music
        parking
        __typename
      }
      coordinates {
        latitude
        longitude
        __typename
      }
      operatingHours {
        monday
        tuesday
        wednesday
        thursday
        friday
        saturday
        sunday
        __typename
      }
      createdAt
      updatedAt
      __typename
    }
  }
`;
export const listVenues = /* GraphQL */ `
  query ListVenues(
    $filter: ModelVenueFilterInput
    $limit: Int
    $nextToken: String
  ) {
    listVenues(filter: $filter, limit: $limit, nextToken: $nextToken) {
      items {
        id
        userId
        name
        description
        type
        capacity
        location
        city
        state
        fullAddress
        pincode
        contactPhone
        contactEmail
        website
        price
        priceRange
        images
        amenities
        rating
        reviewCount
        bookings
        verified
        featured
        status
        availability
        createdAt
        updatedAt
        __typename
      }
      nextToken
      __typename
    }
  }
`;
export const venuesByUserId = /* GraphQL */ `
  query VenuesByUserId(
    $userId: String!
    $sortDirection: ModelSortDirection
    $filter: ModelVenueFilterInput
    $limit: Int
    $nextToken: String
  ) {
    venuesByUserId(
      userId: $userId
      sortDirection: $sortDirection
      filter: $filter
      limit: $limit
      nextToken: $nextToken
    ) {
      items {
        id
        userId
        name
        description
        type
        capacity
        location
        city
        state
        fullAddress
        pincode
        contactPhone
        contactEmail
        website
        price
        priceRange
        images
        amenities
        rating
        reviewCount
        bookings
        verified
        featured
        status
        availability
        createdAt
        updatedAt
        __typename
      }
      nextToken
      __typename
    }
  }
`;
export const getShop = /* GraphQL */ `
  query GetShop($id: ID!) {
    getShop(id: $id) {
      id
      userId
      name
      category
      price
      originalPrice
      discount
      stock
      sku
      brand
      featured
      description
      features
      sizes
      colors
      images
      specifications {
        fabric
        work
        occasion
        care
        delivery
        returnPolicy
        __typename
      }
      rating
      reviewCount
      inStock
      status
      createdAt
      updatedAt
      __typename
    }
  }
`;
export const listShops = /* GraphQL */ `
  query ListShops(
    $filter: ModelShopFilterInput
    $limit: Int
    $nextToken: String
  ) {
    listShops(filter: $filter, limit: $limit, nextToken: $nextToken) {
      items {
        id
        userId
        name
        category
        price
        originalPrice
        discount
        stock
        sku
        brand
        featured
        description
        features
        sizes
        colors
        images
        rating
        reviewCount
        inStock
        status
        createdAt
        updatedAt
        __typename
      }
      nextToken
      __typename
    }
  }
`;
export const shopsByUserId = /* GraphQL */ `
  query ShopsByUserId(
    $userId: String!
    $sortDirection: ModelSortDirection
    $filter: ModelShopFilterInput
    $limit: Int
    $nextToken: String
  ) {
    shopsByUserId(
      userId: $userId
      sortDirection: $sortDirection
      filter: $filter
      limit: $limit
      nextToken: $nextToken
    ) {
      items {
        id
        userId
        name
        category
        price
        originalPrice
        discount
        stock
        sku
        brand
        featured
        description
        features
        sizes
        colors
        images
        rating
        reviewCount
        inStock
        status
        createdAt
        updatedAt
        __typename
      }
      nextToken
      __typename
    }
  }
`;
export const getReview = /* GraphQL */ `
  query GetReview($id: ID!) {
    getReview(id: $id) {
      id
      userId
      name
      email
      location
      weddingDate
      category
      rating
      title
      review
      wouldRecommend
      verified
      status
      entityType
      entityId
      userEntityComposite
      serviceRating
      valueRating
      communicationRating
      professionalismRating
      images
      helpfulCount
      purchaseVerified
      reviewHelpfulUsers
      vendorResponse
      responseDate
      reviewTarget
      adminNotes
      moderatedBy
      moderatedAt
      createdAt
      updatedAt
      __typename
    }
  }
`;
export const listReviews = /* GraphQL */ `
  query ListReviews(
    $filter: ModelReviewFilterInput
    $limit: Int
    $nextToken: String
  ) {
    listReviews(filter: $filter, limit: $limit, nextToken: $nextToken) {
      items {
        id
        userId
        name
        email
        location
        weddingDate
        category
        rating
        title
        review
        wouldRecommend
        verified
        status
        entityType
        entityId
        userEntityComposite
        serviceRating
        valueRating
        communicationRating
        professionalismRating
        images
        helpfulCount
        purchaseVerified
        reviewHelpfulUsers
        vendorResponse
        responseDate
        reviewTarget
        adminNotes
        moderatedBy
        moderatedAt
        createdAt
        updatedAt
        __typename
      }
      nextToken
      __typename
    }
  }
`;
export const reviewsByUserId = /* GraphQL */ `
  query ReviewsByUserId(
    $userId: String!
    $sortDirection: ModelSortDirection
    $filter: ModelReviewFilterInput
    $limit: Int
    $nextToken: String
  ) {
    reviewsByUserId(
      userId: $userId
      sortDirection: $sortDirection
      filter: $filter
      limit: $limit
      nextToken: $nextToken
    ) {
      items {
        id
        userId
        name
        email
        location
        weddingDate
        category
        rating
        title
        review
        wouldRecommend
        verified
        status
        entityType
        entityId
        userEntityComposite
        serviceRating
        valueRating
        communicationRating
        professionalismRating
        images
        helpfulCount
        purchaseVerified
        reviewHelpfulUsers
        vendorResponse
        responseDate
        reviewTarget
        adminNotes
        moderatedBy
        moderatedAt
        createdAt
        updatedAt
        __typename
      }
      nextToken
      __typename
    }
  }
`;
export const reviewsByEntityIdAndCreatedAt = /* GraphQL */ `
  query ReviewsByEntityIdAndCreatedAt(
    $entityId: String!
    $createdAt: ModelStringKeyConditionInput
    $sortDirection: ModelSortDirection
    $filter: ModelReviewFilterInput
    $limit: Int
    $nextToken: String
  ) {
    reviewsByEntityIdAndCreatedAt(
      entityId: $entityId
      createdAt: $createdAt
      sortDirection: $sortDirection
      filter: $filter
      limit: $limit
      nextToken: $nextToken
    ) {
      items {
        id
        userId
        name
        email
        location
        weddingDate
        category
        rating
        title
        review
        wouldRecommend
        verified
        status
        entityType
        entityId
        userEntityComposite
        serviceRating
        valueRating
        communicationRating
        professionalismRating
        images
        helpfulCount
        purchaseVerified
        reviewHelpfulUsers
        vendorResponse
        responseDate
        reviewTarget
        adminNotes
        moderatedBy
        moderatedAt
        createdAt
        updatedAt
        __typename
      }
      nextToken
      __typename
    }
  }
`;
export const reviewsByUserEntityComposite = /* GraphQL */ `
  query ReviewsByUserEntityComposite(
    $userEntityComposite: String!
    $sortDirection: ModelSortDirection
    $filter: ModelReviewFilterInput
    $limit: Int
    $nextToken: String
  ) {
    reviewsByUserEntityComposite(
      userEntityComposite: $userEntityComposite
      sortDirection: $sortDirection
      filter: $filter
      limit: $limit
      nextToken: $nextToken
    ) {
      items {
        id
        userId
        name
        email
        location
        weddingDate
        category
        rating
        title
        review
        wouldRecommend
        verified
        status
        entityType
        entityId
        userEntityComposite
        serviceRating
        valueRating
        communicationRating
        professionalismRating
        images
        helpfulCount
        purchaseVerified
        reviewHelpfulUsers
        vendorResponse
        responseDate
        reviewTarget
        adminNotes
        moderatedBy
        moderatedAt
        createdAt
        updatedAt
        __typename
      }
      nextToken
      __typename
    }
  }
`;
export const getContact = /* GraphQL */ `
  query GetContact($id: ID!) {
    getContact(id: $id) {
      id
      name
      email
      phone
      subject
      message
      inquiryType
      status
      priority
      assignedTo
      responseMessage
      respondedAt
      createdAt
      updatedAt
      __typename
    }
  }
`;
export const listContacts = /* GraphQL */ `
  query ListContacts(
    $filter: ModelContactFilterInput
    $limit: Int
    $nextToken: String
  ) {
    listContacts(filter: $filter, limit: $limit, nextToken: $nextToken) {
      items {
        id
        name
        email
        phone
        subject
        message
        inquiryType
        status
        priority
        assignedTo
        responseMessage
        respondedAt
        createdAt
        updatedAt
        __typename
      }
      nextToken
      __typename
    }
  }
`;
export const getInquiry = /* GraphQL */ `
  query GetInquiry($id: ID!) {
    getInquiry(id: $id) {
      id
      vendorUserId
      vendorId
      vendorName
      customerUserId
      customerName
      customerEmail
      customerPhone
      eventDate
      message
      inquiryType
      status
      priority
      budget
      guestCount
      venue
      additionalServices
      preferredContactTime
      responseMessage
      respondedAt
      assignedTo
      followUpDate
      notes
      createdAt
      updatedAt
      __typename
    }
  }
`;
export const listInquiries = /* GraphQL */ `
  query ListInquiries(
    $filter: ModelInquiryFilterInput
    $limit: Int
    $nextToken: String
  ) {
    listInquiries(filter: $filter, limit: $limit, nextToken: $nextToken) {
      items {
        id
        vendorUserId
        vendorId
        vendorName
        customerUserId
        customerName
        customerEmail
        customerPhone
        eventDate
        message
        inquiryType
        status
        priority
        budget
        guestCount
        venue
        additionalServices
        preferredContactTime
        responseMessage
        respondedAt
        assignedTo
        followUpDate
        notes
        createdAt
        updatedAt
        __typename
      }
      nextToken
      __typename
    }
  }
`;
export const inquiriesByVendorUserId = /* GraphQL */ `
  query InquiriesByVendorUserId(
    $vendorUserId: String!
    $sortDirection: ModelSortDirection
    $filter: ModelInquiryFilterInput
    $limit: Int
    $nextToken: String
  ) {
    inquiriesByVendorUserId(
      vendorUserId: $vendorUserId
      sortDirection: $sortDirection
      filter: $filter
      limit: $limit
      nextToken: $nextToken
    ) {
      items {
        id
        vendorUserId
        vendorId
        vendorName
        customerUserId
        customerName
        customerEmail
        customerPhone
        eventDate
        message
        inquiryType
        status
        priority
        budget
        guestCount
        venue
        additionalServices
        preferredContactTime
        responseMessage
        respondedAt
        assignedTo
        followUpDate
        notes
        createdAt
        updatedAt
        __typename
      }
      nextToken
      __typename
    }
  }
`;
export const inquiriesByVendorId = /* GraphQL */ `
  query InquiriesByVendorId(
    $vendorId: String!
    $sortDirection: ModelSortDirection
    $filter: ModelInquiryFilterInput
    $limit: Int
    $nextToken: String
  ) {
    inquiriesByVendorId(
      vendorId: $vendorId
      sortDirection: $sortDirection
      filter: $filter
      limit: $limit
      nextToken: $nextToken
    ) {
      items {
        id
        vendorUserId
        vendorId
        vendorName
        customerUserId
        customerName
        customerEmail
        customerPhone
        eventDate
        message
        inquiryType
        status
        priority
        budget
        guestCount
        venue
        additionalServices
        preferredContactTime
        responseMessage
        respondedAt
        assignedTo
        followUpDate
        notes
        createdAt
        updatedAt
        __typename
      }
      nextToken
      __typename
    }
  }
`;
export const inquiriesByCustomerUserId = /* GraphQL */ `
  query InquiriesByCustomerUserId(
    $customerUserId: String!
    $sortDirection: ModelSortDirection
    $filter: ModelInquiryFilterInput
    $limit: Int
    $nextToken: String
  ) {
    inquiriesByCustomerUserId(
      customerUserId: $customerUserId
      sortDirection: $sortDirection
      filter: $filter
      limit: $limit
      nextToken: $nextToken
    ) {
      items {
        id
        vendorUserId
        vendorId
        vendorName
        customerUserId
        customerName
        customerEmail
        customerPhone
        eventDate
        message
        inquiryType
        status
        priority
        budget
        guestCount
        venue
        additionalServices
        preferredContactTime
        responseMessage
        respondedAt
        assignedTo
        followUpDate
        notes
        createdAt
        updatedAt
        __typename
      }
      nextToken
      __typename
    }
  }
`;
export const getUserProfile = /* GraphQL */ `
  query GetUserProfile($id: ID!) {
    getUserProfile(id: $id) {
      id
      userId
      firstName
      lastName
      email
      phone
      dateOfBirth
      gender
      address
      city
      state
      pincode
      country
      profilePhoto
      bio
      website
      socialMedia {
        facebook
        instagram
        twitter
        linkedin
        youtube
        __typename
      }
      preferences {
        language
        currency
        timezone
        __typename
      }
      businessInfo {
        businessName
        businessType
        businessAddress
        businessPhone
        businessEmail
        businessWebsite
        gstNumber
        panNumber
        businessLicense
        __typename
      }
      isVendor
      isAdmin
      isSuperAdmin
      role
      permissions
      registrationSource
      accountType
      userTypeForm {
        formType
        submissionDate
        formVersion
        verificationStatus
        approvalStatus
        submittedBy
        reviewedBy
        reviewDate
        reviewNotes
        __typename
      }
      isVerified
      lastLoginAt
      createdAt
      updatedAt
      owner
      __typename
    }
  }
`;
export const listUserProfiles = /* GraphQL */ `
  query ListUserProfiles(
    $filter: ModelUserProfileFilterInput
    $limit: Int
    $nextToken: String
  ) {
    listUserProfiles(filter: $filter, limit: $limit, nextToken: $nextToken) {
      items {
        id
        userId
        firstName
        lastName
        email
        phone
        dateOfBirth
        gender
        address
        city
        state
        pincode
        country
        profilePhoto
        bio
        website
        isVendor
        isAdmin
        isSuperAdmin
        role
        permissions
        registrationSource
        accountType
        isVerified
        lastLoginAt
        createdAt
        updatedAt
        owner
        __typename
      }
      nextToken
      __typename
    }
  }
`;
export const userProfilesByUserId = /* GraphQL */ `
  query UserProfilesByUserId(
    $userId: String!
    $sortDirection: ModelSortDirection
    $filter: ModelUserProfileFilterInput
    $limit: Int
    $nextToken: String
  ) {
    userProfilesByUserId(
      userId: $userId
      sortDirection: $sortDirection
      filter: $filter
      limit: $limit
      nextToken: $nextToken
    ) {
      items {
        id
        userId
        firstName
        lastName
        email
        phone
        dateOfBirth
        gender
        address
        city
        state
        pincode
        country
        profilePhoto
        bio
        website
        isVendor
        isAdmin
        isSuperAdmin
        role
        permissions
        registrationSource
        accountType
        isVerified
        lastLoginAt
        createdAt
        updatedAt
        owner
        __typename
      }
      nextToken
      __typename
    }
  }
`;
export const getBlog = /* GraphQL */ `
  query GetBlog($id: ID!) {
    getBlog(id: $id) {
      id
      title
      content
      excerpt
      category
      authorId
      authorName
      authorType
      featuredImage
      tags
      status
      views
      likes
      comments
      isPinned
      isFeatured
      publishedAt
      createdAt
      updatedAt
      __typename
    }
  }
`;
export const listBlogs = /* GraphQL */ `
  query ListBlogs(
    $filter: ModelBlogFilterInput
    $limit: Int
    $nextToken: String
  ) {
    listBlogs(filter: $filter, limit: $limit, nextToken: $nextToken) {
      items {
        id
        title
        content
        excerpt
        category
        authorId
        authorName
        authorType
        featuredImage
        tags
        status
        views
        likes
        comments
        isPinned
        isFeatured
        publishedAt
        createdAt
        updatedAt
        __typename
      }
      nextToken
      __typename
    }
  }
`;
export const blogsByAuthorId = /* GraphQL */ `
  query BlogsByAuthorId(
    $authorId: String!
    $sortDirection: ModelSortDirection
    $filter: ModelBlogFilterInput
    $limit: Int
    $nextToken: String
  ) {
    blogsByAuthorId(
      authorId: $authorId
      sortDirection: $sortDirection
      filter: $filter
      limit: $limit
      nextToken: $nextToken
    ) {
      items {
        id
        title
        content
        excerpt
        category
        authorId
        authorName
        authorType
        featuredImage
        tags
        status
        views
        likes
        comments
        isPinned
        isFeatured
        publishedAt
        createdAt
        updatedAt
        __typename
      }
      nextToken
      __typename
    }
  }
`;
export const getBooking = /* GraphQL */ `
  query GetBooking($id: ID!) {
    getBooking(id: $id) {
      id
      customerId
      customerName
      customerEmail
      customerPhone
      entityId
      entityType
      entityName
      vendorId
      eventDate
      eventTime
      guestCount
      eventType
      duration
      specialRequests
      budget
      contactPreference
      status
      priority
      notes
      vendorNotes
      estimatedCost
      finalCost
      advanceAmount
      balanceAmount
      paymentStatus
      paymentMethod
      transactionId
      contractSigned
      contractUrl
      cancellationReason
      cancellationDate
      refundAmount
      refundStatus
      followUpDate
      reminderSent
      customerRating
      customerReview
      vendorRating
      vendorReview
      communicationLog {
        timestamp
        type
        from
        to
        subject
        message
        attachments
        status
        __typename
      }
      attachments
      metadata
      createdAt
      updatedAt
      owner
      __typename
    }
  }
`;
export const listBookings = /* GraphQL */ `
  query ListBookings(
    $filter: ModelBookingFilterInput
    $limit: Int
    $nextToken: String
  ) {
    listBookings(filter: $filter, limit: $limit, nextToken: $nextToken) {
      items {
        id
        customerId
        customerName
        customerEmail
        customerPhone
        entityId
        entityType
        entityName
        vendorId
        eventDate
        eventTime
        guestCount
        eventType
        duration
        specialRequests
        budget
        contactPreference
        status
        priority
        notes
        vendorNotes
        estimatedCost
        finalCost
        advanceAmount
        balanceAmount
        paymentStatus
        paymentMethod
        transactionId
        contractSigned
        contractUrl
        cancellationReason
        cancellationDate
        refundAmount
        refundStatus
        followUpDate
        reminderSent
        customerRating
        customerReview
        vendorRating
        vendorReview
        attachments
        metadata
        createdAt
        updatedAt
        owner
        __typename
      }
      nextToken
      __typename
    }
  }
`;
export const bookingsByCustomerId = /* GraphQL */ `
  query BookingsByCustomerId(
    $customerId: String!
    $sortDirection: ModelSortDirection
    $filter: ModelBookingFilterInput
    $limit: Int
    $nextToken: String
  ) {
    bookingsByCustomerId(
      customerId: $customerId
      sortDirection: $sortDirection
      filter: $filter
      limit: $limit
      nextToken: $nextToken
    ) {
      items {
        id
        customerId
        customerName
        customerEmail
        customerPhone
        entityId
        entityType
        entityName
        vendorId
        eventDate
        eventTime
        guestCount
        eventType
        duration
        specialRequests
        budget
        contactPreference
        status
        priority
        notes
        vendorNotes
        estimatedCost
        finalCost
        advanceAmount
        balanceAmount
        paymentStatus
        paymentMethod
        transactionId
        contractSigned
        contractUrl
        cancellationReason
        cancellationDate
        refundAmount
        refundStatus
        followUpDate
        reminderSent
        customerRating
        customerReview
        vendorRating
        vendorReview
        attachments
        metadata
        createdAt
        updatedAt
        owner
        __typename
      }
      nextToken
      __typename
    }
  }
`;
export const bookingsByEntityId = /* GraphQL */ `
  query BookingsByEntityId(
    $entityId: String!
    $sortDirection: ModelSortDirection
    $filter: ModelBookingFilterInput
    $limit: Int
    $nextToken: String
  ) {
    bookingsByEntityId(
      entityId: $entityId
      sortDirection: $sortDirection
      filter: $filter
      limit: $limit
      nextToken: $nextToken
    ) {
      items {
        id
        customerId
        customerName
        customerEmail
        customerPhone
        entityId
        entityType
        entityName
        vendorId
        eventDate
        eventTime
        guestCount
        eventType
        duration
        specialRequests
        budget
        contactPreference
        status
        priority
        notes
        vendorNotes
        estimatedCost
        finalCost
        advanceAmount
        balanceAmount
        paymentStatus
        paymentMethod
        transactionId
        contractSigned
        contractUrl
        cancellationReason
        cancellationDate
        refundAmount
        refundStatus
        followUpDate
        reminderSent
        customerRating
        customerReview
        vendorRating
        vendorReview
        attachments
        metadata
        createdAt
        updatedAt
        owner
        __typename
      }
      nextToken
      __typename
    }
  }
`;
export const bookingsByVendorId = /* GraphQL */ `
  query BookingsByVendorId(
    $vendorId: String!
    $sortDirection: ModelSortDirection
    $filter: ModelBookingFilterInput
    $limit: Int
    $nextToken: String
  ) {
    bookingsByVendorId(
      vendorId: $vendorId
      sortDirection: $sortDirection
      filter: $filter
      limit: $limit
      nextToken: $nextToken
    ) {
      items {
        id
        customerId
        customerName
        customerEmail
        customerPhone
        entityId
        entityType
        entityName
        vendorId
        eventDate
        eventTime
        guestCount
        eventType
        duration
        specialRequests
        budget
        contactPreference
        status
        priority
        notes
        vendorNotes
        estimatedCost
        finalCost
        advanceAmount
        balanceAmount
        paymentStatus
        paymentMethod
        transactionId
        contractSigned
        contractUrl
        cancellationReason
        cancellationDate
        refundAmount
        refundStatus
        followUpDate
        reminderSent
        customerRating
        customerReview
        vendorRating
        vendorReview
        attachments
        metadata
        createdAt
        updatedAt
        owner
        __typename
      }
      nextToken
      __typename
    }
  }
`;
export const getNewsletterSubscription = /* GraphQL */ `
  query GetNewsletterSubscription($id: ID!) {
    getNewsletterSubscription(id: $id) {
      id
      email
      firstName
      lastName
      phone
      city
      state
      weddingDate
      interests
      source
      status
      preferences {
        weddingTips
        vendorRecommendations
        specialOffers
        eventUpdates
        blogUpdates
        frequency
        __typename
      }
      userId
      subscribedAt
      unsubscribedAt
      lastEmailSent
      emailsSent
      emailsOpened
      emailsClicked
      createdAt
      updatedAt
      __typename
    }
  }
`;
export const listNewsletterSubscriptions = /* GraphQL */ `
  query ListNewsletterSubscriptions(
    $filter: ModelNewsletterSubscriptionFilterInput
    $limit: Int
    $nextToken: String
  ) {
    listNewsletterSubscriptions(
      filter: $filter
      limit: $limit
      nextToken: $nextToken
    ) {
      items {
        id
        email
        firstName
        lastName
        phone
        city
        state
        weddingDate
        interests
        source
        status
        userId
        subscribedAt
        unsubscribedAt
        lastEmailSent
        emailsSent
        emailsOpened
        emailsClicked
        createdAt
        updatedAt
        __typename
      }
      nextToken
      __typename
    }
  }
`;
export const newsletterSubscriptionsByEmail = /* GraphQL */ `
  query NewsletterSubscriptionsByEmail(
    $email: String!
    $sortDirection: ModelSortDirection
    $filter: ModelNewsletterSubscriptionFilterInput
    $limit: Int
    $nextToken: String
  ) {
    newsletterSubscriptionsByEmail(
      email: $email
      sortDirection: $sortDirection
      filter: $filter
      limit: $limit
      nextToken: $nextToken
    ) {
      items {
        id
        email
        firstName
        lastName
        phone
        city
        state
        weddingDate
        interests
        source
        status
        userId
        subscribedAt
        unsubscribedAt
        lastEmailSent
        emailsSent
        emailsOpened
        emailsClicked
        createdAt
        updatedAt
        __typename
      }
      nextToken
      __typename
    }
  }
`;
export const newsletterSubscriptionsByUserId = /* GraphQL */ `
  query NewsletterSubscriptionsByUserId(
    $userId: String!
    $sortDirection: ModelSortDirection
    $filter: ModelNewsletterSubscriptionFilterInput
    $limit: Int
    $nextToken: String
  ) {
    newsletterSubscriptionsByUserId(
      userId: $userId
      sortDirection: $sortDirection
      filter: $filter
      limit: $limit
      nextToken: $nextToken
    ) {
      items {
        id
        email
        firstName
        lastName
        phone
        city
        state
        weddingDate
        interests
        source
        status
        userId
        subscribedAt
        unsubscribedAt
        lastEmailSent
        emailsSent
        emailsOpened
        emailsClicked
        createdAt
        updatedAt
        __typename
      }
      nextToken
      __typename
    }
  }
`;
export const getChecklistItem = /* GraphQL */ `
  query GetChecklistItem($id: ID!) {
    getChecklistItem(id: $id) {
      id
      userId
      categoryId
      text
      completed
      dueDate
      priority
      order
      isDefault
      createdAt
      updatedAt
      __typename
    }
  }
`;
export const listChecklistItems = /* GraphQL */ `
  query ListChecklistItems(
    $filter: ModelChecklistItemFilterInput
    $limit: Int
    $nextToken: String
  ) {
    listChecklistItems(filter: $filter, limit: $limit, nextToken: $nextToken) {
      items {
        id
        userId
        categoryId
        text
        completed
        dueDate
        priority
        order
        isDefault
        createdAt
        updatedAt
        __typename
      }
      nextToken
      __typename
    }
  }
`;
export const checklistItemsByUserId = /* GraphQL */ `
  query ChecklistItemsByUserId(
    $userId: String!
    $sortDirection: ModelSortDirection
    $filter: ModelChecklistItemFilterInput
    $limit: Int
    $nextToken: String
  ) {
    checklistItemsByUserId(
      userId: $userId
      sortDirection: $sortDirection
      filter: $filter
      limit: $limit
      nextToken: $nextToken
    ) {
      items {
        id
        userId
        categoryId
        text
        completed
        dueDate
        priority
        order
        isDefault
        createdAt
        updatedAt
        __typename
      }
      nextToken
      __typename
    }
  }
`;
export const checklistItemsByCategoryId = /* GraphQL */ `
  query ChecklistItemsByCategoryId(
    $categoryId: ID!
    $sortDirection: ModelSortDirection
    $filter: ModelChecklistItemFilterInput
    $limit: Int
    $nextToken: String
  ) {
    checklistItemsByCategoryId(
      categoryId: $categoryId
      sortDirection: $sortDirection
      filter: $filter
      limit: $limit
      nextToken: $nextToken
    ) {
      items {
        id
        userId
        categoryId
        text
        completed
        dueDate
        priority
        order
        isDefault
        createdAt
        updatedAt
        __typename
      }
      nextToken
      __typename
    }
  }
`;
export const getChecklistCategory = /* GraphQL */ `
  query GetChecklistCategory($id: ID!) {
    getChecklistCategory(id: $id) {
      id
      userId
      name
      icon
      expanded
      order
      isDefault
      items {
        nextToken
        __typename
      }
      createdAt
      updatedAt
      __typename
    }
  }
`;
export const listChecklistCategories = /* GraphQL */ `
  query ListChecklistCategories(
    $filter: ModelChecklistCategoryFilterInput
    $limit: Int
    $nextToken: String
  ) {
    listChecklistCategories(
      filter: $filter
      limit: $limit
      nextToken: $nextToken
    ) {
      items {
        id
        userId
        name
        icon
        expanded
        order
        isDefault
        createdAt
        updatedAt
        __typename
      }
      nextToken
      __typename
    }
  }
`;
export const checklistCategoriesByUserId = /* GraphQL */ `
  query ChecklistCategoriesByUserId(
    $userId: String!
    $sortDirection: ModelSortDirection
    $filter: ModelChecklistCategoryFilterInput
    $limit: Int
    $nextToken: String
  ) {
    checklistCategoriesByUserId(
      userId: $userId
      sortDirection: $sortDirection
      filter: $filter
      limit: $limit
      nextToken: $nextToken
    ) {
      items {
        id
        userId
        name
        icon
        expanded
        order
        isDefault
        createdAt
        updatedAt
        __typename
      }
      nextToken
      __typename
    }
  }
`;
export const getFavorite = /* GraphQL */ `
  query GetFavorite($id: ID!) {
    getFavorite(id: $id) {
      id
      userId
      entityId
      entityType
      entityName
      entityImage
      entityPrice
      entityLocation
      entityCity
      entityState
      entityRating
      entityReviewCount
      entityDescription
      dateAdded
      notes
      createdAt
      updatedAt
      owner
      __typename
    }
  }
`;
export const listFavorites = /* GraphQL */ `
  query ListFavorites(
    $filter: ModelFavoriteFilterInput
    $limit: Int
    $nextToken: String
  ) {
    listFavorites(filter: $filter, limit: $limit, nextToken: $nextToken) {
      items {
        id
        userId
        entityId
        entityType
        entityName
        entityImage
        entityPrice
        entityLocation
        entityCity
        entityState
        entityRating
        entityReviewCount
        entityDescription
        dateAdded
        notes
        createdAt
        updatedAt
        owner
        __typename
      }
      nextToken
      __typename
    }
  }
`;
export const favoritesByUserId = /* GraphQL */ `
  query FavoritesByUserId(
    $userId: String!
    $sortDirection: ModelSortDirection
    $filter: ModelFavoriteFilterInput
    $limit: Int
    $nextToken: String
  ) {
    favoritesByUserId(
      userId: $userId
      sortDirection: $sortDirection
      filter: $filter
      limit: $limit
      nextToken: $nextToken
    ) {
      items {
        id
        userId
        entityId
        entityType
        entityName
        entityImage
        entityPrice
        entityLocation
        entityCity
        entityState
        entityRating
        entityReviewCount
        entityDescription
        dateAdded
        notes
        createdAt
        updatedAt
        owner
        __typename
      }
      nextToken
      __typename
    }
  }
`;
export const favoritesByEntityId = /* GraphQL */ `
  query FavoritesByEntityId(
    $entityId: String!
    $sortDirection: ModelSortDirection
    $filter: ModelFavoriteFilterInput
    $limit: Int
    $nextToken: String
  ) {
    favoritesByEntityId(
      entityId: $entityId
      sortDirection: $sortDirection
      filter: $filter
      limit: $limit
      nextToken: $nextToken
    ) {
      items {
        id
        userId
        entityId
        entityType
        entityName
        entityImage
        entityPrice
        entityLocation
        entityCity
        entityState
        entityRating
        entityReviewCount
        entityDescription
        dateAdded
        notes
        createdAt
        updatedAt
        owner
        __typename
      }
      nextToken
      __typename
    }
  }
`;
export const getCartItem = /* GraphQL */ `
  query GetCartItem($id: ID!) {
    getCartItem(id: $id) {
      id
      userId
      productId
      productName
      productImage
      productPrice
      originalPrice
      discount
      quantity
      selectedVariant
      selectedSize
      selectedColor
      productBrand
      productCategory
      productDescription
      status
      dateAdded
      dateUpdated
      notes
      createdAt
      updatedAt
      owner
      __typename
    }
  }
`;
export const listCartItems = /* GraphQL */ `
  query ListCartItems(
    $filter: ModelCartItemFilterInput
    $limit: Int
    $nextToken: String
  ) {
    listCartItems(filter: $filter, limit: $limit, nextToken: $nextToken) {
      items {
        id
        userId
        productId
        productName
        productImage
        productPrice
        originalPrice
        discount
        quantity
        selectedVariant
        selectedSize
        selectedColor
        productBrand
        productCategory
        productDescription
        status
        dateAdded
        dateUpdated
        notes
        createdAt
        updatedAt
        owner
        __typename
      }
      nextToken
      __typename
    }
  }
`;
export const cartItemsByUserId = /* GraphQL */ `
  query CartItemsByUserId(
    $userId: String!
    $sortDirection: ModelSortDirection
    $filter: ModelCartItemFilterInput
    $limit: Int
    $nextToken: String
  ) {
    cartItemsByUserId(
      userId: $userId
      sortDirection: $sortDirection
      filter: $filter
      limit: $limit
      nextToken: $nextToken
    ) {
      items {
        id
        userId
        productId
        productName
        productImage
        productPrice
        originalPrice
        discount
        quantity
        selectedVariant
        selectedSize
        selectedColor
        productBrand
        productCategory
        productDescription
        status
        dateAdded
        dateUpdated
        notes
        createdAt
        updatedAt
        owner
        __typename
      }
      nextToken
      __typename
    }
  }
`;
export const cartItemsByProductId = /* GraphQL */ `
  query CartItemsByProductId(
    $productId: String!
    $sortDirection: ModelSortDirection
    $filter: ModelCartItemFilterInput
    $limit: Int
    $nextToken: String
  ) {
    cartItemsByProductId(
      productId: $productId
      sortDirection: $sortDirection
      filter: $filter
      limit: $limit
      nextToken: $nextToken
    ) {
      items {
        id
        userId
        productId
        productName
        productImage
        productPrice
        originalPrice
        discount
        quantity
        selectedVariant
        selectedSize
        selectedColor
        productBrand
        productCategory
        productDescription
        status
        dateAdded
        dateUpdated
        notes
        createdAt
        updatedAt
        owner
        __typename
      }
      nextToken
      __typename
    }
  }
`;
export const getOrder = /* GraphQL */ `
  query GetOrder($id: ID!) {
    getOrder(id: $id) {
      id
      userId
      orderNumber
      status
      paymentStatus
      paymentMethod
      customerName
      customerEmail
      customerPhone
      shippingAddress {
        fullName
        addressLine1
        addressLine2
        city
        state
        pincode
        country
        phone
        landmark
        addressType
        __typename
      }
      billingAddress {
        fullName
        addressLine1
        addressLine2
        city
        state
        pincode
        country
        phone
        landmark
        addressType
        __typename
      }
      items {
        productId
        productName
        productImage
        productPrice
        originalPrice
        discount
        quantity
        selectedVariant
        selectedSize
        selectedColor
        productBrand
        productCategory
        subtotal
        __typename
      }
      subtotal
      shippingCost
      tax
      discount
      total
      razorpayOrderId
      razorpayPaymentId
      razorpaySignature
      transactionId
      estimatedDeliveryDate
      actualDeliveryDate
      trackingNumber
      courierPartner
      specialInstructions
      giftMessage
      isGift
      orderDate
      shippedDate
      deliveredDate
      cancelledDate
      metadata
      notes
      createdAt
      updatedAt
      owner
      __typename
    }
  }
`;
export const listOrders = /* GraphQL */ `
  query ListOrders(
    $filter: ModelOrderFilterInput
    $limit: Int
    $nextToken: String
  ) {
    listOrders(filter: $filter, limit: $limit, nextToken: $nextToken) {
      items {
        id
        userId
        orderNumber
        status
        paymentStatus
        paymentMethod
        customerName
        customerEmail
        customerPhone
        subtotal
        shippingCost
        tax
        discount
        total
        razorpayOrderId
        razorpayPaymentId
        razorpaySignature
        transactionId
        estimatedDeliveryDate
        actualDeliveryDate
        trackingNumber
        courierPartner
        specialInstructions
        giftMessage
        isGift
        orderDate
        shippedDate
        deliveredDate
        cancelledDate
        metadata
        notes
        createdAt
        updatedAt
        owner
        __typename
      }
      nextToken
      __typename
    }
  }
`;
export const ordersByUserId = /* GraphQL */ `
  query OrdersByUserId(
    $userId: String!
    $sortDirection: ModelSortDirection
    $filter: ModelOrderFilterInput
    $limit: Int
    $nextToken: String
  ) {
    ordersByUserId(
      userId: $userId
      sortDirection: $sortDirection
      filter: $filter
      limit: $limit
      nextToken: $nextToken
    ) {
      items {
        id
        userId
        orderNumber
        status
        paymentStatus
        paymentMethod
        customerName
        customerEmail
        customerPhone
        subtotal
        shippingCost
        tax
        discount
        total
        razorpayOrderId
        razorpayPaymentId
        razorpaySignature
        transactionId
        estimatedDeliveryDate
        actualDeliveryDate
        trackingNumber
        courierPartner
        specialInstructions
        giftMessage
        isGift
        orderDate
        shippedDate
        deliveredDate
        cancelledDate
        metadata
        notes
        createdAt
        updatedAt
        owner
        __typename
      }
      nextToken
      __typename
    }
  }
`;
export const ordersByOrderNumber = /* GraphQL */ `
  query OrdersByOrderNumber(
    $orderNumber: String!
    $sortDirection: ModelSortDirection
    $filter: ModelOrderFilterInput
    $limit: Int
    $nextToken: String
  ) {
    ordersByOrderNumber(
      orderNumber: $orderNumber
      sortDirection: $sortDirection
      filter: $filter
      limit: $limit
      nextToken: $nextToken
    ) {
      items {
        id
        userId
        orderNumber
        status
        paymentStatus
        paymentMethod
        customerName
        customerEmail
        customerPhone
        subtotal
        shippingCost
        tax
        discount
        total
        razorpayOrderId
        razorpayPaymentId
        razorpaySignature
        transactionId
        estimatedDeliveryDate
        actualDeliveryDate
        trackingNumber
        courierPartner
        specialInstructions
        giftMessage
        isGift
        orderDate
        shippedDate
        deliveredDate
        cancelledDate
        metadata
        notes
        createdAt
        updatedAt
        owner
        __typename
      }
      nextToken
      __typename
    }
  }
`;
export const getPayment = /* GraphQL */ `
  query GetPayment($id: ID!) {
    getPayment(id: $id) {
      id
      orderId
      userId
      amount
      currency
      paymentMethod
      status
      razorpayOrderId
      razorpayPaymentId
      razorpaySignature
      codAmount
      codCollected
      codCollectedDate
      transactionId
      gatewayResponse
      failureReason
      refundAmount
      refundStatus
      refundDate
      refundTransactionId
      initiatedAt
      completedAt
      metadata
      notes
      createdAt
      updatedAt
      owner
      __typename
    }
  }
`;
export const listPayments = /* GraphQL */ `
  query ListPayments(
    $filter: ModelPaymentFilterInput
    $limit: Int
    $nextToken: String
  ) {
    listPayments(filter: $filter, limit: $limit, nextToken: $nextToken) {
      items {
        id
        orderId
        userId
        amount
        currency
        paymentMethod
        status
        razorpayOrderId
        razorpayPaymentId
        razorpaySignature
        codAmount
        codCollected
        codCollectedDate
        transactionId
        gatewayResponse
        failureReason
        refundAmount
        refundStatus
        refundDate
        refundTransactionId
        initiatedAt
        completedAt
        metadata
        notes
        createdAt
        updatedAt
        owner
        __typename
      }
      nextToken
      __typename
    }
  }
`;
export const paymentsByOrderId = /* GraphQL */ `
  query PaymentsByOrderId(
    $orderId: String!
    $sortDirection: ModelSortDirection
    $filter: ModelPaymentFilterInput
    $limit: Int
    $nextToken: String
  ) {
    paymentsByOrderId(
      orderId: $orderId
      sortDirection: $sortDirection
      filter: $filter
      limit: $limit
      nextToken: $nextToken
    ) {
      items {
        id
        orderId
        userId
        amount
        currency
        paymentMethod
        status
        razorpayOrderId
        razorpayPaymentId
        razorpaySignature
        codAmount
        codCollected
        codCollectedDate
        transactionId
        gatewayResponse
        failureReason
        refundAmount
        refundStatus
        refundDate
        refundTransactionId
        initiatedAt
        completedAt
        metadata
        notes
        createdAt
        updatedAt
        owner
        __typename
      }
      nextToken
      __typename
    }
  }
`;
export const paymentsByUserId = /* GraphQL */ `
  query PaymentsByUserId(
    $userId: String!
    $sortDirection: ModelSortDirection
    $filter: ModelPaymentFilterInput
    $limit: Int
    $nextToken: String
  ) {
    paymentsByUserId(
      userId: $userId
      sortDirection: $sortDirection
      filter: $filter
      limit: $limit
      nextToken: $nextToken
    ) {
      items {
        id
        orderId
        userId
        amount
        currency
        paymentMethod
        status
        razorpayOrderId
        razorpayPaymentId
        razorpaySignature
        codAmount
        codCollected
        codCollectedDate
        transactionId
        gatewayResponse
        failureReason
        refundAmount
        refundStatus
        refundDate
        refundTransactionId
        initiatedAt
        completedAt
        metadata
        notes
        createdAt
        updatedAt
        owner
        __typename
      }
      nextToken
      __typename
    }
  }
`;
export const getWeddingPlan = /* GraphQL */ `
  query GetWeddingPlan($id: ID!) {
    getWeddingPlan(id: $id) {
      id
      userId
      weddingDate
      venue
      budget
      guestCount
      theme
      status
      notes
      createdAt
      updatedAt
      __typename
    }
  }
`;
export const listWeddingPlans = /* GraphQL */ `
  query ListWeddingPlans(
    $filter: ModelWeddingPlanFilterInput
    $limit: Int
    $nextToken: String
  ) {
    listWeddingPlans(filter: $filter, limit: $limit, nextToken: $nextToken) {
      items {
        id
        userId
        weddingDate
        venue
        budget
        guestCount
        theme
        status
        notes
        createdAt
        updatedAt
        __typename
      }
      nextToken
      __typename
    }
  }
`;
export const weddingPlansByUserId = /* GraphQL */ `
  query WeddingPlansByUserId(
    $userId: String!
    $sortDirection: ModelSortDirection
    $filter: ModelWeddingPlanFilterInput
    $limit: Int
    $nextToken: String
  ) {
    weddingPlansByUserIdAndCreatedAt(
      userId: $userId
      sortDirection: $sortDirection
      filter: $filter
      limit: $limit
      nextToken: $nextToken
    ) {
      items {
        id
        userId
        weddingDate
        venue
        budget
        guestCount
        theme
        status
        notes
        createdAt
        updatedAt
        __typename
      }
      nextToken
      __typename
    }
  }
`;
export const weddingPlansByUserIdAndCreatedAt = /* GraphQL */ `
  query WeddingPlansByUserIdAndCreatedAt(
    $userId: String!
    $createdAt: ModelStringKeyConditionInput
    $sortDirection: ModelSortDirection
    $filter: ModelWeddingPlanFilterInput
    $limit: Int
    $nextToken: String
  ) {
    weddingPlansByUserIdAndCreatedAt(
      userId: $userId
      createdAt: $createdAt
      sortDirection: $sortDirection
      filter: $filter
      limit: $limit
      nextToken: $nextToken
    ) {
      items {
        id
        userId
        weddingDate
        venue
        budget
        guestCount
        theme
        status
        notes
        createdAt
        updatedAt
        __typename
      }
      nextToken
      __typename
    }
  }
`;
export const getBudget = /* GraphQL */ `
  query GetBudget($id: ID!) {
    getBudget(id: $id) {
      id
      userId
      weddingPlanId
      name
      totalBudget
      spentAmount
      isTemplate
      templateType
      createdAt
      updatedAt
      __typename
    }
  }
`;
export const listBudgets = /* GraphQL */ `
  query ListBudgets(
    $filter: ModelBudgetFilterInput
    $limit: Int
    $nextToken: String
  ) {
    listBudgets(filter: $filter, limit: $limit, nextToken: $nextToken) {
      items {
        id
        userId
        weddingPlanId
        name
        totalBudget
        spentAmount
        isTemplate
        templateType
        createdAt
        updatedAt
        __typename
      }
      nextToken
      __typename
    }
  }
`;
export const budgetsByUserId = /* GraphQL */ `
  query BudgetsByUserId(
    $userId: String!
    $sortDirection: ModelSortDirection
    $filter: ModelBudgetFilterInput
    $limit: Int
    $nextToken: String
  ) {
    budgetsByUserIdAndCreatedAt(
      userId: $userId
      sortDirection: $sortDirection
      filter: $filter
      limit: $limit
      nextToken: $nextToken
    ) {
      items {
        id
        userId
        weddingPlanId
        name
        totalBudget
        spentAmount
        isTemplate
        templateType
        createdAt
        updatedAt
        __typename
      }
      nextToken
      __typename
    }
  }
`;
export const budgetsByUserIdAndCreatedAt = /* GraphQL */ `
  query BudgetsByUserIdAndCreatedAt(
    $userId: String!
    $createdAt: ModelStringKeyConditionInput
    $sortDirection: ModelSortDirection
    $filter: ModelBudgetFilterInput
    $limit: Int
    $nextToken: String
  ) {
    budgetsByUserIdAndCreatedAt(
      userId: $userId
      createdAt: $createdAt
      sortDirection: $sortDirection
      filter: $filter
      limit: $limit
      nextToken: $nextToken
    ) {
      items {
        id
        userId
        weddingPlanId
        name
        totalBudget
        spentAmount
        isTemplate
        templateType
        createdAt
        updatedAt
        __typename
      }
      nextToken
      __typename
    }
  }
`;
export const getBudgetData = /* GraphQL */ `
  query GetBudgetData($id: ID!) {
    getBudgetData(id: $id) {
      id
      userId
      budgetId
      name
      data
      createdAt
      updatedAt
      __typename
    }
  }
`;
export const listBudgetData = /* GraphQL */ `
  query ListBudgetData(
    $filter: ModelBudgetDataFilterInput
    $limit: Int
    $nextToken: String
  ) {
    listBudgetData(filter: $filter, limit: $limit, nextToken: $nextToken) {
      items {
        id
        userId
        budgetId
        name
        data
        createdAt
        updatedAt
        __typename
      }
      nextToken
      __typename
    }
  }
`;
export const budgetDataByUserId = /* GraphQL */ `
  query BudgetDataByUserId(
    $userId: String!
    $sortDirection: ModelSortDirection
    $filter: ModelBudgetDataFilterInput
    $limit: Int
    $nextToken: String
  ) {
    budgetDataByUserIdAndCreatedAt(
      userId: $userId
      sortDirection: $sortDirection
      filter: $filter
      limit: $limit
      nextToken: $nextToken
    ) {
      items {
        id
        userId
        budgetId
        name
        data
        createdAt
        updatedAt
        __typename
      }
      nextToken
      __typename
    }
  }
`;
export const budgetDataByUserIdAndCreatedAt = /* GraphQL */ `
  query BudgetDataByUserIdAndCreatedAt(
    $userId: String!
    $createdAt: ModelStringKeyConditionInput
    $sortDirection: ModelSortDirection
    $filter: ModelBudgetDataFilterInput
    $limit: Int
    $nextToken: String
  ) {
    budgetDataByUserIdAndCreatedAt(
      userId: $userId
      createdAt: $createdAt
      sortDirection: $sortDirection
      filter: $filter
      limit: $limit
      nextToken: $nextToken
    ) {
      items {
        id
        userId
        budgetId
        name
        data
        createdAt
        updatedAt
        __typename
      }
      nextToken
      __typename
    }
  }
`;
export const budgetDataByBudgetId = /* GraphQL */ `
  query BudgetDataByBudgetId(
    $budgetId: String!
    $sortDirection: ModelSortDirection
    $filter: ModelBudgetDataFilterInput
    $limit: Int
    $nextToken: String
  ) {
    budgetDataByBudgetId(
      budgetId: $budgetId
      sortDirection: $sortDirection
      filter: $filter
      limit: $limit
      nextToken: $nextToken
    ) {
      items {
        id
        userId
        budgetId
        name
        data
        createdAt
        updatedAt
        __typename
      }
      nextToken
      __typename
    }
  }
`;
export const getGuestListData = /* GraphQL */ `
  query GetGuestListData($id: ID!) {
    getGuestListData(id: $id) {
      id
      userId
      name
      data
      totalGuests
      confirmedGuests
      createdAt
      updatedAt
      __typename
    }
  }
`;
export const listGuestListData = /* GraphQL */ `
  query ListGuestListData(
    $filter: ModelGuestListDataFilterInput
    $limit: Int
    $nextToken: String
  ) {
    listGuestListData(filter: $filter, limit: $limit, nextToken: $nextToken) {
      items {
        id
        userId
        name
        data
        totalGuests
        confirmedGuests
        createdAt
        updatedAt
        __typename
      }
      nextToken
      __typename
    }
  }
`;
export const guestListDataByUserId = /* GraphQL */ `
  query GuestListDataByUserId(
    $userId: String!
    $sortDirection: ModelSortDirection
    $filter: ModelGuestListDataFilterInput
    $limit: Int
    $nextToken: String
  ) {
    guestListDataByUserIdAndCreatedAt(
      userId: $userId
      sortDirection: $sortDirection
      filter: $filter
      limit: $limit
      nextToken: $nextToken
    ) {
      items {
        id
        userId
        name
        data
        totalGuests
        confirmedGuests
        createdAt
        updatedAt
        __typename
      }
      nextToken
      __typename
    }
  }
`;
export const guestListDataByUserIdAndCreatedAt = /* GraphQL */ `
  query GuestListDataByUserIdAndCreatedAt(
    $userId: String!
    $createdAt: ModelStringKeyConditionInput
    $sortDirection: ModelSortDirection
    $filter: ModelGuestListDataFilterInput
    $limit: Int
    $nextToken: String
  ) {
    guestListDataByUserIdAndCreatedAt(
      userId: $userId
      createdAt: $createdAt
      sortDirection: $sortDirection
      filter: $filter
      limit: $limit
      nextToken: $nextToken
    ) {
      items {
        id
        userId
        name
        data
        totalGuests
        confirmedGuests
        createdAt
        updatedAt
        __typename
      }
      nextToken
      __typename
    }
  }
`;
export const getPlanningToolsData = /* GraphQL */ `
  query GetPlanningToolsData($id: ID!) {
    getPlanningToolsData(id: $id) {
      id
      userId
      toolType
      name
      data
      metadata
      createdAt
      updatedAt
      __typename
    }
  }
`;
export const listPlanningToolsData = /* GraphQL */ `
  query ListPlanningToolsData(
    $filter: ModelPlanningToolsDataFilterInput
    $limit: Int
    $nextToken: String
  ) {
    listPlanningToolsData(
      filter: $filter
      limit: $limit
      nextToken: $nextToken
    ) {
      items {
        id
        userId
        toolType
        name
        data
        metadata
        createdAt
        updatedAt
        __typename
      }
      nextToken
      __typename
    }
  }
`;
export const planningToolsDataByUserId = /* GraphQL */ `
  query PlanningToolsDataByUserId(
    $userId: String!
    $sortDirection: ModelSortDirection
    $filter: ModelPlanningToolsDataFilterInput
    $limit: Int
    $nextToken: String
  ) {
    planningToolsDataByUserIdAndCreatedAt(
      userId: $userId
      sortDirection: $sortDirection
      filter: $filter
      limit: $limit
      nextToken: $nextToken
    ) {
      items {
        id
        userId
        toolType
        name
        data
        metadata
        createdAt
        updatedAt
        __typename
      }
      nextToken
      __typename
    }
  }
`;
export const planningToolsDataByUserIdAndCreatedAt = /* GraphQL */ `
  query PlanningToolsDataByUserIdAndCreatedAt(
    $userId: String!
    $createdAt: ModelStringKeyConditionInput
    $sortDirection: ModelSortDirection
    $filter: ModelPlanningToolsDataFilterInput
    $limit: Int
    $nextToken: String
  ) {
    planningToolsDataByUserIdAndCreatedAt(
      userId: $userId
      createdAt: $createdAt
      sortDirection: $sortDirection
      filter: $filter
      limit: $limit
      nextToken: $nextToken
    ) {
      items {
        id
        userId
        toolType
        name
        data
        metadata
        createdAt
        updatedAt
        __typename
      }
      nextToken
      __typename
    }
  }
`;
