"use client"

import { useState, useEffect } from 'react'
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Progress } from "@/components/ui/progress"
import { Textarea } from "@/components/ui/textarea"
import { Star, ThumbsUp, Calendar, MapPin, Verified, MessageSquare, Reply, ChevronDown, ChevronUp, Send, X } from "lucide-react"
import EntityReviewService from '@/lib/services/entityReviewService'
import { useAuth } from '@/contexts/AuthContext'
import { showToast } from '@/lib/toast'
import { EntityReviewForm } from './entity-review-form'

interface EntityReviewsProps {
  entityType: 'SHOP' | 'VENUE' | 'VENDOR'
  entityId: string
  entityName: string
}

interface Reply {
  id: string
  userId: string
  name: string
  content: string
  createdAt: string
  isOwner?: boolean
}

interface Review {
  id: string
  userId: string
  name: string
  location: string
  rating: number
  serviceRating?: number
  valueRating?: number
  communicationRating?: number
  professionalismRating?: number
  title: string
  review: string
  wouldRecommend: boolean
  verified: boolean
  helpfulCount: number
  images?: string[]
  createdAt: string
  weddingDate: string
  replies?: Reply[]
  replyCount?: number
}

interface ReviewStats {
  totalReviews: number
  averageRating: number
  averageServiceRating: number
  averageValueRating: number
  averageCommunicationRating: number
  averageProfessionalismRating: number
  recommendationRate: number
  ratingDistribution: { [key: number]: number }
}

export function EntityReviews({ entityType, entityId, entityName }: EntityReviewsProps) {
  // Add error boundary for useAuth
  let authContext
  try {
    authContext = useAuth()
  } catch (error) {
    console.error('Auth context not available:', error)
    // Provide fallback values
    authContext = {
      isAuthenticated: false,
      user: null
    }
  }

  const { isAuthenticated, user } = authContext || {}
  
  const [reviews, setReviews] = useState<Review[]>([])
  const [stats, setStats] = useState<ReviewStats | null>(null)
  const [loading, setLoading] = useState(true)
  const [showReviewForm, setShowReviewForm] = useState(false)
  const [activeTab, setActiveTab] = useState('reviews')
  const [userHasReviewed, setUserHasReviewed] = useState(false)
  const [userReview, setUserReview] = useState<Review | null>(null)
  
  // Pagination state
  const [currentPage, setCurrentPage] = useState(1)
  const [reviewsPerPage] = useState(5)
  const [showAllReviews, setShowAllReviews] = useState(false)
  
  // Reply state
  const [replyingTo, setReplyingTo] = useState<string | null>(null)
  const [replyContent, setReplyContent] = useState('')
  const [submittingReply, setSubmittingReply] = useState(false)
  const [expandedReplies, setExpandedReplies] = useState<Set<string>>(new Set())

  // Pagination calculations
  const startIndex = (currentPage - 1) * reviewsPerPage
  const endIndex = startIndex + reviewsPerPage
  const totalPages = Math.ceil(reviews.length / reviewsPerPage)
  const currentReviews = showAllReviews ? reviews : reviews.slice(startIndex, endIndex)

  useEffect(() => {
    loadReviews()
    if (isAuthenticated && user) {
      checkUserReview()
    }
  }, [entityId, entityType, isAuthenticated, user])

  const loadReviews = async () => {
    try {
      setLoading(true)
      console.log('Loading reviews for:', entityId, entityType)
      
      const response = await EntityReviewService.getEntityReviews(entityId)
      console.log('Reviews response:', response)
      
      if (response && response.reviews) {
        console.log('Setting reviews:', response.reviews)
        setReviews(response.reviews)
        calculateStats(response.reviews)
      } else {
        console.log('No reviews found or invalid response structure')
        setReviews([])
        calculateStats([])
      }
    } catch (error) {
      console.error('Error loading reviews:', error)
      setReviews([])
      calculateStats([])
    } finally {
      setLoading(false)
    }
  }

  const calculateStats = (reviewsData: Review[]) => {
    if (reviewsData.length === 0) {
      setStats({
        totalReviews: 0,
        averageRating: 0,
        averageServiceRating: 0,
        averageValueRating: 0,
        averageCommunicationRating: 0,
        averageProfessionalismRating: 0,
        recommendationRate: 0,
        ratingDistribution: { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 }
      })
      return
    }

    const totalReviews = reviewsData.length
    const totalRating = reviewsData.reduce((sum, review) => sum + review.rating, 0)
    const averageRating = totalRating / totalReviews

    const serviceRatings = reviewsData.filter(r => r.serviceRating).map(r => r.serviceRating!)
    const averageServiceRating = serviceRatings.length > 0 
      ? serviceRatings.reduce((sum, rating) => sum + rating, 0) / serviceRatings.length 
      : 0

    const valueRatings = reviewsData.filter(r => r.valueRating).map(r => r.valueRating!)
    const averageValueRating = valueRatings.length > 0 
      ? valueRatings.reduce((sum, rating) => sum + rating, 0) / valueRatings.length 
      : 0

    const communicationRatings = reviewsData.filter(r => r.communicationRating).map(r => r.communicationRating!)
    const averageCommunicationRating = communicationRatings.length > 0 
      ? communicationRatings.reduce((sum, rating) => sum + rating, 0) / communicationRatings.length 
      : 0

    const professionalismRatings = reviewsData.filter(r => r.professionalismRating).map(r => r.professionalismRating!)
    const averageProfessionalismRating = professionalismRatings.length > 0 
      ? professionalismRatings.reduce((sum, rating) => sum + rating, 0) / professionalismRatings.length 
      : 0

    const recommendationRate = (reviewsData.filter(r => r.wouldRecommend).length / totalReviews) * 100

    const ratingDistribution = { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 }
    reviewsData.forEach(review => {
      ratingDistribution[review.rating as keyof typeof ratingDistribution]++
    })

    setStats({
      totalReviews,
      averageRating,
      averageServiceRating,
      averageValueRating,
      averageCommunicationRating,
      averageProfessionalismRating,
      recommendationRate,
      ratingDistribution
    })
  }

  const checkUserReview = async () => {
    if (!user?.userId) return
    
    try {
      const userReview = reviews.find(review => review.userId === user.userId)
      if (userReview) {
        setUserHasReviewed(true)
        setUserReview(userReview)
      }
    } catch (error) {
      console.error('Error checking user review:', error)
    }
  }

  const handleReviewSubmitted = async (newReview: Review) => {
    setReviews(prev => [newReview, ...prev])
    setUserHasReviewed(true)
    setUserReview(newReview)
    setShowReviewForm(false)
    await loadReviews()
  }

  const handleMarkHelpful = async (reviewId: string) => {
    console.log('Marking review as helpful:', reviewId)
  }

  const handlePageChange = (page: number) => {
    setCurrentPage(page)
  }

  const toggleShowAllReviews = () => {
    setShowAllReviews(!showAllReviews)
  }

  const handleReply = async (reviewId: string) => {
    if (!replyContent.trim()) return
    
    try {
      setSubmittingReply(true)
      
      const updatedReview = await EntityReviewService.addVendorResponse(reviewId, replyContent)
      
      const newReply: Reply = {
        id: `reply-${Date.now()}`,
        userId: user?.userId || '',
        name: user?.name || 'Anonymous',
        content: replyContent,
        createdAt: new Date().toISOString(),
        isOwner: true
      }
      
      setReviews(prev => prev.map(review => 
        review.id === reviewId 
          ? { 
              ...review, 
              replies: [...(review.replies || []), newReply],
              replyCount: (review.replyCount || 0) + 1
            }
          : review
      ))
      
      setReplyContent('')
      setReplyingTo(null)
      showToast.success('Reply added successfully!')
      
      await loadReviews()
      
    } catch (error) {
      console.error('Error adding reply:', error)
      showToast.error('Failed to add reply. Please try again.')
    } finally {
      setSubmittingReply(false)
    }
  }

  const toggleReplies = (reviewId: string) => {
    const newExpanded = new Set(expandedReplies)
    if (newExpanded.has(reviewId)) {
      newExpanded.delete(reviewId)
    } else {
      newExpanded.add(reviewId)
    }
    setExpandedReplies(newExpanded)
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  const renderStars = (rating: number, size: 'sm' | 'md' = 'md') => {
    const starSize = size === 'sm' ? 'h-3 w-3' : 'h-4 w-4'
    return (
      <div className="flex items-center space-x-1">
        {[1, 2, 3, 4, 5].map((star) => (
          <Star
            key={star}
            className={`${starSize} ${
              star <= rating ? 'fill-yellow-400 text-yellow-400' : 'text-gray-300'
            }`}
          />
        ))}
      </div>
    )
  }

  if (typeof window === 'undefined') {
    return (
      <div className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>Reviews</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-center py-8">
              <MessageSquare className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">Loading reviews...</p>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (loading) {
    return (
      <div className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>Reviews</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-center py-8">
              <MessageSquare className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">Loading reviews...</p>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {stats && (
        <Card className="border-accent/20">
          <CardContent className="p-6">
            <div className="flex flex-col lg:flex-row gap-8">
              <div className="flex-1 flex flex-col items-center lg:items-start">
                <div className="text-center lg:text-left mb-4">
                  <div className="text-4xl font-bold text-primary mb-2">
                    {stats.averageRating.toFixed(1)}
                  </div>
                  <div className="flex justify-center lg:justify-start mb-2">
                    {renderStars(stats.averageRating, 'md')}
                  </div>
                  <p className="text-sm text-gray-600">
                    Based on {stats.totalReviews} reviews
                  </p>
                </div>
              </div>

              <div className="flex-1">
                <div className="space-y-2">
                  {[5, 4, 3, 2, 1].map((rating) => {
                    const count = stats.ratingDistribution[rating] || 0
                    const percentage = stats.totalReviews > 0 ? (count / stats.totalReviews) * 100 : 0
                    
                    return (
                      <div key={rating} className="flex items-center space-x-3">
                        <div className="flex items-center space-x-1 min-w-[60px]">
                          <span className="text-sm font-medium text-gray-700">{rating}</span>
                          <div className="flex">
                            {[1, 2, 3, 4, 5].map((star) => (
                              <Star
                                key={star}
                                className={`h-3 w-3 ${
                                  star <= rating ? 'fill-yellow-400 text-yellow-400' : 'text-gray-300'
                                }`}
                              />
                            ))}
                          </div>
                        </div>
                        <div className="flex-1">
                          <Progress 
                            value={percentage} 
                            className="h-2 bg-gray-200"
                          />
                        </div>
                        <div className="min-w-[30px] text-right">
                          <span className="text-sm text-gray-600">{count}</span>
                        </div>
                      </div>
                    )
                  })}
                </div>
              </div>

              <div className="flex justify-center lg:justify-end">
                {isAuthenticated && !userHasReviewed && (
                  <Button 
                    onClick={() => setShowReviewForm(true)}
                    className="bg-primary hover:bg-primary/90 text-white px-6 py-2"
                  >
                    Write a Review
                  </Button>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {showReviewForm && (
        <EntityReviewForm
          entityType={entityType}
          entityName={entityName}
          entityId={entityId}
          onClose={() => setShowReviewForm(false)}
          onSubmitted={handleReviewSubmitted}
        />
      )}

      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle>Reviews ({reviews.length})</CardTitle>
          {isAuthenticated && !userHasReviewed && (
            <Button 
              onClick={() => setShowReviewForm(true)}
              className="bg-primary hover:bg-primary/90 text-white"
            >
              Write a Review
            </Button>
          )}
        </CardHeader>
        <CardContent>
          {reviews.length === 0 ? (
            <div className="text-center py-8">
              <MessageSquare className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-600 mb-2">No reviews yet</h3>
              <p className="text-gray-500 mb-4">Be the first to share your experience!</p>
              {isAuthenticated && (
                <Button 
                  onClick={() => setShowReviewForm(true)}
                  className="bg-primary hover:bg-primary/90 text-white"
                >
                  Write the First Review
                </Button>
              )}
            </div>
          ) : (
            <>
              <div className="mb-4 p-3 bg-blue-50 rounded-lg">
                <p className="text-sm text-blue-700">
                  Debug: Found {reviews.length} reviews. Current page: {currentPage}, 
                  Showing: {startIndex + 1}-{Math.min(endIndex, reviews.length)} of {reviews.length}
                </p>
              </div>

              <div className="space-y-6 max-h-[600px] overflow-y-auto pr-2">
                {currentReviews.map((review) => (
                  <div key={review.id} className="border-b pb-6 last:border-b-0">
                    <div className="flex items-start space-x-4">
                      <Avatar>
                        <AvatarFallback>{review.name.charAt(0).toUpperCase()}</AvatarFallback>
                      </Avatar>
                      <div className="flex-1">
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center space-x-2">
                            <h4 className="font-semibold">{review.name}</h4>
                            {review.verified && (
                              <Badge className="bg-green-600 text-xs">
                                <Verified className="h-3 w-3 mr-1" />
                                Verified
                              </Badge>
                            )}
                          </div>
                          <span className="text-sm text-gray-500">
                            {formatDate(review.createdAt)}
                          </span>
                        </div>
                        
                        <div className="flex items-center space-x-4 mb-2">
                          {renderStars(review.rating)}
                          <span className="font-medium">{review.rating}/5</span>
                          {review.location && (
                            <div className="flex items-center text-sm text-gray-600">
                              <MapPin className="h-3 w-3 mr-1" />
                              {review.location}
                            </div>
                          )}
                        </div>

                        <h5 className="font-semibold mb-2">{review.title}</h5>
                        <p className="text-gray-700 mb-3">{review.review}</p>

                        {(review.serviceRating || review.valueRating || review.communicationRating || review.professionalismRating) && (
                          <div className="grid grid-cols-2 md:grid-cols-4 gap-3 mb-3 p-3 bg-gray-50 rounded-lg">
                            {review.serviceRating && (
                              <div className="text-center">
                                <div className="text-sm font-medium">{review.serviceRating}/5</div>
                                <p className="text-xs text-gray-600">Service</p>
                              </div>
                            )}
                            {review.valueRating && (
                              <div className="text-center">
                                <div className="text-sm font-medium">{review.valueRating}/5</div>
                                <p className="text-xs text-gray-600">Value</p>
                              </div>
                            )}
                            {review.communicationRating && (
                              <div className="text-center">
                                <div className="text-sm font-medium">{review.communicationRating}/5</div>
                                <p className="text-xs text-gray-600">Communication</p>
                              </div>
                            )}
                            {review.professionalismRating && (
                              <div className="text-center">
                                <div className="text-sm font-medium">{review.professionalismRating}/5</div>
                                <p className="text-xs text-gray-600">Professional</p>
                              </div>
                            )}
                          </div>
                        )}

                        <div className="flex items-center justify-between mb-3">
                          <div className="flex items-center space-x-4">
                            {review.wouldRecommend && (
                              <Badge variant="outline" className="text-green-600 border-green-600">
                                Recommends
                              </Badge>
                            )}
                            {review.weddingDate && (
                              <div className="flex items-center text-sm text-gray-600">
                                <Calendar className="h-3 w-3 mr-1" />
                                Wedding: {formatDate(review.weddingDate)}
                              </div>
                            )}
                          </div>
                          
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleMarkHelpful(review.id)}
                            className="text-gray-600 hover:text-primary"
                          >
                            <ThumbsUp className="h-4 w-4 mr-1" />
                            Helpful ({review.helpfulCount || 0})
                          </Button>
                        </div>

                        <div className="border-t pt-3">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => setReplyingTo(replyingTo === review.id ? null : review.id)}
                            className="text-gray-600 hover:text-primary mb-2"
                          >
                            <Reply className="h-4 w-4 mr-1" />
                            Reply
                            {review.replyCount && review.replyCount > 0 && (
                              <span className="ml-1 text-xs bg-gray-200 px-2 py-1 rounded-full">
                                {review.replyCount}
                              </span>
                            )}
                          </Button>

                          {replyingTo === review.id && (
                            <div className="bg-gray-50 p-3 rounded-lg mb-3">
                              <Textarea
                                placeholder="Write your reply..."
                                value={replyContent}
                                onChange={(e) => setReplyContent(e.target.value)}
                                className="mb-2"
                                rows={3}
                              />
                              <div className="flex items-center space-x-2">
                                <Button
                                  size="sm"
                                  onClick={() => handleReply(review.id)}
                                  disabled={submittingReply || !replyContent.trim()}
                                >
                                  {submittingReply ? 'Sending...' : <Send className="h-4 w-4 mr-1" />}
                                  Send Reply
                                </Button>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => {
                                    setReplyingTo(null)
                                    setReplyContent('')
                                  }}
                                >
                                  <X className="h-4 w-4" />
                                </Button>
                              </div>
                            </div>
                          )}

                          {review.replies && review.replies.length > 0 && (
                            <div className="mt-3">
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => toggleReplies(review.id)}
                                className="text-gray-600 hover:text-primary mb-2"
                              >
                                {expandedReplies.has(review.id) ? (
                                  <ChevronUp className="h-4 w-4 mr-1" />
                                ) : (
                                  <ChevronDown className="h-4 w-4 mr-1" />
                                )}
                                {expandedReplies.has(review.id) ? 'Hide' : 'Show'} {review.replies.length} {review.replies.length === 1 ? 'reply' : 'replies'}
                              </Button>

                              {expandedReplies.has(review.id) && (
                                <div className="space-y-3 pl-4 border-l-2 border-gray-200">
                                  {review.replies.map((reply) => (
                                    <div key={reply.id} className="bg-white p-3 rounded-lg border">
                                      <div className="flex items-start space-x-3">
                                        <Avatar className="h-6 w-6">
                                          <AvatarFallback className="text-xs">
                                            {reply.name.charAt(0).toUpperCase()}
                                          </AvatarFallback>
                                        </Avatar>
                                        <div className="flex-1">
                                          <div className="flex items-center space-x-2 mb-1">
                                            <span className="font-medium text-sm">{reply.name}</span>
                                            {reply.isOwner && (
                                              <Badge variant="outline" className="text-xs">
                                                Owner
                                              </Badge>
                                            )}
                                            <span className="text-xs text-gray-500">
                                              {formatDate(reply.createdAt)}
                                            </span>
                                          </div>
                                          <p className="text-sm text-gray-700">{reply.content}</p>
                                        </div>
                                      </div>
                                    </div>
                                  ))}
                                </div>
                              )}
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              {!showAllReviews && reviews.length > reviewsPerPage && (
                <div className="mt-6 flex items-center justify-between">
                  <div className="text-sm text-gray-600">
                    Showing {startIndex + 1}-{Math.min(endIndex, reviews.length)} of {reviews.length} reviews
                  </div>
                  <div className="flex items-center space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handlePageChange(currentPage - 1)}
                      disabled={currentPage === 1}
                    >
                      Previous
                    </Button>
                    <div className="flex items-center space-x-1">
                      {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
                        <Button
                          key={page}
                          variant={currentPage === page ? "default" : "outline"}
                          size="sm"
                          onClick={() => handlePageChange(page)}
                          className="w-8 h-8 p-0"
                        >
                          {page}
                        </Button>
                      ))}
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handlePageChange(currentPage + 1)}
                      disabled={currentPage === totalPages}
                    >
                      Next
                    </Button>
                  </div>
                </div>
              )}

              {reviews.length > reviewsPerPage && (
                <div className="mt-4 text-center">
                  <Button
                    variant="ghost"
                    onClick={toggleShowAllReviews}
                    className="text-primary hover:text-primary/80"
                  >
                    {showAllReviews ? 'Show Less' : `Show All ${reviews.length} Reviews`}
                  </Button>
                </div>
              )}
            </>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
