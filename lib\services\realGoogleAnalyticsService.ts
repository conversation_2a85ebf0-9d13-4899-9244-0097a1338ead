import { BetaAnalyticsDataClient } from '@google-analytics/data'

export class RealGoogleAnalyticsService {
  private static client: BetaAnalyticsDataClient
  private static propertyId = process.env.GA4_PROPERTY_ID || ''

  static initialize() {
    this.client = new BetaAnalyticsDataClient({
      keyFilename: process.env.GOOGLE_APPLICATION_CREDENTIALS,
      projectId: process.env.GOOGLE_CLOUD_PROJECT_ID
    })
  }

  static async getVendorPageAnalytics(vendorId: string, dateRange: string) {
    try {
      const [startDate, endDate] = this.getDateRange(dateRange)
      
      const [pageViews, sessions, users, events] = await Promise.all([
        this.getPageViews(vendorId, startDate, endDate),
        this.getSessions(vendorId, startDate, endDate),
        this.getUsers(vendorId, startDate, endDate),
        this.getEvents(vendorId, startDate, endDate)
      ])

      return {
        pageViews: pageViews.totalPageViews,
        uniqueVisitors: users.totalUsers,
        sessions: sessions.totalSessions,
        bounceRate: sessions.bounceRate,
        avgSessionDuration: sessions.avgSessionDuration,
        trafficSources: await this.getTrafficSources(vendorId, startDate, endDate),
        geographic: await this.getGeographicData(vendorId, startDate, endDate),
        deviceTypes: await this.getDeviceData(vendorId, startDate, endDate)
      }
    } catch (error) {
      console.error('GA4 API Error:', error)
      throw error
    }
  }

  private static async getPageViews(vendorId: string, startDate: string, endDate: string) {
    const [response] = await this.client.runReport({
      property: `properties/${this.propertyId}`,
      dateRanges: [{ startDate, endDate }],
      dimensions: [{ name: 'pagePath' }],
      metrics: [{ name: 'screenPageViews' }],
      dimensionFilter: {
        filter: {
          fieldName: 'pagePath',
          stringFilter: {
            matchType: 'CONTAINS',
            value: `/vendors/${vendorId}`
          }
        }
      }
    })

    return {
      totalPageViews: response.rows?.reduce((sum, row) => 
        sum + parseInt(row.metricValues?.[0]?.value || '0'), 0) || 0
    }
  }

  private static async getSessions(vendorId: string, startDate: string, endDate: string) {
    const [response] = await this.client.runReport({
      property: `properties/${this.propertyId}`,
      dateRanges: [{ startDate, endDate }],
      metrics: [
        { name: 'sessions' },
        { name: 'bounceRate' },
        { name: 'averageSessionDuration' }
      ],
      dimensionFilter: {
        filter: {
          fieldName: 'pagePath',
          stringFilter: {
            matchType: 'CONTAINS',
            value: `/vendors/${vendorId}`
          }
        }
      }
    })

    const row = response.rows?.[0]
    return {
      totalSessions: parseInt(row?.metricValues?.[0]?.value || '0'),
      bounceRate: parseFloat(row?.metricValues?.[1]?.value || '0'),
      avgSessionDuration: row?.metricValues?.[2]?.value || '0'
    }
  }

  private static async getUsers(vendorId: string, startDate: string, endDate: string) {
    const [response] = await this.client.runReport({
      property: `properties/${this.propertyId}`,
      dateRanges: [{ startDate, endDate }],
      metrics: [{ name: 'totalUsers' }],
      dimensionFilter: {
        filter: {
          fieldName: 'pagePath',
          stringFilter: {
            matchType: 'CONTAINS',
            value: `/vendors/${vendorId}`
          }
        }
      }
    })

    return {
      totalUsers: parseInt(response.rows?.[0]?.metricValues?.[0]?.value || '0')
    }
  }

  private static async getEvents(vendorId: string, startDate: string, endDate: string) {
    const [response] = await this.client.runReport({
      property: `properties/${this.propertyId}`,
      dateRanges: [{ startDate, endDate }],
      dimensions: [{ name: 'eventName' }],
      metrics: [{ name: 'eventCount' }],
      dimensionFilter: {
        filter: {
          fieldName: 'customEvent:vendor_id',
          stringFilter: {
            matchType: 'EXACT',
            value: vendorId
          }
        }
      }
    })

    const events: Record<string, number> = {}
    response.rows?.forEach(row => {
      const eventName = row.dimensionValues?.[0]?.value || ''
      const eventCount = parseInt(row.metricValues?.[0]?.value || '0')
      events[eventName] = eventCount
    })

    return events
  }

  private static async getTrafficSources(vendorId: string, startDate: string, endDate: string) {
    const [response] = await this.client.runReport({
      property: `properties/${this.propertyId}`,
      dateRanges: [{ startDate, endDate }],
      dimensions: [{ name: 'sessionDefaultChannelGrouping' }],
      metrics: [{ name: 'sessions' }],
      dimensionFilter: {
        filter: {
          fieldName: 'pagePath',
          stringFilter: {
            matchType: 'CONTAINS',
            value: `/vendors/${vendorId}`
          }
        }
      }
    })

    return response.rows?.map(row => ({
      source: row.dimensionValues?.[0]?.value || '',
      sessions: parseInt(row.metricValues?.[0]?.value || '0')
    })) || []
  }

  private static async getGeographicData(vendorId: string, startDate: string, endDate: string) {
    const [response] = await this.client.runReport({
      property: `properties/${this.propertyId}`,
      dateRanges: [{ startDate, endDate }],
      dimensions: [{ name: 'region' }],
      metrics: [{ name: 'sessions' }],
      dimensionFilter: {
        filter: {
          fieldName: 'pagePath',
          stringFilter: {
            matchType: 'CONTAINS',
            value: `/vendors/${vendorId}`
          }
        }
      }
    })

    return response.rows?.map(row => ({
      region: row.dimensionValues?.[0]?.value || '',
      sessions: parseInt(row.metricValues?.[0]?.value || '0')
    })) || []
  }

  private static async getDeviceData(vendorId: string, startDate: string, endDate: string) {
    const [response] = await this.client.runReport({
      property: `properties/${this.propertyId}`,
      dateRanges: [{ startDate, endDate }],
      dimensions: [{ name: 'deviceCategory' }],
      metrics: [{ name: 'sessions' }],
      dimensionFilter: {
        filter: {
          fieldName: 'pagePath',
          stringFilter: {
            matchType: 'CONTAINS',
            value: `/vendors/${vendorId}`
          }
        }
      }
    })

    return response.rows?.map(row => ({
      device: row.dimensionValues?.[0]?.value || '',
      sessions: parseInt(row.metricValues?.[0]?.value || '0')
    })) || []
  }

  private static getDateRange(range: string): [string, string] {
    const end = new Date()
    const start = new Date()
    
    switch (range) {
      case '7d': start.setDate(end.getDate() - 7); break
      case '30d': start.setDate(end.getDate() - 30); break
      case '90d': start.setDate(end.getDate() - 90); break
      case '1y': start.setFullYear(end.getFullYear() - 1); break
    }
    
    return [
      start.toISOString().split('T')[0],
      end.toISOString().split('T')[0]
    ]
  }
}