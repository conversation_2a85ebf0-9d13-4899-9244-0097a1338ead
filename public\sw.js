// BookmyFestive Service Worker
const VERSION = '1.0.2-1753946177523'; // Auto-increment based on build time
const CACHE_NAME = `BookmyFestive-v${VERSION}`;
const STATIC_CACHE = `BookmyFestive-static-v${VERSION}`;
const DYNAMIC_CACHE = `BookmyFestive-dynamic-v${VERSION}`;

// Cache duration in milliseconds (5 minutes for development, 1 hour for production)
// Note: In service worker, we can't access process.env, so we use a reasonable default
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes for development

// Files to cache immediately
const STATIC_FILES = [
  '/',
  '/manifest.json',
  '/BookmyFestive_favicon.png',
  '/hero_image_1.webp',
  '/hero_image_2.webp',
  '/hero_image_3.webp',
  '/offline',
  '/vendors',
  '/venues',
  '/shop'
];

// Install event - cache static files
self.addEventListener('install', (event) => {
  console.log('Service Worker: Installing...');
  event.waitUntil(
    caches.open(STATIC_CACHE)
      .then((cache) => {
        console.log('Service Worker: Caching static files');
        return cache.addAll(STATIC_FILES);
      })
      .then(() => {
        console.log('Service Worker: Static files cached');
        return self.skipWaiting();
      })
      .catch((error) => {
        console.error('Service Worker: Error caching static files', error);
      })
  );
});

// Activate event - clean up old caches
self.addEventListener('activate', (event) => {
  console.log('Service Worker: Activating...');
  event.waitUntil(
    caches.keys()
      .then((cacheNames) => {
        return Promise.all(
          cacheNames.map((cacheName) => {
            if (cacheName !== STATIC_CACHE && cacheName !== DYNAMIC_CACHE) {
              console.log('Service Worker: Deleting old cache', cacheName);
              return caches.delete(cacheName);
            }
          })
        );
      })
      .then(() => {
        console.log('Service Worker: Activated');
        return self.clients.claim();
      })
  );
});

// Fetch event - intelligent caching strategy
self.addEventListener('fetch', (event) => {
  const { request } = event;
  const url = new URL(request.url);

  // Skip non-GET requests
  if (request.method !== 'GET') return;

  // Skip external requests
  if (url.origin !== location.origin) return;

  // Different strategies for different types of requests
  if (isHTMLRequest(request)) {
    // Network-first for HTML pages (ensures fresh content)
    event.respondWith(networkFirstStrategy(request));
  } else if (isAPIRequest(request)) {
    // Network-first for API calls (ensures fresh data)
    event.respondWith(networkFirstStrategy(request));
  } else if (isStaticAsset(request)) {
    // Stale-while-revalidate for static assets (shows cached version immediately, updates in background)
    event.respondWith(staleWhileRevalidateStrategy(request));
  } else {
    // Default to network-first for other requests
    event.respondWith(networkFirstStrategy(request));
  }
});

// Helper function to check if request is for HTML
function isHTMLRequest(request) {
  return request.destination === 'document' ||
         request.headers.get('accept')?.includes('text/html');
}

// Helper function to check if request is for API
function isAPIRequest(request) {
  const url = new URL(request.url);
  return url.pathname.startsWith('/api/') ||
         url.pathname.includes('graphql') ||
         request.headers.get('accept')?.includes('application/json');
}

// Helper function to check if request is for static assets
function isStaticAsset(request) {
  const url = new URL(request.url);
  return /\.(css|js|png|jpg|jpeg|gif|webp|svg|ico|woff|woff2|ttf|eot)$/i.test(url.pathname);
}

// Network-first strategy
async function networkFirstStrategy(request) {
  try {
    const networkResponse = await fetch(request);

    // Cache successful responses
    if (networkResponse && networkResponse.status === 200) {
      const cache = await caches.open(DYNAMIC_CACHE);
      cache.put(request, networkResponse.clone());
    }

    return networkResponse;
  } catch (error) {
    console.log('Network failed, trying cache:', error);

    // Fallback to cache
    const cachedResponse = await caches.match(request);
    if (cachedResponse) {
      return cachedResponse;
    }

    // Return offline page for navigation requests
    if (isHTMLRequest(request)) {
      return caches.match('/offline');
    }

    throw error;
  }
}

// Stale-while-revalidate strategy for static assets
async function staleWhileRevalidateStrategy(request) {
  const cachedResponse = await caches.match(request);

  // Always try to fetch from network in background
  const fetchPromise = fetch(request).then(async (networkResponse) => {
    if (networkResponse && networkResponse.status === 200) {
      const cache = await caches.open(DYNAMIC_CACHE);
      cache.put(request, networkResponse.clone());
    }
    return networkResponse;
  }).catch(() => {
    // Network failed, that's okay for this strategy
    return null;
  });

  // If we have a cached response, return it immediately
  if (cachedResponse) {
    // Check if cache is stale (older than cache duration)
    const cacheDate = new Date(cachedResponse.headers.get('date') || 0);
    const now = new Date();
    const isStale = (now.getTime() - cacheDate.getTime()) > CACHE_DURATION;

    if (!isStale) {
      // Cache is fresh, return it and update in background
      fetchPromise; // Don't await, let it run in background
      return cachedResponse;
    }
  }

  // No cache or cache is stale, wait for network
  try {
    const networkResponse = await fetchPromise;
    return networkResponse || cachedResponse; // Fallback to cache if network fails
  } catch (error) {
    // Return cached version if available
    if (cachedResponse) {
      return cachedResponse;
    }
    throw error;
  }
}

// Handle background sync
self.addEventListener('sync', (event) => {
  console.log('Service Worker: Background sync', event.tag);
  
  if (event.tag === 'background-sync') {
    event.waitUntil(
      // Handle background sync tasks
      handleBackgroundSync()
    );
  }
});

// Handle push notifications
self.addEventListener('push', (event) => {
  console.log('Service Worker: Push notification received');
  
  const options = {
    body: event.data ? event.data.text() : 'New update from BookmyFestive',
    icon: '/BookmyFestive_favicon.png',
    badge: '/BookmyFestive_favicon.png',
    vibrate: [100, 50, 100],
    data: {
      dateOfArrival: Date.now(),
      primaryKey: 1
    },
    actions: [
      {
        action: 'explore',
        title: 'Explore',
        icon: '/BookmyFestive_favicon.png'
      },
      {
        action: 'close',
        title: 'Close',
        icon: '/BookmyFestive_favicon.png'
      }
    ]
  };

  event.waitUntil(
    self.registration.showNotification('BookmyFestive', options)
  );
});

// Handle notification clicks
self.addEventListener('notificationclick', (event) => {
  console.log('Service Worker: Notification clicked');
  
  event.notification.close();

  if (event.action === 'explore') {
    event.waitUntil(
      clients.openWindow('/')
    );
  } else if (event.action === 'close') {
    // Just close the notification
    return;
  } else {
    // Default action - open the app
    event.waitUntil(
      clients.openWindow('/')
    );
  }
});

// Handle file sharing and cache management
self.addEventListener('message', (event) => {
  if (event.data && event.data.type === 'SHARE_TARGET') {
    console.log('Service Worker: Share target received', event.data);

    // Handle shared files
    if (event.data.files && event.data.files.length > 0) {
      // Process shared files
      handleSharedFiles(event.data.files);
    }

    // Redirect to share page
    event.waitUntil(
      clients.openWindow('/share?' + new URLSearchParams(event.data.params))
    );
  }

  // Handle cache refresh requests
  if (event.data && event.data.type === 'SKIP_WAITING') {
    console.log('Service Worker: Skipping waiting and taking control');
    self.skipWaiting();
  }

  if (event.data && event.data.type === 'CLEAR_CACHE') {
    console.log('Service Worker: Clearing all caches');
    event.waitUntil(
      caches.keys().then(cacheNames => {
        return Promise.all(
          cacheNames.map(cacheName => caches.delete(cacheName))
        );
      })
    );
  }

  // Handle logout - clear auth-related caches
  if (event.data && event.data.type === 'LOGOUT') {
    console.log('Service Worker: Handling logout - clearing auth caches');
    event.waitUntil(
      clearAuthCaches()
    );
  }
});

// Helper function for background sync
async function handleBackgroundSync() {
  try {
    // Handle any pending operations
    console.log('Service Worker: Handling background sync');
    
    // Example: sync offline data, send analytics, etc.
    return Promise.resolve();
  } catch (error) {
    console.error('Service Worker: Background sync failed', error);
    throw error;
  }
}

// Helper function for handling shared files
async function handleSharedFiles(files) {
  try {
    console.log('Service Worker: Processing shared files', files);
    
    // Process shared files (e.g., store in IndexedDB, upload to server)
    // This is a placeholder - implement based on your needs
    
    return Promise.resolve();
  } catch (error) {
    console.error('Service Worker: Failed to process shared files', error);
    throw error;
  }
}

// Helper function for clearing auth-related caches
async function clearAuthCaches() {
  try {
    console.log('Service Worker: Clearing auth-related caches');
    
    // Clear dynamic cache that might contain auth-related API responses
    const cache = await caches.open(DYNAMIC_CACHE);
    const requests = await cache.keys();
    
    // Remove auth-related cached requests
    const authRequests = requests.filter(request => {
      const url = new URL(request.url);
      return url.pathname.includes('/api/auth') || 
             url.pathname.includes('/dashboard') ||
             url.pathname.includes('/profile') ||
             url.pathname.includes('graphql');
    });
    
    await Promise.all(
      authRequests.map(request => cache.delete(request))
    );
    
    console.log('Service Worker: Auth caches cleared');
    return Promise.resolve();
  } catch (error) {
    console.error('Service Worker: Failed to clear auth caches', error);
    throw error;
  }
}

// Handle periodic background sync
self.addEventListener('periodicsync', (event) => {
  if (event.tag === 'content-sync') {
    event.waitUntil(
      // Sync content in the background
      handlePeriodicSync()
    );
  }
});

// Helper function for periodic sync
async function handlePeriodicSync() {
  try {
    console.log('Service Worker: Handling periodic sync');
    
    // Example: update cached content, sync user data, etc.
    return Promise.resolve();
  } catch (error) {
    console.error('Service Worker: Periodic sync failed', error);
    throw error;
  }
}
