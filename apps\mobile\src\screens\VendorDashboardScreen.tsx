import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  RefreshControl,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { useTheme } from '../providers/ThemeProvider';
import { useAuth } from '../providers/AuthProvider';
import LoadingSpinner from '../components/LoadingSpinner';

interface DashboardStats {
  totalProducts: number;
  totalOrders: number;
  pendingOrders: number;
  totalRevenue: number;
  monthlyRevenue: number;
  totalReviews: number;
  averageRating: number;
  profileViews: number;
}

interface RecentOrder {
  id: string;
  customerName: string;
  items: number;
  total: number;
  status: 'pending' | 'confirmed' | 'processing' | 'shipped' | 'delivered';
  createdAt: string;
}

export default function VendorDashboardScreen() {
  const navigation = useNavigation();
  const { theme } = useTheme();
  const { user, userProfile } = useAuth();
  
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [recentOrders, setRecentOrders] = useState<RecentOrder[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      
      // TODO: Replace with actual GraphQL queries
      // const vendorStats = await graphqlService.getVendorStats(user?.id);
      // const vendorOrders = await graphqlService.getVendorRecentOrders(user?.id);
      
      // Mock data for now
      const mockStats: DashboardStats = {
        totalProducts: 24,
        totalOrders: 156,
        pendingOrders: 8,
        totalRevenue: 485000,
        monthlyRevenue: 45000,
        totalReviews: 89,
        averageRating: 4.6,
        profileViews: 1250,
      };

      const mockOrders: RecentOrder[] = [
        {
          id: 'ORD_001',
          customerName: 'Priya Sharma',
          items: 2,
          total: 25000,
          status: 'pending',
          createdAt: '2024-01-25T10:30:00Z',
        },
        {
          id: 'ORD_002',
          customerName: 'Rahul Kumar',
          items: 1,
          total: 15000,
          status: 'confirmed',
          createdAt: '2024-01-24T14:15:00Z',
        },
        {
          id: 'ORD_003',
          customerName: 'Anita Patel',
          items: 3,
          total: 35000,
          status: 'processing',
          createdAt: '2024-01-23T09:45:00Z',
        },
      ];
      
      setStats(mockStats);
      setRecentOrders(mockOrders);
    } catch (error) {
      console.error('Failed to load dashboard data:', error);
      Alert.alert('Error', 'Failed to load dashboard data');
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadDashboardData();
    setRefreshing(false);
  };

  const formatCurrency = (amount: number) => {
    return `₹${amount.toLocaleString('en-IN')}`;
  };

  const getStatusColor = (status: RecentOrder['status']) => {
    switch (status) {
      case 'pending':
        return theme.colors.warning;
      case 'confirmed':
      case 'processing':
        return theme.colors.info;
      case 'shipped':
        return theme.colors.primary;
      case 'delivered':
        return theme.colors.success;
      default:
        return theme.colors.textSecondary;
    }
  };

  const quickActions = [
    {
      icon: 'add-circle-outline',
      title: 'Add Product',
      subtitle: 'List new item',
      onPress: () => navigation.navigate('AddProduct' as never),
      color: theme.colors.primary,
    },
    {
      icon: 'list-outline',
      title: 'Manage Products',
      subtitle: 'Edit inventory',
      onPress: () => navigation.navigate('VendorProducts' as never),
      color: theme.colors.info,
    },
    {
      icon: 'receipt-outline',
      title: 'View Orders',
      subtitle: 'Track orders',
      onPress: () => navigation.navigate('VendorOrders' as never),
      color: theme.colors.success,
    },
    {
      icon: 'analytics-outline',
      title: 'Analytics',
      subtitle: 'View reports',
      onPress: () => navigation.navigate('VendorAnalytics' as never),
      color: theme.colors.secondary,
    },
  ];

  if (loading) {
    return <LoadingSpinner fullScreen text="Loading dashboard..." />;
  }

  return (
    <ScrollView 
      style={[styles.container, { backgroundColor: theme.colors.background }]}
      refreshControl={
        <RefreshControl
          refreshing={refreshing}
          onRefresh={onRefresh}
          colors={[theme.colors.primary]}
          tintColor={theme.colors.primary}
        />
      }
    >
      {/* Welcome Header */}
      <View style={[styles.header, { backgroundColor: theme.colors.primary }]}>
        <View style={styles.welcomeSection}>
          <Text style={styles.welcomeText}>Welcome back,</Text>
          <Text style={styles.businessName}>
            {userProfile?.businessInfo?.businessName || user?.fullName || 'Vendor'}
          </Text>
          <Text style={styles.businessType}>
            {userProfile?.businessInfo?.businessType || 'Festive Services'}
          </Text>
        </View>
        
        <TouchableOpacity 
          style={styles.profileButton}
          onPress={() => navigation.navigate('Profile' as never)}
        >
          <Ionicons name="person-circle-outline" size={32} color="white" />
        </TouchableOpacity>
      </View>

      {/* Stats Overview */}
      {stats && (
        <View style={styles.statsContainer}>
          <View style={styles.statsGrid}>
            <View style={[styles.statCard, { backgroundColor: theme.colors.surface }]}>
              <Ionicons name="cube-outline" size={24} color={theme.colors.primary} />
              <Text style={[styles.statNumber, { color: theme.colors.text }]}>
                {stats.totalProducts}
              </Text>
              <Text style={[styles.statLabel, { color: theme.colors.textSecondary }]}>
                Products
              </Text>
            </View>

            <View style={[styles.statCard, { backgroundColor: theme.colors.surface }]}>
              <Ionicons name="receipt-outline" size={24} color={theme.colors.success} />
              <Text style={[styles.statNumber, { color: theme.colors.text }]}>
                {stats.totalOrders}
              </Text>
              <Text style={[styles.statLabel, { color: theme.colors.textSecondary }]}>
                Total Orders
              </Text>
            </View>

            <View style={[styles.statCard, { backgroundColor: theme.colors.surface }]}>
              <Ionicons name="time-outline" size={24} color={theme.colors.warning} />
              <Text style={[styles.statNumber, { color: theme.colors.text }]}>
                {stats.pendingOrders}
              </Text>
              <Text style={[styles.statLabel, { color: theme.colors.textSecondary }]}>
                Pending
              </Text>
            </View>

            <View style={[styles.statCard, { backgroundColor: theme.colors.surface }]}>
              <Ionicons name="cash-outline" size={24} color={theme.colors.info} />
              <Text style={[styles.statNumber, { color: theme.colors.text }]}>
                {formatCurrency(stats.monthlyRevenue)}
              </Text>
              <Text style={[styles.statLabel, { color: theme.colors.textSecondary }]}>
                This Month
              </Text>
            </View>
          </View>

          {/* Revenue Card */}
          <View style={[styles.revenueCard, { backgroundColor: theme.colors.surface }]}>
            <View style={styles.revenueHeader}>
              <View>
                <Text style={[styles.revenueTitle, { color: theme.colors.text }]}>
                  Total Revenue
                </Text>
                <Text style={[styles.revenueAmount, { color: theme.colors.primary }]}>
                  {formatCurrency(stats.totalRevenue)}
                </Text>
              </View>
              
              <View style={styles.ratingContainer}>
                <Ionicons name="star" size={20} color="#FFD700" />
                <Text style={[styles.ratingText, { color: theme.colors.text }]}>
                  {stats.averageRating}
                </Text>
                <Text style={[styles.reviewCount, { color: theme.colors.textSecondary }]}>
                  ({stats.totalReviews} reviews)
                </Text>
              </View>
            </View>
          </View>
        </View>
      )}

      {/* Quick Actions */}
      <View style={styles.section}>
        <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
          Quick Actions
        </Text>
        
        <View style={styles.actionsGrid}>
          {quickActions.map((action, index) => (
            <TouchableOpacity
              key={index}
              style={[styles.actionCard, { backgroundColor: theme.colors.surface }]}
              onPress={action.onPress}
            >
              <Ionicons name={action.icon as any} size={28} color={action.color} />
              <Text style={[styles.actionTitle, { color: theme.colors.text }]}>
                {action.title}
              </Text>
              <Text style={[styles.actionSubtitle, { color: theme.colors.textSecondary }]}>
                {action.subtitle}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>

      {/* Recent Orders */}
      <View style={styles.section}>
        <View style={styles.sectionHeader}>
          <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
            Recent Orders
          </Text>
          <TouchableOpacity onPress={() => navigation.navigate('VendorOrders' as never)}>
            <Text style={[styles.viewAllText, { color: theme.colors.primary }]}>
              View All
            </Text>
          </TouchableOpacity>
        </View>

        {recentOrders.map((order) => (
          <TouchableOpacity
            key={order.id}
            style={[styles.orderCard, { backgroundColor: theme.colors.surface }]}
            onPress={() => navigation.navigate('OrderDetail' as never, { orderId: order.id } as never)}
          >
            <View style={styles.orderHeader}>
              <View>
                <Text style={[styles.orderCustomer, { color: theme.colors.text }]}>
                  {order.customerName}
                </Text>
                <Text style={[styles.orderId, { color: theme.colors.textSecondary }]}>
                  #{order.id}
                </Text>
              </View>
              
              <View style={styles.orderRight}>
                <Text style={[styles.orderAmount, { color: theme.colors.text }]}>
                  {formatCurrency(order.total)}
                </Text>
                <View style={[styles.statusBadge, { backgroundColor: getStatusColor(order.status) + '20' }]}>
                  <Text style={[styles.statusText, { color: getStatusColor(order.status) }]}>
                    {order.status}
                  </Text>
                </View>
              </View>
            </View>
            
            <Text style={[styles.orderDetails, { color: theme.colors.textSecondary }]}>
              {order.items} item{order.items > 1 ? 's' : ''} • {new Date(order.createdAt).toLocaleDateString()}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    paddingTop: 40,
  },
  welcomeSection: {
    flex: 1,
  },
  welcomeText: {
    color: 'white',
    fontSize: 16,
    opacity: 0.9,
  },
  businessName: {
    color: 'white',
    fontSize: 24,
    fontWeight: 'bold',
    marginTop: 4,
  },
  businessType: {
    color: 'white',
    fontSize: 14,
    opacity: 0.8,
    marginTop: 2,
  },
  profileButton: {
    padding: 8,
  },
  statsContainer: {
    padding: 16,
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
    marginBottom: 16,
  },
  statCard: {
    flex: 1,
    minWidth: '45%',
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  statNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    marginTop: 8,
  },
  statLabel: {
    fontSize: 12,
    marginTop: 4,
  },
  revenueCard: {
    padding: 16,
    borderRadius: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  revenueHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  revenueTitle: {
    fontSize: 14,
    marginBottom: 4,
  },
  revenueAmount: {
    fontSize: 28,
    fontWeight: 'bold',
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  ratingText: {
    fontSize: 16,
    fontWeight: '600',
  },
  reviewCount: {
    fontSize: 12,
  },
  section: {
    padding: 16,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  viewAllText: {
    fontSize: 14,
    fontWeight: '600',
  },
  actionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  actionCard: {
    flex: 1,
    minWidth: '45%',
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  actionTitle: {
    fontSize: 14,
    fontWeight: '600',
    marginTop: 8,
    textAlign: 'center',
  },
  actionSubtitle: {
    fontSize: 12,
    marginTop: 2,
    textAlign: 'center',
  },
  orderCard: {
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  orderHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  orderCustomer: {
    fontSize: 16,
    fontWeight: '600',
  },
  orderId: {
    fontSize: 12,
    marginTop: 2,
  },
  orderRight: {
    alignItems: 'flex-end',
  },
  orderAmount: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    fontSize: 10,
    fontWeight: '600',
    textTransform: 'uppercase',
  },
  orderDetails: {
    fontSize: 12,
  },
});
