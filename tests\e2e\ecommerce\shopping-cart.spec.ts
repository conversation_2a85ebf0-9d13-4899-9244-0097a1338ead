import { test, expect } from '@playwright/test';
import { AuthHelpers } from '../utils/auth-helpers';
import { EcommerceHelpers } from '../utils/ecommerce-helpers';

test.describe('Shopping Cart', () => {
  let authHelpers: AuthHelpers;
  let ecommerceHelpers: EcommerceHelpers;

  test.beforeEach(async ({ page }) => {
    authHelpers = new AuthHelpers(page);
    ecommerceHelpers = new EcommerceHelpers(page);
    
    // Login before each test
    try {
      await authHelpers.login('<EMAIL>', 'TestPassword123!');
    } catch {
      // Skip tests if login fails
      test.skip(true, 'Test user not available');
    }
  });

  test('should display shop page with products', async ({ page }) => {
    await ecommerceHelpers.navigateToShop();
    
    // Check if products are displayed
    const productElements = [
      '.product-card',
      '[data-testid="product-card"]',
      '.shop-item',
      '.product-item'
    ];

    let productsFound = false;
    for (const selector of productElements) {
      if (await ecommerceHelpers.elementExists(selector)) {
        const products = page.locator(selector);
        if (await products.count() > 0) {
          productsFound = true;
          break;
        }
      }
    }

    expect(productsFound).toBe(true);
  });

  test('should add product to cart', async ({ page }) => {
    await ecommerceHelpers.navigateToShop();
    
    // Get initial cart count
    const initialCartCount = await ecommerceHelpers.getCartItemCount();
    
    // Add first product to cart
    await ecommerceHelpers.addProductToCart(0);
    
    // Verify cart count increased
    const newCartCount = await ecommerceHelpers.getCartItemCount();
    expect(newCartCount).toBeGreaterThan(initialCartCount);
  });

  test('should display cart items', async ({ page }) => {
    await ecommerceHelpers.navigateToShop();
    
    // Add a product to cart first
    await ecommerceHelpers.addProductToCart(0);
    
    // View cart
    await ecommerceHelpers.viewCart();
    
    // Check if cart items are displayed
    const cartItemElements = [
      '.cart-item',
      '[data-testid="cart-item"]',
      '.cart-product'
    ];

    let cartItemsFound = false;
    for (const selector of cartItemElements) {
      if (await ecommerceHelpers.elementExists(selector)) {
        const items = page.locator(selector);
        if (await items.count() > 0) {
          cartItemsFound = true;
          break;
        }
      }
    }

    expect(cartItemsFound).toBe(true);
  });

  test('should update product quantity in cart', async ({ page }) => {
    await ecommerceHelpers.navigateToShop();
    
    // Add product to cart
    await ecommerceHelpers.addProductToCart(0);
    
    // Update quantity
    await ecommerceHelpers.updateCartQuantity(0, 3);
    
    // Verify quantity was updated
    await ecommerceHelpers.viewCart();
    const quantityInput = page.locator('.cart-item input[type="number"], [data-testid="quantity-input"]').first();
    
    if (await quantityInput.isVisible()) {
      const quantity = await quantityInput.inputValue();
      expect(quantity).toBe('3');
    }
  });

  test('should remove product from cart', async ({ page }) => {
    await ecommerceHelpers.navigateToShop();
    
    // Add product to cart
    await ecommerceHelpers.addProductToCart(0);
    
    // Get cart count before removal
    const beforeRemovalCount = await ecommerceHelpers.getCartItemCount();
    
    // Remove product from cart
    await ecommerceHelpers.removeFromCart(0);
    
    // Verify cart count decreased
    const afterRemovalCount = await ecommerceHelpers.getCartItemCount();
    expect(afterRemovalCount).toBeLessThan(beforeRemovalCount);
  });

  test('should calculate total price correctly', async ({ page }) => {
    await ecommerceHelpers.navigateToShop();
    
    // Add multiple products to cart
    await ecommerceHelpers.addProductToCart(0);
    
    // Check if there are more products to add
    const productCards = page.locator('.product-card, [data-testid="product-card"]');
    const productCount = await productCards.count();
    
    if (productCount > 1) {
      await ecommerceHelpers.addProductToCart(1);
    }
    
    // View cart and check for total
    await ecommerceHelpers.viewCart();
    
    const totalElements = [
      '.cart-total',
      '[data-testid="cart-total"]',
      '.total-price',
      'text=Total:'
    ];

    let totalFound = false;
    for (const selector of totalElements) {
      if (await ecommerceHelpers.elementExists(selector)) {
        totalFound = true;
        break;
      }
    }

    expect(totalFound).toBe(true);
  });

  test('should clear entire cart', async ({ page }) => {
    await ecommerceHelpers.navigateToShop();
    
    // Add products to cart
    await ecommerceHelpers.addProductToCart(0);
    
    // Clear cart
    await ecommerceHelpers.clearCart();
    
    // Verify cart is empty
    const isEmpty = await ecommerceHelpers.isCartEmpty();
    expect(isEmpty).toBe(true);
  });

  test('should show empty cart message when no items', async ({ page }) => {
    // Ensure cart is empty
    await ecommerceHelpers.clearCart();
    
    // View cart
    await ecommerceHelpers.viewCart();
    
    // Check for empty cart message
    const emptyCartMessages = [
      'text=Your cart is empty',
      'text=No items in cart',
      'text=Cart is empty',
      '.empty-cart'
    ];

    let emptyMessageFound = false;
    for (const message of emptyCartMessages) {
      if (await ecommerceHelpers.elementExists(message)) {
        emptyMessageFound = true;
        break;
      }
    }

    expect(emptyMessageFound).toBe(true);
  });

  test('should persist cart items across page navigation', async ({ page }) => {
    await ecommerceHelpers.navigateToShop();
    
    // Add product to cart
    await ecommerceHelpers.addProductToCart(0);
    
    // Get cart count
    const cartCount = await ecommerceHelpers.getCartItemCount();
    
    // Navigate to different page
    await ecommerceHelpers.navigateTo('/');
    
    // Check if cart count is still the same
    const persistedCartCount = await ecommerceHelpers.getCartItemCount();
    expect(persistedCartCount).toBe(cartCount);
  });

  test('should show product details in cart', async ({ page }) => {
    await ecommerceHelpers.navigateToShop();
    
    // Add product to cart
    await ecommerceHelpers.addProductToCart(0);
    
    // View cart
    await ecommerceHelpers.viewCart();
    
    // Check for product details in cart
    const productDetailElements = [
      '.product-name, [data-testid="product-name"]',
      '.product-price, [data-testid="product-price"]',
      '.product-image, [data-testid="product-image"]'
    ];

    let detailsFound = false;
    for (const selector of productDetailElements) {
      if (await ecommerceHelpers.elementExists(selector)) {
        detailsFound = true;
        break;
      }
    }

    expect(detailsFound).toBe(true);
  });

  test('should handle quantity limits', async ({ page }) => {
    await ecommerceHelpers.navigateToShop();
    
    // Add product to cart
    await ecommerceHelpers.addProductToCart(0);
    
    // Try to set very high quantity
    await ecommerceHelpers.updateCartQuantity(0, 999);
    
    // Check if quantity was limited or error shown
    await ecommerceHelpers.viewCart();
    
    const quantityInput = page.locator('.cart-item input[type="number"]').first();
    if (await quantityInput.isVisible()) {
      const quantity = parseInt(await quantityInput.inputValue());
      
      // Quantity should be reasonable (less than 999 if there are limits)
      // Or error message should be shown
      const errorMessages = [
        'text=Quantity limit exceeded',
        'text=Maximum quantity',
        'text=Stock limit'
      ];

      let errorFound = false;
      for (const error of errorMessages) {
        if (await ecommerceHelpers.elementExists(error)) {
          errorFound = true;
          break;
        }
      }

      // Either quantity was limited or error was shown
      expect(quantity < 999 || errorFound).toBe(true);
    }
  });

  test('should show continue shopping option', async ({ page }) => {
    await ecommerceHelpers.navigateToShop();
    
    // Add product to cart
    await ecommerceHelpers.addProductToCart(0);
    
    // View cart
    await ecommerceHelpers.viewCart();
    
    // Check for continue shopping link/button
    const continueShoppingElements = [
      'button:has-text("Continue Shopping")',
      'a:has-text("Continue Shopping")',
      'button:has-text("Back to Shop")',
      '[data-testid="continue-shopping"]'
    ];

    let continueShoppingFound = false;
    for (const selector of continueShoppingElements) {
      if (await ecommerceHelpers.elementExists(selector)) {
        continueShoppingFound = true;
        break;
      }
    }

    expect(continueShoppingFound).toBe(true);
  });
});
