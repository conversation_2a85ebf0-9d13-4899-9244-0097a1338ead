'use client';

import { NextSeo } from 'next-seo';
import { generatePageSEO, generateDynamicSEO, SITE_URL } from '@/lib/config/seo';

interface PageSEOProps {
  title?: string;
  description?: string;
  image?: string;
  url?: string;
  keywords?: string;
  noindex?: boolean;
  canonical?: string;
}

export const PageSEO: React.FC<PageSEOProps> = ({
  title,
  description,
  image,
  url,
  keywords,
  noindex = false,
  canonical,
}) => {
  const seoData = generateDynamicSEO(
    title || 'BookmyFestive',
    description || 'Your Dream Celebration Starts Here',
    image,
    url,
    keywords
  );

  return (
    <NextSeo
      title={seoData.title}
      description={seoData.description}
      canonical={canonical || seoData.canonical}
      noindex={noindex}
      openGraph={{
        ...seoData.openGraph,
        type: 'website',
        locale: 'en_US',
        siteName: 'BookmyFestive',
      }}
      twitter={{
        handle: '@BookmyFestive',
        site: '@BookmyFestive',
        cardType: 'summary_large_image',
      }}
      additionalMetaTags={[
        ...(seoData.additionalMetaTags || []),
        {
          name: 'robots',
          content: noindex ? 'noindex,nofollow' : 'index,follow',
        },
      ]}
    />
  );
};

// Specific SEO components for different page types
export const HomeSEO = () => {
  const seo = generatePageSEO('home');
  return (
    <PageSEO
      title={seo.title}
      description={seo.description}
      keywords={seo.keywords}
      url="/"
      image="/hero_image_1.jpg"
    />
  );
};

export const VendorsSEO = () => {
  const seo = generatePageSEO('vendors');
  return (
    <PageSEO
      title={seo.title}
      description={seo.description}
      keywords={seo.keywords}
      url="/vendors"
    />
  );
};

export const VenuesSEO = () => {
  const seo = generatePageSEO('venues');
  return (
    <PageSEO
      title={seo.title}
      description={seo.description}
      keywords={seo.keywords}
      url="/venues"
    />
  );
};

export const ShopSEO = () => {
  const seo = generatePageSEO('shop');
  return (
    <PageSEO
      title={seo.title}
      description={seo.description}
      keywords={seo.keywords}
      url="/shop"
    />
  );
};

export const BlogSEO = () => {
  const seo = generatePageSEO('blog');
  return (
    <PageSEO
      title={seo.title}
      description={seo.description}
      keywords={seo.keywords}
      url="/blog"
    />
  );
};

export const PhotosSEO = () => {
  const seo = generatePageSEO('photos');
  return (
    <PageSEO
      title={seo.title}
      description={seo.description}
      keywords={seo.keywords}
      url="/photos"
    />
  );
};

export const RealWeddingsSEO = () => {
  const seo = generatePageSEO('realWeddings');
  return (
    <PageSEO
      title={seo.title}
      description={seo.description}
      keywords={seo.keywords}
      url="/real-weddings"
    />
  );
};

export const PlanningSEO = () => {
  const seo = generatePageSEO('planning');
  return (
    <PageSEO
      title={seo.title}
      description={seo.description}
      keywords={seo.keywords}
      url="/planning"
    />
  );
};

export const ContactSEO = () => {
  const seo = generatePageSEO('contact');
  return (
    <PageSEO
      title={seo.title}
      description={seo.description}
      keywords={seo.keywords}
      url="/contact"
    />
  );
};

export const AboutSEO = () => {
  const seo = generatePageSEO('about');
  return (
    <PageSEO
      title={seo.title}
      description={seo.description}
      keywords={seo.keywords}
      url="/about"
    />
  );
};
