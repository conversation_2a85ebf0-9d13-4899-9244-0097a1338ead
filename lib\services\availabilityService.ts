"use client"

import { generateClient } from '@aws-amplify/api'
import { listBookings } from '@/src/graphql/queries'
import { createBooking, updateBooking } from '@/src/graphql/mutations'
import { getCurrentUser } from 'aws-amplify/auth'

// Create clients for different auth modes
const userPoolClient = generateClient({ authMode: 'userPool' })
const apiKeyClient = generateClient({ authMode: 'apiKey' })

// Helper function to get the appropriate client based on authentication
async function getAuthenticatedClient() {
  try {
    const user = await getCurrentUser()
    if (user) {
      return { client: userPoolClient, authMode: 'userPool', isAuthenticated: true }
    }
  } catch (error) {
    console.log('User not authenticated, using apiKey client')
  }
  return { client: apiKeyClient, authMode: 'apiKey', isAuthenticated: false }
}

export interface AvailabilityCheck {
  entityId: string
  entityType: 'VENDOR' | 'VENUE'
  eventDate: string
  eventTime?: string
  duration?: string
}

export interface AvailabilityResult {
  isAvailable: boolean
  conflictingBookings: any[]
  message: string
  suggestedDates?: string[]
}

export interface BookingConflict {
  bookingId: string
  customerName: string
  eventDate: string
  eventTime: string
  status: string
  duration?: string
}

export interface BookingReservation {
  entityId: string
  entityType: 'VENDOR' | 'VENUE'
  eventDate: string
  eventTime: string
  customerId: string
  expiresAt: string
  reservationId: string
}

export class AvailabilityService {
  // Reservation timeout in minutes
  private static RESERVATION_TIMEOUT = 15

  /**
   * Check if vendor/venue is available for a specific date and time
   */
  static async checkAvailability(params: AvailabilityCheck): Promise<AvailabilityResult> {
    try {
      // Get the appropriate client based on authentication status
      const { client } = await getAuthenticatedClient()

      // Get all confirmed bookings for the entity on the requested date
      const bookingsResult = await client.graphql({
        query: listBookings,
        variables: {
          filter: {
            and: [
              { entityId: { eq: params.entityId } },
              { eventDate: { eq: params.eventDate } },
              {
                or: [
                  { status: { eq: 'CONFIRMED' } },
                  { status: { eq: 'PENDING' } },
                  { status: { eq: 'IN_PROGRESS' } }
                ]
              }
            ]
          }
        }
      })

      const existingBookings = bookingsResult.data.listBookings.items || []

      // Check for time conflicts if time is specified
      const conflictingBookings = this.findTimeConflicts(
        existingBookings,
        params.eventTime,
        params.duration
      )

      const isAvailable = conflictingBookings.length === 0

      // Generate suggested alternative dates if not available
      const suggestedDates = !isAvailable 
        ? await this.generateSuggestedDates(params.entityId, params.entityType, params.eventDate)
        : []

      return {
        isAvailable,
        conflictingBookings,
        message: isAvailable 
          ? 'Available for booking'
          : `Not available - ${conflictingBookings.length} conflicting booking(s) found`,
        suggestedDates
      }
    } catch (error) {
      console.error('Error checking availability:', error)
      return {
        isAvailable: false,
        conflictingBookings: [],
        message: 'Unable to check availability. Please try again.'
      }
    }
  }

  /**
   * Find time conflicts between existing bookings and requested time
   */
  private static findTimeConflicts(
    existingBookings: any[],
    requestedTime?: string,
    requestedDuration?: string
  ): BookingConflict[] {
    if (!requestedTime) {
      // If no specific time requested, consider any booking on that date as a conflict
      // This is suitable for venues that can only handle one event per day
      return existingBookings.map(booking => ({
        bookingId: booking.id,
        customerName: booking.customerName,
        eventDate: booking.eventDate,
        eventTime: booking.eventTime,
        status: booking.status,
        duration: booking.duration
      }))
    }

    const conflicts: BookingConflict[] = []
    const requestedStart = this.parseTime(requestedTime)
    const requestedEnd = this.calculateEndTime(requestedStart, requestedDuration)

    for (const booking of existingBookings) {
      const bookingStart = this.parseTime(booking.eventTime)
      const bookingEnd = this.calculateEndTime(bookingStart, booking.duration)

      // Check for time overlap
      if (this.hasTimeOverlap(requestedStart, requestedEnd, bookingStart, bookingEnd)) {
        conflicts.push({
          bookingId: booking.id,
          customerName: booking.customerName,
          eventDate: booking.eventDate,
          eventTime: booking.eventTime,
          status: booking.status,
          duration: booking.duration
        })
      }
    }

    return conflicts
  }

  /**
   * Parse time string to minutes from midnight
   */
  private static parseTime(timeString: string): number {
    const [hours, minutes] = timeString.split(':').map(Number)
    return hours * 60 + minutes
  }

  /**
   * Calculate end time based on start time and duration
   */
  private static calculateEndTime(startMinutes: number, duration?: string): number {
    if (!duration) {
      // Default duration of 4 hours for events
      return startMinutes + (4 * 60)
    }

    // Parse duration (e.g., "3 hours", "2.5 hours", "180 minutes")
    const durationMatch = duration.match(/(\d+(?:\.\d+)?)\s*(hour|minute)s?/i)
    if (durationMatch) {
      const value = parseFloat(durationMatch[1])
      const unit = durationMatch[2].toLowerCase()
      
      if (unit.startsWith('hour')) {
        return startMinutes + (value * 60)
      } else if (unit.startsWith('minute')) {
        return startMinutes + value
      }
    }

    // Default to 4 hours if duration can't be parsed
    return startMinutes + (4 * 60)
  }

  /**
   * Check if two time ranges overlap
   */
  private static hasTimeOverlap(
    start1: number, end1: number,
    start2: number, end2: number
  ): boolean {
    return start1 < end2 && start2 < end1
  }

  /**
   * Generate suggested alternative dates
   */
  private static async generateSuggestedDates(
    entityId: string,
    entityType: 'VENDOR' | 'VENUE',
    requestedDate: string
  ): Promise<string[]> {
    const suggestions: string[] = []
    const baseDate = new Date(requestedDate)

    // Check next 30 days for availability
    for (let i = 1; i <= 30 && suggestions.length < 5; i++) {
      const checkDate = new Date(baseDate)
      checkDate.setDate(baseDate.getDate() + i)
      
      // Skip if it's a Monday (many venues are closed)
      if (checkDate.getDay() === 1) continue

      const dateString = checkDate.toISOString().split('T')[0]
      
      const availability = await this.checkAvailability({
        entityId,
        entityType,
        eventDate: dateString
      })

      if (availability.isAvailable) {
        suggestions.push(dateString)
      }
    }

    return suggestions
  }

  /**
   * Create a temporary reservation to prevent double booking
   */
  static async createReservation(params: AvailabilityCheck): Promise<{
    success: boolean
    reservationId?: string
    message: string
  }> {
    try {
      const user = await getCurrentUser()
      if (!user) {
        throw new Error('Authentication required')
      }

      // Check availability first
      const availability = await this.checkAvailability(params)
      if (!availability.isAvailable) {
        return {
          success: false,
          message: 'Not available for reservation'
        }
      }

      // Create a temporary booking with RESERVED status
      const reservationId = `RES-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
      const expiresAt = new Date(Date.now() + (this.RESERVATION_TIMEOUT * 60 * 1000))

      const reservationInput = {
        customerId: user.userId,
        customerName: 'RESERVATION',
        customerEmail: user.signInDetails?.loginId || user.username,
        customerPhone: '',
        entityId: params.entityId,
        entityType: params.entityType,
        entityName: 'TEMPORARY_RESERVATION',
        eventDate: params.eventDate,
        eventTime: params.eventTime || '10:00',
        guestCount: 1,
        eventType: 'RESERVATION',
        contactPreference: 'EMAIL',
        status: 'RESERVED',
        priority: 'HIGH',
        notes: `Temporary reservation expires at ${expiresAt.toISOString()}`,
        metadata: JSON.stringify({
          reservationId,
          expiresAt: expiresAt.toISOString(),
          type: 'TEMPORARY_RESERVATION'
        })
      }

      const { client } = await getAuthenticatedClient()
      const result = await client.graphql({
        query: createBooking,
        variables: { input: reservationInput }
      })

      // Schedule cleanup of expired reservation
      setTimeout(() => {
        this.cleanupExpiredReservation(result.data.createBooking.id)
      }, this.RESERVATION_TIMEOUT * 60 * 1000)

      return {
        success: true,
        reservationId,
        message: `Reservation created for ${this.RESERVATION_TIMEOUT} minutes`
      }
    } catch (error) {
      console.error('Error creating reservation:', error)
      return {
        success: false,
        message: 'Failed to create reservation'
      }
    }
  }

  /**
   * Convert reservation to confirmed booking
   */
  static async confirmReservation(
    reservationId: string,
    bookingData: any
  ): Promise<{ success: boolean; booking?: any; message: string }> {
    try {
      // Find the reservation booking
      const { client } = await getAuthenticatedClient()
      const bookingsResult = await client.graphql({
        query: listBookings,
        variables: {
          filter: {
            and: [
              { notes: { contains: reservationId } },
              { status: { ne: 'CANCELLED' } }
            ]
          }
        }
      })

      const reservationBooking = bookingsResult.data.listBookings.items[0]
      if (!reservationBooking) {
        return {
          success: false,
          message: 'Reservation not found or expired'
        }
      }

      // Update the reservation with actual booking data
      const updateInput = {
        id: reservationBooking.id,
        customerName: bookingData.customerName,
        customerPhone: bookingData.customerPhone,
        entityName: bookingData.entityName,
        guestCount: bookingData.guestCount,
        eventType: bookingData.eventType,
        duration: bookingData.duration,
        specialRequests: bookingData.specialRequests,
        budget: bookingData.budget,
        contactPreference: bookingData.contactPreference,
        status: 'PENDING',
        priority: 'MEDIUM',
        notes: bookingData.notes || '',
        metadata: JSON.stringify({
          ...JSON.parse(reservationBooking.metadata || '{}'),
          confirmedAt: new Date().toISOString()
        })
      }

      const result = await client.graphql({
        query: updateBooking,
        variables: { input: updateInput }
      })

      return {
        success: true,
        booking: result.data.updateBooking,
        message: 'Reservation confirmed successfully'
      }
    } catch (error) {
      console.error('Error confirming reservation:', error)
      return {
        success: false,
        message: 'Failed to confirm reservation'
      }
    }
  }

  /**
   * Cleanup expired reservations
   */
  private static async cleanupExpiredReservation(bookingId: string): Promise<void> {
    try {
      const { client } = await getAuthenticatedClient()
      await client.graphql({
        query: updateBooking,
        variables: {
          input: {
            id: bookingId,
            status: 'CANCELLED',
            notes: 'Reservation expired automatically'
          }
        }
      })
      console.log(`Cleaned up expired reservation: ${bookingId}`)
    } catch (error) {
      console.error('Error cleaning up expired reservation:', error)
    }
  }

  /**
   * Get all bookings for a specific date range
   */
  static async getBookingsForDateRange(
    entityId: string,
    startDate: string,
    endDate: string
  ): Promise<any[]> {
    try {
      const { client } = await getAuthenticatedClient()
      const result = await client.graphql({
        query: listBookings,
        variables: {
          filter: {
            and: [
              { entityId: { eq: entityId } },
              { eventDate: { between: [startDate, endDate] } },
              {
                or: [
                  { status: { eq: 'CONFIRMED' } },
                  { status: { eq: 'PENDING' } },
                  { status: { eq: 'IN_PROGRESS' } }
                ]
              }
            ]
          }
        }
      })

      return result.data.listBookings.items || []
    } catch (error) {
      console.error('Error fetching bookings for date range:', error)
      return []
    }
  }

  /**
   * Mock availability check for fallback scenarios
   */
  private static getMockAvailability(params: AvailabilityCheck): AvailabilityResult {
    // Simulate realistic availability based on date and time
    const date = new Date(params.eventDate);
    const isWeekend = date.getDay() === 0 || date.getDay() === 6;
    const isPopularTime = params.eventTime &&
      params.eventTime >= '10:00' && params.eventTime <= '18:00';

    // Higher chance of conflicts on weekends and popular times
    const conflictChance = isWeekend && isPopularTime ? 0.3 : 0.15;
    const hasConflict = Math.random() < conflictChance;

    const conflictingBookings: BookingConflict[] = hasConflict ? [{
      bookingId: 'mock-booking-' + Date.now(),
      customerName: 'Another Customer',
      eventDate: params.eventDate,
      eventTime: params.eventTime || '14:00',
      status: 'CONFIRMED',
      duration: '4 hours'
    }] : [];

    return {
      isAvailable: !hasConflict,
      conflictingBookings,
      message: hasConflict
        ? `Not available at the selected time - conflicting booking exists`
        : `Available for booking on ${params.eventDate}${params.eventTime ? ` at ${params.eventTime}` : ''}`,
      suggestedDates: hasConflict ? this.generateMockSuggestedDates(params.eventDate) : []
    };
  }

  /**
   * Generate mock suggested alternative dates
   */
  private static generateMockSuggestedDates(requestedDate: string): string[] {
    const date = new Date(requestedDate);
    const suggestions: string[] = [];

    // Suggest next 5 days (excluding the requested date)
    for (let i = 1; i <= 5; i++) {
      const nextDate = new Date(date);
      nextDate.setDate(date.getDate() + i);
      suggestions.push(nextDate.toISOString().split('T')[0]);
    }

    return suggestions;
  }
}
