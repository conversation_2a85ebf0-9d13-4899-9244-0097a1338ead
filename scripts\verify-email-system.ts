#!/usr/bin/env ts-node

/**
 * Email System Verification Script
 * Comprehensive verification of all 10 email templates and functionality
 */

import { emailService } from '../lib/services/emailService';

interface VerificationResult {
  emailType: string;
  success: boolean;
  error?: string;
  duration: number;
}

class EmailSystemVerifier {
  private results: VerificationResult[] = [];
  private testEmail = '<EMAIL>';

  async runAllTests(): Promise<void> {
    console.log('🚀 Starting Email System Verification...\n');

    const tests = [
      { name: 'Login OTP Email', test: () => this.testLoginOTP() },
      { name: 'Welcome Signup Email', test: () => this.testWelcomeSignup() },
      { name: 'Newsletter Subscription Email', test: () => this.testNewsletterSubscription() },
      { name: 'Booking Confirmation Email', test: () => this.testBookingConfirmation() },
      { name: 'Payment Success Email', test: () => this.testPaymentSuccess() },
      { name: 'Weekly News Email', test: () => this.testWeeklyNews() },
      { name: 'Offers Email', test: () => this.testOffers() },
      { name: 'Favorites Notification Email', test: () => this.testFavoritesNotification() },
      { name: 'Vendor Launch Email', test: () => this.testVendorLaunch() },
      { name: 'Availability Check Email', test: () => this.testAvailabilityCheck() }
    ];

    for (const test of tests) {
      await this.runTest(test.name, test.test);
    }

    this.printSummary();
  }

  private async runTest(emailType: string, testFunction: () => Promise<boolean>): Promise<void> {
    console.log(`📧 Testing: ${emailType}`);
    const startTime = Date.now();

    try {
      const success = await testFunction();
      const duration = Date.now() - startTime;

      this.results.push({
        emailType,
        success,
        duration
      });

      console.log(`   ${success ? '✅' : '❌'} ${emailType} - ${duration}ms\n`);
    } catch (error) {
      const duration = Date.now() - startTime;
      this.results.push({
        emailType,
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        duration
      });

      console.log(`   ❌ ${emailType} - Error: ${error instanceof Error ? error.message : 'Unknown error'}\n`);
    }
  }

  private async testLoginOTP(): Promise<boolean> {
    return await emailService.sendLoginOTPEmail({
      email: this.testEmail,
      userName: 'John Doe',
      otp: '123456',
      expiryMinutes: 10
    });
  }

  private async testWelcomeSignup(): Promise<boolean> {
    return await emailService.sendWelcomeSignupEmail({
      email: this.testEmail,
      firstName: 'John',
      lastName: 'Doe',
      type: 'signup',
      isVendor: false
    });
  }

  private async testNewsletterSubscription(): Promise<boolean> {
    return await emailService.sendNewsletterWelcomeEmail({
      email: this.testEmail,
      firstName: 'John',
      lastName: 'Doe',
      type: 'newsletter',
      interests: ['photography', 'venues', 'catering'],
      preferences: {
        weddingTips: true,
        vendorRecommendations: true,
        specialOffers: true,
        weeklyNews: true
      }
    });
  }

  private async testBookingConfirmation(): Promise<boolean> {
    return await emailService.sendBookingConfirmationEmail({
      email: this.testEmail,
      userName: 'John Doe',
      bookingId: 'booking_test_123',
      entityName: 'Grand Palace Hotel',
      entityType: 'venue',
      eventDate: '2024-12-25',
      eventTime: '18:00',
      amount: '₹50,000',
      status: 'confirmed'
    });
  }

  private async testPaymentSuccess(): Promise<boolean> {
    return await emailService.sendPaymentSuccessEmail({
      email: this.testEmail,
      userName: 'John Doe',
      orderId: 'order_test_789',
      amount: '₹15,000',
      items: [
        { name: 'Wedding Dress', quantity: 1, price: '₹10,000' },
        { name: 'Wedding Shoes', quantity: 1, price: '₹5,000' }
      ],
      invoiceUrl: 'https://BookmyFestive.com/invoices/test_invoice.pdf'
    });
  }

  private async testWeeklyNews(): Promise<boolean> {
    return await emailService.sendWeeklyNewsEmail({
      email: this.testEmail,
      userName: 'John Doe',
      articles: [
        {
          title: 'Top 10 Wedding Trends for 2024',
          excerpt: 'Discover the latest wedding trends that are taking 2024 by storm...',
          url: 'https://BookmyFestive.com/blog/wedding-trends-2024'
        },
        {
          title: 'How to Choose the Perfect Wedding Venue',
          excerpt: 'A comprehensive guide to selecting the ideal venue for your special day...',
          url: 'https://BookmyFestive.com/blog/choose-wedding-venue'
        }
      ],
      offers: [
        {
          title: 'Photography Package Discount',
          discount: '20% OFF',
          url: 'https://BookmyFestive.com/offers/photography-discount'
        }
      ]
    });
  }

  private async testOffers(): Promise<boolean> {
    return await emailService.sendOffersEmail({
      email: this.testEmail,
      userName: 'John Doe',
      offers: [
        {
          title: 'Exclusive Venue Booking Discount',
          description: 'Book your dream venue with our exclusive discount offer',
          discount: '25% OFF',
          validUntil: '2024-12-31',
          url: 'https://BookmyFestive.com/venues?discount=25off'
        },
        {
          title: 'Photography + Videography Combo',
          description: 'Get both photography and videography services at a special price',
          discount: '30% OFF',
          validUntil: '2024-11-30',
          url: 'https://BookmyFestive.com/vendors/photography?combo=true'
        }
      ]
    });
  }

  private async testFavoritesNotification(): Promise<boolean> {
    return await emailService.sendFavoritesNotificationEmail({
      email: this.testEmail,
      userName: 'John Doe',
      updates: [
        {
          name: 'Dream Photography Studio',
          type: 'vendor',
          updateType: 'price_drop',
          url: 'https://BookmyFestive.com/vendors/dream-photography'
        },
        {
          name: 'Royal Palace Banquet',
          type: 'venue',
          updateType: 'new_photos',
          url: 'https://BookmyFestive.com/venues/royal-palace-banquet'
        }
      ]
    });
  }

  private async testVendorLaunch(): Promise<boolean> {
    return await emailService.sendVendorLaunchEmail({
      email: this.testEmail,
      userName: 'John Doe',
      newVendors: [
        {
          name: 'Artistic Wedding Films',
          category: 'Videography',
          city: 'Mumbai',
          discount: '20% OFF',
          url: 'https://BookmyFestive.com/vendors/artistic-wedding-films'
        },
        {
          name: 'Floral Paradise',
          category: 'Decoration',
          city: 'Delhi',
          discount: '15% OFF',
          url: 'https://BookmyFestive.com/vendors/floral-paradise'
        }
      ]
    });
  }

  private async testAvailabilityCheck(): Promise<boolean> {
    return await emailService.sendAvailabilityCheckEmail({
      email: this.testEmail,
      userName: 'John Doe',
      entityName: 'Grand Palace Hotel',
      entityType: 'venue',
      requestedDate: '2024-12-25',
      status: 'available',
      url: 'https://BookmyFestive.com/venues/grand-palace-hotel'
    });
  }

  private printSummary(): void {
    console.log('📊 Email System Verification Summary');
    console.log('=====================================\n');

    const successful = this.results.filter(r => r.success).length;
    const failed = this.results.filter(r => !r.success).length;
    const totalDuration = this.results.reduce((sum, r) => sum + r.duration, 0);

    console.log(`✅ Successful: ${successful}/${this.results.length}`);
    console.log(`❌ Failed: ${failed}/${this.results.length}`);
    console.log(`⏱️  Total Duration: ${totalDuration}ms`);
    console.log(`📈 Success Rate: ${((successful / this.results.length) * 100).toFixed(1)}%\n`);

    if (failed > 0) {
      console.log('❌ Failed Tests:');
      this.results
        .filter(r => !r.success)
        .forEach(r => {
          console.log(`   - ${r.emailType}: ${r.error || 'Unknown error'}`);
        });
      console.log('');
    }

    console.log('📋 Detailed Results:');
    this.results.forEach(r => {
      console.log(`   ${r.success ? '✅' : '❌'} ${r.emailType} (${r.duration}ms)`);
    });

    console.log('\n🎉 Email System Verification Complete!');

    if (successful === this.results.length) {
      console.log('🌟 All email templates are working perfectly!');
    } else {
      console.log('⚠️  Some email templates need attention. Please check the failed tests above.');
    }
  }
}

// Run verification if this script is executed directly
if (require.main === module) {
  const verifier = new EmailSystemVerifier();
  verifier.runAllTests().catch(error => {
    console.error('❌ Verification failed:', error);
    process.exit(1);
  });
}

export { EmailSystemVerifier };
