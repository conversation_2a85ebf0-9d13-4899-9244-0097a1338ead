import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Alert,
  Modal,
  FlatList,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../providers/ThemeProvider';

interface Guest {
  id: string;
  name: string;
  email?: string;
  phone?: string;
  category: 'family' | 'friends' | 'colleagues' | 'relatives' | 'others';
  rsvpStatus: 'pending' | 'attending' | 'not_attending' | 'maybe';
  plusOne: boolean;
  dietaryRestrictions?: string;
  address?: string;
  invitationSent: boolean;
  notes?: string;
}

interface GuestCategory {
  id: string;
  name: string;
  icon: keyof typeof Ionicons.glyphMap;
  color: string;
  count: number;
}

export default function GuestListManagerScreen() {
  const { theme } = useTheme();
  
  const [showAddGuest, setShowAddGuest] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [searchQuery, setSearchQuery] = useState('');
  
  const [newGuest, setNewGuest] = useState({
    name: '',
    email: '',
    phone: '',
    category: 'family' as Guest['category'],
    plusOne: false,
    dietaryRestrictions: '',
    address: '',
    notes: '',
  });

  const [guests, setGuests] = useState<Guest[]>([
    {
      id: '1',
      name: 'John Smith',
      email: '<EMAIL>',
      phone: '+91 8148376909',
      category: 'family',
      rsvpStatus: 'attending',
      plusOne: true,
      invitationSent: true,
      notes: 'Uncle from father\'s side',
    },
    {
      id: '2',
      name: 'Sarah Johnson',
      email: '<EMAIL>',
      phone: '+91 9876543211',
      category: 'friends',
      rsvpStatus: 'pending',
      plusOne: false,
      invitationSent: true,
      dietaryRestrictions: 'Vegetarian',
    },
    {
      id: '3',
      name: 'Mike Wilson',
      email: '<EMAIL>',
      phone: '+91 9876543212',
      category: 'colleagues',
      rsvpStatus: 'maybe',
      plusOne: true,
      invitationSent: false,
    },
    {
      id: '4',
      name: 'Emily Davis',
      email: '<EMAIL>',
      phone: '+91 9876543213',
      category: 'relatives',
      rsvpStatus: 'not_attending',
      plusOne: false,
      invitationSent: true,
      notes: 'Cousin from mother\'s side',
    },
  ]);

  const categories: GuestCategory[] = [
    {
      id: 'all',
      name: 'All Guests',
      icon: 'people-outline',
      color: theme.colors.primary,
      count: guests.length,
    },
    {
      id: 'family',
      name: 'Family',
      icon: 'home-outline',
      color: '#4CAF50',
      count: guests.filter(g => g.category === 'family').length,
    },
    {
      id: 'friends',
      name: 'Friends',
      icon: 'heart-outline',
      color: '#2196F3',
      count: guests.filter(g => g.category === 'friends').length,
    },
    {
      id: 'colleagues',
      name: 'Colleagues',
      icon: 'briefcase-outline',
      color: '#FF9800',
      count: guests.filter(g => g.category === 'colleagues').length,
    },
    {
      id: 'relatives',
      name: 'Relatives',
      icon: 'people-circle-outline',
      color: '#9C27B0',
      count: guests.filter(g => g.category === 'relatives').length,
    },
    {
      id: 'others',
      name: 'Others',
      icon: 'ellipsis-horizontal-outline',
      color: '#607D8B',
      count: guests.filter(g => g.category === 'others').length,
    },
  ];

  const getFilteredGuests = () => {
    let filtered = guests;
    
    if (selectedCategory !== 'all') {
      filtered = filtered.filter(guest => guest.category === selectedCategory);
    }
    
    if (searchQuery) {
      filtered = filtered.filter(guest => 
        guest.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        guest.email?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        guest.phone?.includes(searchQuery)
      );
    }
    
    return filtered;
  };

  const getRSVPStats = () => {
    const attending = guests.filter(g => g.rsvpStatus === 'attending').length;
    const notAttending = guests.filter(g => g.rsvpStatus === 'not_attending').length;
    const pending = guests.filter(g => g.rsvpStatus === 'pending').length;
    const maybe = guests.filter(g => g.rsvpStatus === 'maybe').length;
    
    return { attending, notAttending, pending, maybe };
  };

  const getTotalWithPlusOnes = () => {
    return guests.reduce((total, guest) => {
      if (guest.rsvpStatus === 'attending') {
        return total + 1 + (guest.plusOne ? 1 : 0);
      }
      return total;
    }, 0);
  };

  const handleAddGuest = () => {
    if (!newGuest.name.trim()) {
      Alert.alert('Error', 'Please enter guest name');
      return;
    }

    const guest: Guest = {
      id: Date.now().toString(),
      name: newGuest.name.trim(),
      email: newGuest.email.trim() || undefined,
      phone: newGuest.phone.trim() || undefined,
      category: newGuest.category,
      rsvpStatus: 'pending',
      plusOne: newGuest.plusOne,
      dietaryRestrictions: newGuest.dietaryRestrictions.trim() || undefined,
      address: newGuest.address.trim() || undefined,
      notes: newGuest.notes.trim() || undefined,
      invitationSent: false,
    };

    setGuests(prev => [...prev, guest]);
    
    // Reset form
    setNewGuest({
      name: '',
      email: '',
      phone: '',
      category: 'family',
      plusOne: false,
      dietaryRestrictions: '',
      address: '',
      notes: '',
    });
    
    setShowAddGuest(false);
    Alert.alert('Success', 'Guest added successfully!');
  };

  const handleUpdateRSVP = (guestId: string, status: Guest['rsvpStatus']) => {
    setGuests(prev => prev.map(guest => 
      guest.id === guestId ? { ...guest, rsvpStatus: status } : guest
    ));
  };

  const handleSendInvitation = (guestId: string) => {
    setGuests(prev => prev.map(guest => 
      guest.id === guestId ? { ...guest, invitationSent: true } : guest
    ));
    Alert.alert('Success', 'Invitation sent successfully!');
  };

  const renderGuestCard = ({ item: guest }: { item: Guest }) => {
    const categoryInfo = categories.find(c => c.id === guest.category);
    
    return (
      <View style={[styles.guestCard, { backgroundColor: theme.colors.surface }]}>
        <View style={styles.guestHeader}>
          <View style={styles.guestInfo}>
            <Text style={[styles.guestName, { color: theme.colors.text }]}>
              {guest.name}
            </Text>
            {guest.email && (
              <Text style={[styles.guestDetail, { color: theme.colors.textSecondary }]}>
                {guest.email}
              </Text>
            )}
            {guest.phone && (
              <Text style={[styles.guestDetail, { color: theme.colors.textSecondary }]}>
                {guest.phone}
              </Text>
            )}
          </View>
          
          <View style={styles.guestActions}>
            <View style={[
              styles.categoryBadge,
              { backgroundColor: categoryInfo?.color + '20' }
            ]}>
              <Ionicons 
                name={categoryInfo?.icon || 'person-outline'} 
                size={12} 
                color={categoryInfo?.color} 
              />
              <Text style={[styles.categoryText, { color: categoryInfo?.color }]}>
                {categoryInfo?.name}
              </Text>
            </View>
          </View>
        </View>

        <View style={styles.guestMeta}>
          <View style={styles.rsvpContainer}>
            <Text style={[styles.rsvpLabel, { color: theme.colors.textSecondary }]}>
              RSVP:
            </Text>
            <View style={styles.rsvpButtons}>
              {(['attending', 'maybe', 'not_attending'] as const).map((status) => (
                <TouchableOpacity
                  key={status}
                  style={[
                    styles.rsvpButton,
                    { backgroundColor: theme.colors.border },
                    guest.rsvpStatus === status && { backgroundColor: theme.colors.primary }
                  ]}
                  onPress={() => handleUpdateRSVP(guest.id, status)}
                >
                  <Text style={[
                    styles.rsvpButtonText,
                    { color: theme.colors.text },
                    guest.rsvpStatus === status && { color: 'white' }
                  ]}>
                    {status === 'attending' ? '✓' : status === 'maybe' ? '?' : '✗'}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>

          <View style={styles.guestFlags}>
            {guest.plusOne && (
              <View style={[styles.flag, { backgroundColor: theme.colors.secondary + '20' }]}>
                <Text style={[styles.flagText, { color: theme.colors.secondary }]}>
                  +1
                </Text>
              </View>
            )}
            
            {guest.dietaryRestrictions && (
              <View style={[styles.flag, { backgroundColor: theme.colors.warning + '20' }]}>
                <Text style={[styles.flagText, { color: theme.colors.warning }]}>
                  Diet
                </Text>
              </View>
            )}
            
            {!guest.invitationSent && (
              <TouchableOpacity
                style={[styles.inviteButton, { backgroundColor: theme.colors.primary }]}
                onPress={() => handleSendInvitation(guest.id)}
              >
                <Text style={styles.inviteButtonText}>Send Invite</Text>
              </TouchableOpacity>
            )}
          </View>
        </View>

        {guest.notes && (
          <Text style={[styles.guestNotes, { color: theme.colors.textSecondary }]}>
            Note: {guest.notes}
          </Text>
        )}
      </View>
    );
  };

  const rsvpStats = getRSVPStats();
  const totalAttending = getTotalWithPlusOnes();
  const filteredGuests = getFilteredGuests();

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {/* Stats Overview */}
      <View style={[styles.statsCard, { backgroundColor: theme.colors.surface }]}>
        <Text style={[styles.statsTitle, { color: theme.colors.text }]}>
          Guest List Overview
        </Text>
        
        <View style={styles.statsGrid}>
          <View style={styles.statItem}>
            <Text style={[styles.statNumber, { color: theme.colors.primary }]}>
              {guests.length}
            </Text>
            <Text style={[styles.statLabel, { color: theme.colors.textSecondary }]}>
              Total Invited
            </Text>
          </View>
          
          <View style={styles.statItem}>
            <Text style={[styles.statNumber, { color: theme.colors.success }]}>
              {totalAttending}
            </Text>
            <Text style={[styles.statLabel, { color: theme.colors.textSecondary }]}>
              Expected Guests
            </Text>
          </View>
          
          <View style={styles.statItem}>
            <Text style={[styles.statNumber, { color: theme.colors.warning }]}>
              {rsvpStats.pending}
            </Text>
            <Text style={[styles.statLabel, { color: theme.colors.textSecondary }]}>
              Pending RSVP
            </Text>
          </View>
        </View>
      </View>

      {/* Category Filter */}
      <ScrollView 
        horizontal 
        showsHorizontalScrollIndicator={false} 
        style={styles.categoryFilter}
        contentContainerStyle={styles.categoryFilterContent}
      >
        {categories.map((category) => (
          <TouchableOpacity
            key={category.id}
            style={[
              styles.categoryFilterButton,
              { backgroundColor: theme.colors.surface },
              selectedCategory === category.id && { backgroundColor: category.color + '20' }
            ]}
            onPress={() => setSelectedCategory(category.id)}
          >
            <Ionicons 
              name={category.icon} 
              size={16} 
              color={selectedCategory === category.id ? category.color : theme.colors.textSecondary} 
            />
            <Text style={[
              styles.categoryFilterText,
              { color: selectedCategory === category.id ? category.color : theme.colors.text }
            ]}>
              {category.name} ({category.count})
            </Text>
          </TouchableOpacity>
        ))}
      </ScrollView>

      {/* Search and Add */}
      <View style={styles.actionBar}>
        <View style={[styles.searchContainer, { backgroundColor: theme.colors.surface }]}>
          <Ionicons name="search-outline" size={20} color={theme.colors.textSecondary} />
          <TextInput
            style={[styles.searchInput, { color: theme.colors.text }]}
            placeholder="Search guests..."
            placeholderTextColor={theme.colors.textSecondary}
            value={searchQuery}
            onChangeText={setSearchQuery}
          />
        </View>
        
        <TouchableOpacity
          style={[styles.addButton, { backgroundColor: theme.colors.primary }]}
          onPress={() => setShowAddGuest(true)}
        >
          <Ionicons name="add-outline" size={20} color="white" />
        </TouchableOpacity>
      </View>

      {/* Guest List */}
      <FlatList
        data={filteredGuests}
        renderItem={renderGuestCard}
        keyExtractor={(item) => item.id}
        style={styles.guestList}
        showsVerticalScrollIndicator={false}
        ListEmptyComponent={
          <View style={styles.emptyState}>
            <Ionicons name="people-outline" size={48} color={theme.colors.textSecondary} />
            <Text style={[styles.emptyText, { color: theme.colors.textSecondary }]}>
              No guests found
            </Text>
          </View>
        }
      />

      {/* Add Guest Modal */}
      <Modal
        visible={showAddGuest}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setShowAddGuest(false)}
      >
        <View style={[styles.modalContainer, { backgroundColor: theme.colors.background }]}>
          <View style={[styles.modalHeader, { backgroundColor: theme.colors.surface }]}>
            <TouchableOpacity onPress={() => setShowAddGuest(false)}>
              <Ionicons name="close-outline" size={24} color={theme.colors.text} />
            </TouchableOpacity>
            <Text style={[styles.modalTitle, { color: theme.colors.text }]}>
              Add Guest
            </Text>
            <TouchableOpacity onPress={handleAddGuest}>
              <Text style={[styles.saveButton, { color: theme.colors.primary }]}>
                Save
              </Text>
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.modalContent}>
            {/* Name */}
            <Text style={[styles.fieldLabel, { color: theme.colors.text }]}>
              Name *
            </Text>
            <TextInput
              style={[styles.textInput, { backgroundColor: theme.colors.surface, color: theme.colors.text }]}
              value={newGuest.name}
              onChangeText={(text) => setNewGuest(prev => ({ ...prev, name: text }))}
              placeholder="Enter guest name"
              placeholderTextColor={theme.colors.textSecondary}
            />

            {/* Email */}
            <Text style={[styles.fieldLabel, { color: theme.colors.text }]}>
              Email
            </Text>
            <TextInput
              style={[styles.textInput, { backgroundColor: theme.colors.surface, color: theme.colors.text }]}
              value={newGuest.email}
              onChangeText={(text) => setNewGuest(prev => ({ ...prev, email: text }))}
              placeholder="Enter email address"
              placeholderTextColor={theme.colors.textSecondary}
              keyboardType="email-address"
            />

            {/* Phone */}
            <Text style={[styles.fieldLabel, { color: theme.colors.text }]}>
              Phone
            </Text>
            <TextInput
              style={[styles.textInput, { backgroundColor: theme.colors.surface, color: theme.colors.text }]}
              value={newGuest.phone}
              onChangeText={(text) => setNewGuest(prev => ({ ...prev, phone: text }))}
              placeholder="Enter phone number"
              placeholderTextColor={theme.colors.textSecondary}
              keyboardType="phone-pad"
            />

            {/* Category */}
            <Text style={[styles.fieldLabel, { color: theme.colors.text }]}>
              Category
            </Text>
            <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.categorySelector}>
              {categories.slice(1).map((category) => (
                <TouchableOpacity
                  key={category.id}
                  style={[
                    styles.categoryOption,
                    { backgroundColor: theme.colors.surface },
                    newGuest.category === category.id && { backgroundColor: category.color + '20' }
                  ]}
                  onPress={() => setNewGuest(prev => ({ ...prev, category: category.id as Guest['category'] }))}
                >
                  <Ionicons name={category.icon} size={16} color={category.color} />
                  <Text style={[
                    styles.categoryOptionText,
                    { color: newGuest.category === category.id ? category.color : theme.colors.text }
                  ]}>
                    {category.name}
                  </Text>
                </TouchableOpacity>
              ))}
            </ScrollView>

            {/* Plus One */}
            <View style={styles.checkboxContainer}>
              <TouchableOpacity
                style={[
                  styles.checkbox,
                  { backgroundColor: theme.colors.surface },
                  newGuest.plusOne && { backgroundColor: theme.colors.primary }
                ]}
                onPress={() => setNewGuest(prev => ({ ...prev, plusOne: !prev.plusOne }))}
              >
                {newGuest.plusOne && (
                  <Ionicons name="checkmark-outline" size={16} color="white" />
                )}
              </TouchableOpacity>
              <Text style={[styles.checkboxLabel, { color: theme.colors.text }]}>
                Plus One
              </Text>
            </View>

            {/* Dietary Restrictions */}
            <Text style={[styles.fieldLabel, { color: theme.colors.text }]}>
              Dietary Restrictions
            </Text>
            <TextInput
              style={[styles.textInput, { backgroundColor: theme.colors.surface, color: theme.colors.text }]}
              value={newGuest.dietaryRestrictions}
              onChangeText={(text) => setNewGuest(prev => ({ ...prev, dietaryRestrictions: text }))}
              placeholder="e.g., Vegetarian, Vegan, Allergies"
              placeholderTextColor={theme.colors.textSecondary}
            />

            {/* Notes */}
            <Text style={[styles.fieldLabel, { color: theme.colors.text }]}>
              Notes
            </Text>
            <TextInput
              style={[styles.textInput, styles.textArea, { backgroundColor: theme.colors.surface, color: theme.colors.text }]}
              value={newGuest.notes}
              onChangeText={(text) => setNewGuest(prev => ({ ...prev, notes: text }))}
              placeholder="Additional notes about the guest"
              placeholderTextColor={theme.colors.textSecondary}
              multiline
              numberOfLines={3}
            />
          </ScrollView>
        </View>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  statsCard: {
    margin: 16,
    padding: 20,
    borderRadius: 16,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  statsTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
    textAlign: 'center',
  },
  statsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  statItem: {
    alignItems: 'center',
    flex: 1,
  },
  statNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    textAlign: 'center',
  },
  categoryFilter: {
    paddingHorizontal: 16,
    marginBottom: 8,
  },
  categoryFilterContent: {
    gap: 8,
  },
  categoryFilterButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 8,
    borderRadius: 20,
    gap: 6,
  },
  categoryFilterText: {
    fontSize: 12,
    fontWeight: '500',
  },
  actionBar: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    marginBottom: 8,
    gap: 12,
  },
  searchContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    gap: 8,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
  },
  addButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    alignItems: 'center',
    justifyContent: 'center',
  },
  guestList: {
    flex: 1,
    paddingHorizontal: 16,
  },
  guestCard: {
    padding: 16,
    borderRadius: 12,
    marginBottom: 8,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  guestHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  guestInfo: {
    flex: 1,
  },
  guestName: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  guestDetail: {
    fontSize: 14,
    marginBottom: 2,
  },
  guestActions: {
    alignItems: 'flex-end',
  },
  categoryBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 4,
    borderRadius: 12,
    gap: 4,
  },
  categoryText: {
    fontSize: 10,
    fontWeight: '600',
  },
  guestMeta: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  rsvpContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  rsvpLabel: {
    fontSize: 12,
    fontWeight: '600',
  },
  rsvpButtons: {
    flexDirection: 'row',
    gap: 4,
  },
  rsvpButton: {
    width: 24,
    height: 24,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
  },
  rsvpButtonText: {
    fontSize: 12,
    fontWeight: 'bold',
  },
  guestFlags: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  flag: {
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 8,
  },
  flagText: {
    fontSize: 10,
    fontWeight: '600',
  },
  inviteButton: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  inviteButtonText: {
    color: 'white',
    fontSize: 10,
    fontWeight: '600',
  },
  guestNotes: {
    fontSize: 12,
    fontStyle: 'italic',
    marginTop: 4,
  },
  emptyState: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 48,
  },
  emptyText: {
    fontSize: 16,
    marginTop: 12,
  },
  modalContainer: {
    flex: 1,
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  saveButton: {
    fontSize: 16,
    fontWeight: '600',
  },
  modalContent: {
    flex: 1,
    padding: 16,
  },
  fieldLabel: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
    marginTop: 16,
  },
  textInput: {
    padding: 12,
    borderRadius: 8,
    fontSize: 16,
    marginBottom: 8,
  },
  textArea: {
    height: 80,
    textAlignVertical: 'top',
  },
  categorySelector: {
    marginBottom: 8,
  },
  categoryOption: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 8,
    borderRadius: 16,
    marginRight: 8,
    gap: 6,
  },
  categoryOptionText: {
    fontSize: 12,
    fontWeight: '500',
  },
  checkboxContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 12,
    gap: 12,
  },
  checkbox: {
    width: 20,
    height: 20,
    borderRadius: 4,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: 'rgba(0,0,0,0.2)',
  },
  checkboxLabel: {
    fontSize: 16,
    fontWeight: '500',
  },
});
