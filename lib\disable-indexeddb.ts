'use client';

/**
 * IndexedDB Disabler
 * 
 * This utility prevents IndexedDB from being created by intercepting
 * and blocking IndexedDB operations that aren't essential for the app.
 * This helps improve Lighthouse performance scores.
 */

// Store original IndexedDB methods
let originalIndexedDB: IDBFactory | undefined;
let originalOpen: typeof IDBFactory.prototype.open | undefined;
let originalDeleteDatabase: typeof IDBFactory.prototype.deleteDatabase | undefined;

// List of database names that are allowed to be created
const ALLOWED_DB_NAMES = [
  // Add any database names that your app actually needs
  // Currently, your app only uses localStorage, so this list is empty
];

// List of database name patterns that should be blocked
const BLOCKED_DB_PATTERNS = [
  'amplify',
  'aws-amplify',
  'datastore',
  'cognito',
  'keyval-store',
  'localforage'
];

/**
 * Check if a database name should be blocked
 */
function shouldBlockDatabase(name: string): boolean {
  // Allow explicitly allowed databases
  if (ALLOWED_DB_NAMES.includes(name)) {
    return false;
  }
  
  // Block databases matching blocked patterns
  return BLOCKED_DB_PATTERNS.some(pattern => 
    name.toLowerCase().includes(pattern.toLowerCase())
  );
}

/**
 * Mock IndexedDB open request that fails gracefully
 */
function createMockOpenRequest(dbName: string): IDBOpenDBRequest {
  const request = {} as IDBOpenDBRequest;
  
  // Set up event handlers
  Object.defineProperty(request, 'onsuccess', { writable: true, value: null });
  Object.defineProperty(request, 'onerror', { writable: true, value: null });
  Object.defineProperty(request, 'onblocked', { writable: true, value: null });
  Object.defineProperty(request, 'onupgradeneeded', { writable: true, value: null });
  
  // Simulate async failure
  setTimeout(() => {
    const error = new DOMException(
      `IndexedDB access blocked for database: ${dbName}`,
      'NotAllowedError'
    );
    
    Object.defineProperty(request, 'error', { value: error });
    
    if (request.onerror) {
      const event = new Event('error');
      Object.defineProperty(event, 'target', { value: request });
      request.onerror(event);
    }
  }, 0);
  
  return request;
}

/**
 * Mock IndexedDB delete request
 */
function createMockDeleteRequest(dbName: string): IDBOpenDBRequest {
  const request = {} as IDBOpenDBRequest;
  
  Object.defineProperty(request, 'onsuccess', { writable: true, value: null });
  Object.defineProperty(request, 'onerror', { writable: true, value: null });
  Object.defineProperty(request, 'onblocked', { writable: true, value: null });
  
  // Simulate successful deletion
  setTimeout(() => {
    if (request.onsuccess) {
      const event = new Event('success');
      Object.defineProperty(event, 'target', { value: request });
      request.onsuccess(event);
    }
  }, 0);
  
  return request;
}

/**
 * Disable IndexedDB by overriding the API
 */
export function disableIndexedDB(): void {
  if (typeof window === 'undefined' || !window.indexedDB) {
    return;
  }
  
  // Store original methods
  originalIndexedDB = window.indexedDB;
  originalOpen = window.indexedDB.open;
  originalDeleteDatabase = window.indexedDB.deleteDatabase;
  
  // Override the open method
  window.indexedDB.open = function(name: string, version?: number): IDBOpenDBRequest {
    if (shouldBlockDatabase(name)) {
      if (process.env.NODE_ENV === 'development') {
        console.log(`🚫 Blocked IndexedDB creation: ${name}`);
      }
      return createMockOpenRequest(name);
    }
    
    // Allow the database to be created
    return originalOpen!.call(this, name, version);
  };
  
  // Override the deleteDatabase method
  window.indexedDB.deleteDatabase = function(name: string): IDBOpenDBRequest {
    if (process.env.NODE_ENV === 'development') {
      console.log(`🗑️ IndexedDB deletion requested: ${name}`);
    }
    return createMockDeleteRequest(name);
  };
  
  if (process.env.NODE_ENV === 'development') {
    console.log('🚫 IndexedDB access has been restricted');
  }
}

/**
 * Restore original IndexedDB functionality
 */
export function restoreIndexedDB(): void {
  if (typeof window === 'undefined' || !originalIndexedDB) {
    return;
  }
  
  window.indexedDB.open = originalOpen!;
  window.indexedDB.deleteDatabase = originalDeleteDatabase!;
  
  if (process.env.NODE_ENV === 'development') {
    console.log('✅ IndexedDB access has been restored');
  }
}

/**
 * Check if IndexedDB is currently disabled
 */
export function isIndexedDBDisabled(): boolean {
  return originalIndexedDB !== undefined;
}

// Auto-disable IndexedDB when this module is imported
if (typeof window !== 'undefined') {
  disableIndexedDB();
}
