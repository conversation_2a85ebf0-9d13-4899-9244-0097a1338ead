import { Page, expect } from '@playwright/test';
import { TestHelpers } from './test-helpers';

export class AuthHelpers extends TestHelpers {
  constructor(page: Page) {
    super(page);
  }

  /**
   * Login with email and password
   */
  async login(email: string, password: string) {
    await this.navigateTo('/login');
    
    // Wait for login form to be visible
    await this.waitForElement('[data-testid="login-form"], form');
    
    // Fill email
    await this.fillInput('input[type="email"], input[name="email"]', email);
    
    // Fill password
    await this.fillInput('input[type="password"], input[name="password"]', password);
    
    // Click login button
    await this.clickElement('button[type="submit"], button:has-text("Sign In"), button:has-text("Login")');
    
    // Wait for navigation or success
    await this.waitForLoadingToComplete();
    await this.waitForNetworkIdle();
    
    // Verify login success by checking for user indicators
    const isLoggedIn = await this.isUserLoggedIn();
    expect(isLoggedIn).toBe(true);
  }

  /**
   * Register new user account
   */
  async register(userData: {
    email: string;
    password: string;
    firstName: string;
    lastName: string;
    phone?: string;
  }) {
    await this.navigateTo('/login');
    
    // Look for signup/register link or tab
    const signupSelectors = [
      'text=Sign Up',
      'text=Register',
      'text=Create Account',
      '[data-testid="signup-tab"]',
      'a[href*="signup"]'
    ];
    
    let signupFound = false;
    for (const selector of signupSelectors) {
      if (await this.elementExists(selector)) {
        await this.clickElement(selector);
        signupFound = true;
        break;
      }
    }
    
    if (!signupFound) {
      // If no signup link found, try direct navigation
      await this.navigateTo('/signup');
    }
    
    await this.waitForElement('form, [data-testid="signup-form"]');
    
    // Fill registration form
    await this.fillInput('input[name="firstName"], input[placeholder*="First"]', userData.firstName);
    await this.fillInput('input[name="lastName"], input[placeholder*="Last"]', userData.lastName);
    await this.fillInput('input[type="email"], input[name="email"]', userData.email);
    await this.fillInput('input[type="password"], input[name="password"]', userData.password);
    
    if (userData.phone) {
      const phoneInput = this.page.locator('input[name="phone"], input[type="tel"]').first();
      if (await phoneInput.isVisible()) {
        await phoneInput.fill(userData.phone);
      }
    }
    
    // Submit registration
    await this.clickElement('button[type="submit"], button:has-text("Sign Up"), button:has-text("Register")');
    
    await this.waitForLoadingToComplete();
    await this.waitForNetworkIdle();
  }

  /**
   * Handle OTP verification if required
   */
  async handleOTPVerification(otp: string = '123456') {
    // Check if OTP verification page is shown
    const otpSelectors = [
      'input[name="otp"]',
      'input[placeholder*="OTP"]',
      'input[placeholder*="verification"]',
      '[data-testid="otp-input"]'
    ];
    
    for (const selector of otpSelectors) {
      if (await this.elementExists(selector)) {
        await this.fillInput(selector, otp);
        
        // Look for verify button
        const verifyButton = this.page.locator('button:has-text("Verify"), button[type="submit"]').first();
        if (await verifyButton.isVisible()) {
          await verifyButton.click();
        }
        
        await this.waitForLoadingToComplete();
        break;
      }
    }
  }

  /**
   * Login with Google OAuth (mock)
   */
  async loginWithGoogle() {
    await this.navigateTo('/login');
    
    // Look for Google login button
    const googleSelectors = [
      'button:has-text("Google")',
      'button:has-text("Continue with Google")',
      '[data-testid="google-login"]',
      '.google-login-button'
    ];
    
    for (const selector of googleSelectors) {
      if (await this.elementExists(selector)) {
        await this.clickElement(selector);
        
        // In a real test, you would handle the OAuth flow
        // For now, we'll assume it redirects back with success
        await this.waitForLoadingToComplete();
        await this.waitForNetworkIdle();
        break;
      }
    }
  }

  /**
   * Reset password flow
   */
  async resetPassword(email: string) {
    await this.navigateTo('/login');
    
    // Look for forgot password link
    const forgotPasswordSelectors = [
      'text=Forgot Password',
      'text=Reset Password',
      'a[href*="forgot"]',
      '[data-testid="forgot-password"]'
    ];
    
    for (const selector of forgotPasswordSelectors) {
      if (await this.elementExists(selector)) {
        await this.clickElement(selector);
        break;
      }
    }
    
    await this.waitForElement('input[type="email"]');
    await this.fillInput('input[type="email"]', email);
    
    await this.clickElement('button[type="submit"], button:has-text("Reset"), button:has-text("Send")');
    
    await this.waitForLoadingToComplete();
  }

  /**
   * Logout user
   */
  async logout() {
    await this.logoutIfLoggedIn();
  }

  /**
   * Login as vendor
   */
  async loginAsVendor(email: string = '<EMAIL>', password: string = 'VendorPass123!') {
    await this.login(email, password);
    
    // Verify vendor-specific elements are visible
    const vendorIndicators = [
      'text=Dashboard',
      'text=My Business',
      'text=Bookings',
      '[data-testid="vendor-menu"]'
    ];
    
    let isVendor = false;
    for (const selector of vendorIndicators) {
      if (await this.elementExists(selector)) {
        isVendor = true;
        break;
      }
    }
    
    expect(isVendor).toBe(true);
  }

  /**
   * Login as admin
   */
  async loginAsAdmin(email: string = '<EMAIL>', password: string = 'AdminPass123!') {
    await this.login(email, password);
    
    // Verify admin-specific elements are visible
    const adminIndicators = [
      'text=Admin Dashboard',
      'text=User Management',
      'text=Admin Panel',
      '[data-testid="admin-menu"]'
    ];
    
    let isAdmin = false;
    for (const selector of adminIndicators) {
      if (await this.elementExists(selector)) {
        isAdmin = true;
        break;
      }
    }
    
    expect(isAdmin).toBe(true);
  }

  /**
   * Check current user role
   */
  async getCurrentUserRole(): Promise<'customer' | 'vendor' | 'admin' | 'unknown'> {
    // Check for admin indicators
    const adminSelectors = ['text=Admin Dashboard', 'text=User Management'];
    for (const selector of adminSelectors) {
      if (await this.elementExists(selector)) {
        return 'admin';
      }
    }
    
    // Check for vendor indicators
    const vendorSelectors = ['text=My Business', 'text=Vendor Dashboard'];
    for (const selector of vendorSelectors) {
      if (await this.elementExists(selector)) {
        return 'vendor';
      }
    }
    
    // Check if logged in as customer
    if (await this.isUserLoggedIn()) {
      return 'customer';
    }
    
    return 'unknown';
  }

  /**
   * Verify user is on correct page after login
   */
  async verifyLoginRedirect(expectedPath?: string) {
    await this.waitForNetworkIdle();
    
    if (expectedPath) {
      await this.waitForUrlChange(expectedPath);
    } else {
      // Verify we're not on login page anymore
      const currentUrl = this.page.url();
      expect(currentUrl).not.toContain('/login');
    }
  }
}
