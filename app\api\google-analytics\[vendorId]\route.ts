import { NextRequest, NextResponse } from 'next/server'
import { RealGoogleAnalyticsService } from '@/lib/services/realGoogleAnalyticsService'

export async function GET(
  request: NextRequest,
  { params }: { params: { vendorId: string } }
) {
  try {
    const { searchParams } = new URL(request.url)
    const dateRange = searchParams.get('range') || '30d'
    
    RealGoogleAnalyticsService.initialize()
    const data = await RealGoogleAnalyticsService.getVendorPageAnalytics(
      params.vendorId,
      dateRange
    )
    
    return NextResponse.json(data)
  } catch (error) {
    console.error('GA API Error:', error)
    return NextResponse.json({ error: 'Failed to fetch analytics' }, { status: 500 })
  }
}