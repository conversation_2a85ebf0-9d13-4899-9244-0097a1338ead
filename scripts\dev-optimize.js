#!/usr/bin/env node

/**
 * Development Performance Optimization Script
 * This script helps optimize the development environment for faster builds and hot reloads
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🚀 Optimizing development environment...\n');

// 1. Check Node.js version
function checkNodeVersion() {
  const nodeVersion = process.version;
  const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);
  
  console.log(`📋 Node.js version: ${nodeVersion}`);
  
  if (majorVersion < 18) {
    console.log('⚠️  Warning: Node.js 18+ recommended for better performance');
    console.log('   Consider upgrading: https://nodejs.org/');
  } else {
    console.log('✅ Node.js version is good');
  }
  console.log('');
}

// 2. Check memory usage
function checkMemoryUsage() {
  const used = process.memoryUsage();
  console.log('💾 Memory Usage:');
  for (let key in used) {
    console.log(`   ${key}: ${Math.round(used[key] / 1024 / 1024 * 100) / 100} MB`);
  }
  console.log('');
}

// 3. Clean Next.js cache
function cleanNextCache() {
  console.log('🧹 Cleaning Next.js cache...');
  try {
    if (fs.existsSync('.next')) {
      execSync('rm -rf .next', { stdio: 'inherit' });
      console.log('✅ Cleaned .next directory');
    }
    
    if (fs.existsSync('node_modules/.cache')) {
      execSync('rm -rf node_modules/.cache', { stdio: 'inherit' });
      console.log('✅ Cleaned node_modules cache');
    }
    
    console.log('');
  } catch (error) {
    console.log('❌ Error cleaning cache:', error.message);
  }
}

// 4. Check package.json scripts
function checkPackageScripts() {
  console.log('📦 Checking package.json scripts...');
  
  const packagePath = path.join(process.cwd(), 'package.json');
  if (fs.existsSync(packagePath)) {
    const pkg = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
    
    // Check if turbo dev script exists
    if (pkg.scripts && pkg.scripts.dev) {
      console.log(`   Current dev script: ${pkg.scripts.dev}`);
      
      if (!pkg.scripts.dev.includes('--turbo')) {
        console.log('💡 Suggestion: Add --turbo flag for faster development');
        console.log('   Example: "next dev --turbo"');
      } else {
        console.log('✅ Turbo mode enabled');
      }
    }
    
    // Check for performance scripts
    const recommendedScripts = {
      'dev:fast': 'next dev --turbo',
      'dev:debug': 'NODE_OPTIONS="--inspect" next dev --turbo',
      'clean': 'rm -rf .next node_modules/.cache',
      'analyze': 'ANALYZE=true npm run build'
    };
    
    console.log('\n💡 Recommended scripts to add:');
    Object.entries(recommendedScripts).forEach(([name, script]) => {
      if (!pkg.scripts || !pkg.scripts[name]) {
        console.log(`   "${name}": "${script}"`);
      }
    });
  }
  console.log('');
}

// 5. Check for large files that might slow down builds
function checkLargeFiles() {
  console.log('📁 Checking for large files...');
  
  const checkDir = (dir, maxSize = 1024 * 1024) => { // 1MB
    const largeFiles = [];
    
    function scanDir(currentDir) {
      try {
        const items = fs.readdirSync(currentDir);
        
        for (const item of items) {
          const fullPath = path.join(currentDir, item);
          const stat = fs.statSync(fullPath);
          
          if (stat.isDirectory()) {
            // Skip node_modules and .next
            if (!['node_modules', '.next', '.git'].includes(item)) {
              scanDir(fullPath);
            }
          } else if (stat.size > maxSize) {
            largeFiles.push({
              path: fullPath,
              size: Math.round(stat.size / 1024 / 1024 * 100) / 100
            });
          }
        }
      } catch (error) {
        // Skip directories we can't read
      }
    }
    
    scanDir(dir);
    return largeFiles;
  };
  
  const largeFiles = checkDir(process.cwd());
  
  if (largeFiles.length > 0) {
    console.log('⚠️  Large files found (>1MB):');
    largeFiles.forEach(file => {
      console.log(`   ${file.path} (${file.size}MB)`);
    });
    console.log('   Consider optimizing or moving these files');
  } else {
    console.log('✅ No large files found');
  }
  console.log('');
}

// 6. Environment recommendations
function environmentRecommendations() {
  console.log('🔧 Development Environment Recommendations:\n');
  
  console.log('1. Use Turbo mode:');
  console.log('   npm run dev -- --turbo');
  console.log('');
  
  console.log('2. Increase Node.js memory limit for large apps:');
  console.log('   NODE_OPTIONS="--max-old-space-size=4096" npm run dev');
  console.log('');
  
  console.log('3. Use SWC instead of Babel (already configured):');
  console.log('   ✅ SWC is enabled in next.config.js');
  console.log('');
  
  console.log('4. Optimize imports:');
  console.log('   - Use dynamic imports for heavy components');
  console.log('   - Import only what you need from libraries');
  console.log('   - Consider using next/dynamic for code splitting');
  console.log('');
  
  console.log('5. Development vs Production:');
  console.log('   - Disable optimizations in development');
  console.log('   - Use React.StrictMode only in development');
  console.log('   - Enable source maps for debugging');
  console.log('');
}

// 7. Create optimized dev script
function createOptimizedDevScript() {
  console.log('📝 Creating optimized development script...');
  
  const devScript = `#!/bin/bash

# Optimized Development Script for BookmyFestive
echo "🚀 Starting optimized development server..."

# Set Node.js options for better performance
export NODE_OPTIONS="--max-old-space-size=4096 --experimental-vm-modules"

# Set environment variables
export NODE_ENV=development
export NEXT_TELEMETRY_DISABLED=1

# Clean cache if requested
if [ "$1" = "--clean" ]; then
  echo "🧹 Cleaning cache..."
  rm -rf .next node_modules/.cache
fi

# Start development server with optimizations
echo "🔥 Starting Next.js with Turbo..."
next dev --turbo --port 3000
`;

  fs.writeFileSync('dev-fast.sh', devScript);
  execSync('chmod +x dev-fast.sh');
  console.log('✅ Created dev-fast.sh script');
  console.log('   Usage: ./dev-fast.sh [--clean]');
  console.log('');
}

// Main execution
async function main() {
  checkNodeVersion();
  checkMemoryUsage();
  cleanNextCache();
  checkPackageScripts();
  checkLargeFiles();
  environmentRecommendations();
  createOptimizedDevScript();
  
  console.log('🎉 Development optimization complete!');
  console.log('\n📋 Quick Start:');
  console.log('   1. Run: ./dev-fast.sh');
  console.log('   2. Or: npm run dev -- --turbo');
  console.log('   3. For clean start: ./dev-fast.sh --clean');
}

main().catch(console.error);
