"use client"

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Users,
  Search,
  Plus,
  Edit,
  Shield,
  Crown,
  Briefcase,
  User,
  Trash2,
  Filter,
  Download,
  Upload,
  MoreHorizontal,
  Eye,
  UserCheck,
  UserX,
  Calendar,
  Mail,
  Phone,
  Menu,
  X
} from 'lucide-react';
import UserRoleManager, { QuickRoleButtons } from '@/components/admin/UserRoleManager';
import { AdminOnlyRoute } from '@/components/RouteProtection';
import { UserRole, RoleUpdateResult } from '@/lib/services/userRoleService';
import { profileService } from '@/lib/services/profileService';
import AdminUserService, { UserProfile, UserSearchFilters } from '@/lib/services/adminUserService';
import toast from 'react-hot-toast';

export default function UserManagementPage() {
  const [users, setUsers] = useState<UserProfile[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedUser, setSelectedUser] = useState<UserProfile | null>(null);
  const [selectedUsers, setSelectedUsers] = useState<string[]>([]);
  const [filters, setFilters] = useState<UserSearchFilters>({});
  const [showFilters, setShowFilters] = useState(false);
  const [showUserDetails, setShowUserDetails] = useState(false);
  const [showRoleManager, setShowRoleManager] = useState(false);
  const [userStats, setUserStats] = useState<any>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [nextToken, setNextToken] = useState<string | undefined>();
  const [hasMore, setHasMore] = useState(true);
  const [itemsPerPage] = useState(10);

  useEffect(() => {
    loadUsers();
    loadUserStats();
  }, []);

  useEffect(() => {
    const delayedSearch = setTimeout(() => {
      loadUsers();
    }, 500);

    return () => clearTimeout(delayedSearch);
  }, [searchTerm, filters]);

  const loadUsers = async (loadMore: boolean = false, page: number = 1) => {
    try {
      setLoading(true);

      const searchFilters: UserSearchFilters = {
        ...filters,
        searchTerm: searchTerm || undefined
      };

      const result = await AdminUserService.getAllUsers(
        searchFilters,
        itemsPerPage,
        loadMore ? nextToken : undefined
      );

      if (result.success && result.data) {
        if (loadMore) {
          setUsers(prev => [...prev, ...result.data!.users]);
        } else {
          setUsers(result.data.users);
          setCurrentPage(page);
        }
        setNextToken(result.data.nextToken);
        setHasMore(!!result.data.nextToken);
      } else {
        toast.error(result.error || 'Failed to load users');
      }
    } catch (error) {
      console.error('Error loading users:', error);
      toast.error('Failed to load users');
    } finally {
      setLoading(false);
    }
  };

  const loadUserStats = async () => {
    try {
      const result = await AdminUserService.getUserStatistics();
      if (result.success) {
        setUserStats(result.data);
      }
    } catch (error) {
      console.error('Error loading user stats:', error);
    }
  };

  const determineUserRole = (user: UserProfile): UserRole => {
    if (user.isSuperAdmin || user.role === 'SUPER_ADMIN') return 'super_admin';
    if (user.isAdmin || user.role === 'ADMIN') return 'admin';
    if (user.isVendor || user.role === 'VENDOR') return 'vendor';
    return 'customer';
  };

  const handleDeleteUser = async (userId: string) => {
    if (!confirm('Are you sure you want to delete this user? This action cannot be undone.')) {
      return;
    }

    try {
      const result = await AdminUserService.deleteUser(userId);
      if (result.success) {
        toast.success('User deleted successfully');
        loadUsers();
        loadUserStats();
      } else {
        toast.error(result.error || 'Failed to delete user');
      }
    } catch (error) {
      console.error('Error deleting user:', error);
      toast.error('Failed to delete user');
    }
  };

  const handleBulkAction = async (action: 'delete' | 'activate' | 'deactivate') => {
    if (selectedUsers.length === 0) {
      toast.error('Please select users first');
      return;
    }

    if (action === 'delete' && !confirm(`Are you sure you want to delete ${selectedUsers.length} users? This action cannot be undone.`)) {
      return;
    }

    try {
      if (action === 'delete') {
        const deletePromises = selectedUsers.map(userId => AdminUserService.deleteUser(userId));
        await Promise.all(deletePromises);
        toast.success(`${selectedUsers.length} users deleted successfully`);
      } else {
        const updatePromises = selectedUsers.map(userId =>
          AdminUserService.updateUser(userId, {
            isVerified: action === 'activate'
          })
        );
        await Promise.all(updatePromises);
        toast.success(`${selectedUsers.length} users ${action}d successfully`);
      }

      setSelectedUsers([]);
      loadUsers();
      loadUserStats();
    } catch (error) {
      console.error(`Error performing bulk ${action}:`, error);
      toast.error(`Failed to ${action} users`);
    }
  };

  const handleSelectAll = () => {
    if (selectedUsers.length === users.length) {
      setSelectedUsers([]);
    } else {
      setSelectedUsers(users.map(user => user.id));
    }
  };

  const handleUserSelect = (userId: string) => {
    setSelectedUsers(prev =>
      prev.includes(userId)
        ? prev.filter(id => id !== userId)
        : [...prev, userId]
    );
  };

  const applyFilters = (newFilters: UserSearchFilters) => {
    setFilters(newFilters);
    setCurrentPage(1);
    setNextToken(undefined);
  };

  const clearFilters = () => {
    setFilters({});
    setSearchTerm('');
    setCurrentPage(1);
    setNextToken(undefined);
  };

  const getRoleBadge = (role: UserRole) => {
    const configs = {
      super_admin: { label: 'Super Admin', icon: Crown, className: 'bg-purple-100 text-purple-800' },
      admin: { label: 'Admin', icon: Shield, className: 'bg-orange-100 text-orange-800' },
      vendor: { label: 'Vendor', icon: Briefcase, className: 'bg-green-100 text-green-800' },
      customer: { label: 'Customer', icon: User, className: 'bg-blue-100 text-blue-800' }
    };

    const config = configs[role];
    const IconComponent = config.icon;

    return (
      <Badge className={`${config.className} text-xs px-2 py-1`}>
        <IconComponent className="w-3 h-3 mr-1" />
        <span className="hidden sm:inline">{config.label}</span>
        <span className="sm:hidden">{config.label.split(' ')[0]}</span>
      </Badge>
    );
  };

  const handleRoleUpdated = (result: RoleUpdateResult) => {
    // Refresh the user list
    loadUsers();
    loadUserStats();
    setShowRoleManager(false);
    setSelectedUser(null);
  };

  const openRoleManager = (user: UserProfile) => {
    setSelectedUser(user);
    setShowRoleManager(true);
  };

  const openUserDetails = (user: UserProfile) => {
    setSelectedUser(user);
    setShowUserDetails(true);
  };

  return (
    <AdminOnlyRoute>
      <div className="space-y-4 sm:space-y-8 px-2 sm:px-0">
        {/* Header */}
        <div className="flex flex-col gap-4 mb-4 sm:mb-6">
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-3 sm:gap-4">
            <div>
              <h1 className="text-2xl sm:text-3xl font-bold text-gray-900 mb-1">Manage Users</h1>
              <p className="text-gray-500 text-sm sm:text-base">{users.length} user{users.length !== 1 ? 's' : ''}</p>
            </div>
            <div className="flex flex-col sm:flex-row gap-2 w-full sm:w-auto">
              {selectedUsers.length > 0 && (
                <Button 
                  variant="outline" 
                  onClick={() => handleBulkAction('delete')} 
                  className="text-red-600 hover:text-red-700 hover:bg-red-50 text-sm"
                >
                  Delete Selected ({selectedUsers.length})
                </Button>
              )}
              <Button 
                className="bg-primary hover:bg-primary/90 text-sm" 
                onClick={() => setShowRoleManager(true)}
              >
                <Plus className="w-4 h-4 mr-2" />
                <span className="hidden sm:inline">Update User Role</span>
                <span className="sm:hidden">Update Role</span>
              </Button>
            </div>
          </div>
        </div>

        {/* Filters */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-3 sm:p-4 mb-4 sm:mb-6">
          <div className="space-y-3 sm:space-y-0 sm:grid sm:grid-cols-1 md:grid-cols-4 gap-3 sm:gap-4">
            <div className="relative">
              <Input 
                type="text" 
                placeholder="Search users..." 
                value={searchTerm} 
                onChange={e => setSearchTerm(e.target.value)} 
                className="w-full pl-10 text-sm" 
              />
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            </div>
            <Select value={filters.role || 'all'} onValueChange={value => applyFilters({ ...filters, role: value === 'all' ? undefined : value })}>
              <SelectTrigger className="text-sm">
                <SelectValue placeholder="All Roles" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Roles</SelectItem>
                <SelectItem value="CUSTOMER">Customer</SelectItem>
                <SelectItem value="VENDOR">Vendor</SelectItem>
                <SelectItem value="ADMIN">Admin</SelectItem>
                <SelectItem value="SUPER_ADMIN">Super Admin</SelectItem>
              </SelectContent>
            </Select>
            <Select value={filters.isAdmin !== undefined ? String(filters.isAdmin) : 'all'} onValueChange={value => applyFilters({ ...filters, isAdmin: value === 'all' ? undefined : value === 'true' })}>
              <SelectTrigger className="text-sm">
                <SelectValue placeholder="All Users" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Users</SelectItem>
                <SelectItem value="true">Admins Only</SelectItem>
                <SelectItem value="false">Non-Admins</SelectItem>
              </SelectContent>
            </Select>
            <Select value={filters.isVendor !== undefined ? String(filters.isVendor) : 'all'} onValueChange={value => applyFilters({ ...filters, isVendor: value === 'all' ? undefined : value === 'true' })}>
              <SelectTrigger className="text-sm">
                <SelectValue placeholder="All Users" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Users</SelectItem>
                <SelectItem value="true">Vendors Only</SelectItem>
                <SelectItem value="false">Non-Vendors</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Select All Section */}
        {users.length > 0 && (
          <div className="flex items-center gap-2 p-3 sm:p-4 bg-gray-50 rounded-lg border border-gray-200">
            <Checkbox 
              checked={selectedUsers.length === users.length && users.length > 0} 
              onCheckedChange={handleSelectAll} 
              className="h-3 w-3 rounded-sm border-gray-400 data-[state=checked]:bg-primary data-[state=checked]:border-primary" 
            />
            <span className="text-sm font-medium text-gray-700">
              Select All ({selectedUsers.length}/{users.length})
            </span>
          </div>
        )}

        {/* User Cards Grid */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-6">
          {loading ? (
            <div className="col-span-full text-center py-8 sm:py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
              <p className="mt-2 text-gray-600 text-sm sm:text-base">Loading users...</p>
            </div>
          ) : users.length === 0 ? (
            <div className="col-span-full text-center py-8 sm:py-12">
              <Users className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-600 text-sm sm:text-base">No users found</p>
            </div>
          ) : (
            users.map((user) => {
              const userRole = determineUserRole(user);
              return (
                <Card key={user.id} className="overflow-hidden rounded-xl sm:rounded-2xl shadow border border-gray-100 bg-white hover:shadow-md transition-shadow relative">
                  <CardContent className="p-4 sm:p-6 flex flex-col h-full">
                    <div className="flex items-start justify-between mb-3">
                      <div className="flex-1 min-w-0">
                        <h2 className="font-bold text-lg sm:text-xl text-gray-900 mb-1 line-clamp-1 truncate">
                          {user.firstName} {user.lastName}
                        </h2>
                        <p className="text-primary font-medium text-xs uppercase tracking-wide mb-1 truncate">
                          {user.email}
                        </p>
                      </div>
                      <div className="flex flex-col gap-1 ml-2">
                        {getRoleBadge(userRole)}
                        <Badge variant={user.isVerified ? 'default' : 'secondary'} className="text-xs px-2 py-1">
                          {user.isVerified ? 'Verified' : 'Pending'}
                        </Badge>
                      </div>
                    </div>
                    
                    <div className="text-gray-500 text-xs sm:text-sm mb-2 flex items-center gap-2">
                      <Phone className="w-3 h-3" />
                      <span className="truncate">{user.phone || '-'}</span>
                    </div>
                    
                    <div className="flex flex-col gap-1 mb-3">
                      <span className="text-xs text-gray-500">
                        Joined: {new Date(user.createdAt).toLocaleDateString()}
                      </span>
                      {user.lastLoginAt && (
                        <span className="text-xs text-gray-500">
                          Last: {new Date(user.lastLoginAt).toLocaleDateString()}
                        </span>
                      )}
                    </div>
                    
                    <div className="flex flex-col sm:flex-row gap-2 mt-auto">
                      <Button 
                        variant="outline" 
                        size="sm" 
                        onClick={() => openUserDetails(user)} 
                        className="flex-1 text-xs sm:text-sm"
                      >
                        <Eye className="w-3 h-3 mr-1 sm:hidden" />
                        <span className="hidden sm:inline">Details</span>
                        <span className="sm:hidden">View</span>
                      </Button>
                      <Button 
                        variant="outline" 
                        size="sm" 
                        onClick={() => openRoleManager(user)} 
                        className="flex-1 text-xs sm:text-sm"
                      >
                        <Edit className="w-3 h-3 mr-1 sm:hidden" />
                        <span className="hidden sm:inline">Edit Role</span>
                        <span className="sm:hidden">Role</span>
                      </Button>
                      <Button 
                        variant="outline" 
                        size="sm" 
                        onClick={() => handleDeleteUser(user.id)} 
                        className="flex-1 text-red-600 hover:text-red-700 text-xs sm:text-sm"
                      >
                        <Trash2 className="w-3 h-3 mr-1 sm:hidden" />
                        <span className="hidden sm:inline">Delete</span>
                        <span className="sm:hidden">Del</span>
                      </Button>
                      <Checkbox 
                        checked={selectedUsers.includes(user.id)} 
                        onCheckedChange={() => handleUserSelect(user.id)} 
                        className="ml-2 mt-1 h-3 w-3 rounded-sm border-gray-400 data-[state=checked]:bg-primary data-[state=checked]:border-primary" 
                      />
                    </div>
                  </CardContent>
                </Card>
              );
            })
          )}
        </div>

        {/* Pagination Controls */}
        {users.length > 0 && (
          <div className="flex flex-col sm:flex-row justify-center items-center gap-3 sm:gap-4 pt-4 sm:pt-6">
            <Button
              variant="outline"
              onClick={() => {
                if (currentPage > 1) {
                  loadUsers(false, currentPage - 1);
                }
              }}
              disabled={loading || currentPage === 1}
              className="text-sm w-full sm:w-auto"
            >
              Previous
            </Button>
            <span className="text-xs sm:text-sm text-gray-600 text-center">
              Page {currentPage} • {users.length} items
            </span>
            <Button
              variant="outline"
              onClick={() => {
                if (hasMore) {
                  loadUsers(false, currentPage + 1);
                }
              }}
              disabled={loading || !hasMore}
              className="text-sm w-full sm:w-auto"
            >
              Next
            </Button>
          </div>
        )}

        {/* User Details Modal */}
        <Dialog open={showUserDetails} onOpenChange={setShowUserDetails}>
          <DialogContent className="max-w-2xl rounded-2xl mx-4 sm:mx-auto">
            <DialogHeader>
              <DialogTitle className="text-lg sm:text-xl">User Details</DialogTitle>
            </DialogHeader>
            {selectedUser && (
              <div className="space-y-4 sm:space-y-6">
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Name</label>
                    <p className="mt-1 text-sm text-gray-900">{selectedUser.firstName} {selectedUser.lastName}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Email</label>
                    <p className="mt-1 text-sm text-gray-900 break-all">{selectedUser.email}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">User ID</label>
                    <p className="mt-1 text-sm text-gray-900 font-mono break-all">{selectedUser.userId}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Role</label>
                    <div className="mt-1">{getRoleBadge(determineUserRole(selectedUser))}</div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Account Type</label>
                    <p className="mt-1 text-sm text-gray-900">{selectedUser.accountType || 'Standard'}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Verification Status</label>
                    <Badge variant={selectedUser.isVerified ? "default" : "secondary"} className="mt-1">
                      {selectedUser.isVerified ? "Verified" : "Pending"}
                    </Badge>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Created At</label>
                    <p className="mt-1 text-sm text-gray-900">{new Date(selectedUser.createdAt).toLocaleString()}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Last Login</label>
                    <p className="mt-1 text-sm text-gray-900">
                      {selectedUser.lastLoginAt ? new Date(selectedUser.lastLoginAt).toLocaleString() : 'Never'}
                    </p>
                  </div>
                </div>
                {selectedUser.businessInfo && (
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 mb-3">Business Information</h3>
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700">Business Name</label>
                        <p className="mt-1 text-sm text-gray-900">{selectedUser.businessInfo.businessName}</p>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700">Business Type</label>
                        <p className="mt-1 text-sm text-gray-900">{selectedUser.businessInfo.businessType}</p>
                      </div>
                    </div>
                  </div>
                )}
                <div className="flex flex-col sm:flex-row gap-2">
                  <Button onClick={() => openRoleManager(selectedUser)} className="text-sm">
                    <Edit className="w-4 h-4 mr-2" />
                    Edit Role
                  </Button>
                  <Button variant="outline" onClick={() => setShowUserDetails(false)} className="text-sm">
                    Close
                  </Button>
                </div>
              </div>
            )}
          </DialogContent>
        </Dialog>

        {/* Role Manager Modal */}
        <Dialog open={showRoleManager} onOpenChange={setShowRoleManager}>
          <DialogContent className="max-w-2xl rounded-2xl mx-4 sm:mx-auto">
            <DialogHeader>
              <DialogTitle className="text-lg sm:text-xl">Update User Role</DialogTitle>
            </DialogHeader>
            <UserRoleManager
              targetUserId={selectedUser?.userId}
              targetUserEmail={selectedUser?.email}
              currentRole={selectedUser ? determineUserRole(selectedUser) : 'customer'}
              onRoleUpdated={handleRoleUpdated}
            />
          </DialogContent>
        </Dialog>
      </div>
    </AdminOnlyRoute>
  );
}
