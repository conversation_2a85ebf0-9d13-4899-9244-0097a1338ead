import { generateClient } from 'aws-amplify/api';
import * as queries from '../../../src/graphql/queries';
import * as mutations from '../../../src/graphql/mutations';
import * as subscriptions from '../../../src/graphql/subscriptions';
import {
  Vendor,
  Venue,
  Shop,
  UserProfile,
  Review,
  Inquiry,
  NewsletterSubscription,
  Blog,
  ModelConnection,
  ModelVendorFilterInput,
  ModelVenueFilterInput,
  ModelShopFilterInput,
  ModelUserProfileFilterInput,
  ModelReviewFilterInput,
  ModelBlogFilterInput,
  CreateVendorInput,
  UpdateVendorInput,
  CreateUserProfileInput,
  UpdateUserProfileInput,
  CreateReviewInput,
  UpdateReviewInput,
  CreateInquiryInput,
  UpdateInquiryInput,
  CreateNewsletterSubscriptionInput,
  UpdateNewsletterSubscriptionInput,
} from '../types/graphql';

// Create shared GraphQL client
const client = generateClient();

export class SharedGraphQLClient {
  // Vendor operations
  async getVendor(id: string): Promise<Vendor | null> {
    try {
      const result = await client.graphql({
        query: queries.getVendor,
        variables: { id },
      });
      return result.data?.getVendor || null;
    } catch (error) {
      console.error('Error fetching vendor:', error);
      throw error;
    }
  }

  async listVendors(
    filter?: ModelVendorFilterInput,
    limit: number = 20,
    nextToken?: string
  ): Promise<ModelConnection<Vendor>> {
    try {
      const result = await client.graphql({
        query: queries.listVendors,
        variables: { filter, limit, nextToken },
      });
      return result.data?.listVendors || { items: [], nextToken: null };
    } catch (error) {
      console.error('Error listing vendors:', error);
      throw error;
    }
  }

  async createVendor(input: CreateVendorInput): Promise<Vendor> {
    try {
      const result = await client.graphql({
        query: mutations.createVendor,
        variables: { input },
      });
      return result.data?.createVendor;
    } catch (error) {
      console.error('Error creating vendor:', error);
      throw error;
    }
  }

  async updateVendor(input: UpdateVendorInput): Promise<Vendor> {
    try {
      const result = await client.graphql({
        query: mutations.updateVendor,
        variables: { input },
      });
      return result.data?.updateVendor;
    } catch (error) {
      console.error('Error updating vendor:', error);
      throw error;
    }
  }

  // Venue operations
  async getVenue(id: string): Promise<Venue | null> {
    try {
      const result = await client.graphql({
        query: queries.getVenue,
        variables: { id },
      });
      return result.data?.getVenue || null;
    } catch (error) {
      console.error('Error fetching venue:', error);
      throw error;
    }
  }

  async listVenues(
    filter?: ModelVenueFilterInput,
    limit: number = 20,
    nextToken?: string
  ): Promise<ModelConnection<Venue>> {
    try {
      const result = await client.graphql({
        query: queries.listVenues,
        variables: { filter, limit, nextToken },
      });
      return result.data?.listVenues || { items: [], nextToken: null };
    } catch (error) {
      console.error('Error listing venues:', error);
      throw error;
    }
  }

  // Shop operations
  async getShop(id: string): Promise<Shop | null> {
    try {
      const result = await client.graphql({
        query: queries.getShop,
        variables: { id },
      });
      return result.data?.getShop || null;
    } catch (error) {
      console.error('Error fetching shop:', error);
      throw error;
    }
  }

  async listShops(
    filter?: ModelShopFilterInput,
    limit: number = 20,
    nextToken?: string
  ): Promise<ModelConnection<Shop>> {
    try {
      const result = await client.graphql({
        query: queries.listShops,
        variables: { filter, limit, nextToken },
      });
      return result.data?.listShops || { items: [], nextToken: null };
    } catch (error) {
      console.error('Error listing shops:', error);
      throw error;
    }
  }

  // User Profile operations
  async getUserProfile(id: string): Promise<UserProfile | null> {
    try {
      const result = await client.graphql({
        query: queries.getUserProfile,
        variables: { id },
      });
      return result.data?.getUserProfile || null;
    } catch (error) {
      console.error('Error fetching user profile:', error);
      throw error;
    }
  }

  async createUserProfile(input: CreateUserProfileInput): Promise<UserProfile> {
    try {
      const result = await client.graphql({
        query: mutations.createUserProfile,
        variables: { input },
      });
      return result.data?.createUserProfile;
    } catch (error) {
      console.error('Error creating user profile:', error);
      throw error;
    }
  }

  async updateUserProfile(input: UpdateUserProfileInput): Promise<UserProfile> {
    try {
      const result = await client.graphql({
        query: mutations.updateUserProfile,
        variables: { input },
      });
      return result.data?.updateUserProfile;
    } catch (error) {
      console.error('Error updating user profile:', error);
      throw error;
    }
  }

  // Review operations
  async getReview(id: string): Promise<Review | null> {
    try {
      const result = await client.graphql({
        query: queries.getReview,
        variables: { id },
      });
      return result.data?.getReview || null;
    } catch (error) {
      console.error('Error fetching review:', error);
      throw error;
    }
  }

  async listReviews(
    filter?: ModelReviewFilterInput,
    limit: number = 20,
    nextToken?: string
  ): Promise<ModelConnection<Review>> {
    try {
      const result = await client.graphql({
        query: queries.listReviews,
        variables: { filter, limit, nextToken },
      });
      return result.data?.listReviews || { items: [], nextToken: null };
    } catch (error) {
      console.error('Error listing reviews:', error);
      throw error;
    }
  }

  async createReview(input: CreateReviewInput): Promise<Review> {
    try {
      const result = await client.graphql({
        query: mutations.createReview,
        variables: { input },
      });
      return result.data?.createReview;
    } catch (error) {
      console.error('Error creating review:', error);
      throw error;
    }
  }

  async updateReview(input: UpdateReviewInput): Promise<Review> {
    try {
      const result = await client.graphql({
        query: mutations.updateReview,
        variables: { input },
      });
      return result.data?.updateReview;
    } catch (error) {
      console.error('Error updating review:', error);
      throw error;
    }
  }

  // Inquiry operations
  async createInquiry(input: CreateInquiryInput): Promise<Inquiry> {
    try {
      const result = await client.graphql({
        query: mutations.createInquiry,
        variables: { input },
      });
      return result.data?.createInquiry;
    } catch (error) {
      console.error('Error creating inquiry:', error);
      throw error;
    }
  }

  async updateInquiry(input: UpdateInquiryInput): Promise<Inquiry> {
    try {
      const result = await client.graphql({
        query: mutations.updateInquiry,
        variables: { input },
      });
      return result.data?.updateInquiry;
    } catch (error) {
      console.error('Error updating inquiry:', error);
      throw error;
    }
  }

  // Newsletter operations
  async createNewsletterSubscription(input: CreateNewsletterSubscriptionInput): Promise<NewsletterSubscription> {
    try {
      const result = await client.graphql({
        query: mutations.createNewsletterSubscription,
        variables: { input },
      });
      return result.data?.createNewsletterSubscription;
    } catch (error) {
      console.error('Error creating newsletter subscription:', error);
      throw error;
    }
  }

  async updateNewsletterSubscription(input: UpdateNewsletterSubscriptionInput): Promise<NewsletterSubscription> {
    try {
      const result = await client.graphql({
        query: mutations.updateNewsletterSubscription,
        variables: { input },
      });
      return result.data?.updateNewsletterSubscription;
    } catch (error) {
      console.error('Error updating newsletter subscription:', error);
      throw error;
    }
  }

  // Blog operations
  async getBlog(id: string): Promise<Blog | null> {
    try {
      const result = await client.graphql({
        query: `
          query GetBlog($id: ID!) {
            getBlog(id: $id) {
              id
              title
              content
              excerpt
              category
              authorId
              authorName
              authorType
              featuredImage
              tags
              status
              views
              likes
              comments
              isPinned
              isFeatured
              publishedAt
              createdAt
              updatedAt
            }
          }
        `,
        variables: { id },
      });
      return result.data?.getBlog || null;
    } catch (error) {
      console.error('Error fetching blog:', error);
      throw error;
    }
  }

  async listBlogs(
    filter?: ModelBlogFilterInput,
    limit: number = 20,
    nextToken?: string
  ): Promise<ModelConnection<Blog>> {
    try {
      const result = await client.graphql({
        query: `
          query ListBlogs(
            $filter: ModelBlogFilterInput
            $limit: Int
            $nextToken: String
          ) {
            listBlogs(filter: $filter, limit: $limit, nextToken: $nextToken) {
              items {
                id
                title
                content
                excerpt
                category
                authorId
                authorName
                authorType
                featuredImage
                tags
                status
                views
                likes
                comments
                isPinned
                isFeatured
                publishedAt
                createdAt
                updatedAt
              }
              nextToken
            }
          }
        `,
        variables: { filter, limit, nextToken },
      });
      return result.data?.listBlogs || { items: [], nextToken: null };
    } catch (error) {
      console.error('Error listing blogs:', error);
      throw error;
    }
  }

  // Subscription operations (for real-time updates)
  subscribeToVendorUpdates(callback: (vendor: Vendor) => void) {
    try {
      const subscription = client.graphql({
        query: subscriptions.onUpdateVendor,
      }).subscribe({
        next: ({ data }) => {
          if (data?.onUpdateVendor) {
            callback(data.onUpdateVendor);
          }
        },
        error: (error) => {
          console.error('Vendor subscription error:', error);
        },
      });
      return subscription;
    } catch (error) {
      console.error('Error setting up vendor subscription:', error);
      throw error;
    }
  }

  subscribeToReviewUpdates(callback: (review: Review) => void) {
    try {
      const subscription = client.graphql({
        query: subscriptions.onCreateReview,
      }).subscribe({
        next: ({ data }) => {
          if (data?.onCreateReview) {
            callback(data.onCreateReview);
          }
        },
        error: (error) => {
          console.error('Review subscription error:', error);
        },
      });
      return subscription;
    } catch (error) {
      console.error('Error setting up review subscription:', error);
      throw error;
    }
  }
}

// Export singleton instance
export const sharedGraphQLClient = new SharedGraphQLClient();
export default SharedGraphQLClient;
