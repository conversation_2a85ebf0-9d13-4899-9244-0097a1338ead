import { getCurrentUser } from 'aws-amplify/auth';

export const debugUserAuth = async () => {
  try {
    const user = await getCurrentUser();
    console.log('Current User:', {
      userId: user.userId,
      username: user.username,
      signInDetails: user.signInDetails,
    });

    // Check user groups from JWT token
    const session = await user.getSignInUserSession?.();
    if (session) {
      const accessToken = session.getAccessToken();
      const idToken = session.getIdToken();
      
      console.log('Access Token Groups:', accessToken.payload['cognito:groups']);
      console.log('ID Token Groups:', idToken.payload['cognito:groups']);
      console.log('Access Token Payload:', accessToken.payload);
      console.log('ID Token Payload:', idToken.payload);
    }

    return user;
  } catch (error) {
    console.error('Debug auth error:', error);
    throw error;
  }
};