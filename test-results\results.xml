<testsuites id="" name="" tests="368" failures="0" skipped="368" errors="0" time="0.18308799999999997">
<testsuite name="admin/admin-dashboard.spec.ts" timestamp="2025-08-01T13:32:21.943Z" hostname="chromium" tests="6" failures="0" skipped="6" time="0" errors="0">
<testcase name="Admin Dashboard › should display admin dashboard" classname="admin/admin-dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Dashboard › should have user management section" classname="admin/admin-dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Dashboard › should have vendor management section" classname="admin/admin-dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Dashboard › should display analytics/statistics" classname="admin/admin-dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin User Management › should list all users" classname="admin/admin-dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin User Management › should allow user search" classname="admin/admin-dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="auth/login.spec.ts" timestamp="2025-08-01T13:32:21.943Z" hostname="chromium" tests="9" failures="0" skipped="9" time="0" errors="0">
<testcase name="User Login › should display login form" classname="auth/login.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Login › should show validation errors for empty fields" classname="auth/login.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Login › should show error for invalid credentials" classname="auth/login.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Login › should successfully login with valid credentials" classname="auth/login.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Login › should redirect to previous page after login" classname="auth/login.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Login › should show/hide password toggle" classname="auth/login.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Login › should have Google login option" classname="auth/login.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Login › should have forgot password link" classname="auth/login.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Login › should have signup link" classname="auth/login.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="auth/signup.spec.ts" timestamp="2025-08-01T13:32:21.943Z" hostname="chromium" tests="9" failures="0" skipped="9" time="0" errors="0">
<testcase name="User Signup › should display signup form" classname="auth/signup.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Signup › should show validation errors for empty required fields" classname="auth/signup.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Signup › should show error for invalid email format" classname="auth/signup.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Signup › should show error for weak password" classname="auth/signup.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Signup › should successfully register new user" classname="auth/signup.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Signup › should show error for existing email" classname="auth/signup.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Signup › should have Google signup option" classname="auth/signup.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Signup › should have login link for existing users" classname="auth/signup.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Signup › should handle terms and conditions checkbox" classname="auth/signup.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="booking/venue-booking.spec.ts" timestamp="2025-08-01T13:32:21.943Z" hostname="chromium" tests="10" failures="0" skipped="10" time="0" errors="0">
<testcase name="Venue Booking › should redirect to login when not authenticated" classname="booking/venue-booking.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Venue Booking › should display venue booking form when authenticated" classname="booking/venue-booking.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Venue Booking › should show validation errors for empty required fields" classname="booking/venue-booking.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Venue Booking › should show error for past date selection" classname="booking/venue-booking.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Venue Booking › should check availability for selected date" classname="booking/venue-booking.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Venue Booking › should successfully submit booking with valid data" classname="booking/venue-booking.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Venue Booking › should display venue information on booking page" classname="booking/venue-booking.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Venue Booking › should handle guest count validation" classname="booking/venue-booking.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Venue Booking › should allow cancellation of booking process" classname="booking/venue-booking.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Venue Booking › should show booking summary before confirmation" classname="booking/venue-booking.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="ecommerce/shopping-cart.spec.ts" timestamp="2025-08-01T13:32:21.943Z" hostname="chromium" tests="12" failures="0" skipped="12" time="0" errors="0">
<testcase name="Shopping Cart › should display shop page with products" classname="ecommerce/shopping-cart.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Shopping Cart › should add product to cart" classname="ecommerce/shopping-cart.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Shopping Cart › should display cart items" classname="ecommerce/shopping-cart.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Shopping Cart › should update product quantity in cart" classname="ecommerce/shopping-cart.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Shopping Cart › should remove product from cart" classname="ecommerce/shopping-cart.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Shopping Cart › should calculate total price correctly" classname="ecommerce/shopping-cart.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Shopping Cart › should clear entire cart" classname="ecommerce/shopping-cart.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Shopping Cart › should show empty cart message when no items" classname="ecommerce/shopping-cart.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Shopping Cart › should persist cart items across page navigation" classname="ecommerce/shopping-cart.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Shopping Cart › should show product details in cart" classname="ecommerce/shopping-cart.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Shopping Cart › should handle quantity limits" classname="ecommerce/shopping-cart.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Shopping Cart › should show continue shopping option" classname="ecommerce/shopping-cart.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="admin/admin-dashboard.spec.ts" timestamp="2025-08-01T13:32:21.943Z" hostname="firefox" tests="6" failures="0" skipped="6" time="0" errors="0">
<testcase name="Admin Dashboard › should display admin dashboard" classname="admin/admin-dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Dashboard › should have user management section" classname="admin/admin-dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Dashboard › should have vendor management section" classname="admin/admin-dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Dashboard › should display analytics/statistics" classname="admin/admin-dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin User Management › should list all users" classname="admin/admin-dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin User Management › should allow user search" classname="admin/admin-dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="auth/login.spec.ts" timestamp="2025-08-01T13:32:21.943Z" hostname="firefox" tests="9" failures="0" skipped="9" time="0" errors="0">
<testcase name="User Login › should display login form" classname="auth/login.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Login › should show validation errors for empty fields" classname="auth/login.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Login › should show error for invalid credentials" classname="auth/login.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Login › should successfully login with valid credentials" classname="auth/login.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Login › should redirect to previous page after login" classname="auth/login.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Login › should show/hide password toggle" classname="auth/login.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Login › should have Google login option" classname="auth/login.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Login › should have forgot password link" classname="auth/login.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Login › should have signup link" classname="auth/login.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="auth/signup.spec.ts" timestamp="2025-08-01T13:32:21.943Z" hostname="firefox" tests="9" failures="0" skipped="9" time="0" errors="0">
<testcase name="User Signup › should display signup form" classname="auth/signup.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Signup › should show validation errors for empty required fields" classname="auth/signup.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Signup › should show error for invalid email format" classname="auth/signup.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Signup › should show error for weak password" classname="auth/signup.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Signup › should successfully register new user" classname="auth/signup.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Signup › should show error for existing email" classname="auth/signup.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Signup › should have Google signup option" classname="auth/signup.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Signup › should have login link for existing users" classname="auth/signup.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Signup › should handle terms and conditions checkbox" classname="auth/signup.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="booking/venue-booking.spec.ts" timestamp="2025-08-01T13:32:21.943Z" hostname="firefox" tests="10" failures="0" skipped="10" time="0" errors="0">
<testcase name="Venue Booking › should redirect to login when not authenticated" classname="booking/venue-booking.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Venue Booking › should display venue booking form when authenticated" classname="booking/venue-booking.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Venue Booking › should show validation errors for empty required fields" classname="booking/venue-booking.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Venue Booking › should show error for past date selection" classname="booking/venue-booking.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Venue Booking › should check availability for selected date" classname="booking/venue-booking.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Venue Booking › should successfully submit booking with valid data" classname="booking/venue-booking.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Venue Booking › should display venue information on booking page" classname="booking/venue-booking.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Venue Booking › should handle guest count validation" classname="booking/venue-booking.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Venue Booking › should allow cancellation of booking process" classname="booking/venue-booking.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Venue Booking › should show booking summary before confirmation" classname="booking/venue-booking.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="ecommerce/shopping-cart.spec.ts" timestamp="2025-08-01T13:32:21.943Z" hostname="firefox" tests="12" failures="0" skipped="12" time="0" errors="0">
<testcase name="Shopping Cart › should display shop page with products" classname="ecommerce/shopping-cart.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Shopping Cart › should add product to cart" classname="ecommerce/shopping-cart.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Shopping Cart › should display cart items" classname="ecommerce/shopping-cart.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Shopping Cart › should update product quantity in cart" classname="ecommerce/shopping-cart.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Shopping Cart › should remove product from cart" classname="ecommerce/shopping-cart.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Shopping Cart › should calculate total price correctly" classname="ecommerce/shopping-cart.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Shopping Cart › should clear entire cart" classname="ecommerce/shopping-cart.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Shopping Cart › should show empty cart message when no items" classname="ecommerce/shopping-cart.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Shopping Cart › should persist cart items across page navigation" classname="ecommerce/shopping-cart.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Shopping Cart › should show product details in cart" classname="ecommerce/shopping-cart.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Shopping Cart › should handle quantity limits" classname="ecommerce/shopping-cart.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Shopping Cart › should show continue shopping option" classname="ecommerce/shopping-cart.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="admin/admin-dashboard.spec.ts" timestamp="2025-08-01T13:32:21.943Z" hostname="webkit" tests="6" failures="0" skipped="6" time="0" errors="0">
<testcase name="Admin Dashboard › should display admin dashboard" classname="admin/admin-dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Dashboard › should have user management section" classname="admin/admin-dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Dashboard › should have vendor management section" classname="admin/admin-dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Dashboard › should display analytics/statistics" classname="admin/admin-dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin User Management › should list all users" classname="admin/admin-dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin User Management › should allow user search" classname="admin/admin-dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="auth/login.spec.ts" timestamp="2025-08-01T13:32:21.943Z" hostname="webkit" tests="9" failures="0" skipped="9" time="0" errors="0">
<testcase name="User Login › should display login form" classname="auth/login.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Login › should show validation errors for empty fields" classname="auth/login.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Login › should show error for invalid credentials" classname="auth/login.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Login › should successfully login with valid credentials" classname="auth/login.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Login › should redirect to previous page after login" classname="auth/login.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Login › should show/hide password toggle" classname="auth/login.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Login › should have Google login option" classname="auth/login.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Login › should have forgot password link" classname="auth/login.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Login › should have signup link" classname="auth/login.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="auth/signup.spec.ts" timestamp="2025-08-01T13:32:21.943Z" hostname="webkit" tests="9" failures="0" skipped="9" time="0" errors="0">
<testcase name="User Signup › should display signup form" classname="auth/signup.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Signup › should show validation errors for empty required fields" classname="auth/signup.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Signup › should show error for invalid email format" classname="auth/signup.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Signup › should show error for weak password" classname="auth/signup.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Signup › should successfully register new user" classname="auth/signup.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Signup › should show error for existing email" classname="auth/signup.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Signup › should have Google signup option" classname="auth/signup.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Signup › should have login link for existing users" classname="auth/signup.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Signup › should handle terms and conditions checkbox" classname="auth/signup.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="booking/venue-booking.spec.ts" timestamp="2025-08-01T13:32:21.943Z" hostname="webkit" tests="10" failures="0" skipped="10" time="0" errors="0">
<testcase name="Venue Booking › should redirect to login when not authenticated" classname="booking/venue-booking.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Venue Booking › should display venue booking form when authenticated" classname="booking/venue-booking.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Venue Booking › should show validation errors for empty required fields" classname="booking/venue-booking.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Venue Booking › should show error for past date selection" classname="booking/venue-booking.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Venue Booking › should check availability for selected date" classname="booking/venue-booking.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Venue Booking › should successfully submit booking with valid data" classname="booking/venue-booking.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Venue Booking › should display venue information on booking page" classname="booking/venue-booking.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Venue Booking › should handle guest count validation" classname="booking/venue-booking.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Venue Booking › should allow cancellation of booking process" classname="booking/venue-booking.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Venue Booking › should show booking summary before confirmation" classname="booking/venue-booking.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="ecommerce/shopping-cart.spec.ts" timestamp="2025-08-01T13:32:21.943Z" hostname="webkit" tests="12" failures="0" skipped="12" time="0" errors="0">
<testcase name="Shopping Cart › should display shop page with products" classname="ecommerce/shopping-cart.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Shopping Cart › should add product to cart" classname="ecommerce/shopping-cart.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Shopping Cart › should display cart items" classname="ecommerce/shopping-cart.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Shopping Cart › should update product quantity in cart" classname="ecommerce/shopping-cart.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Shopping Cart › should remove product from cart" classname="ecommerce/shopping-cart.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Shopping Cart › should calculate total price correctly" classname="ecommerce/shopping-cart.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Shopping Cart › should clear entire cart" classname="ecommerce/shopping-cart.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Shopping Cart › should show empty cart message when no items" classname="ecommerce/shopping-cart.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Shopping Cart › should persist cart items across page navigation" classname="ecommerce/shopping-cart.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Shopping Cart › should show product details in cart" classname="ecommerce/shopping-cart.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Shopping Cart › should handle quantity limits" classname="ecommerce/shopping-cart.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Shopping Cart › should show continue shopping option" classname="ecommerce/shopping-cart.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="admin/admin-dashboard.spec.ts" timestamp="2025-08-01T13:32:21.943Z" hostname="Mobile Chrome" tests="6" failures="0" skipped="6" time="0" errors="0">
<testcase name="Admin Dashboard › should display admin dashboard" classname="admin/admin-dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Dashboard › should have user management section" classname="admin/admin-dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Dashboard › should have vendor management section" classname="admin/admin-dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Dashboard › should display analytics/statistics" classname="admin/admin-dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin User Management › should list all users" classname="admin/admin-dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin User Management › should allow user search" classname="admin/admin-dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="auth/login.spec.ts" timestamp="2025-08-01T13:32:21.943Z" hostname="Mobile Chrome" tests="9" failures="0" skipped="9" time="0" errors="0">
<testcase name="User Login › should display login form" classname="auth/login.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Login › should show validation errors for empty fields" classname="auth/login.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Login › should show error for invalid credentials" classname="auth/login.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Login › should successfully login with valid credentials" classname="auth/login.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Login › should redirect to previous page after login" classname="auth/login.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Login › should show/hide password toggle" classname="auth/login.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Login › should have Google login option" classname="auth/login.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Login › should have forgot password link" classname="auth/login.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Login › should have signup link" classname="auth/login.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="auth/signup.spec.ts" timestamp="2025-08-01T13:32:21.943Z" hostname="Mobile Chrome" tests="9" failures="0" skipped="9" time="0" errors="0">
<testcase name="User Signup › should display signup form" classname="auth/signup.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Signup › should show validation errors for empty required fields" classname="auth/signup.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Signup › should show error for invalid email format" classname="auth/signup.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Signup › should show error for weak password" classname="auth/signup.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Signup › should successfully register new user" classname="auth/signup.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Signup › should show error for existing email" classname="auth/signup.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Signup › should have Google signup option" classname="auth/signup.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Signup › should have login link for existing users" classname="auth/signup.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Signup › should handle terms and conditions checkbox" classname="auth/signup.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="booking/venue-booking.spec.ts" timestamp="2025-08-01T13:32:21.943Z" hostname="Mobile Chrome" tests="10" failures="0" skipped="10" time="0" errors="0">
<testcase name="Venue Booking › should redirect to login when not authenticated" classname="booking/venue-booking.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Venue Booking › should display venue booking form when authenticated" classname="booking/venue-booking.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Venue Booking › should show validation errors for empty required fields" classname="booking/venue-booking.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Venue Booking › should show error for past date selection" classname="booking/venue-booking.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Venue Booking › should check availability for selected date" classname="booking/venue-booking.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Venue Booking › should successfully submit booking with valid data" classname="booking/venue-booking.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Venue Booking › should display venue information on booking page" classname="booking/venue-booking.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Venue Booking › should handle guest count validation" classname="booking/venue-booking.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Venue Booking › should allow cancellation of booking process" classname="booking/venue-booking.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Venue Booking › should show booking summary before confirmation" classname="booking/venue-booking.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="ecommerce/shopping-cart.spec.ts" timestamp="2025-08-01T13:32:21.943Z" hostname="Mobile Chrome" tests="12" failures="0" skipped="12" time="0" errors="0">
<testcase name="Shopping Cart › should display shop page with products" classname="ecommerce/shopping-cart.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Shopping Cart › should add product to cart" classname="ecommerce/shopping-cart.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Shopping Cart › should display cart items" classname="ecommerce/shopping-cart.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Shopping Cart › should update product quantity in cart" classname="ecommerce/shopping-cart.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Shopping Cart › should remove product from cart" classname="ecommerce/shopping-cart.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Shopping Cart › should calculate total price correctly" classname="ecommerce/shopping-cart.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Shopping Cart › should clear entire cart" classname="ecommerce/shopping-cart.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Shopping Cart › should show empty cart message when no items" classname="ecommerce/shopping-cart.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Shopping Cart › should persist cart items across page navigation" classname="ecommerce/shopping-cart.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Shopping Cart › should show product details in cart" classname="ecommerce/shopping-cart.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Shopping Cart › should handle quantity limits" classname="ecommerce/shopping-cart.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Shopping Cart › should show continue shopping option" classname="ecommerce/shopping-cart.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="admin/admin-dashboard.spec.ts" timestamp="2025-08-01T13:32:21.943Z" hostname="Mobile Safari" tests="6" failures="0" skipped="6" time="0" errors="0">
<testcase name="Admin Dashboard › should display admin dashboard" classname="admin/admin-dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Dashboard › should have user management section" classname="admin/admin-dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Dashboard › should have vendor management section" classname="admin/admin-dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Dashboard › should display analytics/statistics" classname="admin/admin-dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin User Management › should list all users" classname="admin/admin-dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin User Management › should allow user search" classname="admin/admin-dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="auth/login.spec.ts" timestamp="2025-08-01T13:32:21.943Z" hostname="Mobile Safari" tests="9" failures="0" skipped="9" time="0" errors="0">
<testcase name="User Login › should display login form" classname="auth/login.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Login › should show validation errors for empty fields" classname="auth/login.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Login › should show error for invalid credentials" classname="auth/login.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Login › should successfully login with valid credentials" classname="auth/login.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Login › should redirect to previous page after login" classname="auth/login.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Login › should show/hide password toggle" classname="auth/login.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Login › should have Google login option" classname="auth/login.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Login › should have forgot password link" classname="auth/login.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Login › should have signup link" classname="auth/login.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="auth/signup.spec.ts" timestamp="2025-08-01T13:32:21.943Z" hostname="Mobile Safari" tests="9" failures="0" skipped="9" time="0" errors="0">
<testcase name="User Signup › should display signup form" classname="auth/signup.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Signup › should show validation errors for empty required fields" classname="auth/signup.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Signup › should show error for invalid email format" classname="auth/signup.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Signup › should show error for weak password" classname="auth/signup.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Signup › should successfully register new user" classname="auth/signup.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Signup › should show error for existing email" classname="auth/signup.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Signup › should have Google signup option" classname="auth/signup.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Signup › should have login link for existing users" classname="auth/signup.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Signup › should handle terms and conditions checkbox" classname="auth/signup.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="booking/venue-booking.spec.ts" timestamp="2025-08-01T13:32:21.943Z" hostname="Mobile Safari" tests="10" failures="0" skipped="10" time="0" errors="0">
<testcase name="Venue Booking › should redirect to login when not authenticated" classname="booking/venue-booking.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Venue Booking › should display venue booking form when authenticated" classname="booking/venue-booking.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Venue Booking › should show validation errors for empty required fields" classname="booking/venue-booking.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Venue Booking › should show error for past date selection" classname="booking/venue-booking.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Venue Booking › should check availability for selected date" classname="booking/venue-booking.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Venue Booking › should successfully submit booking with valid data" classname="booking/venue-booking.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Venue Booking › should display venue information on booking page" classname="booking/venue-booking.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Venue Booking › should handle guest count validation" classname="booking/venue-booking.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Venue Booking › should allow cancellation of booking process" classname="booking/venue-booking.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Venue Booking › should show booking summary before confirmation" classname="booking/venue-booking.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="ecommerce/shopping-cart.spec.ts" timestamp="2025-08-01T13:32:21.943Z" hostname="Mobile Safari" tests="12" failures="0" skipped="12" time="0" errors="0">
<testcase name="Shopping Cart › should display shop page with products" classname="ecommerce/shopping-cart.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Shopping Cart › should add product to cart" classname="ecommerce/shopping-cart.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Shopping Cart › should display cart items" classname="ecommerce/shopping-cart.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Shopping Cart › should update product quantity in cart" classname="ecommerce/shopping-cart.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Shopping Cart › should remove product from cart" classname="ecommerce/shopping-cart.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Shopping Cart › should calculate total price correctly" classname="ecommerce/shopping-cart.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Shopping Cart › should clear entire cart" classname="ecommerce/shopping-cart.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Shopping Cart › should show empty cart message when no items" classname="ecommerce/shopping-cart.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Shopping Cart › should persist cart items across page navigation" classname="ecommerce/shopping-cart.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Shopping Cart › should show product details in cart" classname="ecommerce/shopping-cart.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Shopping Cart › should handle quantity limits" classname="ecommerce/shopping-cart.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Shopping Cart › should show continue shopping option" classname="ecommerce/shopping-cart.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="admin/admin-dashboard.spec.ts" timestamp="2025-08-01T13:32:21.943Z" hostname="Tablet Chrome" tests="6" failures="0" skipped="6" time="0" errors="0">
<testcase name="Admin Dashboard › should display admin dashboard" classname="admin/admin-dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Dashboard › should have user management section" classname="admin/admin-dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Dashboard › should have vendor management section" classname="admin/admin-dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Dashboard › should display analytics/statistics" classname="admin/admin-dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin User Management › should list all users" classname="admin/admin-dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin User Management › should allow user search" classname="admin/admin-dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="auth/login.spec.ts" timestamp="2025-08-01T13:32:21.943Z" hostname="Tablet Chrome" tests="9" failures="0" skipped="9" time="0" errors="0">
<testcase name="User Login › should display login form" classname="auth/login.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Login › should show validation errors for empty fields" classname="auth/login.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Login › should show error for invalid credentials" classname="auth/login.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Login › should successfully login with valid credentials" classname="auth/login.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Login › should redirect to previous page after login" classname="auth/login.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Login › should show/hide password toggle" classname="auth/login.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Login › should have Google login option" classname="auth/login.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Login › should have forgot password link" classname="auth/login.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Login › should have signup link" classname="auth/login.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="auth/signup.spec.ts" timestamp="2025-08-01T13:32:21.943Z" hostname="Tablet Chrome" tests="9" failures="0" skipped="9" time="0" errors="0">
<testcase name="User Signup › should display signup form" classname="auth/signup.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Signup › should show validation errors for empty required fields" classname="auth/signup.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Signup › should show error for invalid email format" classname="auth/signup.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Signup › should show error for weak password" classname="auth/signup.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Signup › should successfully register new user" classname="auth/signup.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Signup › should show error for existing email" classname="auth/signup.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Signup › should have Google signup option" classname="auth/signup.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Signup › should have login link for existing users" classname="auth/signup.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Signup › should handle terms and conditions checkbox" classname="auth/signup.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="booking/venue-booking.spec.ts" timestamp="2025-08-01T13:32:21.943Z" hostname="Tablet Chrome" tests="10" failures="0" skipped="10" time="0" errors="0">
<testcase name="Venue Booking › should redirect to login when not authenticated" classname="booking/venue-booking.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Venue Booking › should display venue booking form when authenticated" classname="booking/venue-booking.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Venue Booking › should show validation errors for empty required fields" classname="booking/venue-booking.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Venue Booking › should show error for past date selection" classname="booking/venue-booking.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Venue Booking › should check availability for selected date" classname="booking/venue-booking.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Venue Booking › should successfully submit booking with valid data" classname="booking/venue-booking.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Venue Booking › should display venue information on booking page" classname="booking/venue-booking.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Venue Booking › should handle guest count validation" classname="booking/venue-booking.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Venue Booking › should allow cancellation of booking process" classname="booking/venue-booking.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Venue Booking › should show booking summary before confirmation" classname="booking/venue-booking.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="ecommerce/shopping-cart.spec.ts" timestamp="2025-08-01T13:32:21.943Z" hostname="Tablet Chrome" tests="12" failures="0" skipped="12" time="0" errors="0">
<testcase name="Shopping Cart › should display shop page with products" classname="ecommerce/shopping-cart.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Shopping Cart › should add product to cart" classname="ecommerce/shopping-cart.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Shopping Cart › should display cart items" classname="ecommerce/shopping-cart.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Shopping Cart › should update product quantity in cart" classname="ecommerce/shopping-cart.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Shopping Cart › should remove product from cart" classname="ecommerce/shopping-cart.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Shopping Cart › should calculate total price correctly" classname="ecommerce/shopping-cart.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Shopping Cart › should clear entire cart" classname="ecommerce/shopping-cart.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Shopping Cart › should show empty cart message when no items" classname="ecommerce/shopping-cart.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Shopping Cart › should persist cart items across page navigation" classname="ecommerce/shopping-cart.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Shopping Cart › should show product details in cart" classname="ecommerce/shopping-cart.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Shopping Cart › should handle quantity limits" classname="ecommerce/shopping-cart.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Shopping Cart › should show continue shopping option" classname="ecommerce/shopping-cart.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="admin/admin-dashboard.spec.ts" timestamp="2025-08-01T13:32:21.943Z" hostname="Desktop Large" tests="6" failures="0" skipped="6" time="0" errors="0">
<testcase name="Admin Dashboard › should display admin dashboard" classname="admin/admin-dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Dashboard › should have user management section" classname="admin/admin-dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Dashboard › should have vendor management section" classname="admin/admin-dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Dashboard › should display analytics/statistics" classname="admin/admin-dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin User Management › should list all users" classname="admin/admin-dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin User Management › should allow user search" classname="admin/admin-dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="auth/login.spec.ts" timestamp="2025-08-01T13:32:21.943Z" hostname="Desktop Large" tests="9" failures="0" skipped="9" time="0" errors="0">
<testcase name="User Login › should display login form" classname="auth/login.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Login › should show validation errors for empty fields" classname="auth/login.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Login › should show error for invalid credentials" classname="auth/login.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Login › should successfully login with valid credentials" classname="auth/login.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Login › should redirect to previous page after login" classname="auth/login.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Login › should show/hide password toggle" classname="auth/login.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Login › should have Google login option" classname="auth/login.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Login › should have forgot password link" classname="auth/login.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Login › should have signup link" classname="auth/login.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="auth/signup.spec.ts" timestamp="2025-08-01T13:32:21.943Z" hostname="Desktop Large" tests="9" failures="0" skipped="9" time="0" errors="0">
<testcase name="User Signup › should display signup form" classname="auth/signup.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Signup › should show validation errors for empty required fields" classname="auth/signup.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Signup › should show error for invalid email format" classname="auth/signup.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Signup › should show error for weak password" classname="auth/signup.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Signup › should successfully register new user" classname="auth/signup.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Signup › should show error for existing email" classname="auth/signup.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Signup › should have Google signup option" classname="auth/signup.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Signup › should have login link for existing users" classname="auth/signup.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Signup › should handle terms and conditions checkbox" classname="auth/signup.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="booking/venue-booking.spec.ts" timestamp="2025-08-01T13:32:21.943Z" hostname="Desktop Large" tests="10" failures="0" skipped="10" time="0" errors="0">
<testcase name="Venue Booking › should redirect to login when not authenticated" classname="booking/venue-booking.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Venue Booking › should display venue booking form when authenticated" classname="booking/venue-booking.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Venue Booking › should show validation errors for empty required fields" classname="booking/venue-booking.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Venue Booking › should show error for past date selection" classname="booking/venue-booking.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Venue Booking › should check availability for selected date" classname="booking/venue-booking.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Venue Booking › should successfully submit booking with valid data" classname="booking/venue-booking.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Venue Booking › should display venue information on booking page" classname="booking/venue-booking.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Venue Booking › should handle guest count validation" classname="booking/venue-booking.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Venue Booking › should allow cancellation of booking process" classname="booking/venue-booking.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Venue Booking › should show booking summary before confirmation" classname="booking/venue-booking.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="ecommerce/shopping-cart.spec.ts" timestamp="2025-08-01T13:32:21.943Z" hostname="Desktop Large" tests="12" failures="0" skipped="12" time="0" errors="0">
<testcase name="Shopping Cart › should display shop page with products" classname="ecommerce/shopping-cart.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Shopping Cart › should add product to cart" classname="ecommerce/shopping-cart.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Shopping Cart › should display cart items" classname="ecommerce/shopping-cart.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Shopping Cart › should update product quantity in cart" classname="ecommerce/shopping-cart.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Shopping Cart › should remove product from cart" classname="ecommerce/shopping-cart.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Shopping Cart › should calculate total price correctly" classname="ecommerce/shopping-cart.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Shopping Cart › should clear entire cart" classname="ecommerce/shopping-cart.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Shopping Cart › should show empty cart message when no items" classname="ecommerce/shopping-cart.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Shopping Cart › should persist cart items across page navigation" classname="ecommerce/shopping-cart.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Shopping Cart › should show product details in cart" classname="ecommerce/shopping-cart.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Shopping Cart › should handle quantity limits" classname="ecommerce/shopping-cart.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Shopping Cart › should show continue shopping option" classname="ecommerce/shopping-cart.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="admin/admin-dashboard.spec.ts" timestamp="2025-08-01T13:32:21.943Z" hostname="Desktop Small" tests="6" failures="0" skipped="6" time="0" errors="0">
<testcase name="Admin Dashboard › should display admin dashboard" classname="admin/admin-dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Dashboard › should have user management section" classname="admin/admin-dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Dashboard › should have vendor management section" classname="admin/admin-dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin Dashboard › should display analytics/statistics" classname="admin/admin-dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin User Management › should list all users" classname="admin/admin-dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Admin User Management › should allow user search" classname="admin/admin-dashboard.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="auth/login.spec.ts" timestamp="2025-08-01T13:32:21.943Z" hostname="Desktop Small" tests="9" failures="0" skipped="9" time="0" errors="0">
<testcase name="User Login › should display login form" classname="auth/login.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Login › should show validation errors for empty fields" classname="auth/login.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Login › should show error for invalid credentials" classname="auth/login.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Login › should successfully login with valid credentials" classname="auth/login.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Login › should redirect to previous page after login" classname="auth/login.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Login › should show/hide password toggle" classname="auth/login.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Login › should have Google login option" classname="auth/login.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Login › should have forgot password link" classname="auth/login.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Login › should have signup link" classname="auth/login.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="auth/signup.spec.ts" timestamp="2025-08-01T13:32:21.943Z" hostname="Desktop Small" tests="9" failures="0" skipped="9" time="0" errors="0">
<testcase name="User Signup › should display signup form" classname="auth/signup.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Signup › should show validation errors for empty required fields" classname="auth/signup.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Signup › should show error for invalid email format" classname="auth/signup.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Signup › should show error for weak password" classname="auth/signup.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Signup › should successfully register new user" classname="auth/signup.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Signup › should show error for existing email" classname="auth/signup.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Signup › should have Google signup option" classname="auth/signup.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Signup › should have login link for existing users" classname="auth/signup.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="User Signup › should handle terms and conditions checkbox" classname="auth/signup.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="booking/venue-booking.spec.ts" timestamp="2025-08-01T13:32:21.943Z" hostname="Desktop Small" tests="10" failures="0" skipped="10" time="0" errors="0">
<testcase name="Venue Booking › should redirect to login when not authenticated" classname="booking/venue-booking.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Venue Booking › should display venue booking form when authenticated" classname="booking/venue-booking.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Venue Booking › should show validation errors for empty required fields" classname="booking/venue-booking.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Venue Booking › should show error for past date selection" classname="booking/venue-booking.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Venue Booking › should check availability for selected date" classname="booking/venue-booking.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Venue Booking › should successfully submit booking with valid data" classname="booking/venue-booking.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Venue Booking › should display venue information on booking page" classname="booking/venue-booking.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Venue Booking › should handle guest count validation" classname="booking/venue-booking.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Venue Booking › should allow cancellation of booking process" classname="booking/venue-booking.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Venue Booking › should show booking summary before confirmation" classname="booking/venue-booking.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="ecommerce/shopping-cart.spec.ts" timestamp="2025-08-01T13:32:21.943Z" hostname="Desktop Small" tests="12" failures="0" skipped="12" time="0" errors="0">
<testcase name="Shopping Cart › should display shop page with products" classname="ecommerce/shopping-cart.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Shopping Cart › should add product to cart" classname="ecommerce/shopping-cart.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Shopping Cart › should display cart items" classname="ecommerce/shopping-cart.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Shopping Cart › should update product quantity in cart" classname="ecommerce/shopping-cart.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Shopping Cart › should remove product from cart" classname="ecommerce/shopping-cart.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Shopping Cart › should calculate total price correctly" classname="ecommerce/shopping-cart.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Shopping Cart › should clear entire cart" classname="ecommerce/shopping-cart.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Shopping Cart › should show empty cart message when no items" classname="ecommerce/shopping-cart.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Shopping Cart › should persist cart items across page navigation" classname="ecommerce/shopping-cart.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Shopping Cart › should show product details in cart" classname="ecommerce/shopping-cart.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Shopping Cart › should handle quantity limits" classname="ecommerce/shopping-cart.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="Shopping Cart › should show continue shopping option" classname="ecommerce/shopping-cart.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
</testsuites>