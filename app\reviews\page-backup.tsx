"use client"

import { useState } from 'react'
import { TopHeader } from '@/components/top-header'
import { Header } from '@/components/header'

export default function ReviewsPageBackup() {
  const [activeTab, setActiveTab] = useState<'read' | 'write'>('read')

  const renderStars = (rating: number) => {
    return (
      <div className="flex space-x-1">
        {[1, 2, 3, 4, 5].map((star) => (
          <svg
            key={star}
            className={`w-5 h-5 ${star <= rating ? 'text-yellow-400' : 'text-gray-300'}`}
            fill="currentColor"
            viewBox="0 0 20 20"
          >
            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
          </svg>
        ))}
      </div>
    );
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <TopHeader />
      <Header />
      
      <div className="container mx-auto px-4 py-8">
        <h1 className="text-3xl font-bold mb-6">Reviews</h1>
        
        <div className="bg-white rounded-lg shadow p-6">
          <p>This is a simplified reviews page to test the basic structure.</p>
          
          <div className="mt-4">
            <h3 className="text-lg font-semibold mb-2">Star Rating Test:</h3>
            {renderStars(4)}
          </div>
          
          <div className="mt-4">
            <button 
              onClick={() => setActiveTab(activeTab === 'read' ? 'write' : 'read')}
              className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
            >
              Toggle Tab: {activeTab}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
