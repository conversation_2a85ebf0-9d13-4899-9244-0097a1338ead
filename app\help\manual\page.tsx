"use client"

import { <PERSON>Header } from "@/components/top-header"
import { <PERSON><PERSON> } from "@/components/header"
import { Foot<PERSON> } from "@/components/footer"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { SimpleSEO } from "@/components/seo/SimpleSEO"
import { 
  ArrowLeft,
  FileText,
  User,
  Search,
  ShoppingBag,
  Calendar,
  Star,
  Settings,
  Shield,
  CreditCard,
  Phone,
  Mail,
  Download,
  BookOpen,
  CheckCircle,
  ArrowRight,
  Users,
  MapPin,
  Camera,
  Heart
} from "lucide-react"
import Link from "next/link"
import { useHelpTranslations } from '@/lib/universal-i18n'

export default function UserManualPage() {
  const helpT = useHelpTranslations()
  const manualSections = [
    {
      icon: User,
      title: "Getting Started",
      description: "Account creation, profile setup, and basic navigation",
      topics: [
        "Creating your account",
        "Email verification and login",
        "Setting up your profile",
        "Understanding the dashboard",
        "Basic navigation tips"
      ],
      link: "/help/getting-started"
    },
    {
      icon: Search,
      title: "Finding Vendors",
      description: "How to search, filter, and connect with wedding vendors",
      topics: [
        "Using search and filters",
        "Reading vendor profiles",
        "Comparing vendors",
        "Contacting vendors",
        "Booking vendor services"
      ],
      link: "/help/vendors"
    },
    {
      icon: MapPin,
      title: "Booking Venues",
      description: "Complete guide to finding and booking wedding venues",
      topics: [
        "Venue types and categories",
        "Availability checking",
        "Site visits and evaluation",
        "Booking process",
        "Payment and contracts"
      ],
      link: "/help/venues"
    },
    {
      icon: ShoppingBag,
      title: "Shopping Guide",
      description: "How to shop for wedding products and manage orders",
      topics: [
        "Browsing product categories",
        "Adding items to cart",
        "Checkout process",
        "Payment methods",
        "Order tracking and returns"
      ],
      link: "/help/shopping"
    },
    {
      icon: Calendar,
      title: "Wedding Planning",
      description: "Tools and tips for planning your perfect wedding",
      topics: [
        "Wedding timeline creation",
        "Budget planning and tracking",
        "Guest list management",
        "Vendor coordination",
        "Day-of planning"
      ],
      link: "/help/planning"
    },
    {
      icon: Star,
      title: "Reviews & Ratings",
      description: "How to write and manage reviews for vendors and venues",
      topics: [
        "Writing helpful reviews",
        "Rating guidelines",
        "Managing your reviews",
        "Reporting inappropriate content",
        "Vendor responses"
      ],
      link: "/help/reviews"
    }
  ]

  const quickReference = [
    {
      category: "Account Management",
      items: [
        { action: "Change Password", path: "/dashboard/settings" },
        { action: "Update Profile", path: "/dashboard/profile" },
        { action: "Manage Preferences", path: "/dashboard/settings" },
        { action: "View Order History", path: "/dashboard/orders" }
      ]
    },
    {
      category: "Booking & Orders",
      items: [
        { action: "Track Bookings", path: "/dashboard/bookings" },
        { action: "View Invoices", path: "/dashboard/invoices" },
        { action: "Cancel Booking", path: "/dashboard/bookings" },
        { action: "Request Refund", path: "/contact" }
      ]
    },
    {
      category: "Communication",
      items: [
        { action: "Contact Vendor", path: "/vendors" },
        { action: "Message Support", path: "/contact" },
        { action: "Live Chat", path: "#" },
        { action: "Submit Feedback", path: "/contact" }
      ]
    },
    {
      category: "Planning Tools",
      items: [
        { action: "Wedding Checklist", path: "/dashboard/planning" },
        { action: "Budget Tracker", path: "/dashboard/planning" },
        { action: "Guest List", path: "/dashboard/planning" },
        { action: "Timeline Creator", path: "/dashboard/planning" }
      ]
    }
  ]

  const troubleshooting = [
    {
      problem: "Can't log in to my account",
      solutions: [
        "Check if your email and password are correct",
        "Try resetting your password",
        "Clear browser cache and cookies",
        "Contact support if issue persists"
      ]
    },
    {
      problem: "Payment failed during checkout",
      solutions: [
        "Check if your card details are correct",
        "Ensure sufficient balance in your account",
        "Try a different payment method",
        "Contact your bank if needed"
      ]
    },
    {
      problem: "Vendor not responding to messages",
      solutions: [
        "Wait 24-48 hours for response",
        "Try calling the vendor directly",
        "Check if the vendor is currently active",
        "Contact support for assistance"
      ]
    },
    {
      problem: "Order not delivered on time",
      solutions: [
        "Check order tracking status",
        "Contact the seller directly",
        "Review delivery timeline in order details",
        "Report the issue to customer support"
      ]
    }
  ]

  const features = [
    {
      feature: "Smart Search",
      description: "Advanced search with filters for location, budget, and preferences"
    },
    {
      feature: "Vendor Comparison",
      description: "Compare multiple vendors side by side to make informed decisions"
    },
    {
      feature: "Real-time Availability",
      description: "Check vendor and venue availability in real-time"
    },
    {
      feature: "Secure Payments",
      description: "Multiple payment options with bank-level security"
    },
    {
      feature: "Planning Tools",
      description: "Comprehensive tools for budget, timeline, and guest management"
    },
    {
      feature: "Mobile App",
      description: "Access all features on-the-go with our mobile application"
    },
    {
      feature: "24/7 Support",
      description: "Round-the-clock customer support via chat, email, and phone"
    },
    {
      feature: "Review System",
      description: "Verified reviews and ratings from real customers"
    }
  ]

  const supportChannels = [
    {
      icon: Phone,
      title: "Phone Support",
      description: "Call us for immediate assistance",
      contact: "+91 **********",
      hours: "9 AM - 9 PM (Mon-Sun)"
    },
    {
      icon: Mail,
      title: "Email Support",
      description: "Send us your queries via email",
      contact: "<EMAIL>",
      hours: "Response within 24 hours"
    },
    {
      icon: Users,
      title: "Live Chat",
      description: "Chat with our support team",
      contact: "Available on website",
      hours: "9 AM - 9 PM (Mon-Sun)"
    },
    {
      icon: BookOpen,
      title: "Help Center",
      description: "Browse our comprehensive guides",
      contact: "help.BookmyFestive.com",
      hours: "Available 24/7"
    }
  ]

  return (
    <>
      <SimpleSEO
        title="User Manual - BookmyFestive Help"
        description="Complete user manual for BookmyFestive. Learn how to use all features, troubleshoot issues, and get the most out of our wedding planning platform."
        keywords="user manual, help guide, wedding platform guide, BookmyFestive manual"
        url="/help/manual"
      />
      
      <div className="min-h-screen bg-background">
        <TopHeader />
        <Header />

        {/* Header */}
        <section className="py-12">
          <div className="container mx-auto px-4">
            <Link href="/help" className="inline-flex items-center gap-2 text-primary hover:underline mb-6">
              <ArrowLeft className="w-4 h-4" />
              Back to Help Center
            </Link>
            
            <div className="text-center mb-12">
              <FileText className="w-16 h-16 mx-auto mb-6 text-primary" />
              <h1 className="text-4xl font-bold mb-4">{helpT.manual?.title || 'User Manual'}</h1>
              <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
                {helpT.manual?.subtitle || 'Complete documentation for using BookmyFestive. Find detailed guides, quick references, and troubleshooting tips for all platform features.'}
              </p>
            </div>
          </div>
        </section>

        {/* Manual Sections */}
        <section className="py-16">
          <div className="container mx-auto px-4">
            <h2 className="text-3xl font-bold text-center mb-12">Manual Sections</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {manualSections.map((section, index) => (
                <Card key={index} className="hover:shadow-lg transition-shadow">
                  <CardHeader>
                    <div className="flex items-center gap-3 mb-4">
                      <section.icon className="w-8 h-8 text-primary" />
                      <div>
                        <CardTitle className="text-lg">{section.title}</CardTitle>
                        <p className="text-sm text-muted-foreground">{section.description}</p>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <ul className="space-y-2 mb-4">
                      {section.topics.map((topic, idx) => (
                        <li key={idx} className="flex items-start gap-2">
                          <CheckCircle className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                          <span className="text-sm">{topic}</span>
                        </li>
                      ))}
                    </ul>
                    <Link href={section.link}>
                      <Button className="w-full">
                        Read Guide
                        <ArrowRight className="w-4 h-4 ml-2" />
                      </Button>
                    </Link>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Quick Reference */}
        <section className="py-16 bg-muted/30">
          <div className="container mx-auto px-4">
            <h2 className="text-3xl font-bold text-center mb-12">Quick Reference</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {quickReference.map((category, index) => (
                <Card key={index}>
                  <CardHeader>
                    <CardTitle className="text-lg text-primary">{category.category}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ul className="space-y-3">
                      {category.items.map((item, idx) => (
                        <li key={idx}>
                          <Link 
                            href={item.path} 
                            className="text-sm hover:text-primary hover:underline flex items-center gap-2"
                          >
                            <ArrowRight className="w-3 h-3" />
                            {item.action}
                          </Link>
                        </li>
                      ))}
                    </ul>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Platform Features */}
        <section className="py-16">
          <div className="container mx-auto px-4">
            <h2 className="text-3xl font-bold text-center mb-12">Platform Features</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {features.map((feature, index) => (
                <Card key={index} className="text-center">
                  <CardContent className="pt-6">
                    <h3 className="font-semibold mb-2">{feature.feature}</h3>
                    <p className="text-sm text-muted-foreground">{feature.description}</p>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Troubleshooting */}
        <section className="py-16 bg-muted/30">
          <div className="container mx-auto px-4">
            <h2 className="text-3xl font-bold text-center mb-12">Common Issues & Solutions</h2>
            <div className="max-w-4xl mx-auto space-y-6">
              {troubleshooting.map((issue, index) => (
                <Card key={index}>
                  <CardHeader>
                    <CardTitle className="text-lg text-red-600">{issue.problem}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ul className="space-y-2">
                      {issue.solutions.map((solution, idx) => (
                        <li key={idx} className="flex items-start gap-2">
                          <CheckCircle className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                          <span className="text-sm">{solution}</span>
                        </li>
                      ))}
                    </ul>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Support Channels */}
        <section className="py-16">
          <div className="container mx-auto px-4">
            <h2 className="text-3xl font-bold text-center mb-12">Get Support</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {supportChannels.map((channel, index) => (
                <Card key={index} className="text-center hover:shadow-lg transition-shadow">
                  <CardHeader>
                    <channel.icon className="w-12 h-12 mx-auto mb-4 text-primary" />
                    <CardTitle className="text-lg">{channel.title}</CardTitle>
                    <p className="text-sm text-muted-foreground">{channel.description}</p>
                  </CardHeader>
                  <CardContent>
                    <p className="font-medium mb-2">{channel.contact}</p>
                    <Badge variant="outline">{channel.hours}</Badge>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Download Section */}
        <section className="py-16 bg-muted/30">
          <div className="container mx-auto px-4 text-center">
            <h2 className="text-3xl font-bold mb-8">Download Resources</h2>
            <p className="text-muted-foreground mb-8 max-w-2xl mx-auto">
              Download our comprehensive guides and checklists to help you plan your perfect wedding.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" className="px-8">
                <Download className="w-5 h-5 mr-2" />
                Wedding Planning Guide (PDF)
              </Button>
              <Button variant="outline" size="lg" className="px-8">
                <Download className="w-5 h-5 mr-2" />
                Vendor Checklist (PDF)
              </Button>
              <Button variant="outline" size="lg" className="px-8">
                <Download className="w-5 h-5 mr-2" />
                Budget Template (Excel)
              </Button>
            </div>
          </div>
        </section>

        <Footer />
      </div>
    </>
  )
}
