import { generateClient } from 'aws-amplify/api';
import { getCurrentUser } from 'aws-amplify/auth';
import { createReview, updateReview } from '@/src/graphql/mutations';
import { reviewsByEntityId, getReview, listReviews } from '@/src/graphql/queries';
import { CreateReviewInput, ReviewCategory, ReviewStatus, ReviewTarget, EntityType } from '@/src/graphql';

// Use API key for admin operations to bypass user-level restrictions
const adminClient = generateClient({
  authMode: 'apiKey'
});

const client = generateClient();

export interface ReviewData {
  name?: string;
  email?: string;
  location?: string;
  weddingDate?: string;
  category?: string;
  rating: number;
  title: string;
  review: string;
  wouldRecommend?: boolean;
  entityType: string;
  entityId: string;
  serviceRating?: number;
  valueRating?: number;
  communicationRating?: number;
  professionalismRating?: number;
  images?: string[];
  // New review routing fields
  reviewTarget?: 'ADMIN' | 'VENDOR';
  adminNotes?: string;
  moderatedBy?: string;
  moderatedAt?: string;
}

export interface ReviewStats {
  totalReviews: number;
  averageRating: number;
  ratingDistribution: { [key: number]: number };
  recommendationRate: number;
}

export interface ServiceResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
}

export class EntityReviewService {

  /**
   * Get reviews for admin dashboard
   */
  static async getAdminReviews(options: {
    reviewTarget?: 'ADMIN' | 'VENDOR';
    entityType?: string;
    statusFilter?: string;
    sortBy?: string;
    limit?: number;
  } = {}): Promise<ServiceResponse<any[]>> {
    try {
      const { reviewTarget, entityType, statusFilter, sortBy = 'newest', limit = 50 } = options;

      // Build filter conditions
      const filter: any = {};

      // Don't filter by reviewTarget in GraphQL - we'll filter after fetching
      // This avoids the schema mismatch issue

      // Add entityType filter if specified
      if (entityType && entityType !== 'ALL') {
        filter.entityType = { eq: entityType };
      }

      // Add status filter if specified
      if (statusFilter && statusFilter !== 'ALL') {
        filter.status = { eq: statusFilter };
      }

      console.log('Filter being sent:', JSON.stringify(filter, null, 2));

      const result = await adminClient.graphql({
        query: listReviews,
        variables: {
          filter,
          limit
        }
      });

      console.log('Admin reviews query result:', result.data?.listReviews);
      console.log('Reviews count:', result.data?.listReviews?.items?.length || 0);

      // Filter by reviewTarget after fetching (since it's not supported in GraphQL filter)
      let filteredItems = result.data.listReviews.items || [];
      if (reviewTarget) {
        filteredItems = filteredItems.filter((item: any) => item.reviewTarget === reviewTarget);
        console.log('Filtered by reviewTarget:', reviewTarget, 'Count:', filteredItems.length);
      }

      return {
        success: true,
        data: filteredItems,
        nextToken: result.data.listReviews.nextToken
      };

    } catch (error) {
      console.error('Error fetching admin reviews:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to fetch admin reviews'
      };
    }
  }

  /**
   * Moderate a review (approve/reject)
   */
  static async moderateReview(
    reviewId: string,
    action: 'APPROVE' | 'REJECT',
    notes?: string
  ): Promise<ServiceResponse<any>> {
    try {
      const user = await getCurrentUser();
      const moderatorId = user.sub || user.userId || user.username;

      const variables = {
        input: {
          id: reviewId,
          status: action === 'APPROVE' ? 'APPROVED' : 'REJECTED',
          adminNotes: notes,
          moderatedBy: moderatorId,
          moderatedAt: new Date().toISOString()
        }
      };

      const result = await adminClient.graphql({
        query: updateReview,
        variables
      });

      return {
        success: true,
        data: result.data.updateReview
      };

    } catch (error) {
      console.error('Error moderating review:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to moderate review'
      };
    }
  }

  /**
   * Create a new review for an entity (alias for createReview)
   */
  static async createEntityReview(reviewData: ReviewData, user?: any): Promise<ServiceResponse<any>> {
    try {
      const result = await this.createReview(reviewData);
      return {
        success: true,
        data: result
      };
    } catch (error) {
      console.error('Error creating entity review:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to create review'
      };
    }
  }

  /**
   * Create a new review for an entity
   */
  static async createReview(reviewData: ReviewData): Promise<Review> {
    try {
      console.log('Creating review with data:', reviewData);

      // Get current user
      let user;
      try {
        user = await getCurrentUser();
      } catch (error) {
        console.log('No authenticated user, proceeding with anonymous review');
        user = null;
      }

      // Get user ID from AuthUser object (Amplify AuthUser uses 'sub' property)
      const userId = user?.sub || user?.userId || user?.username || 'anonymous-user';

      console.log('Resolved userId:', userId);

      // Validate essential fields - entityType and entityId are optional for platform reviews
      const requiredFields = ['rating', 'title', 'review'];
      const missingFields = [];

      for (const field of requiredFields) {
        if (!reviewData[field] || reviewData[field] === '' || reviewData[field] === null || reviewData[field] === undefined) {
          missingFields.push(field);
        }
      }

      if (missingFields.length > 0) {
        throw new Error(`Missing required fields: ${missingFields.join(', ')}`);
      }

      // For product/service reviews, entityType and entityId are required
      if (reviewData.reviewTarget === 'VENDOR' && (!reviewData.entityType || !reviewData.entityId)) {
        throw new Error('entityType and entityId are required for vendor reviews');
      }

      // Create composite key for user-entity uniqueness (null for platform reviews)
      const userEntityComposite = reviewData.entityId
        ? `${userId}#${reviewData.entityType}#${reviewData.entityId}`
        : null;

      // Prepare review data for GraphQL
      const reviewInput: CreateReviewInput = {
        userId: userId,
        name: (reviewData.name || 'Anonymous').trim(),
        email: (reviewData.email || '<EMAIL>').trim().toLowerCase(),
        location: (reviewData.location || 'Not specified').trim(),
        weddingDate: reviewData.weddingDate || new Date().toISOString().split('T')[0],
        category: reviewData.category || 'PRODUCT',
        rating: parseInt(reviewData.rating.toString()),
        title: reviewData.title.trim(),
        review: reviewData.review.trim(),
        wouldRecommend: Boolean(reviewData.wouldRecommend),
        verified: false, // Will be verified later
        status: 'PENDING', // Reviews need approval by default
        entityType: reviewData.entityType,
        entityId: reviewData.entityId,
        userEntityComposite,
        helpfulCount: 0,
        purchaseVerified: false,
        reviewHelpfulUsers: [],
        serviceRating: reviewData.serviceRating,
        valueRating: reviewData.valueRating,
        communicationRating: reviewData.communicationRating,
        professionalismRating: reviewData.professionalismRating,
        images: reviewData.images || []
      };

      // Determine reviewTarget based on category
      let reviewTarget: 'ADMIN' | 'VENDOR' = 'VENDOR'; // Default to VENDOR
      if (reviewInput.category === 'PLATFORM') {
        reviewTarget = 'ADMIN'; // Platform reviews go to admin dashboard
      } else {
        reviewTarget = 'VENDOR'; // Product/service reviews go to vendor dashboard
      }

      // Add the reviewTarget to the input
      const finalReviewInput = {
        ...reviewInput,
        reviewTarget,
        adminNotes: null,
        moderatedBy: null,
        moderatedAt: null
      };

      console.log('Review input prepared:', finalReviewInput);

      // Create the review
      const result = await client.graphql({
        query: createReview,
        variables: { input: finalReviewInput }
      });

      console.log('Review created successfully:', result);

      if (!result.data?.createReview) {
        throw new Error('Failed to create review - no data returned');
      }

      return result.data.createReview as Review;

    } catch (error) {
      console.error('Error creating review:', error);
      throw error;
    }
  }

  /**
   * Get reviews for a specific entity
   */
  static async getEntityReviews(
    entityId: string,
    options: {
      limit?: number;
      nextToken?: string;
      filter?: ModelReviewFilterInput;
    } = {}
  ): Promise<{
    reviews: Review[];
    nextToken?: string;
  }> {
    try {
      // Use listReviews with filter as fallback since reviewsByEntityId might not be deployed
      const entityFilter = {
        entityId: { eq: entityId },
        ...options.filter
      };

      const result = await client.graphql({
        query: listReviews,
        variables: {
          filter: entityFilter,
          limit: options.limit || 20,
          nextToken: options.nextToken
        }
      });

      const reviews = result.data?.listReviews?.items || [];
      const nextToken = result.data?.listReviews?.nextToken;

      return {
        reviews: reviews.filter(review => review !== null) as Review[],
        nextToken: nextToken || undefined
      };

    } catch (error) {
      console.error('Error fetching entity reviews:', error);
      throw error;
    }
  }

  /**
   * Calculate review statistics for an entity
   */
  static async getEntityReviewStats(entityId: string): Promise<ReviewStats> {
    try {
      // Get all reviews for the entity
      const { reviews } = await this.getEntityReviews(entityId, { limit: 1000 });

      if (reviews.length === 0) {
        return {
          totalReviews: 0,
          averageRating: 0,
          ratingDistribution: { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 },
          recommendationRate: 0
        };
      }

      // Calculate statistics
      const totalReviews = reviews.length;
      const totalRating = reviews.reduce((sum, review) => sum + review.rating, 0);
      const averageRating = totalRating / totalReviews;

      // Calculate rating distribution
      const ratingDistribution = { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 };
      reviews.forEach(review => {
        ratingDistribution[review.rating as keyof typeof ratingDistribution]++;
      });

      // Calculate recommendation rate
      const recommendedCount = reviews.filter(review => review.wouldRecommend).length;
      const recommendationRate = (recommendedCount / totalReviews) * 100;

      return {
        totalReviews,
        averageRating: Math.round(averageRating * 10) / 10,
        ratingDistribution,
        recommendationRate: Math.round(recommendationRate)
      };

    } catch (error) {
      console.error('Error calculating review stats:', error);
      throw error;
    }
  }

  /**
   * Update a review
   */
  static async updateReview(reviewId: string, updates: Partial<UpdateReviewInput>): Promise<Review> {
    try {
      const result = await client.graphql({
        query: updateReview,
        variables: {
          input: {
            id: reviewId,
            ...updates
          }
        }
      });

      if (!result.data?.updateReview) {
        throw new Error('Failed to update review');
      }

      return result.data.updateReview as Review;

    } catch (error) {
      console.error('Error updating review:', error);
      throw error;
    }
  }

  /**
   * Add vendor response to a review
   */
  static async addVendorResponse(reviewId: string, response: string): Promise<Review> {
    try {
      return await this.updateReview(reviewId, {
        vendorResponse: response,
        responseDate: new Date().toISOString()
      });

    } catch (error) {
      console.error('Error adding vendor response:', error);
      throw error;
    }
  }

  /**
   * Update review status (for admin moderation)
   */
  static async updateReviewStatus(
    reviewId: string,
    newStatus: string,
    user: any
  ): Promise<ServiceResponse<any>> {
    try {
      const updateInput = {
        id: reviewId,
        status: newStatus,
        moderatedBy: user?.userId || 'admin',
        moderatedAt: new Date().toISOString()
      };

      const result = await adminClient.graphql({
        query: updateReview,
        variables: {
          input: updateInput
        }
      });

      return {
        success: true,
        data: result.data?.updateReview
      };
    } catch (error) {
      console.error('Error updating review status:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to update review status'
      };
    }
  }

  /**
   * Bulk update review status
   */
  static async bulkUpdateReviewStatus(
    updates: Array<{ reviewId: string; newStatus: string }>,
    user: any
  ): Promise<ServiceResponse<any>> {
    try {
      const results = await Promise.all(
        updates.map(update =>
          this.updateReviewStatus(update.reviewId, update.newStatus, user)
        )
      );

      const failedUpdates = results.filter(result => !result.success);

      if (failedUpdates.length > 0) {
        return {
          success: false,
          error: `Failed to update ${failedUpdates.length} reviews`
        };
      }

      return {
        success: true,
        data: results
      };
    } catch (error) {
      console.error('Error bulk updating review status:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to bulk update reviews'
      };
    }
  }

  /**
   * Delete review (admin only)
   */
  static async deleteReview(reviewId: string, user: any): Promise<ServiceResponse<any>> {
    try {
      // Note: This would typically use a deleteReview mutation
      // For now, we'll update the status to 'DELETED'
      return await this.updateReviewStatus(reviewId, 'DELETED', user);
    } catch (error) {
      console.error('Error deleting review:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to delete review'
      };
    }
  }
}

export default EntityReviewService;