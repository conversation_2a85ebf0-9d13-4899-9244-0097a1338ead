import toast from 'react-hot-toast';

/**
 * Custom toast utility with predefined styles and messages
 */
export const showToast = {
  /**
   * Show success toast
   * @param message - Success message to display
   * @param options - Additional toast options
   */
  success: (message: string, options?: any) => {
    return toast.success(message, {
      duration: 4000,
      style: {
        background: '#10B981',
        color: '#fff',
        borderRadius: '8px',
        padding: '16px',
        fontSize: '14px',
        fontWeight: '500',
        boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
      },
      iconTheme: {
        primary: '#fff',
        secondary: '#10B981',
      },
      ...options,
    });
  },

  /**
   * Show error toast
   * @param message - Error message to display
   * @param options - Additional toast options
   */
  error: (message: string, options?: any) => {
    return toast.error(message, {
      duration: 5000, // Longer duration for errors
      style: {
        background: '#EF4444',
        color: '#fff',
        borderRadius: '8px',
        padding: '16px',
        fontSize: '14px',
        fontWeight: '500',
        boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
      },
      iconTheme: {
        primary: '#fff',
        secondary: '#EF4444',
      },
      ...options,
    });
  },

  /**
   * Show warning toast
   * @param message - Warning message to display
   * @param options - Additional toast options
   */
  warning: (message: string, options?: any) => {
    return toast(message, {
      duration: 4000,
      icon: '⚠️',
      style: {
        background: '#F59E0B',
        color: '#fff',
        borderRadius: '8px',
        padding: '16px',
        fontSize: '14px',
        fontWeight: '500',
        boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
      },
      ...options,
    });
  },

  /**
   * Show info toast
   * @param message - Info message to display
   * @param options - Additional toast options
   */
  info: (message: string, options?: any) => {
    return toast(message, {
      duration: 4000,
      icon: 'ℹ️',
      style: {
        background: '#3B82F6',
        color: '#fff',
        borderRadius: '8px',
        padding: '16px',
        fontSize: '14px',
        fontWeight: '500',
        boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
      },
      ...options,
    });
  },

  /**
   * Show loading toast
   * @param message - Loading message to display
   * @param options - Additional toast options
   */
  loading: (message: string, options?: any) => {
    return toast.loading(message, {
      style: {
        background: '#6B7280',
        color: '#fff',
        borderRadius: '8px',
        padding: '16px',
        fontSize: '14px',
        fontWeight: '500',
        boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
      },
      ...options,
    });
  },

  /**
   * Show promise toast (automatically handles loading, success, error states)
   * @param promise - Promise to track
   * @param messages - Messages for different states
   * @param options - Additional toast options
   */
  promise: <T>(
    promise: Promise<T>,
    messages: {
      loading: string;
      success: string | ((data: T) => string);
      error: string | ((error: any) => string);
    },
    options?: any
  ) => {
    return toast.promise(promise, messages, {
      style: {
        borderRadius: '8px',
        padding: '16px',
        fontSize: '14px',
        fontWeight: '500',
        boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
      },
      success: {
        style: {
          background: '#10B981',
          color: '#fff',
        },
        iconTheme: {
          primary: '#fff',
          secondary: '#10B981',
        },
      },
      error: {
        style: {
          background: '#EF4444',
          color: '#fff',
        },
        iconTheme: {
          primary: '#fff',
          secondary: '#EF4444',
        },
      },
      loading: {
        style: {
          background: '#6B7280',
          color: '#fff',
        },
      },
      ...options,
    });
  },

  /**
   * Dismiss a specific toast
   * @param toastId - ID of the toast to dismiss
   */
  dismiss: (toastId?: string) => {
    return toast.dismiss(toastId);
  },

  /**
   * Dismiss all toasts
   */
  dismissAll: () => {
    return toast.dismiss();
  },

  /**
   * Custom toast with full control
   * @param message - Message to display
   * @param options - Toast options
   */
  custom: (message: string, options?: any) => {
    return toast(message, options);
  },
};

/**
 * Predefined toast messages for common actions
 */
export const toastMessages = {
  // Authentication
  auth: {
    loginSuccess: 'Welcome back! You have been logged in successfully.',
    loginError: 'Login failed. Please check your credentials and try again.',
    logoutSuccess: 'You have been logged out successfully.',
    signupSuccess: 'Account created successfully! Welcome to BookmyFestive.',
    signupError: 'Account creation failed. Please try again.',
    authRequired: 'Please log in to continue.',
  },

  // Reviews
  review: {
    submitSuccess: 'Review submitted successfully! It will be published after moderation.',
    submitError: 'Failed to submit review. Please try again.',
    updateSuccess: 'Review updated successfully!',
    updateError: 'Failed to update review. Please try again.',
    deleteSuccess: 'Review deleted successfully.',
    deleteError: 'Failed to delete review. Please try again.',
    approveSuccess: 'Review approved successfully!',
    rejectSuccess: 'Review rejected successfully.',
    helpfulSuccess: 'Thank you for marking this review as helpful!',
    helpfulError: 'You have already marked this review as helpful.',
    duplicateError: 'You have already reviewed this item. You can edit your existing review instead.',
  },

  // Cart
  cart: {
    addSuccess: 'Item added to cart successfully!',
    removeSuccess: 'Item removed from cart.',
    updateSuccess: 'Cart updated successfully!',
    clearSuccess: 'Cart cleared successfully.',
    checkoutSuccess: 'Order placed successfully!',
    checkoutError: 'Checkout failed. Please try again.',
  },

  // General
  general: {
    saveSuccess: 'Changes saved successfully!',
    saveError: 'Failed to save changes. Please try again.',
    loadError: 'Failed to load data. Please refresh the page.',
    networkError: 'Network error. Please check your connection and try again.',
    unexpectedError: 'An unexpected error occurred. Please try again.',
    comingSoon: 'This feature is coming soon!',
    underMaintenance: 'This feature is currently under maintenance.',
  },

  // Form validation
  validation: {
    required: 'This field is required.',
    invalidEmail: 'Please enter a valid email address.',
    invalidPhone: 'Please enter a valid phone number.',
    passwordTooShort: 'Password must be at least 8 characters long.',
    passwordMismatch: 'Passwords do not match.',
    invalidDate: 'Please enter a valid date.',
    invalidRating: 'Rating must be between 1 and 5.',
  },

  // File upload
  upload: {
    success: 'File uploaded successfully!',
    error: 'File upload failed. Please try again.',
    invalidType: 'Invalid file type. Please select a valid file.',
    tooLarge: 'File is too large. Please select a smaller file.',
    tooMany: 'Too many files selected. Please select fewer files.',
  },
};

export default showToast;
