import { CartItem, Cart } from '../types';

export class CartService {
  private storageKey = 'thirumanam-cart';
  private platform: 'web' | 'mobile';

  constructor(platform: 'web' | 'mobile' = 'web') {
    this.platform = platform;
  }

  // Storage abstraction
  private async getStorage(): Promise<any> {
    if (this.platform === 'web') {
      return localStorage;
    } else {
      // For React Native, use AsyncStorage
      const AsyncStorage = await import('@react-native-async-storage/async-storage');
      return AsyncStorage.default;
    }
  }

  private async getItem(key: string): Promise<string | null> {
    const storage = await this.getStorage();
    
    if (this.platform === 'web') {
      return storage.getItem(key);
    } else {
      return await storage.getItem(key);
    }
  }

  private async setItem(key: string, value: string): Promise<void> {
    const storage = await this.getStorage();
    
    if (this.platform === 'web') {
      storage.setItem(key, value);
    } else {
      await storage.setItem(key, value);
    }
  }

  private async removeItem(key: string): Promise<void> {
    const storage = await this.getStorage();
    
    if (this.platform === 'web') {
      storage.removeItem(key);
    } else {
      await storage.removeItem(key);
    }
  }

  // Cart operations
  async loadCart(): Promise<Cart> {
    try {
      const cartData = await this.getItem(this.storageKey);
      if (cartData) {
        const cart = JSON.parse(cartData);
        return {
          items: cart.items || [],
          total: cart.total || 0,
          itemCount: cart.itemCount || 0,
          lastUpdated: cart.lastUpdated || new Date().toISOString(),
        };
      }
    } catch (error) {
      console.error('Error loading cart:', error);
    }

    return {
      items: [],
      total: 0,
      itemCount: 0,
      lastUpdated: new Date().toISOString(),
    };
  }

  async saveCart(cart: Cart): Promise<void> {
    try {
      const cartData = {
        ...cart,
        lastUpdated: new Date().toISOString(),
      };
      await this.setItem(this.storageKey, JSON.stringify(cartData));
    } catch (error) {
      console.error('Error saving cart:', error);
    }
  }

  async addItem(item: CartItem): Promise<Cart> {
    const cart = await this.loadCart();
    const existingItemIndex = cart.items.findIndex(
      (cartItem) => cartItem.id === item.id
    );

    if (existingItemIndex >= 0) {
      // Update quantity if item already exists
      cart.items[existingItemIndex].quantity += item.quantity;
    } else {
      // Add new item
      cart.items.push(item);
    }

    // Recalculate totals
    cart.total = cart.items.reduce(
      (sum, cartItem) => sum + cartItem.price * cartItem.quantity,
      0
    );
    cart.itemCount = cart.items.reduce(
      (sum, cartItem) => sum + cartItem.quantity,
      0
    );

    await this.saveCart(cart);
    return cart;
  }

  async removeItem(itemId: string | number): Promise<Cart> {
    const cart = await this.loadCart();
    cart.items = cart.items.filter((item) => item.id !== itemId);

    // Recalculate totals
    cart.total = cart.items.reduce(
      (sum, item) => sum + item.price * item.quantity,
      0
    );
    cart.itemCount = cart.items.reduce(
      (sum, item) => sum + item.quantity,
      0
    );

    await this.saveCart(cart);
    return cart;
  }

  async updateQuantity(itemId: string | number, quantity: number): Promise<Cart> {
    const cart = await this.loadCart();
    const itemIndex = cart.items.findIndex((item) => item.id === itemId);

    if (itemIndex >= 0) {
      if (quantity <= 0) {
        // Remove item if quantity is 0 or negative
        cart.items.splice(itemIndex, 1);
      } else {
        cart.items[itemIndex].quantity = quantity;
      }

      // Recalculate totals
      cart.total = cart.items.reduce(
        (sum, item) => sum + item.price * item.quantity,
        0
      );
      cart.itemCount = cart.items.reduce(
        (sum, item) => sum + item.quantity,
        0
      );

      await this.saveCart(cart);
    }

    return cart;
  }

  async clearCart(): Promise<Cart> {
    const emptyCart: Cart = {
      items: [],
      total: 0,
      itemCount: 0,
      lastUpdated: new Date().toISOString(),
    };

    await this.saveCart(emptyCart);
    return emptyCart;
  }

  async getItemCount(): Promise<number> {
    const cart = await this.loadCart();
    return cart.itemCount;
  }

  async getTotal(): Promise<number> {
    const cart = await this.loadCart();
    return cart.total;
  }

  async isItemInCart(itemId: string | number): Promise<boolean> {
    const cart = await this.loadCart();
    return cart.items.some((item) => item.id === itemId);
  }

  async getCartSummary(): Promise<{
    itemCount: number;
    total: number;
    categories: string[];
  }> {
    const cart = await this.loadCart();
    const categories = [...new Set(cart.items.map((item) => item.category).filter(Boolean))];

    return {
      itemCount: cart.itemCount,
      total: cart.total,
      categories,
    };
  }

  // Sync cart with server (for logged-in users)
  async syncWithServer(apiService: any): Promise<Cart> {
    try {
      const localCart = await this.loadCart();
      
      // Upload local cart to server
      if (localCart.items.length > 0) {
        await apiService.syncCart(localCart);
      }

      // Download server cart
      const serverCartResponse = await apiService.getCart();
      if (serverCartResponse.success && serverCartResponse.data) {
        const serverCart = serverCartResponse.data;
        await this.saveCart(serverCart);
        return serverCart;
      }

      return localCart;
    } catch (error) {
      console.error('Error syncing cart with server:', error);
      return await this.loadCart();
    }
  }

  // Merge carts (useful when user logs in)
  async mergeCarts(serverCart: Cart): Promise<Cart> {
    const localCart = await this.loadCart();
    const mergedItems: CartItem[] = [...serverCart.items];

    // Add local items that don't exist in server cart
    localCart.items.forEach((localItem) => {
      const existsInServer = serverCart.items.some(
        (serverItem) => serverItem.id === localItem.id
      );
      
      if (!existsInServer) {
        mergedItems.push(localItem);
      }
    });

    const mergedCart: Cart = {
      items: mergedItems,
      total: mergedItems.reduce(
        (sum, item) => sum + item.price * item.quantity,
        0
      ),
      itemCount: mergedItems.reduce(
        (sum, item) => sum + item.quantity,
        0
      ),
      lastUpdated: new Date().toISOString(),
    };

    await this.saveCart(mergedCart);
    return mergedCart;
  }
}

// Create singleton instances
export const webCartService = new CartService('web');
export const mobileCartService = new CartService('mobile');

export default CartService;
