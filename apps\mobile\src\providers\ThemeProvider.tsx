import React, { createContext, useContext, ReactNode } from 'react';

export interface Theme {
  colors: {
    // Primary colors
    primary: string;
    primaryForeground: string;
    secondary: string;
    secondaryForeground: string;
    accent: string;
    accentForeground: string;

    // Background colors
    background: string;
    foreground: string;
    surface: string;
    card: string;
    cardForeground: string;

    // Text colors
    text: string;
    textSecondary: string;
    textMuted: string;

    // Border and input colors
    border: string;
    input: string;
    ring: string;

    // Status colors
    error: string;
    success: string;
    warning: string;
    info: string;
    destructive: string;
    destructiveForeground: string;

    // Muted colors
    muted: string;
    mutedForeground: string;

    // Popover colors
    popover: string;
    popoverForeground: string;

    // Chart colors
    chart1: string;
    chart2: string;
    chart3: string;
    chart4: string;
    chart5: string;
  };
  spacing: {
    xs: number;
    sm: number;
    md: number;
    lg: number;
    xl: number;
    xxl: number;
  };
  typography: {
    h1: {
      fontSize: number;
      fontWeight: string;
      lineHeight: number;
      letterSpacing?: number;
    };
    h2: {
      fontSize: number;
      fontWeight: string;
      lineHeight: number;
      letterSpacing?: number;
    };
    h3: {
      fontSize: number;
      fontWeight: string;
      lineHeight: number;
      letterSpacing?: number;
    };
    h4: {
      fontSize: number;
      fontWeight: string;
      lineHeight: number;
      letterSpacing?: number;
    };
    body: {
      fontSize: number;
      fontWeight: string;
      lineHeight: number;
    };
    bodyLarge: {
      fontSize: number;
      fontWeight: string;
      lineHeight: number;
    };
    bodySmall: {
      fontSize: number;
      fontWeight: string;
      lineHeight: number;
    };
    caption: {
      fontSize: number;
      fontWeight: string;
      lineHeight: number;
    };
    button: {
      fontSize: number;
      fontWeight: string;
      lineHeight: number;
    };
  };
  borderRadius: {
    sm: number;
    md: number;
    lg: number;
    xl: number;
    xxl: number;
  };
  shadows: {
    sm: {
      shadowColor: string;
      shadowOffset: { width: number; height: number };
      shadowOpacity: number;
      shadowRadius: number;
      elevation: number;
    };
    md: {
      shadowColor: string;
      shadowOffset: { width: number; height: number };
      shadowOpacity: number;
      shadowRadius: number;
      elevation: number;
    };
    lg: {
      shadowColor: string;
      shadowOffset: { width: number; height: number };
      shadowOpacity: number;
      shadowRadius: number;
      elevation: number;
    };
    lg: number;
    xl: number;
  };
}

const defaultTheme: Theme = {
  colors: {
    // Primary colors - exact match to web app HSL values
    primary: '#610f13', // hsl(357, 70%, 21%) - maroon
    primaryForeground: '#FDF6E3', // hsl(36, 53%, 98%)
    secondary: '#6B8E23', // hsl(90, 34%, 45%) - leaf green
    secondaryForeground: '#FDF6E3', // hsl(36, 53%, 98%)
    accent: '#F6C244', // hsl(44, 89%, 62%) - gold
    accentForeground: '#3D2914', // hsl(25, 60%, 20%)

    // Background colors - exact match to web app
    background: '#FFFFFF', // hsl(0, 0%, 100%)
    foreground: '#0A0A0A', // hsl(0, 0%, 3.9%)
    surface: '#F8FAFC',
    card: '#FFFFFF', // hsl(0, 0%, 100%)
    cardForeground: '#0A0A0A', // hsl(0, 0%, 3.9%)

    // Popover colors
    popover: '#FFFFFF', // hsl(0, 0%, 100%)
    popoverForeground: '#0A0A0A', // hsl(0, 0%, 3.9%)

    // Text colors - exact match to web app
    text: '#0A0A0A', // hsl(0, 0%, 3.9%)
    textSecondary: '#64748B',
    textMuted: '#94A3B8',
    muted: '#F1F5F9', // hsl(36, 53%, 85%)
    mutedForeground: '#64748B', // hsl(25, 60%, 40%)

    // Border and input colors - exact match to web app
    border: '#E2E8F0', // hsl(36, 53%, 80%)
    input: '#E2E8F0', // hsl(36, 53%, 80%)
    ring: '#9B1C31', // hsl(347, 69%, 34%)

    // Status colors - exact match to web app
    error: '#EF4444',
    success: '#10B981',
    warning: '#F59E0B',
    info: '#3B82F6',
    destructive: '#DC2626', // hsl(0, 84.2%, 60.2%)
    destructiveForeground: '#FDF6E3', // hsl(36, 53%, 98%)

    // Muted colors
    muted: '#F1F5F9',
    mutedForeground: '#64748B',

    // Popover colors
    popover: '#FFFFFF',
    popoverForeground: '#0A0A0A',

    // Status colors - exact match to web app
    error: '#EF4444',
    success: '#10B981',
    warning: '#F59E0B',
    info: '#3B82F6',
    destructive: '#DC2626', // hsl(0, 84.2%, 60.2%)
    destructiveForeground: '#FDF6E3', // hsl(36, 53%, 98%)

    // Chart colors - exact match to web app
    chart1: '#9B1C31', // hsl(347, 69%, 34%) - maroon
    chart2: '#6B8E23', // hsl(90, 34%, 45%) - leaf green
    chart3: '#64748B', // hsl(25, 60%, 40%)
    chart4: '#E6E3D7', // hsl(36, 53%, 91%)
    chart5: '#F6C244', // hsl(44, 89%, 62%) - gold
  },
  spacing: {
    xs: 4,
    sm: 8,
    md: 16,
    lg: 24,
    xl: 32,
    xxl: 48,
  },
  typography: {
    // Typography scale matching web app
    h1: {
      fontSize: 36, // text-4xl equivalent
      fontWeight: 'bold',
      lineHeight: 40,
      letterSpacing: -0.025,
    },
    h2: {
      fontSize: 30, // text-3xl equivalent
      fontWeight: 'bold',
      lineHeight: 36,
      letterSpacing: -0.025,
    },
    h3: {
      fontSize: 24, // text-2xl equivalent
      fontWeight: '600',
      lineHeight: 32,
    },
    h4: {
      fontSize: 20, // text-xl equivalent
      fontWeight: '600',
      lineHeight: 28,
    },
    body: {
      fontSize: 16, // text-base equivalent
      fontWeight: 'normal',
      lineHeight: 24,
    },
    bodyLarge: {
      fontSize: 18, // text-lg equivalent
      fontWeight: 'normal',
      lineHeight: 28,
    },
    bodySmall: {
      fontSize: 14, // text-sm equivalent
      fontWeight: 'normal',
      lineHeight: 20,
    },
    caption: {
      fontSize: 12, // text-xs equivalent
      fontWeight: 'normal',
      lineHeight: 16,
    },
    button: {
      fontSize: 14, // text-sm equivalent
      fontWeight: '500',
      lineHeight: 20,
    },
  },
  borderRadius: {
    // Border radius matching web app --radius: 0.5rem (8px)
    sm: 4, // calc(var(--radius) - 4px) = 4px
    md: 6, // calc(var(--radius) - 2px) = 6px
    lg: 8, // var(--radius) = 8px
    xl: 12,
    xxl: 16,
  },
  // Shadows matching web app
  shadows: {
    sm: {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.05,
      shadowRadius: 2,
      elevation: 1,
    },
    md: {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 0.1,
      shadowRadius: 6,
      elevation: 3,
    },
    lg: {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 10 },
      shadowOpacity: 0.15,
      shadowRadius: 15,
      elevation: 5,
    },
  },
};

interface ThemeContextType {
  theme: Theme;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

interface ThemeProviderProps {
  children: ReactNode;
  theme?: Theme;
}

export function ThemeProvider({ children, theme = defaultTheme }: ThemeProviderProps) {
  const contextValue: ThemeContextType = {
    theme,
  };

  return (
    <ThemeContext.Provider value={contextValue}>
      {children}
    </ThemeContext.Provider>
  );
}

export function useTheme(): ThemeContextType {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
}
