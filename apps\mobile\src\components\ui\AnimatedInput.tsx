import React, { useRef, useState, useEffect } from 'react';
import {
  View,
  TextInput,
  Text,
  StyleSheet,
  Animated,
  TouchableOpacity,
  TextInputProps,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import * as Haptics from 'expo-haptics';
import { useTheme } from '../../providers/ThemeProvider';

interface AnimatedInputProps extends TextInputProps {
  label?: string;
  error?: string;
  leftIcon?: string;
  rightIcon?: string;
  onRightIconPress?: () => void;
  variant?: 'outlined' | 'filled' | 'underlined';
  size?: 'small' | 'medium' | 'large';
  animationType?: 'float' | 'slide' | 'scale';
  hapticFeedback?: boolean;
  showCharacterCount?: boolean;
  maxLength?: number;
  required?: boolean;
  style?: any;
  containerStyle?: any;
}

export default function AnimatedInput({
  label,
  error,
  leftIcon,
  rightIcon,
  onRightIconPress,
  variant = 'outlined',
  size = 'medium',
  animationType = 'float',
  hapticFeedback = true,
  showCharacterCount = false,
  maxLength,
  required = false,
  style,
  containerStyle,
  value,
  onFocus,
  onBlur,
  ...props
}: AnimatedInputProps) {
  const { theme } = useTheme();
  const [isFocused, setIsFocused] = useState(false);
  const [hasValue, setHasValue] = useState(!!value);

  // Animation values
  const labelAnim = useRef(new Animated.Value(hasValue || isFocused ? 1 : 0)).current;
  const borderAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(1)).current;
  const shakeAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    setHasValue(!!value);
    if (animationType === 'float') {
      Animated.timing(labelAnim, {
        toValue: hasValue || isFocused ? 1 : 0,
        duration: 200,
        useNativeDriver: false,
      }).start();
    }
  }, [value, hasValue, isFocused, animationType]);

  useEffect(() => {
    if (error) {
      startShakeAnimation();
    }
  }, [error]);

  const getSizeValues = () => {
    switch (size) {
      case 'small':
        return {
          height: 40,
          fontSize: 14,
          paddingHorizontal: 12,
          paddingVertical: 8,
          iconSize: 18,
        };
      case 'large':
        return {
          height: 56,
          fontSize: 18,
          paddingHorizontal: 16,
          paddingVertical: 16,
          iconSize: 24,
        };
      default: // medium
        return {
          height: 48,
          fontSize: 16,
          paddingHorizontal: 14,
          paddingVertical: 12,
          iconSize: 20,
        };
    }
  };

  const startShakeAnimation = () => {
    if (hapticFeedback) {
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
    }

    Animated.sequence([
      Animated.timing(shakeAnim, { toValue: 10, duration: 100, useNativeDriver: true }),
      Animated.timing(shakeAnim, { toValue: -10, duration: 100, useNativeDriver: true }),
      Animated.timing(shakeAnim, { toValue: 10, duration: 100, useNativeDriver: true }),
      Animated.timing(shakeAnim, { toValue: 0, duration: 100, useNativeDriver: true }),
    ]).start();
  };

  const handleFocus = (e: any) => {
    setIsFocused(true);
    
    if (hapticFeedback) {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }

    // Animate border
    Animated.timing(borderAnim, {
      toValue: 1,
      duration: 200,
      useNativeDriver: false,
    }).start();

    // Animate label for float type
    if (animationType === 'float') {
      Animated.timing(labelAnim, {
        toValue: 1,
        duration: 200,
        useNativeDriver: false,
      }).start();
    }

    // Scale animation
    if (animationType === 'scale') {
      Animated.spring(scaleAnim, {
        toValue: 1.02,
        useNativeDriver: true,
      }).start();
    }

    if (onFocus) {
      onFocus(e);
    }
  };

  const handleBlur = (e: any) => {
    setIsFocused(false);

    // Animate border
    Animated.timing(borderAnim, {
      toValue: 0,
      duration: 200,
      useNativeDriver: false,
    }).start();

    // Animate label for float type
    if (animationType === 'float' && !hasValue) {
      Animated.timing(labelAnim, {
        toValue: 0,
        duration: 200,
        useNativeDriver: false,
      }).start();
    }

    // Scale animation
    if (animationType === 'scale') {
      Animated.spring(scaleAnim, {
        toValue: 1,
        useNativeDriver: true,
      }).start();
    }

    if (onBlur) {
      onBlur(e);
    }
  };

  const getContainerStyle = () => {
    const { height } = getSizeValues();
    const baseStyle: any = {
      marginBottom: theme.spacing.md,
    };

    switch (variant) {
      case 'filled':
        baseStyle.backgroundColor = theme.colors.surface;
        baseStyle.borderRadius = theme.borderRadius.md;
        break;
      case 'underlined':
        baseStyle.borderBottomWidth = 1;
        baseStyle.borderBottomColor = error 
          ? theme.colors.error 
          : borderAnim.interpolate({
              inputRange: [0, 1],
              outputRange: [theme.colors.border, theme.colors.primary],
            });
        break;
      default: // outlined
        baseStyle.borderWidth = 1;
        baseStyle.borderRadius = theme.borderRadius.md;
        baseStyle.borderColor = error 
          ? theme.colors.error 
          : borderAnim.interpolate({
              inputRange: [0, 1],
              outputRange: [theme.colors.border, theme.colors.primary],
            });
    }

    return baseStyle;
  };

  const getInputStyle = () => {
    const { height, fontSize, paddingHorizontal, paddingVertical } = getSizeValues();
    
    return {
      height,
      fontSize,
      paddingHorizontal: leftIcon ? paddingHorizontal + 30 : paddingHorizontal,
      paddingVertical,
      color: theme.colors.text,
      paddingRight: rightIcon ? paddingHorizontal + 30 : paddingHorizontal,
    };
  };

  const getLabelStyle = () => {
    if (animationType !== 'float') {
      return {
        color: error ? theme.colors.error : theme.colors.textSecondary,
        fontSize: 14,
        marginBottom: theme.spacing.xs,
      };
    }

    const { fontSize, paddingHorizontal } = getSizeValues();
    
    return {
      position: 'absolute' as const,
      left: leftIcon ? paddingHorizontal + 30 : paddingHorizontal,
      top: labelAnim.interpolate({
        inputRange: [0, 1],
        outputRange: [fontSize + 8, -8],
      }),
      fontSize: labelAnim.interpolate({
        inputRange: [0, 1],
        outputRange: [fontSize, 12],
      }),
      color: error 
        ? theme.colors.error 
        : labelAnim.interpolate({
            inputRange: [0, 1],
            outputRange: [theme.colors.textSecondary, theme.colors.primary],
          }),
      backgroundColor: variant === 'outlined' ? theme.colors.background : 'transparent',
      paddingHorizontal: variant === 'outlined' ? 4 : 0,
      zIndex: 1,
    };
  };

  const { iconSize, paddingHorizontal } = getSizeValues();

  return (
    <Animated.View 
      style={[
        containerStyle,
        { transform: [{ translateX: shakeAnim }, { scale: scaleAnim }] }
      ]}
    >
      {/* Static Label (for non-float animations) */}
      {label && animationType !== 'float' && (
        <Text style={getLabelStyle()}>
          {label}
          {required && <Text style={{ color: theme.colors.error }}> *</Text>}
        </Text>
      )}

      {/* Input Container */}
      <Animated.View style={getContainerStyle()}>
        {/* Floating Label */}
        {label && animationType === 'float' && (
          <Animated.Text style={getLabelStyle()}>
            {label}
            {required && <Text style={{ color: theme.colors.error }}> *</Text>}
          </Animated.Text>
        )}

        {/* Left Icon */}
        {leftIcon && (
          <View style={[styles.leftIcon, { left: paddingHorizontal }]}>
            <Ionicons 
              name={leftIcon as any} 
              size={iconSize} 
              color={isFocused ? theme.colors.primary : theme.colors.textSecondary} 
            />
          </View>
        )}

        {/* Text Input */}
        <TextInput
          style={[getInputStyle(), style]}
          value={value}
          onFocus={handleFocus}
          onBlur={handleBlur}
          onChangeText={(text) => {
            setHasValue(!!text);
            if (props.onChangeText) {
              props.onChangeText(text);
            }
          }}
          maxLength={maxLength}
          placeholderTextColor={theme.colors.textSecondary}
          {...props}
        />

        {/* Right Icon */}
        {rightIcon && (
          <TouchableOpacity
            style={[styles.rightIcon, { right: paddingHorizontal }]}
            onPress={onRightIconPress}
            activeOpacity={0.7}
          >
            <Ionicons 
              name={rightIcon as any} 
              size={iconSize} 
              color={isFocused ? theme.colors.primary : theme.colors.textSecondary} 
            />
          </TouchableOpacity>
        )}
      </Animated.View>

      {/* Error Message */}
      {error && (
        <Text style={[styles.errorText, { color: theme.colors.error }]}>
          {error}
        </Text>
      )}

      {/* Character Count */}
      {showCharacterCount && maxLength && (
        <Text style={[styles.characterCount, { color: theme.colors.textSecondary }]}>
          {(value?.length || 0)}/{maxLength}
        </Text>
      )}
    </Animated.View>
  );
}

const styles = StyleSheet.create({
  leftIcon: {
    position: 'absolute',
    top: '50%',
    transform: [{ translateY: -10 }],
    zIndex: 1,
  },
  rightIcon: {
    position: 'absolute',
    top: '50%',
    transform: [{ translateY: -10 }],
    zIndex: 1,
  },
  errorText: {
    fontSize: 12,
    marginTop: 4,
  },
  characterCount: {
    fontSize: 12,
    textAlign: 'right',
    marginTop: 4,
  },
});
