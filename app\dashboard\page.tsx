'use client'
import { Bar<PERSON><PERSON>2, Users, Image as ImageIcon, Star, User, ShoppingBag, MapPin, Plus, ArrowRight, Menu, Bell } from 'lucide-react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import Link from 'next/link'

export default function DashboardHome() {
  // Mock analytics data
  const stats = [
    { label: 'Total Bookings', value: 24, icon: <BarChart2 className="h-5 w-5 text-primary" /> },
    { label: 'Orders', value: 5, icon: <ShoppingBag className="h-5 w-5 text-pink-600" /> },
    { label: 'Profile Completeness', value: '85%', icon: <User className="h-5 w-5 text-blue-600" /> },
  ]
  // Mock bookings trend
  const bookingsTrend = [4, 6, 3, 8, 2, 1, 0, 5, 7, 4, 6, 8]
  const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']

  return (
    <div className="max-w-7xl mx-auto px-3 sm:px-4 lg:px-6 py-3 sm:py-4 lg:py-6 pb-safe">
      {/* Header Section */}
      <div className="mb-4 sm:mb-6 lg:mb-8">
        <h1 className="text-xl sm:text-2xl lg:text-3xl xl:text-4xl font-bold text-primary leading-tight">
          Welcome to Your Dashboard
        </h1>
        <p className="text-gray-600 mt-1 sm:mt-2 text-xs sm:text-sm lg:text-base">
          Manage your bookings, orders, and profile from one place
        </p>
      </div>

      {/* Analytics Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4 lg:gap-6 mb-6 sm:mb-8 lg:mb-12">
        {stats.map((stat, i) => (
          <div 
            key={i} 
            className="bg-white rounded-lg sm:rounded-xl lg:rounded-2xl shadow-sm sm:shadow-lg p-3 sm:p-4 lg:p-6 xl:p-8 flex flex-col items-center justify-center gap-2 sm:gap-3 lg:gap-4 transition-all duration-200 hover:shadow-lg hover:scale-[1.02] border border-gray-100 touch-manipulation min-h-[120px] sm:min-h-[140px]"
          >
            <div className="p-2 sm:p-3 lg:p-4 rounded-full bg-primary/10 flex items-center justify-center mb-1 sm:mb-2">
              {stat.icon}
            </div>
            <div className="text-xl sm:text-2xl lg:text-3xl xl:text-4xl font-extrabold text-gray-900 text-center">
              {stat.value}
            </div>
            <div className="text-gray-500 text-xs sm:text-sm lg:text-base font-medium text-center leading-tight">
              {stat.label}
            </div>
          </div>
        ))}
      </div>

      {/* Bookings Trend Chart */}
      <div className="bg-white shadow-sm sm:shadow-lg rounded-lg sm:rounded-xl lg:rounded-2xl p-3 sm:p-4 lg:p-6 xl:p-8 mb-6 sm:mb-8 lg:mb-12 border border-gray-100">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-3 sm:mb-4 lg:mb-6 gap-1 sm:gap-2">
          <div className="font-semibold text-base sm:text-lg lg:text-xl text-primary">
            Bookings Trend (2024)
          </div>
          <div className="text-xs sm:text-sm text-gray-400">
            (Demo Data)
          </div>
        </div>
        
        {/* Chart Container */}
        <div className="relative">
          <div className="flex items-end gap-1 sm:gap-2 lg:gap-3 h-20 sm:h-24 lg:h-32 xl:h-36 overflow-x-auto pb-3 sm:pb-4 scrollbar-hide">
            {bookingsTrend.map((val, i) => (
              <div key={i} className="flex flex-col items-center min-w-[1.5rem] sm:min-w-[2rem] lg:min-w-[2.5rem] xl:min-w-[3rem]">
                <div
                  className="bg-primary shadow-sm rounded-t-sm w-full"
                  style={{ 
                    height: `${Math.max(val * 6, 3)}px`, 
                    minHeight: '3px',
                    maxHeight: '100px'
                  }}
                  title={`${months[i]}: ${val} bookings`}
                />
                <div className="text-xs text-gray-400 mt-1 sm:mt-2 text-center">
                  {months[i].slice(0, 3)}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="space-y-3 sm:space-y-0 sm:flex sm:flex-wrap sm:gap-3 sm:justify-center">
        <Link 
          href="/dashboard/bookings" 
          className="block w-full sm:w-auto px-4 sm:px-6 py-3 rounded-full bg-primary text-white font-semibold shadow-sm hover:bg-primary/90 transition-all duration-200 text-center text-sm sm:text-base hover:shadow-md touch-manipulation min-h-[44px] flex items-center justify-center"
        >
          View Bookings
        </Link>
        <Link 
          href="/dashboard/profile" 
          className="block w-full sm:w-auto px-4 sm:px-6 py-3 rounded-full bg-blue-600 text-white font-semibold shadow-sm hover:bg-blue-700 transition-all duration-200 text-center text-sm sm:text-base hover:shadow-md touch-manipulation min-h-[44px] flex items-center justify-center"
        >
          Edit Profile
        </Link>
      </div>

      {/* Additional Mobile-Friendly Features */}
      <div className="mt-6 sm:mt-8 lg:mt-12 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4 lg:gap-6">
        {/* Recent Activity */}
        <div className="bg-white rounded-lg sm:rounded-xl p-3 sm:p-4 lg:p-6 shadow-sm border border-gray-100">
          <h3 className="font-semibold text-gray-900 mb-2 sm:mb-3 text-sm sm:text-base">Recent Activity</h3>
          <div className="space-y-1.5 sm:space-y-2">
            <div className="flex items-center gap-2 text-xs sm:text-sm">
              <div className="w-1.5 h-1.5 sm:w-2 sm:h-2 bg-green-500 rounded-full"></div>
              <span className="text-gray-600">New booking confirmed</span>
            </div>
            <div className="flex items-center gap-2 text-xs sm:text-sm">
              <div className="w-1.5 h-1.5 sm:w-2 sm:h-2 bg-blue-500 rounded-full"></div>
              <span className="text-gray-600">Profile updated</span>
            </div>
            <div className="flex items-center gap-2 text-xs sm:text-sm">
              <div className="w-1.5 h-1.5 sm:w-2 sm:h-2 bg-yellow-500 rounded-full"></div>
              <span className="text-gray-600">Payment received</span>
            </div>
          </div>
        </div>

        {/* Quick Stats */}
        <div className="bg-white rounded-lg sm:rounded-xl p-3 sm:p-4 lg:p-6 shadow-sm border border-gray-100">
          <h3 className="font-semibold text-gray-900 mb-2 sm:mb-3 text-sm sm:text-base">Quick Stats</h3>
          <div className="space-y-1.5 sm:space-y-2">
            <div className="flex justify-between text-xs sm:text-sm">
              <span className="text-gray-600">This Month</span>
              <span className="font-medium">8 bookings</span>
            </div>
            <div className="flex justify-between text-xs sm:text-sm">
              <span className="text-gray-600">Total Spent</span>
              <span className="font-medium">₹45,000</span>
            </div>
            <div className="flex justify-between text-xs sm:text-sm">
              <span className="text-gray-600">Reviews Given</span>
              <span className="font-medium">12</span>
            </div>
          </div>
        </div>

        {/* Help & Support */}
        <div className="bg-white rounded-lg sm:rounded-xl p-3 sm:p-4 lg:p-6 shadow-sm border border-gray-100 sm:col-span-2 lg:col-span-1">
          <h3 className="font-semibold text-gray-900 mb-2 sm:mb-3 text-sm sm:text-base">Need Help?</h3>
          <div className="space-y-1.5 sm:space-y-2">
            <Link 
              href="/help" 
              className="block text-xs sm:text-sm text-primary hover:underline touch-manipulation py-1"
            >
              View Help Center
            </Link>
            <Link 
              href="/contact" 
              className="block text-xs sm:text-sm text-primary hover:underline touch-manipulation py-1"
            >
              Contact Support
            </Link>
            <Link 
              href="/faq" 
              className="block text-xs sm:text-sm text-primary hover:underline touch-manipulation py-1"
            >
              FAQ
            </Link>
          </div>
        </div>
      </div>

      {/* Mobile Quick Actions */}
      <div className="fixed bottom-20 right-4 z-40 md:hidden">
        <div className="flex flex-col gap-3">
          <button className="w-12 h-12 bg-primary text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-200 flex items-center justify-center touch-manipulation">
            <Bell className="w-5 h-5" />
          </button>
          <button className="w-12 h-12 bg-blue-600 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-200 flex items-center justify-center touch-manipulation">
            <Menu className="w-5 h-5" />
          </button>
        </div>
      </div>
    </div>
  )
} 