"use client"

import { TopHeader } from "@/components/top-header"
import { <PERSON><PERSON> } from "@/components/header"
import { Foot<PERSON> } from "@/components/footer"
import { usePWADetection } from "@/hooks/use-pwa-detection"

interface LayoutWrapperProps {
  children: React.ReactNode
  showTopHeader?: boolean
  showHeader?: boolean
  showFooter?: boolean
  hideFooterInPWA?: boolean
}

export function LayoutWrapper({
  children,
  showTopHeader = true,
  showHeader = true,
  showFooter = true,
  hideFooterInPWA = true
}: LayoutWrapperProps) {
  const { isStandalone } = usePWADetection()

  // Determine if footer should be shown
  const shouldShowFooter = showFooter && !(hideFooterInPWA && isStandalone)

  return (
    <>
      {showTopHeader && <TopHeader />}
      {showHeader && <Header />}
      {children}
      {shouldShowFooter && <Footer />}
    </>
  )
}
