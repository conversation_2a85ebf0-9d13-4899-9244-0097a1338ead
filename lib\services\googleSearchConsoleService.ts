import { google } from 'googleapis'

export class GoogleSearchConsoleService {
  private static auth = new google.auth.GoogleAuth({
    keyFile: process.env.GOOGLE_APPLICATION_CREDENTIALS,
    scopes: ['https://www.googleapis.com/auth/webmasters.readonly']
  })

  static async getSearchAnalytics(siteUrl: string, vendorId: string, dateRange: string) {
    try {
      const authClient = await this.auth.getClient()
      const searchconsole = google.searchconsole({ version: 'v1', auth: authClient })
      
      const [startDate, endDate] = this.getDateRange(dateRange)
      
      const response = await searchconsole.searchanalytics.query({
        siteUrl,
        requestBody: {
          startDate,
          endDate,
          dimensions: ['query', 'page', 'device'],
          dimensionFilterGroups: [{
            filters: [{
              dimension: 'page',
              operator: 'contains',
              expression: `/vendors/${vendorId}`
            }]
          }],
          rowLimit: 1000
        }
      })

      return this.parseSearchData(response.data)
    } catch (error) {
      console.error('Search Console API Error:', error)
      throw error
    }
  }

  static async getTopQueries(siteUrl: string, vendorId: string, dateRange: string) {
    try {
      const authClient = await this.auth.getClient()
      const searchconsole = google.searchconsole({ version: 'v1', auth: authClient })
      
      const [startDate, endDate] = this.getDateRange(dateRange)
      
      const response = await searchconsole.searchanalytics.query({
        siteUrl,
        requestBody: {
          startDate,
          endDate,
          dimensions: ['query'],
          dimensionFilterGroups: [{
            filters: [{
              dimension: 'page',
              operator: 'contains',
              expression: `/vendors/${vendorId}`
            }]
          }],
          rowLimit: 50
        }
      })

      return response.data.rows?.map(row => ({
        query: row.keys?.[0] || '',
        clicks: row.clicks || 0,
        impressions: row.impressions || 0,
        ctr: row.ctr || 0,
        position: row.position || 0
      })) || []
    } catch (error) {
      console.error('Search Console Queries Error:', error)
      return []
    }
  }

  static async getPagePerformance(siteUrl: string, vendorId: string, dateRange: string) {
    try {
      const authClient = await this.auth.getClient()
      const searchconsole = google.searchconsole({ version: 'v1', auth: authClient })
      
      const [startDate, endDate] = this.getDateRange(dateRange)
      
      const response = await searchconsole.searchanalytics.query({
        siteUrl,
        requestBody: {
          startDate,
          endDate,
          dimensions: ['page'],
          dimensionFilterGroups: [{
            filters: [{
              dimension: 'page',
              operator: 'contains',
              expression: `/vendors/${vendorId}`
            }]
          }],
          rowLimit: 25
        }
      })

      return response.data.rows?.map(row => ({
        page: row.keys?.[0] || '',
        clicks: row.clicks || 0,
        impressions: row.impressions || 0,
        ctr: row.ctr || 0,
        position: row.position || 0
      })) || []
    } catch (error) {
      console.error('Search Console Pages Error:', error)
      return []
    }
  }

  private static parseSearchData(data: any) {
    const rows = data.rows || []
    
    const totalClicks = rows.reduce((sum: number, row: any) => sum + (row.clicks || 0), 0)
    const totalImpressions = rows.reduce((sum: number, row: any) => sum + (row.impressions || 0), 0)
    const avgCTR = totalImpressions > 0 ? (totalClicks / totalImpressions) * 100 : 0
    const avgPosition = rows.length > 0 
      ? rows.reduce((sum: number, row: any) => sum + (row.position || 0), 0) / rows.length 
      : 0

    const deviceData = this.groupByDevice(rows)
    const queryData = this.groupByQuery(rows)

    return {
      totalClicks,
      totalImpressions,
      avgCTR: Math.round(avgCTR * 100) / 100,
      avgPosition: Math.round(avgPosition * 10) / 10,
      deviceBreakdown: deviceData,
      topQueries: queryData.slice(0, 10)
    }
  }

  private static groupByDevice(rows: any[]) {
    const devices: Record<string, any> = {}
    
    rows.forEach(row => {
      const device = row.keys?.[2] || 'unknown'
      if (!devices[device]) {
        devices[device] = { clicks: 0, impressions: 0 }
      }
      devices[device].clicks += row.clicks || 0
      devices[device].impressions += row.impressions || 0
    })

    return Object.entries(devices).map(([device, data]) => ({
      device,
      clicks: data.clicks,
      impressions: data.impressions,
      ctr: data.impressions > 0 ? (data.clicks / data.impressions) * 100 : 0
    }))
  }

  private static groupByQuery(rows: any[]) {
    const queries: Record<string, any> = {}
    
    rows.forEach(row => {
      const query = row.keys?.[0] || 'unknown'
      if (!queries[query]) {
        queries[query] = { clicks: 0, impressions: 0, positions: [] }
      }
      queries[query].clicks += row.clicks || 0
      queries[query].impressions += row.impressions || 0
      queries[query].positions.push(row.position || 0)
    })

    return Object.entries(queries)
      .map(([query, data]) => ({
        query,
        clicks: data.clicks,
        impressions: data.impressions,
        ctr: data.impressions > 0 ? (data.clicks / data.impressions) * 100 : 0,
        avgPosition: data.positions.length > 0 
          ? data.positions.reduce((a: number, b: number) => a + b, 0) / data.positions.length 
          : 0
      }))
      .sort((a, b) => b.clicks - a.clicks)
  }

  private static getDateRange(range: string): [string, string] {
    const end = new Date()
    const start = new Date()
    
    switch (range) {
      case '7d': start.setDate(end.getDate() - 7); break
      case '30d': start.setDate(end.getDate() - 30); break
      case '90d': start.setDate(end.getDate() - 90); break
      case '1y': start.setFullYear(end.getFullYear() - 1); break
    }
    
    return [
      start.toISOString().split('T')[0],
      end.toISOString().split('T')[0]
    ]
  }
}