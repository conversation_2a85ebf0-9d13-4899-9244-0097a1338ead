version: 1
backend:
  phases:
    build:
      commands:
        - '# Execute Amplify CLI with the helper script'
        - amplifyPush --simple
frontend:
  phases:
    preBuild:
      commands:
        - npm ci --legacy-peer-deps
    build:
      commands:
        - npm run build
  artifacts:
    baseDirectory: .next
    files:
      - '**/*'
  cache:
    paths:
      - node_modules/**/*
      - .next/cache/**/*
  customHeaders:
    - pattern: '/sitemap.xml'
      headers:
        - key: 'Content-Type'
          value: 'application/xml'
        - key: 'Cache-Control'
          value: 'public, max-age=86400'
    - pattern: '/robots.txt'
      headers:
        - key: 'Content-Type'
          value: 'text/plain'
        - key: 'Cache-Control'
          value: 'public, max-age=86400'