'use client';

import React, { useState, useEffect } from 'react';
import { AuthenticatedRoute } from '@/components/RouteProtection';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { 
  Plus, 
  Edit, 
  Trash2, 
  Save, 
  X, 
  Crown, 
  DollarSign,
  Users,
  TrendingUp,
  Settings
} from 'lucide-react';
import { AdminPricingService, type AdminPricingPlan, type SubscriptionStats } from '@/lib/services/adminPricingService';
import { useAuth } from '@/contexts/AuthContext';
import toast from 'react-hot-toast';

interface PricingPlanForm {
  id?: string;
  name: string;
  description: string;
  price: number;
  currency: string;
  duration: 'MONTHLY' | 'QUARTERLY' | 'YEARLY';
  features: string[];
  isActive: boolean;
  isPopular: boolean;
  discountPercentage?: number;
}

export default function AdminPricingPage() {
  const [plans, setPlans] = useState<AdminPricingPlan[]>([]);
  const [stats, setStats] = useState<SubscriptionStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [editingPlan, setEditingPlan] = useState<PricingPlanForm | null>(null);
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [newFeature, setNewFeature] = useState('');
  const { userProfile } = useAuth();

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    setLoading(true);
    try {
      const [pricingPlans, subscriptionStats] = await Promise.all([
        AdminPricingService.getAllPricingPlans(),
        AdminPricingService.getSubscriptionStats()
      ]);
      setPlans(pricingPlans);
      setStats(subscriptionStats);
    } catch (error) {
      console.error('Error loading data:', error);
      toast.error('Failed to load pricing data');
    } finally {
      setLoading(false);
    }
  };

  const handleCreatePlan = () => {
    setEditingPlan({
      name: '',
      description: '',
      price: 0,
      currency: 'INR',
      duration: 'MONTHLY',
      features: [],
      isActive: true,
      isPopular: false
    });
    setShowCreateForm(true);
  };

  const handleEditPlan = (plan: AdminPricingPlan) => {
    setEditingPlan({
      id: plan.id,
      name: plan.name,
      description: plan.description,
      price: plan.price,
      currency: plan.currency,
      duration: plan.duration,
      features: [...plan.features],
      isActive: plan.isActive,
      isPopular: plan.isPopular,
      discountPercentage: plan.discountPercentage
    });
    setShowCreateForm(true);
  };

  const handleSavePlan = async () => {
    if (!editingPlan) return;

    try {
      const planData: AdminPricingPlan = {
        id: editingPlan.id,
        name: editingPlan.name,
        description: editingPlan.description,
        price: editingPlan.price,
        currency: editingPlan.currency,
        duration: editingPlan.duration,
        features: editingPlan.features,
        isActive: editingPlan.isActive,
        isPopular: editingPlan.isPopular,
        discountPercentage: editingPlan.discountPercentage
      };

      const result = editingPlan.id
        ? await AdminPricingService.updatePricingPlan(planData)
        : await AdminPricingService.createPricingPlan(planData);

      if (result.success) {
        toast.success(result.message);
        setShowCreateForm(false);
        setEditingPlan(null);
        loadData();
      } else {
        toast.error(result.message);
      }
    } catch (error) {
      console.error('Error saving plan:', error);
      toast.error('Failed to save plan');
    }
  };

  const handleDeletePlan = async (planId: string) => {
    const confirmed = confirm('Are you sure you want to delete this pricing plan?');
    if (!confirmed) return;

    try {
      const result = await AdminPricingService.deletePricingPlan(planId);
      if (result.success) {
        toast.success(result.message);
        loadData();
      } else {
        toast.error(result.message);
      }
    } catch (error) {
      console.error('Error deleting plan:', error);
      toast.error('Failed to delete plan');
    }
  };

  const addFeature = () => {
    if (!newFeature.trim() || !editingPlan) return;
    
    setEditingPlan({
      ...editingPlan,
      features: [...editingPlan.features, newFeature.trim()]
    });
    setNewFeature('');
  };

  const removeFeature = (index: number) => {
    if (!editingPlan) return;
    
    setEditingPlan({
      ...editingPlan,
      features: editingPlan.features.filter((_, i) => i !== index)
    });
  };

  const formatPrice = (price: number, currency: string) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 0
    }).format(price);
  };

  // Check if user is admin
  if (!userProfile?.isAdmin) {
    return (
      <AuthenticatedRoute>
        <div className="container mx-auto px-4 py-8">
          <Card className="max-w-md mx-auto">
            <CardHeader className="text-center">
              <CardTitle>Admin Access Required</CardTitle>
              <CardDescription>
                This page is only available for admin users.
              </CardDescription>
            </CardHeader>
          </Card>
        </div>
      </AuthenticatedRoute>
    );
  }

  if (loading) {
    return (
      <AuthenticatedRoute>
        <div className="flex justify-center items-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-red-600"></div>
          <span className="ml-3 text-gray-600">Loading pricing plans...</span>
        </div>
      </AuthenticatedRoute>
    );
  }

  return (
    <AuthenticatedRoute>
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Pricing Management</h1>
          <p className="text-gray-600">
            Manage vendor subscription pricing plans and features
          </p>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <DollarSign className="h-5 w-5 text-blue-600" />
                </div>
                <div>
                  <p className="text-sm text-gray-600">Total Plans</p>
                  <p className="text-2xl font-bold">{plans.length}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-green-100 rounded-lg">
                  <TrendingUp className="h-5 w-5 text-green-600" />
                </div>
                <div>
                  <p className="text-sm text-gray-600">Active Subscriptions</p>
                  <p className="text-2xl font-bold">{stats?.activeSubscriptions || 0}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-purple-100 rounded-lg">
                  <Crown className="h-5 w-5 text-purple-600" />
                </div>
                <div>
                  <p className="text-sm text-gray-600">Total Revenue</p>
                  <p className="text-2xl font-bold">
                    {stats ? formatPrice(stats.totalRevenue, 'INR') : '₹0'}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-orange-100 rounded-lg">
                  <Users className="h-5 w-5 text-orange-600" />
                </div>
                <div>
                  <p className="text-sm text-gray-600">Monthly Revenue</p>
                  <p className="text-2xl font-bold">
                    {stats ? formatPrice(stats.monthlyRevenue, 'INR') : '₹0'}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Action Bar */}
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-xl font-semibold">Pricing Plans</h2>
          <Button 
            onClick={handleCreatePlan}
            className="bg-red-600 hover:bg-red-700"
          >
            <Plus className="h-4 w-4 mr-2" />
            Create New Plan
          </Button>
        </div>

        {/* Pricing Plans Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
          {plans.map((plan) => (
            <Card key={plan.id} className={`${plan.isPopular ? 'ring-2 ring-red-500' : ''}`}>
              <CardHeader>
                <div className="flex justify-between items-start">
                  <div>
                    <CardTitle className="flex items-center gap-2">
                      {plan.name}
                      {plan.isPopular && (
                        <Badge className="bg-red-600 text-white">
                          <Crown className="h-3 w-3 mr-1" />
                          Popular
                        </Badge>
                      )}
                    </CardTitle>
                    <CardDescription>{plan.description}</CardDescription>
                  </div>
                  <div className="flex gap-1">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleEditPlan(plan)}
                    >
                      <Edit className="h-3 w-3" />
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleDeletePlan(plan.id)}
                      className="text-red-600 hover:text-red-700"
                    >
                      <Trash2 className="h-3 w-3" />
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-center mb-4">
                  <div className="text-3xl font-bold">
                    {formatPrice(plan.price, plan.currency)}
                  </div>
                  <div className="text-gray-600">/{plan.duration.toLowerCase()}</div>
                  {plan.discountPercentage && (
                    <Badge className="bg-green-100 text-green-800 mt-2">
                      {plan.discountPercentage}% OFF
                    </Badge>
                  )}
                </div>
                
                <div className="space-y-2 mb-4">
                  {plan.features.slice(0, 3).map((feature, index) => (
                    <div key={index} className="flex items-center gap-2 text-sm">
                      <div className="w-1.5 h-1.5 bg-green-500 rounded-full"></div>
                      <span>{feature}</span>
                    </div>
                  ))}
                  {plan.features.length > 3 && (
                    <div className="text-sm text-gray-500">
                      +{plan.features.length - 3} more features
                    </div>
                  )}
                </div>
                
                <div className="flex justify-between items-center">
                  <Badge variant={plan.isActive ? "default" : "secondary"}>
                    {plan.isActive ? "Active" : "Inactive"}
                  </Badge>
                  <span className="text-xs text-gray-500">ID: {plan.id}</span>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Create/Edit Plan Modal */}
        {showCreateForm && editingPlan && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div className="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
              <div className="p-6 border-b">
                <div className="flex items-center justify-between">
                  <h2 className="text-2xl font-bold">
                    {editingPlan.id ? 'Edit Pricing Plan' : 'Create New Pricing Plan'}
                  </h2>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      setShowCreateForm(false);
                      setEditingPlan(null);
                    }}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              <div className="p-6 space-y-6">
                {/* Basic Information */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="name">Plan Name</Label>
                    <Input
                      id="name"
                      value={editingPlan.name}
                      onChange={(e) => setEditingPlan({...editingPlan, name: e.target.value})}
                      placeholder="e.g., Professional"
                    />
                  </div>

                  <div>
                    <Label htmlFor="price">Price</Label>
                    <Input
                      id="price"
                      type="number"
                      value={editingPlan.price}
                      onChange={(e) => setEditingPlan({...editingPlan, price: Number(e.target.value)})}
                      placeholder="1999"
                    />
                  </div>
                </div>

                <div>
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    value={editingPlan.description}
                    onChange={(e) => setEditingPlan({...editingPlan, description: e.target.value})}
                    placeholder="Brief description of the plan"
                    rows={3}
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <Label htmlFor="duration">Duration</Label>
                    <select
                      id="duration"
                      value={editingPlan.duration}
                      onChange={(e) => setEditingPlan({...editingPlan, duration: e.target.value as any})}
                      className="w-full p-2 border border-gray-300 rounded-md"
                    >
                      <option value="MONTHLY">Monthly</option>
                      <option value="QUARTERLY">Quarterly</option>
                      <option value="YEARLY">Yearly</option>
                    </select>
                  </div>

                  <div>
                    <Label htmlFor="currency">Currency</Label>
                    <select
                      id="currency"
                      value={editingPlan.currency}
                      onChange={(e) => setEditingPlan({...editingPlan, currency: e.target.value})}
                      className="w-full p-2 border border-gray-300 rounded-md"
                    >
                      <option value="INR">INR (₹)</option>
                      <option value="USD">USD ($)</option>
                    </select>
                  </div>

                  <div>
                    <Label htmlFor="discount">Discount %</Label>
                    <Input
                      id="discount"
                      type="number"
                      value={editingPlan.discountPercentage || ''}
                      onChange={(e) => setEditingPlan({...editingPlan, discountPercentage: e.target.value ? Number(e.target.value) : undefined})}
                      placeholder="20"
                      min="0"
                      max="100"
                    />
                  </div>
                </div>

                {/* Switches */}
                <div className="flex gap-6">
                  <div className="flex items-center space-x-2">
                    <Switch
                      id="isActive"
                      checked={editingPlan.isActive}
                      onCheckedChange={(checked) => setEditingPlan({...editingPlan, isActive: checked})}
                    />
                    <Label htmlFor="isActive">Active Plan</Label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Switch
                      id="isPopular"
                      checked={editingPlan.isPopular}
                      onCheckedChange={(checked) => setEditingPlan({...editingPlan, isPopular: checked})}
                    />
                    <Label htmlFor="isPopular">Popular Plan</Label>
                  </div>
                </div>

                {/* Features */}
                <div>
                  <Label>Features</Label>
                  <div className="space-y-2 mt-2">
                    {editingPlan.features.map((feature, index) => (
                      <div key={index} className="flex items-center gap-2">
                        <Input
                          value={feature}
                          onChange={(e) => {
                            const newFeatures = [...editingPlan.features];
                            newFeatures[index] = e.target.value;
                            setEditingPlan({...editingPlan, features: newFeatures});
                          }}
                          className="flex-1"
                        />
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => removeFeature(index)}
                          className="text-red-600"
                        >
                          <Trash2 className="h-3 w-3" />
                        </Button>
                      </div>
                    ))}

                    <div className="flex items-center gap-2">
                      <Input
                        value={newFeature}
                        onChange={(e) => setNewFeature(e.target.value)}
                        placeholder="Add new feature"
                        className="flex-1"
                        onKeyPress={(e) => e.key === 'Enter' && addFeature()}
                      />
                      <Button
                        size="sm"
                        onClick={addFeature}
                        disabled={!newFeature.trim()}
                      >
                        <Plus className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>
                </div>
              </div>

              <div className="p-6 border-t bg-gray-50 flex justify-end gap-3">
                <Button
                  variant="outline"
                  onClick={() => {
                    setShowCreateForm(false);
                    setEditingPlan(null);
                  }}
                >
                  Cancel
                </Button>
                <Button
                  onClick={handleSavePlan}
                  className="bg-red-600 hover:bg-red-700"
                  disabled={!editingPlan.name || !editingPlan.description || editingPlan.price <= 0}
                >
                  <Save className="h-4 w-4 mr-2" />
                  {editingPlan.id ? 'Update Plan' : 'Create Plan'}
                </Button>
              </div>
            </div>
          </div>
        )}
      </div>
    </AuthenticatedRoute>
  );
}
