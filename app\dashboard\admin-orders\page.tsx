"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog"
import { Package, Search, Calendar, CreditCard, Truck, Eye, Edit, Trash2, RefreshCw, Filter, MapPin, Phone, Mail, AlertCircle, DollarSign, Users, TrendingUp } from "lucide-react"
import { OrderService, OrderData } from "@/lib/services/orderService"
import { useAuth } from "@/contexts/AuthContext"
import { showToast } from "@/lib/toast"

export default function AdminOrdersPage() {
  const { isAuthenticated, user, isAdmin, userType } = useAuth()
  const router = useRouter()
  const [orders, setOrders] = useState<OrderData[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState('all')
  const [paymentFilter, setPaymentFilter] = useState('all')
  const [dateFilter, setDateFilter] = useState('all')
  const [filteredOrders, setFilteredOrders] = useState<OrderData[]>([])
  const [selectedOrder, setSelectedOrder] = useState<OrderData | null>(null)
  const [isUpdateDialogOpen, setIsUpdateDialogOpen] = useState(false)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [updateData, setUpdateData] = useState({
    status: '',
    paymentStatus: '',
    trackingNumber: '',
    courierPartner: '',
    notes: ''
  })
  const [currentPage, setCurrentPage] = useState(1)
  const [itemsPerPage] = useState(10)

  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/login?redirect=/dashboard/admin-orders')
      return
    }

    // Check if user is admin
    if (!isAdmin) {
      router.push('/dashboard')
      showToast.error('Access denied. Admin privileges required.')
      return
    }

    loadAllOrders()
  }, [isAuthenticated, user])

  useEffect(() => {
    filterOrders()
  }, [searchTerm, statusFilter, paymentFilter, dateFilter, orders])

  const loadAllOrders = async () => {
    try {
      setLoading(true)
      // This would be a new method to get all orders for admin
      const result = await OrderService.getAllOrders(500) // Admin can see more orders

      if (result.success) {
        setOrders(result.orders || [])
      } else {
        showToast.error('Failed to load orders')
        setOrders([]) // Set empty array as fallback
      }
    } catch (error) {
      console.error('Error loading admin orders:', error)
      showToast.error('Failed to load orders')
      setOrders([]) // Set empty array as fallback
    } finally {
      setLoading(false)
    }
  }

  const filterOrders = () => {
    let filtered = orders

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter(order =>
        order.orderNumber?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        order.customerName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        order.customerEmail?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (order.items && order.items.some(item => item.productName?.toLowerCase().includes(searchTerm.toLowerCase())))
      )
    }

    // Status filter
    if (statusFilter !== 'all') {
      filtered = filtered.filter(order => order.status === statusFilter)
    }

    // Payment filter
    if (paymentFilter !== 'all') {
      filtered = filtered.filter(order => order.paymentStatus === paymentFilter)
    }

    // Date filter
    if (dateFilter !== 'all') {
      const now = new Date()
      const filterDate = new Date()
      
      switch (dateFilter) {
        case 'today':
          filterDate.setHours(0, 0, 0, 0)
          break
        case 'week':
          filterDate.setDate(now.getDate() - 7)
          break
        case 'month':
          filterDate.setMonth(now.getMonth() - 1)
          break
        case 'quarter':
          filterDate.setMonth(now.getMonth() - 3)
          break
      }
      
      filtered = filtered.filter(order => new Date(order.orderDate) >= filterDate)
    }

    setFilteredOrders(filtered)
  }

  const handleUpdateOrder = async () => {
    if (!selectedOrder) return

    try {
      let result
      try {
        // Try the main update method first
        result = await OrderService.updateOrderStatus(
          selectedOrder.id,
          updateData.status as any,
          {
            paymentStatus: updateData.paymentStatus as any,
            trackingNumber: updateData.trackingNumber,
            courierPartner: updateData.courierPartner,
            adminNotes: updateData.notes
          }
        )
      } catch (updateError) {
        console.log('Main update failed, using simple update:', updateError.message)
        // Fallback to simple update method
        result = await OrderService.updateOrderStatusSimple(
          selectedOrder.id,
          updateData.status as any,
          {
            paymentStatus: updateData.paymentStatus as any,
            trackingNumber: updateData.trackingNumber,
            courierPartner: updateData.courierPartner,
            adminNotes: updateData.notes
          }
        )
      }

      if (result.success) {
        showToast.success(result.message || 'Order updated successfully')
        setIsUpdateDialogOpen(false)
        loadAllOrders()
      } else {
        showToast.error(result.message || 'Failed to update order')
      }
    } catch (error) {
      console.error('Error updating order:', error)
      showToast.error('Failed to update order')
    }
  }

  const handleDeleteOrder = async () => {
    if (!selectedOrder) return

    try {
      const result = await OrderService.deleteOrder(selectedOrder.id)

      if (result.success) {
        showToast.success('Order deleted successfully')
        setIsDeleteDialogOpen(false)
        loadAllOrders()
      } else {
        showToast.error('Failed to delete order')
      }
    } catch (error) {
      console.error('Error deleting order:', error)
      showToast.error('Failed to delete order')
    }
  }

  const openUpdateDialog = (order: OrderData) => {
    setSelectedOrder(order)
    setUpdateData({
      status: order.status,
      paymentStatus: order.paymentStatus,
      trackingNumber: order.trackingNumber || '',
      courierPartner: order.courierPartner || '',
      notes: ''
    })
    setIsUpdateDialogOpen(true)
  }

  const openDeleteDialog = (order: OrderData) => {
    setSelectedOrder(order)
    setIsDeleteDialogOpen(true)
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'PENDING': return 'bg-yellow-100 text-yellow-800'
      case 'CONFIRMED': return 'bg-green-100 text-green-800'
      case 'PROCESSING': return 'bg-blue-100 text-blue-800'
      case 'SHIPPED': return 'bg-purple-100 text-purple-800'
      case 'OUT_FOR_DELIVERY': return 'bg-indigo-100 text-indigo-800'
      case 'DELIVERED': return 'bg-green-100 text-green-800'
      case 'CANCELLED': return 'bg-red-100 text-red-800'
      case 'RETURNED': return 'bg-orange-100 text-orange-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getPaymentStatusColor = (status: string) => {
    switch (status) {
      case 'PAID': return 'bg-green-100 text-green-800'
      case 'COD_PENDING': return 'bg-yellow-100 text-yellow-800'
      case 'COD_COLLECTED': return 'bg-green-100 text-green-800'
      case 'FAILED': return 'bg-red-100 text-red-800'
      case 'PENDING': return 'bg-yellow-100 text-yellow-800'
      case 'REFUNDED': return 'bg-blue-100 text-blue-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  // Calculate stats
  const totalRevenue = filteredOrders.reduce((sum, order) => sum + order.total, 0)
  const totalOrders = filteredOrders.length
  const pendingOrders = filteredOrders.filter(o => o.status === 'PENDING').length
  const completedOrders = filteredOrders.filter(o => o.status === 'DELIVERED').length

  const getActiveOrders = () => filteredOrders.filter(order =>
    ['PENDING', 'CONFIRMED', 'PROCESSING', 'SHIPPED', 'OUT_FOR_DELIVERY'].includes(order.status)
  )

  const getCompletedOrders = () => filteredOrders.filter(order =>
    ['DELIVERED', 'CANCELLED', 'RETURNED'].includes(order.status)
  )

  // Pagination logic
  const getPaginatedOrders = (orders: OrderData[]) => {
    const startIndex = (currentPage - 1) * itemsPerPage
    const endIndex = startIndex + itemsPerPage
    return orders.slice(startIndex, endIndex)
  }

  const getTotalPages = (orders: OrderData[]) => Math.ceil(orders.length / itemsPerPage)

  const goToPage = (page: number) => {
    setCurrentPage(page)
    window.scrollTo({ top: 0, behavior: 'smooth' })
  }

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#F6C244]"></div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-4 sm:space-y-6 px-2 sm:px-0">
      {/* Header */}
      <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-4 sm:p-6 border border-blue-100">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-3 sm:gap-4">
          <div>
            <h1 className="text-2xl sm:text-3xl font-bold text-gray-900 mb-1 flex items-center gap-2">
              <Package className="h-6 w-6 sm:h-8 sm:w-8 text-blue-600" />
              Order Management
            </h1>
            <p className="text-gray-600 text-sm sm:text-base">Monitor and manage all customer orders across the platform</p>
          </div>
          <div className="flex gap-2">
            <Button onClick={loadAllOrders} variant="outline" className="flex items-center gap-2 text-sm hover:bg-blue-50">
              <RefreshCw className="h-4 w-4" />
              <span className="hidden sm:inline">Refresh Data</span>
              <span className="sm:hidden">Refresh</span>
            </Button>
          </div>
        </div>
      </div>

      {/* Enhanced Stats Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-6">
        <Card className="hover:shadow-lg transition-all duration-200 border-l-4 border-l-green-500">
          <CardContent className="p-4 sm:p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-xs sm:text-sm font-medium text-gray-600 mb-1">Total Revenue</p>
                <p className="text-lg sm:text-2xl font-bold text-gray-900">₹{totalRevenue.toLocaleString()}</p>
                <p className="text-xs text-green-600 mt-1">+12% from last month</p>
              </div>
              <div className="p-3 bg-green-100 rounded-full">
                <DollarSign className="h-6 w-6 text-green-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="hover:shadow-lg transition-all duration-200 border-l-4 border-l-blue-500">
          <CardContent className="p-4 sm:p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-xs sm:text-sm font-medium text-gray-600 mb-1">Total Orders</p>
                <p className="text-lg sm:text-2xl font-bold text-gray-900">{totalOrders}</p>
                <p className="text-xs text-blue-600 mt-1">All time orders</p>
              </div>
              <div className="p-3 bg-blue-100 rounded-full">
                <Package className="h-6 w-6 text-blue-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="hover:shadow-lg transition-all duration-200 border-l-4 border-l-yellow-500">
          <CardContent className="p-4 sm:p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-xs sm:text-sm font-medium text-gray-600 mb-1">Pending Orders</p>
                <p className="text-lg sm:text-2xl font-bold text-gray-900">{pendingOrders}</p>
                <p className="text-xs text-yellow-600 mt-1">Needs attention</p>
              </div>
              <div className="p-3 bg-yellow-100 rounded-full">
                <AlertCircle className="h-6 w-6 text-yellow-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="hover:shadow-lg transition-all duration-200 border-l-4 border-l-purple-500">
          <CardContent className="p-4 sm:p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-xs sm:text-sm font-medium text-gray-600 mb-1">Completed</p>
                <p className="text-lg sm:text-2xl font-bold text-gray-900">{completedOrders}</p>
                <p className="text-xs text-purple-600 mt-1">{((completedOrders/totalOrders)*100).toFixed(1)}% completion rate</p>
              </div>
              <div className="p-3 bg-purple-100 rounded-full">
                <TrendingUp className="h-6 w-6 text-purple-600" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Enhanced Filters */}
      <Card className="shadow-sm border-gray-200">
        <CardHeader className="pb-3">
          <div className="flex items-center gap-2">
            <Filter className="h-5 w-5 text-gray-600" />
            <h3 className="text-lg font-semibold text-gray-900">Filter & Search Orders</h3>
          </div>
        </CardHeader>
        <CardContent className="p-3 sm:p-6">
          <div className="space-y-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="Search by order number, customer name, email, or product..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 text-sm bg-gray-50 border-gray-200 focus:bg-white"
              />
            </div>
            
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3">
              <div>
                <Label className="text-xs font-medium text-gray-700 mb-1 block">Order Status</Label>
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger className="text-sm">
                    <SelectValue placeholder="All Status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Status</SelectItem>
                    <SelectItem value="PENDING">🟡 Pending</SelectItem>
                    <SelectItem value="CONFIRMED">🟢 Confirmed</SelectItem>
                    <SelectItem value="PROCESSING">🔵 Processing</SelectItem>
                    <SelectItem value="SHIPPED">🟣 Shipped</SelectItem>
                    <SelectItem value="OUT_FOR_DELIVERY">🚚 Out for Delivery</SelectItem>
                    <SelectItem value="DELIVERED">✅ Delivered</SelectItem>
                    <SelectItem value="CANCELLED">❌ Cancelled</SelectItem>
                    <SelectItem value="RETURNED">🔄 Returned</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label className="text-xs font-medium text-gray-700 mb-1 block">Payment Status</Label>
                <Select value={paymentFilter} onValueChange={setPaymentFilter}>
                  <SelectTrigger className="text-sm">
                    <SelectValue placeholder="All Payments" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Payments</SelectItem>
                    <SelectItem value="PAID">💳 Paid</SelectItem>
                    <SelectItem value="PENDING">⏳ Pending</SelectItem>
                    <SelectItem value="COD_PENDING">📦 COD Pending</SelectItem>
                    <SelectItem value="COD_COLLECTED">💰 COD Collected</SelectItem>
                    <SelectItem value="FAILED">❌ Failed</SelectItem>
                    <SelectItem value="REFUNDED">💸 Refunded</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label className="text-xs font-medium text-gray-700 mb-1 block">Time Period</Label>
                <Select value={dateFilter} onValueChange={setDateFilter}>
                  <SelectTrigger className="text-sm">
                    <SelectValue placeholder="All Time" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">📅 All Time</SelectItem>
                    <SelectItem value="today">📍 Today</SelectItem>
                    <SelectItem value="week">📊 Last Week</SelectItem>
                    <SelectItem value="month">📈 Last Month</SelectItem>
                    <SelectItem value="quarter">📉 Last 3 Months</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="flex flex-col justify-end">
                <div className="bg-blue-50 rounded-lg p-3 text-center">
                  <div className="text-lg font-bold text-blue-600">{filteredOrders.length}</div>
                  <div className="text-xs text-blue-600">Orders Found</div>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Orders Tabs */}
      <Tabs defaultValue="active" className="space-y-4 sm:space-y-6">
        <TabsList className="grid w-full grid-cols-2 bg-gray-100 p-1">
          <TabsTrigger value="active" className="flex items-center gap-2 text-xs sm:text-sm data-[state=active]:bg-white data-[state=active]:shadow-sm">
            <div className="flex items-center gap-2">
              <div className="p-1 bg-orange-100 rounded">
                <Package className="h-3 w-3 sm:h-4 sm:w-4 text-orange-600" />
              </div>
              <div className="text-left">
                <div className="font-medium">
                  <span className="hidden sm:inline">Active Orders</span>
                  <span className="sm:hidden">Active</span>
                </div>
                <div className="text-xs text-gray-500">{getActiveOrders().length} orders</div>
              </div>
            </div>
          </TabsTrigger>
          <TabsTrigger value="completed" className="flex items-center gap-2 text-xs sm:text-sm data-[state=active]:bg-white data-[state=active]:shadow-sm">
            <div className="flex items-center gap-2">
              <div className="p-1 bg-green-100 rounded">
                <Calendar className="h-3 w-3 sm:h-4 sm:w-4 text-green-600" />
              </div>
              <div className="text-left">
                <div className="font-medium">
                  <span className="hidden sm:inline">Completed Orders</span>
                  <span className="sm:hidden">Completed</span>
                </div>
                <div className="text-xs text-gray-500">{getCompletedOrders().length} orders</div>
              </div>
            </div>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="active" className="space-y-3 sm:space-y-4">
          {getActiveOrders().length === 0 ? (
            <Card>
              <CardContent className="text-center py-8 sm:py-12">
                <Package className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-base sm:text-lg font-medium text-gray-900 mb-2">No active orders</h3>
                <p className="text-gray-600 text-sm sm:text-base">No active orders found matching your filters.</p>
              </CardContent>
            </Card>
          ) : (
            <>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {getPaginatedOrders(getActiveOrders()).map((order) => (
                  <AdminOrderCard
                    key={order.id}
                    order={order}
                    getStatusColor={getStatusColor}
                    getPaymentStatusColor={getPaymentStatusColor}
                    onUpdate={openUpdateDialog}
                    onDelete={openDeleteDialog}
                  />
                ))}
              </div>

              {/* Pagination Controls for Active Orders */}
              {getActiveOrders().length > itemsPerPage && (
                <div className="flex flex-col sm:flex-row justify-center items-center gap-3 sm:gap-4 pt-4 sm:pt-6">
                  <Button
                    variant="outline"
                    onClick={() => goToPage(currentPage - 1)}
                    disabled={currentPage === 1}
                    className="text-sm w-full sm:w-auto"
                  >
                    Previous
                  </Button>
                  <span className="text-xs sm:text-sm text-gray-600 text-center">
                    Page {currentPage} of {getTotalPages(getActiveOrders())} • {getActiveOrders().length} total orders
                  </span>
                  <Button
                    variant="outline"
                    onClick={() => goToPage(currentPage + 1)}
                    disabled={currentPage >= getTotalPages(getActiveOrders())}
                    className="text-sm w-full sm:w-auto"
                  >
                    Next
                  </Button>
                </div>
              )}
            </>
          )}
        </TabsContent>

        <TabsContent value="completed" className="space-y-3 sm:space-y-4">
          {getCompletedOrders().length === 0 ? (
            <Card>
              <CardContent className="text-center py-8 sm:py-12">
                <Calendar className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-base sm:text-lg font-medium text-gray-900 mb-2">No completed orders</h3>
                <p className="text-gray-600 text-sm sm:text-base">No completed orders found matching your filters.</p>
              </CardContent>
            </Card>
          ) : (
            <>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {getPaginatedOrders(getCompletedOrders()).map((order) => (
                  <AdminOrderCard
                    key={order.id}
                    order={order}
                    getStatusColor={getStatusColor}
                    getPaymentStatusColor={getPaymentStatusColor}
                    onUpdate={openUpdateDialog}
                    onDelete={openDeleteDialog}
                  />
                ))}
              </div>

              {/* Pagination Controls for Completed Orders */}
              {getCompletedOrders().length > itemsPerPage && (
                <div className="flex flex-col sm:flex-row justify-center items-center gap-3 sm:gap-4 pt-4 sm:pt-6">
                  <Button
                    variant="outline"
                    onClick={() => goToPage(currentPage - 1)}
                    disabled={currentPage === 1}
                    className="text-sm w-full sm:w-auto"
                  >
                    Previous
                  </Button>
                  <span className="text-xs sm:text-sm text-gray-600 text-center">
                    Page {currentPage} of {getTotalPages(getCompletedOrders())} • {getCompletedOrders().length} total orders
                  </span>
                  <Button
                    variant="outline"
                    onClick={() => goToPage(currentPage + 1)}
                    disabled={currentPage >= getTotalPages(getCompletedOrders())}
                    className="text-sm w-full sm:w-auto"
                  >
                    Next
                  </Button>
                </div>
              )}
            </>
          )}
        </TabsContent>
      </Tabs>

      {/* Update Order Dialog */}
      <Dialog open={isUpdateDialogOpen} onOpenChange={setIsUpdateDialogOpen}>
        <DialogContent className="max-w-md mx-4 sm:mx-auto">
          <DialogHeader>
            <DialogTitle className="text-lg sm:text-xl">Update Order</DialogTitle>
          </DialogHeader>
          <div className="space-y-3 sm:space-y-4">
            <div>
              <Label htmlFor="status" className="text-sm">Order Status</Label>
              <Select value={updateData.status} onValueChange={(value) => setUpdateData(prev => ({ ...prev, status: value }))}>
                <SelectTrigger className="text-sm">
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="PENDING">Pending</SelectItem>
                  <SelectItem value="CONFIRMED">Confirmed</SelectItem>
                  <SelectItem value="PROCESSING">Processing</SelectItem>
                  <SelectItem value="SHIPPED">Shipped</SelectItem>
                  <SelectItem value="OUT_FOR_DELIVERY">Out for Delivery</SelectItem>
                  <SelectItem value="DELIVERED">Delivered</SelectItem>
                  <SelectItem value="CANCELLED">Cancelled</SelectItem>
                  <SelectItem value="RETURNED">Returned</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="paymentStatus" className="text-sm">Payment Status</Label>
              <Select value={updateData.paymentStatus} onValueChange={(value) => setUpdateData(prev => ({ ...prev, paymentStatus: value }))}>
                <SelectTrigger className="text-sm">
                  <SelectValue placeholder="Select payment status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="PENDING">Pending</SelectItem>
                  <SelectItem value="PAID">Paid</SelectItem>
                  <SelectItem value="COD_PENDING">COD Pending</SelectItem>
                  <SelectItem value="COD_COLLECTED">COD Collected</SelectItem>
                  <SelectItem value="FAILED">Failed</SelectItem>
                  <SelectItem value="REFUNDED">Refunded</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="trackingNumber" className="text-sm">Tracking Number</Label>
              <Input
                id="trackingNumber"
                value={updateData.trackingNumber}
                onChange={(e) => setUpdateData(prev => ({ ...prev, trackingNumber: e.target.value }))}
                placeholder="Enter tracking number"
                className="text-sm"
              />
            </div>

            <div>
              <Label htmlFor="courierPartner" className="text-sm">Courier Partner</Label>
              <Input
                id="courierPartner"
                value={updateData.courierPartner}
                onChange={(e) => setUpdateData(prev => ({ ...prev, courierPartner: e.target.value }))}
                placeholder="e.g., BlueDart, FedEx"
                className="text-sm"
              />
            </div>

            <div>
              <Label htmlFor="notes" className="text-sm">Admin Notes</Label>
              <Textarea
                id="notes"
                value={updateData.notes}
                onChange={(e) => setUpdateData(prev => ({ ...prev, notes: e.target.value }))}
                placeholder="Add admin notes"
                rows={3}
                className="text-sm"
              />
            </div>

            <div className="flex flex-col sm:flex-row gap-2 sm:gap-3">
              <Button onClick={handleUpdateOrder} className="flex-1 text-sm">
                Update Order
              </Button>
              <Button variant="outline" onClick={() => setIsUpdateDialogOpen(false)} className="text-sm">
                Cancel
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Delete Order Dialog */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent className="mx-4 sm:mx-auto">
          <AlertDialogHeader>
            <AlertDialogTitle className="text-lg sm:text-xl">Delete Order</AlertDialogTitle>
            <AlertDialogDescription className="text-sm">
              Are you sure you want to delete order #{selectedOrder?.orderNumber}? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel className="text-sm">Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleDeleteOrder} className="bg-red-600 hover:bg-red-700 text-sm">
              Delete Order
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}

// Admin Order Card Component
function AdminOrderCard({ order, getStatusColor, getPaymentStatusColor, onUpdate, onDelete }: {
  order: OrderData,
  getStatusColor: (status: string) => string,
  getPaymentStatusColor: (status: string) => string,
  onUpdate: (order: OrderData) => void,
  onDelete: (order: OrderData) => void
}) {
  const router = useRouter()

  return (
    <Card className="hover:shadow-lg transition-shadow">
      <CardHeader className="p-4 sm:p-6">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-start gap-3 sm:gap-4">
          <div className="flex-1 min-w-0">
            <CardTitle className="text-base sm:text-lg truncate">Order #{order.orderNumber}</CardTitle>
            <div className="flex flex-col sm:flex-row items-start sm:items-center gap-2 sm:gap-4 text-xs sm:text-sm text-gray-600 mt-1">
              <span className="flex items-center gap-1">
                <Calendar className="h-3 w-3 sm:h-4 sm:w-4" />
                {new Date(order.orderDate).toLocaleDateString()}
              </span>
              <span className="flex items-center gap-1">
                <CreditCard className="h-3 w-3 sm:h-4 sm:w-4" />
                <span className="truncate">{order.paymentMethod}</span>
              </span>
              <span className="flex items-center gap-1 min-w-0">
                <Users className="h-3 w-3 sm:h-4 sm:w-4 flex-shrink-0" />
                <span className="truncate">{order.customerName || 'N/A'}</span>
              </span>
            </div>
          </div>
          <div className="text-right">
            <div className="text-base sm:text-lg font-bold">₹{order.total.toLocaleString()}</div>
            <div className="flex flex-col sm:flex-row gap-1 sm:gap-2 mt-1">
              <Badge className={`${getStatusColor(order.status)} text-xs px-2 py-1`}>{order.status}</Badge>
              <Badge className={`${getPaymentStatusColor(order.paymentStatus)} text-xs px-2 py-1`}>
                {order.paymentStatus.replace('_', ' ')}
              </Badge>
            </div>
          </div>
        </div>
      </CardHeader>
      <CardContent className="p-4 sm:p-6">
        {/* Customer Information */}
        <div className="bg-gray-50 rounded-lg p-3 sm:p-4 mb-3 sm:mb-4">
          <h4 className="font-semibold text-xs sm:text-sm mb-2">Customer Information</h4>
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-2 sm:gap-3 text-xs sm:text-sm">
            <div className="flex items-center gap-2">
              <Mail className="h-3 w-3 sm:h-4 sm:w-4 text-gray-400" />
              <span className="truncate">{order.customerEmail || 'N/A'}</span>
            </div>
            <div className="flex items-center gap-2">
              <Phone className="h-3 w-3 sm:h-4 sm:w-4 text-gray-400" />
              <span className="truncate">{order.customerPhone || 'N/A'}</span>
            </div>
            <div className="flex items-center gap-2">
              <MapPin className="h-3 w-3 sm:h-4 sm:w-4 text-gray-400" />
              <span className="truncate">
                {order.shippingAddress?.city || 'N/A'}, {order.shippingAddress?.state || 'N/A'}
              </span>
            </div>
          </div>
        </div>

        {/* Order Items Summary */}
        <div className="space-y-2 mb-3 sm:mb-4">
          <h4 className="font-semibold text-xs sm:text-sm">Order Items ({order.items?.length || 0})</h4>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
            {order.items && order.items.length > 0 ? (
              <>
                {order.items.slice(0, 4).map((item, index) => (
                  <div key={index} className="flex items-center space-x-2 text-xs sm:text-sm">
                    <span className="font-medium">{item.quantity}x</span>
                    <span className="truncate">{item.productName}</span>
                    <span className="text-gray-500">₹{item.subtotal?.toLocaleString() || '0'}</span>
                  </div>
                ))}
                {order.items.length > 4 && (
                  <div className="text-xs sm:text-sm text-gray-500">+{order.items.length - 4} more items</div>
                )}
              </>
            ) : (
              <div className="text-xs sm:text-sm text-gray-500">No items found</div>
            )}
          </div>
        </div>

        {/* Order Summary */}
        <div className="bg-blue-50 rounded-lg p-3 sm:p-4 mb-3 sm:mb-4">
          <div className="grid grid-cols-2 sm:grid-cols-4 gap-2 sm:gap-4 text-xs sm:text-sm">
            <div>
              <span className="text-gray-600">Subtotal:</span>
              <div className="font-medium">₹{order.subtotal?.toLocaleString() || '0'}</div>
            </div>
            <div>
              <span className="text-gray-600">Shipping:</span>
              <div className="font-medium">₹{order.shippingCost?.toLocaleString() || '0'}</div>
            </div>
            <div>
              <span className="text-gray-600">Tax:</span>
              <div className="font-medium">₹{order.tax?.toLocaleString() || '0'}</div>
            </div>
            <div>
              <span className="text-gray-600">Total:</span>
              <div className="font-bold text-base sm:text-lg">₹{order.total?.toLocaleString() || '0'}</div>
            </div>
          </div>
        </div>

        {/* Tracking Information */}
        {(order.trackingNumber || order.courierPartner) && (
          <div className="bg-purple-50 rounded-lg p-3 sm:p-4 mb-3 sm:mb-4">
            <h4 className="font-semibold text-xs sm:text-sm mb-2">Tracking Information</h4>
            <div className="text-xs sm:text-sm">
              {order.trackingNumber && (
                <p><span className="font-medium">Tracking Number:</span> {order.trackingNumber}</p>
              )}
              {order.courierPartner && (
                <p><span className="font-medium">Courier Partner:</span> {order.courierPartner}</p>
              )}
            </div>
          </div>
        )}

        {/* Actions */}
        <div className="flex flex-col sm:flex-row gap-2 sm:gap-3">
          <Button
            variant="outline"
            size="sm"
            onClick={() => router.push(`/dashboard/order-confirmation?orderId=${order.id}`)}
            className="flex items-center gap-2 text-xs sm:text-sm"
          >
            <Eye className="h-3 w-3 sm:h-4 sm:w-4" />
            <span className="hidden sm:inline">View Details</span>
            <span className="sm:hidden">View</span>
          </Button>

          <Button
            size="sm"
            onClick={() => onUpdate(order)}
            className="flex items-center gap-2 bg-[#F6C244] hover:bg-[#F6C244]/90 text-black text-xs sm:text-sm"
          >
            <Edit className="h-3 w-3 sm:h-4 sm:w-4" />
            <span className="hidden sm:inline">Edit Order</span>
            <span className="sm:hidden">Edit</span>
          </Button>

          <Button
            variant="outline"
            size="sm"
            onClick={() => onDelete(order)}
            className="flex items-center gap-2 text-red-600 hover:text-red-700 border-red-200 hover:border-red-300 text-xs sm:text-sm"
          >
            <Trash2 className="h-3 w-3 sm:h-4 sm:w-4" />
            <span className="hidden sm:inline">Delete</span>
            <span className="sm:hidden">Del</span>
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}
