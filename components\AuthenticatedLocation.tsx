"use client"

import React from 'react'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  MapPin,
  LogIn,
  UserPlus,
  Lock,
  Shield,
  Eye,
  EyeOff
} from 'lucide-react'
import { useAuth } from '@/contexts/AuthContext'
import { useRouter } from 'next/navigation'

interface AuthenticatedLocationProps {
  entity: {
    id: string
    name: string
    location?: string
    city?: string
    state?: string
    pincode?: string
    fullAddress?: string
    address?: string
  }
  entityType: 'VENUE' | 'VENDOR'
}

export function AuthenticatedLocation({
  entity,
  entityType
}: AuthenticatedLocationProps) {
  const { isAuthenticated, isLoading } = useAuth()
  const router = useRouter()

  // Login prompt component for non-authenticated users
  const LoginPrompt = () => (
    <Card>
      <CardContent className="p-4 sm:p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-base sm:text-lg font-bold">Location</h3>
          <Badge variant="secondary" className="bg-amber-100 text-amber-800">
            <Lock className="w-3 h-3 mr-1" />
            Login Required
          </Badge>
        </div>
        
        <div className="text-center py-6">
          <div className="flex justify-center mb-4">
            <div className="w-16 h-16 bg-amber-100 rounded-full flex items-center justify-center">
              <EyeOff className="w-8 h-8 text-amber-600" />
            </div>
          </div>
          
          <h4 className="text-lg font-semibold text-gray-900 mb-2">
            Location Information Hidden
          </h4>
          
          <p className="text-sm text-gray-600 mb-6">
            {entityType === 'VENUE' 
              ? 'Sign in to view the exact venue location and address details.'
              : 'Sign in to view the vendor\'s exact location and contact address.'
            }
          </p>
          
          <div className="space-y-3">
            <Button
              onClick={() => router.push('/login')}
              className="w-full bg-primary hover:bg-primary/90"
            >
              <LogIn className="w-4 h-4 mr-2" />
              Sign In
            </Button>

          </div>
          
          <p className="text-xs text-gray-500 mt-4">
            <Shield className="w-3 h-3 inline mr-1" />
            Your privacy and security are our priority
          </p>
        </div>
      </CardContent>
    </Card>
  )

  // Loading state
  if (isLoading) {
    return (
      <Card>
        <CardContent className="p-4 sm:p-6">
          <div className="animate-pulse">
            <div className="h-6 bg-gray-200 rounded w-1/3 mb-4"></div>
            <div className="space-y-3">
              <div className="h-4 bg-gray-200 rounded"></div>
              <div className="h-4 bg-gray-200 rounded w-2/3"></div>
              <div className="h-4 bg-gray-200 rounded w-1/2"></div>
            </div>
            <div className="h-24 bg-gray-200 rounded-lg mt-4"></div>
          </div>
        </CardContent>
      </Card>
    )
  }

  // Show login prompt for non-authenticated users
  if (!isAuthenticated) {
    return <LoginPrompt />
  }

  // Show location information for authenticated users
  return (
    <Card>
      <CardContent className="p-4 sm:p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-base sm:text-lg font-bold">Location</h3>
          <Badge className="bg-green-100 text-green-800">
            <Eye className="w-3 h-3 mr-1" />
            Visible
          </Badge>
        </div>
        
        <div className="space-y-2 text-xs sm:text-sm">
          <div className="flex items-start">
            <MapPin className="h-4 w-4 text-gray-500 mr-2 mt-0.5 flex-shrink-0" />
            <div className="min-w-0">
              {entity.location && <p className="break-words">{entity.location}</p>}
              {entity.city && entity.state && <p className="break-words">{entity.city}, {entity.state}</p>}
              {entity.pincode && <p className="break-words">{entity.pincode}</p>}
              {entity.fullAddress && <p className="mt-1 break-words">{entity.fullAddress}</p>}
              {entity.address && <p className="mt-1 break-words">{entity.address}</p>}
              {!entity.location && !entity.city && !entity.state && !entity.fullAddress && !entity.address && (
                <p className="text-gray-500 italic">Location information not available</p>
              )}
            </div>
          </div>
        </div>
        
        <div className="mt-4 h-24 sm:h-32 bg-gray-200 rounded-lg flex items-center justify-center">
          <span className="text-gray-500 text-xs sm:text-sm">Map View</span>
        </div>
        
        <div className="mt-4 pt-4 border-t border-gray-200">
          <p className="text-xs text-gray-500 text-center">
            <Shield className="w-3 h-3 inline mr-1" />
            Location visible to authenticated users only
          </p>
        </div>
      </CardContent>
    </Card>
  )
} 