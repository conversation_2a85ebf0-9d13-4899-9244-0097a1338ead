import React from 'react';
import { View, Text, StyleSheet, Dimensions } from 'react-native';
import { <PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON> } from 'react-native-chart-kit';
import { useTheme } from '../../providers/ThemeProvider';
import type { CategoryData } from '../../services/analyticsService';

interface PieChartProps {
  data: CategoryData[];
  title?: string;
  height?: number;
  showLegend?: boolean;
  showPercentages?: boolean;
}

const { width: screenWidth } = Dimensions.get('window');

const PieChart: React.FC<PieChartProps> = ({
  data,
  title,
  height = 220,
  showLegend = true,
  showPercentages = true,
}) => {
  const { theme } = useTheme();

  if (!data || data.length === 0) {
    return (
      <View style={[styles.container, { backgroundColor: theme.colors.surface }]}>
        {title && (
          <Text style={[styles.title, { color: theme.colors.text }]}>{title}</Text>
        )}
        <View style={styles.noDataContainer}>
          <Text style={[styles.noDataText, { color: theme.colors.textSecondary }]}>
            No data available
          </Text>
        </View>
      </View>
    );
  }

  const chartData = data.map(item => ({
    name: item.name,
    population: item.value,
    color: item.color,
    legendFontColor: theme.colors.textSecondary,
    legendFontSize: 12,
  }));

  const chartConfig = {
    backgroundColor: theme.colors.surface,
    backgroundGradientFrom: theme.colors.surface,
    backgroundGradientTo: theme.colors.surface,
    color: (opacity = 1) => `rgba(0, 0, 0, ${opacity})`,
    labelColor: (opacity = 1) => theme.colors.textSecondary,
    style: {
      borderRadius: 16,
    },
  };

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.surface }]}>
      {title && (
        <Text style={[styles.title, { color: theme.colors.text }]}>{title}</Text>
      )}
      
      <View style={styles.chartContainer}>
        <RNPieChart
          data={chartData}
          width={screenWidth - 48}
          height={height}
          chartConfig={chartConfig}
          accessor="population"
          backgroundColor="transparent"
          paddingLeft="15"
          center={[10, 0]}
          absolute={!showPercentages}
        />
      </View>

      {showLegend && (
        <View style={styles.legendContainer}>
          {data.map((item, index) => (
            <View key={index} style={styles.legendItem}>
              <View 
                style={[
                  styles.legendColor, 
                  { backgroundColor: item.color }
                ]} 
              />
              <Text style={[styles.legendText, { color: theme.colors.text }]}>
                {item.name}
              </Text>
              <Text style={[styles.legendValue, { color: theme.colors.textSecondary }]}>
                {showPercentages ? `${item.percentage}%` : item.value.toLocaleString()}
              </Text>
            </View>
          ))}
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    borderRadius: 12,
    padding: 16,
    marginVertical: 8,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
  },
  chartContainer: {
    alignItems: 'center',
    overflow: 'hidden',
  },
  noDataContainer: {
    height: 220,
    justifyContent: 'center',
    alignItems: 'center',
  },
  noDataText: {
    fontSize: 14,
    fontStyle: 'italic',
  },
  legendContainer: {
    marginTop: 16,
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
  },
  legendItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  legendColor: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 12,
  },
  legendText: {
    flex: 1,
    fontSize: 14,
    fontWeight: '500',
  },
  legendValue: {
    fontSize: 14,
    fontWeight: '600',
  },
});

export default PieChart;
