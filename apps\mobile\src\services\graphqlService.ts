import { generateClient } from 'aws-amplify/api';

// Import all GraphQL operations from local copies
import * as queries from '../graphql/queries';
import * as mutations from '../graphql/mutations';
import * as subscriptions from '../graphql/subscriptions';
// Initialize the GraphQL client
const client = generateClient();

// Mobile GraphQL service using complete backend schema
export class GraphQLService {
  // ==================== VENDOR OPERATIONS ====================

  async getVendor(id: string) {
    try {
      const result = await client.graphql({
        query: queries.getVendor,
        variables: { id },
      });
      return result.data?.getVendor || null;
    } catch (error) {
      console.error('Error fetching vendor:', error);
      throw error;
    }
  }

  async listVendors(filter?: any, limit: number = 20, nextToken?: string) {
    try {
      const result = await client.graphql({
        query: queries.listVendors,
        variables: { filter, limit, nextToken },
      });
      return result.data?.listVendors || { items: [], nextToken: null };
    } catch (error) {
      console.error('Error listing vendors:', error);
      throw error;
    }
  }

  async vendorsByUserId(userId: string, limit: number = 20, nextToken?: string) {
    try {
      const result = await client.graphql({
        query: queries.vendorsByUserId,
        variables: { userId, limit, nextToken },
      });
      return result.data?.vendorsByUserId || { items: [], nextToken: null };
    } catch (error) {
      console.error('Error fetching vendors by user ID:', error);
      throw error;
    }
  }

  async createVendor(input: any) {
    try {
      const result = await client.graphql({
        query: mutations.createVendor,
        variables: { input },
      });
      return result.data?.createVendor || null;
    } catch (error) {
      console.error('Error creating vendor:', error);
      throw error;
    }
  }

  async updateVendor(input: any) {
    try {
      const result = await client.graphql({
        query: mutations.updateVendor,
        variables: { input },
      });
      return result.data?.updateVendor || null;
    } catch (error) {
      console.error('Error updating vendor:', error);
      throw error;
    }
  }

  async deleteVendor(input: any) {
    try {
      const result = await client.graphql({
        query: mutations.deleteVendor,
        variables: { input },
      });
      return result.data?.deleteVendor || null;
    } catch (error) {
      console.error('Error deleting vendor:', error);
      throw error;
    }
  }

  // ==================== VENUE OPERATIONS ====================

  async getVenue(id: string) {
    try {
      const result = await client.graphql({
        query: queries.getVenue,
        variables: { id },
      });
      return result.data?.getVenue || null;
    } catch (error) {
      console.error('Error fetching venue:', error);
      throw error;
    }
  }

  async listVenues(filter?: any, limit: number = 20, nextToken?: string) {
    try {
      const result = await client.graphql({
        query: queries.listVenues,
        variables: { filter, limit, nextToken },
      });
      return result.data?.listVenues || { items: [], nextToken: null };
    } catch (error) {
      console.error('Error listing venues:', error);
      throw error;
    }
  }

  async venuesByUserId(userId: string, limit: number = 20, nextToken?: string) {
    try {
      const result = await client.graphql({
        query: queries.venuesByUserId,
        variables: { userId, limit, nextToken },
      });
      return result.data?.venuesByUserId || { items: [], nextToken: null };
    } catch (error) {
      console.error('Error fetching venues by user ID:', error);
      throw error;
    }
  }

  async createVenue(input: any) {
    try {
      const result = await client.graphql({
        query: mutations.createVenue,
        variables: { input },
      });
      return result.data?.createVenue || null;
    } catch (error) {
      console.error('Error creating venue:', error);
      throw error;
    }
  }

  async updateVenue(input: any) {
    try {
      const result = await client.graphql({
        query: mutations.updateVenue,
        variables: { input },
      });
      return result.data?.updateVenue || null;
    } catch (error) {
      console.error('Error updating venue:', error);
      throw error;
    }
  }

  async deleteVenue(input: any) {
    try {
      const result = await client.graphql({
        query: mutations.deleteVenue,
        variables: { input },
      });
      return result.data?.deleteVenue || null;
    } catch (error) {
      console.error('Error deleting venue:', error);
      throw error;
    }
  }

  // ==================== SHOP OPERATIONS ====================

  async getShop(id: string) {
    try {
      const result = await client.graphql({
        query: queries.getShop,
        variables: { id },
      });
      return result.data?.getShop || null;
    } catch (error) {
      console.error('Error fetching shop:', error);
      throw error;
    }
  }

  async listShops(filter?: any, limit: number = 20, nextToken?: string) {
    try {
      const result = await client.graphql({
        query: queries.listShops,
        variables: { filter, limit, nextToken },
      });
      return result.data?.listShops || { items: [], nextToken: null };
    } catch (error) {
      console.error('Error listing shops:', error);
      throw error;
    }
  }

  async shopsByUserId(userId: string, limit: number = 20, nextToken?: string) {
    try {
      const result = await client.graphql({
        query: queries.shopsByUserId,
        variables: { userId, limit, nextToken },
      });
      return result.data?.shopsByUserId || { items: [], nextToken: null };
    } catch (error) {
      console.error('Error fetching shops by user ID:', error);
      throw error;
    }
  }

  async createShop(input: any) {
    try {
      const result = await client.graphql({
        query: mutations.createShop,
        variables: { input },
      });
      return result.data?.createShop || null;
    } catch (error) {
      console.error('Error creating shop:', error);
      throw error;
    }
  }

  async updateShop(input: any) {
    try {
      const result = await client.graphql({
        query: mutations.updateShop,
        variables: { input },
      });
      return result.data?.updateShop || null;
    } catch (error) {
      console.error('Error updating shop:', error);
      throw error;
    }
  }

  async deleteShop(input: any) {
    try {
      const result = await client.graphql({
        query: mutations.deleteShop,
        variables: { input },
      });
      return result.data?.deleteShop || null;
    } catch (error) {
      console.error('Error deleting shop:', error);
      throw error;
    }
  }

  // Alias for backward compatibility
  async listProducts(filter?: any, limit: number = 20, nextToken?: string) {
    return this.listShops(filter, limit, nextToken);
  }

  // ==================== REVIEW OPERATIONS ====================

  async getReview(id: string) {
    try {
      const result = await client.graphql({
        query: queries.getReview,
        variables: { id },
      });
      return result.data?.getReview || null;
    } catch (error) {
      console.error('Error fetching review:', error);
      throw error;
    }
  }

  async listReviews(filter?: any, limit: number = 20, nextToken?: string) {
    try {
      const result = await client.graphql({
        query: queries.listReviews,
        variables: { filter, limit, nextToken },
      });
      return result.data?.listReviews || { items: [], nextToken: null };
    } catch (error) {
      console.error('Error listing reviews:', error);
      throw error;
    }
  }

  async reviewsByUserId(userId: string, limit: number = 20, nextToken?: string) {
    try {
      const result = await client.graphql({
        query: queries.reviewsByUserId,
        variables: { userId, limit, nextToken },
      });
      return result.data?.reviewsByUserId || { items: [], nextToken: null };
    } catch (error) {
      console.error('Error fetching reviews by user ID:', error);
      throw error;
    }
  }

  async reviewsByEntityIdAndCreatedAt(entityId: string, createdAt?: any, limit: number = 20, nextToken?: string) {
    try {
      const result = await client.graphql({
        query: queries.reviewsByEntityIdAndCreatedAt,
        variables: { entityId, createdAt, limit, nextToken },
      });
      return result.data?.reviewsByEntityIdAndCreatedAt || { items: [], nextToken: null };
    } catch (error) {
      console.error('Error fetching reviews by entity ID and created at:', error);
      throw error;
    }
  }

  async createReview(input: any) {
    try {
      const result = await client.graphql({
        query: mutations.createReview,
        variables: { input },
      });
      return result.data?.createReview || null;
    } catch (error) {
      console.error('Error creating review:', error);
      throw error;
    }
  }

  async updateReview(input: any) {
    try {
      const result = await client.graphql({
        query: mutations.updateReview,
        variables: { input },
      });
      return result.data?.updateReview || null;
    } catch (error) {
      console.error('Error updating review:', error);
      throw error;
    }
  }

  async deleteReview(input: any) {
    try {
      const result = await client.graphql({
        query: mutations.deleteReview,
        variables: { input },
      });
      return result.data?.deleteReview || null;
    } catch (error) {
      console.error('Error deleting review:', error);
      throw error;
    }
  }

  // ==================== USER PROFILE OPERATIONS ====================

  async getUserProfile(id: string) {
    try {
      const result = await client.graphql({
        query: queries.getUserProfile,
        variables: { id },
      });
      return result.data?.getUserProfile || null;
    } catch (error) {
      console.error('Error fetching user profile:', error);
      throw error;
    }
  }

  async listUserProfiles(filter?: any, limit: number = 20, nextToken?: string) {
    try {
      const result = await client.graphql({
        query: queries.listUserProfiles,
        variables: { filter, limit, nextToken },
      });
      return result.data?.listUserProfiles || { items: [], nextToken: null };
    } catch (error) {
      console.error('Error listing user profiles:', error);
      throw error;
    }
  }

  async userProfilesByUserId(userId: string, limit: number = 20, nextToken?: string) {
    try {
      const result = await client.graphql({
        query: queries.userProfilesByUserId,
        variables: { userId, limit, nextToken },
      });
      return result.data?.userProfilesByUserId || { items: [], nextToken: null };
    } catch (error) {
      console.error('Error fetching user profiles by user ID:', error);
      throw error;
    }
  }

  async createUserProfile(input: any) {
    try {
      const result = await client.graphql({
        query: mutations.createUserProfile,
        variables: { input },
      });
      return result.data?.createUserProfile || null;
    } catch (error) {
      console.error('Error creating user profile:', error);
      throw error;
    }
  }

  async updateUserProfile(input: any) {
    try {
      const result = await client.graphql({
        query: mutations.updateUserProfile,
        variables: { input },
      });
      return result.data?.updateUserProfile || null;
    } catch (error) {
      console.error('Error updating user profile:', error);
      throw error;
    }
  }

  async deleteUserProfile(input: any) {
    try {
      const result = await client.graphql({
        query: mutations.deleteUserProfile,
        variables: { input },
      });
      return result.data?.deleteUserProfile || null;
    } catch (error) {
      console.error('Error deleting user profile:', error);
      throw error;
    }
  }

  // ==================== CONTACT OPERATIONS ====================

  async getContact(id: string) {
    try {
      const result = await client.graphql({
        query: queries.getContact,
        variables: { id },
      });
      return result.data?.getContact || null;
    } catch (error) {
      console.error('Error fetching contact:', error);
      throw error;
    }
  }

  async listContacts(filter?: any, limit: number = 20, nextToken?: string) {
    try {
      const result = await client.graphql({
        query: queries.listContacts,
        variables: { filter, limit, nextToken },
      });
      return result.data?.listContacts || { items: [], nextToken: null };
    } catch (error) {
      console.error('Error listing contacts:', error);
      throw error;
    }
  }

  async createContact(input: any) {
    try {
      const result = await client.graphql({
        query: mutations.createContact,
        variables: { input },
      });
      return result.data?.createContact || null;
    } catch (error) {
      console.error('Error creating contact:', error);
      throw error;
    }
  }

  async updateContact(input: any) {
    try {
      const result = await client.graphql({
        query: mutations.updateContact,
        variables: { input },
      });
      return result.data?.updateContact || null;
    } catch (error) {
      console.error('Error updating contact:', error);
      throw error;
    }
  }

  async deleteContact(input: any) {
    try {
      const result = await client.graphql({
        query: mutations.deleteContact,
        variables: { input },
      });
      return result.data?.deleteContact || null;
    } catch (error) {
      console.error('Error deleting contact:', error);
      throw error;
    }
  }

  // ==================== INQUIRY OPERATIONS ====================

  async getInquiry(id: string) {
    try {
      const result = await client.graphql({
        query: queries.getInquiry,
        variables: { id },
      });
      return result.data?.getInquiry || null;
    } catch (error) {
      console.error('Error fetching inquiry:', error);
      throw error;
    }
  }

  async listInquiries(filter?: any, limit: number = 20, nextToken?: string) {
    try {
      const result = await client.graphql({
        query: queries.listInquiries,
        variables: { filter, limit, nextToken },
      });
      return result.data?.listInquiries || { items: [], nextToken: null };
    } catch (error) {
      console.error('Error listing inquiries:', error);
      throw error;
    }
  }

  async inquiriesByVendorUserId(vendorUserId: string, limit: number = 20, nextToken?: string) {
    try {
      const result = await client.graphql({
        query: queries.inquiriesByVendorUserId,
        variables: { vendorUserId, limit, nextToken },
      });
      return result.data?.inquiriesByVendorUserId || { items: [], nextToken: null };
    } catch (error) {
      console.error('Error fetching inquiries by vendor user ID:', error);
      throw error;
    }
  }

  async inquiriesByCustomerUserId(customerUserId: string, limit: number = 20, nextToken?: string) {
    try {
      const result = await client.graphql({
        query: queries.inquiriesByCustomerUserId,
        variables: { customerUserId, limit, nextToken },
      });
      return result.data?.inquiriesByCustomerUserId || { items: [], nextToken: null };
    } catch (error) {
      console.error('Error fetching inquiries by customer user ID:', error);
      throw error;
    }
  }

  async createInquiry(input: any) {
    try {
      const result = await client.graphql({
        query: mutations.createInquiry,
        variables: { input },
      });
      return result.data?.createInquiry || null;
    } catch (error) {
      console.error('Error creating inquiry:', error);
      throw error;
    }
  }

  async updateInquiry(input: any) {
    try {
      const result = await client.graphql({
        query: mutations.updateInquiry,
        variables: { input },
      });
      return result.data?.updateInquiry || null;
    } catch (error) {
      console.error('Error updating inquiry:', error);
      throw error;
    }
  }

  async deleteInquiry(input: any) {
    try {
      const result = await client.graphql({
        query: mutations.deleteInquiry,
        variables: { input },
      });
      return result.data?.deleteInquiry || null;
    } catch (error) {
      console.error('Error deleting inquiry:', error);
      throw error;
    }
  }

  // ==================== BLOG OPERATIONS ====================

  async getBlog(id: string) {
    try {
      const result = await client.graphql({
        query: queries.getBlog,
        variables: { id },
      });
      return result.data?.getBlog || null;
    } catch (error) {
      console.error('Error fetching blog:', error);
      throw error;
    }
  }

  async listBlogs(filter?: any, limit: number = 20, nextToken?: string) {
    try {
      const result = await client.graphql({
        query: queries.listBlogs,
        variables: { filter, limit, nextToken },
      });
      return result.data?.listBlogs || { items: [], nextToken: null };
    } catch (error) {
      console.error('Error listing blogs:', error);
      throw error;
    }
  }

  async blogsByAuthorId(authorId: string, limit: number = 20, nextToken?: string) {
    try {
      const result = await client.graphql({
        query: queries.blogsByAuthorId,
        variables: { authorId, limit, nextToken },
      });
      return result.data?.blogsByAuthorId || { items: [], nextToken: null };
    } catch (error) {
      console.error('Error fetching blogs by author ID:', error);
      throw error;
    }
  }

  async createBlog(input: any) {
    try {
      const result = await client.graphql({
        query: mutations.createBlog,
        variables: { input },
      });
      return result.data?.createBlog || null;
    } catch (error) {
      console.error('Error creating blog:', error);
      throw error;
    }
  }

  async updateBlog(input: any) {
    try {
      const result = await client.graphql({
        query: mutations.updateBlog,
        variables: { input },
      });
      return result.data?.updateBlog || null;
    } catch (error) {
      console.error('Error updating blog:', error);
      throw error;
    }
  }

  async deleteBlog(input: any) {
    try {
      const result = await client.graphql({
        query: mutations.deleteBlog,
        variables: { input },
      });
      return result.data?.deleteBlog || null;
    } catch (error) {
      console.error('Error deleting blog:', error);
      throw error;
    }
  }

  // ==================== BOOKING OPERATIONS ====================

  async getBooking(id: string) {
    try {
      const result = await client.graphql({
        query: queries.getBooking,
        variables: { id },
      });
      return result.data?.getBooking || null;
    } catch (error) {
      console.error('Error fetching booking:', error);
      throw error;
    }
  }

  async listBookings(filter?: any, limit: number = 20, nextToken?: string) {
    try {
      const result = await client.graphql({
        query: queries.listBookings,
        variables: { filter, limit, nextToken },
      });
      return result.data?.listBookings || { items: [], nextToken: null };
    } catch (error) {
      console.error('Error listing bookings:', error);
      throw error;
    }
  }

  async bookingsByCustomerId(customerId: string, limit: number = 20, nextToken?: string) {
    try {
      const result = await client.graphql({
        query: queries.bookingsByCustomerId,
        variables: { customerId, limit, nextToken },
      });
      return result.data?.bookingsByCustomerId || { items: [], nextToken: null };
    } catch (error) {
      console.error('Error fetching bookings by customer ID:', error);
      throw error;
    }
  }

  async bookingsByVendorId(vendorId: string, limit: number = 20, nextToken?: string) {
    try {
      const result = await client.graphql({
        query: queries.bookingsByVendorId,
        variables: { vendorId, limit, nextToken },
      });
      return result.data?.bookingsByVendorId || { items: [], nextToken: null };
    } catch (error) {
      console.error('Error fetching bookings by vendor ID:', error);
      throw error;
    }
  }

  async createBooking(input: any) {
    try {
      const result = await client.graphql({
        query: mutations.createBooking,
        variables: { input },
      });
      return result.data?.createBooking || null;
    } catch (error) {
      console.error('Error creating booking:', error);
      throw error;
    }
  }

  async updateBooking(input: any) {
    try {
      const result = await client.graphql({
        query: mutations.updateBooking,
        variables: { input },
      });
      return result.data?.updateBooking || null;
    } catch (error) {
      console.error('Error updating booking:', error);
      throw error;
    }
  }

  async deleteBooking(input: any) {
    try {
      const result = await client.graphql({
        query: mutations.deleteBooking,
        variables: { input },
      });
      return result.data?.deleteBooking || null;
    } catch (error) {
      console.error('Error deleting booking:', error);
      throw error;
    }
  }

  // ==================== NEWSLETTER OPERATIONS ====================

  async getNewsletterSubscription(id: string) {
    try {
      const result = await client.graphql({
        query: queries.getNewsletterSubscription,
        variables: { id },
      });
      return result.data?.getNewsletterSubscription || null;
    } catch (error) {
      console.error('Error fetching newsletter subscription:', error);
      throw error;
    }
  }

  async listNewsletterSubscriptions(filter?: any, limit: number = 20, nextToken?: string) {
    try {
      const result = await client.graphql({
        query: queries.listNewsletterSubscriptions,
        variables: { filter, limit, nextToken },
      });
      return result.data?.listNewsletterSubscriptions || { items: [], nextToken: null };
    } catch (error) {
      console.error('Error listing newsletter subscriptions:', error);
      throw error;
    }
  }

  async newsletterSubscriptionsByEmail(email: string, limit: number = 20, nextToken?: string) {
    try {
      const result = await client.graphql({
        query: queries.newsletterSubscriptionsByEmail,
        variables: { email, limit, nextToken },
      });
      return result.data?.newsletterSubscriptionsByEmail || { items: [], nextToken: null };
    } catch (error) {
      console.error('Error fetching newsletter subscriptions by email:', error);
      throw error;
    }
  }

  async createNewsletterSubscription(input: any) {
    try {
      const result = await client.graphql({
        query: mutations.createNewsletterSubscription,
        variables: { input },
      });
      return result.data?.createNewsletterSubscription || null;
    } catch (error) {
      console.error('Error creating newsletter subscription:', error);
      throw error;
    }
  }

  async updateNewsletterSubscription(input: any) {
    try {
      const result = await client.graphql({
        query: mutations.updateNewsletterSubscription,
        variables: { input },
      });
      return result.data?.updateNewsletterSubscription || null;
    } catch (error) {
      console.error('Error updating newsletter subscription:', error);
      throw error;
    }
  }

  async deleteNewsletterSubscription(input: any) {
    try {
      const result = await client.graphql({
        query: mutations.deleteNewsletterSubscription,
        variables: { input },
      });
      return result.data?.deleteNewsletterSubscription || null;
    } catch (error) {
      console.error('Error deleting newsletter subscription:', error);
      throw error;
    }
  }

  // ==================== CART OPERATIONS ====================

  async getCartItem(id: string) {
    try {
      const result = await client.graphql({
        query: queries.getCartItem,
        variables: { id },
      });
      return result.data?.getCartItem || null;
    } catch (error) {
      console.error('Error fetching cart item:', error);
      throw error;
    }
  }

  async listCartItems(filter?: any, limit: number = 20, nextToken?: string) {
    try {
      const result = await client.graphql({
        query: queries.listCartItems,
        variables: { filter, limit, nextToken },
      });
      return result.data?.listCartItems || { items: [], nextToken: null };
    } catch (error) {
      console.error('Error listing cart items:', error);
      throw error;
    }
  }

  async cartItemsByUserId(userId: string, limit: number = 20, nextToken?: string) {
    try {
      const result = await client.graphql({
        query: queries.cartItemsByUserId,
        variables: { userId, limit, nextToken },
      });
      return result.data?.cartItemsByUserId || { items: [], nextToken: null };
    } catch (error) {
      console.error('Error fetching cart items by user ID:', error);
      throw error;
    }
  }

  async createCartItem(input: any) {
    try {
      const result = await client.graphql({
        query: mutations.createCartItem,
        variables: { input },
      });
      return result.data?.createCartItem || null;
    } catch (error) {
      console.error('Error creating cart item:', error);
      throw error;
    }
  }

  async updateCartItem(input: any) {
    try {
      const result = await client.graphql({
        query: mutations.updateCartItem,
        variables: { input },
      });
      return result.data?.updateCartItem || null;
    } catch (error) {
      console.error('Error updating cart item:', error);
      throw error;
    }
  }

  async deleteCartItem(input: any) {
    try {
      const result = await client.graphql({
        query: mutations.deleteCartItem,
        variables: { input },
      });
      return result.data?.deleteCartItem || null;
    } catch (error) {
      console.error('Error deleting cart item:', error);
      throw error;
    }
  }

  // ==================== ORDER OPERATIONS ====================

  async getOrder(id: string) {
    try {
      const result = await client.graphql({
        query: queries.getOrder,
        variables: { id },
      });
      return result.data?.getOrder || null;
    } catch (error) {
      console.error('Error fetching order:', error);
      throw error;
    }
  }

  async listOrders(filter?: any, limit: number = 20, nextToken?: string) {
    try {
      const result = await client.graphql({
        query: queries.listOrders,
        variables: { filter, limit, nextToken },
      });
      return result.data?.listOrders || { items: [], nextToken: null };
    } catch (error) {
      console.error('Error listing orders:', error);
      throw error;
    }
  }

  async ordersByUserId(userId: string, limit: number = 20, nextToken?: string) {
    try {
      const result = await client.graphql({
        query: queries.ordersByUserId,
        variables: { userId, limit, nextToken },
      });
      return result.data?.ordersByUserId || { items: [], nextToken: null };
    } catch (error) {
      console.error('Error fetching orders by user ID:', error);
      throw error;
    }
  }

  async createOrder(input: any) {
    try {
      const result = await client.graphql({
        query: mutations.createOrder,
        variables: { input },
      });
      return result.data?.createOrder || null;
    } catch (error) {
      console.error('Error creating order:', error);
      throw error;
    }
  }

  async updateOrder(input: any) {
    try {
      const result = await client.graphql({
        query: mutations.updateOrder,
        variables: { input },
      });
      return result.data?.updateOrder || null;
    } catch (error) {
      console.error('Error updating order:', error);
      throw error;
    }
  }

  async deleteOrder(input: any) {
    try {
      const result = await client.graphql({
        query: mutations.deleteOrder,
        variables: { input },
      });
      return result.data?.deleteOrder || null;
    } catch (error) {
      console.error('Error deleting order:', error);
      throw error;
    }
  }

  // ==================== FAVORITES OPERATIONS ====================

  async getFavorite(id: string) {
    try {
      const result = await client.graphql({
        query: queries.getFavorite,
        variables: { id },
      });
      return result.data?.getFavorite || null;
    } catch (error) {
      console.error('Error fetching favorite:', error);
      throw error;
    }
  }

  async listFavorites(filter?: any, limit: number = 20, nextToken?: string) {
    try {
      const result = await client.graphql({
        query: queries.listFavorites,
        variables: { filter, limit, nextToken },
      });
      return result.data?.listFavorites || { items: [], nextToken: null };
    } catch (error) {
      console.error('Error listing favorites:', error);
      throw error;
    }
  }

  async favoritesByUserId(userId: string, limit: number = 20, nextToken?: string) {
    try {
      const result = await client.graphql({
        query: queries.favoritesByUserId,
        variables: { userId, limit, nextToken },
      });
      return result.data?.favoritesByUserId || { items: [], nextToken: null };
    } catch (error) {
      console.error('Error fetching favorites by user ID:', error);
      throw error;
    }
  }

  async createFavorite(input: any) {
    try {
      const result = await client.graphql({
        query: mutations.createFavorite,
        variables: { input },
      });
      return result.data?.createFavorite || null;
    } catch (error) {
      console.error('Error creating favorite:', error);
      throw error;
    }
  }

  async updateFavorite(input: any) {
    try {
      const result = await client.graphql({
        query: mutations.updateFavorite,
        variables: { input },
      });
      return result.data?.updateFavorite || null;
    } catch (error) {
      console.error('Error updating favorite:', error);
      throw error;
    }
  }

  async deleteFavorite(input: any) {
    try {
      const result = await client.graphql({
        query: mutations.deleteFavorite,
        variables: { input },
      });
      return result.data?.deleteFavorite || null;
    } catch (error) {
      console.error('Error deleting favorite:', error);
      throw error;
    }
  }

  // ==================== PAYMENT OPERATIONS ====================

  async getPayment(id: string) {
    try {
      const result = await client.graphql({
        query: queries.getPayment,
        variables: { id },
      });
      return result.data?.getPayment || null;
    } catch (error) {
      console.error('Error fetching payment:', error);
      throw error;
    }
  }

  async listPayments(filter?: any, limit: number = 20, nextToken?: string) {
    try {
      const result = await client.graphql({
        query: queries.listPayments,
        variables: { filter, limit, nextToken },
      });
      return result.data?.listPayments || { items: [], nextToken: null };
    } catch (error) {
      console.error('Error listing payments:', error);
      throw error;
    }
  }

  async paymentsByOrderId(orderId: string, limit: number = 20, nextToken?: string) {
    try {
      const result = await client.graphql({
        query: queries.paymentsByOrderId,
        variables: { orderId, limit, nextToken },
      });
      return result.data?.paymentsByOrderId || { items: [], nextToken: null };
    } catch (error) {
      console.error('Error fetching payments by order ID:', error);
      throw error;
    }
  }

  async createPayment(input: any) {
    try {
      const result = await client.graphql({
        query: mutations.createPayment,
        variables: { input },
      });
      return result.data?.createPayment || null;
    } catch (error) {
      console.error('Error creating payment:', error);
      throw error;
    }
  }

  async updatePayment(input: any) {
    try {
      const result = await client.graphql({
        query: mutations.updatePayment,
        variables: { input },
      });
      return result.data?.updatePayment || null;
    } catch (error) {
      console.error('Error updating payment:', error);
      throw error;
    }
  }

  async deletePayment(input: any) {
    try {
      const result = await client.graphql({
        query: mutations.deletePayment,
        variables: { input },
      });
      return result.data?.deletePayment || null;
    } catch (error) {
      console.error('Error deleting payment:', error);
      throw error;
    }
  }

  // ==================== WEDDING PLAN OPERATIONS ====================

  async getWeddingPlan(id: string) {
    try {
      const result = await client.graphql({
        query: queries.getWeddingPlan,
        variables: { id },
      });
      return result.data?.getWeddingPlan || null;
    } catch (error) {
      console.error('Error fetching wedding plan:', error);
      throw error;
    }
  }

  async listWeddingPlans(filter?: any, limit: number = 20, nextToken?: string) {
    try {
      const result = await client.graphql({
        query: queries.listWeddingPlans,
        variables: { filter, limit, nextToken },
      });
      return result.data?.listWeddingPlans || { items: [], nextToken: null };
    } catch (error) {
      console.error('Error listing wedding plans:', error);
      throw error;
    }
  }

  async weddingPlansByUserId(userId: string, limit: number = 20, nextToken?: string) {
    try {
      const result = await client.graphql({
        query: queries.weddingPlansByUserId,
        variables: { userId, limit, nextToken },
      });
      return result.data?.weddingPlansByUserId || { items: [], nextToken: null };
    } catch (error) {
      console.error('Error fetching wedding plans by user ID:', error);
      throw error;
    }
  }

  async createWeddingPlan(input: any) {
    try {
      const result = await client.graphql({
        query: mutations.createWeddingPlan,
        variables: { input },
      });
      return result.data?.createWeddingPlan || null;
    } catch (error) {
      console.error('Error creating wedding plan:', error);
      throw error;
    }
  }

  async updateWeddingPlan(input: any) {
    try {
      const result = await client.graphql({
        query: mutations.updateWeddingPlan,
        variables: { input },
      });
      return result.data?.updateWeddingPlan || null;
    } catch (error) {
      console.error('Error updating wedding plan:', error);
      throw error;
    }
  }

  async deleteWeddingPlan(input: any) {
    try {
      const result = await client.graphql({
        query: mutations.deleteWeddingPlan,
        variables: { input },
      });
      return result.data?.deleteWeddingPlan || null;
    } catch (error) {
      console.error('Error deleting wedding plan:', error);
      throw error;
    }
  }

  // ==================== BUDGET OPERATIONS ====================

  async getBudget(id: string) {
    try {
      const result = await client.graphql({
        query: queries.getBudget,
        variables: { id },
      });
      return result.data?.getBudget || null;
    } catch (error) {
      console.error('Error fetching budget:', error);
      throw error;
    }
  }

  async listBudgets(filter?: any, limit: number = 20, nextToken?: string) {
    try {
      const result = await client.graphql({
        query: queries.listBudgets,
        variables: { filter, limit, nextToken },
      });
      return result.data?.listBudgets || { items: [], nextToken: null };
    } catch (error) {
      console.error('Error listing budgets:', error);
      throw error;
    }
  }

  async budgetsByUserId(userId: string, limit: number = 20, nextToken?: string) {
    try {
      const result = await client.graphql({
        query: queries.budgetsByUserId,
        variables: { userId, limit, nextToken },
      });
      return result.data?.budgetsByUserId || { items: [], nextToken: null };
    } catch (error) {
      console.error('Error fetching budgets by user ID:', error);
      throw error;
    }
  }

  async createBudget(input: any) {
    try {
      const result = await client.graphql({
        query: mutations.createBudget,
        variables: { input },
      });
      return result.data?.createBudget || null;
    } catch (error) {
      console.error('Error creating budget:', error);
      throw error;
    }
  }

  async updateBudget(input: any) {
    try {
      const result = await client.graphql({
        query: mutations.updateBudget,
        variables: { input },
      });
      return result.data?.updateBudget || null;
    } catch (error) {
      console.error('Error updating budget:', error);
      throw error;
    }
  }

  async deleteBudget(input: any) {
    try {
      const result = await client.graphql({
        query: mutations.deleteBudget,
        variables: { input },
      });
      return result.data?.deleteBudget || null;
    } catch (error) {
      console.error('Error deleting budget:', error);
      throw error;
    }
  }

  // ==================== BUDGET DATA OPERATIONS ====================

  async getBudgetData(id: string) {
    try {
      const result = await client.graphql({
        query: queries.getBudgetData,
        variables: { id },
      });
      return result.data?.getBudgetData || null;
    } catch (error) {
      console.error('Error fetching budget data:', error);
      throw error;
    }
  }

  async listBudgetData(filter?: any, limit: number = 20, nextToken?: string) {
    try {
      const result = await client.graphql({
        query: queries.listBudgetData,
        variables: { filter, limit, nextToken },
      });
      return result.data?.listBudgetData || { items: [], nextToken: null };
    } catch (error) {
      console.error('Error listing budget data:', error);
      throw error;
    }
  }

  async budgetDataByUserId(userId: string, limit: number = 20, nextToken?: string) {
    try {
      const result = await client.graphql({
        query: queries.budgetDataByUserId,
        variables: { userId, limit, nextToken },
      });
      return result.data?.budgetDataByUserId || { items: [], nextToken: null };
    } catch (error) {
      console.error('Error fetching budget data by user ID:', error);
      throw error;
    }
  }

  async createBudgetData(input: any) {
    try {
      const result = await client.graphql({
        query: mutations.createBudgetData,
        variables: { input },
      });
      return result.data?.createBudgetData || null;
    } catch (error) {
      console.error('Error creating budget data:', error);
      throw error;
    }
  }

  async updateBudgetData(input: any) {
    try {
      const result = await client.graphql({
        query: mutations.updateBudgetData,
        variables: { input },
      });
      return result.data?.updateBudgetData || null;
    } catch (error) {
      console.error('Error updating budget data:', error);
      throw error;
    }
  }

  async deleteBudgetData(input: any) {
    try {
      const result = await client.graphql({
        query: mutations.deleteBudgetData,
        variables: { input },
      });
      return result.data?.deleteBudgetData || null;
    } catch (error) {
      console.error('Error deleting budget data:', error);
      throw error;
    }
  }

  // ==================== GUEST LIST OPERATIONS ====================

  async getGuestListData(id: string) {
    try {
      const result = await client.graphql({
        query: queries.getGuestListData,
        variables: { id },
      });
      return result.data?.getGuestListData || null;
    } catch (error) {
      console.error('Error fetching guest list data:', error);
      throw error;
    }
  }

  async listGuestListData(filter?: any, limit: number = 20, nextToken?: string) {
    try {
      const result = await client.graphql({
        query: queries.listGuestListData,
        variables: { filter, limit, nextToken },
      });
      return result.data?.listGuestListData || { items: [], nextToken: null };
    } catch (error) {
      console.error('Error listing guest list data:', error);
      throw error;
    }
  }

  async guestListDataByUserId(userId: string, limit: number = 20, nextToken?: string) {
    try {
      const result = await client.graphql({
        query: queries.guestListDataByUserId,
        variables: { userId, limit, nextToken },
      });
      return result.data?.guestListDataByUserId || { items: [], nextToken: null };
    } catch (error) {
      console.error('Error fetching guest list data by user ID:', error);
      throw error;
    }
  }

  async createGuestListData(input: any) {
    try {
      const result = await client.graphql({
        query: mutations.createGuestListData,
        variables: { input },
      });
      return result.data?.createGuestListData || null;
    } catch (error) {
      console.error('Error creating guest list data:', error);
      throw error;
    }
  }

  async updateGuestListData(input: any) {
    try {
      const result = await client.graphql({
        query: mutations.updateGuestListData,
        variables: { input },
      });
      return result.data?.updateGuestListData || null;
    } catch (error) {
      console.error('Error updating guest list data:', error);
      throw error;
    }
  }

  async deleteGuestListData(input: any) {
    try {
      const result = await client.graphql({
        query: mutations.deleteGuestListData,
        variables: { input },
      });
      return result.data?.deleteGuestListData || null;
    } catch (error) {
      console.error('Error deleting guest list data:', error);
      throw error;
    }
  }

  // ==================== PLANNING TOOLS OPERATIONS ====================

  async getPlanningToolsData(id: string) {
    try {
      const result = await client.graphql({
        query: queries.getPlanningToolsData,
        variables: { id },
      });
      return result.data?.getPlanningToolsData || null;
    } catch (error) {
      console.error('Error fetching planning tools data:', error);
      throw error;
    }
  }

  async listPlanningToolsData(filter?: any, limit: number = 20, nextToken?: string) {
    try {
      const result = await client.graphql({
        query: queries.listPlanningToolsData,
        variables: { filter, limit, nextToken },
      });
      return result.data?.listPlanningToolsData || { items: [], nextToken: null };
    } catch (error) {
      console.error('Error listing planning tools data:', error);
      throw error;
    }
  }

  async planningToolsDataByUserId(userId: string, limit: number = 20, nextToken?: string) {
    try {
      const result = await client.graphql({
        query: queries.planningToolsDataByUserId,
        variables: { userId, limit, nextToken },
      });
      return result.data?.planningToolsDataByUserId || { items: [], nextToken: null };
    } catch (error) {
      console.error('Error fetching planning tools data by user ID:', error);
      throw error;
    }
  }

  async createPlanningToolsData(input: any) {
    try {
      const result = await client.graphql({
        query: mutations.createPlanningToolsData,
        variables: { input },
      });
      return result.data?.createPlanningToolsData || null;
    } catch (error) {
      console.error('Error creating planning tools data:', error);
      throw error;
    }
  }

  async updatePlanningToolsData(input: any) {
    try {
      const result = await client.graphql({
        query: mutations.updatePlanningToolsData,
        variables: { input },
      });
      return result.data?.updatePlanningToolsData || null;
    } catch (error) {
      console.error('Error updating planning tools data:', error);
      throw error;
    }
  }

  async deletePlanningToolsData(input: any) {
    try {
      const result = await client.graphql({
        query: mutations.deletePlanningToolsData,
        variables: { input },
      });
      return result.data?.deletePlanningToolsData || null;
    } catch (error) {
      console.error('Error deleting planning tools data:', error);
      throw error;
    }
  }

  // ==================== CHECKLIST OPERATIONS ====================

  async getChecklistItem(id: string) {
    try {
      const result = await client.graphql({
        query: queries.getChecklistItem,
        variables: { id },
      });
      return result.data?.getChecklistItem || null;
    } catch (error) {
      console.error('Error fetching checklist item:', error);
      throw error;
    }
  }

  async listChecklistItems(filter?: any, limit: number = 20, nextToken?: string) {
    try {
      const result = await client.graphql({
        query: queries.listChecklistItems,
        variables: { filter, limit, nextToken },
      });
      return result.data?.listChecklistItems || { items: [], nextToken: null };
    } catch (error) {
      console.error('Error listing checklist items:', error);
      throw error;
    }
  }

  async checklistItemsByUserId(userId: string, limit: number = 20, nextToken?: string) {
    try {
      const result = await client.graphql({
        query: queries.checklistItemsByUserId,
        variables: { userId, limit, nextToken },
      });
      return result.data?.checklistItemsByUserId || { items: [], nextToken: null };
    } catch (error) {
      console.error('Error fetching checklist items by user ID:', error);
      throw error;
    }
  }

  async createChecklistItem(input: any) {
    try {
      const result = await client.graphql({
        query: mutations.createChecklistItem,
        variables: { input },
      });
      return result.data?.createChecklistItem || null;
    } catch (error) {
      console.error('Error creating checklist item:', error);
      throw error;
    }
  }

  async updateChecklistItem(input: any) {
    try {
      const result = await client.graphql({
        query: mutations.updateChecklistItem,
        variables: { input },
      });
      return result.data?.updateChecklistItem || null;
    } catch (error) {
      console.error('Error updating checklist item:', error);
      throw error;
    }
  }

  async deleteChecklistItem(input: any) {
    try {
      const result = await client.graphql({
        query: mutations.deleteChecklistItem,
        variables: { input },
      });
      return result.data?.deleteChecklistItem || null;
    } catch (error) {
      console.error('Error deleting checklist item:', error);
      throw error;
    }
  }

  async getChecklistCategory(id: string) {
    try {
      const result = await client.graphql({
        query: queries.getChecklistCategory,
        variables: { id },
      });
      return result.data?.getChecklistCategory || null;
    } catch (error) {
      console.error('Error fetching checklist category:', error);
      throw error;
    }
  }

  async listChecklistCategories(filter?: any, limit: number = 20, nextToken?: string) {
    try {
      const result = await client.graphql({
        query: queries.listChecklistCategories,
        variables: { filter, limit, nextToken },
      });
      return result.data?.listChecklistCategories || { items: [], nextToken: null };
    } catch (error) {
      console.error('Error listing checklist categories:', error);
      throw error;
    }
  }

  async createChecklistCategory(input: any) {
    try {
      const result = await client.graphql({
        query: mutations.createChecklistCategory,
        variables: { input },
      });
      return result.data?.createChecklistCategory || null;
    } catch (error) {
      console.error('Error creating checklist category:', error);
      throw error;
    }
  }

  async updateChecklistCategory(input: any) {
    try {
      const result = await client.graphql({
        query: mutations.updateChecklistCategory,
        variables: { input },
      });
      return result.data?.updateChecklistCategory || null;
    } catch (error) {
      console.error('Error updating checklist category:', error);
      throw error;
    }
  }

  async deleteChecklistCategory(input: any) {
    try {
      const result = await client.graphql({
        query: mutations.deleteChecklistCategory,
        variables: { input },
      });
      return result.data?.deleteChecklistCategory || null;
    } catch (error) {
      console.error('Error deleting checklist category:', error);
      throw error;
    }
  }

  // ==================== SUBSCRIPTION OPERATIONS ====================

  // Vendor subscriptions
  subscribeToVendorCreation() {
    return client.graphql({ query: subscriptions.onCreateVendor });
  }

  subscribeToVendorUpdates() {
    return client.graphql({ query: subscriptions.onUpdateVendor });
  }

  subscribeToVendorDeletion() {
    return client.graphql({ query: subscriptions.onDeleteVendor });
  }

  // Venue subscriptions
  subscribeToVenueCreation() {
    return client.graphql({ query: subscriptions.onCreateVenue });
  }

  subscribeToVenueUpdates() {
    return client.graphql({ query: subscriptions.onUpdateVenue });
  }

  subscribeToVenueDeletion() {
    return client.graphql({ query: subscriptions.onDeleteVenue });
  }

  // Shop subscriptions
  subscribeToShopCreation() {
    return client.graphql({ query: subscriptions.onCreateShop });
  }

  subscribeToShopUpdates() {
    return client.graphql({ query: subscriptions.onUpdateShop });
  }

  subscribeToShopDeletion() {
    return client.graphql({ query: subscriptions.onDeleteShop });
  }

  // Review subscriptions
  subscribeToReviewCreation() {
    return client.graphql({ query: subscriptions.onCreateReview });
  }

  subscribeToReviewUpdates() {
    return client.graphql({ query: subscriptions.onUpdateReview });
  }

  subscribeToReviewDeletion() {
    return client.graphql({ query: subscriptions.onDeleteReview });
  }

  // Order subscriptions
  subscribeToOrderCreation() {
    return client.graphql({ query: subscriptions.onCreateOrder });
  }

  subscribeToOrderUpdates() {
    return client.graphql({ query: subscriptions.onUpdateOrder });
  }

  subscribeToOrderDeletion() {
    return client.graphql({ query: subscriptions.onDeleteOrder });
  }

  // Booking subscriptions
  subscribeToBookingCreation() {
    return client.graphql({ query: subscriptions.onCreateBooking });
  }

  subscribeToBookingUpdates() {
    return client.graphql({ query: subscriptions.onUpdateBooking });
  }

  subscribeToBookingDeletion() {
    return client.graphql({ query: subscriptions.onDeleteBooking });
  }

  // ==================== UTILITY METHODS ====================

  // Get comprehensive statistics for admin dashboard
  async getAdminStatistics() {
    try {
      const [vendors, venues, shops, orders, reviews, users] = await Promise.all([
        this.listVendors({}, 1000),
        this.listVenues({}, 1000),
        this.listShops({}, 1000),
        this.listOrders({}, 1000),
        this.listReviews({}, 1000),
        this.listUserProfiles({}, 1000),
      ]);

      return {
        totalVendors: vendors.items.length,
        totalVenues: venues.items.length,
        totalProducts: shops.items.length,
        totalOrders: orders.items.length,
        totalReviews: reviews.items.length,
        totalUsers: users.items.length,
        activeVendors: vendors.items.filter((v: any) => v.status === 'ACTIVE').length,
        activeVenues: venues.items.filter((v: any) => v.status === 'ACTIVE').length,
        pendingOrders: orders.items.filter((o: any) => o.status === 'PENDING').length,
        completedOrders: orders.items.filter((o: any) => o.status === 'COMPLETED').length,
        averageRating: reviews.items.length > 0
          ? reviews.items.reduce((sum: number, r: any) => sum + (r.rating || 0), 0) / reviews.items.length
          : 0,
      };
    } catch (error) {
      console.error('Error fetching admin statistics:', error);
      throw error;
    }
  }

  // Search across all entities
  async searchAll(searchTerm: string, limit: number = 20) {
    try {
      const searchFilter = {
        or: [
          { name: { contains: searchTerm } },
          { description: { contains: searchTerm } },
        ]
      };

      const [vendors, venues, shops] = await Promise.all([
        this.listVendors(searchFilter, limit),
        this.listVenues(searchFilter, limit),
        this.listShops(searchFilter, limit),
      ]);

      return {
        vendors: vendors.items,
        venues: venues.items,
        shops: shops.items,
      };
    } catch (error) {
      console.error('Error searching all entities:', error);
      throw error;
    }
  }
}


// Export singleton instance
export const graphqlService = new GraphQLService();
export default GraphQLService;
