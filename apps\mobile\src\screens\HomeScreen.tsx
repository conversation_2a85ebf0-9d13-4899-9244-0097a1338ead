import React, { useState, useEffect, useRef } from 'react';
import { View, Text, ScrollView, StyleSheet, TouchableOpacity, Image, Dimensions, TextInput, ImageBackground, FlatList, Alert } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../providers/ThemeProvider';
import { useAuth } from '../providers/AuthProvider';
import { graphqlService } from '../services/graphqlService';
import { Card, CardContent, CardHeader, CardTitle, Button, Input, Badge } from '../components/ui';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { AppStackParamList } from '../navigation/AppNavigator';

type NavigationProp = StackNavigationProp<AppStackParamList>;

const serviceButtonClass = "inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-10 px-4 py-2 w-full md:w-auto";

interface FeaturedVendor {
  id: string;
  name: string;
  city: string;
  state: string;
  rating: number;
  profilePhoto?: string;
  category: string;
}

interface ServiceCategory {
  id: string;
  name: string;
  icon: string;
  color: string;
  route: string;
}

interface HeroImage {
  id: string;
  uri: any; // Using any for require() result
  title: string;
}

export default function HomeScreen() {
  const { theme } = useTheme();
  const { user } = useAuth();
  const navigation = useNavigation<NavigationProp>();
  const [featuredVendors, setFeaturedVendors] = useState<FeaturedVendor[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedLocation, setSelectedLocation] = useState('');
  const [currentHeroIndex, setCurrentHeroIndex] = useState(0);
  const [showLocationSuggestions, setShowLocationSuggestions] = useState(false);
  const [locationInput, setLocationInput] = useState('');
  const [selectedDate, setSelectedDate] = useState('');
  const [isSearching, setIsSearching] = useState(false);

  // Services carousel state
  const [canScrollServicesLeft, setCanScrollServicesLeft] = useState(false);
  const [canScrollServicesRight, setCanScrollServicesRight] = useState(true);
  const servicesCarouselRef = useRef<ScrollView>(null);

  const { width } = Dimensions.get('window');
  const heroScrollRef = useRef<FlatList>(null);

  // Hero images data - matching web app
  const heroImages: HeroImage[] = [
    {
      id: '1',
      uri: require('../../assets/hero_image_1.webp'),
      title: 'Beautiful wedding celebration with traditional Tamil Nadu elements'
    },
    {
      id: '2',
      uri: require('../../assets/hero_image_2.webp'),
      title: 'Elegant wedding venue with traditional decorations'
    },
    {
      id: '3',
      uri: require('../../assets/hero_image_3.webp'),
      title: 'Traditional wedding ceremony with cultural elements'
    }
  ];

  // Service categories data - matching web app
  const serviceCategories: ServiceCategory[] = [
    { id: '1', name: 'Venues', icon: 'business', color: theme.colors.primary, route: 'Venues' },
    { id: '2', name: 'Vendors', icon: 'people', color: theme.colors.secondary, route: 'Vendors' },
    { id: '3', name: 'Photos', icon: 'camera', color: theme.colors.chart3, route: 'Photos' },
    { id: '4', name: 'Shop', icon: 'bag', color: theme.colors.accent, route: 'Shop' },
    { id: '5', name: 'Planning', icon: 'calendar', color: theme.colors.chart4, route: 'Planning' },
    { id: '6', name: 'Real Weddings', icon: 'heart', color: theme.colors.chart5, route: 'RealWeddings' },
  ];

  useEffect(() => {
    loadFeaturedVendors();
  }, []);

  // Auto-scroll hero images
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentHeroIndex((prevIndex) => {
        const nextIndex = (prevIndex + 1) % heroImages.length;
        heroScrollRef.current?.scrollToIndex({ index: nextIndex, animated: true });
        return nextIndex;
      });
    }, 4000);

    return () => clearInterval(interval);
  }, []);

  // Services carousel functions
  const checkServicesScrollPosition = () => {
    if (servicesCarouselRef.current) {
      // For React Native ScrollView, we need to handle scroll position differently
      // This is a simplified version - in a real app you'd use onScroll event
      setCanScrollServicesLeft(true); // Simplified for mobile
      setCanScrollServicesRight(true); // Simplified for mobile
    }
  };

  const scrollServicesLeft = () => {
    if (servicesCarouselRef.current) {
      const scrollAmount = 200; // Adjust for mobile card width
      servicesCarouselRef.current.scrollTo({
        x: -scrollAmount,
        animated: true
      });
    }
  };

  const scrollServicesRight = () => {
    if (servicesCarouselRef.current) {
      const scrollAmount = 200; // Adjust for mobile card width
      servicesCarouselRef.current.scrollTo({
        x: scrollAmount,
        animated: true
      });
    }
  };

  // Handle search functionality
  const handleSearch = () => {
    if (!searchTerm.trim() && !locationInput.trim()) {
      Alert.alert('Search Required', 'Please enter a search term or location');
      return;
    }

    // Determine target screen based on search term
    const searchLower = searchTerm.toLowerCase();
    let targetScreen: keyof AppStackParamList = 'Vendors'; // Default

    if (searchLower.includes('venue') || searchLower.includes('hall') || searchLower.includes('resort')) {
      targetScreen = 'Venues';
    } else if (searchLower.includes('shop') || searchLower.includes('dress') || searchLower.includes('jewelry')) {
      targetScreen = 'Shop';
    }

    // Navigate with search parameters
    const params: any = {};
    if (searchTerm.trim()) params.query = searchTerm;
    if (locationInput.trim()) params.city = locationInput;

    navigation.navigate(targetScreen, params);
  };

  // Handle service category navigation
  const handleServicePress = (route: string) => {
    navigation.navigate(route as keyof AppStackParamList);
  };

  const loadFeaturedVendors = async () => {
    try {
      setLoading(true);
      const result = await graphqlService.listVendors(
        { featured: { eq: true } },
        6 // Limit to 6 featured vendors
      );

      if (result.items) {
        const vendors: FeaturedVendor[] = result.items.map((vendor: any) => ({
          id: vendor.id,
          name: vendor.name,
          city: vendor.city || '',
          state: vendor.state || '',
          rating: vendor.rating || 0,
          profilePhoto: vendor.profilePhoto,
          category: vendor.category || '',
        }));
        setFeaturedVendors(vendors);
      }
    } catch (error) {
      console.error('Error loading featured vendors:', error);
      // Fallback to empty array on error
      setFeaturedVendors([]);
    } finally {
      setLoading(false);
    }
  };

  // Render hero image item
  const renderHeroItem = ({ item }: { item: HeroImage }) => (
    <View style={[styles.heroSlide, { width }]}>
      <Image
        source={item.uri}
        style={styles.heroImage}
        resizeMode="cover"
      />
    </View>
  );

  // Render service category item
  const renderServiceCategory = ({ item }: { item: ServiceCategory }) => (
    <TouchableOpacity
      style={[styles.categoryCard, { backgroundColor: item.color }]}
      onPress={() => handleServicePress(item.route)}
    >
      <Ionicons name={item.icon as any} size={32} color="white" />
      <Text style={styles.categoryText}>{item.name}</Text>
    </TouchableOpacity>
  );

  const nextTestimonial = () => {
    setCurrentTestimonial((prev) => (prev + 1) % testimonials.length);
  };

  const prevTestimonial = () => {
    setCurrentTestimonial((prev) => (prev - 1 + testimonials.length) % testimonials.length);
  };

  const renderTestimonial = ({ item }: { item: Testimonial }) => (
    <View style={styles.testimonialContainer}>
      <Text style={styles.testimonialQuote}>"</Text>
      <Text style={styles.testimonialText}>{item.quote}</Text>
      <View style={styles.testimonialAuthor}>
        <View style={[styles.testimonialAvatar, { backgroundColor: item.color }]}>
          <Text style={styles.testimonialInitials}>{item.initials}</Text>
        </View>
        <View style={styles.testimonialInfo}>
          <Text style={styles.testimonialName}>{item.name}</Text>
          <Text style={styles.testimonialLocation}>{item.location} • {item.year}</Text>
          <View style={styles.verifiedBadge}>
            <View style={styles.verifiedDot} />
            <Text style={styles.verifiedText}>Verified Customer</Text>
          </View>
        </View>
      </View>
    </View>
  );

  return (
    <ScrollView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {/* Hero Section - Matching Web App */}
      <View style={styles.heroContainer}>
        <FlatList
          ref={heroScrollRef}
          data={heroImages}
          renderItem={renderHeroItem}
          keyExtractor={(item) => item.id}
          horizontal
          pagingEnabled
          showsHorizontalScrollIndicator={false}
          onMomentumScrollEnd={(event) => {
            const index = Math.round(event.nativeEvent.contentOffset.x / width);
            setCurrentHeroIndex(index);
          }}
          style={styles.heroCarousel}
        />

        {/* Hero Overlay with Search */}
        <View style={styles.heroOverlay}>
          {/* Main Heading - Matching Web App */}
          <View style={styles.heroContent}>
            <Text style={[styles.heroMainTitle, { color: theme.colors.primaryForeground }]}>
            Plan Your Dream Celebration with Us!
            </Text>
            <Text style={[styles.heroSubtitle, { color: theme.colors.primaryForeground }]}>
              Find the best wedding vendors, venues, and services for your special day
            </Text>
          </View>

          {/* Search Card - Matching Web App Design */}
          <Card style={styles.searchCard}>
            <CardContent style={styles.searchCardContent}>
              <View style={styles.searchRow}>
                <View style={styles.searchInputContainer}>
                  <Ionicons name="search-outline" size={20} color={theme.colors.mutedForeground} />
                  <TextInput
                    style={[styles.searchTextInput, { color: theme.colors.foreground }]}
                    placeholder="Search vendors, venues..."
                    placeholderTextColor={theme.colors.mutedForeground}
                    value={searchTerm}
                    onChangeText={setSearchTerm}
                  />
                </View>
                <View style={styles.searchInputContainer}>
                  <Ionicons name="location-outline" size={20} color={theme.colors.mutedForeground} />
                  <TextInput
                    style={[styles.searchTextInput, { color: theme.colors.foreground }]}
                    placeholder="Enter city or location"
                    placeholderTextColor={theme.colors.mutedForeground}
                    value={locationInput}
                    onChangeText={setLocationInput}
                  />
                </View>
              </View>
              <Button
                title="Search"
                onPress={handleSearch}
                style={styles.searchButton}
                variant="default"
                size="lg"
              />
            </CardContent>
          </Card>
        </View>

        {/* Hero Indicators */}
        <View style={styles.heroIndicators}>
          {heroImages.map((_, index) => (
            <View
              key={index}
              style={[
                styles.heroIndicator,
                {
                  backgroundColor: index === currentHeroIndex
                    ? theme.colors.primaryForeground
                    : 'rgba(255, 255, 255, 0.5)'
                }
              ]}
            />
          ))}
        </View>
      </View>

      {/* Festive Services Section - Matching Web App */}
      <View style={[styles.section, { backgroundColor: theme.colors.background }]}>
        <View style={styles.sectionContainer}>
          <Text style={[styles.sectionTitle, { color: theme.colors.foreground }]}>
            Popular Festive Services
          </Text>
          <Text style={[styles.sectionSubtitle, { color: theme.colors.mutedForeground }]}>
            Discover the best Festive Services for your special day
          </Text>
        </View>

          <View style={styles.scrollControls}>
            <TouchableOpacity
              style={[
                styles.scrollButton,
                !canScrollServicesLeft && styles.scrollButtonDisabled
              ]}
              onPress={scrollServicesLeft}
              disabled={!canScrollServicesLeft}
            >
              <Ionicons name="chevron-back" size={20} color="#666" />
            </TouchableOpacity>
            <TouchableOpacity
              style={[
                styles.scrollButton,
                !canScrollServicesRight && styles.scrollButtonDisabled
              ]}
              onPress={scrollServicesRight}
              disabled={!canScrollServicesRight}
            >
              <Ionicons name="chevron-forward" size={20} color="#666" />
            </TouchableOpacity>
          </View>

          <ScrollView
            ref={servicesCarouselRef}
            horizontal
            showsHorizontalScrollIndicator={false}
            style={styles.servicesCarousel}
            contentContainerStyle={styles.servicesCarouselContent}
          >
            {/* Marriage Mahal */}
            <View style={styles.categoryCard}>
              <TouchableOpacity
                style={styles.categoryImageButton}
                onPress={() => navigation.navigate('Vendors', { category: 'marriage-mahal' })}
              >
                <Image
                  source={require('../../assets/mahal.webp')}
                  style={styles.categoryImage}
                  resizeMode="cover"
                />
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.categoryButton}
                onPress={() => navigation.navigate('Vendors', { category: 'marriage-mahal' })}
              >
                <Text style={styles.categoryButtonText}>Marriage Mahal</Text>
              </TouchableOpacity>
            </View>

            {/* Photo & Videographers */}
            <View style={styles.categoryCard}>
              <TouchableOpacity
                style={styles.categoryImageButton}
                onPress={() => navigation.navigate('Vendors', { category: 'photo-videographers' })}
              >
                <Image
                  source={require('../../assets/photographer.webp')}
                  style={styles.categoryImage}
                  resizeMode="cover"
                />
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.categoryButton}
                onPress={() => navigation.navigate('Vendors', { category: 'photo-videographers' })}
              >
                <Text style={styles.categoryButtonText}>Photo & Videographers</Text>
              </TouchableOpacity>
            </View>

            {/* Cooks & Caterings */}
            <View style={styles.categoryCard}>
              <TouchableOpacity
                style={styles.categoryImageButton}
                onPress={() => navigation.navigate('Vendors', { category: 'cooks-caterings' })}
              >
                <Image
                  source={require('../../assets/catering.webp')}
                  style={styles.categoryImage}
                  resizeMode="cover"
                />
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.categoryButton}
                onPress={() => navigation.navigate('Vendors', { category: 'cooks-caterings' })}
              >
                <Text style={styles.categoryButtonText}>Cooks & Caterings</Text>
              </TouchableOpacity>
            </View>

            {/* Makeup & Mehandi Artists */}
            <View style={styles.categoryCard}>
              <TouchableOpacity
                style={styles.categoryImageButton}
                onPress={() => navigation.navigate('Vendors', { category: 'bridal-makeup-artists' })}
              >
                <Image
                  source={require('../../assets/bride_makeup.webp')}
                  style={styles.categoryImage}
                  resizeMode="cover"
                />
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.categoryButton}
                onPress={() => navigation.navigate('Vendors', { category: 'bridal-makeup-artists' })}
              >
                <Text style={styles.categoryButtonText}>Makeup & Mehandi Artists</Text>
              </TouchableOpacity>
            </View>

            {/* Musical Artists */}
            <View style={styles.categoryCard}>
              <TouchableOpacity
                style={styles.categoryImageButton}
                onPress={() => navigation.navigate('Vendors', { category: 'musical-artists' })}
              >
                <Image
                  source={require('../../assets/nadasvaram.webp')}
                  style={styles.categoryImage}
                  resizeMode="cover"
                />
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.categoryButton}
                onPress={() => navigation.navigate('Vendors', { category: 'musical-artists' })}
              >
                <Text style={styles.categoryButtonText}>Musical Artists</Text>
              </TouchableOpacity>
            </View>

            {/* Invitations */}
            <View style={styles.categoryCard}>
              <TouchableOpacity
                style={styles.categoryImageButton}
                onPress={() => navigation.navigate('Vendors', { category: 'invitations' })}
              >
                <Image
                  source={require('../../assets/Invitations.webp')}
                  style={styles.categoryImage}
                  resizeMode="cover"
                />
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.categoryButton}
                onPress={() => navigation.navigate('Vendors', { category: 'invitations' })}
              >
                <Text style={styles.categoryButtonText}>Invitations</Text>
              </TouchableOpacity>
            </View>

            {/* Wedding Jewellery Sets */}
            <View style={styles.categoryCard}>
              <TouchableOpacity
                style={styles.categoryImageButton}
                onPress={() => navigation.navigate('Vendors', { category: 'wedding-jewellery-sets' })}
              >
                <Image
                  source={require('../../assets/wedding_jewels.webp')}
                  style={styles.categoryImage}
                  resizeMode="cover"
                />
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.categoryButton}
                onPress={() => navigation.navigate('Vendors', { category: 'wedding-jewellery-sets' })}
              >
                <Text style={styles.categoryButtonText}>Wedding Jewellery Sets</Text>
              </TouchableOpacity>
            </View>

            {/* Marriage Outfits */}
            <View style={styles.categoryCard}>
              <TouchableOpacity
                style={styles.categoryImageButton}
                onPress={() => navigation.navigate('Vendors', { category: 'marriage-outfits' })}
              >
                <Image
                  source={require('../../assets/dress_store.webp')}
                  style={styles.categoryImage}
                  resizeMode="cover"
                />
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.categoryButton}
                onPress={() => navigation.navigate('Vendors', { category: 'marriage-outfits' })}
              >
                <Text style={styles.categoryButtonText}>Marriage Outfits</Text>
              </TouchableOpacity>
            </View>

            {/* Astrologer */}
            <View style={styles.categoryCard}>
              <TouchableOpacity
                style={styles.categoryImageButton}
                onPress={() => navigation.navigate('Vendors', { category: 'astrologer' })}
              >
                <Image
                  source={require('../../assets/astrologer.webp')}
                  style={styles.categoryImage}
                  resizeMode="cover"
                />
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.categoryButton}
                onPress={() => navigation.navigate('Vendors', { category: 'astrologer' })}
              >
                <Text style={styles.categoryButtonText}>Astrologers</Text>
              </TouchableOpacity>
            </View>

            {/* DJ Music */}
            <View style={styles.categoryCard}>
              <TouchableOpacity
                style={styles.categoryImageButton}
                onPress={() => navigation.navigate('Vendors', { category: 'dj-music' })}
              >
                <Image
                  source={require('../../assets/dj_music.webp')}
                  style={styles.categoryImage}
                  resizeMode="cover"
                />
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.categoryButton}
                onPress={() => navigation.navigate('Vendors', { category: 'dj-music' })}
              >
                <Text style={styles.categoryButtonText}>DJ Music</Text>
              </TouchableOpacity>
            </View>

            {/* Decorators */}
            <View style={styles.categoryCard}>
              <TouchableOpacity
                style={styles.categoryImageButton}
                onPress={() => navigation.navigate('Vendors', { category: 'decorators' })}
              >
                <Image
                  source={require('../../assets/decorators.webp')}
                  style={styles.categoryImage}
                  resizeMode="cover"
                />
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.categoryButton}
                onPress={() => navigation.navigate('Vendors', { category: 'decorators' })}
              >
                <Text style={styles.categoryButtonText}>Decorators</Text>
              </TouchableOpacity>
            </View>

            {/* Snacks Stall */}
            <View style={styles.categoryCard}>
              <TouchableOpacity
                style={styles.categoryImageButton}
                onPress={() => navigation.navigate('Vendors', { category: 'snacks-shops' })}
              >
                <Image
                  source={require('../../assets/snacks_shop.webp')}
                  style={styles.categoryImage}
                  resizeMode="cover"
                />
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.categoryButton}
                onPress={() => navigation.navigate('Vendors', { category: 'snacks-shops' })}
              >
                <Text style={styles.categoryButtonText}>Snacks Stall</Text>
              </TouchableOpacity>
            </View>

            {/* Event Organizers */}
            <View style={styles.categoryCard}>
              <TouchableOpacity
                style={styles.categoryImageButton}
                onPress={() => navigation.navigate('Vendors', { category: 'event-organizers' })}
              >
                <Image
                  source={require('../../assets/event_organizers.webp')}
                  style={styles.categoryImage}
                  resizeMode="cover"
                />
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.categoryButton}
                onPress={() => navigation.navigate('Vendors', { category: 'event-organizers' })}
              >
                <Text style={styles.categoryButtonText}>Event Organizers</Text>
              </TouchableOpacity>
            </View>

            {/* Iyer/Pandit */}
            <View style={styles.categoryCard}>
              <TouchableOpacity
                style={styles.categoryImageButton}
                onPress={() => navigation.navigate('Vendors', { category: 'iyer-pandit' })}
              >
                <Image
                  source={require('../../assets/iyer_image.webp')}
                  style={styles.categoryImage}
                  resizeMode="cover"
                />
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.categoryButton}
                onPress={() => navigation.navigate('Vendors', { category: 'iyer-pandit' })}
              >
                <Text style={styles.categoryButtonText}>Iyer/Pandit</Text>
              </TouchableOpacity>
            </View>

            {/* Return Gift */}
            <View style={styles.categoryCard}>
              <TouchableOpacity
                style={styles.categoryImageButton}
                onPress={() => navigation.navigate('Vendors', { category: 'return-gift' })}
              >
                <Image
                  source={require('../../assets/return_gift.webp')}
                  style={styles.categoryImage}
                  resizeMode="cover"
                />
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.categoryButton}
                onPress={() => navigation.navigate('Vendors', { category: 'return-gift' })}
              >
                <Text style={styles.categoryButtonText}>Return Gift</Text>
              </TouchableOpacity>
            </View>

            {/* Flower Shops */}
            <View style={styles.categoryCard}>
              <TouchableOpacity
                style={styles.categoryImageButton}
                onPress={() => navigation.navigate('Vendors', { category: 'flower-shops' })}
              >
                <Image
                  source={require('../../assets/flower_shops.webp')}
                  style={styles.categoryImage}
                  resizeMode="cover"
                />
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.categoryButton}
                onPress={() => navigation.navigate('Vendors', { category: 'flower-shops' })}
              >
                <Text style={styles.categoryButtonText}>Flower Shops</Text>
              </TouchableOpacity>
            </View>

            {/* Travels */}
            <View style={styles.categoryCard}>
              <TouchableOpacity
                style={styles.categoryImageButton}
                onPress={() => navigation.navigate('Vendors', { category: 'travels' })}
              >
                <Image
                  source={require('../../assets/transportations.webp')}
                  style={styles.categoryImage}
                  resizeMode="cover"
                />
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.categoryButton}
                onPress={() => navigation.navigate('Vendors', { category: 'travels' })}
              >
                <Text style={styles.categoryButtonText}>Travels</Text>
              </TouchableOpacity>
            </View>
          </ScrollView>
   
      </View>


      {/* Featured Vendors */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>
          Featured Vendors
        </Text>
        <View style={styles.featuredVendorsGrid}>
          {loading ? (
            // Loading placeholder
            [1, 2].map((item) => (
              <View
                key={item}
                style={[styles.featuredVendorCard, { backgroundColor: theme.colors.surface }]}
              >
                <View style={[styles.featuredVendorImage, { backgroundColor: theme.colors.border }]} />
              </View>
            ))
          ) : featuredVendors.length > 0 ? (
            featuredVendors.slice(0, 2).map((vendor) => (
              <TouchableOpacity
                key={vendor.id}
                style={[styles.featuredVendorCard, { backgroundColor: theme.colors.surface }]}
                onPress={() => navigation.navigate('VendorDetail', { vendorId: vendor.id })}
              >
                {vendor.profilePhoto ? (
                  <Image
                    source={{ uri: vendor.profilePhoto }}
                    style={styles.featuredVendorImage}
                  />
                ) : (
                  <View style={[styles.featuredVendorImage, { backgroundColor: theme.colors.border }]}>
                    <Ionicons name="business-outline" size={40} color={theme.colors.textSecondary} />
                  </View>
                )}
              </TouchableOpacity>
            ))
          ) : (
            <View style={styles.featuredVendorCard}>
              <View style={[styles.featuredVendorImage, { backgroundColor: theme.colors.border }]}>
                <Ionicons name="business-outline" size={40} color={theme.colors.textSecondary} />
              </View>
            </View>
          )}
        </View>
      </View>

    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  // Hero Section Styles - Matching Web App
  heroContainer: {
    height: 500, // Increased height to match web app
    position: 'relative',
  },
  heroCarousel: {
    flex: 1,
  },
  heroOverlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(0, 0, 0, 0.5)', // Darker overlay for better text readability
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24, // Increased padding
  },
  heroContent: {
    alignItems: 'center',
    marginBottom: 32, // mb-8 equivalent
  },
  heroMainTitle: {
    fontSize: 32, // text-3xl equivalent
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 16, // mb-4
    letterSpacing: -0.025, // tracking-tight
    lineHeight: 40,
  },
  heroSubtitle: {
    fontSize: 18, // text-lg equivalent
    textAlign: 'center',
    opacity: 0.9,
    lineHeight: 28,
    paddingHorizontal: 16,
  },
  heroIndicators: {
    position: 'absolute',
    bottom: 20,
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'center',
    gap: 8,
  },
  heroIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
  },
  // Search Card Styles - Matching Web App
  searchCard: {
    marginHorizontal: 16,
    marginTop: 16,
  },
  searchCardContent: {
    padding: 24, // p-6 to match web app
  },
  searchRow: {
    flexDirection: 'column', // Stack vertically on mobile
    gap: 12, // gap-3
    marginBottom: 16, // mb-4
  },
  searchInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'transparent',
    borderRadius: 8, // rounded-lg
    paddingHorizontal: 12, // px-3
    paddingVertical: 12, // py-3
    gap: 8, // gap-2
    borderWidth: 1,
    borderColor: '#E2E8F0', // border color from theme
  },
  searchTextInput: {
    flex: 1,
    fontSize: 14, // text-sm
    fontWeight: '400',
  },
  searchButton: {
    width: '100%', // Full width on mobile
    marginTop: 8,
  },
  // Section Styles - Matching Web App
  section: {
    paddingVertical: 48, // py-12 to match web app
    paddingHorizontal: 16, // px-4
  },
  sectionContainer: {
    alignItems: 'center',
    marginBottom: 32, // mb-8
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  sectionIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#fef2f2',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  sectionTitle: {
    fontSize: 30, // text-3xl to match web app
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 8, // mb-2
    letterSpacing: -0.025, // tracking-tight
  },
  sectionSubtitle: {
    fontSize: 18, // text-lg
    textAlign: 'center',
    marginBottom: 32, // mb-8
    lineHeight: 28,
  },
  // Categories Section Styles
  categoriesSection: {
    position: 'relative',
    backgroundColor: 'white',
    overflow: 'hidden',
  },
  decorativeTop: {
    position: 'absolute',
    top: 0,
    right: 0,
    width: 128,
    height: 128,
    backgroundColor: 'rgba(139, 21, 56, 0.2)',
    borderRadius: 64,
    transform: [{ translateX: 64 }, { translateY: -64 }],
  },
  decorativeBottom: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    width: 160,
    height: 160,
    backgroundColor: 'rgba(124, 179, 66, 0.1)',
    borderRadius: 80,
    transform: [{ translateX: -80 }, { translateY: 80 }],
  },
  categoryContainer: {
    position: 'relative',
    paddingHorizontal: 8,
  },
  categoryHeader: {
    alignItems: 'center',
    marginBottom: 16,
  },
  categoryIconContainer: {
    width: 64,
    height: 64,
    backgroundColor: '#8B1538',
    borderRadius: 32,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 5,
  },
  scrollControls: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    gap: 8,
    marginBottom: 16,
  },
  scrollButton: {
    width: 32,
    height: 32,
    backgroundColor: '#f5f5f5',
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  scrollButtonDisabled: {
    opacity: 0.5,
  },
  servicesCarousel: {
    paddingBottom: 8,
  },
  servicesCarouselContent: {
    paddingHorizontal: 8,
    gap: 8,
  },
  categoryCard: {
    width: 176,
    height: 240,
    marginRight: 8,
    backgroundColor: 'white',
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    overflow: 'hidden',
  },
  categoryImageButton: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  categoryImage: {
    width: '100%',
    height: '100%',
  },
  categoryButton: {
    height: 64,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'white',
    borderTopWidth: 1,
    borderTopColor: '#e5e5e5',
  },
  categoryButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
    textAlign: 'center',
  },
  // Featured Vendors Styles
  featuredVendorsGrid: {
    flexDirection: 'row',
    gap: 12,
  },
  featuredVendorCard: {
    flex: 1,
    height: 120,
    backgroundColor: 'white',
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    justifyContent: 'center',
    alignItems: 'center',
  },
  featuredVendorImage: {
    width: 80,
    height: 80,
    backgroundColor: '#f0f0f0',
    borderRadius: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  // Mobile Filter Bar Styles
  mobileFilterBar: {
    width: '100%',
    backgroundColor: 'white',
    paddingHorizontal: 8,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#e5e5e5',
  },
  mobileFilterForm: {
    flexDirection: 'column',
    gap: 8,
    width: '100%',
    maxWidth: 320,
    alignSelf: 'center',
  },
  mobileFilterRow: {
    flexDirection: 'row',
    gap: 8,
    width: '100%',
  },
  mobileFilterInputContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f8f9fa',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
    gap: 8,
    borderWidth: 1,
    borderColor: '#e9ecef',
  },
  mobileFilterInput: {
    flex: 1,
    fontSize: 14,
    color: '#333',
  },
  mobileFilterButton: {
    backgroundColor: '#5d1417',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    width: '100%',
  },
  mobileFilterButtonText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 14,
    fontWeight: '600',
  },
});
