#!/usr/bin/env node

/**
 * Phase 2 Operations Test Script
 * Tests all custom mutations, queries, and subscriptions
 */

const { generateClient } = require('@aws-amplify/api');
const { Amplify } = require('aws-amplify');

// Configure Amplify (you'll need to update this with your actual config)
const awsConfig = {
  aws_project_region: 'ap-south-1',
  aws_appsync_graphqlEndpoint: 'YOUR_GRAPHQL_ENDPOINT',
  aws_appsync_region: 'ap-south-1',
  aws_appsync_authenticationType: 'API_KEY',
  aws_appsync_apiKey: 'YOUR_API_KEY'
};

Amplify.configure(awsConfig);
const client = generateClient();

// Test data
const testData = {
  email: {
    emailType: 'LOGIN_OTP',
    recipient: '<EMAIL>',
    templateData: JSON.stringify({
      userName: 'Test User',
      otp: '123456',
      expiryMinutes: 10
    })
  },
  invoice: {
    type: 'PRODUCT_ORDER',
    customerId: 'test-customer-123',
    customerName: '<PERSON>',
    customerEmail: '<EMAIL>',
    customerPhone: '+91-9876543210',
    items: [{
      id: 'item-1',
      name: 'Wedding Decoration Package',
      description: 'Complete wedding decoration with flowers and lighting',
      quantity: 1,
      unitPrice: 50000,
      totalPrice: 50000,
      taxRate: 18,
      taxAmount: 9000
    }],
    subtotal: 50000,
    taxAmount: 9000,
    discountAmount: 0,
    totalAmount: 59000,
    paymentStatus: 'PAID',
    paymentMethod: 'RAZORPAY',
    transactionId: 'txn_123456789'
  },
  subscription: {
    vendorId: 'test-vendor-123',
    planId: 'plan-basic-monthly',
    paymentMethod: 'RAZORPAY',
    autoRenew: true
  }
};

// GraphQL Mutations
const SEND_EMAIL = `
  mutation SendEmail($input: SendEmailInput!) {
    sendEmail(input: $input) {
      success
      messageId
      emailLogId
      error
    }
  }
`;

const GENERATE_INVOICE = `
  mutation GenerateInvoice($input: GenerateInvoiceInput!) {
    generateInvoice(input: $input) {
      success
      invoiceId
      invoiceNumber
      pdfUrl
      error
    }
  }
`;

const CREATE_SUBSCRIPTION = `
  mutation CreateSubscription($input: CreateSubscriptionInput!) {
    createSubscription(input: $input) {
      success
      subscriptionId
      paymentUrl
      error
    }
  }
`;

// GraphQL Queries
const GET_EMAIL_STATS = `
  query GetEmailStats($startDate: AWSDateTime!, $endDate: AWSDateTime!) {
    getEmailStats(startDate: $startDate, endDate: $endDate) {
      totalSent
      totalDelivered
      totalFailed
      deliveryRate
      byType {
        emailType
        sent
        delivered
        failed
      }
    }
  }
`;

const GET_VENDOR_SUBSCRIPTION = `
  query GetVendorSubscription($vendorId: ID!) {
    getVendorSubscription(vendorId: $vendorId) {
      id
      status
      startDate
      endDate
      amount
      currency
      autoRenew
    }
  }
`;

// Test functions
async function testSendEmail() {
  console.log('🧪 Testing sendEmail mutation...');
  try {
    const result = await client.graphql({
      query: SEND_EMAIL,
      variables: { input: testData.email }
    });
    
    if (result.data.sendEmail.success) {
      console.log('✅ Email sent successfully!');
      console.log(`   Message ID: ${result.data.sendEmail.messageId}`);
      console.log(`   Email Log ID: ${result.data.sendEmail.emailLogId}`);
    } else {
      console.log('❌ Email sending failed:');
      console.log(`   Error: ${result.data.sendEmail.error}`);
    }
  } catch (error) {
    console.log('❌ Email mutation error:', error.message);
  }
}

async function testGenerateInvoice() {
  console.log('\n🧪 Testing generateInvoice mutation...');
  try {
    const result = await client.graphql({
      query: GENERATE_INVOICE,
      variables: { input: testData.invoice }
    });
    
    if (result.data.generateInvoice.success) {
      console.log('✅ Invoice generated successfully!');
      console.log(`   Invoice ID: ${result.data.generateInvoice.invoiceId}`);
      console.log(`   Invoice Number: ${result.data.generateInvoice.invoiceNumber}`);
      console.log(`   PDF URL: ${result.data.generateInvoice.pdfUrl}`);
    } else {
      console.log('❌ Invoice generation failed:');
      console.log(`   Error: ${result.data.generateInvoice.error}`);
    }
  } catch (error) {
    console.log('❌ Invoice mutation error:', error.message);
  }
}

async function testCreateSubscription() {
  console.log('\n🧪 Testing createSubscription mutation...');
  try {
    const result = await client.graphql({
      query: CREATE_SUBSCRIPTION,
      variables: { input: testData.subscription }
    });
    
    if (result.data.createSubscription.success) {
      console.log('✅ Subscription created successfully!');
      console.log(`   Subscription ID: ${result.data.createSubscription.subscriptionId}`);
      console.log(`   Payment URL: ${result.data.createSubscription.paymentUrl}`);
    } else {
      console.log('❌ Subscription creation failed:');
      console.log(`   Error: ${result.data.createSubscription.error}`);
    }
  } catch (error) {
    console.log('❌ Subscription mutation error:', error.message);
  }
}

async function testGetEmailStats() {
  console.log('\n🧪 Testing getEmailStats query...');
  try {
    const endDate = new Date().toISOString();
    const startDate = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(); // 7 days ago
    
    const result = await client.graphql({
      query: GET_EMAIL_STATS,
      variables: { startDate, endDate }
    });
    
    const stats = result.data.getEmailStats;
    console.log('✅ Email stats retrieved successfully!');
    console.log(`   Total Sent: ${stats.totalSent}`);
    console.log(`   Total Delivered: ${stats.totalDelivered}`);
    console.log(`   Delivery Rate: ${stats.deliveryRate}%`);
    console.log(`   Email Types: ${stats.byType.length} types tracked`);
  } catch (error) {
    console.log('❌ Email stats query error:', error.message);
  }
}

async function testGetVendorSubscription() {
  console.log('\n🧪 Testing getVendorSubscription query...');
  try {
    const result = await client.graphql({
      query: GET_VENDOR_SUBSCRIPTION,
      variables: { vendorId: testData.subscription.vendorId }
    });
    
    const subscription = result.data.getVendorSubscription;
    if (subscription) {
      console.log('✅ Vendor subscription retrieved successfully!');
      console.log(`   Status: ${subscription.status}`);
      console.log(`   Amount: ${subscription.currency} ${subscription.amount}`);
      console.log(`   Period: ${subscription.startDate} to ${subscription.endDate}`);
      console.log(`   Auto Renew: ${subscription.autoRenew}`);
    } else {
      console.log('ℹ️  No subscription found for vendor');
    }
  } catch (error) {
    console.log('❌ Vendor subscription query error:', error.message);
  }
}

// Main test runner
async function runTests() {
  console.log('🚀 Starting Phase 2 Operations Test\n');
  console.log('====================================');
  
  // Test mutations
  await testSendEmail();
  await testGenerateInvoice();
  await testCreateSubscription();
  
  // Test queries
  await testGetEmailStats();
  await testGetVendorSubscription();
  
  console.log('\n====================================');
  console.log('🎉 Phase 2 Operations Test Complete!');
  console.log('\nNote: Update the AWS config at the top of this file with your actual GraphQL endpoint and API key.');
}

// Run tests if called directly
if (require.main === module) {
  runTests().catch(console.error);
}

module.exports = {
  runTests,
  testSendEmail,
  testGenerateInvoice,
  testCreateSubscription,
  testGetEmailStats,
  testGetVendorSubscription
};
