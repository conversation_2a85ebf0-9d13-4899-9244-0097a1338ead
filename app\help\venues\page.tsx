"use client"

import { TopHeader } from "@/components/top-header"
import { Header } from "@/components/header"
import { Footer } from "@/components/footer"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { SimpleSEO } from "@/components/seo/SimpleSEO"
import { 
  ArrowLeft,
  MapPin,
  Users,
  Calendar,
  Camera,
  Car,
  Utensils,
  Wifi,
  Music,
  Shield,
  CheckCircle,
  AlertCircle,
  Star,
  Clock,
  Phone,
  Mail,
  Eye,
  Heart,
  Filter,
  Search
} from "lucide-react"
import Link from "next/link"
import { useHelpTranslations } from '@/lib/universal-i18n'

export default function VenuesHelpPage() {
  const helpT = useHelpTranslations()
  const venueTypes = [
    {
      icon: MapPin,
      title: "Banquet Halls",
      description: "Indoor venues with full facilities",
      features: [
        "Climate controlled environment",
        "Built-in sound systems",
        "Catering kitchen facilities",
        "Parking availability",
        "Backup power systems"
      ],
      capacity: "100-1000 guests",
      bestFor: "Traditional ceremonies, receptions"
    },
    {
      icon: Users,
      title: "Hotels & Resorts",
      description: "Full-service wedding destinations",
      features: [
        "Guest accommodation",
        "Multiple event spaces",
        "In-house catering",
        "Wedding planning services",
        "Spa and recreation facilities"
      ],
      capacity: "50-500 guests",
      bestFor: "Destination weddings, multi-day events"
    },
    {
      icon: Camera,
      title: "Heritage Properties",
      description: "Palaces, forts, and historic venues",
      features: [
        "Unique architectural beauty",
        "Rich cultural heritage",
        "Stunning photo opportunities",
        "Exclusive ambiance",
        "Historical significance"
      ],
      capacity: "100-800 guests",
      bestFor: "Royal-themed weddings, photography"
    },
    {
      icon: MapPin,
      title: "Outdoor Venues",
      description: "Gardens, beaches, and open spaces",
      features: [
        "Natural scenic beauty",
        "Fresh air environment",
        "Flexible decoration options",
        "Unique photo backdrops",
        "Connection with nature"
      ],
      capacity: "50-1000+ guests",
      bestFor: "Garden parties, beach weddings"
    }
  ]

  const bookingProcess = [
    {
      step: "1. Research & Shortlist",
      description: "Find venues that match your requirements and budget",
      tips: [
        "Use filters for location, capacity, and budget",
        "Read reviews and check ratings",
        "View photo galleries and virtual tours",
        "Check availability for your preferred dates"
      ]
    },
    {
      step: "2. Site Visits",
      description: "Visit shortlisted venues in person",
      tips: [
        "Schedule visits during similar lighting conditions",
        "Bring your partner and key family members",
        "Ask about restrictions and policies",
        "Take photos and notes for comparison"
      ]
    },
    {
      step: "3. Compare Options",
      description: "Evaluate venues based on your criteria",
      tips: [
        "Compare total costs including hidden fees",
        "Consider guest convenience and accessibility",
        "Evaluate included services and amenities",
        "Check cancellation and refund policies"
      ]
    },
    {
      step: "4. Book & Confirm",
      description: "Secure your chosen venue with proper documentation",
      tips: [
        "Read the contract thoroughly",
        "Understand payment schedules",
        "Confirm all included services in writing",
        "Get contact details for day-of coordination"
      ]
    }
  ]

  const importantQuestions = [
    {
      category: "Capacity & Layout",
      questions: [
        "What is the maximum guest capacity?",
        "Can the space be configured for both ceremony and reception?",
        "Are there separate areas for different functions?",
        "Is there space for a dance floor and entertainment?"
      ]
    },
    {
      category: "Services & Amenities",
      questions: [
        "What services are included in the package?",
        "Is catering provided in-house or can we bring external caterers?",
        "Are tables, chairs, and basic decor included?",
        "Is there a bridal suite or preparation area?"
      ]
    },
    {
      category: "Logistics & Policies",
      questions: [
        "What are the setup and breakdown timings?",
        "Are there noise restrictions or curfews?",
        "What is the parking capacity?",
        "Are there restrictions on decorations or vendors?"
      ]
    },
    {
      category: "Costs & Payments",
      questions: [
        "What is included in the base price?",
        "Are there additional charges for overtime?",
        "What is the payment schedule?",
        "What is the cancellation and refund policy?"
      ]
    }
  ]

  const venueChecklist = [
    "Location accessibility for guests",
    "Adequate parking facilities",
    "Backup power and lighting",
    "Clean and well-maintained restrooms",
    "Kitchen facilities for catering",
    "Sound system and microphone setup",
    "Climate control (AC/heating)",
    "Security arrangements",
    "Emergency exits and safety measures",
    "Vendor access and loading areas",
    "Bridal preparation rooms",
    "Storage space for decorations"
  ]

  const budgetTips = [
    {
      tip: "Compare Total Costs",
      description: "Look beyond base prices to include all additional fees, taxes, and service charges"
    },
    {
      tip: "Understand Package Inclusions",
      description: "Know exactly what's included to avoid surprise costs for basic amenities"
    },
    {
      tip: "Negotiate Wisely",
      description: "Ask about discounts for off-peak dates or package deals with preferred vendors"
    },
    {
      tip: "Plan for Extras",
      description: "Budget for additional costs like overtime, extra decorations, or upgraded services"
    }
  ]

  return (
    <>
      <SimpleSEO
        title="Booking Wedding Venues Guide - BookmyFestive Help"
        description="Complete guide to finding and booking the perfect wedding venue. Learn about venue types, booking process, important questions, and budget tips."
        keywords="wedding venues, venue booking, banquet halls, wedding halls, venue selection"
        url="/help/venues"
      />
      
      <div className="min-h-screen bg-background">
        <TopHeader />
        <Header />

        {/* Header */}
        <section className="py-12">
          <div className="container mx-auto px-4">
            <Link href="/help" className="inline-flex items-center gap-2 text-primary hover:underline mb-6">
              <ArrowLeft className="w-4 h-4" />
              Back to Help Center
            </Link>
            
            <div className="text-center mb-12">
              <MapPin className="w-16 h-16 mx-auto mb-6 text-primary" />
              <h1 className="text-4xl font-bold mb-4">{helpT.venues?.title || 'Booking Wedding Venues'}</h1>
              <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
                {helpT.venues?.subtitle || 'Your comprehensive guide to finding and booking the perfect wedding venue. From banquet halls to heritage properties, learn how to choose the ideal setting for your special day.'}
              </p>
            </div>
          </div>
        </section>

        {/* Venue Types */}
        <section className="py-16">
          <div className="container mx-auto px-4">
            <h2 className="text-3xl font-bold text-center mb-12">Types of Wedding Venues</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {venueTypes.map((venue, index) => (
                <Card key={index} className="hover:shadow-lg transition-shadow">
                  <CardHeader>
                    <div className="flex items-center gap-3 mb-4">
                      <venue.icon className="w-8 h-8 text-primary" />
                      <div>
                        <CardTitle className="text-xl">{venue.title}</CardTitle>
                        <p className="text-sm text-muted-foreground">{venue.description}</p>
                      </div>
                    </div>
                    <div className="flex gap-2 mb-4">
                      <Badge variant="outline">{venue.capacity}</Badge>
                      <Badge variant="secondary">{venue.bestFor}</Badge>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <ul className="space-y-2">
                      {venue.features.map((feature, idx) => (
                        <li key={idx} className="flex items-start gap-2">
                          <CheckCircle className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                          <span className="text-sm">{feature}</span>
                        </li>
                      ))}
                    </ul>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Booking Process */}
        <section className="py-16 bg-muted/30">
          <div className="container mx-auto px-4">
            <h2 className="text-3xl font-bold text-center mb-12">Venue Booking Process</h2>
            <div className="max-w-4xl mx-auto space-y-8">
              {bookingProcess.map((process, index) => (
                <Card key={index}>
                  <CardHeader>
                    <CardTitle className="text-xl text-primary">{process.step}</CardTitle>
                    <p className="text-muted-foreground">{process.description}</p>
                  </CardHeader>
                  <CardContent>
                    <ul className="space-y-2">
                      {process.tips.map((tip, idx) => (
                        <li key={idx} className="flex items-start gap-2">
                          <CheckCircle className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                          <span className="text-sm">{tip}</span>
                        </li>
                      ))}
                    </ul>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Important Questions */}
        <section className="py-16">
          <div className="container mx-auto px-4">
            <h2 className="text-3xl font-bold text-center mb-12">Questions to Ask Venues</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {importantQuestions.map((category, index) => (
                <Card key={index}>
                  <CardHeader>
                    <CardTitle className="text-lg text-primary">{category.category}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ul className="space-y-3">
                      {category.questions.map((question, idx) => (
                        <li key={idx} className="flex items-start gap-2">
                          <span className="text-primary font-bold">?</span>
                          <span className="text-sm">{question}</span>
                        </li>
                      ))}
                    </ul>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Venue Checklist */}
        <section className="py-16 bg-muted/30">
          <div className="container mx-auto px-4">
            <h2 className="text-3xl font-bold text-center mb-12">Venue Inspection Checklist</h2>
            <div className="max-w-4xl mx-auto">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-3">
                    <CheckCircle className="w-6 h-6 text-primary" />
                    Essential Things to Check
                  </CardTitle>
                  <p className="text-muted-foreground">Use this checklist during your venue visits</p>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {venueChecklist.map((item, index) => (
                      <div key={index} className="flex items-start gap-2">
                        <CheckCircle className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                        <span className="text-sm">{item}</span>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </section>

        {/* Budget Tips */}
        <section className="py-16">
          <div className="container mx-auto px-4">
            <h2 className="text-3xl font-bold text-center mb-12">Budget Tips</h2>
            <div className="max-w-4xl mx-auto space-y-6">
              {budgetTips.map((tip, index) => (
                <Card key={index}>
                  <CardContent className="pt-6">
                    <div className="flex items-start gap-4">
                      <div className="w-8 h-8 bg-primary/10 rounded-lg flex items-center justify-center flex-shrink-0">
                        <span className="text-primary font-bold">{index + 1}</span>
                      </div>
                      <div>
                        <h3 className="font-semibold mb-2">{tip.tip}</h3>
                        <p className="text-sm text-muted-foreground">{tip.description}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Quick Actions */}
        <section className="py-16 bg-muted/30">
          <div className="container mx-auto px-4 text-center">
            <h2 className="text-3xl font-bold mb-8">Start Your Venue Search</h2>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/venues">
                <Button size="lg" className="px-8">
                  <Search className="w-5 h-5 mr-2" />
                  Browse All Venues
                </Button>
              </Link>
              <Link href="/venues?type=banquet">
                <Button variant="outline" size="lg" className="px-8">
                  <MapPin className="w-5 h-5 mr-2" />
                  Banquet Halls
                </Button>
              </Link>
              <Link href="/venues?type=resort">
                <Button variant="outline" size="lg" className="px-8">
                  <Users className="w-5 h-5 mr-2" />
                  Hotels & Resorts
                </Button>
              </Link>
            </div>
          </div>
        </section>

        <Footer />
      </div>
    </>
  )
}
