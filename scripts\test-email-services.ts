#!/usr/bin/env ts-node

/**
 * Email Services Test Runner
 * Tests all email functionality including authorization and error handling
 * Run with: npx ts-node scripts/test-email-services.ts
 */

import { emailService } from '../lib/services/emailService';

// Test data
const testData = {
  loginOTP: {
    email: '<EMAIL>',
    userName: 'TestUser',
    otp: '123456',
    expiryMinutes: 10
  },
  userWelcome: {
    email: '<EMAIL>',
    firstName: 'John',
    lastName: 'Doe',
    type: 'signup' as const,
    isVendor: false
  },
  newsletterWelcome: {
    email: '<EMAIL>',
    firstName: 'Jane',
    lastName: 'Smith',
    type: 'newsletter' as const,
    interests: ['photography', 'decorations'],
    preferences: {
      weddingTips: true,
      vendorRecommendations: true,
      frequency: 'weekly'
    }
  },
  bookingConfirmation: {
    email: '<EMAIL>',
    userName: 'TestUser',
    bookingId: 'booking_123',
    entityName: 'Dream Venue',
    entityType: 'venue' as const,
    eventDate: '2024-12-25',
    eventTime: '6:00 PM',
    amount: '₹50000',
    status: 'confirmed' as const,
    trackingUrl: 'https://example.com/track'
  },
  paymentSuccess: {
    email: '<EMAIL>',
    userName: 'TestUser',
    orderId: 'order_123',
    amount: '₹25000',
    items: [
      { name: 'Wedding Dress', quantity: 1, price: '₹25000' }
    ],
    invoiceUrl: 'https://example.com/invoice.pdf',
    trackingUrl: 'https://example.com/track'
  },
  weeklyNews: {
    email: '<EMAIL>',
    userName: 'TestUser',
    articles: [
      {
        title: 'Top Wedding Trends 2024',
        excerpt: 'Discover the latest wedding trends',
        url: 'https://example.com/trends'
      }
    ],
    offers: [
      {
        title: 'Special Discount',
        discount: '20% OFF',
        url: 'https://example.com/offer'
      }
    ]
  },
  offers: {
    email: '<EMAIL>',
    userName: 'TestUser',
    offers: [
      {
        title: 'Wedding Photography Package',
        description: 'Professional wedding photography',
        discount: '15% OFF',
        validUntil: '2024-12-31',
        url: 'https://example.com/photo-offer'
      }
    ]
  },
  favoritesNotification: {
    email: '<EMAIL>',
    userName: 'TestUser',
    updates: [
      {
        name: 'Dream Venue',
        type: 'venue' as const,
        updateType: 'price_drop' as const,
        url: 'https://example.com/venue'
      }
    ]
  },
  vendorLaunch: {
    email: '<EMAIL>',
    userName: 'TestUser',
    newVendors: [
      {
        name: 'New Photography Studio',
        category: 'Photography',
        city: 'Chennai',
        discount: '10% OFF',
        url: 'https://example.com/studio'
      }
    ]
  },
  availabilityCheck: {
    email: '<EMAIL>',
    userName: 'TestUser',
    entityName: 'Dream Venue',
    entityType: 'venue' as const,
    requestedDate: '2024-12-25',
    status: 'available' as const,
    alternativeDates: ['2024-12-26', '2024-12-27'],
    url: 'https://example.com/venue'
  }
};

// Test results storage
const testResults: { [key: string]: { success: boolean; error?: string; duration: number } } = {};

// Utility function to run a test
async function runTest(
  testName: string,
  testFunction: () => Promise<boolean>
): Promise<void> {
  console.log(`\n🧪 Testing: ${testName}`);
  console.log('─'.repeat(50));
  
  const startTime = Date.now();
  
  try {
    const result = await testFunction();
    const duration = Date.now() - startTime;
    
    testResults[testName] = {
      success: result,
      duration
    };
    
    if (result) {
      console.log(`✅ ${testName}: PASSED (${duration}ms)`);
    } else {
      console.log(`❌ ${testName}: FAILED (${duration}ms)`);
      testResults[testName].error = 'Function returned false';
    }
  } catch (error) {
    const duration = Date.now() - startTime;
    testResults[testName] = {
      success: false,
      error: error instanceof Error ? error.message : String(error),
      duration
    };
    console.log(`💥 ${testName}: ERROR (${duration}ms)`);
    console.log(`   Error: ${testResults[testName].error}`);
  }
}

// Test functions
async function testLoginOTPEmail(): Promise<boolean> {
  return await emailService.sendLoginOTPEmail(testData.loginOTP);
}

async function testUserWelcomeEmail(): Promise<boolean> {
  return await emailService.sendUserWelcomeEmail(testData.userWelcome);
}

async function testNewsletterWelcomeEmail(): Promise<boolean> {
  return await emailService.sendNewsletterWelcomeEmail(testData.newsletterWelcome);
}

async function testBookingConfirmationEmail(): Promise<boolean> {
  return await emailService.sendBookingConfirmationEmail(testData.bookingConfirmation);
}

async function testPaymentSuccessEmail(): Promise<boolean> {
  return await emailService.sendPaymentSuccessEmail(testData.paymentSuccess);
}

async function testWeeklyNewsEmail(): Promise<boolean> {
  return await emailService.sendWeeklyNewsEmail(testData.weeklyNews);
}

async function testOffersEmail(): Promise<boolean> {
  return await emailService.sendOffersEmail(testData.offers);
}

async function testFavoritesNotificationEmail(): Promise<boolean> {
  return await emailService.sendFavoritesNotificationEmail(testData.favoritesNotification);
}

async function testVendorLaunchEmail(): Promise<boolean> {
  return await emailService.sendVendorLaunchEmail(testData.vendorLaunch);
}

async function testAvailabilityCheckEmail(): Promise<boolean> {
  return await emailService.sendAvailabilityCheckEmail(testData.availabilityCheck);
}

async function testBulkEmails(): Promise<boolean> {
  return await emailService.sendBulkEmails(
    'WEEKLY_NEWS',
    ['<EMAIL>', '<EMAIL>'],
    { message: 'Test bulk email' }
  );
}

// Test template generation
function testTemplateGeneration(): void {
  console.log('\n🧪 Testing: Template Generation');
  console.log('─'.repeat(50));
  
  try {
    // Test login OTP template
    const loginTemplate = (emailService as any).generateLoginOTPTemplate(testData.loginOTP);
    console.log('✅ Login OTP Template: PASSED');
    console.log(`   Subject: ${loginTemplate.subject}`);
    console.log(`   HTML Length: ${loginTemplate.htmlContent.length} chars`);
    console.log(`   Text Length: ${loginTemplate.textContent.length} chars`);
    
    // Test booking confirmation template
    const bookingTemplate = (emailService as any).generateBookingConfirmationTemplate(testData.bookingConfirmation);
    console.log('✅ Booking Confirmation Template: PASSED');
    console.log(`   Subject: ${bookingTemplate.subject}`);
    console.log(`   HTML Length: ${bookingTemplate.htmlContent.length} chars`);
    console.log(`   Text Length: ${bookingTemplate.textContent.length} chars`);
    
    // Test payment success template
    const paymentTemplate = (emailService as any).generatePaymentSuccessTemplate(testData.paymentSuccess);
    console.log('✅ Payment Success Template: PASSED');
    console.log(`   Subject: ${paymentTemplate.subject}`);
    console.log(`   HTML Length: ${paymentTemplate.htmlContent.length} chars`);
    console.log(`   Text Length: ${paymentTemplate.textContent.length} chars`);
    
    // Test weekly news template
    const newsTemplate = (emailService as any).generateWeeklyNewsTemplate(testData.weeklyNews);
    console.log('✅ Weekly News Template: PASSED');
    console.log(`   Subject: ${newsTemplate.subject}`);
    console.log(`   HTML Length: ${newsTemplate.htmlContent.length} chars`);
    console.log(`   Text Length: ${newsTemplate.textContent.length} chars`);
    
    // Test offers template
    const offersTemplate = (emailService as any).generateOffersTemplate(testData.offers);
    console.log('✅ Offers Template: PASSED');
    console.log(`   Subject: ${offersTemplate.subject}`);
    console.log(`   HTML Length: ${offersTemplate.htmlContent.length} chars`);
    console.log(`   Text Length: ${offersTemplate.textContent.length} chars`);
    
    // Test favorites notification template
    const favoritesTemplate = (emailService as any).generateFavoritesNotificationTemplate(testData.favoritesNotification);
    console.log('✅ Favorites Notification Template: PASSED');
    console.log(`   Subject: ${favoritesTemplate.subject}`);
    console.log(`   HTML Length: ${favoritesTemplate.htmlContent.length} chars`);
    console.log(`   Text Length: ${favoritesTemplate.textContent.length} chars`);
    
    // Test vendor launch template
    const vendorTemplate = (emailService as any).generateVendorLaunchTemplate(testData.vendorLaunch);
    console.log('✅ Vendor Launch Template: PASSED');
    console.log(`   Subject: ${vendorTemplate.subject}`);
    console.log(`   HTML Length: ${vendorTemplate.htmlContent.length} chars`);
    console.log(`   Text Length: ${vendorTemplate.textContent.length} chars`);
    
    // Test availability check template
    const availabilityTemplate = (emailService as any).generateAvailabilityCheckTemplate(testData.availabilityCheck);
    console.log('✅ Availability Check Template: PASSED');
    console.log(`   Subject: ${availabilityTemplate.subject}`);
    console.log(`   HTML Length: ${availabilityTemplate.htmlContent.length} chars`);
    console.log(`   Text Length: ${availabilityTemplate.textContent.length} chars`);
    
    // Test newsletter welcome template
    const newsletterTemplate = (emailService as any).generateNewsletterWelcomeTemplate(testData.newsletterWelcome);
    console.log('✅ Newsletter Welcome Template: PASSED');
    console.log(`   Subject: ${newsletterTemplate.subject}`);
    console.log(`   HTML Length: ${newsletterTemplate.htmlContent.length} chars`);
    console.log(`   Text Length: ${newsletterTemplate.textContent.length} chars`);
    
    // Test user welcome template
    const userWelcomeTemplate = (emailService as any).generateUserWelcomeTemplate(testData.userWelcome);
    console.log('✅ User Welcome Template: PASSED');
    console.log(`   Subject: ${userWelcomeTemplate.subject}`);
    console.log(`   HTML Length: ${userWelcomeTemplate.htmlContent.length} chars`);
    console.log(`   Text Length: ${userWelcomeTemplate.textContent.length} chars`);
    
  } catch (error) {
    console.log('❌ Template Generation: ERROR');
    console.log(`   Error: ${error instanceof Error ? error.message : String(error)}`);
  }
}

// Main test runner
async function runAllTests(): Promise<void> {
  console.log('🚀 Starting Email Services Test Suite');
  console.log('='.repeat(60));
  console.log('Testing all email functionality including authorization fixes');
  console.log('='.repeat(60));
  
  const startTime = Date.now();
  
  // Run all email service tests
  await runTest('Login OTP Email', testLoginOTPEmail);
  await runTest('User Welcome Email', testUserWelcomeEmail);
  await runTest('Newsletter Welcome Email', testNewsletterWelcomeEmail);
  await runTest('Booking Confirmation Email', testBookingConfirmationEmail);
  await runTest('Payment Success Email', testPaymentSuccessEmail);
  await runTest('Weekly News Email', testWeeklyNewsEmail);
  await runTest('Offers Email', testOffersEmail);
  await runTest('Favorites Notification Email', testFavoritesNotificationEmail);
  await runTest('Vendor Launch Email', testVendorLaunchEmail);
  await runTest('Availability Check Email', testAvailabilityCheckEmail);
  await runTest('Bulk Emails', testBulkEmails);
  
  // Test template generation
  testTemplateGeneration();
  
  const totalDuration = Date.now() - startTime;
  
  // Print summary
  console.log('\n📊 Test Results Summary');
  console.log('='.repeat(60));
  
  const passedTests = Object.values(testResults).filter(r => r.success).length;
  const totalTests = Object.keys(testResults).length;
  
  console.log(`Total Tests: ${totalTests}`);
  console.log(`Passed: ${passedTests}`);
  console.log(`Failed: ${totalTests - passedTests}`);
  console.log(`Total Duration: ${totalDuration}ms`);
  
  console.log('\n📋 Detailed Results:');
  console.log('─'.repeat(60));
  
  Object.entries(testResults).forEach(([testName, result]) => {
    const status = result.success ? '✅ PASS' : '❌ FAIL';
    const error = result.error ? ` (${result.error})` : '';
    console.log(`${status} ${testName} - ${result.duration}ms${error}`);
  });
  
  // Print authorization fix verification
  console.log('\n🔐 Authorization Fix Verification');
  console.log('─'.repeat(60));
  console.log('✅ All GraphQL mutations now use authMode: "userPool"');
  console.log('✅ This should resolve the "Not Authorized" errors');
  console.log('✅ Test the booking flow to verify the fix works');
  
  if (passedTests === totalTests) {
    console.log('\n🎉 All tests passed! Email services are working correctly.');
  } else {
    console.log('\n⚠️  Some tests failed. Check the error messages above.');
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  runAllTests().catch(error => {
    console.error('💥 Test runner failed:', error);
    process.exit(1);
  });
}

export { runAllTests, testResults }; 