/**
 * Invoice Service for generating invoices for products, bookings, and subscriptions using AWS Amplify
 * Handles PDF generation, invoice storage, and invoice management
 */

import { generateClient } from '@aws-amplify/api';
import { 
  createInvoice, 
  updateInvoice, 
  deleteInvoice 
} from '@/src/graphql/mutations';
import { 
  getInvoice, 
  listInvoices, 
  invoicesByCustomerId, 
  invoicesByVendorId,
  invoicesByInvoiceNumber
} from '@/src/graphql/queries';

const client = generateClient();

export interface InvoiceItem {
  id: string;
  name: string;
  description?: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
  taxRate?: number;
  taxAmount?: number;
}

export interface InvoiceData {
  invoiceNumber: string;
  invoiceDate: string;
  dueDate?: string;
  type: 'product_order' | 'venue_booking' | 'vendor_booking' | 'subscription';
  
  // Customer/Client Information
  customer: {
    id: string;
    name: string;
    email: string;
    phone?: string;
    address?: {
      street: string;
      city: string;
      state: string;
      pincode: string;
      country: string;
    };
  };
  
  // Vendor/Business Information (for bookings)
  vendor?: {
    id: string;
    name: string;
    businessName?: string;
    email: string;
    phone?: string;
    address?: {
      street: string;
      city: string;
      state: string;
      pincode: string;
      country: string;
    };
    gstNumber?: string;
  };
  
  // Invoice Items
  items: InvoiceItem[];
  
  // Pricing Details
  subtotal: number;
  taxAmount: number;
  discountAmount?: number;
  totalAmount: number;
  
  // Payment Information
  paymentStatus: 'PAID' | 'PENDING' | 'OVERDUE' | 'CANCELLED';
  paymentMethod?: string;
  paymentDate?: string;
  transactionId?: string;
  
  // Additional Information
  notes?: string;
  terms?: string;
  
  // Booking specific (for venue/vendor bookings)
  eventDate?: string;
  eventTime?: string;
  eventLocation?: string;
}

export interface InvoiceTemplate {
  companyName: string;
  companyLogo?: string;
  companyAddress: {
    street: string;
    city: string;
    state: string;
    pincode: string;
    country: string;
  };
  companyContact: {
    email: string;
    phone: string;
    website?: string;
  };
  gstNumber?: string;
  panNumber?: string;
}

class InvoiceService {
  private defaultTemplate: InvoiceTemplate = {
    companyName: 'BookmyFestive',
    companyLogo: '/logo.png',
    companyAddress: {
      street: 'Wedding Planning Hub',
      city: 'Chennai',
      state: 'Tamil Nadu',
      pincode: '600001',
      country: 'India'
    },
    companyContact: {
      email: '<EMAIL>',
      phone: '+91 8148376909',
      website: 'https://BookmyFestive.com'
    },
    gstNumber: 'GST123456789',
    panNumber: 'PAN123456789'
  };

  /**
   * Map payment status to valid GraphQL enum values
   */
  private mapPaymentStatus(status: string): 'PAID' | 'PENDING' | 'OVERDUE' | 'CANCELLED' {
    const statusLower = status.toLowerCase();
    switch (statusLower) {
      case 'paid':
      case 'completed':
      case 'success':
        return 'PAID';
      case 'pending':
      case 'processing':
      case 'cod_pending':
        return 'PENDING';
      case 'overdue':
      case 'late':
        return 'OVERDUE';
      case 'cancelled':
      case 'failed':
      case 'refunded':
        return 'CANCELLED';
      default:
        return 'PENDING';
    }
  }

  /**
   * Generate invoice for booking (generic method for any booking type)
   */
  async generateBookingInvoice(bookingData: {
    bookingId: string;
    customerId: string;
    customerName: string;
    customerEmail: string;
    customerPhone?: string;
    customerAddress?: {
      street: string;
      city: string;
      state: string;
      pincode: string;
      country: string;
    };
    vendorId?: string;
    vendorName?: string;
    vendorEmail?: string;
    vendorBusinessName?: string;
    vendorGstNumber?: string;
    vendorAddress?: {
      street: string;
      city: string;
      state: string;
      pincode: string;
      country: string;
    };
    venueId?: string;
    venueName?: string;
    venueEmail?: string;
    venueAddress?: {
      street: string;
      city: string;
      state: string;
      pincode: string;
      country: string;
    };
    serviceName: string;
    serviceDescription?: string;
    eventDate: string;
    eventTime?: string;
    eventLocation?: string;
    eventDuration?: string;
    capacity?: string;
    amount: number;
    taxAmount?: number;
    discountAmount?: number;
    totalAmount: number;
    paymentStatus: string;
    paymentMethod?: string;
    transactionId?: string;
    terms?: string;
    bookingType: 'vendor' | 'venue';
  }): Promise<{ success: boolean; invoiceId?: string; invoiceUrl?: string; error?: string }> {
    try {
      if (bookingData.bookingType === 'vendor') {
        return await this.generateVendorServiceInvoice({
          bookingId: bookingData.bookingId,
          customerId: bookingData.customerId,
          customerName: bookingData.customerName,
          customerEmail: bookingData.customerEmail,
          customerPhone: bookingData.customerPhone,
          customerAddress: bookingData.customerAddress,
          vendorId: bookingData.vendorId || '',
          vendorName: bookingData.vendorName || 'Vendor',
          vendorEmail: bookingData.vendorEmail || '',
          vendorBusinessName: bookingData.vendorBusinessName,
          vendorGstNumber: bookingData.vendorGstNumber,
          vendorAddress: bookingData.vendorAddress,
          serviceName: bookingData.serviceName,
          serviceDescription: bookingData.serviceDescription,
          eventDate: bookingData.eventDate,
          eventTime: bookingData.eventTime,
          eventLocation: bookingData.eventLocation,
          amount: bookingData.amount,
          taxAmount: bookingData.taxAmount,
          discountAmount: bookingData.discountAmount,
          totalAmount: bookingData.totalAmount,
          paymentStatus: bookingData.paymentStatus,
          paymentMethod: bookingData.paymentMethod,
          transactionId: bookingData.transactionId,
          terms: bookingData.terms
        });
      } else if (bookingData.bookingType === 'venue') {
        return await this.generateVenueBookingInvoice({
          bookingId: bookingData.bookingId,
          customerId: bookingData.customerId,
          customerName: bookingData.customerName,
          customerEmail: bookingData.customerEmail,
          customerPhone: bookingData.customerPhone,
          customerAddress: bookingData.customerAddress,
          venueId: bookingData.venueId || '',
          venueName: bookingData.venueName || 'Venue',
          venueEmail: bookingData.venueEmail || '',
          venueAddress: bookingData.venueAddress || {
            street: 'Venue Street',
            city: 'Venue City',
            state: 'Venue State',
            pincode: '600001',
            country: 'India'
          },
          packageName: bookingData.serviceName,
          packageDescription: bookingData.serviceDescription,
          eventDate: bookingData.eventDate,
          eventTime: bookingData.eventTime,
          eventDuration: bookingData.eventDuration,
          capacity: bookingData.capacity,
          amount: bookingData.amount,
          taxAmount: bookingData.taxAmount,
          discountAmount: bookingData.discountAmount,
          totalAmount: bookingData.totalAmount,
          paymentStatus: bookingData.paymentStatus,
          paymentMethod: bookingData.paymentMethod,
          transactionId: bookingData.transactionId,
          terms: bookingData.terms
        });
      } else {
        throw new Error('Invalid booking type. Must be "vendor" or "venue"');
      }
    } catch (error) {
      console.error('Error generating booking invoice:', error);
      return { success: false, error: 'Failed to generate booking invoice' };
    }
  }

  /**
   * Generate invoice for product order
   */
  async generateProductOrderInvoice(orderData: {
    orderId: string;
    customerId: string;
    customerName: string;
    customerEmail: string;
    items: Array<{
      productId: string;
      productName: string;
      quantity: number;
      price: number;
    }>;
    totalAmount: number;
    paymentStatus: string;
    paymentMethod?: string;
    transactionId?: string;
  }): Promise<{ success: boolean; invoiceUrl?: string; error?: string }> {
    try {
      // Generate required fields
      const invoiceNumber = this.generateInvoiceNumber('ORD');
      const invoiceDate = new Date().toISOString().split('T')[0];
      
      const invoiceInput = {
        invoiceNumber: invoiceNumber,
        invoiceDate: invoiceDate,
        type: 'PRODUCT_ORDER',
        customerId: orderData.customerId,
        customerName: orderData.customerName,
        customerEmail: orderData.customerEmail,
        items: orderData.items.map(item => ({
          id: item.productId,
          name: item.productName,
          description: `Product: ${item.productName}`,
          quantity: item.quantity,
          unitPrice: item.price,
          totalPrice: item.quantity * item.price,
          taxRate: 0,
          taxAmount: 0
        })),
        subtotal: orderData.totalAmount,
        taxAmount: 0, // Calculate based on items
        totalAmount: orderData.totalAmount,
        paymentStatus: this.mapPaymentStatus(orderData.paymentStatus),
        paymentMethod: orderData.paymentMethod,
        paymentDate: this.mapPaymentStatus(orderData.paymentStatus) === 'PAID' ? new Date().toISOString() : undefined,
        transactionId: orderData.transactionId,
        notes: `Order ID: ${orderData.orderId}`
      };

      console.log('Creating invoice with input:', invoiceInput);

      const result = await client.graphql({
        query: createInvoice,
        variables: { input: invoiceInput },
        authMode: 'userPool'
      });

      const response = (result.data as any)?.createInvoice;

      if (response?.success) {
        return {
          success: true,
          invoiceUrl: response.pdfUrl
        };
      } else {
        return {
          success: false,
          error: response?.error || 'Failed to generate invoice'
        };
      }
    } catch (error: any) {
      console.error('Error generating product order invoice:', error);
      return { success: false, error: error.message || 'Failed to generate invoice' };
    }
  }

  /**
   * Generate invoice for shopping order (product purchase)
   */
  async generateShoppingOrderInvoice(orderData: {
    orderId: string;
    customerId: string;
    customerName: string;
    customerEmail: string;
    customerPhone?: string;
    customerAddress?: {
      street: string;
      city: string;
      state: string;
      pincode: string;
      country: string;
    };
    items: Array<{
      productId: string;
      productName: string;
      description?: string;
      quantity: number;
      unitPrice: number;
      taxRate?: number;
    }>;
    subtotal: number;
    taxAmount: number;
    discountAmount?: number;
    totalAmount: number;
    paymentStatus: string;
    paymentMethod?: string;
    transactionId?: string;
    shippingAddress?: {
      street: string;
      city: string;
      state: string;
      pincode: string;
      country: string;
    };
  }): Promise<{ success: boolean; invoiceId?: string; invoiceUrl?: string; error?: string }> {
    try {
      const invoiceData: InvoiceData = {
        invoiceNumber: this.generateInvoiceNumber('ORD'),
        invoiceDate: new Date().toISOString().split('T')[0],
        type: 'product_order',
        customer: {
          id: orderData.customerId,
          name: orderData.customerName,
          email: orderData.customerEmail,
          phone: orderData.customerPhone,
          address: orderData.customerAddress
        },
        items: orderData.items.map(item => ({
          id: item.productId,
          name: item.productName,
          description: item.description || '',
          quantity: item.quantity,
          unitPrice: item.unitPrice,
          totalPrice: item.quantity * item.unitPrice,
          taxRate: item.taxRate || 0,
          taxAmount: (item.quantity * item.unitPrice * (item.taxRate || 0)) / 100
        })),
        subtotal: orderData.subtotal,
        taxAmount: orderData.taxAmount,
        discountAmount: orderData.discountAmount || 0,
        totalAmount: orderData.totalAmount,
        paymentStatus: this.mapPaymentStatus(orderData.paymentStatus),
        paymentMethod: orderData.paymentMethod,
        transactionId: orderData.transactionId,
        notes: `Shopping Order ID: ${orderData.orderId}${orderData.shippingAddress ? `\nShipping to: ${orderData.shippingAddress.street}, ${orderData.shippingAddress.city}` : ''}`
      };

      return await this.createInvoice(invoiceData);
    } catch (error) {
      console.error('Error generating shopping order invoice:', error);
      return { success: false, error: 'Failed to generate invoice' };
    }
  }

  /**
   * Generate invoice for vendor service booking
   */
  async generateVendorServiceInvoice(bookingData: {
    bookingId: string;
    customerId: string;
    customerName: string;
    customerEmail: string;
    customerPhone?: string;
    customerAddress?: {
      street: string;
      city: string;
      state: string;
      pincode: string;
      country: string;
    };
    vendorId: string;
    vendorName: string;
    vendorEmail: string;
    vendorBusinessName?: string;
    vendorGstNumber?: string;
    vendorAddress?: {
      street: string;
      city: string;
      state: string;
      pincode: string;
      country: string;
    };
    serviceName: string;
    serviceDescription?: string;
    eventDate: string;
    eventTime?: string;
    eventLocation?: string;
    amount: number;
    taxAmount?: number;
    discountAmount?: number;
    totalAmount: number;
    paymentStatus: string;
    paymentMethod?: string;
    transactionId?: string;
    terms?: string;
  }): Promise<{ success: boolean; invoiceId?: string; invoiceUrl?: string; error?: string }> {
    try {
      const invoiceData: InvoiceData = {
        invoiceNumber: this.generateInvoiceNumber('VND'),
        invoiceDate: new Date().toISOString().split('T')[0],
        type: 'vendor_booking',
        customer: {
          id: bookingData.customerId,
          name: bookingData.customerName,
          email: bookingData.customerEmail,
          phone: bookingData.customerPhone,
          address: bookingData.customerAddress
        },
        vendor: {
          id: bookingData.vendorId,
          name: bookingData.vendorName,
          businessName: bookingData.vendorBusinessName,
          email: bookingData.vendorEmail,
          address: bookingData.vendorAddress,
          gstNumber: bookingData.vendorGstNumber
        },
        items: [{
          id: bookingData.bookingId,
          name: bookingData.serviceName,
          description: bookingData.serviceDescription || `${bookingData.serviceName} service for ${bookingData.eventDate}`,
          quantity: 1,
          unitPrice: bookingData.amount,
          totalPrice: bookingData.amount,
          taxRate: bookingData.taxAmount ? (bookingData.taxAmount / bookingData.amount) * 100 : 0,
          taxAmount: bookingData.taxAmount || 0
        }],
        subtotal: bookingData.amount,
        taxAmount: bookingData.taxAmount || 0,
        discountAmount: bookingData.discountAmount || 0,
        totalAmount: bookingData.totalAmount,
        paymentStatus: bookingData.paymentStatus as any,
        paymentMethod: bookingData.paymentMethod,
        transactionId: bookingData.transactionId,
        eventDate: bookingData.eventDate,
        eventTime: bookingData.eventTime,
        eventLocation: bookingData.eventLocation,
        notes: `Vendor Service Booking ID: ${bookingData.bookingId}`,
        terms: bookingData.terms
      };

      return await this.createInvoice(invoiceData);
    } catch (error) {
      console.error('Error generating vendor service invoice:', error);
      return { success: false, error: 'Failed to generate invoice' };
    }
  }

  /**
   * Generate invoice for venue booking
   */
  async generateVenueBookingInvoice(bookingData: {
    bookingId: string;
    customerId: string;
    customerName: string;
    customerEmail: string;
    customerPhone?: string;
    customerAddress?: {
      street: string;
      city: string;
      state: string;
      pincode: string;
      country: string;
    };
    venueId: string;
    venueName: string;
    venueEmail: string;
    venueAddress: {
      street: string;
      city: string;
      state: string;
      pincode: string;
      country: string;
    };
    packageName: string;
    packageDescription?: string;
    eventDate: string;
    eventTime?: string;
    eventDuration?: string;
    capacity?: string;
    amount: number;
    taxAmount?: number;
    discountAmount?: number;
    totalAmount: number;
    paymentStatus: string;
    paymentMethod?: string;
    transactionId?: string;
    terms?: string;
  }): Promise<{ success: boolean; invoiceId?: string; invoiceUrl?: string; error?: string }> {
    try {
      const invoiceData: InvoiceData = {
        invoiceNumber: this.generateInvoiceNumber('VEN'),
        invoiceDate: new Date().toISOString().split('T')[0],
        type: 'venue_booking',
        customer: {
          id: bookingData.customerId,
          name: bookingData.customerName,
          email: bookingData.customerEmail,
          phone: bookingData.customerPhone,
          address: bookingData.customerAddress
        },
        vendor: {
          id: bookingData.venueId,
          name: bookingData.venueName,
          businessName: bookingData.venueName,
          email: bookingData.venueEmail,
          address: bookingData.venueAddress
        },
        items: [{
          id: bookingData.bookingId,
          name: `${bookingData.packageName} - ${bookingData.venueName}`,
          description: `${bookingData.packageDescription || 'Venue booking package'} for ${bookingData.eventDate}${bookingData.eventDuration ? ` (${bookingData.eventDuration})` : ''}${bookingData.capacity ? ` - Capacity: ${bookingData.capacity}` : ''}`,
          quantity: 1,
          unitPrice: bookingData.amount,
          totalPrice: bookingData.amount,
          taxRate: bookingData.taxAmount ? (bookingData.taxAmount / bookingData.amount) * 100 : 0,
          taxAmount: bookingData.taxAmount || 0
        }],
        subtotal: bookingData.amount,
        taxAmount: bookingData.taxAmount || 0,
        discountAmount: bookingData.discountAmount || 0,
        totalAmount: bookingData.totalAmount,
        paymentStatus: bookingData.paymentStatus as any,
        paymentMethod: bookingData.paymentMethod,
        transactionId: bookingData.transactionId,
        eventDate: bookingData.eventDate,
        eventTime: bookingData.eventTime,
        eventLocation: `${bookingData.venueAddress.street}, ${bookingData.venueAddress.city}`,
        notes: `Venue Booking ID: ${bookingData.bookingId}`,
        terms: bookingData.terms
      };

      return await this.createInvoice(invoiceData);
    } catch (error) {
      console.error('Error generating venue booking invoice:', error);
      return { success: false, error: 'Failed to generate invoice' };
    }
  }

  /**
   * Generate invoice for vendor subscription
   */
  async generateSubscriptionInvoice(subscriptionData: {
    subscriptionId: string;
    vendorId: string;
    vendorName: string;
    vendorEmail: string;
    planName: string;
    planPrice: number;
    billingPeriod: string;
    paymentStatus: string;
    paymentMethod?: string;
    transactionId?: string;
  }): Promise<{ success: boolean; invoiceUrl?: string; error?: string }> {
    try {
      const invoiceData: InvoiceData = {
        invoiceNumber: this.generateInvoiceNumber('SUB'),
        invoiceDate: new Date().toISOString().split('T')[0],
        type: 'subscription',
        customer: {
          id: subscriptionData.vendorId,
          name: subscriptionData.vendorName,
          email: subscriptionData.vendorEmail
        },
        items: [{
          id: subscriptionData.subscriptionId,
          name: `${subscriptionData.planName} - ${subscriptionData.billingPeriod}`,
          description: `Vendor subscription plan`,
          quantity: 1,
          unitPrice: subscriptionData.planPrice,
          totalPrice: subscriptionData.planPrice
        }],
        subtotal: subscriptionData.planPrice,
        taxAmount: 0,
        totalAmount: subscriptionData.planPrice,
        paymentStatus: subscriptionData.paymentStatus as any,
        paymentMethod: subscriptionData.paymentMethod,
        transactionId: subscriptionData.transactionId,
        notes: `Subscription ID: ${subscriptionData.subscriptionId}`
      };

      return await this.createInvoice(invoiceData);
    } catch (error) {
      console.error('Error generating subscription invoice:', error);
      return { success: false, error: 'Failed to generate invoice' };
    }
  }

  /**
   * Create and store invoice using Amplify GraphQL
   */
  private async createInvoice(invoiceData: InvoiceData): Promise<{ success: boolean; invoiceUrl?: string; error?: string }> {
    try {
      const invoiceInput = {
        invoiceNumber: invoiceData.invoiceNumber,
        invoiceDate: invoiceData.invoiceDate,
        dueDate: invoiceData.dueDate,
        type: invoiceData.type.toUpperCase(),
        customerId: invoiceData.customer.id,
        customerName: invoiceData.customer.name,
        customerEmail: invoiceData.customer.email,
        customerPhone: invoiceData.customer.phone,
        customerAddress: invoiceData.customer.address,
        vendorId: invoiceData.vendor?.id,
        vendorName: invoiceData.vendor?.name,
        vendorEmail: invoiceData.vendor?.email,
        vendorBusinessName: invoiceData.vendor?.businessName,
        vendorAddress: invoiceData.vendor?.address,
        vendorGstNumber: invoiceData.vendor?.gstNumber,
        items: invoiceData.items.map(item => ({
          id: item.id,
          name: item.name,
          description: item.description,
          quantity: item.quantity,
          unitPrice: item.unitPrice,
          totalPrice: item.totalPrice,
          taxRate: item.taxRate || 0,
          taxAmount: item.taxAmount || 0
        })),
        subtotal: invoiceData.subtotal,
        taxAmount: invoiceData.taxAmount || 0,
        discountAmount: invoiceData.discountAmount || 0,
        totalAmount: invoiceData.totalAmount,
        paymentStatus: invoiceData.paymentStatus.toUpperCase(),
        paymentMethod: invoiceData.paymentMethod,
        paymentDate: invoiceData.paymentDate,
        transactionId: invoiceData.transactionId,
        notes: invoiceData.notes,
        terms: invoiceData.terms,
        eventDate: invoiceData.eventDate,
        eventTime: invoiceData.eventTime,
        eventLocation: invoiceData.eventLocation
      };

      const result = await client.graphql({
        query: createInvoice,
        variables: { input: invoiceInput },
        authMode: 'userPool'
      });

      const createdInvoice = result.data.createInvoice;

      if (createdInvoice) {
        console.log('Invoice created successfully:', createdInvoice.id);
        return {
          success: true,
          invoiceUrl: createdInvoice.pdfUrl
        };
      } else {
        return { success: false, error: 'Invoice creation failed' };
      }
    } catch (error) {
      console.error('Error creating invoice:', error);
      return { success: false, error: error.message || 'Network error occurred' };
    }
  }

  /**
   * Get invoice by ID using Amplify GraphQL
   */
  async getInvoice(invoiceId: string): Promise<InvoiceData | null> {
    try {
      const result = await client.graphql({
        query: getInvoice,
        variables: { id: invoiceId },
        authMode: 'userPool'
      });

      const invoice = (result.data as any)?.getInvoice;
      if (!invoice) {
        return null;
      }

      // Transform GraphQL response to InvoiceData format
      return {
        invoiceNumber: invoice.invoiceNumber,
        invoiceDate: invoice.invoiceDate,
        dueDate: invoice.dueDate,
        type: invoice.type?.toLowerCase() as any,
        customer: {
          id: invoice.customerId,
          name: invoice.customerName,
          email: invoice.customerEmail,
          phone: invoice.customerPhone,
          address: invoice.customerAddress
        },
        vendor: invoice.vendorId ? {
          id: invoice.vendorId,
          name: invoice.vendorName,
          businessName: invoice.vendorBusinessName,
          email: invoice.vendorEmail || ''
        } : undefined,
        items: invoice.items || [],
        subtotal: invoice.subtotal,
        taxAmount: invoice.taxAmount,
        discountAmount: invoice.discountAmount,
        totalAmount: invoice.totalAmount,
        paymentStatus: invoice.paymentStatus?.toLowerCase() as any,
        paymentMethod: invoice.paymentMethod,
        paymentDate: invoice.paymentDate,
        transactionId: invoice.transactionId,
        eventDate: invoice.eventDate,
        eventLocation: invoice.eventLocation,
        notes: invoice.notes,
        terms: invoice.terms
      };
    } catch (error) {
      console.error('Error fetching invoice:', error);
      return null;
    }
  }

  /**
   * Get invoices for a customer/vendor
   */
  async getInvoicesForUser(userId: string, userType: 'customer' | 'vendor' | 'admin'): Promise<InvoiceData[]> {
    try {
      console.log('Fetching invoices for user:', userId, 'type:', userType);

      let result;

      if (userType === 'admin') {
        // Admin can see all invoices
        result = await client.graphql({
          query: listInvoices,
          variables: { limit: 100 }
        });
      } else if (userType === 'vendor') {
        // Vendor sees invoices where they are the vendor
        result = await client.graphql({
          query: invoicesByVendorId,
          variables: { vendorId: userId, limit: 50 },
          authMode: 'userPool'
        });
      } else {
        // Customer sees invoices where they are the customer
        result = await client.graphql({
          query: invoicesByCustomerId,
          variables: { customerId: userId, limit: 50 },
          authMode: 'userPool'
        });
      }

      const invoices = result.data?.listInvoices?.items ||
                      result.data?.invoicesByVendorId?.items ||
                      result.data?.invoicesByCustomerId?.items ||
                      [];

      // Transform GraphQL response to InvoiceData format
      return invoices.map((invoice: any) => ({
        id: invoice.id,
        invoiceNumber: invoice.invoiceNumber,
        invoiceDate: invoice.invoiceDate,
        dueDate: invoice.dueDate,
        type: invoice.type?.toLowerCase() as any,
        customer: {
          id: invoice.customerId,
          name: invoice.customerName,
          email: invoice.customerEmail
        },
        vendor: invoice.vendorId ? {
          id: invoice.vendorId,
          name: invoice.vendorName,
          businessName: invoice.vendorBusinessName,
          email: invoice.vendorEmail || ''
        } : undefined,
        items: [], // Items would need to be fetched separately if needed
        subtotal: invoice.subtotal,
        taxAmount: invoice.taxAmount,
        discountAmount: invoice.discountAmount || 0,
        totalAmount: invoice.totalAmount,
        paymentStatus: invoice.paymentStatus?.toLowerCase() as any,
        paymentMethod: invoice.paymentMethod,
        paymentDate: invoice.paymentDate,
        transactionId: invoice.transactionId,
        eventDate: invoice.eventDate,
        eventLocation: invoice.eventLocation,
        notes: invoice.notes,
        terms: invoice.terms
      }));
    } catch (error) {
      console.error('Error fetching user invoices:', error);
      // Fallback to mock data if GraphQL fails
      console.log('Falling back to mock data due to error');
      return this.getMockInvoices(userType);
    }
  }

  /**
   * Get invoices by invoice number
   */
  async getInvoiceByNumber(invoiceNumber: string): Promise<InvoiceData | null> {
    try {
      const result = await client.graphql({
        query: invoicesByInvoiceNumber,
        variables: { invoiceNumber },
        authMode: 'userPool'
      });

      const invoice = (result.data as any)?.invoicesByInvoiceNumber?.items?.[0];
      if (!invoice) {
        return null;
      }

      return this.transformGraphQLInvoice(invoice);
    } catch (error) {
      console.error('Error fetching invoice by number:', error);
      return null;
    }
  }

  /**
   * Get invoices for admin dashboard (all invoices with pagination)
   */
  async getInvoicesForAdmin(
    limit: number = 50,
    nextToken?: string,
    filters?: {
      type?: string;
      paymentStatus?: string;
      dateFrom?: string;
      dateTo?: string;
      customerId?: string;
      vendorId?: string;
    }
  ): Promise<{ invoices: InvoiceData[]; nextToken?: string; total?: number }> {
    try {
      let query = listInvoices;
      let variables: any = { limit };

      if (nextToken) {
        variables.nextToken = nextToken;
      }

      // Apply filters if provided
      if (filters) {
        // Note: For complex filtering, you might need to use a custom resolver
        // or implement filtering in the application layer
        if (filters.type) {
          variables.filter = { type: { eq: filters.type.toUpperCase() } };
        }
        if (filters.paymentStatus) {
          variables.filter = { 
            ...variables.filter,
            paymentStatus: { eq: filters.paymentStatus.toUpperCase() }
          };
        }
      }

      const result = await client.graphql({
        query,
        variables
      });

      const invoices = result.data?.listInvoices?.items || [];
      const nextTokenResult = result.data?.listInvoices?.nextToken;
      const total = result.data?.listInvoices?.items?.length || 0;

      return {
        invoices: invoices.map(invoice => this.transformGraphQLInvoice(invoice)),
        nextToken: nextTokenResult,
        total
      };
    } catch (error) {
      console.error('Error fetching admin invoices:', error);
      return { invoices: [], total: 0 };
    }
  }

  /**
   * Get invoice statistics for dashboard
   */
  async getInvoiceStatistics(): Promise<{
    totalInvoices: number;
    totalRevenue: number;
    pendingAmount: number;
    paidAmount: number;
    overdueAmount: number;
    cancelledAmount: number;
    monthlyStats: Array<{
      month: string;
      count: number;
      revenue: number;
    }>;
  }> {
    try {
      // Get all invoices for statistics
      const result = await client.graphql({
        query: listInvoices,
        variables: { limit: 1000 } // Get more invoices for accurate stats
      });

      const invoices = result.data?.listInvoices?.items || [];

      // Calculate statistics
      const stats = {
        totalInvoices: invoices.length,
        totalRevenue: 0,
        pendingAmount: 0,
        paidAmount: 0,
        overdueAmount: 0,
        cancelledAmount: 0,
        monthlyStats: [] as Array<{ month: string; count: number; revenue: number }>
      };

      const monthlyData: { [key: string]: { count: number; revenue: number } } = {};

      invoices.forEach(invoice => {
        const amount = invoice.totalAmount || 0;
        stats.totalRevenue += amount;

        // Categorize by payment status
        switch (invoice.paymentStatus?.toLowerCase()) {
          case 'paid':
            stats.paidAmount += amount;
            break;
          case 'pending':
            stats.pendingAmount += amount;
            break;
          case 'overdue':
            stats.overdueAmount += amount;
            break;
          case 'cancelled':
            stats.cancelledAmount += amount;
            break;
        }

        // Monthly statistics
        if (invoice.createdAt) {
          const date = new Date(invoice.createdAt);
          const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
          
          if (!monthlyData[monthKey]) {
            monthlyData[monthKey] = { count: 0, revenue: 0 };
          }
          
          monthlyData[monthKey].count++;
          monthlyData[monthKey].revenue += amount;
        }
      });

      // Convert monthly data to array
      stats.monthlyStats = Object.entries(monthlyData)
        .map(([month, data]) => ({
          month,
          count: data.count,
          revenue: data.revenue
        }))
        .sort((a, b) => a.month.localeCompare(b.month));

      return stats;
    } catch (error) {
      console.error('Error fetching invoice statistics:', error);
      return {
        totalInvoices: 0,
        totalRevenue: 0,
        pendingAmount: 0,
        paidAmount: 0,
        overdueAmount: 0,
        cancelledAmount: 0,
        monthlyStats: []
      };
    }
  }

  /**
   * Update invoice payment status using Amplify GraphQL
   */
  async updatePaymentStatus(
    invoiceId: string,
    paymentStatus: 'paid' | 'pending' | 'overdue' | 'cancelled',
    paymentData?: {
      paymentMethod: string;
      transactionId: string;
      paymentDate: string;
    }
  ): Promise<{ success: boolean; error?: string }> {
    try {
      const updateInput: any = {
        id: invoiceId,
        paymentStatus: paymentStatus.toUpperCase()
      };

      if (paymentData) {
        updateInput.paymentMethod = paymentData.paymentMethod;
        updateInput.transactionId = paymentData.transactionId;
        updateInput.paymentDate = paymentData.paymentDate;
      }

      const result = await client.graphql({
        query: updateInvoice,
        variables: { input: updateInput }
      });

      if (result.data?.updateInvoice) {
        console.log('Payment status updated successfully for invoice:', invoiceId);
        return { success: true };
      } else {
        return { success: false, error: 'Failed to update payment status' };
      }
    } catch (error) {
      console.error('Error updating payment status:', error);
      return { success: false, error: error.message || 'Network error occurred' };
    }
  }

  /**
   * Delete invoice using Amplify GraphQL
   */
  async deleteInvoice(invoiceId: string): Promise<{ success: boolean; error?: string }> {
    try {
      const result = await client.graphql({
        query: deleteInvoice,
        variables: { id: invoiceId }
      });

      if (result.data?.deleteInvoice) {
        console.log('Invoice deleted successfully:', invoiceId);
        return { success: true };
      } else {
        return { success: false, error: 'Failed to delete invoice' };
      }
    } catch (error) {
      console.error('Error deleting invoice:', error);
      return { success: false, error: error.message || 'Network error occurred' };
    }
  }

  /**
   * Get mock invoices for testing
   */
  private getMockInvoices(userType: 'customer' | 'vendor' | 'admin'): InvoiceData[] {
    const baseInvoices = [
      {
        invoiceNumber: 'ORD250815237',
        invoiceDate: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
        dueDate: new Date(Date.now() + 29 * 24 * 60 * 60 * 1000).toISOString(),
        type: 'product_order' as const,
        customer: {
          id: 'customer_test_001',
          name: 'Test Customer',
          email: '<EMAIL>',
          phone: '+91 8148376909',
          address: {
            street: '123 Test Street',
            city: 'Test City',
            state: 'Test State',
            pincode: '600001',
            country: 'India'
          }
        },
        vendor: {
          id: 'vendor_test_001',
          name: 'Test Vendor',
          businessName: 'Test Business',
          email: '<EMAIL>',
          address: {
            street: '456 Vendor Street',
            city: 'Vendor City',
            state: 'Vendor State',
            pincode: '600002',
            country: 'India'
          },
          gstNumber: 'GST123456789'
        },
        items: [
          {
            id: 'item_test_001',
            name: 'Test Product 1',
            description: 'Test product description for testing PDF generation',
            quantity: 2,
            unitPrice: 1000,
            totalPrice: 2000
          },
          {
            id: 'item_test_002',
            name: 'Test Product 2',
            description: 'Another test product for comprehensive testing',
            quantity: 1,
            unitPrice: 1500,
            totalPrice: 1500
          }
        ],
        subtotal: 3500,
        taxAmount: 630,
        discountAmount: 100,
        totalAmount: 4030,
        paymentStatus: 'pending' as const,
        paymentMethod: 'UPI',
        transactionId: 'TXN_TEST_001',
        notes: 'Test invoice for PDF generation testing',
        terms: 'Payment due within 30 days. This is a test invoice.',
        eventDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
        eventLocation: 'Test Venue, Test City'
      },
      {
        invoiceNumber: 'INV-2024-001',
        invoiceDate: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
        dueDate: new Date(Date.now() + 23 * 24 * 60 * 60 * 1000).toISOString(),
        type: 'product_order' as const,
        customer: {
          id: 'customer_123',
          name: 'Priya & Arjun',
          email: '<EMAIL>',
          phone: '+91 8148376909',
          address: {
            street: 'Chennai, Tamil Nadu',
            city: 'Chennai',
            state: 'Tamil Nadu',
            pincode: '600001',
            country: 'India'
          }
        },
        vendor: {
          id: 'vendor_456',
          name: 'Dream Photographers',
          businessName: 'Dream Photography Studio',
          email: '<EMAIL>',
          address: {
            street: 'T. Nagar, Chennai',
            city: 'Chennai',
            state: 'Tamil Nadu',
            pincode: '600017',
            country: 'India'
          },
          gstNumber: 'GST123456789'
        },
        items: [
          {
            id: 'item_1',
            name: 'Wedding Photography',
            description: 'Full day wedding photography with 500+ edited photos',
            quantity: 1,
            unitPrice: 25000,
            totalPrice: 25000
          }
        ],
        subtotal: 25000,
        taxAmount: 4500,
        discountAmount: 0,
        totalAmount: 29500,
        paymentStatus: 'paid' as const,
        paymentMethod: 'UPI',
        paymentDate: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
        transactionId: 'TXN123456789',
        notes: 'Wedding photography package',
        terms: 'Payment due within 30 days',
        eventDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
        eventLocation: 'Grand Palace, Chennai'
      },
      {
        invoiceNumber: 'INV-2024-002',
        invoiceDate: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
        dueDate: new Date(Date.now() + 27 * 24 * 60 * 60 * 1000).toISOString(),
        type: 'venue_booking' as const,
        customer: {
          id: 'customer_124',
          name: 'Kavya & Ravi',
          email: '<EMAIL>',
          phone: '+91 9876543212',
          address: {
            street: 'Bangalore, Karnataka',
            city: 'Bangalore',
            state: 'Karnataka',
            pincode: '560001',
            country: 'India'
          }
        },
        vendor: {
          id: 'vendor_457',
          name: 'Floral Decorations',
          businessName: 'Floral Art Studio',
          email: '<EMAIL>',
          address: {
            street: 'Koramangala, Bangalore',
            city: 'Bangalore',
            state: 'Karnataka',
            pincode: '560034',
            country: 'India'
          },
          gstNumber: 'GST987654321'
        },
        items: [
          {
            id: 'item_2',
            name: 'Mehendi Decoration',
            description: 'Traditional mehendi ceremony decoration with flowers',
            quantity: 1,
            unitPrice: 15000,
            totalPrice: 15000
          }
        ],
        subtotal: 15000,
        taxAmount: 2700,
        discountAmount: 0,
        totalAmount: 17700,
        paymentStatus: 'pending' as const,
        paymentMethod: 'Bank Transfer',
        notes: 'Mehendi decoration services',
        terms: 'Payment due within 30 days',
        eventDate: new Date(Date.now() + 45 * 24 * 60 * 60 * 1000).toISOString(),
        eventLocation: 'Home Function, Bangalore'
      }
    ];

    return baseInvoices;
  }

  /**
   * Transform GraphQL invoice response to InvoiceData format
   */
  private transformGraphQLInvoice(invoice: any): InvoiceData {
    return {
      id: invoice.id,
      invoiceNumber: invoice.invoiceNumber,
      invoiceDate: invoice.invoiceDate,
      dueDate: invoice.dueDate,
      type: invoice.type?.toLowerCase() as any,
      customer: {
        id: invoice.customerId,
        name: invoice.customerName,
        email: invoice.customerEmail,
        phone: invoice.customerPhone,
        address: invoice.customerAddress
      },
      vendor: invoice.vendorId ? {
        id: invoice.vendorId,
        name: invoice.vendorName,
        businessName: invoice.vendorBusinessName,
        email: invoice.vendorEmail,
        address: invoice.vendorAddress,
        gstNumber: invoice.vendorGstNumber
      } : undefined,
      items: invoice.items || [],
      subtotal: invoice.subtotal,
      taxAmount: invoice.taxAmount,
      discountAmount: invoice.discountAmount,
      totalAmount: invoice.totalAmount,
      paymentStatus: invoice.paymentStatus?.toLowerCase() as any,
      paymentMethod: invoice.paymentMethod,
      paymentDate: invoice.paymentDate,
      transactionId: invoice.transactionId,
      notes: invoice.notes,
      terms: invoice.terms,
      eventDate: invoice.eventDate,
      eventTime: invoice.eventTime,
      eventLocation: invoice.eventLocation
    };
  }

  /**
   * Generate unique invoice number
   */
  private generateInvoiceNumber(prefix: string): string {
    const date = new Date();
    const year = date.getFullYear().toString().slice(-2);
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
    
    return `${prefix}${year}${month}${day}${random}`;
  }

  /**
   * Download invoice PDF
   */
  async downloadInvoicePDF(invoiceNumber: string): Promise<{ success: boolean; blob?: Blob; error?: string }> {
    try {
      console.log('Download requested for invoice:', invoiceNumber);

      // First, try to get the invoice data from database by invoice number
      console.log('🔍 Searching for invoice in database...');
      let invoice = await this.getInvoiceByNumber(invoiceNumber);
      
      if (invoice) {
        console.log('✅ Invoice found in database:', invoice.invoiceNumber);
      } else {
        console.log('❌ Invoice not found in database, trying mock data...');
        const mockInvoices = this.getMockInvoices('customer');
        console.log('📋 Mock invoices available:', mockInvoices.map(inv => inv.invoiceNumber));
        invoice = mockInvoices.find(inv => inv.invoiceNumber === invoiceNumber) || null;
        
        if (invoice) {
          console.log('✅ Invoice found in mock data:', invoice.invoiceNumber);
        } else {
          console.log('❌ Invoice not found in mock data either');
        }
      }
      
      if (!invoice) {
        return { success: false, error: `Invoice ${invoiceNumber} not found in database or mock data` };
      }

      console.log('📄 Generating PDF for invoice:', invoice.invoiceNumber);
      // Generate actual PDF content using jsPDF
      const pdfContent = await this.generateInvoicePDFContent(invoice);
      
      // Create a blob with the actual PDF content
      const blob = new Blob([pdfContent], { type: 'application/pdf' });
      console.log('✅ PDF generated successfully, blob size:', blob.size);

      return { success: true, blob };
    } catch (error) {
      console.error('Error downloading invoice PDF:', error);
      return { success: false, error: 'Failed to generate PDF' };
    }
  }

  /**
   * Generate PDF content for an invoice using jsPDF
   */
  private async generateInvoicePDFContent(invoice: InvoiceData): Promise<Uint8Array> {
    // Dynamic import of jsPDF to avoid SSR issues
    const { jsPDF } = await import('jspdf');
    
    const doc = new jsPDF();
    
    // Set document properties
    doc.setProperties({
      title: `Invoice ${invoice.invoiceNumber}`,
      subject: 'Invoice from BookmyFestive',
      author: 'BookmyFestive',
      creator: 'BookmyFestive Invoice System'
    });

    // Add company header
    doc.setFontSize(20);
    doc.setTextColor(163, 21, 21); // Brand color
    doc.text('BookmyFestive', 105, 20, { align: 'center' });
    
    doc.setFontSize(12);
    doc.setTextColor(100, 100, 100);
    doc.text('Wedding Planning Hub', 105, 30, { align: 'center' });
    doc.text('Chennai, Tamil Nadu - 600001', 105, 37, { align: 'center' });
    doc.text('Phone: +91 8148376909 | Email: <EMAIL>', 105, 44, { align: 'center' });

    // Add invoice title
    doc.setFontSize(18);
    doc.setTextColor(0, 0, 0);
    doc.text('INVOICE', 105, 60, { align: 'center' });

    // Invoice details section
    doc.setFontSize(10);
    doc.setTextColor(0, 0, 0);
    
    let yPos = 80;
    
    // Left column - Invoice details
    doc.setFont('helvetica', 'bold');
    doc.text('Invoice Number:', 20, yPos);
    doc.setFont('helvetica', 'normal');
    doc.text(invoice.invoiceNumber, 60, yPos);
    
    doc.setFont('helvetica', 'bold');
    doc.text('Invoice Date:', 20, yPos + 8);
    doc.setFont('helvetica', 'normal');
    doc.text(invoice.invoiceDate, 60, yPos + 8);
    
    if (invoice.dueDate) {
      doc.setFont('helvetica', 'bold');
      doc.text('Due Date:', 20, yPos + 16);
      doc.setFont('helvetica', 'normal');
      doc.text(invoice.dueDate, 60, yPos + 16);
    }

    // Right column - Payment status
    doc.setFont('helvetica', 'bold');
    doc.text('Payment Status:', 120, yPos);
    doc.setFont('helvetica', 'normal');
    doc.text(invoice.paymentStatus.toUpperCase(), 160, yPos);
    
    if (invoice.paymentMethod) {
      doc.setFont('helvetica', 'bold');
      doc.text('Payment Method:', 120, yPos + 8);
      doc.setFont('helvetica', 'normal');
      doc.text(invoice.paymentMethod, 160, yPos + 8);
    }

    yPos += 30;

    // Customer Information
    doc.setFont('helvetica', 'bold');
    doc.setTextColor(163, 21, 21);
    doc.text('CUSTOMER INFORMATION', 20, yPos);
    doc.setTextColor(0, 0, 0);
    
    yPos += 8;
    doc.setFont('helvetica', 'normal');
    doc.text(`Name: ${invoice.customer.name}`, 20, yPos);
    yPos += 6;
    doc.text(`Email: ${invoice.customer.email}`, 20, yPos);
    yPos += 6;
    
    if (invoice.customer.phone) {
      doc.text(`Phone: ${invoice.customer.phone}`, 20, yPos);
      yPos += 6;
    }
    
    if (invoice.customer.address) {
      const addr = invoice.customer.address;
      doc.text(`Address: ${addr.street}, ${addr.city}, ${addr.state} ${addr.pincode}, ${addr.country}`, 20, yPos);
      yPos += 6;
    }

    yPos += 5;

    // Vendor Information (if exists)
    if (invoice.vendor) {
      doc.setFont('helvetica', 'bold');
      doc.setTextColor(163, 21, 21);
      doc.text('VENDOR INFORMATION', 20, yPos);
      doc.setTextColor(0, 0, 0);
      
      yPos += 8;
      doc.setFont('helvetica', 'normal');
      doc.text(`Name: ${invoice.vendor.name}`, 20, yPos);
      yPos += 6;
      doc.text(`Email: ${invoice.vendor.email}`, 20, yPos);
      yPos += 6;
      
      if (invoice.vendor.businessName) {
        doc.text(`Business: ${invoice.vendor.businessName}`, 20, yPos);
        yPos += 6;
      }
      
      if (invoice.vendor.gstNumber) {
        doc.text(`GST: ${invoice.vendor.gstNumber}`, 20, yPos);
        yPos += 6;
      }
      
      yPos += 5;
    }

    // Items table
    doc.setFont('helvetica', 'bold');
    doc.setTextColor(163, 21, 21);
    doc.text('ITEMS', 20, yPos);
    doc.setTextColor(0, 0, 0);
    
    yPos += 8;
    
    // Table headers
    doc.setFont('helvetica', 'bold');
    doc.text('Item', 20, yPos);
    doc.text('Qty', 100, yPos);
    doc.text('Unit Price', 130, yPos);
    doc.text('Total', 170, yPos);
    
    yPos += 6;
    doc.line(20, yPos, 190, yPos); // Horizontal line
    
    yPos += 8;
    doc.setFont('helvetica', 'normal');
    
    // Table rows
    invoice.items.forEach(item => {
      // Check if we need a new page
      if (yPos > 250) {
        doc.addPage();
        yPos = 20;
      }
      
      doc.text(item.name, 20, yPos);
      doc.text(item.quantity.toString(), 100, yPos);
      doc.text(`₹${item.unitPrice.toLocaleString()}`, 130, yPos);
      doc.text(`₹${item.totalPrice.toLocaleString()}`, 170, yPos);
      yPos += 6;
      
      if (item.description) {
        doc.setFontSize(8);
        doc.setTextColor(100, 100, 100);
        doc.text(item.description, 25, yPos);
        doc.setFontSize(10);
        doc.setTextColor(0, 0, 0);
        yPos += 5;
      }
    });

    yPos += 10;
    doc.line(20, yPos, 190, yPos); // Horizontal line
    
    yPos += 8;

    // Pricing summary
    doc.setFont('helvetica', 'bold');
    doc.text('PRICING SUMMARY', 20, yPos);
    doc.setFont('helvetica', 'normal');
    
    yPos += 8;
    doc.text(`Subtotal: ₹${invoice.subtotal.toLocaleString()}`, 120, yPos);
    yPos += 6;
    
    if (invoice.taxAmount > 0) {
      doc.text(`Tax: ₹${invoice.taxAmount.toLocaleString()}`, 120, yPos);
      yPos += 6;
    }
    
    if (invoice.discountAmount && invoice.discountAmount > 0) {
      doc.text(`Discount: ₹${invoice.discountAmount.toLocaleString()}`, 120, yPos);
      yPos += 6;
    }
    
    doc.setFont('helvetica', 'bold');
    doc.setTextColor(163, 21, 21);
    doc.text(`Total Amount: ₹${invoice.totalAmount.toLocaleString()}`, 120, yPos);
    doc.setTextColor(0, 0, 0);
    
    yPos += 15;

    // Additional information
    if (invoice.eventDate || invoice.eventLocation) {
      doc.setFont('helvetica', 'bold');
      doc.text('EVENT DETAILS', 20, yPos);
      doc.setFont('helvetica', 'normal');
      
      yPos += 8;
      if (invoice.eventDate) {
        doc.text(`Event Date: ${invoice.eventDate}`, 20, yPos);
        yPos += 6;
      }
      if (invoice.eventLocation) {
        doc.text(`Event Location: ${invoice.eventLocation}`, 20, yPos);
        yPos += 6;
      }
      yPos += 5;
    }

    // Notes and terms
    if (invoice.notes || invoice.terms) {
      if (invoice.notes) {
        doc.setFont('helvetica', 'bold');
        doc.text('Notes:', 20, yPos);
        doc.setFont('helvetica', 'normal');
        doc.text(invoice.notes, 20, yPos + 6);
        yPos += 12;
      }
      
      if (invoice.terms) {
        doc.setFont('helvetica', 'bold');
        doc.text('Terms:', 20, yPos);
        doc.setFont('helvetica', 'normal');
        doc.text(invoice.terms, 20, yPos + 6);
        yPos += 12;
      }
    }

    // Footer
    doc.setFontSize(8);
    doc.setTextColor(100, 100, 100);
    doc.text('Thank you for choosing BookmyFestive!', 105, yPos + 10, { align: 'center' });
    doc.text('Making your Celebration dreams come true! 💕', 105, yPos + 16, { align: 'center' });
    doc.text('© 2025 BookmyFestive. All rights reserved.', 105, yPos + 22, { align: 'center' });

    // Return the PDF as Uint8Array
    const arrayBuffer = doc.output('arraybuffer');
    return new Uint8Array(arrayBuffer);
  }
}

export const invoiceService = new InvoiceService();
export default invoiceService;
