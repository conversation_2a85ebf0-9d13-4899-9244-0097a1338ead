/**
 * Invoice Service for generating invoices for products, bookings, and subscriptions using AWS Amplify
 * Handles PDF generation, invoice storage, and invoice management
 */

import { generateClient } from '@aws-amplify/api';
import { generateInvoice, downloadInvoice } from '@/src/graphql/mutations';
import { getInvoicesByCustomer, getInvoicesByVendor } from '@/src/graphql/queries';

const client = generateClient();

export interface InvoiceItem {
  id: string;
  name: string;
  description?: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
  taxRate?: number;
  taxAmount?: number;
}

export interface InvoiceData {
  invoiceNumber: string;
  invoiceDate: string;
  dueDate?: string;
  type: 'product_order' | 'venue_booking' | 'vendor_booking' | 'subscription';
  
  // Customer/Client Information
  customer: {
    id: string;
    name: string;
    email: string;
    phone?: string;
    address?: {
      street: string;
      city: string;
      state: string;
      pincode: string;
      country: string;
    };
  };
  
  // Vendor/Business Information (for bookings)
  vendor?: {
    id: string;
    name: string;
    businessName?: string;
    email: string;
    phone?: string;
    address?: {
      street: string;
      city: string;
      state: string;
      pincode: string;
      country: string;
    };
    gstNumber?: string;
  };
  
  // Invoice Items
  items: InvoiceItem[];
  
  // Pricing Details
  subtotal: number;
  taxAmount: number;
  discountAmount?: number;
  totalAmount: number;
  
  // Payment Information
  paymentStatus: 'paid' | 'pending' | 'overdue' | 'cancelled';
  paymentMethod?: string;
  paymentDate?: string;
  transactionId?: string;
  
  // Additional Information
  notes?: string;
  terms?: string;
  
  // Booking specific (for venue/vendor bookings)
  eventDate?: string;
  eventTime?: string;
  eventLocation?: string;
}

export interface InvoiceTemplate {
  companyName: string;
  companyLogo?: string;
  companyAddress: {
    street: string;
    city: string;
    state: string;
    pincode: string;
    country: string;
  };
  companyContact: {
    email: string;
    phone: string;
    website?: string;
  };
  gstNumber?: string;
  panNumber?: string;
}

class InvoiceService {
  private defaultTemplate: InvoiceTemplate = {
    companyName: 'BookmyFestive',
    companyLogo: '/logo.png',
    companyAddress: {
      street: 'Wedding Planning Hub',
      city: 'Chennai',
      state: 'Tamil Nadu',
      pincode: '600001',
      country: 'India'
    },
    companyContact: {
      email: '<EMAIL>',
      phone: '+91 9876543210',
      website: 'https://BookmyFestive.com'
    },
    gstNumber: 'GST123456789',
    panNumber: 'PAN123456789'
  };

  /**
   * Generate invoice for product order
   */
  async generateProductOrderInvoice(orderData: {
    orderId: string;
    customerId: string;
    customerName: string;
    customerEmail: string;
    items: Array<{
      productId: string;
      productName: string;
      quantity: number;
      price: number;
    }>;
    totalAmount: number;
    paymentStatus: string;
    paymentMethod?: string;
    transactionId?: string;
  }): Promise<{ success: boolean; invoiceUrl?: string; error?: string }> {
    try {
      const invoiceInput = {
        type: 'PRODUCT_ORDER',
        customerId: orderData.customerId,
        customerName: orderData.customerName,
        customerEmail: orderData.customerEmail,
        items: orderData.items.map(item => ({
          id: item.productId,
          name: item.productName,
          quantity: item.quantity,
          unitPrice: item.price,
          totalPrice: item.quantity * item.price
        })),
        subtotal: orderData.totalAmount,
        taxAmount: 0, // Calculate based on items
        totalAmount: orderData.totalAmount,
        paymentStatus: orderData.paymentStatus.toUpperCase(),
        paymentMethod: orderData.paymentMethod,
        paymentDate: orderData.paymentStatus === 'paid' ? new Date().toISOString() : undefined,
        transactionId: orderData.transactionId,
        notes: `Order ID: ${orderData.orderId}`
      };

      const result = await client.graphql({
        query: generateInvoice,
        variables: { input: invoiceInput }
      });

      const response = result.data.generateInvoice;

      if (response.success) {
        return {
          success: true,
          invoiceUrl: response.pdfUrl
        };
      } else {
        return {
          success: false,
          error: response.error || 'Failed to generate invoice'
        };
      }
    } catch (error) {
      console.error('Error generating product order invoice:', error);
      return { success: false, error: 'Failed to generate invoice' };
    }
  }

  /**
   * Generate invoice for venue/vendor booking
   */
  async generateBookingInvoice(bookingData: {
    bookingId: string;
    bookingType: 'venue' | 'vendor';
    customerId: string;
    customerName: string;
    customerEmail: string;
    vendorId: string;
    vendorName: string;
    vendorEmail: string;
    serviceName: string;
    eventDate: string;
    eventTime?: string;
    amount: number;
    paymentStatus: string;
    paymentMethod?: string;
    transactionId?: string;
  }): Promise<{ success: boolean; invoiceUrl?: string; error?: string }> {
    try {
      const invoiceData: InvoiceData = {
        invoiceNumber: this.generateInvoiceNumber(bookingData.bookingType === 'venue' ? 'VEN' : 'VND'),
        invoiceDate: new Date().toISOString().split('T')[0],
        type: bookingData.bookingType === 'venue' ? 'venue_booking' : 'vendor_booking',
        customer: {
          id: bookingData.customerId,
          name: bookingData.customerName,
          email: bookingData.customerEmail
        },
        vendor: {
          id: bookingData.vendorId,
          name: bookingData.vendorName,
          email: bookingData.vendorEmail
        },
        items: [{
          id: bookingData.bookingId,
          name: bookingData.serviceName,
          description: `${bookingData.bookingType === 'venue' ? 'Venue' : 'Vendor'} booking for ${bookingData.eventDate}`,
          quantity: 1,
          unitPrice: bookingData.amount,
          totalPrice: bookingData.amount
        }],
        subtotal: bookingData.amount,
        taxAmount: 0,
        totalAmount: bookingData.amount,
        paymentStatus: bookingData.paymentStatus as any,
        paymentMethod: bookingData.paymentMethod,
        transactionId: bookingData.transactionId,
        eventDate: bookingData.eventDate,
        eventTime: bookingData.eventTime,
        notes: `Booking ID: ${bookingData.bookingId}`
      };

      return await this.createInvoice(invoiceData);
    } catch (error) {
      console.error('Error generating booking invoice:', error);
      return { success: false, error: 'Failed to generate invoice' };
    }
  }

  /**
   * Generate invoice for vendor subscription
   */
  async generateSubscriptionInvoice(subscriptionData: {
    subscriptionId: string;
    vendorId: string;
    vendorName: string;
    vendorEmail: string;
    planName: string;
    planPrice: number;
    billingPeriod: string;
    paymentStatus: string;
    paymentMethod?: string;
    transactionId?: string;
  }): Promise<{ success: boolean; invoiceUrl?: string; error?: string }> {
    try {
      const invoiceData: InvoiceData = {
        invoiceNumber: this.generateInvoiceNumber('SUB'),
        invoiceDate: new Date().toISOString().split('T')[0],
        type: 'subscription',
        customer: {
          id: subscriptionData.vendorId,
          name: subscriptionData.vendorName,
          email: subscriptionData.vendorEmail
        },
        items: [{
          id: subscriptionData.subscriptionId,
          name: `${subscriptionData.planName} - ${subscriptionData.billingPeriod}`,
          description: `Vendor subscription plan`,
          quantity: 1,
          unitPrice: subscriptionData.planPrice,
          totalPrice: subscriptionData.planPrice
        }],
        subtotal: subscriptionData.planPrice,
        taxAmount: 0,
        totalAmount: subscriptionData.planPrice,
        paymentStatus: subscriptionData.paymentStatus as any,
        paymentMethod: subscriptionData.paymentMethod,
        transactionId: subscriptionData.transactionId,
        notes: `Subscription ID: ${subscriptionData.subscriptionId}`
      };

      return await this.createInvoice(invoiceData);
    } catch (error) {
      console.error('Error generating subscription invoice:', error);
      return { success: false, error: 'Failed to generate invoice' };
    }
  }

  /**
   * Create and store invoice using Amplify GraphQL
   */
  private async createInvoice(invoiceData: InvoiceData): Promise<{ success: boolean; invoiceUrl?: string; error?: string }> {
    try {
      const invoiceInput = {
        invoiceNumber: invoiceData.invoiceNumber,
        invoiceDate: invoiceData.invoiceDate,
        dueDate: invoiceData.dueDate,
        type: invoiceData.type.toUpperCase(),
        customerId: invoiceData.customer.id,
        customerName: invoiceData.customer.name,
        customerEmail: invoiceData.customer.email,
        customerPhone: invoiceData.customer.phone,
        customerAddress: invoiceData.customer.address,
        vendorId: invoiceData.vendor?.id,
        vendorName: invoiceData.vendor?.name,
        vendorEmail: invoiceData.vendor?.email,
        vendorBusinessName: invoiceData.vendor?.businessName,
        vendorAddress: invoiceData.vendor?.address,
        vendorGstNumber: invoiceData.vendor?.gstNumber,
        items: invoiceData.items.map(item => ({
          id: item.id,
          name: item.name,
          description: item.description,
          quantity: item.quantity,
          unitPrice: item.unitPrice,
          totalPrice: item.totalPrice,
          taxRate: item.taxRate || 0,
          taxAmount: item.taxAmount || 0
        })),
        subtotal: invoiceData.subtotal,
        taxAmount: invoiceData.taxAmount || 0,
        discountAmount: invoiceData.discountAmount || 0,
        totalAmount: invoiceData.totalAmount,
        paymentStatus: invoiceData.paymentStatus.toUpperCase(),
        paymentMethod: invoiceData.paymentMethod,
        paymentDate: invoiceData.paymentDate,
        transactionId: invoiceData.transactionId,
        notes: invoiceData.notes,
        terms: invoiceData.terms,
        eventDate: invoiceData.eventDate,
        eventTime: invoiceData.eventTime,
        eventLocation: invoiceData.eventLocation
      };

      const result = await client.graphql({
        query: `
          mutation CreateInvoice($input: CreateInvoiceInput!) {
            createInvoice(input: $input) {
              id
              invoiceNumber
              invoiceDate
              totalAmount
              paymentStatus
              pdfUrl
              createdAt
            }
          }
        `,
        variables: { input: invoiceInput }
      });

      const createdInvoice = result.data?.createInvoice;

      if (createdInvoice) {
        console.log('Invoice created successfully:', createdInvoice.id);
        return {
          success: true,
          invoiceUrl: createdInvoice.pdfUrl
        };
      } else {
        return { success: false, error: 'Invoice creation failed' };
      }
    } catch (error) {
      console.error('Error creating invoice:', error);
      return { success: false, error: error.message || 'Network error occurred' };
    }
  }

  /**
   * Get invoice by ID using Amplify GraphQL
   */
  async getInvoice(invoiceId: string): Promise<InvoiceData | null> {
    try {
      const result = await client.graphql({
        query: `
          query GetInvoice($id: ID!) {
            getInvoice(id: $id) {
              id
              invoiceNumber
              invoiceDate
              dueDate
              type
              customerId
              customerName
              customerEmail
              customerPhone
              customerAddress {
                street
                city
                state
                pincode
                country
              }
              vendorId
              vendorName
              vendorEmail
              vendorBusinessName
              vendorAddress {
                street
                city
                state
                pincode
                country
              }
              vendorGstNumber
              items {
                id
                name
                description
                quantity
                unitPrice
                totalPrice
                taxRate
                taxAmount
              }
              subtotal
              taxAmount
              discountAmount
              totalAmount
              paymentStatus
              paymentMethod
              paymentDate
              transactionId
              notes
              terms
              eventDate
              eventTime
              eventLocation
              pdfUrl
              createdAt
              updatedAt
            }
          }
        `,
        variables: { id: invoiceId }
      });

      const invoice = result.data?.getInvoice;
      if (!invoice) {
        return null;
      }

      // Transform GraphQL response to InvoiceData format
      return {
        id: invoice.id,
        invoiceNumber: invoice.invoiceNumber,
        invoiceDate: invoice.invoiceDate,
        dueDate: invoice.dueDate,
        type: invoice.type.toLowerCase() as any,
        customer: {
          id: invoice.customerId,
          name: invoice.customerName,
          email: invoice.customerEmail,
          phone: invoice.customerPhone,
          address: invoice.customerAddress
        },
        vendor: invoice.vendorId ? {
          id: invoice.vendorId,
          name: invoice.vendorName,
          businessName: invoice.vendorBusinessName,
          email: invoice.vendorEmail,
          address: invoice.vendorAddress,
          gstNumber: invoice.vendorGstNumber
        } : undefined,
        items: invoice.items || [],
        subtotal: invoice.subtotal,
        taxAmount: invoice.taxAmount,
        discountAmount: invoice.discountAmount,
        totalAmount: invoice.totalAmount,
        paymentStatus: invoice.paymentStatus.toLowerCase() as any,
        paymentMethod: invoice.paymentMethod,
        paymentDate: invoice.paymentDate,
        transactionId: invoice.transactionId,
        notes: invoice.notes,
        terms: invoice.terms,
        eventDate: invoice.eventDate,
        eventTime: invoice.eventTime,
        eventLocation: invoice.eventLocation
      };
    } catch (error) {
      console.error('Error fetching invoice:', error);
      return null;
    }
  }

  /**
   * Get invoices for a customer/vendor
   */
  async getInvoicesForUser(userId: string, userType: 'customer' | 'vendor' | 'admin'): Promise<InvoiceData[]> {
    try {
      console.log('Fetching invoices for user:', userId, 'type:', userType);

      let result;

      if (userType === 'admin') {
        // Admin can see all invoices
        result = await client.graphql({
          query: `
            query ListInvoices($limit: Int) {
              listInvoices(limit: $limit) {
                items {
                  id
                  invoiceNumber
                  invoiceDate
                  dueDate
                  type
                  customerId
                  customerName
                  customerEmail
                  vendorId
                  vendorName
                  vendorBusinessName
                  subtotal
                  taxAmount
                  totalAmount
                  paymentStatus
                  paymentMethod
                  paymentDate
                  transactionId
                  eventDate
                  eventLocation
                  createdAt
                  updatedAt
                }
              }
            }
          `,
          variables: { limit: 100 }
        });
      } else if (userType === 'vendor') {
        // Vendor sees invoices where they are the vendor
        result = await client.graphql({
          query: `
            query GetInvoicesByVendor($vendorId: ID!, $limit: Int) {
              getInvoicesByVendor(vendorId: $vendorId, limit: $limit) {
                items {
                  id
                  invoiceNumber
                  invoiceDate
                  dueDate
                  type
                  customerId
                  customerName
                  customerEmail
                  vendorId
                  vendorName
                  vendorBusinessName
                  subtotal
                  taxAmount
                  totalAmount
                  paymentStatus
                  paymentMethod
                  paymentDate
                  transactionId
                  eventDate
                  eventLocation
                  createdAt
                  updatedAt
                }
              }
            }
          `,
          variables: { vendorId: userId, limit: 50 }
        });
      } else {
        // Customer sees invoices where they are the customer
        result = await client.graphql({
          query: `
            query GetInvoicesByCustomer($customerId: ID!, $limit: Int) {
              getInvoicesByCustomer(customerId: $customerId, limit: $limit) {
                items {
                  id
                  invoiceNumber
                  invoiceDate
                  dueDate
                  type
                  customerId
                  customerName
                  customerEmail
                  vendorId
                  vendorName
                  vendorBusinessName
                  subtotal
                  taxAmount
                  totalAmount
                  paymentStatus
                  paymentMethod
                  paymentDate
                  transactionId
                  eventDate
                  eventLocation
                  createdAt
                  updatedAt
                }
              }
            }
          `,
          variables: { customerId: userId, limit: 50 }
        });
      }

      const invoices = result.data?.listInvoices?.items ||
                      result.data?.getInvoicesByVendor?.items ||
                      result.data?.getInvoicesByCustomer?.items ||
                      [];

      // Transform GraphQL response to InvoiceData format
      return invoices.map((invoice: any) => ({
        id: invoice.id,
        invoiceNumber: invoice.invoiceNumber,
        invoiceDate: invoice.invoiceDate,
        dueDate: invoice.dueDate,
        type: invoice.type?.toLowerCase() as any,
        customer: {
          id: invoice.customerId,
          name: invoice.customerName,
          email: invoice.customerEmail
        },
        vendor: invoice.vendorId ? {
          id: invoice.vendorId,
          name: invoice.vendorName,
          businessName: invoice.vendorBusinessName,
          email: invoice.vendorEmail || ''
        } : undefined,
        items: [], // Items would need to be fetched separately if needed
        subtotal: invoice.subtotal,
        taxAmount: invoice.taxAmount,
        discountAmount: invoice.discountAmount || 0,
        totalAmount: invoice.totalAmount,
        paymentStatus: invoice.paymentStatus?.toLowerCase() as any,
        paymentMethod: invoice.paymentMethod,
        paymentDate: invoice.paymentDate,
        transactionId: invoice.transactionId,
        eventDate: invoice.eventDate,
        eventLocation: invoice.eventLocation,
        notes: invoice.notes,
        terms: invoice.terms
      }));
    } catch (error) {
      console.error('Error fetching user invoices:', error);
      // Fallback to mock data if GraphQL fails
      console.log('Falling back to mock data due to error');
      return this.getMockInvoices(userType);
    }
  }

  /**
   * Update invoice payment status using Amplify GraphQL
   */
  async updatePaymentStatus(
    invoiceId: string,
    paymentStatus: 'paid' | 'pending' | 'overdue' | 'cancelled',
    paymentData?: {
      paymentMethod: string;
      transactionId: string;
      paymentDate: string;
    }
  ): Promise<{ success: boolean; error?: string }> {
    try {
      const updateInput: any = {
        id: invoiceId,
        paymentStatus: paymentStatus.toUpperCase()
      };

      if (paymentData) {
        updateInput.paymentMethod = paymentData.paymentMethod;
        updateInput.transactionId = paymentData.transactionId;
        updateInput.paymentDate = paymentData.paymentDate;
      }

      const result = await client.graphql({
        query: `
          mutation UpdateInvoice($input: UpdateInvoiceInput!) {
            updateInvoice(input: $input) {
              id
              paymentStatus
              paymentMethod
              paymentDate
              transactionId
              updatedAt
            }
          }
        `,
        variables: { input: updateInput }
      });

      if (result.data?.updateInvoice) {
        console.log('Payment status updated successfully for invoice:', invoiceId);
        return { success: true };
      } else {
        return { success: false, error: 'Failed to update payment status' };
      }
    } catch (error) {
      console.error('Error updating payment status:', error);
      return { success: false, error: error.message || 'Network error occurred' };
    }
  }

  /**
   * Get mock invoices for testing
   */
  private getMockInvoices(userType: 'customer' | 'vendor' | 'admin'): InvoiceData[] {
    const baseInvoices = [
      {
        id: 'inv_001',
        invoiceNumber: 'INV-2024-001',
        customerId: 'customer_123',
        vendorId: 'vendor_456',
        invoiceDate: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
        dueDate: new Date(Date.now() + 23 * 24 * 60 * 60 * 1000).toISOString(),
        subtotal: 25000,
        taxAmount: 4500,
        totalAmount: 29500,
        paymentStatus: 'paid' as const,
        paymentMethod: 'UPI',
        paymentDate: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
        transactionId: 'TXN123456789',
        notes: 'Wedding photography package',
        customer: {
          name: 'Priya & Arjun',
          email: '<EMAIL>',
          phone: '+91 9876543210',
          address: 'Chennai, Tamil Nadu'
        },
        vendor: {
          name: 'Dream Photographers',
          email: '<EMAIL>',
          phone: '+91 9876543211',
          address: 'T. Nagar, Chennai'
        },
        items: [
          {
            id: 'item_1',
            name: 'Wedding Photography',
            description: 'Full day wedding photography with 500+ edited photos',
            quantity: 1,
            unitPrice: 25000,
            totalPrice: 25000
          }
        ],
        eventDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
        eventLocation: 'Grand Palace, Chennai',
        createdAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
        updatedAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
        type: 'product_order'
      },
      {
        id: 'inv_002',
        invoiceNumber: 'INV-2024-002',
        customerId: 'customer_124',
        vendorId: 'vendor_457',
        invoiceDate: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
        dueDate: new Date(Date.now() + 27 * 24 * 60 * 60 * 1000).toISOString(),
        subtotal: 15000,
        taxAmount: 2700,
        totalAmount: 17700,
        paymentStatus: 'pending' as const,
        paymentMethod: 'Bank Transfer',
        notes: 'Mehendi decoration services',
        customer: {
          name: 'Kavya & Ravi',
          email: '<EMAIL>',
          phone: '+91 **********',
          address: 'Bangalore, Karnataka'
        },
        vendor: {
          name: 'Floral Decorations',
          email: '<EMAIL>',
          phone: '+91 **********',
          address: 'Koramangala, Bangalore'
        },
        items: [
          {
            id: 'item_2',
            name: 'Mehendi Decoration',
            description: 'Traditional mehendi ceremony decoration with flowers',
            quantity: 1,
            unitPrice: 15000,
            totalPrice: 15000
          }
        ],
        eventDate: new Date(Date.now() + 45 * 24 * 60 * 60 * 1000).toISOString(),
        eventLocation: 'Home Function, Bangalore',
        createdAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
        updatedAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
        type:'venue_booking'
      }
    ];

    return baseInvoices;
  }

  /**
   * Generate unique invoice number
   */
  private generateInvoiceNumber(prefix: string): string {
    const date = new Date();
    const year = date.getFullYear().toString().slice(-2);
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
    
    return `${prefix}${year}${month}${day}${random}`;
  }

  /**
   * Download invoice PDF
   */
  async downloadInvoicePDF(invoiceId: string): Promise<{ success: boolean; blob?: Blob; error?: string }> {
    try {
      console.log('Download requested for invoice:', invoiceId);

      // For now, simulate PDF download since API endpoint is not set up
      // In a real implementation, this would generate or fetch the actual PDF
      const mockPdfContent = `Invoice ${invoiceId} - PDF content would be here`;
      const blob = new Blob([mockPdfContent], { type: 'application/pdf' });

      return { success: true, blob };
    } catch (error) {
      console.error('Error downloading invoice PDF:', error);
      return { success: false, error: 'Network error occurred' };
    }
  }
}

export const invoiceService = new InvoiceService();
export default invoiceService;
