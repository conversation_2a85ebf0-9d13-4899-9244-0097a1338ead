import React from 'react';
import { View, Text, StyleSheet, Dimensions } from 'react-native';
import { LineChart as RNLine<PERSON><PERSON> } from 'react-native-chart-kit';
import { useTheme } from '../../providers/ThemeProvider';
import type { ChartDataPoint } from '../../services/analyticsService';

interface LineChartProps {
  data: ChartDataPoint[];
  title?: string;
  color?: string;
  height?: number;
  showValues?: boolean;
  formatValue?: (value: number) => string;
}

const { width: screenWidth } = Dimensions.get('window');

const LineChart: React.FC<LineChartProps> = ({
  data,
  title,
  color,
  height = 220,
  showValues = false,
  formatValue,
}) => {
  const { theme } = useTheme();

  if (!data || data.length === 0) {
    return (
      <View style={[styles.container, { backgroundColor: theme.colors.surface }]}>
        {title && (
          <Text style={[styles.title, { color: theme.colors.text }]}>{title}</Text>
        )}
        <View style={styles.noDataContainer}>
          <Text style={[styles.noDataText, { color: theme.colors.textSecondary }]}>
            No data available
          </Text>
        </View>
      </View>
    );
  }

  const chartData = {
    labels: data.map(item => item.label || item.date.split('-')[2]),
    datasets: [
      {
        data: data.map(item => item.value),
        color: (opacity = 1) => color || theme.colors.primary,
        strokeWidth: 3,
      },
    ],
  };

  const chartConfig = {
    backgroundColor: theme.colors.surface,
    backgroundGradientFrom: theme.colors.surface,
    backgroundGradientTo: theme.colors.surface,
    decimalPlaces: 0,
    color: (opacity = 1) => `rgba(0, 0, 0, ${opacity * 0.6})`,
    labelColor: (opacity = 1) => theme.colors.textSecondary,
    style: {
      borderRadius: 16,
    },
    propsForDots: {
      r: '4',
      strokeWidth: '2',
      stroke: color || theme.colors.primary,
      fill: color || theme.colors.primary,
    },
    propsForBackgroundLines: {
      strokeDasharray: '',
      stroke: theme.colors.border,
      strokeWidth: 1,
    },
    propsForLabels: {
      fontSize: 12,
    },
    formatYLabel: formatValue || ((value: string) => {
      const num = parseFloat(value);
      if (num >= 1000000) {
        return `${(num / 1000000).toFixed(1)}M`;
      } else if (num >= 1000) {
        return `${(num / 1000).toFixed(1)}K`;
      }
      return num.toString();
    }) as (yLabel: string) => string,
  };

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.surface }]}>
      {title && (
        <Text style={[styles.title, { color: theme.colors.text }]}>{title}</Text>
      )}
      
      <View style={styles.chartContainer}>
        <RNLineChart
          data={chartData}
          width={screenWidth - 48} // Account for padding
          height={height}
          chartConfig={chartConfig}
          bezier
          style={styles.chart}
          withInnerLines={true}
          withOuterLines={false}
          withVerticalLines={false}
          withHorizontalLines={true}
          withDots={true}
          withShadow={false}
          fromZero={false}
        />
      </View>

      {showValues && (
        <View style={styles.valuesContainer}>
          <View style={styles.valueItem}>
            <Text style={[styles.valueLabel, { color: theme.colors.textSecondary }]}>
              Min
            </Text>
            <Text style={[styles.valueText, { color: theme.colors.text }]}>
              {formatValue ? formatValue(Math.min(...data.map(d => d.value))) : Math.min(...data.map(d => d.value))}
            </Text>
          </View>
          
          <View style={styles.valueItem}>
            <Text style={[styles.valueLabel, { color: theme.colors.textSecondary }]}>
              Max
            </Text>
            <Text style={[styles.valueText, { color: theme.colors.text }]}>
              {formatValue ? formatValue(Math.max(...data.map(d => d.value))) : Math.max(...data.map(d => d.value))}
            </Text>
          </View>
          
          <View style={styles.valueItem}>
            <Text style={[styles.valueLabel, { color: theme.colors.textSecondary }]}>
              Avg
            </Text>
            <Text style={[styles.valueText, { color: theme.colors.text }]}>
              {formatValue ? 
                formatValue(data.reduce((sum, d) => sum + d.value, 0) / data.length) : 
                Math.round(data.reduce((sum, d) => sum + d.value, 0) / data.length)
              }
            </Text>
          </View>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    borderRadius: 12,
    padding: 16,
    marginVertical: 8,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
  },
  chartContainer: {
    alignItems: 'center',
    overflow: 'hidden',
  },
  chart: {
    borderRadius: 12,
  },
  noDataContainer: {
    height: 220,
    justifyContent: 'center',
    alignItems: 'center',
  },
  noDataText: {
    fontSize: 14,
    fontStyle: 'italic',
  },
  valuesContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginTop: 16,
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
  },
  valueItem: {
    alignItems: 'center',
  },
  valueLabel: {
    fontSize: 12,
    marginBottom: 4,
  },
  valueText: {
    fontSize: 14,
    fontWeight: '600',
  },
});

export default LineChart;
