"use client"

import { TopHeader } from "@/components/top-header"
import { Head<PERSON> } from "@/components/header"
import { Footer } from "@/components/footer"
import { SimpleSEO } from "@/components/seo/SimpleSEO"
import { Calendar, Clock, CheckCircle, AlertCircle, Star, ClipboardList, ShoppingBag, Edit3 } from "lucide-react"

export default function WeddingTimelinePage() {
  const timelinePhases = [
    {
      phase: "12+ Months Before",
      color: "bg-purple-100 text-purple-800",
      icon: <Star className="w-7 h-7 text-gray-500" />,
      priority: "High Priority",
      tasks: [
        { task: "Set wedding date and budget", priority: "high", category: "Planning" },
        { task: "Create guest list (rough estimate)", priority: "high", category: "Planning" },
        { task: "Book wedding venue", priority: "high", category: "Venue" },
        { task: "Hire wedding photographer", priority: "high", category: "Photography" },
        { task: "Book caterer or catering venue", priority: "high", category: "Catering" },
        { task: "Start shopping for wedding outfits", priority: "medium", category: "Shopping" },
        { task: "Research and shortlist vendors", priority: "medium", category: "Vendors" }
      ]
    },
    {
      phase: "9-11 Months Before",
      color: "bg-blue-100 text-blue-800",
      icon: <ClipboardList className="w-7 h-7 text-gray-500" />,
      priority: "High Priority",
      tasks: [
        { task: "Finalize guest list", priority: "high", category: "Planning" },
        { task: "Book decorator and florist", priority: "high", category: "Decoration" },
        { task: "Hire music/DJ/band", priority: "high", category: "Entertainment" },
        { task: "Book makeup artist and hairstylist", priority: "high", category: "Beauty" },
        { task: "Order wedding invitations", priority: "medium", category: "Invitations" },
        { task: "Book honeymoon travel", priority: "medium", category: "Travel" },
        { task: "Start Festive Shopping", priority: "medium", category: "Shopping" }
      ]
    },
    {
      phase: "6-8 Months Before",
      color: "bg-green-100 text-green-800",
      icon: <ShoppingBag className="w-7 h-7 text-gray-500" />,
      priority: "Medium Priority",
      tasks: [
        { task: "Finalize wedding outfits", priority: "high", category: "Shopping" },
        { task: "Book wedding jewelry", priority: "high", category: "Jewelry" },
        { task: "Arrange transportation", priority: "medium", category: "Transport" },
        { task: "Plan pre-wedding photoshoot", priority: "medium", category: "Photography" },
        { task: "Book accommodation for guests", priority: "medium", category: "Accommodation" },
        { task: "Finalize menu with caterer", priority: "medium", category: "Catering" },
        { task: "Order wedding favors", priority: "low", category: "Gifts" }
      ]
    },
    {
      phase: "3-5 Months Before",
      color: "bg-yellow-100 text-yellow-800",
      icon: <Edit3 className="w-7 h-7 text-gray-500" />,
      priority: "Medium Priority",
      tasks: [
        { task: "Send wedding invitations", priority: "high", category: "Invitations" },
        { task: "Finalize decoration theme", priority: "high", category: "Decoration" },
        { task: "Book wedding videographer", priority: "medium", category: "Photography" },
        { task: "Plan sangeet and mehendi", priority: "medium", category: "Events" },
        { task: "Arrange wedding insurance", priority: "medium", category: "Legal" },
        { task: "Book spa and beauty treatments", priority: "low", category: "Beauty" },
        { task: "Plan bachelor/bachelorette party", priority: "low", category: "Events" }
      ]
    }
  ]

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'bg-red-100 text-red-800'
      case 'medium': return 'bg-yellow-100 text-yellow-800'
      case 'low': return 'bg-green-100 text-green-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getPriorityIcon = (priority: string) => {
    switch (priority) {
      case 'high': return <AlertCircle className="w-3 h-3" />
      case 'medium': return <Clock className="w-3 h-3" />
      case 'low': return <CheckCircle className="w-3 h-3" />
      default: return <CheckCircle className="w-3 h-3" />
    }
  }

  return (
    <>
      <SimpleSEO
        title="Wedding Planning Timeline - BookmyFestive"
        description="Complete wedding planning timeline with month-by-month checklist. Plan your perfect wedding with our comprehensive timeline guide from 12 months to wedding day."
        keywords="wedding timeline, wedding planning checklist, wedding schedule, month by month wedding planning, BookmyFestive"
        url="/planning/timeline"
      />
      
      <div className="min-h-screen bg-background">
        <TopHeader />
        <Header />
        
        <main className="py-4 px-3 sm:py-6 sm:px-4 lg:py-8 lg:pt-20">
          <div className="max-w-7xl mx-auto">
            {/* Header Section */}
            <div className="text-center mb-6 sm:mb-8 lg:mb-12">
              <div className="inline-flex items-center justify-center w-12 h-12 sm:w-16 sm:h-16 bg-gradient-to-br from-primary to-pink-600 rounded-full mb-3 sm:mb-4 lg:mb-6 shadow-lg">
                <Calendar className="w-6 h-6 sm:w-8 sm:h-8 text-white" />
              </div>
              <h1 className="text-xl sm:text-2xl lg:text-3xl xl:text-4xl font-bold text-foreground mb-2 sm:mb-3 lg:mb-4">
                Wedding Planning Timeline
              </h1>
              <p className="text-sm sm:text-base lg:text-lg text-muted-foreground max-w-2xl mx-auto mb-3 sm:mb-4 lg:mb-6 px-2 sm:px-4">
                Your complete month-by-month guide to planning the perfect wedding
              </p>
            </div>

                        {/* Timeline Overview */}
            <section className="py-8 sm:py-12 lg:py-16">
              <div className="container mx-auto px-4 max-w-6xl">
                {/* Timeline Phases */}
                <div className="space-y-8">
                  {timelinePhases.map((phase, index) => (
                    <div key={index} className="mb-8">
                      <div className="flex items-center gap-3 mb-6">
                        <div className="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center">
                          {phase.icon}
                        </div>
                        <div>
                          <h2 className="text-xl font-semibold text-foreground">{phase.phase}</h2>
                          <p className="text-sm text-muted-foreground">{phase.tasks.length} tasks</p>
                        </div>
                      </div>
                      
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {phase.tasks.map((item, taskIndex) => (
                          <div 
                            key={taskIndex}
                            className="p-4 bg-card border border-border rounded-lg hover:shadow-sm transition-shadow"
                          >
                            <div className="flex items-start gap-3">
                              <div className="mt-1 text-muted-foreground">
                                {getPriorityIcon(item.priority)}
                              </div>
                              <div className="flex-1">
                                <h4 className="font-medium text-foreground mb-1">{item.task}</h4>
                                <div className="flex items-center gap-2">
                                  <span className={`text-xs px-2 py-1 rounded-full ${getPriorityColor(item.priority)}`}>
                                    {item.priority}
                                  </span>
                                </div>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </section>
          </div>
        </main>

        <Footer />
      </div>
    </>
  )
}
