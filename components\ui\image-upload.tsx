"use client"

import React, { useState, useRef } from 'react'
import { Upload, X, Image as ImageIcon, Eye, AlertTriangle } from 'lucide-react'
import { Button } from './button'
import { showToast, toastMessages } from '@/lib/toast'
import {
  compressImageForDB,
  compressImagesForDB,
  checkImagesSizeLimit,
  DB_LIMITS
} from '@/lib/image-optimization'

interface ImageUploadProps {
  images: string[]
  onImagesChange: (images: string[]) => void
  maxImages?: number
  label?: string
  description?: string
  className?: string
  compressForDB?: boolean // New prop to enable DB compression
  showSizeWarning?: boolean // Show size warning for DynamoDB limits
}

export function ImageUpload({
  images,
  onImagesChange,
  maxImages = 10,
  label = "Upload Images",
  description = "Upload product images (JPG, PNG, WebP)",
  className = "",
  compressForDB = false,
  showSizeWarning = false
}: ImageUploadProps) {
  const [dragActive, setDragActive] = useState(false)
  const [uploading, setUploading] = useState(false)
  const [previewImage, setPreviewImage] = useState<string | null>(null)
  const [sizeWarning, setSizeWarning] = useState<string | null>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)

  // Handle file selection
  const handleFiles = async (files: FileList) => {
    if (files.length === 0) return

    setUploading(true)
    setSizeWarning(null)
    const newImages: string[] = []

    for (let i = 0; i < files.length && images.length + newImages.length < maxImages; i++) {
      const file = files[i]

      // Validate file type
      if (!file.type.startsWith('image/')) {
        showToast.error(`${file.name} is not an image file`)
        continue
      }

      // Validate file size (5MB limit for initial upload)
      if (file.size > 5 * 1024 * 1024) {
        showToast.error(`${file.name} is too large. Maximum size is 5MB`)
        continue
      }

      try {
        let processedImage: string

        if (compressForDB) {
          // Compress image for database storage
          processedImage = await compressImageForDB(file, {
            maxSizeKB: DB_LIMITS.TARGET_IMAGE_SIZE / 1024,
            quality: 50,
            maxWidth: 800,
            maxHeight: 600
          })

          showToast.success(`${file.name} compressed for database storage`)
        } else {
          // Convert to base64 for preview (in real app, upload to cloud storage)
          processedImage = await fileToBase64(file)
        }

        newImages.push(processedImage)
      } catch (error) {
        console.error('Error processing file:', error)
        showToast.error(`Error processing ${file.name}`)
      }
    }

    const updatedImages = [...images, ...newImages]

    // Check size limits if enabled
    if (showSizeWarning || compressForDB) {
      const sizeCheck = checkImagesSizeLimit(updatedImages)
      if (!sizeCheck.isValid) {
        setSizeWarning(
          `Warning: Images exceed DynamoDB size limit by ${sizeCheck.exceedsBy?.toFixed(1)}KB. ` +
          `Total: ${sizeCheck.totalSizeKB.toFixed(1)}KB, Limit: ${sizeCheck.maxAllowedKB.toFixed(1)}KB`
        )

        if (compressForDB) {
          // Auto-compress all images if they exceed the limit
          try {
            const compressedImages = await compressImagesForDB(updatedImages)
            onImagesChange(compressedImages)
            showToast.success('Images automatically compressed to fit database limits')
          } catch (error) {
            console.error('Error compressing images:', error)
            showToast.error('Failed to compress images. Please reduce the number of images.')
          }
        } else {
          onImagesChange(updatedImages)
        }
      } else {
        onImagesChange(updatedImages)
      }
    } else {
      onImagesChange(updatedImages)
    }

    setUploading(false)
  }

  // Convert file to base64
  const fileToBase64 = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.readAsDataURL(file)
      reader.onload = () => resolve(reader.result as string)
      reader.onerror = error => reject(error)
    })
  }

  // Handle drag events
  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true)
    } else if (e.type === "dragleave") {
      setDragActive(false)
    }
  }

  // Handle drop
  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setDragActive(false)
    
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFiles(e.dataTransfer.files)
    }
  }

  // Handle input change
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    e.preventDefault()
    if (e.target.files && e.target.files[0]) {
      handleFiles(e.target.files)
    }
  }

  // Remove image
  const removeImage = (index: number) => {
    const newImages = images.filter((_, i) => i !== index)
    onImagesChange(newImages)
  }

  // Move image position
  const moveImage = (fromIndex: number, toIndex: number) => {
    const newImages = [...images]
    const [movedImage] = newImages.splice(fromIndex, 1)
    newImages.splice(toIndex, 0, movedImage)
    onImagesChange(newImages)
  }

  return (
    <div className={className}>
      <label className="block text-sm font-medium text-gray-700 mb-2">{label}</label>
      
      {/* Upload Area */}
      <div
        className={`relative border-2 border-dashed rounded-lg p-6 text-center transition-colors ${
          dragActive 
            ? 'border-primary bg-primary/5' 
            : 'border-gray-300 hover:border-gray-400'
        } ${uploading ? 'opacity-50 pointer-events-none' : ''}`}
        onDragEnter={handleDrag}
        onDragLeave={handleDrag}
        onDragOver={handleDrag}
        onDrop={handleDrop}
      >
        <input
          ref={fileInputRef}
          type="file"
          multiple
          accept="image/*"
          onChange={handleChange}
          className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
          disabled={uploading || images.length >= maxImages}
        />
        
        <div className="space-y-2">
          <Upload className="mx-auto h-8 w-8 text-gray-400" />
          <div className="text-sm text-gray-600">
            <span className="font-medium text-primary cursor-pointer">Click to upload</span> or drag and drop
          </div>
          <p className="text-xs text-gray-500">{description}</p>
          <p className="text-xs text-gray-500">
            {images.length}/{maxImages} images uploaded
          </p>
        </div>

        {uploading && (
          <div className="absolute inset-0 flex items-center justify-center bg-white/80">
            <div className="text-sm text-gray-600">Uploading...</div>
          </div>
        )}
      </div>

      {/* Size Warning */}
      {sizeWarning && (
        <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg flex items-start gap-2">
          <AlertTriangle className="h-5 w-5 text-yellow-600 mt-0.5 flex-shrink-0" />
          <div className="text-sm text-yellow-800">
            <p className="font-medium">Database Size Warning</p>
            <p>{sizeWarning}</p>
            {compressForDB && (
              <p className="mt-1 text-xs">Images will be automatically compressed when saving to database.</p>
            )}
          </div>
        </div>
      )}

      {/* Image Grid */}
      {images.length > 0 && (
        <div className="mt-4">
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
            {images.map((image, index) => (
              <div key={index} className="relative group">
                <div className="aspect-square bg-gray-100 rounded-lg overflow-hidden">
                  <img
                    src={image}
                    alt={`Upload ${index + 1}`}
                    className="w-full h-full object-cover"
                  />
                </div>
                
                {/* Image Controls */}
                <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity rounded-lg flex items-center justify-center gap-2">
                  <Button
                    type="button"
                    size="sm"
                    variant="secondary"
                    onClick={() => setPreviewImage(image)}
                    className="h-8 w-8 p-0"
                  >
                    <Eye className="h-4 w-4" />
                  </Button>
                  <Button
                    type="button"
                    size="sm"
                    variant="destructive"
                    onClick={() => removeImage(index)}
                    className="h-8 w-8 p-0"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>

                {/* Main Image Badge */}
                {index === 0 && (
                  <div className="absolute top-2 left-2 bg-primary text-white text-xs px-2 py-1 rounded">
                    Main
                  </div>
                )}

                {/* Position Controls */}
                <div className="absolute bottom-2 right-2 flex gap-1">
                  {index > 0 && (
                    <Button
                      type="button"
                      size="sm"
                      variant="secondary"
                      onClick={() => moveImage(index, index - 1)}
                      className="h-6 w-6 p-0 text-xs"
                    >
                      ←
                    </Button>
                  )}
                  {index < images.length - 1 && (
                    <Button
                      type="button"
                      size="sm"
                      variant="secondary"
                      onClick={() => moveImage(index, index + 1)}
                      className="h-6 w-6 p-0 text-xs"
                    >
                      →
                    </Button>
                  )}
                </div>
              </div>
            ))}
          </div>
          
          <p className="text-xs text-gray-500 mt-2">
            First image will be used as the main image. Drag to reorder or use arrow buttons.
          </p>
        </div>
      )}

      {/* Preview Modal */}
      {previewImage && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/80 p-4">
          <div className="relative max-w-4xl max-h-full">
            <img
              src={previewImage}
              alt="Preview"
              className="max-w-full max-h-full object-contain"
            />
            <Button
              type="button"
              variant="secondary"
              size="sm"
              onClick={() => setPreviewImage(null)}
              className="absolute top-4 right-4"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </div>
      )}
    </div>
  )
}
