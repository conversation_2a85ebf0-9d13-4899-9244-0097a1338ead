"use client"

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Phone, Mail, User, MapPin, Heart, CheckCircle, 
  RefreshCw, AlertCircle, ArrowRight 
} from 'lucide-react';
import { useRouter } from 'next/navigation';
import MobileAuthService from '@/lib/services/mobileAuthService';
import { profileService } from '@/lib/services/profileService';
import toast from 'react-hot-toast';

interface MobileRegistrationFormProps {
  onSuccess?: () => void;
  redirectTo?: string;
}

export default function MobileRegistrationForm({ onSuccess, redirectTo = '/dashboard' }: MobileRegistrationFormProps) {
  const router = useRouter();
  
  // Form state
  const [step, setStep] = useState<'details' | 'otp' | 'complete'>('details');
  const [registrationType, setRegistrationType] = useState<'mobile' | 'email'>('mobile');
  
  // User details
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phoneNumber: '',
    countryCode: '+91',
    city: '',
    state: '',
    weddingDate: '',
    partnerName: '',
    budgetRange: '',
    agreeToTerms: false,
    agreeToMarketing: false
  });
  
  // OTP state
  const [otp, setOtp] = useState('');
  const [resendTimer, setResendTimer] = useState(0);
  
  // UI state
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  // Country codes and other options
  const countryCodes = MobileAuthService.getSupportedCountryCodes();
  const indianStates = [
    'Tamil Nadu', 'Karnataka', 'Kerala', 'Andhra Pradesh', 'Telangana',
    'Maharashtra', 'Gujarat', 'Rajasthan', 'Punjab', 'Haryana',
    'Uttar Pradesh', 'Madhya Pradesh', 'West Bengal', 'Odisha', 'Bihar'
  ];
  
  const budgetRanges = [
    'Under ₹2 Lakhs', '₹2-5 Lakhs', '₹5-10 Lakhs', 
    '₹10-20 Lakhs', '₹20-50 Lakhs', 'Above ₹50 Lakhs'
  ];

  // Start resend timer
  const startResendTimer = () => {
    setResendTimer(30);
    const timer = setInterval(() => {
      setResendTimer((prev) => {
        if (prev <= 1) {
          clearInterval(timer);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);
  };

  // Handle form input changes
  const handleInputChange = (field: string, value: string | boolean) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Validate form data
  const validateForm = (): boolean => {
    if (!formData.firstName.trim()) {
      setError('First name is required');
      return false;
    }
    
    if (!formData.lastName.trim()) {
      setError('Last name is required');
      return false;
    }
    
    if (registrationType === 'email') {
      if (!formData.email || !MobileAuthService.validateEmail(formData.email)) {
        setError('Please enter a valid email address');
        return false;
      }
    } else {
      if (!formData.phoneNumber || !MobileAuthService.validatePhoneNumber(formData.phoneNumber)) {
        setError('Please enter a valid 10-digit mobile number');
        return false;
      }
    }
    
    if (!formData.agreeToTerms) {
      setError('Please agree to the terms and conditions');
      return false;
    }
    
    return true;
  };

  // Handle registration
  const handleRegistration = async () => {
    if (!validateForm()) return;
    
    setLoading(true);
    setError('');
    
    try {
      let result;
      
      if (registrationType === 'email') {
        result = await MobileAuthService.sendEmailOTP(formData.email);
      } else {
        result = await MobileAuthService.sendMobileOTP(formData.phoneNumber, formData.countryCode);
      }
      
      if (result.success) {
        setStep('otp');
        setSuccess(result.message);
        startResendTimer();
        toast.success('OTP sent successfully!');
      } else {
        setError(result.message);
      }
    } catch (error: any) {
      setError('Registration failed. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Handle OTP verification
  const handleOTPVerification = async () => {
    if (!otp || otp.length !== 6) {
      setError('Please enter the 6-digit OTP');
      return;
    }
    
    setLoading(true);
    setError('');
    
    try {
      let result;
      
      if (registrationType === 'email') {
        result = await MobileAuthService.verifyEmailOTP(formData.email, otp);
      } else {
        result = await MobileAuthService.verifyMobileOTP(formData.phoneNumber, otp, formData.countryCode);
      }
      
      if (result.success) {
        // Create user profile
        await createUserProfile(result.user);
        setStep('complete');
        setSuccess('Registration completed successfully!');
        toast.success('Welcome to BookmyFestive!');
        
        // Redirect after a short delay
        setTimeout(() => {
          onSuccess?.();
          router.push(redirectTo);
        }, 2000);
      } else {
        setError(result.message);
      }
    } catch (error: any) {
      setError('OTP verification failed. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Create user profile
  const createUserProfile = async (user: any) => {
    try {
      const profileData = {
        firstName: formData.firstName,
        lastName: formData.lastName,
        email: formData.email || user.attributes?.email,
        phoneNumber: registrationType === 'mobile' ? `${formData.countryCode}${formData.phoneNumber}` : undefined,
        city: formData.city,
        state: formData.state,
        weddingDate: formData.weddingDate,
        partnerName: formData.partnerName,
        budgetRange: formData.budgetRange,
        registrationSource: registrationType === 'mobile' ? 'MOBILE_OTP' : 'EMAIL_OTP',
        accountType: 'PERSONAL',
        isVerified: true,
        agreeToMarketing: formData.agreeToMarketing
      };
      
      await profileService.createProfile(user.username, profileData);
    } catch (error) {
      console.error('Error creating profile:', error);
      // Don't throw error as user is already registered
    }
  };

  // Handle resend OTP
  const handleResendOTP = async () => {
    if (resendTimer > 0) return;
    
    setLoading(true);
    setError('');
    
    try {
      const result = await MobileAuthService.resendOTP(
        registrationType === 'email' ? formData.email : formData.phoneNumber,
        registrationType,
        formData.countryCode
      );
      
      if (result.success) {
        setSuccess('OTP resent successfully!');
        startResendTimer();
        toast.success('OTP resent!');
      } else {
        setError(result.message);
      }
    } catch (error: any) {
      setError('Failed to resend OTP. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card className="w-full max-w-lg mx-auto">
      <CardHeader className="text-center">
        <CardTitle className="text-2xl font-bold text-gray-900">
          {step === 'details' && 'Create Your Account'}
          {step === 'otp' && 'Verify Your Account'}
          {step === 'complete' && 'Welcome to BookmyFestive!'}
        </CardTitle>
        <p className="text-gray-600">
          {step === 'details' && 'Join thousands of couples planning their dream wedding'}
          {step === 'otp' && `Enter the OTP sent to your ${registrationType === 'email' ? 'email' : 'mobile'}`}
          {step === 'complete' && 'Your account has been created successfully'}
        </p>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* Error/Success Messages */}
        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}
        
        {success && (
          <Alert className="border-green-200 bg-green-50">
            <CheckCircle className="h-4 w-4 text-green-600" />
            <AlertDescription className="text-green-800">{success}</AlertDescription>
          </Alert>
        )}

        {/* Step 1: User Details */}
        {step === 'details' && (
          <div className="space-y-4">
            {/* Registration Type */}
            <div className="grid grid-cols-2 gap-2">
              <Button
                variant={registrationType === 'mobile' ? 'default' : 'outline'}
                onClick={() => setRegistrationType('mobile')}
                className="flex items-center gap-2"
              >
                <Phone className="w-4 h-4" />
                Mobile
              </Button>
              <Button
                variant={registrationType === 'email' ? 'default' : 'outline'}
                onClick={() => setRegistrationType('email')}
                className="flex items-center gap-2"
              >
                <Mail className="w-4 h-4" />
                Email
              </Button>
            </div>

            {/* Name Fields */}
            <div className="grid grid-cols-2 gap-3">
              <div>
                <Label htmlFor="firstName">First Name</Label>
                <Input
                  id="firstName"
                  value={formData.firstName}
                  onChange={(e) => handleInputChange('firstName', e.target.value)}
                  placeholder="Enter first name"
                />
              </div>
              <div>
                <Label htmlFor="lastName">Last Name</Label>
                <Input
                  id="lastName"
                  value={formData.lastName}
                  onChange={(e) => handleInputChange('lastName', e.target.value)}
                  placeholder="Enter last name"
                />
              </div>
            </div>

            {/* Contact Information */}
            {registrationType === 'email' ? (
              <div>
                <Label htmlFor="email">Email Address</Label>
                <Input
                  id="email"
                  type="email"
                  value={formData.email}
                  onChange={(e) => handleInputChange('email', e.target.value)}
                  placeholder="Enter your email"
                />
              </div>
            ) : (
              <div>
                <Label htmlFor="mobile">Mobile Number</Label>
                <div className="flex gap-2">
                  <Select value={formData.countryCode} onValueChange={(value) => handleInputChange('countryCode', value)}>
                    <SelectTrigger className="w-24">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {countryCodes.map((country) => (
                        <SelectItem key={country.code} value={country.code}>
                          {country.flag} {country.code}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <Input
                    id="mobile"
                    type="tel"
                    value={formData.phoneNumber}
                    onChange={(e) => handleInputChange('phoneNumber', e.target.value.replace(/\D/g, ''))}
                    placeholder="Enter mobile number"
                    maxLength={10}
                    className="flex-1"
                  />
                </div>
              </div>
            )}

            {/* Location */}
            <div className="grid grid-cols-2 gap-3">
              <div>
                <Label htmlFor="state">State</Label>
                <Select value={formData.state} onValueChange={(value) => handleInputChange('state', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select state" />
                  </SelectTrigger>
                  <SelectContent>
                    {indianStates.map((state) => (
                      <SelectItem key={state} value={state}>
                        {state}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="city">City</Label>
                <Input
                  id="city"
                  value={formData.city}
                  onChange={(e) => handleInputChange('city', e.target.value)}
                  placeholder="Enter your city"
                />
              </div>
            </div>

            {/* Wedding Details (Optional) */}
            <div className="space-y-3">
              <div>
                <Label htmlFor="weddingDate">Wedding Date (Optional)</Label>
                <Input
                  id="weddingDate"
                  type="date"
                  value={formData.weddingDate}
                  onChange={(e) => handleInputChange('weddingDate', e.target.value)}
                />
              </div>
              
              <div>
                <Label htmlFor="budgetRange">Budget Range (Optional)</Label>
                <Select value={formData.budgetRange} onValueChange={(value) => handleInputChange('budgetRange', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select budget range" />
                  </SelectTrigger>
                  <SelectContent>
                    {budgetRanges.map((range) => (
                      <SelectItem key={range} value={range}>
                        {range}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Terms and Conditions */}
            <div className="space-y-3">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="terms"
                  checked={formData.agreeToTerms}
                  onCheckedChange={(checked) => handleInputChange('agreeToTerms', checked as boolean)}
                />
                <Label htmlFor="terms" className="text-sm">
                  I agree to the{' '}
                  <a href="/terms" className="text-primary hover:underline">Terms & Conditions</a>
                  {' '}and{' '}
                  <a href="/privacy" className="text-primary hover:underline">Privacy Policy</a>
                </Label>
              </div>
              
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="marketing"
                  checked={formData.agreeToMarketing}
                  onCheckedChange={(checked) => handleInputChange('agreeToMarketing', checked as boolean)}
                />
                <Label htmlFor="marketing" className="text-sm">
                  I want to receive wedding tips and vendor recommendations
                </Label>
              </div>
            </div>

            <Button
              onClick={handleRegistration}
              disabled={loading}
              className="w-full"
            >
              {loading ? (
                <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
              ) : (
                <ArrowRight className="w-4 h-4 mr-2" />
              )}
              Create Account
            </Button>
          </div>
        )}

        {/* Step 2: OTP Verification */}
        {step === 'otp' && (
          <div className="space-y-4">
            <div>
              <Label htmlFor="otp">Enter OTP</Label>
              <Input
                id="otp"
                type="text"
                placeholder="Enter 6-digit OTP"
                value={otp}
                onChange={(e) => setOtp(e.target.value.replace(/\D/g, '').slice(0, 6))}
                maxLength={6}
                className="text-center text-lg tracking-widest"
              />
            </div>

            <Button
              onClick={handleOTPVerification}
              disabled={loading || otp.length !== 6}
              className="w-full"
            >
              {loading ? (
                <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
              ) : (
                <CheckCircle className="w-4 h-4 mr-2" />
              )}
              Verify & Create Account
            </Button>

            <div className="text-center">
              <Button
                variant="link"
                onClick={handleResendOTP}
                disabled={resendTimer > 0 || loading}
                className="text-sm"
              >
                {resendTimer > 0 ? `Resend in ${resendTimer}s` : 'Resend OTP'}
              </Button>
            </div>
          </div>
        )}

        {/* Step 3: Complete */}
        {step === 'complete' && (
          <div className="text-center space-y-4">
            <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto">
              <CheckCircle className="w-8 h-8 text-green-600" />
            </div>
            <p className="text-gray-600">
              Redirecting you to your dashboard...
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
