import React from 'react';
import {
  View,
  Text,
  StyleSheet,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { useTheme } from '../providers/ThemeProvider';
import { Button } from '../components/ui/Button';
import { Card } from '../components/ui/Card';

export default function NotFoundScreen() {
  const navigation = useNavigation();
  const { theme } = useTheme();

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
      padding: 20,
      justifyContent: 'center',
    },
    content: {
      alignItems: 'center',
      gap: 24,
    },
    notFoundIcon: {
      fontSize: 64,
      color: theme.colors.textSecondary,
      marginBottom: 16,
    },
    title: {
      fontSize: 24,
      fontWeight: 'bold',
      color: theme.colors.text,
      textAlign: 'center',
      marginBottom: 8,
    },
    message: {
      fontSize: 16,
      color: theme.colors.textSecondary,
      textAlign: 'center',
      lineHeight: 24,
      marginBottom: 24,
    },
    buttonContainer: {
      width: '100%',
      gap: 12,
    },
  });

  return (
    <View style={styles.container}>
      <Card>
        <View style={styles.content}>
          <Text style={styles.notFoundIcon}>🔍</Text>
          <Text style={styles.title}>Page Not Found</Text>
          <Text style={styles.message}>
            The page you're looking for doesn't exist or has been moved.
          </Text>
          
          <View style={styles.buttonContainer}>
            <Button
              onPress={() => navigation.goBack()}
              variant="default"
            >
              Go Back
            </Button>
            
            <Button
              onPress={() => navigation.navigate('MainTabs' as never)}
              variant="outline"
            >
              Go to Home
            </Button>
          </View>
        </View>
      </Card>
    </View>
  );
}
