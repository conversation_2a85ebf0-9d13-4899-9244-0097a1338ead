import React, { useState } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, SafeAreaView, Image, ScrollView } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../providers/ThemeProvider';
import { useAuth } from '../providers/AuthProvider';
import { useNavigation } from '@react-navigation/native';
import { Badge, Button } from './ui';
import HamburgerMenu from './HamburgerMenu';
import type { AppStackParamList } from '../navigation/AppNavigator';
import type { StackNavigationProp } from '@react-navigation/stack';

type NavigationProp = StackNavigationProp<AppStackParamList>;

export interface HeaderProps {
  title?: string;
  showBack?: boolean;
  showProfile?: boolean;
  showNotifications?: boolean;
  showSearch?: boolean;
  showHamburger?: boolean;
  showLogo?: boolean;
  showNavigation?: boolean;
  onSearchPress?: () => void;
  rightComponent?: React.ReactNode;
  variant?: 'default' | 'minimal' | 'transparent';
}

export function Header({
  title,
  showBack = false,
  showProfile = true,
  showNotifications = true,
  showSearch = true,
  showHamburger = true,
  showLogo = true,
  showNavigation = true,
  onSearchPress,
  rightComponent,
  variant = 'default',
}: HeaderProps) {
  const { theme } = useTheme();
  const { user } = useAuth();
  const navigation = useNavigation<NavigationProp>();
  const [hamburgerMenuVisible, setHamburgerMenuVisible] = useState(false);
  const [vendorsMenuOpen, setVendorsMenuOpen] = useState(false);
  const [venuesMenuOpen, setVenuesMenuOpen] = useState(false);
  const [shopMenuOpen, setShopMenuOpen] = useState(false);
  const [planningMenuOpen, setPlanningMenuOpen] = useState(false);

  const handleBackPress = () => {
    navigation.goBack();
  };

  const handleProfilePress = () => {
    if (user) {
      navigation.navigate('Profile');
    } else {
      navigation.navigate('Login');
    }
  };

  const handleNotificationsPress = () => {
    navigation.navigate('Notifications');
  };

  const handleSearchPress = () => {
    if (onSearchPress) {
      onSearchPress();
    } else {
      navigation.navigate('Search');
    }
  };

  const handleHamburgerPress = () => {
    setHamburgerMenuVisible(true);
  };

  const getHeaderStyle = () => {
    const baseStyle = {
      backgroundColor: theme.colors.background,
      borderBottomColor: theme.colors.border,
    };

    switch (variant) {
      case 'transparent':
        return {
          ...baseStyle,
          backgroundColor: 'transparent',
          borderBottomWidth: 0,
        };
      case 'minimal':
        return {
          ...baseStyle,
          borderBottomWidth: 0,
        };
      default:
        return {
          ...baseStyle,
          borderBottomWidth: 1,
        };
    }
  };

  return (
    <SafeAreaView style={[styles.safeArea, { backgroundColor: variant === 'transparent' ? 'transparent' : theme.colors.background }]}>
      <View style={[styles.container, getHeaderStyle()]}>
      

        {/* Back Button Section */}
        {showBack && (
          <View style={styles.leftSection}>
            <TouchableOpacity onPress={handleBackPress} style={styles.iconButton}>
              <Ionicons name="arrow-back" size={24} color={theme.colors.foreground} />
            </TouchableOpacity>
          </View>
        )}

        {/* Hamburger Menu for Mobile */}
        {showHamburger && !showBack && (
          <View style={styles.leftSection}>
            <TouchableOpacity onPress={handleHamburgerPress} style={styles.iconButton}>
              <Ionicons name="menu" size={24} color={theme.colors.foreground} />
            </TouchableOpacity>
          </View>
        )}

        {/* Title Section */}
        <View style={styles.centerSection}>
          {title && (
            <Text style={[styles.title, { color: theme.colors.foreground }]} numberOfLines={1}>
              {title}
            </Text>
          )}
        </View>

        <View style={styles.rightSection}>
          {showSearch && (
            <TouchableOpacity onPress={handleSearchPress} style={styles.iconButton}>
              <Ionicons name="search" size={24} color={theme.colors.foreground} />
            </TouchableOpacity>
          )}

          {showNotifications && user && (
            <TouchableOpacity onPress={handleNotificationsPress} style={styles.iconButton}>
              <View style={styles.notificationContainer}>
                <Ionicons name="notifications-outline" size={24} color={theme.colors.foreground} />
                {/* Notification badge - you can add logic to show count */}
                <Badge variant="destructive" style={styles.notificationBadge}>
                  3
                </Badge>
              </View>
            </TouchableOpacity>
          )}

          {/* Cart Icon - matching web app */}
          <TouchableOpacity onPress={() => navigation.navigate('Cart')} style={styles.iconButton}>
            <View style={styles.notificationContainer}>
              <Ionicons name="bag-outline" size={24} color={theme.colors.foreground} />
              {/* Cart badge - you can add logic to show count */}
              <Badge variant="destructive" style={styles.notificationBadge}>
                2
              </Badge>
            </View>
          </TouchableOpacity>

          {showProfile && (
            <TouchableOpacity onPress={handleProfilePress} style={styles.iconButton}>
              {user ? (
                <View style={[styles.profileAvatar, { backgroundColor: theme.colors.primary }]}>
                  <Text style={[styles.profileInitial, { color: theme.colors.primaryForeground }]}>
                    {user.fullName?.charAt(0).toUpperCase() || 'U'}
                  </Text>
                </View>
              ) : (
                <Button
                  title="Login"
                  variant="outline"
                  size="sm"
                  onPress={handleProfilePress}
                />
              )}
            </TouchableOpacity>
          )}

          {rightComponent}
        </View>
      </View>

      <HamburgerMenu
        visible={hamburgerMenuVisible}
        onClose={() => setHamburgerMenuVisible(false)}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  safeArea: {
    backgroundColor: 'white',
  },
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16, // px-4 to match web app
    paddingVertical: 16, // py-4 to match web app
    borderBottomWidth: 1,
    minHeight: 64, // h-16 to match web app
  },
  leftSection: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
    minWidth: 100,
  },
  centerSection: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  rightSection: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-end',
    gap: 8, // gap-2 to match web app
    minWidth: 100,
  },
  logoSection: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  logoText: {
    fontSize: 24, // text-2xl to match web app
    fontWeight: 'bold',
    letterSpacing: -0.025, // tracking-tight
  },
  title: {
    fontSize: 20, // text-xl
    fontWeight: '600', // font-semibold
    textAlign: 'center',
    letterSpacing: -0.025,
  },
  navigationSection: {
    flex: 1,
    marginHorizontal: 16,
  },
  navigationContent: {
    alignItems: 'center',
    gap: 24, // gap-6
  },
  navItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    gap: 4,
  },
  navText: {
    fontSize: 14, // text-sm
    fontWeight: '500', // font-medium
  },
  iconButton: {
    padding: 8,
    marginHorizontal: 4,
  },
  notificationContainer: {
    position: 'relative',
  },
  notificationBadge: {
    position: 'absolute',
    top: -4,
    right: -4,
    minWidth: 18,
    height: 18,
    borderRadius: 9,
    paddingHorizontal: 4,
    paddingVertical: 0,
  },
  profileAvatar: {
    width: 32,
    height: 32,
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
  profileInitial: {
    fontSize: 14,
    fontWeight: '600',
  },
});
