"use client";

import { useState, useEffect } from "react";
import { useSearchParams } from "next/navigation";
import { TopHeader } from "@/components/top-header";
import { Header } from "@/components/header";
import { Footer } from "@/components/footer";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Search,
  MapPin,
  Star,
  Users,
  Heart,
  Loader2,
  Building,
  Trees,
  Castle,
  Waves,
  Home,
  Crown,
  CheckCircle,
  Share2,
  Filter,
  Calendar,
  X,
} from "lucide-react";

import Link from "next/link";
import { venueService, type VenueResponse } from "@/lib/services/venueService";
import { useSafeTranslation } from '@/hooks/use-safe-translation';

// Venue types for category browsing
const venueTypes = [
  { icon: Building, name: "Banquet Hall", value: "Banquet Hall" },
  { icon: Trees, name: "Resort", value: "Resort" },
  { icon: Castle, name: "Heritage", value: "Heritage Property" },
  { icon: Waves, name: "Beach Resort", value: "Beach Resort" },
  { icon: Home, name: "Farm House", value: "Farm House" },
  { icon: Crown, name: "Palace", value: "Palace" },
];

export default function VenuesPage() {
  const searchParams = useSearchParams();
  const { t } = useSafeTranslation();
  const [venues, setVenues] = useState<VenueResponse[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [nextToken, setNextToken] = useState<string | undefined>(undefined);
  const [hasMoreData, setHasMoreData] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCity, setSelectedCity] = useState("");

  const [selectedType, setSelectedType] = useState("all");
  const [selectedCapacity, setSelectedCapacity] = useState("all");
  const [selectedBudget, setSelectedBudget] = useState("all");
  const [selectedAmenity, setSelectedAmenity] = useState("all");
  const [sortBy, setSortBy] = useState("default");
  const [imageErrors, setImageErrors] = useState<Set<string>>(new Set());

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(10); // 10 venues per page
  const [totalServerItems, setTotalServerItems] = useState(0);
  const [serverPages, setServerPages] = useState<{
    [key: number]: VenueResponse[];
  }>({});
  const [loadedPages, setLoadedPages] = useState<Set<number>>(new Set([1]));

  // Load venues on component mount
  useEffect(() => {
    loadVenues();

    // Handle search parameters from home page and destinations ad
    const searchParam = searchParams.get("search");
    const locationParam = searchParams.get("location");
    const cityParam = searchParams.get("city");
    const stateParam = searchParams.get("state");
    const sourceParam = searchParams.get("source");

    if (searchParam) {
      setSearchTerm(searchParam);
    }

    // Handle location from different sources
    if (cityParam && stateParam) {
      // From destinations ad - combine city and state
      setSelectedCity(`${cityParam}, ${stateParam}`);
    } else if (locationParam) {
      // From home page search
      setSelectedCity(locationParam);
    }

    // Log source for analytics
    if (sourceParam) {
      console.log("Venues page accessed from:", sourceParam);
    }
  }, [searchParams]);

  const loadVenues = async (reset: boolean = false) => {
    try {
      setLoading(true);
      setError(null);

      if (reset) {
        // Reset all pagination state
        setServerPages({});
        setLoadedPages(new Set());
        setCurrentPage(1);
        setNextToken(undefined);
      }

      const venuesData = await venueService.getAllVenues(50); // Load 50 venues to enable pagination

      // Extract the venues array from the response and ensure it's an array
      const venuesArray = venuesData?.venues || [];
      const validVenues = Array.isArray(venuesArray) ? venuesArray : [];

      // Store all venues for client-side pagination
      setVenues(validVenues);
      setTotalServerItems(validVenues.length);

      // Organize venues into pages for traditional pagination
      const pages: { [key: number]: VenueResponse[] } = {};
      for (let i = 0; i < validVenues.length; i += itemsPerPage) {
        const pageNumber = Math.floor(i / itemsPerPage) + 1;
        pages[pageNumber] = validVenues.slice(i, i + itemsPerPage);
      }
      setServerPages(pages);
      setLoadedPages(new Set(Object.keys(pages).map(Number)));

      setNextToken(venuesData?.nextToken);
      setHasMoreData(!!venuesData?.nextToken);
    } catch (err) {
      console.error("Error loading venues:", err);
      setError("Failed to load venues. Please try again.");
      setVenues([]); // Ensure venues is always an array
    } finally {
      setLoading(false);
    }
  };

  // Helper function to get venue image with fallback
  const getVenueImage = (venue: VenueResponse) => {
    if (imageErrors.has(venue.id!)) {
      return "/placeholder.svg";
    }

    // Check if venue has images and the first image is valid
    const firstImage = venue.images?.[0];
    if (
      firstImage &&
      firstImage.trim() !== "" &&
      firstImage !== "undefined" &&
      firstImage !== "null"
    ) {
      return firstImage;
    }

    return "/placeholder.svg";
  };

  // Handle image load errors
  const handleImageError = (venueId: string) => {
    setImageErrors((prev) => new Set([...prev, venueId]));
  };

  // Filter and sort venues
  const filteredVenues = (Array.isArray(venues) ? venues : []).filter(
    (venue) => {
      const matchesSearch =
        venue.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        venue.description?.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesCity =
        !selectedCity ||
        venue.city?.toLowerCase().includes(selectedCity.toLowerCase());
      const matchesType =
        !selectedType || selectedType === "all" || venue.type === selectedType;

      return matchesSearch && matchesCity && matchesType;
    }
  );

  // Sort venues
  const sortedVenues = [...filteredVenues].sort((a, b) => {
    switch (sortBy) {
      case "rating":
        return (b.rating || 0) - (a.rating || 0);
      case "price-low":
        return (
          (parseFloat(a.price.replace(/[^\d]/g, "")) || 0) -
          (parseFloat(b.price.replace(/[^\d]/g, "")) || 0)
        );
      case "price-high":
        return (
          (parseFloat(b.price.replace(/[^\d]/g, "")) || 0) -
          (parseFloat(a.price.replace(/[^\d]/g, "")) || 0)
        );
      case "default":
      default:
        return a.name.localeCompare(b.name);
    }
  });

  // Hybrid pagination logic
  const totalPages = Math.ceil(sortedVenues.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const paginatedVenues = sortedVenues.slice(startIndex, endIndex);

  // Reset and reload data when filters change
  useEffect(() => {
    setCurrentPage(1);
    loadVenues(true); // Reset and load first page
  }, [searchTerm, selectedCity, selectedType, sortBy]);

  // Traditional pagination functions
  const goToPage = (page: number) => {
    setCurrentPage(page);
    window.scrollTo({ top: 0, behavior: "smooth" });
  };

  const goToPrevious = () => {
    if (currentPage > 1) {
      goToPage(currentPage - 1);
    }
  };

  const goToNext = () => {
    if (currentPage < totalPages) {
      goToPage(currentPage + 1);
    }
  };

  const getPageNumbers = () => {
    const pages = [];
    const maxVisiblePages = 5;
    let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
    let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);

    if (endPage - startPage + 1 < maxVisiblePages) {
      startPage = Math.max(1, endPage - maxVisiblePages + 1);
    }

    for (let i = startPage; i <= endPage; i++) {
      pages.push(i);
    }
    return pages;
  };

  // Load more data function for additional server data
  const loadMoreVenues = async () => {
    if (hasMoreData && !loading) {
      await loadVenues(false); // Load next page and append
    }
  };

  const showLoadMore =
    hasMoreData &&
    !loading &&
    totalPages <= Math.ceil(venues.length / itemsPerPage);

  return (
    <div className="min-h-screen bg-white">
      <TopHeader />
      <Header />

      {/* Enhanced Hero Section */}
      <section className="relative bg-gradient-to-br from-primary/90 via-primary to-primary/80 py-12 md:py-24 overflow-hidden">
        <div className="absolute inset-0 w-full h-full z-0">
          <div className="absolute inset-0 bg-black/60"></div>
          <img
            src="/mahal_hero.webp"
            alt="Wedding Venues"
            className="w-full h-full object-cover object-center"
          />
        </div>
        <div className="relative container mx-auto px-4 z-10">
          <div className="text-center mb-8 md:mb-12">
            <h1 className="text-3xl md:text-5xl lg:text-6xl font-bold text-white mb-4 md:mb-6 font-sans tracking-tight drop-shadow-2xl">
              Find Your Perfect Festive Venue
            </h1>
          </div>

          {/* Enhanced Search and Filters - responsive for mobile */}
          <div className="bg-white/95 backdrop-blur-sm rounded-2xl shadow-lg p-4 md:p-3 max-w-3xl mx-auto border border-white/20 w-full">
            {/* Desktop search form */}
            <div className="hidden md:flex flex-row gap-2 w-full flex-nowrap">
              {/* Search Venue Input */}
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search Venue"
                  className="pl-10 pr-2 h-10 rounded-xl border-0 bg-gray-50 focus:bg-white focus:ring-2 focus:ring-primary/20 text-base w-full text-gray-900 placeholder:text-gray-500 transition-all"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  style={{ minWidth: 0 }}
                  autoComplete="off"
                />
              </div>
              {/* City Input */}
              <div className="relative flex-1">
                <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  type="text"
                  placeholder="Enter City"
                  className="pl-10 pr-2 h-10 rounded-xl border-0 bg-gray-50 focus:bg-white focus:ring-2 focus:ring-primary/20 text-base w-full text-gray-900 placeholder:text-gray-500 transition-all"
                  value={selectedCity}
                  onChange={(e) => setSelectedCity(e.target.value)}
                  autoComplete="off"
                  style={{ minWidth: 0 }}
                />
              </div>

              <div className="w-auto">
                <Button
                  className="h-10 bg-primary hover:bg-primary/90 rounded-xl text-base font-semibold shadow-lg hover:shadow-xl transition-all duration-200 px-6"
                  onClick={loadVenues}
                  disabled={loading}
                >
                  {loading ? (
                    <Loader2 className="h-5 w-5 animate-spin mr-2" />
                  ) : (
                    <Search className="h-5 w-5 mr-2" />
                  )}
                  Search
                </Button>
              </div>
            </div>

            {/* Mobile-only simplified search form */}
            <div className="md:hidden">
              <div className="grid grid-cols-2 gap-2 mb-2">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <input
                    type="text"
                    placeholder="Search"
                    className="pl-10 pr-2 h-10 rounded-lg border-0 bg-gray-50 focus:bg-white focus:ring-2 focus:ring-primary/20 text-sm w-full text-gray-900 placeholder:text-gray-500 transition-all"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    autoComplete="off"
                  />
                </div>
                <div className="relative">
                  <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    type="text"
                    placeholder="City"
                    className="pl-10 pr-2 h-10 rounded-lg border-0 bg-gray-50 focus:bg-white focus:ring-2 focus:ring-primary/20 text-sm w-full text-gray-900 placeholder:text-gray-500 transition-all"
                    value={selectedCity}
                    onChange={(e) => setSelectedCity(e.target.value)}
                    autoComplete="off"
                  />
                </div>
              </div>
              <div className="w-full">
                <Button
                  className="h-10 bg-primary hover:bg-primary/90 rounded-lg text-sm font-semibold shadow-lg hover:shadow-xl transition-all duration-200 w-full"
                  onClick={loadVenues}
                  disabled={loading}
                >
                  {loading ? (
                    <Loader2 className="h-4 w-4 animate-spin mr-1" />
                  ) : (
                    <Search className="h-4 w-4 mr-1" />
                  )}
                  Search
                </Button>
              </div>
            </div>

            {/* Desktop filters */}
            <div className="hidden md:flex flex-row gap-3 mt-2 items-center w-full">
              <div className="w-60">
                <Select
                  value={selectedCapacity}
                  onValueChange={setSelectedCapacity}
                >
                  <SelectTrigger className="h-9 rounded-lg border-0 bg-gray-50 focus:bg-white focus:ring-2 focus:ring-primary/20 text-sm w-full text-gray-900 transition-all">
                    <SelectValue placeholder="Capacity" className="text-gray-900" />
                  </SelectTrigger>
                  <SelectContent className="rounded-lg">
                    <SelectItem value="all">All Capacities</SelectItem>
                    <SelectItem value="small">Small</SelectItem>
                    <SelectItem value="medium">Medium</SelectItem>
                    <SelectItem value="large">Large</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="w-60">
                <Select
                  value={selectedBudget}
                  onValueChange={setSelectedBudget}
                >
                  <SelectTrigger className="h-9 rounded-lg border-0 bg-gray-50 focus:bg-white focus:ring-2 focus:ring-primary/20 text-sm w-full text-gray-900 transition-all">
                    <SelectValue placeholder="Budget" className="text-gray-900" />
                  </SelectTrigger>
                  <SelectContent className="rounded-lg">
                    <SelectItem value="all">All Budgets</SelectItem>
                    <SelectItem value="low">Low</SelectItem>
                    <SelectItem value="mid">Mid</SelectItem>
                    <SelectItem value="high">High</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="w-60">
                <Select value={sortBy} onValueChange={setSortBy}>
                  <SelectTrigger className="h-9 rounded-lg border-0 bg-gray-50 focus:bg-white focus:ring-2 focus:ring-primary/20 text-sm w-full text-gray-900 transition-all">
                    <SelectValue placeholder="Sort by" className="text-gray-900" />
                  </SelectTrigger>
                  <SelectContent className="rounded-lg">
                    <SelectItem value="default">Default</SelectItem>
                    <SelectItem value="rating">Highest Rated</SelectItem>
                    <SelectItem value="price-low">
                      Price: Low to High
                    </SelectItem>
                    <SelectItem value="price-high">
                      Price: High to Low
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Mobile-only simplified filters - 3 per row */}
            <div className="md:hidden mt-3">
              <div className="grid grid-cols-3 gap-2">
                <div>
                  <Select
                    value={selectedCapacity}
                    onValueChange={setSelectedCapacity}
                  >
                    <SelectTrigger className="h-10 rounded-lg border border-gray-200 focus:border-primary text-xs w-full text-gray-900">
                      <SelectValue placeholder="Capacity" className="text-gray-900" />
                    </SelectTrigger>
                    <SelectContent className="rounded-lg">
                      <SelectItem value="all" className="text-gray-900">All Capacities</SelectItem>
                      <SelectItem value="small" className="text-gray-900">Small</SelectItem>
                      <SelectItem value="medium" className="text-gray-900">Medium</SelectItem>
                      <SelectItem value="large" className="text-gray-900">Large</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Select
                    value={selectedBudget}
                    onValueChange={setSelectedBudget}
                  >
                    <SelectTrigger className="h-10 rounded-lg border border-gray-200 focus:border-primary text-xs w-full text-gray-900">
                      <SelectValue placeholder="Budget" className="text-gray-900" />
                    </SelectTrigger>
                    <SelectContent className="rounded-lg">
                      <SelectItem value="all" className="text-gray-900">All Budgets</SelectItem>
                      <SelectItem value="low" className="text-gray-900">Low</SelectItem>
                      <SelectItem value="mid" className="text-gray-900">Mid</SelectItem>
                      <SelectItem value="high" className="text-gray-900">High</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Select value={sortBy} onValueChange={setSortBy}>
                    <SelectTrigger className="h-10 rounded-lg border border-gray-200 focus:border-primary text-xs w-full text-gray-900">
                      <SelectValue placeholder="Sort" className="text-gray-900" />
                    </SelectTrigger>
                    <SelectContent className="rounded-lg">
                      <SelectItem value="default" className="text-gray-900">Default</SelectItem>
                      <SelectItem value="rating" className="text-gray-900">Highest Rated</SelectItem>
                      <SelectItem value="price-low" className="text-gray-900">Price: Low-High</SelectItem>
                      <SelectItem value="price-high" className="text-gray-900">
                        Price: High-Low
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Venues Grid */}
      <section className="py-8 md:py-12">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 md:mb-8 gap-4">
            <h2 className="text-xl md:text-2xl font-bold">
              Wedding Venues ({sortedVenues.length})
            </h2>
          </div>

          {/* Loading State */}
          {loading && (
            <div className="flex justify-center items-center py-8 md:py-12">
              <Loader2 className="h-6 w-6 md:h-8 md:w-8 animate-spin text-primary" />
              <span className="ml-2 text-gray-600 text-sm md:text-base">
                Loading venues...
              </span>
            </div>
          )}

          {/* Error State */}
          {error && (
            <div className="text-center py-8 md:py-12">
              <p className="text-red-600 mb-4 text-sm md:text-base">{error}</p>
              <Button
                onClick={loadVenues}
                className="bg-primary hover:bg-primary/90 text-sm md:text-base"
              >
                Try Again
              </Button>
            </div>
          )}

          {/* No Results */}
          {!loading && !error && sortedVenues.length === 0 && (
            <div className="text-center py-8 md:py-12">
              <p className="text-gray-600 mb-4 text-sm md:text-base">
                No venues found matching your criteria.
              </p>
              <Button
                onClick={() => {
                  setSearchTerm("");
                  setSelectedCity("");
                  setSelectedType("all");
                  setSelectedCapacity("all");
                  setSelectedBudget("all");
                  setSortBy("default");
                }}
                variant="outline"
                className="text-sm md:text-base"
              >
                Clear Filters
              </Button>
            </div>
          )}

          {/* Enhanced Venues Grid */}
          {!loading && !error && sortedVenues.length > 0 && (
            <div className="grid grid-cols-2 md:grid-cols-2 lg:grid-cols-3 gap-2 md:gap-8">
              {paginatedVenues.map((venue) => (
                <Link
                  key={venue.id}
                  href={`/venues/${venue.id}`}
                  className="group block overflow-hidden rounded-2xl md:rounded-3xl shadow-lg hover:shadow-2xl transition-all duration-300 border-0 bg-white hover:-translate-y-1 md:hover:-translate-y-2 focus:outline-none focus:ring-2 focus:ring-primary scale-100"
                  style={{
                    width: "100%",
                    margin: "0 auto",
                    textDecoration: "none",
                  }}
                  tabIndex={0}
                  aria-label={`View details for ${venue.name}`}
                >
                  <Card className="border-0 bg-white">
                    <div className="relative overflow-hidden">
                      <img
                        src={getVenueImage(venue)}
                        alt={venue.name}
                        className="w-full h-48 md:h-56 object-cover transition-transform duration-300 group-hover:scale-110"
                        onError={() => handleImageError(venue.id!)}
                      />
                      <div className="absolute inset-0 bg-gradient-to-t from-black/50 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    </div>
                    <CardContent className="p-4 md:p-6 flex flex-col h-full">
                      <div className="mb-3 md:mb-4">
                        <div className="flex items-start justify-between mb-2">
                          <div className="flex-1">
                            <h3 className="font-bold text-lg md:text-xl text-gray-900 mb-1 line-clamp-1">
                              {venue.name}
                            </h3>
                            <p className="text-[#a31515] font-medium text-xs md:text-sm uppercase tracking-wide mb-1">
                              {venue.type}
                            </p>
                          </div>
                        </div>
                        <div className="flex items-center text-gray-500 text-xs md:text-sm mb-2 md:mb-3">
                          <MapPin className="h-3 w-3 md:h-4 md:w-4 mr-1 text-primary" />
                          <span>
                            {venue.location}, {venue.city}, {venue.state}
                          </span>
                          <span className="mx-2">·</span>
                          <Users className="h-3 w-3 md:h-4 md:w-4 mr-1 text-primary" />
                          <span>{venue.capacity}</span>
                        </div>
                        <div className="flex items-center justify-between text-xs md:text-sm mb-3 md:mb-4">
                          <span className="font-bold text-lg md:text-xl text-primary">
                            {venue.price}
                          </span>
                          <span className="flex items-center gap-1 bg-yellow-50 px-2 py-1 rounded-full ml-2">
                            <Star className="h-3 w-3 md:h-4 md:w-4 fill-yellow-400 text-yellow-400" />
                            <span className="text-xs md:text-sm font-semibold text-gray-900">
                              {venue.rating || "N/A"}
                            </span>
                          </span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </Link>
              ))}
            </div>
          )}

          {/* Traditional Pagination */}
          {!loading && !error && sortedVenues.length > 0 && (
            <div className="flex flex-col items-center mt-16 space-y-4">
              {/* Results info */}
              <div className="text-xs md:text-sm text-gray-600 text-center">
                Showing {startIndex + 1}-
                {Math.min(endIndex, sortedVenues.length)} of{" "}
                {sortedVenues.length} venues
                {hasMoreData && (
                  <span className="ml-2 text-primary">
                    ({venues.length} loaded from server)
                  </span>
                )}
              </div>

              {/* Pagination controls */}
              {totalPages > 1 ? (
                <div className="flex items-center space-x-2">
                  <Button
                    variant="outline"
                    onClick={goToPrevious}
                    disabled={currentPage === 1}
                    className="rounded-full px-3 md:px-4 text-xs md:text-sm"
                  >
                    Previous
                  </Button>

                  {getPageNumbers().map((page) => (
                    <Button
                      key={page}
                      variant={currentPage === page ? "default" : "outline"}
                      onClick={() => goToPage(page)}
                      className={`rounded-full px-3 md:px-4 text-xs md:text-sm ${
                        currentPage === page
                          ? "bg-primary text-white shadow"
                          : "hover:bg-gray-100"
                      }`}
                    >
                      {page}
                    </Button>
                  ))}

                  <Button
                    variant="outline"
                    onClick={goToNext}
                    disabled={currentPage === totalPages}
                    className="rounded-full px-3 md:px-4 text-xs md:text-sm"
                  >
                    Next
                  </Button>
                </div>
              ) : (
                <div className="text-center py-4">
                  <span className="text-sm text-gray-500">
                    Showing all {sortedVenues.length} venues
                  </span>
                </div>
              )}

              {/* Load More Server Data Button */}
              {showLoadMore && (
                <Button
                  onClick={loadMoreVenues}
                  disabled={loading}
                  variant="outline"
                  className="mt-4 px-4 md:px-6 py-2 text-xs md:text-sm"
                >
                  {loading ? (
                    <>
                      <div className="animate-spin rounded-full h-3 w-3 md:h-4 md:w-4 border-b-2 border-primary mr-2"></div>
                      Loading more...
                    </>
                  ) : (
                    "Load More from Server"
                  )}
                </Button>
              )}
            </div>
          )}
        </div>
      </section>

      <Footer />
    </div>
  );
}
