'use client';

import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON>, Bell, Mail, Heart, Tag, Gift, <PERSON>, Per<PERSON>, Clock } from 'lucide-react';
import { Header } from "@/components/header";
import { Footer } from "@/components/footer";
import { TopHeader } from "@/components/top-header";
import NewsletterSubscription from '@/components/newsletter/NewsletterSubscription';

export default function OffersPage() {
  const [timeLeft, setTimeLeft] = useState({
    days: 0,
    hours: 0,
    minutes: 0,
    seconds: 0
  });

  useEffect(() => {
    const launchDate = new Date('2024-12-31T00:00:00').getTime();
    
    const timer = setInterval(() => {
      const now = new Date().getTime();
      const distance = launchDate - now;
      
      if (distance > 0) {
        setTimeLeft({
          days: Math.floor(distance / (1000 * 60 * 60 * 24)),
          hours: Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60)),
          minutes: Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60)),
          seconds: Math.floor((distance % (1000 * 60)) / 1000)
        });
      }
    }, 1000);

    return () => clearInterval(timer);
  }, []);



  return (
    <div className="min-h-screen bg-gradient-to-br from-pink-50 via-purple-50 to-indigo-50">
      <TopHeader />
      <Header />
      
      <main className="pt-20">
        <section className="relative overflow-hidden py-20">
          <div className="absolute inset-0">
            <div className="absolute top-20 left-10 w-20 h-20 bg-pink-200 rounded-full opacity-20 animate-pulse"></div>
            <div className="absolute top-40 right-20 w-16 h-16 bg-purple-200 rounded-full opacity-20 animate-pulse"></div>
            <div className="absolute bottom-20 left-1/4 w-12 h-12 bg-indigo-200 rounded-full opacity-20 animate-pulse"></div>
            <div className="absolute bottom-40 right-1/3 w-24 h-24 bg-pink-200 rounded-full opacity-20 animate-pulse"></div>
          </div>

          <div className="container mx-auto px-4 text-center relative z-10">
            <div className="max-w-4xl mx-auto">
              <div className="inline-flex items-center gap-2 bg-gradient-to-r from-pink-500 to-purple-600 text-white px-6 py-3 rounded-full text-sm font-medium mb-8">
                <Sparkles className="w-4 h-4" />
                Coming Soon
                <Sparkles className="w-4 h-4" />
              </div>

              <h1 className="text-5xl md:text-7xl font-bold bg-gradient-to-r from-pink-600 via-purple-600 to-indigo-600 bg-clip-text text-transparent mb-6">
                Amazing Offers
              </h1>

              <p className="text-xl md:text-2xl text-gray-600 mb-8 leading-relaxed">
                Get ready for incredible deals and exclusive discounts on your dream Celebration Services
              </p>

              <div className="flex justify-center gap-4 mb-12">
                {Object.entries(timeLeft).map(([unit, value]) => (
                  <div key={unit} className="bg-white rounded-xl shadow-lg p-4 min-w-[80px]">
                    <div className="text-2xl md:text-3xl font-bold text-gray-800">{value}</div>
                    <div className="text-sm text-gray-500 capitalize">{unit}</div>
                  </div>
                ))}
              </div>

              <div className="max-w-md mx-auto">
                <NewsletterSubscription
                  source="OFFERS_PAGE"
                  variant="compact"
                  className="[&_button]:bg-gradient-to-r [&_button]:from-pink-500 [&_button]:to-purple-600 [&_button]:text-white [&_button]:hover:from-pink-600 [&_button]:hover:to-purple-700"
                  onSuccess={() => {
                    // Optional: Add any additional success handling
                  }}
                />
              </div>
            </div>
          </div>
        </section>

        <section className="py-20 bg-white">
          <div className="container mx-auto px-4">
            <div className="text-center mb-16">
              <h2 className="text-4xl font-bold text-gray-800 mb-4">What's Coming</h2>
              <p className="text-xl text-gray-600">Sneak peek at the amazing offers we're preparing for you</p>
            </div>

            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
              <div className="bg-gradient-to-br from-white to-gray-50 rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100">
                <div className="bg-gradient-to-r from-pink-500 to-purple-600 text-white w-12 h-12 rounded-lg flex items-center justify-center mb-4">
                  <Heart className="w-6 h-6" />
                </div>
                <h3 className="text-xl font-semibold text-gray-800 mb-2">Wedding Package Deals</h3>
                <p className="text-gray-600 mb-4">Exclusive bundles for venues, photography, and catering</p>
                <div className="bg-gradient-to-r from-pink-100 to-purple-100 text-purple-700 px-3 py-1 rounded-full text-sm font-medium inline-block">
                  Up to 30% OFF
                </div>
              </div>

              <div className="bg-gradient-to-br from-white to-gray-50 rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100">
                <div className="bg-gradient-to-r from-pink-500 to-purple-600 text-white w-12 h-12 rounded-lg flex items-center justify-center mb-4">
                  <Tag className="w-6 h-6" />
                </div>
                <h3 className="text-xl font-semibold text-gray-800 mb-2">Early Bird Discounts</h3>
                <p className="text-gray-600 mb-4">Special rates for bookings made 6+ months in advance</p>
                <div className="bg-gradient-to-r from-pink-100 to-purple-100 text-purple-700 px-3 py-1 rounded-full text-sm font-medium inline-block">
                  15% OFF
                </div>
              </div>

              <div className="bg-gradient-to-br from-white to-gray-50 rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100">
                <div className="bg-gradient-to-r from-pink-500 to-purple-600 text-white w-12 h-12 rounded-lg flex items-center justify-center mb-4">
                  <Sparkles className="w-6 h-6" />
                </div>
                <h3 className="text-xl font-semibold text-gray-800 mb-2">Seasonal Promotions</h3>
                <p className="text-gray-600 mb-4">Limited-time offers for off-season weddings</p>
                <div className="bg-gradient-to-r from-pink-100 to-purple-100 text-purple-700 px-3 py-1 rounded-full text-sm font-medium inline-block">
                  25% OFF
                </div>
              </div>

              <div className="bg-gradient-to-br from-white to-gray-50 rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100">
                <div className="bg-gradient-to-r from-pink-500 to-purple-600 text-white w-12 h-12 rounded-lg flex items-center justify-center mb-4">
                  <Gift className="w-6 h-6" />
                </div>
                <h3 className="text-xl font-semibold text-gray-800 mb-2">Loyalty Rewards</h3>
                <p className="text-gray-600 mb-4">Cashback and rewards for repeat customers</p>
                <div className="bg-gradient-to-r from-pink-100 to-purple-100 text-purple-700 px-3 py-1 rounded-full text-sm font-medium inline-block">
                  Cashback
                </div>
              </div>
            </div>
          </div>
        </section>

        <section className="py-20 bg-gradient-to-r from-pink-50 to-purple-50">
          <div className="container mx-auto px-4 text-center">
            <div className="max-w-3xl mx-auto">
              <h2 className="text-4xl font-bold text-gray-800 mb-6">Why Wait for Our Offers?</h2>
              <div className="grid md:grid-cols-3 gap-8 mt-12">
                <div className="text-center">
                  <div className="bg-white w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4 shadow-lg">
                    <Percent className="w-8 h-8 text-pink-500" />
                  </div>
                  <h3 className="text-xl font-semibold mb-2">Best Prices</h3>
                  <p className="text-gray-600">Guaranteed lowest prices on Festive Services</p>
                </div>
                <div className="text-center">
                  <div className="bg-white w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4 shadow-lg">
                    <Star className="w-8 h-8 text-purple-500" />
                  </div>
                  <h3 className="text-xl font-semibold mb-2">Premium Quality</h3>
                  <p className="text-gray-600">Only verified and top-rated vendors</p>
                </div>
                <div className="text-center">
                  <div className="bg-white w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4 shadow-lg">
                    <Clock className="w-8 h-8 text-indigo-500" />
                  </div>
                  <h3 className="text-xl font-semibold mb-2">Limited Time</h3>
                  <p className="text-gray-600">Exclusive deals for early subscribers</p>
                </div>
              </div>
            </div>
          </div>
        </section>
      </main>

      <Footer />
    </div>
  );
}
