// Simulated OTP service for testing
// In production, this would integrate with AWS SNS for SMS and SES for email

interface OTPData {
  code: string;
  expiresAt: number;
  attempts: number;
}

class OTPService {
  private otpStore: Map<string, OTPData> = new Map();
  private readonly OTP_EXPIRY_TIME = 5 * 60 * 1000; // 5 minutes
  private readonly MAX_ATTEMPTS = 3;

  generateOTP(): string {
    return Math.floor(100000 + Math.random() * 900000).toString();
  }

  async sendOTP(emailOrPhone: string): Promise<{ success: boolean; message: string }> {
    try {
      const code = this.generateOTP();
      const expiresAt = Date.now() + this.OTP_EXPIRY_TIME;
      
      this.otpStore.set(emailOrPhone, {
        code,
        expiresAt,
        attempts: 0
      });

      // Simulate sending OTP
      console.log(`OTP for ${emailOrPhone}: ${code}`);
      
      // In production, you would:
      // - Use AWS SNS to send SMS for phone numbers
      // - Use AWS SES to send email for email addresses
      
      return {
        success: true,
        message: `OTP sent to ${emailOrPhone}`
      };
    } catch (error) {
      return {
        success: false,
        message: 'Failed to send OTP'
      };
    }
  }

  verifyOTP(emailOrPhone: string, inputCode: string): { success: boolean; message: string } {
    const otpData = this.otpStore.get(emailOrPhone);
    
    if (!otpData) {
      return {
        success: false,
        message: 'No OTP found. Please request a new one.'
      };
    }

    if (Date.now() > otpData.expiresAt) {
      this.otpStore.delete(emailOrPhone);
      return {
        success: false,
        message: 'OTP has expired. Please request a new one.'
      };
    }

    if (otpData.attempts >= this.MAX_ATTEMPTS) {
      this.otpStore.delete(emailOrPhone);
      return {
        success: false,
        message: 'Too many attempts. Please request a new OTP.'
      };
    }

    otpData.attempts++;

    if (otpData.code !== inputCode) {
      return {
        success: false,
        message: `Invalid OTP. ${this.MAX_ATTEMPTS - otpData.attempts} attempts remaining.`
      };
    }

    // OTP verified successfully
    this.otpStore.delete(emailOrPhone);
    return {
      success: true,
      message: 'OTP verified successfully'
    };
  }

  clearExpiredOTPs(): void {
    const now = Date.now();
    for (const [key, value] of this.otpStore.entries()) {
      if (now > value.expiresAt) {
        this.otpStore.delete(key);
      }
    }
  }
}

export const otpService = new OTPService();

// Clean up expired OTPs every minute - only in browser environment
if (typeof window !== 'undefined') {
  const cleanupInterval = setInterval(() => {
    otpService.clearExpiredOTPs();
  }, 60000);

  // Clean up interval when page unloads
  if (typeof window !== 'undefined') {
    window.addEventListener('beforeunload', () => {
      clearInterval(cleanupInterval);
    });
  }
}
