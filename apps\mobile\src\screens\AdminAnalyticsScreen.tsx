import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  RefreshControl,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { useTheme } from '../providers/ThemeProvider';
import { useAuth } from '../providers/AuthProvider';
import LoadingSpinner from '../components/LoadingSpinner';
import LineChart from '../components/charts/LineChart';
import Pie<PERSON><PERSON> from '../components/charts/PieChart';
import BarChart from '../components/charts/BarChart';
import {
  analyticsService,
  TIMEFRAMES,
  type AdminAnalytics,
  type AnalyticsTimeframe,
} from '../services/analyticsService';

export default function AdminAnalyticsScreen() {
  const navigation = useNavigation();
  const { theme } = useTheme();
  const { user } = useAuth();
  
  const [analytics, setAnalytics] = useState<AdminAnalytics | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [selectedTimeframe, setSelectedTimeframe] = useState<AnalyticsTimeframe['value']>('30d');

  useEffect(() => {
    loadAnalytics();
  }, [selectedTimeframe]);

  const loadAnalytics = async () => {
    try {
      setLoading(true);
      const data = await analyticsService.getAdminAnalytics(selectedTimeframe);
      setAnalytics(data);
    } catch (error) {
      console.error('Failed to load analytics:', error);
      Alert.alert('Error', 'Failed to load analytics data');
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadAnalytics();
    setRefreshing(false);
  };

  const renderTimeframeSelector = () => (
    <View style={styles.timeframeContainer}>
      <ScrollView horizontal showsHorizontalScrollIndicator={false}>
        {TIMEFRAMES.map((timeframe) => (
          <TouchableOpacity
            key={timeframe.value}
            style={[
              styles.timeframeButton,
              {
                backgroundColor: selectedTimeframe === timeframe.value 
                  ? theme.colors.primary 
                  : theme.colors.surface,
                borderColor: theme.colors.border,
              },
            ]}
            onPress={() => setSelectedTimeframe(timeframe.value)}
          >
            <Text
              style={[
                styles.timeframeText,
                {
                  color: selectedTimeframe === timeframe.value 
                    ? 'white' 
                    : theme.colors.text,
                },
              ]}
            >
              {timeframe.label}
            </Text>
          </TouchableOpacity>
        ))}
      </ScrollView>
    </View>
  );

  const renderOverviewCards = () => {
    if (!analytics) return null;

    const cards = [
      {
        title: 'Total Users',
        value: analyticsService.formatNumber(analytics.overview.totalUsers),
        growth: analytics.overview.userGrowth,
        icon: 'people-outline',
        color: theme.colors.primary,
      },
      {
        title: 'Total Vendors',
        value: analyticsService.formatNumber(analytics.overview.totalVendors),
        growth: analytics.overview.vendorGrowth,
        icon: 'business-outline',
        color: theme.colors.success,
      },
      {
        title: 'Total Orders',
        value: analyticsService.formatNumber(analytics.overview.totalOrders),
        growth: analytics.overview.orderGrowth,
        icon: 'receipt-outline',
        color: theme.colors.info,
      },
      {
        title: 'Total Revenue',
        value: analyticsService.formatCurrency(analytics.overview.totalRevenue),
        growth: analytics.overview.monthlyGrowth,
        icon: 'trending-up-outline',
        color: theme.colors.warning,
      },
    ];

    return (
      <View style={styles.overviewContainer}>
        {cards.map((card, index) => (
          <View
            key={index}
            style={[styles.overviewCard, { backgroundColor: theme.colors.surface }]}
          >
            <View style={styles.cardHeader}>
              <Ionicons name={card.icon as any} size={24} color={card.color} />
              <View style={[styles.growthBadge, { 
                backgroundColor: card.growth >= 0 ? '#e8f5e8' : '#ffeaea' 
              }]}>
                <Ionicons 
                  name={card.growth >= 0 ? 'trending-up' : 'trending-down'} 
                  size={12} 
                  color={card.growth >= 0 ? '#4caf50' : '#f44336'} 
                />
                <Text style={[styles.growthText, { 
                  color: card.growth >= 0 ? '#4caf50' : '#f44336' 
                }]}>
                  {analyticsService.formatPercentage(Math.abs(card.growth))}
                </Text>
              </View>
            </View>
            <Text style={[styles.cardValue, { color: theme.colors.text }]}>
              {card.value}
            </Text>
            <Text style={[styles.cardTitle, { color: theme.colors.textSecondary }]}>
              {card.title}
            </Text>
          </View>
        ))}
      </View>
    );
  };

  const renderInsights = () => {
    if (!analytics) return null;

    const insights = [
      {
        title: 'Peak Order Day',
        value: analytics.insights.peakOrderDay,
        icon: 'calendar-outline',
      },
      {
        title: 'Top Category',
        value: analytics.insights.topPerformingCategory,
        icon: 'trophy-outline',
      },
      {
        title: 'Avg Order Value',
        value: analyticsService.formatCurrency(analytics.insights.averageOrderValue),
        icon: 'cash-outline',
      },
      {
        title: 'Customer Retention',
        value: analyticsService.formatPercentage(analytics.insights.customerRetentionRate),
        icon: 'heart-outline',
      },
      {
        title: 'Vendor Approval Rate',
        value: analyticsService.formatPercentage(analytics.insights.vendorApprovalRate),
        icon: 'checkmark-circle-outline',
      },
      {
        title: 'Platform Utilization',
        value: analyticsService.formatPercentage(analytics.insights.platformUtilization),
        icon: 'speedometer-outline',
      },
    ];

    return (
      <View style={[styles.insightsContainer, { backgroundColor: theme.colors.surface }]}>
        <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
          Key Insights
        </Text>
        <View style={styles.insightsGrid}>
          {insights.map((insight, index) => (
            <View key={index} style={styles.insightItem}>
              <Ionicons 
                name={insight.icon as any} 
                size={20} 
                color={theme.colors.primary} 
              />
              <Text style={[styles.insightValue, { color: theme.colors.text }]}>
                {insight.value}
              </Text>
              <Text style={[styles.insightTitle, { color: theme.colors.textSecondary }]}>
                {insight.title}
              </Text>
            </View>
          ))}
        </View>
      </View>
    );
  };

  if (loading) {
    return <LoadingSpinner fullScreen text="Loading analytics..." />;
  }

  return (
    <ScrollView 
      style={[styles.container, { backgroundColor: theme.colors.background }]}
      refreshControl={
        <RefreshControl
          refreshing={refreshing}
          onRefresh={onRefresh}
          colors={[theme.colors.primary]}
          tintColor={theme.colors.primary}
        />
      }
    >
      {/* Header */}
      <View style={[styles.header, { backgroundColor: theme.colors.primary }]}>
        <TouchableOpacity 
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color="white" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Platform Analytics</Text>
        <View style={styles.headerSpacer} />
      </View>

      {/* Timeframe Selector */}
      {renderTimeframeSelector()}

      {/* Overview Cards */}
      {renderOverviewCards()}

      {/* Charts */}
      {analytics && (
        <>
          <LineChart
            data={analytics.charts.userRegistrations}
            title="User Registrations"
            color={theme.colors.primary}
            showValues
            formatValue={analyticsService.formatNumber}
          />

          <BarChart
            data={analytics.charts.orderVolume}
            title="Order Volume"
            color={theme.colors.success}
            showValues
            formatValue={analyticsService.formatNumber}
          />

          <LineChart
            data={analytics.charts.revenue}
            title="Revenue Trend"
            color={theme.colors.warning}
            showValues
            formatValue={analyticsService.formatCurrency}
          />

          <PieChart
            data={analytics.charts.topCategories}
            title="Top Categories"
            showLegend
            showPercentages
          />

          <PieChart
            data={analytics.charts.topCities}
            title="Top Cities"
            showLegend
            showPercentages
          />

          <PieChart
            data={analytics.charts.orderStatus}
            title="Order Status Distribution"
            showLegend
            showPercentages
          />
        </>
      )}

      {/* Insights */}
      {renderInsights()}

      <View style={styles.bottomSpacer} />
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingTop: 50,
    paddingBottom: 16,
    paddingHorizontal: 16,
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    flex: 1,
    fontSize: 20,
    fontWeight: '600',
    color: 'white',
    textAlign: 'center',
  },
  headerSpacer: {
    width: 40,
  },
  timeframeContainer: {
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  timeframeButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    marginRight: 8,
    borderWidth: 1,
  },
  timeframeText: {
    fontSize: 14,
    fontWeight: '500',
  },
  overviewContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    paddingHorizontal: 16,
    marginBottom: 16,
  },
  overviewCard: {
    width: '48%',
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
    marginRight: '2%',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  growthBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 10,
  },
  growthText: {
    fontSize: 10,
    fontWeight: '600',
    marginLeft: 2,
  },
  cardValue: {
    fontSize: 20,
    fontWeight: '700',
    marginBottom: 4,
  },
  cardTitle: {
    fontSize: 12,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
  },
  insightsContainer: {
    margin: 16,
    padding: 16,
    borderRadius: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  insightsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  insightItem: {
    width: '48%',
    alignItems: 'center',
    marginBottom: 16,
  },
  insightValue: {
    fontSize: 16,
    fontWeight: '600',
    marginTop: 8,
    marginBottom: 4,
  },
  insightTitle: {
    fontSize: 12,
    textAlign: 'center',
  },
  bottomSpacer: {
    height: 32,
  },
});
