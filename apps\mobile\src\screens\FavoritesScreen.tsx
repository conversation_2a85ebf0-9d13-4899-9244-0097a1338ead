import React, { useState } from 'react';
import { View, Text, ScrollView, StyleSheet, TouchableOpacity, FlatList } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../providers/ThemeProvider';

interface FavoriteItem {
  id: string;
  name: string;
  type: 'vendor' | 'venue' | 'product';
  image: string;
  location?: string;
  price?: number;
  rating: number;
}

const mockFavorites: FavoriteItem[] = [
  {
    id: '1',
    name: 'Royal Photography',
    type: 'vendor',
    image: '/placeholder-image.jpg
    location: 'Chennai, Tamil Nadu',
    rating: 4.8,
  },
  {
    id: '2',
    name: 'Grand Palace Hotel',
    type: 'venue',
    image: '/placeholder-image.jpg
    location: 'Mumbai, Maharashtra',
    rating: 4.6,
  },
  {
    id: '3',
    name: 'Designer Bridal Lehenga',
    type: 'product',
    image: '/placeholder-image.jpg
    price: 45000,
    rating: 4.7,
  },
];

const categories = ['All', 'Vendors', 'Venues', 'Products'];

export default function FavoritesScreen() {
  const { theme } = useTheme();
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [favorites] = useState(mockFavorites);

  const filteredFavorites = favorites.filter(item => {
    if (selectedCategory === 'All') return true;
    return selectedCategory.toLowerCase().includes(item.type);
  });

  const renderFavoriteItem = ({ item }: { item: FavoriteItem }) => (
    <TouchableOpacity style={[styles.favoriteCard, { backgroundColor: theme.colors.surface }]}>
      <View style={styles.favoriteImage} />
      
      <View style={styles.favoriteInfo}>
        <View style={styles.favoriteHeader}>
          <Text style={[styles.favoriteName, { color: theme.colors.text }]} numberOfLines={1}>
            {item.name}
          </Text>
          <TouchableOpacity style={styles.removeButton}>
            <Ionicons name="heart" size={20} color="#EF4444" />
          </TouchableOpacity>
        </View>
        
        <View style={styles.favoriteDetails}>
          <Ionicons 
            name={item.type === 'vendor' ? 'people' : item.type === 'venue' ? 'location' : 'bag'} 
            size={14} 
            color={theme.colors.textSecondary} 
          />
          <Text style={[styles.favoriteType, { color: theme.colors.textSecondary }]}>
            {item.type.charAt(0).toUpperCase() + item.type.slice(1)}
          </Text>
        </View>
        
        {item.location && (
          <Text style={[styles.favoriteLocation, { color: theme.colors.textSecondary }]}>
            {item.location}
          </Text>
        )}
        
        {item.price && (
          <Text style={[styles.favoritePrice, { color: theme.colors.primary }]}>
            ₹{item.price.toLocaleString()}
          </Text>
        )}
        
        <View style={styles.ratingContainer}>
          <Ionicons name="star" size={14} color="#F59E0B" />
          <Text style={[styles.rating, { color: theme.colors.text }]}>
            {item.rating}
          </Text>
        </View>
      </View>
    </TouchableOpacity>
  );

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={[styles.title, { color: theme.colors.text }]}>
          My Favorites
        </Text>
        <Text style={[styles.subtitle, { color: theme.colors.textSecondary }]}>
          {filteredFavorites.length} saved items
        </Text>
      </View>

      {/* Categories */}
      <ScrollView 
        horizontal 
        showsHorizontalScrollIndicator={false}
        style={styles.categoriesContainer}
        contentContainerStyle={styles.categoriesContent}
      >
        {categories.map((category) => (
          <TouchableOpacity
            key={category}
            style={[
              styles.categoryChip,
              {
                backgroundColor: selectedCategory === category 
                  ? theme.colors.primary 
                  : theme.colors.surface,
              }
            ]}
            onPress={() => setSelectedCategory(category)}
          >
            <Text style={[
              styles.categoryText,
              {
                color: selectedCategory === category 
                  ? 'white' 
                  : theme.colors.text,
              }
            ]}>
              {category}
            </Text>
          </TouchableOpacity>
        ))}
      </ScrollView>

      {/* Favorites List */}
      {filteredFavorites.length > 0 ? (
        <FlatList
          data={filteredFavorites}
          renderItem={renderFavoriteItem}
          keyExtractor={(item) => item.id}
          contentContainerStyle={styles.favoritesContainer}
          showsVerticalScrollIndicator={false}
        />
      ) : (
        <View style={styles.emptyState}>
          <Ionicons name="heart-outline" size={64} color={theme.colors.textSecondary} />
          <Text style={[styles.emptyTitle, { color: theme.colors.text }]}>
            No favorites yet
          </Text>
          <Text style={[styles.emptySubtitle, { color: theme.colors.textSecondary }]}>
            Start adding vendors, venues, and products to your favorites
          </Text>
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    padding: 16,
    paddingBottom: 8,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 14,
  },
  categoriesContainer: {
    marginBottom: 16,
  },
  categoriesContent: {
    paddingHorizontal: 16,
    gap: 8,
  },
  categoryChip: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
  },
  categoryText: {
    fontSize: 14,
    fontWeight: '500',
  },
  favoritesContainer: {
    paddingHorizontal: 16,
    paddingBottom: 20,
  },
  favoriteCard: {
    flexDirection: 'row',
    padding: 12,
    borderRadius: 12,
    marginBottom: 12,
  },
  favoriteImage: {
    width: 80,
    height: 80,
    backgroundColor: '#E5E7EB',
    borderRadius: 8,
    marginRight: 12,
  },
  favoriteInfo: {
    flex: 1,
    gap: 4,
  },
  favoriteHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  favoriteName: {
    fontSize: 16,
    fontWeight: '600',
    flex: 1,
    marginRight: 8,
  },
  removeButton: {
    padding: 4,
  },
  favoriteDetails: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  favoriteType: {
    fontSize: 12,
    textTransform: 'capitalize',
  },
  favoriteLocation: {
    fontSize: 14,
  },
  favoritePrice: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  rating: {
    fontSize: 14,
    fontWeight: '500',
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginTop: 16,
    marginBottom: 8,
  },
  emptySubtitle: {
    fontSize: 16,
    textAlign: 'center',
    lineHeight: 24,
  },
});
