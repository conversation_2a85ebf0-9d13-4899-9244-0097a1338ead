'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { 
  Upload, 
  Download, 
  CheckCircle, 
  AlertCircle, 
  Loader2,
  Database,
  Users,
  Calculator,
  X
} from 'lucide-react'
import { useAuth } from '@/contexts/AuthContext'
import { toast } from 'sonner'
import simplePlanningService from '@/lib/services/simplePlanningService'

interface MigrationData {
  hasBudgetData: boolean
  hasGuestData: boolean
  hasAnyData: boolean
  budgetCount: number
  guestCount: number
}

interface MigrationResult {
  success: boolean
  message: string
  migratedItems: number
  errors: string[]
}

export default function PlanningDataMigration() {
  const [mounted, setMounted] = useState(false)
  const { user } = useAuth()
  const [migrationData, setMigrationData] = useState<MigrationData | null>(null)
  const [migrating, setMigrating] = useState(false)
  const [migrationProgress, setMigrationProgress] = useState(0)
  const [migrationResults, setMigrationResults] = useState<{
    budget: MigrationResult | null
    guestList: MigrationResult | null
  }>({ budget: null, guestList: null })
  const [showComponent, setShowComponent] = useState(false)

  // Prevent SSR issues
  useEffect(() => {
    setMounted(true)
  }, [])

  // Check for migration data on component mount
  useEffect(() => {
    if (mounted) {
      checkMigrationData()
    }
  }, [mounted])

  const checkMigrationData = () => {
    const budgetData = localStorage.getItem('wedding-budgets')
    const guestData = localStorage.getItem('wedding-guests')

    let budgetCount = 0
    let guestCount = 0

    try {
      if (budgetData) {
        const budgets = JSON.parse(budgetData)
        budgetCount = Array.isArray(budgets) ? budgets.length : 0
      }
    } catch (error) {
      console.error('Error parsing budget data:', error)
    }

    try {
      if (guestData) {
        const guests = JSON.parse(guestData)
        guestCount = Array.isArray(guests) ? guests.length : 0
      }
    } catch (error) {
      console.error('Error parsing guest data:', error)
    }

    const data: MigrationData = {
      hasBudgetData: budgetCount > 0,
      hasGuestData: guestCount > 0,
      hasAnyData: budgetCount > 0 || guestCount > 0,
      budgetCount,
      guestCount
    }

    setMigrationData(data)
    setShowComponent(data.hasAnyData)
  }

  const migrateBudgetData = async (): Promise<MigrationResult> => {
    try {
      const budgetData = localStorage.getItem('wedding-budgets')
      
      if (!budgetData) {
        return {
          success: true,
          message: 'No budget data found',
          migratedItems: 0,
          errors: []
        }
      }

      const budgets = JSON.parse(budgetData)
      
      if (!Array.isArray(budgets) || budgets.length === 0) {
        return {
          success: true,
          message: 'No valid budget data to migrate',
          migratedItems: 0,
          errors: []
        }
      }

      const migratedBudgets = await simplePlanningService.migrateBudgetFromLocalStorage(user!.userId, budgets)

      // Clear localStorage after successful migration
      localStorage.removeItem('wedding-budgets')

      return {
        success: true,
        message: `Successfully migrated ${migratedBudgets.length} budget(s)`,
        migratedItems: migratedBudgets.length,
        errors: []
      }

    } catch (error) {
      console.error('Error migrating budget data:', error)
      return {
        success: false,
        message: 'Failed to migrate budget data',
        migratedItems: 0,
        errors: [error instanceof Error ? error.message : 'Unknown error']
      }
    }
  }

  const migrateGuestListData = async (): Promise<MigrationResult> => {
    try {
      const guestData = localStorage.getItem('wedding-guests')
      
      if (!guestData) {
        return {
          success: true,
          message: 'No guest list data found',
          migratedItems: 0,
          errors: []
        }
      }

      const guests = JSON.parse(guestData)
      
      if (!Array.isArray(guests) || guests.length === 0) {
        return {
          success: true,
          message: 'No valid guest data to migrate',
          migratedItems: 0,
          errors: []
        }
      }

      await simplePlanningService.migrateGuestListFromLocalStorage(user!.userId, guests)

      // Clear localStorage after successful migration
      localStorage.removeItem('wedding-guests')

      return {
        success: true,
        message: `Successfully migrated guest list with ${guests.length} guest(s)`,
        migratedItems: guests.length,
        errors: []
      }

    } catch (error) {
      console.error('Error migrating guest list data:', error)
      return {
        success: false,
        message: 'Failed to migrate guest list data',
        migratedItems: 0,
        errors: [error instanceof Error ? error.message : 'Unknown error']
      }
    }
  }

  const handleMigration = async () => {
    if (!user?.userId || !migrationData) return

    setMigrating(true)
    setMigrationProgress(0)

    try {
      // Step 1: Migrate budget data
      setMigrationProgress(25)
      const budgetResult = await migrateBudgetData()
      setMigrationResults(prev => ({ ...prev, budget: budgetResult }))

      // Step 2: Migrate guest list data
      setMigrationProgress(75)
      const guestListResult = await migrateGuestListData()
      setMigrationResults(prev => ({ ...prev, guestList: guestListResult }))

      setMigrationProgress(100)

      // Show results
      const totalMigrated = budgetResult.migratedItems + guestListResult.migratedItems
      const totalErrors = budgetResult.errors.length + guestListResult.errors.length

      if (totalMigrated > 0) {
        toast.success(`Successfully migrated ${totalMigrated} items to your account!`)
      }

      if (totalErrors > 0) {
        toast.error(`Migration completed with ${totalErrors} errors`)
      }

      // Hide component after successful migration
      setTimeout(() => {
        setShowComponent(false)
      }, 3000)

    } catch (error) {
      console.error('Migration failed:', error)
      toast.error('Migration failed. Please try again.')
    } finally {
      setMigrating(false)
    }
  }

  const handleSkip = () => {
    setShowComponent(false)
    toast.info('Migration skipped. Your data remains in local storage.')
  }

  if (!mounted || !user || !migrationData?.hasAnyData || !showComponent) {
    return null
  }

  return (
    <Card className="border-blue-200 bg-blue-50 mb-6">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Database className="h-5 w-5 text-blue-600" />
            <CardTitle className="text-blue-800">Save Your Planning Data</CardTitle>
          </div>
          <Button
            onClick={() => setShowComponent(false)}
            variant="ghost"
            size="sm"
            className="h-6 w-6 p-0"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>
        <CardDescription className="text-blue-700">
          We found existing planning data in your browser. Save it to your account for better access across devices.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Data Summary */}
        <div className="grid grid-cols-2 gap-4">
          {migrationData.hasBudgetData && (
            <div className="flex items-center gap-2 p-3 bg-white rounded-lg">
              <Calculator className="h-4 w-4 text-green-600" />
              <div>
                <p className="text-sm font-medium">{migrationData.budgetCount} Budget(s)</p>
                <p className="text-xs text-gray-600">Ready to migrate</p>
              </div>
            </div>
          )}
          
          {migrationData.hasGuestData && (
            <div className="flex items-center gap-2 p-3 bg-white rounded-lg">
              <Users className="h-4 w-4 text-blue-600" />
              <div>
                <p className="text-sm font-medium">{migrationData.guestCount} Guest(s)</p>
                <p className="text-xs text-gray-600">Ready to migrate</p>
              </div>
            </div>
          )}
        </div>

        {/* Migration Progress */}
        {migrating && (
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <Loader2 className="h-4 w-4 animate-spin" />
              <span className="text-sm">Migrating your data...</span>
            </div>
            <Progress value={migrationProgress} className="w-full" />
          </div>
        )}

        {/* Migration Results */}
        {migrationResults.budget && (
          <Alert className={migrationResults.budget.success ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'}>
            <CheckCircle className="h-4 w-4" />
            <AlertDescription>
              Budget Migration: {migrationResults.budget.message}
            </AlertDescription>
          </Alert>
        )}

        {migrationResults.guestList && (
          <Alert className={migrationResults.guestList.success ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'}>
            <CheckCircle className="h-4 w-4" />
            <AlertDescription>
              Guest List Migration: {migrationResults.guestList.message}
            </AlertDescription>
          </Alert>
        )}

        {/* Action Buttons */}
        <div className="flex gap-3">
          <Button 
            onClick={handleMigration} 
            disabled={migrating}
            className="flex items-center gap-2"
          >
            {migrating ? (
              <>
                <Loader2 className="h-4 w-4 animate-spin" />
                Migrating...
              </>
            ) : (
              <>
                <Upload className="h-4 w-4" />
                Save to Account
              </>
            )}
          </Button>
        </div>

        <p className="text-xs text-blue-600">
          💡 Saving to your account allows access from any device and prevents data loss.
        </p>
      </CardContent>
    </Card>
  )
}
