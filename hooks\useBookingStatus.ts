"use client"

import { useState, useEffect, useCallback } from 'react'
import { BookingService } from '@/lib/services/bookingService'
import { useAuth } from '@/contexts/AuthContext'

export interface BookingStatusResult {
  canBook: boolean
  reason: 'available' | 'pending_confirmation' | 'already_confirmed' | 'date_unavailable' | 'not_authenticated' | 'error'
  message: string
  bookingId?: string
  status?: string
  loading: boolean
  error?: string
  conflictingBookings?: any[]
}

export interface UseBookingStatusProps {
  entityId: string
  entityType: 'VENDOR' | 'VENUE'
  eventDate?: string
  autoCheck?: boolean // Whether to automatically check on mount
}

export function useBookingStatus({
  entityId,
  entityType,
  eventDate,
  autoCheck = true
}: UseBookingStatusProps) {
  const { isAuthenticated, user } = useAuth()
  const [bookingStatus, setBookingStatus] = useState<BookingStatusResult>({
    canBook: true,
    reason: 'available',
    message: 'Available for booking',
    loading: false
  })

  const checkBookingStatus = useCallback(async (dateToCheck?: string) => {
    const checkDate = dateToCheck || eventDate
    
    if (!checkDate) {
      setBookingStatus({
        canBook: true,
        reason: 'available',
        message: 'Please select a date',
        loading: false
      })
      return
    }

    if (!entityId) {
      setBookingStatus({
        canBook: false,
        reason: 'error',
        message: 'Invalid entity',
        loading: false,
        error: 'Entity ID is required'
      })
      return
    }

    setBookingStatus(prev => ({ ...prev, loading: true }))

    try {
      let result

      if (isAuthenticated && user?.userId) {
        // For authenticated users, check user-specific booking status
        result = await BookingService.canUserBook(
          entityId,
          entityType,
          checkDate,
          user.userId
        )

        setBookingStatus({
          canBook: result.canBook,
          reason: result.reason as any,
          message: result.message,
          bookingId: result.bookingId,
          status: result.status,
          loading: false,
          conflictingBookings: result.conflictingBookings
        })
      } else {
        // For non-authenticated users, check general availability
        const generalResult = await BookingService.checkGeneralAvailability(
          entityId,
          entityType,
          checkDate
        )

        setBookingStatus({
          canBook: generalResult.available,
          reason: generalResult.available ? 'available' : 'date_unavailable',
          message: generalResult.message,
          loading: false,
          conflictingBookings: generalResult.conflictingBookings
        })
      }

    } catch (error) {
      console.error('Error checking booking status:', error)

      // Provide user-friendly error message
      let userMessage = 'Unable to check availability. Please try again.'
      let reason: any = 'error'

      if (error instanceof Error) {
        if (error.message.includes('UnauthorizedException') || error.message.includes('Unauthorized')) {
          userMessage = 'Please login to check booking status. You can still proceed with booking.'
          reason = 'auth_error'
          console.error('Authentication error detected:', error.message)
        } else if (error.message.includes('ModelBookingStatusInput') || error.message.includes('input object type')) {
          userMessage = 'Booking system is being updated. You can still proceed with booking.'
          reason = 'schema_error'
          console.error('GraphQL schema issue detected:', error.message)
        } else if (error.message.includes('Network') || error.message.includes('fetch')) {
          userMessage = 'Network error. Please check your connection and try again.'
          reason = 'network_error'
        }
      }

      setBookingStatus({
        canBook: true, // Allow booking on error to not block users
        reason: reason,
        message: userMessage,
        loading: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      })
    }
  }, [entityId, entityType, eventDate, user?.userId])

  // Auto-check on mount and when dependencies change
  useEffect(() => {
    if (autoCheck && eventDate) {
      checkBookingStatus()
    }
  }, [autoCheck, eventDate, checkBookingStatus])

  // Manual refresh function
  const refreshStatus = useCallback((dateToCheck?: string) => {
    checkBookingStatus(dateToCheck)
  }, [checkBookingStatus])

  // Cancel booking function
  const cancelBooking = useCallback(async (bookingId: string, reason?: string) => {
    if (!bookingId) {
      throw new Error('Booking ID is required')
    }

    setBookingStatus(prev => ({ ...prev, loading: true }))

    try {
      const result = await BookingService.cancelUserBooking(bookingId, reason)
      
      if (result.success) {
        // Refresh status after successful cancellation
        await checkBookingStatus()
        return result
      } else {
        throw new Error(result.message)
      }

    } catch (error) {
      setBookingStatus(prev => ({ ...prev, loading: false }))
      throw error
    }
  }, [checkBookingStatus])

  // Get button text based on status
  const getButtonText = useCallback(() => {
    if (bookingStatus.loading) {
      return 'Checking...'
    }

    switch (bookingStatus.reason) {
      case 'not_authenticated':
        return 'Login to Book'
      case 'pending_confirmation':
        return 'Pending Confirmation'
      case 'already_confirmed':
        return 'Already Booked'
      case 'date_unavailable':
        return 'Date Unavailable'
      case 'error':
        return 'Book Now'
      case 'available':
      default:
        return 'Book Now'
    }
  }, [bookingStatus])

  // Get button variant based on status
  const getButtonVariant = useCallback(() => {
    switch (bookingStatus.reason) {
      case 'pending_confirmation':
        return 'secondary'
      case 'already_confirmed':
        return 'outline'
      case 'date_unavailable':
        return 'destructive'
      case 'not_authenticated':
        return 'default'
      case 'available':
      case 'error':
      default:
        return 'default'
    }
  }, [bookingStatus])

  // Check if button should be disabled
  const isButtonDisabled = useCallback(() => {
    return !bookingStatus.canBook || bookingStatus.loading
  }, [bookingStatus])

  return {
    ...bookingStatus,
    checkBookingStatus: refreshStatus,
    cancelBooking,
    getButtonText,
    getButtonVariant,
    isButtonDisabled,
    isAuthenticated
  }
}

export default useBookingStatus
