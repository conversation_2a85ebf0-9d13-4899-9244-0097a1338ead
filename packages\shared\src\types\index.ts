// User Types
export interface User {
  id: string;
  email: string;
  fullName: string;
  phone?: string;
  isAdmin?: boolean;
  isSuperAdmin?: boolean;
  role?: string;
  permissions?: string[];
  createdAt: string;
  updatedAt: string;
}

export interface UserProfile extends User {
  avatar?: string;
  dateOfBirth?: string;
  address?: string;
  city?: string;
  state?: string;
  pincode?: string;
  weddingDate?: string;
  partnerName?: string;
  budget?: number;
  preferences?: string[];
}

// Cart Types
export interface CartItem {
  id: number | string;
  name: string;
  price: number;
  quantity: number;
  image?: string;
  vendorId?: string;
  vendorName?: string;
  category?: string;
  description?: string;
  specifications?: Record<string, any>;
}

export interface Cart {
  items: CartItem[];
  total: number;
  itemCount: number;
  lastUpdated: string;
}

// Vendor Types
export interface Vendor {
  id: string;
  name: string;
  email: string;
  phone: string;
  businessType: string;
  description: string;
  address: string;
  city: string;
  state: string;
  pincode: string;
  images: string[];
  rating: number;
  reviewCount: number;
  verified: boolean;
  featured: boolean;
  priceRange: {
    min: number;
    max: number;
  };
  services: string[];
  socialMedia?: {
    website?: string;
    instagram?: string;
    facebook?: string;
    youtube?: string;
  };
  createdAt: string;
  updatedAt: string;
}

// Venue Types
export interface Venue {
  id: string;
  name: string;
  description: string;
  address: string;
  city: string;
  state: string;
  pincode: string;
  images: string[];
  capacity: {
    min: number;
    max: number;
  };
  priceRange: {
    min: number;
    max: number;
  };
  amenities: string[];
  venueType: string;
  rating: number;
  reviewCount: number;
  verified: boolean;
  featured: boolean;
  availability: string[];
  contactInfo: {
    phone: string;
    email: string;
    website?: string;
  };
  location: {
    latitude: number;
    longitude: number;
  };
  createdAt: string;
  updatedAt: string;
}

// Product Types
export interface Product {
  id: string;
  name: string;
  description: string;
  price: number;
  images: string[];
  category: string;
  subcategory?: string;
  vendorId: string;
  vendorName: string;
  inStock: boolean;
  specifications: Record<string, any>;
  rating: number;
  reviewCount: number;
  tags: string[];
  createdAt: string;
  updatedAt: string;
}

// Review Types
export interface Review {
  id: string;
  userId: string;
  userName: string;
  userAvatar?: string;
  targetId: string; // vendor, venue, or product ID
  targetType: 'vendor' | 'venue' | 'product';
  rating: number;
  title: string;
  comment: string;
  images?: string[];
  helpful: number;
  verified: boolean;
  createdAt: string;
  updatedAt: string;
}

// API Response Types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
  pagination?: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// Search and Filter Types
export interface SearchFilters {
  query?: string;
  city?: string;
  state?: string;
  category?: string;
  priceMin?: number;
  priceMax?: number;
  rating?: number;
  verified?: boolean;
  featured?: boolean;
  sortBy?: 'name' | 'price' | 'rating' | 'date';
  sortOrder?: 'asc' | 'desc';
  page?: number;
  limit?: number;
}

// Auth Types
export interface AuthState {
  isAuthenticated: boolean;
  isLoading: boolean;
  user: User | null;
  userProfile: UserProfile | null;
  userType: 'customer' | 'vendor' | 'admin' | null;
  error: string | null;
}

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface SignupData {
  fullName: string;
  email: string;
  phone?: string;
  password: string;
  userType?: 'customer' | 'vendor';
}

// Platform Types
export type Platform = 'web' | 'mobile' | 'desktop';

// Navigation Types
export interface NavigationItem {
  id: string;
  label: string;
  href: string;
  icon?: string;
  badge?: number;
  children?: NavigationItem[];
}

// Notification Types
export interface Notification {
  id: string;
  title: string;
  message: string;
  type: 'info' | 'success' | 'warning' | 'error';
  read: boolean;
  actionUrl?: string;
  createdAt: string;
}

// Location Types
export interface Location {
  latitude: number;
  longitude: number;
  address?: string;
  city?: string;
  state?: string;
  country?: string;
  pincode?: string;
}

// Error Types
export interface AppError {
  code: string;
  message: string;
  details?: any;
  timestamp: string;
}

// Export GraphQL types
export * from './graphql';

// Export utility types
export interface PlatformInfo {
  isWeb: boolean;
  isMobile: boolean;
  isReactNative: boolean;
  hasSessionStorage: boolean;
  hasAsyncStorage: boolean;
}

// Export utilities
export { storage, platformStorage } from '../utils/storage';
export { Platform, SafeAPI, PlatformComponent, getPlatformInfo } from '../utils/platform';
