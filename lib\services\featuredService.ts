import { vendorService } from './vendorService'
import { venueService } from './venueService'
import { shopService } from './shopService'

export interface FeaturedVendor {
  id: string
  name: string
  image: string
  category: string
  rating: number
  city: string
  featured: boolean
}

export interface FeaturedVenue {
  id: string
  name: string
  image: string
  category: string
  rating: number
  city: string
  featured: boolean
}

export interface FeaturedShop {
  id: string
  name: string
  image: string
  category: string
  price: string
  rating: number
  featured: boolean
}

class FeaturedService {
  // Cache for featured items to avoid repeated API calls
  private featuredVendorsCache: FeaturedVendor[] | null = null
  private featuredVenuesCache: FeaturedVenue[] | null = null
  private featuredShopsCache: FeaturedShop[] | null = null
  private cacheExpiry: number = 5 * 60 * 1000 // 5 minutes
  private lastFetchTime: { [key: string]: number } = {}

  private isCacheValid(type: string): boolean {
    const lastFetch = this.lastFetchTime[type]
    return lastFetch && (Date.now() - lastFetch) < this.cacheExpiry
  }

  async getFeaturedVendors(limit: number = 3): Promise<FeaturedVendor[]> {
    if (this.featuredVendorsCache && this.isCacheValid('vendors')) {
      return this.featuredVendorsCache.slice(0, limit)
    }

    try {
      const response = await vendorService.listVendors()
      
      // Filter and sort vendors to get featured ones
      const featuredVendors = response.items
        .filter(vendor => vendor.featured || vendor.rating >= 4.5)
        .sort((a, b) => {
          // Prioritize featured vendors, then by rating
          if (a.featured && !b.featured) return -1
          if (!a.featured && b.featured) return 1
          return (b.rating || 0) - (a.rating || 0)
        })
        .slice(0, 6) // Get top 6 for caching
        .map(vendor => ({
          id: vendor.id,
          name: vendor.name,
          image: vendor.gallery?.[0] || '/placeholder-vendor.jpg',
          category: vendor.category || 'Vendor',
          rating: vendor.rating || 0,
          city: vendor.city || '',
          featured: vendor.featured || false
        }))

      this.featuredVendorsCache = featuredVendors
      this.lastFetchTime['vendors'] = Date.now()
      
      return featuredVendors.slice(0, limit)
    } catch (error) {
      console.error('Error fetching featured vendors:', error)
      return this.getFallbackVendors(limit)
    }
  }

  async getFeaturedVenues(limit: number = 3): Promise<FeaturedVenue[]> {
    if (this.featuredVenuesCache && this.isCacheValid('venues')) {
      return this.featuredVenuesCache.slice(0, limit)
    }

    try {
      const response = await venueService.listVenues()
      
      // Filter and sort venues to get featured ones
      const featuredVenues = response.items
        .filter(venue => venue.featured || venue.rating >= 4.5)
        .sort((a, b) => {
          // Prioritize featured venues, then by rating
          if (a.featured && !b.featured) return -1
          if (!a.featured && b.featured) return 1
          return (b.rating || 0) - (a.rating || 0)
        })
        .slice(0, 6) // Get top 6 for caching
        .map(venue => ({
          id: venue.id,
          name: venue.name,
          image: venue.images?.[0] || '/placeholder.svg',
          category: venue.category || 'Venue',
          rating: venue.rating || 0,
          city: venue.city || '',
          featured: venue.featured || false
        }))

      this.featuredVenuesCache = featuredVenues
      this.lastFetchTime['venues'] = Date.now()
      
      return featuredVenues.slice(0, limit)
    } catch (error) {
      console.error('Error fetching featured venues:', error)
      return this.getFallbackVenues(limit)
    }
  }

  async getFeaturedShops(limit: number = 3): Promise<FeaturedShop[]> {
    if (this.featuredShopsCache && this.isCacheValid('shops')) {
      return this.featuredShopsCache.slice(0, limit)
    }

    try {
      const response = await shopService.listShops()
      
      // Filter and sort shops to get featured ones
      const featuredShops = response.items
        .filter(shop => shop.featured || shop.rating >= 4.5)
        .sort((a, b) => {
          // Prioritize featured shops, then by rating
          if (a.featured && !b.featured) return -1
          if (!a.featured && b.featured) return 1
          return (b.rating || 0) - (a.rating || 0)
        })
        .slice(0, 6) // Get top 6 for caching
        .map(shop => ({
          id: shop.id,
          name: shop.name,
          image: shop.images?.[0] || '/placeholder.svg',
          category: shop.category || 'Product',
          price: shop.price || '',
          rating: shop.rating || 0,
          featured: shop.featured || false
        }))

      this.featuredShopsCache = featuredShops
      this.lastFetchTime['shops'] = Date.now()
      
      return featuredShops.slice(0, limit)
    } catch (error) {
      console.error('Error fetching featured shops:', error)
      return this.getFallbackShops(limit)
    }
  }

  // Fallback data when API fails
  private getFallbackVendors(limit: number): FeaturedVendor[] {
    const fallbackVendors = [
      {
        id: 'vendor-1',
        name: 'Elite Photography',
        image: '/placeholder-vendor.jpg',
        category: 'Photography',
        rating: 4.8,
        city: 'Chennai',
        featured: true
      },
      {
        id: 'vendor-2',
        name: 'Royal Caterers',
        image: '/placeholder-vendor.jpg',
        category: 'Catering',
        rating: 4.7,
        city: 'Bangalore',
        featured: true
      },
      {
        id: 'vendor-3',
        name: 'Bridal Makeup Studio',
        image: '/placeholder-vendor.jpg',
        category: 'Makeup',
        rating: 4.9,
        city: 'Mumbai',
        featured: true
      }
    ]
    return fallbackVendors.slice(0, limit)
  }

  private getFallbackVenues(limit: number): FeaturedVenue[] {
    const fallbackVenues = [
      {
        id: 'venue-1',
        name: 'Grand Palace Hall',
        image: '/placeholder.svg',
        category: 'Banquet Hall',
        rating: 4.8,
        city: 'Chennai',
        featured: true
      },
      {
        id: 'venue-2',
        name: 'Royal Resort',
        image: '/placeholder.svg',
        category: 'Resort',
        rating: 4.7,
        city: 'Bangalore',
        featured: true
      },
      {
        id: 'venue-3',
        name: 'Heritage Mansion',
        image: '/placeholder.svg',
        category: 'Heritage',
        rating: 4.9,
        city: 'Mumbai',
        featured: true
      }
    ]
    return fallbackVenues.slice(0, limit)
  }

  private getFallbackShops(limit: number): FeaturedShop[] {
    const fallbackShops = [
      {
        id: 'shop-1',
        name: 'Wedding Gift Set',
        image: '/placeholder.svg',
        category: 'Gifts',
        price: '₹2,999',
        rating: 4.8,
        featured: true
      },
      {
        id: 'shop-2',
        name: 'Bridal Jewelry',
        image: '/placeholder.svg',
        category: 'Jewelry',
        price: '₹15,999',
        rating: 4.7,
        featured: true
      },
      {
        id: 'shop-3',
        name: 'Home Decor Set',
        image: '/placeholder.svg',
        category: 'Decor',
        price: '₹5,999',
        rating: 4.9,
        featured: true
      }
    ]
    return fallbackShops.slice(0, limit)
  }

  // Clear cache manually if needed
  clearCache(): void {
    this.featuredVendorsCache = null
    this.featuredVenuesCache = null
    this.featuredShopsCache = null
    this.lastFetchTime = {}
  }
}

export const featuredService = new FeaturedService()
