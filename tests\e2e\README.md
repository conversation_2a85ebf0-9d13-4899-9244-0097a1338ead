# E2E Testing for BookmyFestive

This directory contains comprehensive end-to-end tests for the BookmyFestive wedding platform using Playwright.

## Test Structure

```
tests/e2e/
├── utils/                  # Test utilities and helpers
│   ├── test-helpers.ts     # Base test helper class
│   ├── auth-helpers.ts     # Authentication utilities
│   ├── booking-helpers.ts  # Booking system utilities
│   └── ecommerce-helpers.ts # E-commerce utilities
├── auth/                   # Authentication tests
│   ├── login.spec.ts       # User login tests
│   └── signup.spec.ts      # User registration tests
├── booking/                # Booking system tests
│   └── venue-booking.spec.ts # Venue booking tests
├── ecommerce/              # E-commerce tests
│   └── shopping-cart.spec.ts # Shopping cart tests
├── admin/                  # Admin dashboard tests
│   └── admin-dashboard.spec.ts # Admin functionality tests
└── README.md              # This file
```

## Test Categories

### 1. Authentication Tests (`auth/`)
- **Login Tests**: User login validation, error handling, redirect behavior
- **Signup Tests**: User registration, form validation, OTP verification
- **Google OAuth**: Social login integration testing
- **Password Reset**: Forgot password functionality

### 2. Booking System Tests (`booking/`)
- **Venue Booking**: Complete booking flow for venues
- **Vendor Booking**: Service provider booking process
- **Availability Checking**: Date availability validation
- **Form Validation**: Required field validation and error handling
- **Booking Confirmation**: Success flow and confirmation display

### 3. E-commerce Tests (`ecommerce/`)
- **Shopping Cart**: Add/remove items, quantity updates
- **Product Browsing**: Search, filter, and sort functionality
- **Checkout Process**: Complete purchase flow
- **Order Management**: Order confirmation and tracking

### 4. Admin Dashboard Tests (`admin/`)
- **User Management**: Admin user operations
- **Vendor Approval**: Vendor verification process
- **Dashboard Analytics**: Statistics and reporting
- **Content Management**: Admin content operations

## Test Utilities

### TestHelpers (`utils/test-helpers.ts`)
Base utility class providing common functionality:
- Page navigation and loading
- Element interaction and waiting
- Screenshot capture
- Test data generation
- Network idle detection

### AuthHelpers (`utils/auth-helpers.ts`)
Authentication-specific utilities:
- Login/logout operations
- Role-based authentication (Customer, Vendor, Admin)
- OTP verification handling
- Google OAuth simulation

### BookingHelpers (`utils/booking-helpers.ts`)
Booking system utilities:
- Venue/vendor booking flows
- Form filling and validation
- Availability checking
- Booking confirmation verification

### EcommerceHelpers (`utils/ecommerce-helpers.ts`)
E-commerce utilities:
- Cart operations
- Product search and filtering
- Checkout process
- Order management

## Running Tests

### Prerequisites
```bash
# Install dependencies
npm install

# Install Playwright browsers
npx playwright install
```

### Run All Tests
```bash
# Run all E2E tests
npm run test:e2e

# Run with UI mode
npm run test:e2e:ui

# Run specific test file
npx playwright test tests/e2e/auth/login.spec.ts

# Run tests for specific browser
npx playwright test --project=chromium
```

### Run Tests by Category
```bash
# Authentication tests only
npx playwright test tests/e2e/auth/

# Booking tests only
npx playwright test tests/e2e/booking/

# E-commerce tests only
npx playwright test tests/e2e/ecommerce/

# Admin tests only
npx playwright test tests/e2e/admin/
```

### Mobile Testing
```bash
# Run mobile tests
npx playwright test --project="Mobile Chrome"
npx playwright test --project="Mobile Safari"
npx playwright test --project="Tablet Chrome"
```

### Debug Mode
```bash
# Run in debug mode
npx playwright test --debug

# Run specific test in debug mode
npx playwright test tests/e2e/auth/login.spec.ts --debug
```

## Test Configuration

### Browser Support
- **Desktop**: Chrome, Firefox, Safari
- **Mobile**: Chrome Mobile, Safari Mobile
- **Tablet**: iPad Pro simulation
- **Screen Sizes**: 1024x768, 1280x720, 1920x1080

### Test Environment
- **Base URL**: http://localhost:3000
- **Test Data**: Generated dynamically for each test
- **Screenshots**: Captured on failure
- **Videos**: Recorded for failed tests
- **Reports**: HTML, JSON, and JUnit formats

## Test Data Management

### Dynamic Test Data
Tests use dynamically generated test data to avoid conflicts:
```typescript
const testData = authHelpers.generateTestData();
// Generates unique email, name, phone, etc.
```

### Test User Accounts
Some tests require pre-existing user accounts:
- **Regular User**: <EMAIL>
- **Vendor User**: <EMAIL>  
- **Admin User**: <EMAIL>

*Note: These accounts need to be created manually in the test database*

## CI/CD Integration

### GitHub Actions
Tests run automatically on:
- Push to main/develop branches
- Pull requests
- Daily scheduled runs (2 AM UTC)

### Test Matrix
- **Desktop Browsers**: Chrome, Firefox, Safari
- **Mobile Devices**: Mobile Chrome, Mobile Safari, Tablet
- **Smoke Tests**: Critical path validation

## Best Practices

### Writing Tests
1. **Use Page Object Pattern**: Utilize helper classes for reusable functionality
2. **Independent Tests**: Each test should be able to run independently
3. **Descriptive Names**: Use clear, descriptive test names
4. **Proper Cleanup**: Ensure tests clean up after themselves
5. **Error Handling**: Handle expected failures gracefully

### Test Data
1. **Dynamic Generation**: Use generated test data when possible
2. **Unique Identifiers**: Ensure test data doesn't conflict
3. **Cleanup**: Remove test data after test completion
4. **Realistic Data**: Use realistic test data for better validation

### Debugging
1. **Screenshots**: Automatic screenshots on failure
2. **Console Logs**: Capture browser console output
3. **Network Logs**: Monitor network requests
4. **Step-by-step**: Use debug mode for step-by-step execution

## Known Limitations

1. **Test User Dependencies**: Some tests require pre-existing user accounts
2. **Database State**: Tests may fail if database is empty or in unexpected state
3. **External Services**: Tests involving external APIs may be flaky
4. **Real-time Features**: WebSocket or real-time features may need special handling

## Troubleshooting

### Common Issues

1. **Login Failures**: Ensure test users exist in the database
2. **Timeout Errors**: Increase timeout for slow operations
3. **Element Not Found**: Check if UI has changed or elements are dynamically loaded
4. **Network Errors**: Verify development server is running

### Debug Commands
```bash
# Check Playwright installation
npx playwright --version

# List available browsers
npx playwright install --dry-run

# Generate test code
npx playwright codegen localhost:3000

# Show test report
npx playwright show-report
```

## Contributing

When adding new tests:
1. Follow existing patterns and structure
2. Use appropriate helper classes
3. Add proper documentation
4. Ensure tests are independent and reliable
5. Update this README if adding new test categories
