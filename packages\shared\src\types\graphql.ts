/* eslint-disable */
// Shared GraphQL types for both web and mobile

// Base types
export interface ModelConnection<T> {
  items: T[];
  nextToken?: string;
}

// Vendor types
export interface Vendor {
  id: string;
  userId: string;
  name: string;
  description: string;
  contact: string;
  email: string;
  address: string;
  city: string;
  state: string;
  pincode?: string;
  website?: string;
  category: string;
  profilePhoto?: string;
  gallery?: string[];
  services?: VendorService[];
  socialMedia?: SocialMedia;
  experience: number;
  events: number;
  responseTime?: string;
  rating: number;
  reviewCount: number;
  verified: boolean;
  featured: boolean;
  availability?: string[];
  priceRange?: string;
  specializations?: string[];
  awards?: string[];
  languages?: string[];
  coverage?: string[];
  equipment?: string[];
  status: string;
  createdAt: string;
  updatedAt: string;
}

export interface VendorService {
  name: string;
  price: number;
  description: string;
}

export interface SocialMedia {
  facebook?: string;
  instagram?: string;
  youtube?: string;
  twitter?: string;
  linkedin?: string;
}

// Venue types
export interface Venue {
  id: string;
  userId: string;
  name: string;
  description: string;
  address: string;
  city: string;
  state: string;
  pincode?: string;
  contact: string;
  email: string;
  website?: string;
  venueType: string;
  capacity?: VenueCapacity;
  priceRange?: string;
  amenities?: string[];
  gallery?: string[];
  availability?: string[];
  rating: number;
  reviewCount: number;
  verified: boolean;
  featured: boolean;
  policies?: string[];
  catering: boolean;
  decoration: boolean;
  parking: boolean;
  accommodation: boolean;
  status: string;
  createdAt: string;
  updatedAt: string;
}

export interface VenueCapacity {
  min: number;
  max: number;
}

// Shop types
export interface Shop {
  id: string;
  userId: string;
  name: string;
  description: string;
  contact: string;
  email: string;
  address: string;
  city: string;
  state: string;
  pincode?: string;
  website?: string;
  category: string;
  profilePhoto?: string;
  gallery?: string[];
  products?: Product[];
  socialMedia?: SocialMedia;
  rating: number;
  reviewCount: number;
  verified: boolean;
  featured: boolean;
  policies?: string[];
  shipping?: ShippingInfo;
  returns?: string[];
  status: string;
  createdAt: string;
  updatedAt: string;
}

export interface Product {
  id?: string;
  name: string;
  price: number;
  description: string;
  images?: string[];
  category: string;
  inStock: boolean;
  sizes?: string[];
  colors?: string[];
  material?: string;
  care?: string[];
  features?: string[];
}

export interface ShippingInfo {
  freeShipping: boolean;
  shippingCost?: number;
  estimatedDays?: number;
  regions?: string[];
}

// User Profile types
export interface UserProfile {
  id: string;
  email: string;
  fullName: string;
  phone?: string;
  dateOfBirth?: string;
  address?: string;
  city?: string;
  state?: string;
  pincode?: string;
  weddingDate?: string;
  partnerName?: string;
  budget?: string;
  preferences?: string[];
  isAdmin: boolean;
  isSuperAdmin: boolean;
  role: string;
  permissions?: string[];
  avatar?: string;
  createdAt: string;
  updatedAt: string;
}

// Review types
export interface Review {
  id: string;
  userId: string;
  userName: string;
  userEmail: string;
  targetId: string;
  targetType: string;
  rating: number;
  title: string;
  comment: string;
  images?: string[];
  helpful?: number;
  verified: boolean;
  response?: string;
  status: string;
  createdAt: string;
  updatedAt: string;
}

// Inquiry types
export interface Inquiry {
  id: string;
  userId: string;
  userName: string;
  userEmail: string;
  userPhone?: string;
  targetId: string;
  targetType: string;
  eventDate?: string;
  eventLocation?: string;
  budget?: string;
  guestCount?: number;
  message: string;
  status: string;
  response?: string;
  createdAt: string;
  updatedAt: string;
}

// Newsletter types
export interface NewsletterSubscription {
  id: string;
  email: string;
  name?: string;
  preferences?: string[];
  status: string;
  createdAt: string;
  updatedAt: string;
}

// Blog types
export interface Blog {
  id: string;
  title: string;
  content: string;
  excerpt?: string;
  category: string;
  authorId: string;
  authorName: string;
  authorType: string;
  featuredImage?: string;
  tags?: string[];
  status: string;
  views: number;
  likes: number;
  comments: number;
  isPinned: boolean;
  isFeatured: boolean;
  publishedAt?: string;
  createdAt: string;
  updatedAt: string;
}

// Filter types
export interface ModelVendorFilterInput {
  id?: ModelIDInput;
  userId?: ModelIDInput;
  name?: ModelStringInput;
  category?: ModelStringInput;
  city?: ModelStringInput;
  state?: ModelStringInput;
  verified?: ModelBooleanInput;
  featured?: ModelBooleanInput;
  status?: ModelStringInput;
  and?: ModelVendorFilterInput[];
  or?: ModelVendorFilterInput[];
  not?: ModelVendorFilterInput;
}

export interface ModelVenueFilterInput {
  id?: ModelIDInput;
  userId?: ModelIDInput;
  name?: ModelStringInput;
  venueType?: ModelStringInput;
  city?: ModelStringInput;
  state?: ModelStringInput;
  verified?: ModelBooleanInput;
  featured?: ModelBooleanInput;
  status?: ModelStringInput;
  and?: ModelVenueFilterInput[];
  or?: ModelVenueFilterInput[];
  not?: ModelVenueFilterInput;
}

export interface ModelShopFilterInput {
  id?: ModelIDInput;
  userId?: ModelIDInput;
  name?: ModelStringInput;
  category?: ModelStringInput;
  city?: ModelStringInput;
  state?: ModelStringInput;
  verified?: ModelBooleanInput;
  featured?: ModelBooleanInput;
  status?: ModelStringInput;
  and?: ModelShopFilterInput[];
  or?: ModelShopFilterInput[];
  not?: ModelShopFilterInput;
}

export interface ModelUserProfileFilterInput {
  id?: ModelIDInput;
  email?: ModelStringInput;
  fullName?: ModelStringInput;
  role?: ModelStringInput;
  isAdmin?: ModelBooleanInput;
  isSuperAdmin?: ModelBooleanInput;
  and?: ModelUserProfileFilterInput[];
  or?: ModelUserProfileFilterInput[];
  not?: ModelUserProfileFilterInput;
}

export interface ModelReviewFilterInput {
  id?: ModelIDInput;
  userId?: ModelIDInput;
  targetId?: ModelIDInput;
  targetType?: ModelStringInput;
  rating?: ModelIntInput;
  verified?: ModelBooleanInput;
  status?: ModelStringInput;
  and?: ModelReviewFilterInput[];
  or?: ModelReviewFilterInput[];
  not?: ModelReviewFilterInput;
}

export interface ModelBlogFilterInput {
  id?: ModelIDInput;
  title?: ModelStringInput;
  category?: ModelStringInput;
  authorId?: ModelIDInput;
  authorType?: ModelStringInput;
  status?: ModelStringInput;
  isPinned?: ModelBooleanInput;
  isFeatured?: ModelBooleanInput;
  and?: ModelBlogFilterInput[];
  or?: ModelBlogFilterInput[];
  not?: ModelBlogFilterInput;
}

// Input types
export interface ModelIDInput {
  ne?: string;
  eq?: string;
  le?: string;
  lt?: string;
  ge?: string;
  gt?: string;
  contains?: string;
  notContains?: string;
  between?: string[];
  beginsWith?: string;
  attributeExists?: boolean;
  attributeType?: ModelAttributeTypes;
  size?: ModelSizeInput;
}

export interface ModelStringInput {
  ne?: string;
  eq?: string;
  le?: string;
  lt?: string;
  ge?: string;
  gt?: string;
  contains?: string;
  notContains?: string;
  between?: string[];
  beginsWith?: string;
  attributeExists?: boolean;
  attributeType?: ModelAttributeTypes;
  size?: ModelSizeInput;
}

export interface ModelIntInput {
  ne?: number;
  eq?: number;
  le?: number;
  lt?: number;
  ge?: number;
  gt?: number;
  between?: number[];
  attributeExists?: boolean;
  attributeType?: ModelAttributeTypes;
}

export interface ModelBooleanInput {
  ne?: boolean;
  eq?: boolean;
  attributeExists?: boolean;
  attributeType?: ModelAttributeTypes;
}

export interface ModelSizeInput {
  ne?: number;
  eq?: number;
  le?: number;
  lt?: number;
  ge?: number;
  gt?: number;
  between?: number[];
}

export enum ModelAttributeTypes {
  binary = "binary",
  binarySet = "binarySet",
  bool = "bool",
  list = "list",
  map = "map",
  number = "number",
  numberSet = "numberSet",
  string = "string",
  stringSet = "stringSet",
  _null = "_null"
}

// Create/Update input types
export interface CreateVendorInput {
  id?: string;
  userId: string;
  name: string;
  description: string;
  contact: string;
  email: string;
  address: string;
  city: string;
  state: string;
  pincode?: string;
  website?: string;
  category: string;
  profilePhoto?: string;
  gallery?: string[];
  services?: VendorService[];
  socialMedia?: SocialMedia;
  experience: number;
  events: number;
  responseTime?: string;
  rating?: number;
  reviewCount?: number;
  verified?: boolean;
  featured?: boolean;
  availability?: string[];
  priceRange?: string;
  specializations?: string[];
  awards?: string[];
  languages?: string[];
  coverage?: string[];
  equipment?: string[];
  status: string;
}

export interface UpdateVendorInput {
  id: string;
  userId?: string;
  name?: string;
  description?: string;
  contact?: string;
  email?: string;
  address?: string;
  city?: string;
  state?: string;
  pincode?: string;
  website?: string;
  category?: string;
  profilePhoto?: string;
  gallery?: string[];
  services?: VendorService[];
  socialMedia?: SocialMedia;
  experience?: number;
  events?: number;
  responseTime?: string;
  rating?: number;
  reviewCount?: number;
  verified?: boolean;
  featured?: boolean;
  availability?: string[];
  priceRange?: string;
  specializations?: string[];
  awards?: string[];
  languages?: string[];
  coverage?: string[];
  equipment?: string[];
  status?: string;
}

export interface CreateUserProfileInput {
  id?: string;
  email: string;
  fullName: string;
  phone?: string;
  dateOfBirth?: string;
  address?: string;
  city?: string;
  state?: string;
  pincode?: string;
  weddingDate?: string;
  partnerName?: string;
  budget?: string;
  preferences?: string[];
  isAdmin?: boolean;
  isSuperAdmin?: boolean;
  role: string;
  permissions?: string[];
  avatar?: string;
}

export interface UpdateUserProfileInput {
  id: string;
  email?: string;
  fullName?: string;
  phone?: string;
  dateOfBirth?: string;
  address?: string;
  city?: string;
  state?: string;
  pincode?: string;
  weddingDate?: string;
  partnerName?: string;
  budget?: string;
  preferences?: string[];
  isAdmin?: boolean;
  isSuperAdmin?: boolean;
  role?: string;
  permissions?: string[];
  avatar?: string;
}

export interface CreateReviewInput {
  id?: string;
  userId: string;
  userName: string;
  userEmail: string;
  targetId: string;
  targetType: string;
  rating: number;
  title: string;
  comment: string;
  images?: string[];
  helpful?: number;
  verified?: boolean;
  response?: string;
  status: string;
}

export interface UpdateReviewInput {
  id: string;
  userId?: string;
  userName?: string;
  userEmail?: string;
  targetId?: string;
  targetType?: string;
  rating?: number;
  title?: string;
  comment?: string;
  images?: string[];
  helpful?: number;
  verified?: boolean;
  response?: string;
  status?: string;
}

export interface CreateInquiryInput {
  id?: string;
  userId: string;
  userName: string;
  userEmail: string;
  userPhone?: string;
  targetId: string;
  targetType: string;
  eventDate?: string;
  eventLocation?: string;
  budget?: string;
  guestCount?: number;
  message: string;
  status: string;
  response?: string;
}

export interface UpdateInquiryInput {
  id: string;
  userId?: string;
  userName?: string;
  userEmail?: string;
  userPhone?: string;
  targetId?: string;
  targetType?: string;
  eventDate?: string;
  eventLocation?: string;
  budget?: string;
  guestCount?: number;
  message?: string;
  status?: string;
  response?: string;
}

export interface CreateNewsletterSubscriptionInput {
  id?: string;
  email: string;
  name?: string;
  preferences?: string[];
  status: string;
}

export interface UpdateNewsletterSubscriptionInput {
  id: string;
  email?: string;
  name?: string;
  preferences?: string[];
  status?: string;
}
