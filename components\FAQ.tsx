"use client"

import { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { 
  ChevronDown, 
  ChevronUp, 
  Search, 
  HelpCircle,
  Users,
  MapPin,
  ShoppingBag,
  Calendar,
  Star,
  CreditCard,
  Settings,
  Phone
} from 'lucide-react'

interface FAQItem {
  id: string
  question: string
  answer: string
  category: string
  tags: string[]
  popular?: boolean
}

interface FAQProps {
  showSearch?: boolean
  showCategories?: boolean
  limit?: number
  category?: string
}

export function FAQ({ showSearch = true, showCategories = true, limit, category }: FAQProps) {
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedCategory, setSelectedCategory] = useState(category || 'all')
  const [expandedItems, setExpandedItems] = useState<string[]>([])

  const categories = [
    { id: 'all', name: 'All Questions', icon: HelpCircle },
    { id: 'account', name: 'Account & Profile', icon: Users },
    { id: 'vendors', name: 'Finding Vendors', icon: Search },
    { id: 'venues', name: 'Booking Venues', icon: MapPin },
    { id: 'shopping', name: 'Shopping', icon: ShoppingBag },
    { id: 'planning', name: 'Wedding Planning', icon: Calendar },
    { id: 'reviews', name: 'Reviews & Ratings', icon: Star },
    { id: 'payments', name: 'Payments & Billing', icon: CreditCard },
    { id: 'technical', name: 'Technical Support', icon: Settings }
  ]

  const faqData: FAQItem[] = [
    // Account & Profile
    {
      id: '1',
      question: 'How do I create an account on BookmyFestive?',
      answer: 'Creating an account is simple! Click the "Sign Up" button on our homepage, choose your account type (Customer for couples, Vendor for service providers), fill in your basic details, and verify your email or phone number. You can then complete your profile with additional information.',
      category: 'account',
      tags: ['signup', 'registration', 'account'],
      popular: true
    },
    {
      id: '2',
      question: 'What\'s the difference between Customer and Vendor accounts?',
      answer: 'Customer accounts are for couples planning their wedding - you can search vendors, book services, and use planning tools. Vendor accounts are for wedding service providers - you can create business profiles, receive inquiries, and manage bookings. Choose the type that matches your needs.',
      category: 'account',
      tags: ['account types', 'customer', 'vendor'],
      popular: true
    },
    {
      id: '3',
      question: 'Can I change my account type after registration?',
      answer: 'Yes, you can change your account type by contacting our support team. However, this will reset your profile information, so we recommend choosing the correct type during initial registration.',
      category: 'account',
      tags: ['account type', 'change', 'support']
    },

    // Finding Vendors
    {
      id: '4',
      question: 'How do I find vendors in my city?',
      answer: 'Use our vendor search feature by selecting your city from the location dropdown, choose the service category you need (photography, catering, etc.), and apply filters like budget range and ratings. You can also use the "Near Me" option to find vendors close to your location.',
      category: 'vendors',
      tags: ['search', 'location', 'filters'],
      popular: true
    },
    {
      id: '5',
      question: 'How do I contact vendors?',
      answer: 'You can contact vendors through our inquiry system by clicking "Contact Vendor" on their profile. Fill out the inquiry form with your requirements, and the vendor will respond directly. You can also call them directly if their phone number is listed.',
      category: 'vendors',
      tags: ['contact', 'inquiry', 'communication'],
      popular: true
    },
    {
      id: '6',
      question: 'What should I include in my vendor inquiry?',
      answer: 'Include your wedding date, location, guest count, budget range, and specific requirements. The more details you provide, the better vendors can understand your needs and provide accurate quotes.',
      category: 'vendors',
      tags: ['inquiry', 'requirements', 'details']
    },

    // Booking Venues
    {
      id: '7',
      question: 'How do I book a venue through BookmyFestive?',
      answer: 'Browse venues in your preferred location, check availability for your wedding date, contact the venue through our platform, schedule a site visit, review the contract terms, and make the advance payment to confirm your booking.',
      category: 'venues',
      tags: ['booking', 'venue', 'process'],
      popular: true
    },
    {
      id: '8',
      question: 'What information do I need to provide when booking a venue?',
      answer: 'You\'ll need your wedding date, expected guest count, event timing (morning/evening), catering requirements, decoration preferences, and any special needs. Having this information ready will help venues provide accurate quotes.',
      category: 'venues',
      tags: ['booking', 'information', 'requirements']
    },

    // Shopping
    {
      id: '9',
      question: 'How does the shopping feature work?',
      answer: 'Browse our shop section to find wedding essentials like decorations, favors, and accessories. Add items to your cart, review your order, and proceed to checkout. We partner with trusted vendors to ensure quality products.',
      category: 'shopping',
      tags: ['shop', 'cart', 'checkout'],
      popular: true
    },
    {
      id: '10',
      question: 'What payment methods do you accept?',
      answer: 'We accept all major credit cards, debit cards, UPI payments, net banking, and digital wallets. All payments are processed securely through our encrypted payment gateway.',
      category: 'payments',
      tags: ['payment', 'methods', 'security'],
      popular: true
    },

    // Wedding Planning
    {
      id: '11',
      question: 'How do I use the wedding planning tools?',
      answer: 'Access planning tools from your dashboard. Create your wedding checklist based on your date, set up a budget tracker, manage your guest list, and track vendor bookings. These tools help you stay organized throughout your planning journey.',
      category: 'planning',
      tags: ['planning tools', 'checklist', 'budget'],
      popular: true
    },
    {
      id: '12',
      question: 'When should I start planning my wedding?',
      answer: 'We recommend starting 6-12 months before your wedding date. This gives you enough time to book popular vendors, compare options, and plan without stress. Use our timeline feature to create a personalized planning schedule.',
      category: 'planning',
      tags: ['timeline', 'planning', 'schedule']
    },

    // Reviews & Ratings
    {
      id: '13',
      question: 'How do I write a review for a vendor?',
      answer: 'After your event, go to your bookings in the dashboard and click "Write Review" next to the vendor. Rate your experience (1-5 stars) and write detailed feedback about service quality, professionalism, and value for money.',
      category: 'reviews',
      tags: ['reviews', 'ratings', 'feedback'],
      popular: true
    },
    {
      id: '14',
      question: 'Can I edit or delete my review?',
      answer: 'You can edit your review within 30 days of posting. To delete a review, contact our support team with a valid reason. We maintain review integrity, so deletions are only allowed in exceptional circumstances.',
      category: 'reviews',
      tags: ['edit', 'delete', 'review']
    },

    // Technical Support
    {
      id: '15',
      question: 'I\'m having trouble logging in. What should I do?',
      answer: 'First, check your email and password spelling. Use the "Forgot Password" option to reset if needed. Clear your browser cache, try a different browser, or contact our technical support team if the issue persists.',
      category: 'technical',
      tags: ['login', 'password', 'troubleshooting']
    },
    {
      id: '16',
      question: 'Is my personal information secure on BookmyFestive?',
      answer: 'Yes, we use industry-standard encryption and security measures to protect your data. We never share your personal information with third parties without your consent. Read our Privacy Policy for detailed information.',
      category: 'technical',
      tags: ['security', 'privacy', 'data protection'],
      popular: true
    }
  ]

  const toggleExpanded = (id: string) => {
    setExpandedItems(prev => 
      prev.includes(id) 
        ? prev.filter(item => item !== id)
        : [...prev, id]
    )
  }

  const filteredFAQs = faqData.filter(faq => {
    const matchesCategory = selectedCategory === 'all' || faq.category === selectedCategory
    const matchesSearch = searchQuery === '' || 
      faq.question.toLowerCase().includes(searchQuery.toLowerCase()) ||
      faq.answer.toLowerCase().includes(searchQuery.toLowerCase()) ||
      faq.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))
    
    return matchesCategory && matchesSearch
  }).slice(0, limit)

  const popularFAQs = faqData.filter(faq => faq.popular).slice(0, 6)

  return (
    <div className="space-y-6">
      {/* Search */}
      {showSearch && (
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-5 h-5" />
          <Input
            type="text"
            placeholder="Search frequently asked questions..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>
      )}

      {/* Categories */}
      {showCategories && (
        <div className="flex flex-wrap gap-2">
          {categories.map((cat) => (
            <Button
              key={cat.id}
              variant={selectedCategory === cat.id ? "default" : "outline"}
              size="sm"
              onClick={() => setSelectedCategory(cat.id)}
              className="flex items-center gap-2"
            >
              <cat.icon className="w-4 h-4" />
              {cat.name}
            </Button>
          ))}
        </div>
      )}

      {/* Popular Questions (only show when no search/filter) */}
      {searchQuery === '' && selectedCategory === 'all' && !limit && (
        <div>
          <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
            <Star className="w-5 h-5 text-yellow-500" />
            Popular Questions
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-8">
            {popularFAQs.map((faq) => (
              <Card key={faq.id} className="cursor-pointer hover:shadow-md transition-shadow">
                <CardContent className="p-4">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <h4 className="font-medium text-sm mb-2">{faq.question}</h4>
                      <div className="flex flex-wrap gap-1">
                        {faq.tags.slice(0, 2).map((tag) => (
                          <Badge key={tag} variant="secondary" className="text-xs">
                            {tag}
                          </Badge>
                        ))}
                      </div>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => toggleExpanded(faq.id)}
                    >
                      {expandedItems.includes(faq.id) ? (
                        <ChevronUp className="w-4 h-4" />
                      ) : (
                        <ChevronDown className="w-4 h-4" />
                      )}
                    </Button>
                  </div>
                  {expandedItems.includes(faq.id) && (
                    <div className="mt-3 pt-3 border-t text-sm text-muted-foreground">
                      {faq.answer}
                    </div>
                  )}
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      )}

      {/* All Questions */}
      <div>
        {searchQuery || selectedCategory !== 'all' ? (
          <h3 className="text-lg font-semibold mb-4">
            {filteredFAQs.length} question{filteredFAQs.length !== 1 ? 's' : ''} found
          </h3>
        ) : (
          <h3 className="text-lg font-semibold mb-4">All Questions</h3>
        )}
        
        <div className="space-y-4">
          {filteredFAQs.map((faq) => (
            <Card key={faq.id}>
              <CardHeader 
                className="cursor-pointer"
                onClick={() => toggleExpanded(faq.id)}
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <CardTitle className="text-base font-medium mb-2">
                      {faq.question}
                      {faq.popular && (
                        <Badge variant="secondary" className="ml-2 text-xs">
                          Popular
                        </Badge>
                      )}
                    </CardTitle>
                    <div className="flex flex-wrap gap-1">
                      {faq.tags.map((tag) => (
                        <Badge key={tag} variant="outline" className="text-xs">
                          {tag}
                        </Badge>
                      ))}
                    </div>
                  </div>
                  <Button variant="ghost" size="sm">
                    {expandedItems.includes(faq.id) ? (
                      <ChevronUp className="w-4 h-4" />
                    ) : (
                      <ChevronDown className="w-4 h-4" />
                    )}
                  </Button>
                </div>
              </CardHeader>
              {expandedItems.includes(faq.id) && (
                <CardContent className="pt-0">
                  <div className="text-muted-foreground">
                    {faq.answer}
                  </div>
                </CardContent>
              )}
            </Card>
          ))}
        </div>
      </div>

      {/* No Results */}
      {filteredFAQs.length === 0 && (
        <div className="text-center py-12">
          <HelpCircle className="w-12 h-12 mx-auto text-muted-foreground mb-4" />
          <h3 className="text-lg font-semibold mb-2">No questions found</h3>
          <p className="text-muted-foreground mb-4">
            Try adjusting your search or browse different categories
          </p>
          <Button variant="outline">
            <Phone className="w-4 h-4 mr-2" />
            Contact Support
          </Button>
        </div>
      )}
    </div>
  )
}

export default FAQ
