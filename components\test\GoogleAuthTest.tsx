"use client"

import React, { useEffect, useState } from 'react'
import { GoogleAuthService } from '@/lib/services/googleAuthService'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { CheckCircle, AlertCircle, Loader2 } from 'lucide-react'

export default function GoogleAuthTest() {
  const [authResult, setAuthResult] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    // Initialize Google Auth listener
    GoogleAuthService.initializeAuthListener((result) => {
      setAuthResult(result)
      setIsLoading(false)
      
      if (!result.success) {
        setError(result.error || 'Authentication failed')
      }
    })

    // Check if we're returning from OAuth callback
    const handleOAuthCallback = async () => {
      const result = await GoogleAuthService.handleOAuthCallback()
      if (result) {
        setAuthResult(result)
        if (!result.success) {
          setError(result.error || 'OAuth callback failed')
        }
      }
    }

    handleOAuthCallback()
  }, [])

  const handleGoogleSignIn = async () => {
    setIsLoading(true)
    setError(null)
    setAuthResult(null)
    
    try {
      await GoogleAuthService.signInWithGoogle()
      // The redirect will happen automatically
    } catch (error: any) {
      setError(error.message || 'Google sign-in failed')
      setIsLoading(false)
    }
  }

  const handleSignOut = async () => {
    setIsLoading(true)
    setError(null)
    
    try {
      await GoogleAuthService.signOut()
      setAuthResult(null)
    } catch (error: any) {
      setError(error.message || 'Sign out failed')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader>
        <CardTitle className="text-center">Google Authentication Test</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Sign In Button */}
        {!authResult?.success && (
          <Button
            onClick={handleGoogleSignIn}
            disabled={isLoading}
            className="w-full flex items-center justify-center gap-2"
          >
            {isLoading ? (
              <Loader2 className="w-4 h-4 animate-spin" />
            ) : (
              <svg className="w-4 h-4" viewBox="0 0 24 24">
                <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
              </svg>
            )}
            {isLoading ? 'Connecting...' : 'Sign in with Google'}
          </Button>
        )}

        {/* Success State */}
        {authResult?.success && (
          <div className="space-y-4">
            <div className="flex items-center gap-2 p-3 bg-green-50 border border-green-200 rounded-lg">
              <CheckCircle className="w-5 h-5 text-green-600" />
              <span className="text-green-800 font-medium">Successfully signed in!</span>
            </div>
            
            {authResult.user && (
              <div className="p-3 bg-gray-50 border rounded-lg">
                <h4 className="font-medium mb-2">User Information:</h4>
                <div className="text-sm space-y-1">
                  <p><strong>Email:</strong> {authResult.user.signInDetails?.loginId || 'N/A'}</p>
                  <p><strong>Username:</strong> {authResult.user.username || 'N/A'}</p>
                  <p><strong>User ID:</strong> {authResult.user.userId || 'N/A'}</p>
                </div>
              </div>
            )}

            <Button
              onClick={handleSignOut}
              disabled={isLoading}
              variant="outline"
              className="w-full"
            >
              {isLoading ? (
                <Loader2 className="w-4 h-4 animate-spin mr-2" />
              ) : null}
              Sign Out
            </Button>
          </div>
        )}

        {/* Error State */}
        {error && (
          <div className="flex items-center gap-2 p-3 bg-red-50 border border-red-200 rounded-lg">
            <AlertCircle className="w-5 h-5 text-red-600" />
            <span className="text-red-800 text-sm">{error}</span>
          </div>
        )}

        {/* Loading State */}
        {isLoading && !authResult && (
          <div className="flex items-center justify-center gap-2 p-3 bg-blue-50 border border-blue-200 rounded-lg">
            <Loader2 className="w-4 h-4 animate-spin text-blue-600" />
            <span className="text-blue-800 text-sm">Processing authentication...</span>
          </div>
        )}

        {/* Instructions */}
        <div className="text-xs text-gray-500 space-y-1">
          <p><strong>Note:</strong> This test component demonstrates Google OAuth integration.</p>
          <p>• No password or OTP required for Google users</p>
          <p>• Automatic account creation on first sign-in</p>
          <p>• Secure OAuth 2.0 flow with token management</p>
        </div>
      </CardContent>
    </Card>
  )
}
