"use client"

import React, { useState, useEffect } from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Check, Crown, Loader2 } from 'lucide-react'
import { VendorSubscriptionService, VendorSubscriptionPlan } from '@/lib/services/vendorSubscriptionService'
import toast from 'react-hot-toast'

interface VendorPricingStepProps {
  vendorId: string
  vendorInfo: {
    name: string
    email: string
    phone?: string
  }
  onSubscriptionComplete: (subscriptionId: string) => void
  onSkip: () => void
}

export default function VendorPricingStep({ 
  vendorId, 
  vendorInfo, 
  onSubscriptionComplete, 
  onSkip 
}: VendorPricingStepProps) {
  const [plans, setPlans] = useState<VendorSubscriptionPlan[]>([])
  const [selectedPlan, setSelectedPlan] = useState<VendorSubscriptionPlan | null>(null)
  const [loading, setLoading] = useState(true)
  const [processing, setProcessing] = useState(false)
  const [paymentMethod, setPaymentMethod] = useState<'RAZORPAY' | 'UPI' | 'CREDIT_CARD' | 'DEBIT_CARD' | 'NET_BANKING'>('RAZORPAY')

  useEffect(() => {
    loadPricingPlans()
    loadRazorpaySDK()
  }, [])

  const loadPricingPlans = async () => {
    try {
      const pricingPlans = await VendorSubscriptionService.getPricingPlans()
      setPlans(pricingPlans)
      
      // Auto-select popular plan if available
      const popularPlan = pricingPlans.find(plan => plan.isPopular)
      if (popularPlan) {
        setSelectedPlan(popularPlan)
      }
    } catch (error) {
      console.error('Error loading pricing plans:', error)
      toast.error('Failed to load pricing plans')
    } finally {
      setLoading(false)
    }
  }

  const loadRazorpaySDK = async () => {
    const loaded = await VendorSubscriptionService.loadRazorpaySDK()
    if (!loaded) {
      toast.error('Failed to load payment gateway. Please refresh the page.')
    }
  }

  const handleSubscribe = async () => {
    if (!selectedPlan) {
      toast.error('Please select a subscription plan')
      return
    }

    setProcessing(true)
    try {
      const result = await VendorSubscriptionService.processSubscriptionPayment(
        {
          vendorId,
          planId: selectedPlan.id,
          paymentMethod,
          autoRenew: true,
          vendorEmail: vendorInfo.email,
          vendorName: vendorInfo.name
        },
        selectedPlan,
        vendorInfo
      )

      if (result.success && result.subscriptionId) {
        toast.success('Subscription activated successfully!')
        onSubscriptionComplete(result.subscriptionId)
      } else {
        toast.error(result.message || 'Subscription failed')
      }
    } catch (error) {
      console.error('Error processing subscription:', error)
      toast.error('An error occurred while processing your subscription')
    } finally {
      setProcessing(false)
    }
  }

  const formatPrice = (price: number, currency: string) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 0
    }).format(price)
  }

  const getDurationText = (duration: string) => {
    switch (duration) {
      case 'MONTHLY': return 'per month'
      case 'QUARTERLY': return 'per quarter'
      case 'YEARLY': return 'per year'
      default: return 'per month'
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <Loader2 className="h-8 w-8 animate-spin" />
        <span className="ml-2">Loading pricing plans...</span>
      </div>
    )
  }

  return (
    <div className="max-w-6xl mx-auto p-6">
      <div className="text-center mb-8">
        <h2 className="text-3xl font-bold text-gray-900 mb-4">
          Choose Your Subscription Plan
        </h2>
        <p className="text-lg text-gray-600 mb-6">
          Select a plan to start showcasing your business on BookmyFestive
        </p>
        <div className="flex justify-center gap-4 mb-6">
          <Button
            variant="outline"
            onClick={onSkip}
            className="px-6"
          >
            Skip for Now
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
        {plans.map((plan) => (
          <Card 
            key={plan.id}
            className={`relative cursor-pointer transition-all duration-200 ${
              selectedPlan?.id === plan.id 
                ? 'ring-2 ring-red-500 shadow-lg' 
                : 'hover:shadow-md'
            } ${plan.isPopular ? 'border-red-200' : ''}`}
            onClick={() => setSelectedPlan(plan)}
          >
            {plan.isPopular && (
              <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                <Badge className="bg-red-600 text-white px-3 py-1">
                  <Crown className="w-3 h-3 mr-1" />
                  Most Popular
                </Badge>
              </div>
            )}
            
            <CardHeader className="text-center pb-4">
              <CardTitle className="text-xl font-bold">{plan.name}</CardTitle>
              <CardDescription className="text-sm">{plan.description}</CardDescription>
              <div className="mt-4">
                <span className="text-3xl font-bold text-gray-900">
                  {formatPrice(plan.price, plan.currency)}
                </span>
                <span className="text-gray-600 ml-1">
                  {getDurationText(plan.duration)}
                </span>
              </div>
              {plan.discountPercentage && (
                <Badge variant="secondary" className="mt-2">
                  {plan.discountPercentage}% OFF
                </Badge>
              )}
            </CardHeader>
            
            <CardContent>
              <ul className="space-y-3">
                {plan.features.map((feature, index) => (
                  <li key={index} className="flex items-start">
                    <Check className="w-4 h-4 text-green-500 mt-0.5 mr-3 flex-shrink-0" />
                    <span className="text-sm text-gray-700">{feature}</span>
                  </li>
                ))}
              </ul>
            </CardContent>
          </Card>
        ))}
      </div>

      {selectedPlan && (
        <div className="bg-gray-50 rounded-lg p-6">
          <h3 className="text-lg font-semibold mb-4">Payment Method</h3>
          <div className="grid grid-cols-2 md:grid-cols-5 gap-3 mb-6">
            {[
              { value: 'RAZORPAY', label: 'Razorpay' },
              { value: 'UPI', label: 'UPI' },
              { value: 'CREDIT_CARD', label: 'Credit Card' },
              { value: 'DEBIT_CARD', label: 'Debit Card' },
              { value: 'NET_BANKING', label: 'Net Banking' }
            ].map((method) => (
              <Button
                key={method.value}
                variant={paymentMethod === method.value ? 'default' : 'outline'}
                size="sm"
                onClick={() => setPaymentMethod(method.value as any)}
                className="text-xs"
              >
                {method.label}
              </Button>
            ))}
          </div>

          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button
              onClick={handleSubscribe}
              disabled={processing}
              className="bg-red-600 hover:bg-red-700 text-white px-8 py-3"
            >
              {processing ? (
                <>
                  <Loader2 className="w-4 h-4 animate-spin mr-2" />
                  Processing...
                </>
              ) : (
                <>
                  Subscribe to {selectedPlan.name}
                  <span className="ml-2">
                    {formatPrice(selectedPlan.price, selectedPlan.currency)}
                  </span>
                </>
              )}
            </Button>
            
            <Button
              variant="outline"
              onClick={onSkip}
              disabled={processing}
              className="px-8 py-3"
            >
              Skip for Now
            </Button>
          </div>

          <p className="text-xs text-gray-500 text-center mt-4">
            You can change or cancel your subscription anytime from your dashboard.
            Secure payments powered by Razorpay.
          </p>
        </div>
      )}
    </div>
  )
}
