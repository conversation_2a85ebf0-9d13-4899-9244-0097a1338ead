{"api": {"thirumanam360": {"dependsOn": [{"attributes": ["UserPoolId"], "category": "auth", "resourceName": "thirumanam360fb0771b2"}], "output": {"authConfig": {"additionalAuthenticationProviders": [{"authenticationType": "AMAZON_COGNITO_USER_POOLS", "userPoolConfig": {"userPoolId": "auththirumanam360fb0771b2"}}], "defaultAuthentication": {"apiKeyConfig": {"apiKeyExpirationDays": 7}, "authenticationType": "API_KEY"}}}, "providerPlugin": "awscloudformation", "service": "AppSync"}}, "auth": {"thirumanam360fb0771b2": {"customAuth": false, "dependsOn": [], "frontendAuthConfig": {"mfaConfiguration": "OFF", "mfaTypes": ["SMS"], "passwordProtectionSettings": {"passwordPolicyCharacters": [], "passwordPolicyMinLength": 8}, "signupAttributes": ["EMAIL"], "socialProviders": [], "usernameAttributes": ["EMAIL", "PHONE_NUMBER"], "verificationMechanisms": ["EMAIL"]}, "providerPlugin": "awscloudformation", "service": "Cognito"}}}