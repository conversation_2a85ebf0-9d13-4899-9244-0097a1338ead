import React from 'react';
import { View, ScrollView, SafeAreaView, StatusBar } from 'react-native';
import { Header, HeaderProps } from './Header';
import { Footer } from './Footer';
import { useTheme } from '../providers/ThemeProvider';

interface LayoutWrapperProps {
  children: React.ReactNode;
  showHeader?: boolean;
  showFooter?: boolean;
  headerProps?: Partial<HeaderProps>;
  scrollable?: boolean;
  safeArea?: boolean;
  backgroundColor?: string;
  contentContainerStyle?: any;
  style?: any;
}

export function LayoutWrapper({
  children,
  showHeader = true,
  showFooter = true,
  headerProps = {},
  scrollable = true,
  safeArea = true,
  backgroundColor,
  contentContainerStyle,
  style,
}: LayoutWrapperProps) {
  const { theme } = useTheme();

  const defaultBackgroundColor = backgroundColor || theme.colors.background;

  const content = (
    <View style={[{ flex: 1, backgroundColor: defaultBackgroundColor }, style]}>
      <StatusBar 
        barStyle={theme.dark ? 'light-content' : 'dark-content'}
        backgroundColor={theme.colors.background}
      />
      
      {showHeader && <Header {...headerProps} />}
      
      {scrollable ? (
        <ScrollView
          style={{ flex: 1 }}
          contentContainerStyle={[
            { flexGrow: 1 },
            contentContainerStyle
          ]}
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="handled"
        >
          {children}
          {showFooter && <Footer />}
        </ScrollView>
      ) : (
        <View style={{ flex: 1 }}>
          {children}
          {showFooter && <Footer />}
        </View>
      )}
    </View>
  );

  if (safeArea) {
    return (
      <SafeAreaView style={{ flex: 1, backgroundColor: defaultBackgroundColor }}>
        {content}
      </SafeAreaView>
    );
  }

  return content;
}

// Specialized layout components for common use cases

export function ScreenLayout({
  children,
  title,
  showBack = false,
  showSearch = false,
  scrollable = true,
  ...props
}: LayoutWrapperProps & {
  title?: string;
  showBack?: boolean;
  showSearch?: boolean;
}) {
  return (
    <LayoutWrapper
      headerProps={{
        title,
        showBack,
        showSearch,
        ...props.headerProps,
      }}
      scrollable={scrollable}
      {...props}
    >
      {children}
    </LayoutWrapper>
  );
}

export function AuthLayout({
  children,
  ...props
}: LayoutWrapperProps) {
  return (
    <LayoutWrapper
      showHeader={false}
      showFooter={false}
      scrollable={false}
      safeArea={true}
      {...props}
    >
      {children}
    </LayoutWrapper>
  );
}

export function OnboardingLayout({
  children,
  ...props
}: LayoutWrapperProps) {
  return (
    <LayoutWrapper
      showHeader={false}
      showFooter={false}
      scrollable={false}
      safeArea={true}
      {...props}
    >
      {children}
    </LayoutWrapper>
  );
}

export function ModalLayout({
  children,
  title,
  onClose,
  ...props
}: LayoutWrapperProps & {
  title?: string;
  onClose?: () => void;
}) {
  return (
    <LayoutWrapper
      headerProps={{
        title,
        showBack: true,
        showProfile: false,
        showNotifications: false,
        showSearch: false,
        showHamburger: false,
        ...props.headerProps,
      }}
      showFooter={false}
      scrollable={true}
      {...props}
    >
      {children}
    </LayoutWrapper>
  );
}

export function TabLayout({
  children,
  ...props
}: LayoutWrapperProps) {
  return (
    <LayoutWrapper
      headerProps={{
        showHamburger: true,
        showProfile: true,
        showNotifications: true,
        showSearch: true,
        ...props.headerProps,
      }}
      showFooter={false} // Tab navigator handles bottom navigation
      scrollable={true}
      {...props}
    >
      {children}
    </LayoutWrapper>
  );
}
