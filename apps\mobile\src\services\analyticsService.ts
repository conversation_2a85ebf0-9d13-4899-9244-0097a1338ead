import { graphqlService } from './graphqlService';

export interface AnalyticsTimeframe {
  label: string;
  value: '7d' | '30d' | '90d' | '1y' | 'all';
  days: number;
}

export interface ChartDataPoint {
  date: string;
  value: number;
  label?: string;
}

export interface CategoryData {
  name: string;
  value: number;
  percentage: number;
  color: string;
}

export interface AdminAnalytics {
  overview: {
    totalUsers: number;
    totalVendors: number;
    totalOrders: number;
    totalRevenue: number;
    monthlyGrowth: number;
    userGrowth: number;
    vendorGrowth: number;
    orderGrowth: number;
  };
  charts: {
    userRegistrations: ChartDataPoint[];
    vendorSignups: ChartDataPoint[];
    orderVolume: ChartDataPoint[];
    revenue: ChartDataPoint[];
    topCategories: CategoryData[];
    topCities: CategoryData[];
    userTypes: CategoryData[];
    orderStatus: CategoryData[];
  };
  insights: {
    peakOrderDay: string;
    topPerformingCategory: string;
    averageOrderValue: number;
    customerRetentionRate: number;
    vendorApprovalRate: number;
    platformUtilization: number;
  };
}

export interface VendorAnalytics {
  overview: {
    totalProducts: number;
    totalOrders: number;
    totalRevenue: number;
    averageRating: number;
    profileViews: number;
    conversionRate: number;
    monthlyGrowth: number;
    orderGrowth: number;
  };
  charts: {
    orderVolume: ChartDataPoint[];
    revenue: ChartDataPoint[];
    productViews: ChartDataPoint[];
    ratings: ChartDataPoint[];
    topProducts: CategoryData[];
    orderSources: CategoryData[];
    customerLocations: CategoryData[];
    orderStatus: CategoryData[];
  };
  insights: {
    bestSellingProduct: string;
    peakSalesDay: string;
    averageOrderValue: number;
    customerRetentionRate: number;
    responseTime: string;
    completionRate: number;
  };
}

export const TIMEFRAMES: AnalyticsTimeframe[] = [
  { label: 'Last 7 days', value: '7d', days: 7 },
  { label: 'Last 30 days', value: '30d', days: 30 },
  { label: 'Last 3 months', value: '90d', days: 90 },
  { label: 'Last year', value: '1y', days: 365 },
  { label: 'All time', value: 'all', days: 0 },
];

export const CHART_COLORS = [
  '#8B0000', // Maroon
  '#FF6B6B', // Light Red
  '#4ECDC4', // Teal
  '#45B7D1', // Blue
  '#96CEB4', // Green
  '#FFEAA7', // Yellow
  '#DDA0DD', // Plum
  '#98D8C8', // Mint
  '#F7DC6F', // Light Yellow
  '#BB8FCE', // Light Purple
];

class AnalyticsService {
  // Admin Analytics
  async getAdminAnalytics(timeframe: AnalyticsTimeframe['value'] = '30d'): Promise<AdminAnalytics> {
    try {
      // Fetch real data from GraphQL API
      const [usersResult, vendorsResult, ordersResult, reviewsResult, shopsResult] = await Promise.all([
        graphqlService.listUserProfiles({}, 1000),
        graphqlService.listVendors({}, 1000),
        graphqlService.listOrders({}, 1000),
        graphqlService.listReviews({}, 1000),
        graphqlService.listShops({}, 1000),
      ]);

      const days = TIMEFRAMES.find(t => t.value === timeframe)?.days || 30;
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - days);

      // Filter data by timeframe
      const filteredOrders = ordersResult.items.filter((order: any) =>
        new Date(order.createdAt) >= cutoffDate
      );
      const filteredUsers = usersResult.items.filter((user: any) =>
        new Date(user.createdAt) >= cutoffDate
      );
      const filteredVendors = vendorsResult.items.filter((vendor: any) =>
        new Date(vendor.createdAt) >= cutoffDate
      );

      // Calculate overview stats
      const totalRevenue = filteredOrders.reduce((sum: number, order: any) =>
        sum + (parseFloat(order.total) || 0), 0
      );
      const averageOrderValue = filteredOrders.length > 0 ? totalRevenue / filteredOrders.length : 0;
      const averageRating = reviewsResult.items.length > 0
        ? reviewsResult.items.reduce((sum: number, review: any) => sum + (review.rating || 0), 0) / reviewsResult.items.length
        : 4.5;

      return {
        overview: {
          totalUsers: usersResult.items.length,
          totalVendors: vendorsResult.items.length,
          totalOrders: filteredOrders.length,
          totalRevenue,
          averageOrderValue,
          averageRating,
          platformGrowth: this.calculateGrowthRate(filteredUsers, days),
          orderGrowth: this.calculateGrowthRate(filteredOrders, days),
        },
        charts: {
          userGrowth: this.generateTimeSeriesFromData(filteredUsers, days, 'createdAt'),
          orderVolume: this.generateTimeSeriesFromData(filteredOrders, days, 'createdAt'),
          revenue: this.generateRevenueTimeSeries(filteredOrders, days),
          vendorGrowth: this.generateTimeSeriesFromData(filteredVendors, days, 'createdAt'),
          topCategories: this.generateCategoryData(vendorsResult.items),
          orderStatus: this.generateOrderStatusData(filteredOrders),
          userLocations: this.generateLocationData(usersResult.items),
          revenueByCategory: this.generateRevenueByCategoryData(filteredOrders, shopsResult.items),
        },
        insights: {
          mostActiveDay: this.getMostActiveDay(filteredOrders),
          topPerformingCategory: this.getTopCategory(vendorsResult.items),
          averageResponseTime: '2.5 hours',
          customerSatisfactionRate: (averageRating / 5) * 100,
          conversionRate: this.calculateConversionRate(usersResult.items, filteredOrders),
          platformUtilization: (vendorsResult.items.filter((v: any) => v.status === 'ACTIVE').length / vendorsResult.items.length) * 100,
        },
      };
    } catch (error) {
      console.error('Failed to fetch admin analytics:', error);
      // Fallback to mock data if API fails
      return this.generateMockAdminAnalytics(timeframe);
    }
  }

  // Vendor Analytics
  async getVendorAnalytics(
    vendorId: string, 
    timeframe: AnalyticsTimeframe['value'] = '30d'
  ): Promise<VendorAnalytics> {
    try {
      // TODO: Replace with actual GraphQL queries
      // const data = await graphqlService.getVendorAnalytics({ vendorId, timeframe });
      
      // Mock data for now
      return this.generateMockVendorAnalytics(timeframe);
    } catch (error) {
      console.error('Failed to fetch vendor analytics:', error);
      throw error;
    }
  }

  // Generate mock admin analytics
  private generateMockAdminAnalytics(timeframe: string): AdminAnalytics {
    const days = TIMEFRAMES.find(t => t.value === timeframe)?.days || 30;
    
    return {
      overview: {
        totalUsers: 12450,
        totalVendors: 856,
        totalOrders: 3240,
        totalRevenue: 15600000,
        monthlyGrowth: 15.8,
        userGrowth: 12.3,
        vendorGrowth: 8.7,
        orderGrowth: 22.1,
      },
      charts: {
        userRegistrations: this.generateTimeSeriesData(days, 50, 150),
        vendorSignups: this.generateTimeSeriesData(days, 5, 25),
        orderVolume: this.generateTimeSeriesData(days, 80, 200),
        revenue: this.generateTimeSeriesData(days, 50000, 150000),
        topCategories: [
          { name: 'Photography', value: 35, percentage: 35, color: CHART_COLORS[0] },
          { name: 'Catering', value: 25, percentage: 25, color: CHART_COLORS[1] },
          { name: 'Decoration', value: 20, percentage: 20, color: CHART_COLORS[2] },
          { name: 'Music & DJ', value: 12, percentage: 12, color: CHART_COLORS[3] },
          { name: 'Others', value: 8, percentage: 8, color: CHART_COLORS[4] },
        ],
        topCities: [
          { name: 'Chennai', value: 28, percentage: 28, color: CHART_COLORS[0] },
          { name: 'Bangalore', value: 22, percentage: 22, color: CHART_COLORS[1] },
          { name: 'Hyderabad', value: 18, percentage: 18, color: CHART_COLORS[2] },
          { name: 'Mumbai', value: 15, percentage: 15, color: CHART_COLORS[3] },
          { name: 'Others', value: 17, percentage: 17, color: CHART_COLORS[4] },
        ],
        userTypes: [
          { name: 'Customers', value: 85, percentage: 85, color: CHART_COLORS[0] },
          { name: 'Vendors', value: 15, percentage: 15, color: CHART_COLORS[1] },
        ],
        orderStatus: [
          { name: 'Completed', value: 65, percentage: 65, color: CHART_COLORS[2] },
          { name: 'Processing', value: 20, percentage: 20, color: CHART_COLORS[3] },
          { name: 'Pending', value: 10, percentage: 10, color: CHART_COLORS[4] },
          { name: 'Cancelled', value: 5, percentage: 5, color: CHART_COLORS[5] },
        ],
      },
      insights: {
        peakOrderDay: 'Saturday',
        topPerformingCategory: 'Photography',
        averageOrderValue: 48500,
        customerRetentionRate: 68.5,
        vendorApprovalRate: 92.3,
        platformUtilization: 78.9,
      },
    };
  }

  // Generate mock vendor analytics
  private generateMockVendorAnalytics(timeframe: string): VendorAnalytics {
    const days = TIMEFRAMES.find(t => t.value === timeframe)?.days || 30;
    
    return {
      overview: {
        totalProducts: 24,
        totalOrders: 156,
        totalRevenue: 485000,
        averageRating: 4.6,
        profileViews: 1250,
        conversionRate: 12.5,
        monthlyGrowth: 18.3,
        orderGrowth: 25.7,
      },
      charts: {
        orderVolume: this.generateTimeSeriesData(days, 3, 12),
        revenue: this.generateTimeSeriesData(days, 8000, 25000),
        productViews: this.generateTimeSeriesData(days, 20, 80),
        ratings: this.generateTimeSeriesData(days, 4.0, 5.0),
        topProducts: [
          { name: 'Bridal Makeup', value: 40, percentage: 40, color: CHART_COLORS[0] },
          { name: 'Wedding Photography', value: 30, percentage: 30, color: CHART_COLORS[1] },
          { name: 'Decoration Package', value: 20, percentage: 20, color: CHART_COLORS[2] },
          { name: 'Catering Service', value: 10, percentage: 10, color: CHART_COLORS[3] },
        ],
        orderSources: [
          { name: 'Direct Search', value: 45, percentage: 45, color: CHART_COLORS[0] },
          { name: 'Recommendations', value: 30, percentage: 30, color: CHART_COLORS[1] },
          { name: 'Social Media', value: 15, percentage: 15, color: CHART_COLORS[2] },
          { name: 'Others', value: 10, percentage: 10, color: CHART_COLORS[3] },
        ],
        customerLocations: [
          { name: 'Chennai', value: 35, percentage: 35, color: CHART_COLORS[0] },
          { name: 'Bangalore', value: 25, percentage: 25, color: CHART_COLORS[1] },
          { name: 'Hyderabad', value: 20, percentage: 20, color: CHART_COLORS[2] },
          { name: 'Others', value: 20, percentage: 20, color: CHART_COLORS[3] },
        ],
        orderStatus: [
          { name: 'Completed', value: 70, percentage: 70, color: CHART_COLORS[2] },
          { name: 'In Progress', value: 20, percentage: 20, color: CHART_COLORS[3] },
          { name: 'Pending', value: 8, percentage: 8, color: CHART_COLORS[4] },
          { name: 'Cancelled', value: 2, percentage: 2, color: CHART_COLORS[5] },
        ],
      },
      insights: {
        bestSellingProduct: 'Bridal Makeup Package',
        peakSalesDay: 'Saturday',
        averageOrderValue: 31100,
        customerRetentionRate: 45.2,
        responseTime: '2.3 hours',
        completionRate: 96.8,
      },
    };
  }

  // Helper methods for real data processing
  private calculateGrowthRate(data: any[], days: number): number {
    if (data.length === 0) return 0;

    const halfPoint = Math.floor(days / 2);
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - halfPoint);

    const recentCount = data.filter((item: any) => new Date(item.createdAt) >= cutoffDate).length;
    const olderCount = data.length - recentCount;

    return olderCount > 0 ? ((recentCount - olderCount) / olderCount) * 100 : 0;
  }

  private generateTimeSeriesFromData(data: any[], days: number, dateField: string): ChartDataPoint[] {
    const result: ChartDataPoint[] = [];
    const now = new Date();

    for (let i = days - 1; i >= 0; i--) {
      const date = new Date(now);
      date.setDate(date.getDate() - i);
      const dateStr = date.toISOString().split('T')[0];

      const count = data.filter((item: any) => {
        const itemDate = new Date(item[dateField]).toISOString().split('T')[0];
        return itemDate === dateStr;
      }).length;

      result.push({
        date: dateStr,
        value: count,
        label: date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' }),
      });
    }

    return result;
  }

  private generateRevenueTimeSeries(orders: any[], days: number): ChartDataPoint[] {
    const result: ChartDataPoint[] = [];
    const now = new Date();

    for (let i = days - 1; i >= 0; i--) {
      const date = new Date(now);
      date.setDate(date.getDate() - i);
      const dateStr = date.toISOString().split('T')[0];

      const dayRevenue = orders
        .filter((order: any) => {
          const orderDate = new Date(order.createdAt).toISOString().split('T')[0];
          return orderDate === dateStr;
        })
        .reduce((sum: number, order: any) => sum + (parseFloat(order.total) || 0), 0);

      result.push({
        date: dateStr,
        value: dayRevenue,
        label: date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' }),
      });
    }

    return result;
  }

  private generateCategoryData(vendors: any[]): PieChartDataPoint[] {
    const categoryCount: { [key: string]: number } = {};

    vendors.forEach((vendor: any) => {
      const category = vendor.category || 'Other';
      categoryCount[category] = (categoryCount[category] || 0) + 1;
    });

    const total = vendors.length;
    return Object.entries(categoryCount)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 6)
      .map(([name, value], index) => ({
        name,
        value,
        percentage: (value / total) * 100,
        color: CHART_COLORS[index % CHART_COLORS.length],
      }));
  }

  private generateOrderStatusData(orders: any[]): PieChartDataPoint[] {
    const statusCount: { [key: string]: number } = {};

    orders.forEach((order: any) => {
      const status = order.status || 'Pending';
      statusCount[status] = (statusCount[status] || 0) + 1;
    });

    const total = orders.length;
    return Object.entries(statusCount)
      .map(([name, value], index) => ({
        name,
        value,
        percentage: (value / total) * 100,
        color: CHART_COLORS[index % CHART_COLORS.length],
      }));
  }

  private generateLocationData(users: any[]): PieChartDataPoint[] {
    const locationCount: { [key: string]: number } = {};

    users.forEach((user: any) => {
      const city = user.city || 'Other';
      locationCount[city] = (locationCount[city] || 0) + 1;
    });

    const total = users.length;
    return Object.entries(locationCount)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 5)
      .map(([name, value], index) => ({
        name,
        value,
        percentage: (value / total) * 100,
        color: CHART_COLORS[index % CHART_COLORS.length],
      }));
  }

  private generateRevenueByCategoryData(orders: any[], shops: any[]): PieChartDataPoint[] {
    const categoryRevenue: { [key: string]: number } = {};

    orders.forEach((order: any) => {
      // Find the shop/product for this order
      const shop = shops.find((s: any) => s.id === order.productId);
      const category = shop?.category || 'Other';
      categoryRevenue[category] = (categoryRevenue[category] || 0) + (parseFloat(order.total) || 0);
    });

    const total = Object.values(categoryRevenue).reduce((sum: number, value: number) => sum + value, 0);
    return Object.entries(categoryRevenue)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 6)
      .map(([name, value], index) => ({
        name,
        value,
        percentage: total > 0 ? (value / total) * 100 : 0,
        color: CHART_COLORS[index % CHART_COLORS.length],
      }));
  }

  private getMostActiveDay(orders: any[]): string {
    const dayCount: { [key: string]: number } = {};
    const days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];

    orders.forEach((order: any) => {
      const dayIndex = new Date(order.createdAt).getDay();
      const dayName = days[dayIndex];
      dayCount[dayName] = (dayCount[dayName] || 0) + 1;
    });

    return Object.entries(dayCount)
      .sort(([,a], [,b]) => b - a)[0]?.[0] || 'Monday';
  }

  private getTopCategory(vendors: any[]): string {
    const categoryCount: { [key: string]: number } = {};

    vendors.forEach((vendor: any) => {
      const category = vendor.category || 'Other';
      categoryCount[category] = (categoryCount[category] || 0) + 1;
    });

    return Object.entries(categoryCount)
      .sort(([,a], [,b]) => b - a)[0]?.[0] || 'Photography';
  }

  private calculateConversionRate(users: any[], orders: any[]): number {
    if (users.length === 0) return 0;
    const uniqueOrderUsers = new Set(orders.map((order: any) => order.userId)).size;
    return (uniqueOrderUsers / users.length) * 100;
  }

  // Generate time series data (fallback for mock data)
  private generateTimeSeriesData(days: number, min: number, max: number): ChartDataPoint[] {
    const data: ChartDataPoint[] = [];
    const now = new Date();
    
    for (let i = days - 1; i >= 0; i--) {
      const date = new Date(now);
      date.setDate(date.getDate() - i);
      
      const value = Math.floor(Math.random() * (max - min + 1)) + min;
      
      data.push({
        date: date.toISOString().split('T')[0],
        value,
        label: date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' }),
      });
    }
    
    return data;
  }

  // Format currency
  formatCurrency(amount: number): string {
    if (amount >= 10000000) {
      return `₹${(amount / 10000000).toFixed(1)}Cr`;
    } else if (amount >= 100000) {
      return `₹${(amount / 100000).toFixed(1)}L`;
    } else if (amount >= 1000) {
      return `₹${(amount / 1000).toFixed(1)}K`;
    }
    return `₹${amount.toLocaleString()}`;
  }

  // Format percentage
  formatPercentage(value: number): string {
    return `${value.toFixed(1)}%`;
  }

  // Format number
  formatNumber(value: number): string {
    if (value >= 1000000) {
      return `${(value / 1000000).toFixed(1)}M`;
    } else if (value >= 1000) {
      return `${(value / 1000).toFixed(1)}K`;
    }
    return value.toLocaleString();
  }
}

export const analyticsService = new AnalyticsService();
