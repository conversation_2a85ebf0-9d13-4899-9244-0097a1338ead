import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Alert,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { useTheme } from '../../providers/ThemeProvider';
import { Button } from '../../components/ui/Button';
import { Input } from '../../components/ui/Input';
import { Card } from '../../components/ui/Card';

export default function VendorSignupScreen() {
  const navigation = useNavigation();
  const { theme } = useTheme();
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    businessName: '',
    ownerName: '',
    email: '',
    phone: '',
    password: '',
    confirmPassword: '',
    category: '',
    city: '',
    state: '',
    experience: '',
    description: '',
  });

  const vendorCategories = [
    'Photography',
    'Videography',
    'Catering',
    'Decoration',
    'Makeup Artist',
    'Mehendi Artist',
    'DJ/Music',
    'Wedding Planner',
    'Florist',
    'Transportation',
    'Other',
  ];

  const handleSignup = async () => {
    if (!formData.businessName || !formData.email || !formData.password) {
      Alert.alert('Error', 'Please fill in all required fields');
      return;
    }

    if (formData.password !== formData.confirmPassword) {
      Alert.alert('Error', 'Passwords do not match');
      return;
    }

    setLoading(true);
    try {
      // TODO: Implement vendor signup API
      await new Promise(resolve => setTimeout(resolve, 2000)); // Simulate API call
      
      Alert.alert(
        'Application Submitted',
        'Your vendor application has been submitted successfully. We will review and get back to you within 2-3 business days.',
        [
          {
            text: 'OK',
            onPress: () => navigation.goBack(),
          },
        ]
      );
    } catch (error: any) {
      Alert.alert('Signup Failed', error.message || 'Please try again');
    } finally {
      setLoading(false);
    }
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    scrollContainer: {
      flexGrow: 1,
      padding: 20,
    },
    header: {
      alignItems: 'center',
      marginBottom: 32,
    },
    title: {
      fontSize: 28,
      fontWeight: 'bold',
      color: theme.colors.primary,
      marginBottom: 8,
    },
    subtitle: {
      fontSize: 16,
      color: theme.colors.textSecondary,
      textAlign: 'center',
      lineHeight: 24,
    },
    form: {
      gap: 16,
    },
    sectionTitle: {
      fontSize: 18,
      fontWeight: '600',
      color: theme.colors.text,
      marginTop: 16,
      marginBottom: 8,
    },
    inputContainer: {
      gap: 8,
    },
    label: {
      fontSize: 16,
      fontWeight: '600',
      color: theme.colors.text,
    },
    requiredLabel: {
      color: theme.colors.destructive,
    },
    row: {
      flexDirection: 'row',
      gap: 12,
    },
    halfInput: {
      flex: 1,
    },
    categoryGrid: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      gap: 8,
      marginTop: 8,
    },
    categoryChip: {
      paddingHorizontal: 12,
      paddingVertical: 8,
      borderRadius: 20,
      borderWidth: 1,
      borderColor: theme.colors.border,
      backgroundColor: theme.colors.background,
    },
    selectedCategory: {
      backgroundColor: theme.colors.primary,
      borderColor: theme.colors.primary,
    },
    categoryText: {
      fontSize: 14,
      color: theme.colors.text,
    },
    selectedCategoryText: {
      color: theme.colors.primaryForeground,
    },
    textArea: {
      minHeight: 80,
      textAlignVertical: 'top',
    },
    signupButton: {
      marginTop: 24,
    },
    loginContainer: {
      flexDirection: 'row',
      justifyContent: 'center',
      alignItems: 'center',
      marginTop: 24,
    },
    loginText: {
      color: theme.colors.textSecondary,
      fontSize: 14,
    },
    loginLink: {
      color: theme.colors.primary,
      fontSize: 14,
      fontWeight: '600',
      marginLeft: 4,
    },
    benefits: {
      marginTop: 24,
      padding: 16,
      backgroundColor: theme.colors.secondary + '20',
      borderRadius: 8,
    },
    benefitsTitle: {
      fontSize: 16,
      fontWeight: '600',
      color: theme.colors.text,
      marginBottom: 8,
    },
    benefitItem: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: 6,
    },
    benefitIcon: {
      fontSize: 16,
      marginRight: 8,
      color: theme.colors.secondary,
    },
    benefitText: {
      fontSize: 14,
      color: theme.colors.textSecondary,
      flex: 1,
    },
  });

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <ScrollView
        contentContainerStyle={styles.scrollContainer}
        keyboardShouldPersistTaps="handled"
      >
        <View style={styles.header}>
          <Text style={styles.title}>Join as Vendor</Text>
          <Text style={styles.subtitle}>
            Grow your wedding business with BookmyFestive
          </Text>
        </View>

        <Card>
          <View style={styles.form}>
            <Text style={styles.sectionTitle}>Business Information</Text>
            
            <View style={styles.inputContainer}>
              <Text style={styles.label}>
                Business Name <Text style={styles.requiredLabel}>*</Text>
              </Text>
              <Input
                placeholder="Enter your business name"
                value={formData.businessName}
                onChangeText={(text) =>
                  setFormData({ ...formData, businessName: text })
                }
              />
            </View>

            <View style={styles.inputContainer}>
              <Text style={styles.label}>
                Owner Name <Text style={styles.requiredLabel}>*</Text>
              </Text>
              <Input
                placeholder="Enter owner's full name"
                value={formData.ownerName}
                onChangeText={(text) =>
                  setFormData({ ...formData, ownerName: text })
                }
              />
            </View>

            <View style={styles.inputContainer}>
              <Text style={styles.label}>
                Service Category <Text style={styles.requiredLabel}>*</Text>
              </Text>
              <View style={styles.categoryGrid}>
                {vendorCategories.map((category) => (
                  <Button
                    key={category}
                    variant="ghost"
                    size="sm"
                    style={[
                      styles.categoryChip,
                      formData.category === category && styles.selectedCategory,
                    ]}
                    onPress={() =>
                      setFormData({ ...formData, category })
                    }
                  >
                    <Text
                      style={[
                        styles.categoryText,
                        formData.category === category && styles.selectedCategoryText,
                      ]}
                    >
                      {category}
                    </Text>
                  </Button>
                ))}
              </View>
            </View>

            <Text style={styles.sectionTitle}>Contact Information</Text>

            <View style={styles.inputContainer}>
              <Text style={styles.label}>
                Email <Text style={styles.requiredLabel}>*</Text>
              </Text>
              <Input
                placeholder="Enter your email"
                value={formData.email}
                onChangeText={(text) =>
                  setFormData({ ...formData, email: text })
                }
                keyboardType="email-address"
                autoCapitalize="none"
              />
            </View>

            <View style={styles.inputContainer}>
              <Text style={styles.label}>
                Phone Number <Text style={styles.requiredLabel}>*</Text>
              </Text>
              <Input
                placeholder="Enter your phone number"
                value={formData.phone}
                onChangeText={(text) =>
                  setFormData({ ...formData, phone: text })
                }
                keyboardType="phone-pad"
              />
            </View>

            <View style={styles.row}>
              <View style={[styles.inputContainer, styles.halfInput]}>
                <Text style={styles.label}>City</Text>
                <Input
                  placeholder="City"
                  value={formData.city}
                  onChangeText={(text) =>
                    setFormData({ ...formData, city: text })
                  }
                />
              </View>
              <View style={[styles.inputContainer, styles.halfInput]}>
                <Text style={styles.label}>State</Text>
                <Input
                  placeholder="State"
                  value={formData.state}
                  onChangeText={(text) =>
                    setFormData({ ...formData, state: text })
                  }
                />
              </View>
            </View>

            <Text style={styles.sectionTitle}>Account Security</Text>

            <View style={styles.inputContainer}>
              <Text style={styles.label}>
                Password <Text style={styles.requiredLabel}>*</Text>
              </Text>
              <Input
                placeholder="Create a password"
                value={formData.password}
                onChangeText={(text) =>
                  setFormData({ ...formData, password: text })
                }
                secureTextEntry
              />
            </View>

            <View style={styles.inputContainer}>
              <Text style={styles.label}>
                Confirm Password <Text style={styles.requiredLabel}>*</Text>
              </Text>
              <Input
                placeholder="Confirm your password"
                value={formData.confirmPassword}
                onChangeText={(text) =>
                  setFormData({ ...formData, confirmPassword: text })
                }
                secureTextEntry
              />
            </View>

            <Button
              onPress={handleSignup}
              loading={loading}
              style={styles.signupButton}
            >
              Submit Application
            </Button>
          </View>
        </Card>

        <View style={styles.benefits}>
          <Text style={styles.benefitsTitle}>Why join BookmyFestive?</Text>
          <View style={styles.benefitItem}>
            <Text style={styles.benefitIcon}>✓</Text>
            <Text style={styles.benefitText}>Reach thousands of potential customers</Text>
          </View>
          <View style={styles.benefitItem}>
            <Text style={styles.benefitIcon}>✓</Text>
            <Text style={styles.benefitText}>Manage bookings and orders easily</Text>
          </View>
          <View style={styles.benefitItem}>
            <Text style={styles.benefitIcon}>✓</Text>
            <Text style={styles.benefitText}>Secure payment processing</Text>
          </View>
          <View style={styles.benefitItem}>
            <Text style={styles.benefitIcon}>✓</Text>
            <Text style={styles.benefitText}>24/7 customer support</Text>
          </View>
        </View>

        <View style={styles.loginContainer}>
          <Text style={styles.loginText}>Already have an account?</Text>
          <Button
            variant="ghost"
            size="sm"
            onPress={() => navigation.navigate('Login' as never)}
          >
            <Text style={styles.loginLink}>Sign In</Text>
          </Button>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
}
