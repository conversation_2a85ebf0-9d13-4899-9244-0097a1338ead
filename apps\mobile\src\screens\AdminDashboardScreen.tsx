import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  RefreshControl,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { useTheme } from '../providers/ThemeProvider';
import { useAuth } from '../providers/AuthProvider';
import LoadingSpinner from '../components/LoadingSpinner';

interface AdminStats {
  totalUsers: number;
  totalVendors: number;
  totalOrders: number;
  totalRevenue: number;
  monthlyRevenue: number;
  pendingApprovals: number;
  activeProducts: number;
  totalReviews: number;
  platformGrowth: number;
}

interface RecentActivity {
  id: string;
  type: 'user_registration' | 'vendor_signup' | 'order_placed' | 'review_submitted' | 'product_added';
  title: string;
  description: string;
  timestamp: string;
  status?: 'pending' | 'approved' | 'rejected';
}

export default function AdminDashboardScreen() {
  const navigation = useNavigation();
  const { theme } = useTheme();
  const { user, userProfile } = useAuth();
  
  const [stats, setStats] = useState<AdminStats | null>(null);
  const [recentActivity, setRecentActivity] = useState<RecentActivity[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      
      // Fetch real data from GraphQL API
      const [usersResult, vendorsResult, ordersResult, reviewsResult, shopsResult] = await Promise.all([
        graphqlService.listUserProfiles({}, 1000),
        graphqlService.listVendors({}, 1000),
        graphqlService.listOrders({}, 1000),
        graphqlService.listReviews({}, 1000),
        graphqlService.listShops({}, 1000),
      ]);

      // Calculate stats from real data
      const totalUsers = usersResult.items.length;
      const totalVendors = vendorsResult.items.length;
      const totalOrders = ordersResult.items.length;
      const totalReviews = reviewsResult.items.length;
      const activeProducts = shopsResult.items.filter((item: any) => item.status === 'ACTIVE').length;

      // Calculate revenue from orders
      const totalRevenue = ordersResult.items.reduce((sum: number, order: any) => {
        return sum + (parseFloat(order.total) || 0);
      }, 0);

      // Calculate monthly revenue (current month)
      const currentMonth = new Date().getMonth();
      const currentYear = new Date().getFullYear();
      const monthlyRevenue = ordersResult.items
        .filter((order: any) => {
          const orderDate = new Date(order.createdAt);
          return orderDate.getMonth() === currentMonth && orderDate.getFullYear() === currentYear;
        })
        .reduce((sum: number, order: any) => sum + (parseFloat(order.total) || 0), 0);

      // Count pending approvals (vendors with pending status)
      const pendingApprovals = vendorsResult.items.filter((vendor: any) =>
        vendor.status === 'PENDING' || !vendor.verified
      ).length;

      // Calculate platform growth (simplified - comparing this month vs last month users)
      const lastMonth = currentMonth === 0 ? 11 : currentMonth - 1;
      const lastMonthYear = currentMonth === 0 ? currentYear - 1 : currentYear;

      const thisMonthUsers = usersResult.items.filter((user: any) => {
        const userDate = new Date(user.createdAt);
        return userDate.getMonth() === currentMonth && userDate.getFullYear() === currentYear;
      }).length;

      const lastMonthUsers = usersResult.items.filter((user: any) => {
        const userDate = new Date(user.createdAt);
        return userDate.getMonth() === lastMonth && userDate.getFullYear() === lastMonthYear;
      }).length;

      const platformGrowth = lastMonthUsers > 0 ? ((thisMonthUsers - lastMonthUsers) / lastMonthUsers) * 100 : 0;

      const realStats: AdminStats = {
        totalUsers,
        totalVendors,
        totalOrders,
        totalRevenue,
        monthlyRevenue,
        pendingApprovals,
        activeProducts,
        totalReviews,
        platformGrowth,
      };

      // Generate recent activity from real data
      const recentActivity: RecentActivity[] = [];

      // Add recent user registrations
      const recentUsers = usersResult.items
        .sort((a: any, b: any) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
        .slice(0, 2);

      recentUsers.forEach((user: any) => {
        recentActivity.push({
          id: `USER_${user.id}`,
          type: 'user_registration',
          title: 'New User Registration',
          description: `${user.firstName || 'User'} ${user.lastName || ''} joined the platform`,
          timestamp: user.createdAt,
        });
      });

      // Add recent vendor registrations
      const recentVendors = vendorsResult.items
        .sort((a: any, b: any) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
        .slice(0, 2);

      recentVendors.forEach((vendor: any) => {
        recentActivity.push({
          id: `VENDOR_${vendor.id}`,
          type: 'vendor_signup',
          title: 'New Vendor Registration',
          description: `${vendor.name} applied for vendor account`,
          timestamp: vendor.createdAt,
          status: vendor.verified ? undefined : 'pending',
        });
      });

      // Add recent orders
      const recentOrders = ordersResult.items
        .sort((a: any, b: any) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
        .slice(0, 2);

      recentOrders.forEach((order: any) => {
        recentActivity.push({
          id: `ORDER_${order.id}`,
          type: 'order_placed',
          title: 'New Order Placed',
          description: `Order ${order.orderNumber || order.id} for ₹${order.total}`,
          timestamp: order.createdAt,
        });
      });

      // Sort activity by timestamp
      recentActivity.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());

      setStats(realStats);
      setRecentActivity(recentActivity.slice(0, 10)); // Show top 10 activities
    } catch (error) {
      console.error('Failed to load admin dashboard data:', error);
      Alert.alert('Error', 'Failed to load dashboard data');
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadDashboardData();
    setRefreshing(false);
  };

  const formatCurrency = (amount: number) => {
    return `₹${(amount / 100000).toFixed(1)}L`;
  };

  const getActivityIcon = (type: RecentActivity['type']) => {
    switch (type) {
      case 'user_registration':
        return 'person-add-outline';
      case 'vendor_signup':
        return 'business-outline';
      case 'order_placed':
        return 'bag-outline';
      case 'review_submitted':
        return 'star-outline';
      case 'product_added':
        return 'cube-outline';
      default:
        return 'notifications-outline';
    }
  };

  const getActivityColor = (type: RecentActivity['type']) => {
    switch (type) {
      case 'user_registration':
        return theme.colors.success;
      case 'vendor_signup':
        return theme.colors.primary;
      case 'order_placed':
        return theme.colors.info;
      case 'review_submitted':
        return theme.colors.warning;
      case 'product_added':
        return theme.colors.secondary;
      default:
        return theme.colors.textSecondary;
    }
  };

  const quickActions = [
    {
      icon: 'people-outline',
      title: 'Manage Users',
      subtitle: 'View all users',
      onPress: () => navigation.navigate('AdminUsers' as never),
      color: theme.colors.primary,
    },
    {
      icon: 'business-outline',
      title: 'Vendor Approvals',
      subtitle: `${stats?.pendingApprovals || 0} pending`,
      onPress: () => navigation.navigate('AdminVendorApprovals' as never),
      color: theme.colors.warning,
    },
    {
      icon: 'receipt-outline',
      title: 'Order Management',
      subtitle: 'Track all orders',
      onPress: () => navigation.navigate('AdminOrders' as never),
      color: theme.colors.info,
    },
    {
      icon: 'analytics-outline',
      title: 'Analytics',
      subtitle: 'Platform insights',
      onPress: () => navigation.navigate('AdminAnalytics' as never),
      color: theme.colors.success,
    },
    {
      icon: 'settings-outline',
      title: 'Platform Settings',
      subtitle: 'Configure app',
      onPress: () => navigation.navigate('AdminSettings' as never),
      color: theme.colors.secondary,
    },
    {
      icon: 'flag-outline',
      title: 'Content Moderation',
      subtitle: 'Review reports',
      onPress: () => navigation.navigate('AdminModeration' as never),
      color: theme.colors.error,
    },
  ];

  if (loading) {
    return <LoadingSpinner fullScreen text="Loading admin dashboard..." />;
  }

  return (
    <ScrollView 
      style={[styles.container, { backgroundColor: theme.colors.background }]}
      refreshControl={
        <RefreshControl
          refreshing={refreshing}
          onRefresh={onRefresh}
          colors={[theme.colors.primary]}
          tintColor={theme.colors.primary}
        />
      }
    >
      {/* Welcome Header */}
      <View style={[styles.header, { backgroundColor: theme.colors.primary }]}>
        <View style={styles.welcomeSection}>
          <Text style={styles.welcomeText}>Admin Dashboard</Text>
          <Text style={styles.adminName}>
            {user?.fullName || 'Administrator'}
          </Text>
          <Text style={styles.platformName}>BookmyFestive Platform</Text>
        </View>
        
        <TouchableOpacity 
          style={styles.profileButton}
          onPress={() => navigation.navigate('Profile' as never)}
        >
          <Ionicons name="person-circle-outline" size={32} color="white" />
        </TouchableOpacity>
      </View>

      {/* Stats Overview */}
      {stats && (
        <View style={styles.statsContainer}>
          <View style={styles.statsGrid}>
            <View style={[styles.statCard, { backgroundColor: theme.colors.surface }]}>
              <Ionicons name="people-outline" size={24} color={theme.colors.primary} />
              <Text style={[styles.statNumber, { color: theme.colors.text }]}>
                {stats.totalUsers.toLocaleString()}
              </Text>
              <Text style={[styles.statLabel, { color: theme.colors.textSecondary }]}>
                Total Users
              </Text>
            </View>

            <View style={[styles.statCard, { backgroundColor: theme.colors.surface }]}>
              <Ionicons name="business-outline" size={24} color={theme.colors.success} />
              <Text style={[styles.statNumber, { color: theme.colors.text }]}>
                {stats.totalVendors}
              </Text>
              <Text style={[styles.statLabel, { color: theme.colors.textSecondary }]}>
                Vendors
              </Text>
            </View>

            <View style={[styles.statCard, { backgroundColor: theme.colors.surface }]}>
              <Ionicons name="receipt-outline" size={24} color={theme.colors.info} />
              <Text style={[styles.statNumber, { color: theme.colors.text }]}>
                {stats.totalOrders.toLocaleString()}
              </Text>
              <Text style={[styles.statLabel, { color: theme.colors.textSecondary }]}>
                Total Orders
              </Text>
            </View>

            <View style={[styles.statCard, { backgroundColor: theme.colors.surface }]}>
              <Ionicons name="trending-up-outline" size={24} color={theme.colors.warning} />
              <Text style={[styles.statNumber, { color: theme.colors.text }]}>
                +{stats.platformGrowth}%
              </Text>
              <Text style={[styles.statLabel, { color: theme.colors.textSecondary }]}>
                Growth
              </Text>
            </View>
          </View>

          {/* Revenue Card */}
          <View style={[styles.revenueCard, { backgroundColor: theme.colors.surface }]}>
            <View style={styles.revenueHeader}>
              <View>
                <Text style={[styles.revenueTitle, { color: theme.colors.text }]}>
                  Platform Revenue
                </Text>
                <Text style={[styles.revenueAmount, { color: theme.colors.primary }]}>
                  {formatCurrency(stats.totalRevenue)}
                </Text>
                <Text style={[styles.monthlyRevenue, { color: theme.colors.textSecondary }]}>
                  This month: {formatCurrency(stats.monthlyRevenue)}
                </Text>
              </View>
              
              <View style={styles.pendingContainer}>
                <Ionicons name="time-outline" size={20} color={theme.colors.warning} />
                <Text style={[styles.pendingText, { color: theme.colors.warning }]}>
                  {stats.pendingApprovals} pending approvals
                </Text>
              </View>
            </View>
          </View>
        </View>
      )}

      {/* Quick Actions */}
      <View style={styles.section}>
        <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
          Quick Actions
        </Text>
        
        <View style={styles.actionsGrid}>
          {quickActions.map((action, index) => (
            <TouchableOpacity
              key={index}
              style={[styles.actionCard, { backgroundColor: theme.colors.surface }]}
              onPress={action.onPress}
            >
              <Ionicons name={action.icon as any} size={24} color={action.color} />
              <Text style={[styles.actionTitle, { color: theme.colors.text }]}>
                {action.title}
              </Text>
              <Text style={[styles.actionSubtitle, { color: theme.colors.textSecondary }]}>
                {action.subtitle}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>

      {/* Recent Activity */}
      <View style={styles.section}>
        <View style={styles.sectionHeader}>
          <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
            Recent Activity
          </Text>
          <TouchableOpacity onPress={() => navigation.navigate('AdminActivity' as never)}>
            <Text style={[styles.viewAllText, { color: theme.colors.primary }]}>
              View All
            </Text>
          </TouchableOpacity>
        </View>

        {recentActivity.map((activity) => (
          <View
            key={activity.id}
            style={[styles.activityCard, { backgroundColor: theme.colors.surface }]}
          >
            <View style={styles.activityIcon}>
              <Ionicons 
                name={getActivityIcon(activity.type) as any} 
                size={20} 
                color={getActivityColor(activity.type)} 
              />
            </View>
            
            <View style={styles.activityContent}>
              <Text style={[styles.activityTitle, { color: theme.colors.text }]}>
                {activity.title}
              </Text>
              <Text style={[styles.activityDescription, { color: theme.colors.textSecondary }]}>
                {activity.description}
              </Text>
              <Text style={[styles.activityTime, { color: theme.colors.textSecondary }]}>
                {new Date(activity.timestamp).toLocaleString()}
              </Text>
            </View>
            
            {activity.status && (
              <View style={[styles.statusBadge, { 
                backgroundColor: activity.status === 'pending' ? theme.colors.warning + '20' : theme.colors.success + '20' 
              }]}>
                <Text style={[styles.statusText, { 
                  color: activity.status === 'pending' ? theme.colors.warning : theme.colors.success 
                }]}>
                  {activity.status.toUpperCase()}
                </Text>
              </View>
            )}
          </View>
        ))}
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    paddingTop: 40,
  },
  welcomeSection: {
    flex: 1,
  },
  welcomeText: {
    color: 'white',
    fontSize: 16,
    opacity: 0.9,
  },
  adminName: {
    color: 'white',
    fontSize: 24,
    fontWeight: 'bold',
    marginTop: 4,
  },
  platformName: {
    color: 'white',
    fontSize: 14,
    opacity: 0.8,
    marginTop: 2,
  },
  profileButton: {
    padding: 8,
  },
  statsContainer: {
    padding: 16,
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
    marginBottom: 16,
  },
  statCard: {
    flex: 1,
    minWidth: '45%',
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  statNumber: {
    fontSize: 20,
    fontWeight: 'bold',
    marginTop: 8,
  },
  statLabel: {
    fontSize: 12,
    marginTop: 4,
  },
  revenueCard: {
    padding: 16,
    borderRadius: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  revenueHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  revenueTitle: {
    fontSize: 14,
    marginBottom: 4,
  },
  revenueAmount: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  monthlyRevenue: {
    fontSize: 12,
    marginTop: 4,
  },
  pendingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  pendingText: {
    fontSize: 12,
    fontWeight: '600',
  },
  section: {
    padding: 16,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  viewAllText: {
    fontSize: 14,
    fontWeight: '600',
  },
  actionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  actionCard: {
    flex: 1,
    minWidth: '45%',
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  actionTitle: {
    fontSize: 14,
    fontWeight: '600',
    marginTop: 8,
    textAlign: 'center',
  },
  actionSubtitle: {
    fontSize: 12,
    marginTop: 2,
    textAlign: 'center',
  },
  activityCard: {
    flexDirection: 'row',
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
    alignItems: 'flex-start',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  activityIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(0,0,0,0.05)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  activityContent: {
    flex: 1,
  },
  activityTitle: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 2,
  },
  activityDescription: {
    fontSize: 12,
    marginBottom: 4,
  },
  activityTime: {
    fontSize: 10,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    fontSize: 10,
    fontWeight: '600',
  },
});
