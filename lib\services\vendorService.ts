import { generateClient } from 'aws-amplify/api';
import { getCurrentUser } from 'aws-amplify/auth';
import { createVendor, updateVendor, deleteVendor } from '@/src/graphql/mutations';
import { listVendors, getVendor, vendorsByUserId } from '@/src/graphql/queries';
import { handleVendorOperation } from '@/lib/dynamodb-error-handler';

// Create client with user pool authentication for authenticated operations
const authenticatedClient = generateClient({
  authMode: 'userPool'
});

// Create client with API key for public operations
const publicClient = generateClient({
  authMode: 'apiKey'
});

export interface VendorData {
  id?: string;
  name: string;
  category: string;
  description: string;
  contact: string;
  email: string;
  address: string;
  city: string;
  state: string;
  pincode: string;
  website: string;
  socialMedia: {
    facebook: string;
    instagram: string;
    youtube: string;
  };
  profilePhoto: string;
  gallery: string[];
  services: Array<{
    name: string;
    price: string;
    description?: string;
  }>;
  experience: string;
  events: string;
  responseTime: string;
  rating: number;
  reviewCount: number;
  verified: boolean;
  featured: boolean;
  availability: string;
  priceRange: string;
  specializations: string[];
  awards: string[];
  languages: string[];
  coverage: string[];
  equipment: string[];
  status: string;
}

export interface VendorResponse {
  id: string;
  userId: string;
  name: string;
  category: string;
  description?: string;
  contact: string;
  email?: string;
  address?: string;
  city: string;
  state: string;
  pincode?: string;
  website?: string;
  socialMedia?: {
    facebook?: string;
    instagram?: string;
    youtube?: string;
  };
  profilePhoto?: string;
  gallery?: string[];
  services?: Array<{
    name?: string;
    price?: string;
    description?: string;
  }>;
  experience?: string;
  events?: string;
  responseTime?: string;
  rating?: number;
  reviewCount?: number;
  verified?: boolean;
  featured?: boolean;
  availability?: string;
  priceRange?: string;
  specializations?: string[];
  awards?: string[];
  languages?: string[];
  coverage?: string[];
  equipment?: string[];
  status?: string;
  createdAt: string;
  updatedAt: string;
}

class VendorService {
  /**
   * Get current user ID
   */
  private async getCurrentUserId(): Promise<string> {
    try {
      const user = await getCurrentUser();
      return user.userId;
    } catch (error) {
      throw new Error('User not authenticated');
    }
  }

  /**
   * Create a new vendor
   */
  async createVendor(vendorData: VendorData): Promise<VendorResponse> {
    let input: any = null;

    try {
      const userId = await this.getCurrentUserId();

      // Filter out empty services and ensure only valid fields
      // Temporarily removing description field until schema is deployed
      const validServices = vendorData.services
        .filter(service => service.name && service.name.trim() !== '')
        .map(service => ({
          name: service.name,
          price: service.price
          // description: service.description // Temporarily commented out
        }));

      // Temporarily exclude socialMedia until schema is deployed
      input = {
        userId,
        name: vendorData.name,
        category: vendorData.category,
        description: vendorData.description,
        contact: vendorData.contact,
        email: vendorData.email,
        address: vendorData.address,
        city: vendorData.city,
        state: vendorData.state,
        pincode: vendorData.pincode,
        website: vendorData.website,
        // socialMedia: vendorData.socialMedia, // Temporarily commented out
        profilePhoto: vendorData.profilePhoto,
        gallery: vendorData.gallery,
        services: validServices,
        experience: vendorData.experience,
        events: vendorData.events,
        responseTime: vendorData.responseTime,
        rating: vendorData.rating,
        reviewCount: vendorData.reviewCount,
        verified: vendorData.verified,
        featured: vendorData.featured,
        availability: vendorData.availability,
        priceRange: vendorData.priceRange,
        specializations: vendorData.specializations,
        awards: vendorData.awards,
        languages: vendorData.languages,
        coverage: vendorData.coverage,
        equipment: vendorData.equipment,
        status: vendorData.status
      };

      console.log('Creating vendor with input:', JSON.stringify(input, null, 2));

      return await handleVendorOperation(async (data) => {
        const result = await authenticatedClient.graphql({
          query: createVendor,
          variables: { input: data }
        });
        return result.data.createVendor;
      }, input);
    } catch (error) {
      console.error('Error creating vendor:', error);
      if (input) {
        console.error('Input that caused error:', JSON.stringify(input, null, 2));
      }
      throw new Error('Failed to create vendor');
    }
  }

  /**
   * Update an existing vendor
   */
  async updateVendor(vendorId: string, vendorData: Partial<VendorData>): Promise<VendorResponse> {
    let input: any = null;

    try {
      const userId = await this.getCurrentUserId();

      // First verify that the vendor belongs to the current user
      const existingVendor = await this.getVendor(vendorId);
      if (existingVendor.userId !== userId) {
        throw new Error('Unauthorized: You can only update your own vendors');
      }

      // Filter out empty services if services are being updated
      const processedData = { ...vendorData };
      if (processedData.services) {
        // Temporarily removing description field until schema is deployed
        processedData.services = processedData.services
          .filter(service => service.name && service.name.trim() !== '')
          .map(service => ({
            name: service.name,
            price: service.price
            // description: service.description // Temporarily commented out
          }));
      }

      // Temporarily exclude socialMedia until schema is deployed
      if (processedData.socialMedia) {
        delete processedData.socialMedia;
      }

      input = {
        id: vendorId,
        ...processedData
      };

      console.log('Updating vendor with input:', JSON.stringify(input, null, 2));

      return await handleVendorOperation(async (data) => {
        const result = await authenticatedClient.graphql({
          query: updateVendor,
          variables: { input: data }
        });
        return result.data.updateVendor;
      }, input);
    } catch (error) {
      console.error('Error updating vendor:', error);
      if (input) {
        console.error('Input that caused error:', JSON.stringify(input, null, 2));
      }
      throw new Error('Failed to update vendor');
    }
  }

  /**
   * Delete a vendor
   */
  async deleteVendor(vendorId: string): Promise<boolean> {
    try {
      const userId = await this.getCurrentUserId();
      
      // First verify that the vendor belongs to the current user
      const existingVendor = await this.getVendor(vendorId);
      if (existingVendor.userId !== userId) {
        throw new Error('Unauthorized: You can only delete your own vendors');
      }

      await authenticatedClient.graphql({
        query: deleteVendor,
        variables: { input: { id: vendorId } }
      });

      return true;
    } catch (error) {
      console.error('Error deleting vendor:', error);
      throw new Error('Failed to delete vendor');
    }
  }

  /**
   * Get a specific vendor by ID
   */
  async getVendor(vendorId: string): Promise<VendorResponse> {
    try {
      const result = await publicClient.graphql({
        query: getVendor,
        variables: { id: vendorId }
      });

      return result.data.getVendor;
    } catch (error) {
      console.error('Error fetching vendor:', error);
      throw new Error('Failed to fetch vendor');
    }
  }

  /**
   * Get all vendors for the current user
   */
  async getUserVendors(): Promise<VendorResponse[]> {
    try {
      const userId = await this.getCurrentUserId();

      const result = await authenticatedClient.graphql({
        query: vendorsByUserId,
        variables: {
          userId: userId
        }
      });

      return result.data.vendorsByUserId.items;
    } catch (error) {
      console.error('Error fetching user vendors:', error);
      throw new Error('Failed to fetch vendors');
    }
  }

  /**
   * Get all vendors (public - for browsing)
   */
  async getAllVendors(limit: number = 10, nextToken?: string, dateFilter?: string): Promise<{
    vendors: VendorResponse[];
    nextToken?: string;
  }> {
    try {
      const result = await publicClient.graphql({
        query: listVendors,
        variables: {
          limit,
          nextToken,
          filter: {
            status: { eq: 'active' }
          }
        }
      });

      let vendors = result.data.listVendors.items;

      // Client-side date filtering if dateFilter is provided
      // In a real implementation, this would be done server-side with proper date availability fields
      if (dateFilter) {
        vendors = vendors.filter(vendor => {
          // For now, we assume all vendors are available unless they have specific unavailable dates
          // This logic can be enhanced when proper date availability fields are added to the schema
          return true; // Default to available
        });
      }

      return {
        vendors,
        nextToken: result.data.listVendors.nextToken
      };
    } catch (error) {
      console.error('Error fetching all vendors:', error);
      throw new Error('Failed to fetch vendors');
    }
  }

  /**
   * List vendors with pagination
   */
  async listVendors(filter?: any, limit: number = 10, nextToken?: string): Promise<{
    items: VendorResponse[];
    nextToken?: string;
  }> {
    try {
      const result = await publicClient.graphql({
        query: listVendors,
        variables: { filter, limit, nextToken }
      });
      return result.data.listVendors;
    } catch (error) {
      console.error('Error listing vendors:', error);
      throw error;
    }
  }

  /**
   * Search vendors by category
   */
  async getVendorsByCategory(category: string): Promise<VendorResponse[]> {
    try {
      const result = await publicClient.graphql({
        query: listVendors,
        variables: {
          filter: {
            category: { eq: category },
            status: { eq: 'active' }
          }
        }
      });

      return result.data.listVendors.items;
    } catch (error) {
      console.error('Error fetching vendors by category:', error);
      throw new Error('Failed to fetch vendors by category');
    }
  }

  /**
   * Search vendors by city
   */
  async getVendorsByCity(city: string): Promise<VendorResponse[]> {
    try {
      const result = await publicClient.graphql({
        query: listVendors,
        variables: {
          filter: {
            city: { eq: city },
            status: { eq: 'active' }
          }
        }
      });

      return result.data.listVendors.items;
    } catch (error) {
      console.error('Error fetching vendors by city:', error);
      throw new Error('Failed to fetch vendors by city');
    }
  }
}

export const vendorService = new VendorService();
