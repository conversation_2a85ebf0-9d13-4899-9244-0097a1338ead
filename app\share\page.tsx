'use client';

import React, { useEffect, useState } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Share2, Home, Copy, CheckCircle, Info, Facebook, Twitter, Linkedin, MessageCircle } from 'lucide-react';
import { showToast } from '@/lib/toast';

interface SharedData {
  title?: string;
  text?: string;
  url?: string;
  files?: FileList;
}

export default function SharePage() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const [sharedData, setSharedData] = useState<SharedData | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const handleSharedData = async () => {
      try {
        // Get shared data from URL parameters
        const title = searchParams.get('title');
        const text = searchParams.get('text');
        const url = searchParams.get('url');
        const protocol = searchParams.get('protocol');

        if (title || text || url || protocol) {
          setSharedData({
            title: title || '',
            text: text || '',
            url: url || ''
          });
        }

        setLoading(false);
      } catch (error) {
        console.error('Error handling shared data:', error);
        setLoading(false);
      }
    };

    handleSharedData();
  }, [searchParams]);

  const handleGoHome = () => {
    router.push('/');
  };

  const handleShareToSocial = (platform: string) => {
    if (!sharedData) return;

    const shareText = `${sharedData.title} - ${sharedData.text}`;
    const shareUrl = sharedData.url || window.location.origin;

    let socialUrl = '';
    switch (platform) {
      case 'facebook':
        socialUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(shareUrl)}&quote=${encodeURIComponent(shareText)}`;
        break;
      case 'twitter':
        socialUrl = `https://twitter.com/intent/tweet?text=${encodeURIComponent(shareText)}&url=${encodeURIComponent(shareUrl)}`;
        break;
      case 'whatsapp':
        socialUrl = `https://wa.me/?text=${encodeURIComponent(shareText + ' ' + shareUrl)}`;
        break;
      case 'linkedin':
        socialUrl = `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(shareUrl)}`;
        break;
      default:
        return;
    }

    window.open(socialUrl, '_blank', 'noopener,noreferrer');
  };

  const handleCopyToClipboard = async () => {
    if (!sharedData) return;

    const textToCopy = `${sharedData.title}\n${sharedData.text}\n${sharedData.url}`;
    
    try {
      await navigator.clipboard.writeText(textToCopy);
      showToast.success('Content copied to clipboard!');
    } catch (error) {
      console.error('Failed to copy to clipboard:', error);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center p-4">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="mt-3 text-muted-foreground">Processing shared content...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background p-4">
      <div className="max-w-2xl mx-auto">
        <Card className="shadow-lg">
          <CardHeader className="bg-primary text-primary-foreground">
            <CardTitle className="flex items-center gap-2">
              <Share2 className="h-5 w-5" />
              Shared Content - BookmyFestive
            </CardTitle>
          </CardHeader>
          <CardContent className="p-6">
              {sharedData && (sharedData.title || sharedData.text || sharedData.url) ? (
                <>
                  <Alert className="mb-6">
                    <CheckCircle className="h-4 w-4" />
                    <AlertDescription>
                      Content received successfully!
                    </AlertDescription>
                  </Alert>

                  {sharedData.title && (
                    <div className="mb-4">
                      <h3 className="font-semibold text-sm text-muted-foreground mb-1">Title:</h3>
                      <p className="text-foreground">{sharedData.title}</p>
                    </div>
                  )}

                  {sharedData.text && (
                    <div className="mb-4">
                      <h3 className="font-semibold text-sm text-muted-foreground mb-1">Description:</h3>
                      <p className="text-foreground">{sharedData.text}</p>
                    </div>
                  )}

                  {sharedData.url && (
                    <div className="mb-4">
                      <h3 className="font-semibold text-sm text-muted-foreground mb-1">URL:</h3>
                      <a
                        href={sharedData.url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-primary hover:underline break-all"
                      >
                        {sharedData.url}
                      </a>
                    </div>
                  )}

                  <div className="border-t pt-6 mb-6">
                    <h3 className="font-semibold mb-4">Share to Social Media</h3>
                    <div className="flex flex-wrap gap-2 mb-4">
                      <Button
                        variant="default"
                        size="sm"
                        onClick={() => handleShareToSocial('facebook')}
                        className="bg-blue-600 hover:bg-blue-700"
                      >
                        <Facebook className="h-4 w-4 mr-1" />
                        Facebook
                      </Button>
                      <Button
                        variant="default"
                        size="sm"
                        onClick={() => handleShareToSocial('twitter')}
                        className="bg-sky-500 hover:bg-sky-600"
                      >
                        <Twitter className="h-4 w-4 mr-1" />
                        Twitter
                      </Button>
                      <Button
                        variant="default"
                        size="sm"
                        onClick={() => handleShareToSocial('whatsapp')}
                        className="bg-green-600 hover:bg-green-700"
                      >
                        <MessageCircle className="h-4 w-4 mr-1" />
                        WhatsApp
                      </Button>
                      <Button
                        variant="default"
                        size="sm"
                        onClick={() => handleShareToSocial('linkedin')}
                        className="bg-blue-700 hover:bg-blue-800"
                      >
                        <Linkedin className="h-4 w-4 mr-1" />
                        LinkedIn
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={handleCopyToClipboard}
                      >
                        <Copy className="h-4 w-4 mr-1" />
                        Copy
                      </Button>
                  </div>
                  </div>
                </>
              ) : (
                <Alert>
                  <Info className="h-4 w-4" />
                  <AlertDescription>
                    No shared content detected. This page handles content shared to BookmyFestive from other apps.
                  </AlertDescription>
                </Alert>
              )}

              <div className="border-t pt-6 text-center">
                <Button variant="outline" onClick={handleGoHome}>
                  <Home className="h-4 w-4 mr-2" />
                  Go to Homepage
                </Button>
              </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
