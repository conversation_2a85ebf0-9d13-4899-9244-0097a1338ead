"use client"

import { useState, useEffect } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Checkbox } from '@/components/ui/checkbox';
import { ImageUpload } from '@/components/ui/image-upload';
import {
  Briefcase,
  Search,
  Plus,
  Trash2,
  Eye,
  CheckCircle,
  XCircle,
  Star,
  MapPin,
  Mail,
  Edit,
  Phone
} from 'lucide-react';
import { AdminOnlyRoute } from '@/components/RouteProtection';
import AdminContentService, { ContentFilters } from '@/lib/services/adminContentService';
import toast from 'react-hot-toast';

export default function AdminVendorsPage() {
  const [vendors, setVendors] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedVendors, setSelectedVendors] = useState<string[]>([]);
  const [filters, setFilters] = useState<ContentFilters>({});
  const [showVendorDetails, setShowVendorDetails] = useState(false);
  const [selectedVendor, setSelectedVendor] = useState<any>(null);
  const [vendorStats, setVendorStats] = useState<any>(null);
  const [nextToken, setNextToken] = useState<string | undefined>();
  const [hasMore, setHasMore] = useState(true);
  const [sortBy, setSortBy] = useState('name');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(10);
  // Add state for edit form
  const [editForm, setEditForm] = useState<any>(null);
  const [saving, setSaving] = useState(false);
  // Add state for multi-step form
  const [editStep, setEditStep] = useState(1);
  const totalEditSteps = 4;

  useEffect(() => {
    loadVendors();
    loadVendorStats();
  }, []);

  useEffect(() => {
    const delayedSearch = setTimeout(() => {
      loadVendors();
    }, 500);
    return () => clearTimeout(delayedSearch);
  }, [searchTerm, filters, sortBy]);

  const loadVendors = async (loadMore: boolean = false, page: number = 1) => {
    try {
      setLoading(true);
      const searchFilters: ContentFilters = {
        ...filters,
        searchTerm: searchTerm || undefined,
        // sortBy removed to fix linter error
      };
      const result = await AdminContentService.getAllVendors(
        searchFilters,
        itemsPerPage,
        loadMore ? nextToken : undefined
      );
      if (result.success && result.data) {
        if (loadMore) {
          setVendors(prev => [...prev, ...result.data!.vendors]);
        } else {
          setVendors(result.data.vendors);
          setCurrentPage(page);
        }
        setNextToken(result.data.nextToken);
        setHasMore(!!result.data.nextToken);
      } else {
        toast.error(result.error || 'Failed to load vendors');
      }
    } catch (error) {
      console.error('Error loading vendors:', error);
      toast.error('Failed to load vendors');
    } finally {
      setLoading(false);
    }
  };

  const loadVendorStats = async () => {
    try {
      const result = await AdminContentService.getContentStatistics();
      if (result.success) {
        setVendorStats(result.data);
      }
    } catch (error) {
      console.error('Error loading vendor stats:', error);
    }
  };

  const handleDeleteVendor = async (vendorId: string) => {
    if (!confirm('Are you sure you want to delete this vendor? This action cannot be undone.')) {
      return;
    }
    try {
      const result = await AdminContentService.deleteVendor(vendorId);
      if (result.success) {
        toast.success('Vendor deleted successfully');
        loadVendors();
        loadVendorStats();
      } else {
        toast.error(result.error || 'Failed to delete vendor');
      }
    } catch (error) {
      console.error('Error deleting vendor:', error);
      toast.error('Failed to delete vendor');
    }
  };

  const handleBulkDelete = async () => {
    if (selectedVendors.length === 0) return;
    if (!confirm(`Delete ${selectedVendors.length} selected vendors?`)) return;
    try {
      const result = await AdminContentService.bulkDelete('vendor', selectedVendors);
      if (result.success) {
        toast.success(`${selectedVendors.length} vendors deleted successfully`);
        setSelectedVendors([]);
        loadVendors();
        loadVendorStats();
      } else {
        toast.error(result.error || 'Failed to delete vendors');
      }
    } catch (error) {
      console.error('Error deleting vendors:', error);
      toast.error('Failed to delete vendors');
    }
  };

  const handleSelectAll = () => {
    if (selectedVendors.length === vendors.length) {
      setSelectedVendors([]);
    } else {
      setSelectedVendors(vendors.map(vendor => vendor.id));
    }
  };

  const handleVendorSelect = (vendorId: string) => {
    setSelectedVendors(prev =>
      prev.includes(vendorId)
        ? prev.filter(id => id !== vendorId)
        : [...prev, vendorId]
    );
  };

  const applyFilters = (key: string, value: any) => {
    setFilters(prev => ({ ...prev, [key]: value || undefined }));
    setNextToken(undefined);
  };

  const clearFilters = () => {
    setFilters({});
    setSearchTerm('');
    setSortBy('name');
    setNextToken(undefined);
  };

  // When opening modal, reset step to 1
  const openVendorDetails = (vendor: any) => {
    setSelectedVendor(vendor);
    setEditForm({ ...vendor, services: vendor.services ? [...vendor.services] : [], gallery: vendor.gallery ? [...vendor.gallery] : [], specializations: vendor.specializations ? [...vendor.specializations] : [], languages: vendor.languages ? [...vendor.languages] : [] });
    setEditStep(1);
    setShowVendorDetails(true);
  };

  // Handle form field changes
  const handleEditChange = (field: string, value: any) => {
    setEditForm((prev: any) => ({ ...prev, [field]: value }));
  };

  // Handle service changes
  const handleServiceChange = (idx: number, key: string, value: string) => {
    setEditForm((prev: any) => {
      const services = [...(prev.services || [])];
      services[idx] = { ...services[idx], [key]: value };
      return { ...prev, services };
    });
  };
  const addService = () => {
    setEditForm((prev: any) => ({ ...prev, services: [...(prev.services || []), { name: '', price: '' }] }));
  };
  const removeService = (idx: number) => {
    setEditForm((prev: any) => {
      const services = [...(prev.services || [])];
      services.splice(idx, 1);
      return { ...prev, services };
    });
  };

  // Save handler
  const handleSaveEdit = async () => {
    if (!editForm) return;
    setSaving(true);
    try {
      // Sanitize input: only send valid fields, remove undefined/null/empty, clean services
      const allowedFields = [
        'name', 'category', 'email', 'contact', 'address', 'city', 'state', 'rating', 'status', 'description', 'featured', 'verified', 'services',
        'pincode', 'website', 'profilePhoto', 'gallery', 'experience', 'events', 'responseTime', 'reviewCount', 'availability', 'priceRange', 'specializations', 'awards', 'languages', 'coverage', 'equipment'
      ];
      const cleaned: any = {};
      for (const key of allowedFields) {
        if (key in editForm && editForm[key] !== undefined && editForm[key] !== null && editForm[key] !== '') {
          if (key === 'services' && Array.isArray(editForm.services)) {
            cleaned.services = editForm.services
              .filter((s: any) => s && s.name && s.name.trim() && s.price && s.price.toString().trim())
              .map((s: any) => ({ name: s.name.trim(), price: s.price.toString().trim() }));
          } else {
            cleaned[key] = editForm[key];
          }
        }
      }
      cleaned.id = editForm.id;
      const result = await AdminContentService.updateVendor(editForm.id, cleaned);
      if (result.success) {
        toast.success('Vendor updated successfully');
        setShowVendorDetails(false);
        setEditForm(null);
        loadVendors();
      } else {
        toast.error(result.error || 'Failed to update vendor');
      }
    } catch (error) {
      toast.error('Failed to update vendor');
    } finally {
      setSaving(false);
    }
  };

  const getStatusBadge = (vendor: any) => {
    if (vendor.verified) {
      return <Badge className="bg-green-100 text-green-800 text-xs px-2 py-1"><CheckCircle className="w-3 h-3 mr-1" />Verified</Badge>;
    }
    return <Badge variant="secondary" className="text-xs px-2 py-1"><XCircle className="w-3 h-3 mr-1" />Unverified</Badge>;
  };
  const getFeaturedBadge = (vendor: any) => {
    if (vendor.featured) {
      return <Badge className="bg-yellow-100 text-yellow-800 text-xs px-2 py-1"><Star className="w-3 h-3 mr-1" />Featured</Badge>;
    }
    return null;
  };

  // --- UI ---
  return (
    <AdminOnlyRoute>
      <div className="space-y-4 sm:space-y-8 px-2 sm:px-0">
        {/* Header */}
        <div className="flex flex-col gap-4 mb-4 sm:mb-6">
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-3 sm:gap-4">
            <div>
              <h1 className="text-2xl sm:text-3xl font-bold text-gray-900 mb-1">Manage Vendors</h1>
              <p className="text-gray-500 text-sm sm:text-base">{vendors.length} vendor{vendors.length !== 1 ? 's' : ''}</p>
            </div>
            <div className="flex flex-col sm:flex-row gap-2 w-full sm:w-auto">
              {selectedVendors.length > 0 && (
                <Button 
                  variant="outline" 
                  onClick={handleBulkDelete} 
                  className="text-red-600 hover:text-red-700 hover:bg-red-50 text-sm"
                >
                  Delete Selected ({selectedVendors.length})
                </Button>
              )}
              <Button className="bg-primary hover:bg-primary/90 text-sm">
                <Plus className="w-4 h-4 mr-2" />
                <span className="hidden sm:inline">Add Vendor</span>
                <span className="sm:hidden">Add</span>
              </Button>
            </div>
          </div>
        </div>

        {/* Filters */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-3 sm:p-4 mb-4 sm:mb-6">
          <div className="space-y-3 sm:space-y-0 sm:grid sm:grid-cols-1 md:grid-cols-6 gap-3 sm:gap-4">
            <div className="relative">
              <Input 
                type="text" 
                placeholder="Search vendors..." 
                value={searchTerm} 
                onChange={e => setSearchTerm(e.target.value)} 
                className="w-full pl-10 text-sm" 
              />
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            </div>
            <Select value={filters.category || 'all'} onValueChange={value => applyFilters('category', value === 'all' ? undefined : value)}>
              <SelectTrigger className="text-sm">
                <SelectValue placeholder="All Categories" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Categories</SelectItem>
                <SelectItem value="Photography">Photography</SelectItem>
                <SelectItem value="Catering">Catering</SelectItem>
                <SelectItem value="Decoration">Decoration</SelectItem>
                <SelectItem value="Music">Music</SelectItem>
                <SelectItem value="Makeup">Makeup</SelectItem>
              </SelectContent>
            </Select>
            <Select value={filters.status || 'all'} onValueChange={value => applyFilters('status', value === 'all' ? undefined : value)}>
              <SelectTrigger className="text-sm">
                <SelectValue placeholder="All Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="inactive">Inactive</SelectItem>
                <SelectItem value="pending">Pending</SelectItem>
              </SelectContent>
            </Select>
            <Select value={filters.verified !== undefined ? String(filters.verified) : 'all'} onValueChange={value => applyFilters('verified', value === 'all' ? undefined : value === 'true')}>
              <SelectTrigger className="text-sm">
                <SelectValue placeholder="All Vendors" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Vendors</SelectItem>
                <SelectItem value="true">Verified Only</SelectItem>
                <SelectItem value="false">Unverified Only</SelectItem>
              </SelectContent>
            </Select>
            <Select value={filters.featured !== undefined ? String(filters.featured) : 'all'} onValueChange={value => applyFilters('featured', value === 'all' ? undefined : value === 'true')}>
              <SelectTrigger className="text-sm">
                <SelectValue placeholder="All Vendors" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Vendors</SelectItem>
                <SelectItem value="true">Featured Only</SelectItem>
                <SelectItem value="false">Non-Featured</SelectItem>
              </SelectContent>
            </Select>
            <Select value={sortBy} onValueChange={value => setSortBy(value)}>
              <SelectTrigger className="text-sm">
                <SelectValue placeholder="Sort by Name" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="name">Sort by Name</SelectItem>
                <SelectItem value="category">Sort by Category</SelectItem>
                <SelectItem value="rating">Sort by Rating</SelectItem>
                <SelectItem value="city">Sort by City</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Vendor Cards Grid */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-6">
          {loading ? (
            <div className="col-span-full text-center py-8 sm:py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
              <p className="mt-2 text-gray-600 text-sm sm:text-base">Loading vendors...</p>
            </div>
          ) : vendors.length === 0 ? (
            <div className="col-span-full text-center py-8 sm:py-12">
              <Briefcase className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-600 text-sm sm:text-base">No vendors found</p>
            </div>
          ) : (
            vendors.map((vendor) => (
              <Card key={vendor.id} className="overflow-hidden rounded-xl sm:rounded-2xl shadow border border-gray-100 bg-white hover:shadow-md transition-shadow relative">
                <CardContent className="p-4 sm:p-6 flex flex-col h-full">
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex-1 min-w-0">
                      <h2 className="font-bold text-lg sm:text-xl text-gray-900 mb-1 line-clamp-1 truncate">
                        {vendor.name}
                      </h2>
                      <p className="text-primary font-medium text-xs uppercase tracking-wide mb-1 truncate">
                        {vendor.category}
                      </p>
                    </div>
                    <div className="flex flex-col gap-1 ml-2">
                      {getFeaturedBadge(vendor)}
                      <Badge variant={vendor.status === 'active' ? 'default' : 'secondary'} className="text-xs px-2 py-1">
                        {vendor.status || 'active'}
                      </Badge>
                    </div>
                  </div>
                  
                  <div className="text-gray-500 text-xs sm:text-sm mb-2 flex items-center gap-2">
                    <Mail className="w-3 h-3" />
                    <span className="truncate">{vendor.email}</span>
                  </div>
                  
                  <div className="text-gray-500 text-xs sm:text-sm mb-2 flex items-center gap-2">
                    <MapPin className="w-3 h-3" />
                    <span className="truncate">{vendor.city}, {vendor.state}</span>
                  </div>
                  
                  <div className="flex items-center gap-2 mb-3">
                    {getStatusBadge(vendor)}
                  </div>
                  
                  <div className="flex items-center justify-between text-sm mb-4">
                    <span className="flex items-center gap-1 bg-yellow-50 px-2 py-1 rounded-full">
                      <Star className="h-3 w-3 sm:h-4 sm:w-4 fill-yellow-400 text-yellow-400" />
                      <span className="text-xs sm:text-sm font-semibold text-gray-900">
                        {vendor.rating || 'N/A'}
                      </span>
                    </span>
                  </div>
                  
                  <div className="flex flex-col sm:flex-row gap-2 mt-auto">
                    <Button 
                      variant="outline" 
                      size="sm" 
                      onClick={() => openVendorDetails(vendor)} 
                      className="flex-1 text-xs sm:text-sm"
                    >
                      <Edit className="w-3 h-3 mr-1 sm:hidden" />
                      <span className="hidden sm:inline">Edit</span>
                      <span className="sm:hidden">Edit</span>
                    </Button>
                    <Button 
                      variant="outline" 
                      size="sm" 
                      onClick={() => handleDeleteVendor(vendor.id)} 
                      className="flex-1 text-red-600 hover:text-red-700 text-xs sm:text-sm"
                    >
                      <Trash2 className="w-3 h-3 mr-1 sm:hidden" />
                      <span className="hidden sm:inline">Delete</span>
                      <span className="sm:hidden">Del</span>
                    </Button>
                    <Checkbox 
                      checked={selectedVendors.includes(vendor.id)} 
                      onCheckedChange={() => handleVendorSelect(vendor.id)} 
                      className="ml-2 mt-1" 
                    />
                  </div>
                </CardContent>
              </Card>
            ))
          )}
        </div>

        {/* Pagination Controls */}
        {vendors.length > 0 && (
          <div className="flex flex-col sm:flex-row justify-center items-center gap-3 sm:gap-4 pt-4 sm:pt-6">
            <Button
              variant="outline"
              onClick={() => {
                if (currentPage > 1) {
                  loadVendors(false, currentPage - 1);
                }
              }}
              disabled={loading || currentPage === 1}
              className="text-sm w-full sm:w-auto"
            >
              Previous
            </Button>
            <span className="text-xs sm:text-sm text-gray-600 text-center">
              Page {currentPage} • {vendors.length} items
            </span>
            <Button
              variant="outline"
              onClick={() => {
                if (hasMore) {
                  loadVendors(false, currentPage + 1);
                }
              }}
              disabled={loading || !hasMore}
              className="text-sm w-full sm:w-auto"
            >
              Next
            </Button>
          </div>
        )}

        {/* Vendor Details Modal */}
        <Dialog open={showVendorDetails} onOpenChange={setShowVendorDetails}>
          <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto rounded-2xl mx-4 sm:mx-auto">
            <DialogHeader>
              <DialogTitle className="text-lg sm:text-xl">Edit Vendor</DialogTitle>
            </DialogHeader>
            {editForm && (
              <form className="space-y-4 sm:space-y-6" onSubmit={e => { e.preventDefault(); handleSaveEdit(); }}>
                {/* Progress Bar */}
                <div className="mb-4">
                  <div className="w-full h-3 rounded-full bg-gray-200 mb-1">
                    <div className="h-3 rounded-full bg-[#800000] transition-all" style={{ width: `${(editStep / totalEditSteps) * 100}%` }} />
                  </div>
                  <div className="flex justify-between text-xs sm:text-sm text-gray-500">
                    <span>Step {editStep} of {totalEditSteps}</span>
                    <span>{['Basic Info', 'Location', 'Professional', 'Images & Skills'][editStep-1]}</span>
                  </div>
                </div>

                {/* Step 1: Basic Info */}
                {editStep === 1 && (
                  <div className="bg-gray-50 p-4 sm:p-6 rounded-lg">
                    <h3 className="text-lg font-semibold mb-4">Basic Information</h3>
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Vendor Name *</label>
                        <Input value={editForm.name || ''} onChange={e => handleEditChange('name', e.target.value)} required className="text-sm" />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Category *</label>
                        <Input value={editForm.category || ''} onChange={e => handleEditChange('category', e.target.value)} required className="text-sm" />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Contact Number *</label>
                        <Input value={editForm.contact || ''} onChange={e => handleEditChange('contact', e.target.value)} required className="text-sm" />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Email Address</label>
                        <Input value={editForm.email || ''} onChange={e => handleEditChange('email', e.target.value)} className="text-sm" />
                      </div>
                      <div className="col-span-1 sm:col-span-2">
                        <label className="block text-sm font-medium text-gray-700 mb-2">Description</label>
                        <textarea className="border rounded-lg px-3 py-2 w-full h-24 focus:ring-2 focus:ring-primary focus:border-transparent text-sm" value={editForm.description || ''} onChange={e => handleEditChange('description', e.target.value)} />
                      </div>
                    </div>
                  </div>
                )}

                {/* Step 2: Location */}
                {editStep === 2 && (
                  <div className="bg-gray-50 p-4 sm:p-6 rounded-lg">
                    <h3 className="text-lg font-semibold mb-4">Location Information</h3>
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
                      <div className="col-span-1 sm:col-span-2">
                        <label className="block text-sm font-medium text-gray-700 mb-2">Address</label>
                        <Input value={editForm.address || ''} onChange={e => handleEditChange('address', e.target.value)} className="text-sm" />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">City *</label>
                        <Input value={editForm.city || ''} onChange={e => handleEditChange('city', e.target.value)} required className="text-sm" />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">State *</label>
                        <Input value={editForm.state || ''} onChange={e => handleEditChange('state', e.target.value)} required className="text-sm" />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Pincode</label>
                        <Input value={editForm.pincode || ''} onChange={e => handleEditChange('pincode', e.target.value)} className="text-sm" />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Website</label>
                        <Input value={editForm.website || ''} onChange={e => handleEditChange('website', e.target.value)} className="text-sm" />
                      </div>
                    </div>
                    <div className="mt-4">
                      <h4 className="font-semibold mb-2 text-sm">Social Media</h4>
                      <div className="grid grid-cols-1 sm:grid-cols-3 gap-3 sm:gap-4">
                        <Input placeholder="facebook.com/yourpage" value={editForm.facebook || ''} onChange={e => handleEditChange('facebook', e.target.value)} className="text-sm" />
                        <Input placeholder="instagram.com/yourpage" value={editForm.instagram || ''} onChange={e => handleEditChange('instagram', e.target.value)} className="text-sm" />
                        <Input placeholder="youtube.com/yourchannel" value={editForm.youtube || ''} onChange={e => handleEditChange('youtube', e.target.value)} className="text-sm" />
                      </div>
                    </div>
                  </div>
                )}

                {/* Step 3: Professional & Services */}
                {editStep === 3 && (
                  <div className="bg-gray-50 p-4 sm:p-6 rounded-lg">
                    <h3 className="text-lg font-semibold mb-4">Professional & Services</h3>
                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Experience</label>
                        <Input value={editForm.experience || ''} onChange={e => handleEditChange('experience', e.target.value)} className="text-sm" />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Events Completed</label>
                        <Input value={editForm.events || ''} onChange={e => handleEditChange('events', e.target.value)} className="text-sm" />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Response Time</label>
                        <Input value={editForm.responseTime || ''} onChange={e => handleEditChange('responseTime', e.target.value)} className="text-sm" />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Price Range</label>
                        <Input value={editForm.priceRange || ''} onChange={e => handleEditChange('priceRange', e.target.value)} className="text-sm" />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Availability</label>
                        <Input value={editForm.availability || ''} onChange={e => handleEditChange('availability', e.target.value)} className="text-sm" />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Status</label>
                        <Input value={editForm.status || ''} onChange={e => handleEditChange('status', e.target.value)} className="text-sm" />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Rating (0-5)</label>
                        <Input type="number" min={0} max={5} step={0.1} value={editForm.rating || 0} onChange={e => handleEditChange('rating', parseFloat(e.target.value))} className="text-sm" />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Review Count</label>
                        <Input type="number" min={0} value={editForm.reviewCount || 0} onChange={e => handleEditChange('reviewCount', parseInt(e.target.value))} className="text-sm" />
                      </div>
                      <div className="flex items-center gap-4 mt-6 col-span-1 sm:col-span-2 lg:col-span-3">
                        <Checkbox checked={!!editForm.verified} onCheckedChange={v => handleEditChange('verified', v)} />
                        <span className="text-sm">Verified Vendor</span>
                        <Checkbox checked={!!editForm.featured} onCheckedChange={v => handleEditChange('featured', v)} />
                        <span className="text-sm">Featured</span>
                      </div>
                    </div>
                    <div className="mt-6">
                      <h4 className="font-semibold mb-2 text-sm">Services & Pricing</h4>
                      {(editForm.services || []).map((service: any, idx: number) => (
                        <div key={idx} className="flex flex-col sm:flex-row gap-2 items-center mb-2">
                          <Input className="flex-1 text-sm" placeholder="Service Name" value={service.name} onChange={e => handleServiceChange(idx, 'name', e.target.value)} />
                          <Input className="w-full sm:w-32 text-sm" placeholder="Price" value={service.price} onChange={e => handleServiceChange(idx, 'price', e.target.value)} />
                          <Input className="flex-1 text-sm" placeholder="Description" value={service.description || ''} onChange={e => handleServiceChange(idx, 'description', e.target.value)} />
                          <Button type="button" variant="destructive" size="sm" onClick={() => removeService(idx)} className="text-xs">Remove</Button>
                        </div>
                      ))}
                      <Button type="button" variant="outline" size="sm" onClick={addService} className="text-sm">+ Add Service</Button>
                    </div>
                  </div>
                )}

                {/* Step 4: Images & Specializations */}
                {editStep === 4 && (
                  <div className="bg-gray-50 p-4 sm:p-6 rounded-lg">
                    <h3 className="text-lg font-semibold mb-4">Images & Specializations</h3>
                    <div className="mb-4">
                      <label className="block text-sm font-medium text-gray-700 mb-2">Profile Image</label>
                      <ImageUpload
                        images={editForm?.profilePhoto ? [editForm.profilePhoto] : []}
                        onImagesChange={images => handleEditChange('profilePhoto', images[0] || '')}
                        maxImages={1}
                        label="Upload Profile Image"
                        description="Upload main profile image (JPG, PNG, WebP)"
                        compressForDB={true}
                        showSizeWarning={true}
                      />
                    </div>
                    <div className="mb-4">
                      <label className="block text-sm font-medium text-gray-700 mb-2">Gallery Images</label>
                      <ImageUpload
                        images={editForm?.gallery || []}
                        onImagesChange={images => handleEditChange('gallery', images)}
                        maxImages={12}
                        label="Upload Portfolio Images"
                        description="Upload portfolio images (JPG, PNG, WebP) - Images will be compressed for database storage"
                        compressForDB={true}
                        showSizeWarning={true}
                      />
                    </div>
                    <div className="mb-4">
                      <label className="block text-sm font-medium text-gray-700 mb-2">Or Add Image URLs</label>
                      <div className="flex flex-col sm:flex-row gap-2">
                        <Input className="flex-1 text-sm" placeholder="https://example.com/portfolio-image.jpg" />
                        <Button type="button" variant="outline" size="sm" className="text-sm">Add URL</Button>
                      </div>
                    </div>
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Specializations</label>
                        <div className="flex flex-col sm:flex-row gap-2 mb-2">
                          <Input className="flex-1 text-sm" placeholder="Add specialization" />
                          <Button type="button" variant="outline" size="sm" className="text-sm">Add</Button>
                        </div>
                        {/* List of specializations */}
                        <div className="flex flex-wrap gap-2">
                          {(editForm.specializations || []).map((spec: string, idx: number) => (
                            <span key={idx} className="bg-gray-200 rounded-full px-3 py-1 text-xs">{spec}</span>
                          ))}
                        </div>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Languages</label>
                        <div className="flex flex-col sm:flex-row gap-2 mb-2">
                          <Input className="flex-1 text-sm" placeholder="Add language" />
                          <Button type="button" variant="outline" size="sm" className="text-sm">Add</Button>
                        </div>
                        {/* List of languages */}
                        <div className="flex flex-wrap gap-2">
                          {(editForm.languages || []).map((lang: string, idx: number) => (
                            <span key={idx} className="bg-gray-200 rounded-full px-3 py-1 text-xs">{lang}</span>
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {/* Navigation Buttons */}
                <div className="flex flex-col sm:flex-row gap-3 justify-between pt-4 border-t mt-6">
                  <Button type="button" variant="outline" onClick={() => setShowVendorDetails(false)} disabled={saving} className="text-sm">Cancel</Button>
                  <div className="flex flex-col sm:flex-row gap-2">
                    {editStep > 1 && (
                      <Button type="button" variant="outline" onClick={() => setEditStep(editStep - 1)} disabled={saving} className="text-sm">Back</Button>
                    )}
                    {editStep < totalEditSteps && (
                      <Button type="button" className="bg-primary hover:bg-primary/90 text-sm" onClick={() => setEditStep(editStep + 1)} disabled={saving}>Next</Button>
                    )}
                    {editStep === totalEditSteps && (
                      <Button type="submit" className="bg-primary hover:bg-primary/90 text-sm" disabled={saving}>{saving ? 'Saving...' : 'Save Changes'}</Button>
                    )}
                  </div>
                </div>
              </form>
            )}
          </DialogContent>
        </Dialog>
      </div>
    </AdminOnlyRoute>
  );
}