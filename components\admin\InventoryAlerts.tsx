"use client"

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { AlertTriangle, Package, TrendingDown, RefreshCw } from "lucide-react"
import { shopService } from '@/lib/services/shopService'
import { InventoryService } from '@/lib/services/inventoryService'

interface LowStockProduct {
  id: string
  name: string
  stock: number
  category: string
  price: string
  inStock: boolean
}

export default function InventoryAlerts() {
  const [lowStockProducts, setLowStockProducts] = useState<LowStockProduct[]>([])
  const [loading, setLoading] = useState(true)
  const [refreshing, setRefreshing] = useState(false)

  const loadLowStockProducts = async () => {
    try {
      setRefreshing(true)
      
      // Get all products and filter for low stock
      const productsResult = await shopService.listShops()
      if (productsResult.success && productsResult.shops) {
        const lowStock = productsResult.shops.filter(product => 
          product.stock <= 5 && product.inStock
        ).map(product => ({
          id: product.id,
          name: product.name,
          stock: product.stock,
          category: product.category,
          price: product.price,
          inStock: product.inStock
        }))
        
        setLowStockProducts(lowStock)
      }
    } catch (error) {
      console.error('Error loading low stock products:', error)
    } finally {
      setLoading(false)
      setRefreshing(false)
    }
  }

  useEffect(() => {
    loadLowStockProducts()
  }, [])

  const getStockBadgeColor = (stock: number) => {
    if (stock === 0) return 'destructive'
    if (stock <= 2) return 'destructive'
    if (stock <= 5) return 'secondary'
    return 'default'
  }

  const getStockBadgeText = (stock: number) => {
    if (stock === 0) return 'Out of Stock'
    if (stock <= 2) return 'Critical'
    if (stock <= 5) return 'Low Stock'
    return 'In Stock'
  }

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Package className="w-5 h-5" />
            Inventory Alerts
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <RefreshCw className="w-6 h-6 animate-spin text-gray-400" />
            <span className="ml-2 text-gray-500">Loading inventory data...</span>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Package className="w-5 h-5" />
            Inventory Alerts
            {lowStockProducts.length > 0 && (
              <Badge variant="destructive" className="ml-2">
                {lowStockProducts.length}
              </Badge>
            )}
          </CardTitle>
          <Button
            variant="outline"
            size="sm"
            onClick={loadLowStockProducts}
            disabled={refreshing}
          >
            <RefreshCw className={`w-4 h-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        {lowStockProducts.length === 0 ? (
          <div className="text-center py-8">
            <Package className="w-12 h-12 text-green-500 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">All Good!</h3>
            <p className="text-gray-500">No low stock alerts at the moment.</p>
          </div>
        ) : (
          <div className="space-y-4">
            <div className="flex items-center gap-2 p-3 bg-orange-50 border border-orange-200 rounded-lg">
              <AlertTriangle className="w-5 h-5 text-orange-600" />
              <div>
                <p className="text-sm font-medium text-orange-800">
                  {lowStockProducts.length} product{lowStockProducts.length > 1 ? 's' : ''} running low on stock
                </p>
                <p className="text-xs text-orange-600">
                  Consider restocking these items soon
                </p>
              </div>
            </div>

            <div className="space-y-3">
              {lowStockProducts.map((product) => (
                <div
                  key={product.id}
                  className="flex items-center justify-between p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  <div className="flex-1 min-w-0">
                    <h4 className="text-sm font-medium text-gray-900 truncate">
                      {product.name}
                    </h4>
                    <div className="flex items-center gap-2 mt-1">
                      <span className="text-xs text-gray-500">
                        {product.category}
                      </span>
                      <span className="text-xs text-gray-400">•</span>
                      <span className="text-xs font-medium text-gray-700">
                        ₹{product.price}
                      </span>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-3">
                    <div className="text-right">
                      <div className="text-sm font-medium text-gray-900">
                        {product.stock} units
                      </div>
                      <Badge 
                        variant={getStockBadgeColor(product.stock) as any}
                        className="text-xs"
                      >
                        {getStockBadgeText(product.stock)}
                      </Badge>
                    </div>
                    
                    <div className="flex items-center">
                      <TrendingDown className="w-4 h-4 text-red-500" />
                    </div>
                  </div>
                </div>
              ))}
            </div>

            <div className="pt-4 border-t border-gray-200">
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-500">
                  Last updated: {new Date().toLocaleTimeString()}
                </span>
                <Button variant="outline" size="sm">
                  View All Products
                </Button>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
