'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Checkbox } from '@/components/ui/checkbox'
import {
  Search,
  Filter,
  Edit,
  Trash2,
  Eye,
  Star,
  Pin,
  Calendar,
  User,
  Tag,
  TrendingUp,
  MoreHorizontal,
  Plus
} from '@/lib/icon-imports'
import { useAuth } from '@/contexts/AuthContext'
import { BlogService, Blog, BlogStatus, BlogCategory, AuthorType } from '@/lib/services/blogService'
import { toast } from 'sonner'
import { AdminOnlyRoute } from '@/components/RouteProtection'
import { useRouter } from 'next/navigation'

interface BlogFilters {
  status?: BlogStatus
  category?: BlogCategory
  authorType?: AuthorType
  featured?: boolean
  pinned?: boolean
  searchTerm?: string
  dateFrom?: string
  dateTo?: string
}

function AdminBlogsPageContent() {
  const router = useRouter()
  const { user, isAuthenticated, isAdmin } = useAuth()
  const [blogs, setBlogs] = useState<Blog[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedBlogs, setSelectedBlogs] = useState<string[]>([])
  const [searchTerm, setSearchTerm] = useState('')
  const [filters, setFilters] = useState<BlogFilters>({})
  const [nextToken, setNextToken] = useState<string | undefined>()
  const [hasMore, setHasMore] = useState(false)

  // Load blogs on component mount
  useEffect(() => {
    if (isAuthenticated) {
      loadBlogs()
    }
  }, [isAuthenticated])

  // Handle search term changes with debounce
  useEffect(() => {
    if (!isAuthenticated) return

    const timeoutId = setTimeout(() => {
      loadBlogs()
    }, 500) // 500ms debounce

    return () => clearTimeout(timeoutId)
  }, [searchTerm, isAuthenticated])

  const loadBlogs = async (loadMore: boolean = false) => {
    try {
      setLoading(true)
      const result = await BlogService.listBlogs({
        ...filters,
        searchTerm: searchTerm || undefined
      }, 50, loadMore ? nextToken : undefined)
      
      if (loadMore) {
        setBlogs(prev => [...prev, ...result.blogs])
      } else {
        setBlogs(result.blogs)
      }
      setNextToken(result.nextToken)
      setHasMore(!!result.nextToken)
    } catch (error) {
      console.error('Error loading blogs:', error)
      toast.error('Failed to load blogs')
    } finally {
      setLoading(false)
    }
  }

  const applyFilters = async (key: keyof BlogFilters, value: any) => {
    const newFilters = { ...filters, [key]: value === 'all' ? undefined : value }
    setFilters(newFilters)

    // Reload blogs with new filters immediately
    try {
      setLoading(true)
      const result = await BlogService.listBlogs({
        ...newFilters,
        searchTerm: searchTerm || undefined
      }, 50)

      setBlogs(result.blogs)
      setNextToken(result.nextToken)
      setHasMore(!!result.nextToken)
    } catch (error) {
      console.error('Error applying filters:', error)
      toast.error('Failed to apply filters')
    } finally {
      setLoading(false)
    }
  }

  const handleBlogSelect = (blogId: string) => {
    setSelectedBlogs(prev => 
      prev.includes(blogId) 
        ? prev.filter(id => id !== blogId)
        : [...prev, blogId]
    )
  }

  const handleSelectAll = () => {
    if (selectedBlogs.length === blogs.length) {
      setSelectedBlogs([])
    } else {
      setSelectedBlogs(blogs.map(blog => blog.id))
    }
  }

  const handleBulkAction = async (action: 'feature' | 'unfeature' | 'pin' | 'unpin' | 'publish' | 'archive' | 'delete') => {
    if (selectedBlogs.length === 0) {
      toast.error('Please select blogs first')
      return
    }

    try {
      switch (action) {
        case 'feature':
          await BlogService.bulkUpdateBlogs(selectedBlogs, { isFeatured: true })
          break
        case 'unfeature':
          await BlogService.bulkUpdateBlogs(selectedBlogs, { isFeatured: false })
          break
        case 'pin':
          await BlogService.bulkUpdateBlogs(selectedBlogs, { isPinned: true })
          break
        case 'unpin':
          await BlogService.bulkUpdateBlogs(selectedBlogs, { isPinned: false })
          break
        case 'publish':
          await BlogService.bulkUpdateBlogs(selectedBlogs, { status: BlogStatus.PUBLISHED })
          break
        case 'archive':
          await BlogService.bulkUpdateBlogs(selectedBlogs, { status: BlogStatus.ARCHIVED })
          break
        case 'delete':
          await BlogService.bulkDeleteBlogs(selectedBlogs)
          break
      }

      toast.success(`Successfully ${action}d ${selectedBlogs.length} blog(s)`)
      setSelectedBlogs([])
      loadBlogs()
    } catch (error) {
      console.error(`Error ${action}ing blogs:`, error)
      toast.error(`Failed to ${action} blogs`)
    }
  }

  const handleDeleteBlog = async (blogId: string) => {
    if (!confirm('Are you sure you want to delete this blog?')) return

    try {
      await BlogService.deleteBlog(blogId, isAdmin)
      toast.success('Blog deleted successfully!')
      loadBlogs()
    } catch (error) {
      console.error('Error deleting blog:', error)
      toast.error('Failed to delete blog')
    }
  }

  const getStatusColor = (status: BlogStatus) => {
    switch (status) {
      case BlogStatus.PUBLISHED: return 'bg-green-100 text-green-800'
      case BlogStatus.DRAFT: return 'bg-yellow-100 text-yellow-800'
      case BlogStatus.ARCHIVED: return 'bg-gray-100 text-gray-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getAuthorTypeColor = (authorType: AuthorType) => {
    switch (authorType) {
      case AuthorType.ADMIN: return 'bg-purple-100 text-purple-800'
      case AuthorType.VENDOR: return 'bg-blue-100 text-blue-800'
      case AuthorType.EXPERT: return 'bg-orange-100 text-orange-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <div className="space-y-4 sm:space-y-6 px-2 sm:px-0">
        {/* Header */}
        <div className="flex flex-col gap-4 mb-4 sm:mb-6">
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-3 sm:gap-4">
            <div>
              <h1 className="text-2xl sm:text-3xl font-bold text-gray-900 mb-1">Blog Management</h1>
              <p className="text-gray-500 text-sm sm:text-base">Manage all blog posts across the platform</p>
            </div>
            <div className="flex flex-col sm:flex-row gap-2 w-full sm:w-auto">
              <Button
                onClick={() => router.push('/dashboard/blogs/create', '_blank')}
                className="bg-primary hover:bg-primary/90 text-sm"
              >
                <Plus className="w-4 h-4 mr-1" />
                <span className="hidden sm:inline">Create New Blog</span>
                <span className="sm:hidden">New Blog</span>
              </Button>
              {selectedBlogs.length > 0 && (
                <div className="flex flex-wrap gap-2">
                  <Button variant="outline" size="sm" onClick={() => handleBulkAction('feature')} className="text-xs sm:text-sm">
                    <Star className="w-3 h-3 mr-1 sm:hidden" />
                    <span className="hidden sm:inline">Feature</span>
                    <span className="sm:hidden">Feature</span>
                  </Button>
                  <Button variant="outline" size="sm" onClick={() => handleBulkAction('pin')} className="text-xs sm:text-sm">
                    <Pin className="w-3 h-3 mr-1 sm:hidden" />
                    <span className="hidden sm:inline">Pin</span>
                    <span className="sm:hidden">Pin</span>
                  </Button>
                  <Button variant="outline" size="sm" onClick={() => handleBulkAction('publish')} className="text-xs sm:text-sm">
                    <span className="hidden sm:inline">Publish</span>
                    <span className="sm:hidden">Pub</span>
                  </Button>
                  <Button variant="outline" size="sm" onClick={() => handleBulkAction('delete')} className="text-red-600 text-xs sm:text-sm">
                    <Trash2 className="w-3 h-3 mr-1 sm:hidden" />
                    <span className="hidden sm:inline">Delete</span>
                    <span className="sm:hidden">Del</span>
                  </Button>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Filters */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-3 sm:p-4 mb-4 sm:mb-6">
          <div className="space-y-3 sm:space-y-0 sm:grid sm:grid-cols-1 md:grid-cols-6 gap-3 sm:gap-4">
            <div className="relative">
              <Input
                type="text"
                placeholder="Search blogs..."
                value={searchTerm}
                onChange={e => setSearchTerm(e.target.value)}
                className="w-full pl-10 text-sm"
              />
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            </div>
            <Select value={filters.status || 'all'} onValueChange={value => applyFilters('status', value)}>
              <SelectTrigger className="text-sm">
                <SelectValue placeholder="All Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value={BlogStatus.DRAFT}>Draft</SelectItem>
                <SelectItem value={BlogStatus.PUBLISHED}>Published</SelectItem>
                <SelectItem value={BlogStatus.ARCHIVED}>Archived</SelectItem>
              </SelectContent>
            </Select>
            <Select value={filters.category || 'all'} onValueChange={value => applyFilters('category', value)}>
              <SelectTrigger className="text-sm">
                <SelectValue placeholder="All Categories" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Categories</SelectItem>
                <SelectItem value={BlogCategory.WEDDING_PLANNING}>Wedding Planning</SelectItem>
                <SelectItem value={BlogCategory.VENUE_SELECTION}>Venue Selection</SelectItem>
                <SelectItem value={BlogCategory.PHOTOGRAPHY_VIDEOGRAPHY}>Photography & Videography</SelectItem>
                <SelectItem value={BlogCategory.CATERING_FOOD}>Catering & Food</SelectItem>
                <SelectItem value={BlogCategory.DECORATIONS_THEMES}>Decorations & Themes</SelectItem>
                <SelectItem value={BlogCategory.FASHION_BEAUTY}>Fashion & Beauty</SelectItem>
                <SelectItem value={BlogCategory.REAL_WEDDINGS}>Real Weddings</SelectItem>
                <SelectItem value={BlogCategory.EXPERT_TIPS}>Expert Tips</SelectItem>
                <SelectItem value={BlogCategory.VENDOR_SPOTLIGHT}>Vendor Spotlight</SelectItem>
              </SelectContent>
            </Select>
            <Select value={filters.authorType || 'all'} onValueChange={value => applyFilters('authorType', value)}>
              <SelectTrigger className="text-sm">
                <SelectValue placeholder="All Authors" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Authors</SelectItem>
                <SelectItem value={AuthorType.ADMIN}>Admin</SelectItem>
                <SelectItem value={AuthorType.VENDOR}>Vendor</SelectItem>
                <SelectItem value={AuthorType.EXPERT}>Expert</SelectItem>
              </SelectContent>
            </Select>
            <div className="flex flex-col sm:flex-row items-start sm:items-center gap-3 sm:gap-4">
              <label className="flex items-center gap-2 text-xs sm:text-sm">
                <Checkbox 
                  checked={filters.featured || false}
                  onCheckedChange={(checked) => applyFilters('featured', checked)}
                />
                <span className="hidden sm:inline">Featured</span>
                <span className="sm:hidden">Feat</span>
              </label>
              <label className="flex items-center gap-2 text-xs sm:text-sm">
                <Checkbox 
                  checked={filters.pinned || false}
                  onCheckedChange={(checked) => applyFilters('pinned', checked)}
                />
                <span className="hidden sm:inline">Pinned</span>
                <span className="sm:hidden">Pin</span>
              </label>
            </div>
            <div className="flex items-center gap-2">
              <Filter className="w-4 h-4 text-gray-400" />
              <span className="text-xs sm:text-sm text-gray-600">{blogs.length} blogs</span>
            </div>
          </div>
        </div>

        {/* Bulk Actions */}
        {selectedBlogs.length > 0 && (
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 sm:p-4">
            <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-3 sm:gap-4">
              <span className="text-xs sm:text-sm text-blue-800">
                {selectedBlogs.length} blog(s) selected
              </span>
              <div className="flex flex-wrap gap-2">
                <Button variant="outline" size="sm" onClick={() => handleBulkAction('feature')} className="text-xs">
                  <Star className="w-3 h-3 mr-1 sm:hidden" />
                  <span className="hidden sm:inline">Feature</span>
                  <span className="sm:hidden">Feat</span>
                </Button>
                <Button variant="outline" size="sm" onClick={() => handleBulkAction('unfeature')} className="text-xs">
                  <span className="hidden sm:inline">Unfeature</span>
                  <span className="sm:hidden">Unfeat</span>
                </Button>
                <Button variant="outline" size="sm" onClick={() => handleBulkAction('pin')} className="text-xs">
                  <Pin className="w-3 h-3 mr-1 sm:hidden" />
                  <span className="hidden sm:inline">Pin</span>
                  <span className="sm:hidden">Pin</span>
                </Button>
                <Button variant="outline" size="sm" onClick={() => handleBulkAction('unpin')} className="text-xs">
                  <span className="hidden sm:inline">Unpin</span>
                  <span className="sm:hidden">Unpin</span>
                </Button>
                <Button variant="outline" size="sm" onClick={() => handleBulkAction('publish')} className="text-xs">
                  <span className="hidden sm:inline">Publish</span>
                  <span className="sm:hidden">Pub</span>
                </Button>
                <Button variant="outline" size="sm" onClick={() => handleBulkAction('archive')} className="text-xs">
                  <span className="hidden sm:inline">Archive</span>
                  <span className="sm:hidden">Arch</span>
                </Button>
                <Button variant="outline" size="sm" onClick={() => handleBulkAction('delete')} className="text-red-600 text-xs">
                  <Trash2 className="w-3 h-3 mr-1 sm:hidden" />
                  <span className="hidden sm:inline">Delete</span>
                  <span className="sm:hidden">Del</span>
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* Blog List */}
        {loading ? (
          <div className="text-center py-8 sm:py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
            <p className="mt-2 text-gray-600 text-sm sm:text-base">Loading blogs...</p>
          </div>
        ) : blogs.length === 0 ? (
          <div className="text-center py-8 sm:py-12">
            <p className="text-gray-600 text-sm sm:text-base">No blogs found.</p>
          </div>
        ) : (
          <div className="space-y-4">
            {/* Select All */}
            <div className="flex items-center gap-2 p-3 sm:p-4 bg-gray-50 rounded-lg">
              <Checkbox
                checked={selectedBlogs.length === blogs.length && blogs.length > 0}
                onCheckedChange={handleSelectAll}
              />
              <span className="text-xs sm:text-sm text-gray-600">
                Select all ({blogs.length} blogs)
              </span>
            </div>

            {/* Blog Cards */}
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-6">
              {blogs.map(blog => (
                <Card key={blog.id} className="hover:shadow-lg transition-shadow">
                  <CardHeader className="pb-3 p-4 sm:p-6">
                    <div className="flex justify-between items-start gap-2">
                      <div className="flex items-start gap-2 flex-1">
                        <Checkbox
                          checked={selectedBlogs.includes(blog.id)}
                          onCheckedChange={() => handleBlogSelect(blog.id)}
                          className="mt-1"
                        />
                        <div className="flex-1 min-w-0">
                          <CardTitle className="text-base sm:text-lg line-clamp-2 break-words">{blog.title}</CardTitle>
                          <div className="flex flex-wrap items-center gap-1 sm:gap-2 mt-2">
                            <Badge className={`${getStatusColor(blog.status)} text-xs px-2 py-1`}>
                              {blog.status}
                            </Badge>
                            <Badge className={`${getAuthorTypeColor(blog.authorType)} text-xs px-2 py-1`}>
                              {blog.authorType}
                            </Badge>
                            {blog.isFeatured && (
                              <Badge className="bg-yellow-100 text-yellow-800 text-xs px-2 py-1">
                                <Star className="w-3 h-3 mr-1" />
                                <span className="hidden sm:inline">Featured</span>
                                <span className="sm:hidden">Feat</span>
                              </Badge>
                            )}
                            {blog.isPinned && (
                              <Badge className="bg-red-100 text-red-800 text-xs px-2 py-1">
                                <Pin className="w-3 h-3 mr-1" />
                                <span className="hidden sm:inline">Pinned</span>
                                <span className="sm:hidden">Pin</span>
                              </Badge>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="flex flex-col sm:flex-row items-start sm:items-center gap-2 sm:gap-4 text-xs sm:text-sm text-gray-500 mt-2">
                      <div className="flex items-center gap-1">
                        <User className="w-3 h-3 sm:w-4 sm:h-4" />
                        <span className="truncate">{blog.authorName}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <Calendar className="w-3 h-3 sm:w-4 sm:h-4" />
                        {new Date(blog.createdAt).toLocaleDateString()}
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-3 sm:space-y-4 p-4 sm:p-6">
                    <p className="text-gray-600 text-xs sm:text-sm line-clamp-3">
                      {blog.excerpt || blog.content.substring(0, 150) + '...'}
                    </p>
                    <div className="flex flex-wrap items-center gap-3 sm:gap-4 text-xs sm:text-sm text-gray-500">
                      <div className="flex items-center gap-1">
                        <Eye className="w-3 h-3 sm:w-4 sm:h-4" />
                        {blog.views || 0}
                      </div>
                      <div className="flex items-center gap-1">
                        <TrendingUp className="w-3 h-3 sm:w-4 sm:h-4" />
                        {blog.likes || 0}
                      </div>
                      <div className="flex items-center gap-1">
                        <Tag className="w-3 h-3 sm:w-4 sm:h-4" />
                        <span className="truncate">{blog.category.replace(/_/g, ' ')}</span>
                      </div>
                    </div>
                    <div className="flex flex-col sm:flex-row gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        className="flex-1 text-xs sm:text-sm"
                        onClick={() => router.push(`/dashboard/blogs/edit/${blog.id}`, '_blank')}
                      >
                        <Edit className="w-3 h-3 mr-1 sm:hidden" />
                        <span className="hidden sm:inline">Edit</span>
                        <span className="sm:hidden">Edit</span>
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleDeleteBlog(blog.id)}
                        className="flex-1 text-red-600 hover:text-red-700 text-xs sm:text-sm"
                      >
                        <Trash2 className="w-3 h-3 mr-1 sm:hidden" />
                        <span className="hidden sm:inline">Delete</span>
                        <span className="sm:hidden">Del</span>
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            {/* Load More */}
            {hasMore && (
              <div className="text-center py-4">
                <Button variant="outline" onClick={() => loadBlogs(true)} disabled={loading} className="text-sm">
                  {loading ? 'Loading...' : 'Load More'}
                </Button>
              </div>
            )}
          </div>
        )}
    </div>
  )
}

export default function AdminBlogsPage() {
  return (
    <AdminOnlyRoute>
      <AdminBlogsPageContent />
    </AdminOnlyRoute>
  )
}
