import React, { useRef, useState } from 'react';
import {
  View,
  ScrollView,
  RefreshControl,
  Animated,
  PanGestureHandler,
  State,
  StyleSheet,
  Dimensions,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import * as Haptics from 'expo-haptics';
import { useTheme } from '../../providers/ThemeProvider';
import EnhancedLoading from './EnhancedLoading';

const { height: screenHeight } = Dimensions.get('window');

interface PullToRefreshProps {
  children: React.ReactNode;
  onRefresh: () => Promise<void>;
  refreshing?: boolean;
  pullDistance?: number;
  animationType?: 'spinner' | 'arrow' | 'custom';
  customRefreshComponent?: React.ReactNode;
  hapticFeedback?: boolean;
  style?: any;
  contentContainerStyle?: any;
}

export default function PullToRefresh({
  children,
  onRefresh,
  refreshing = false,
  pullDistance = 80,
  animationType = 'spinner',
  customRefreshComponent,
  hapticFeedback = true,
  style,
  contentContainerStyle,
}: PullToRefreshProps) {
  const { theme } = useTheme();
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [pullProgress, setPullProgress] = useState(0);
  
  // Animation values
  const translateY = useRef(new Animated.Value(0)).current;
  const rotateAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0)).current;
  const opacityAnim = useRef(new Animated.Value(0)).current;

  const handleRefresh = async () => {
    if (isRefreshing) return;

    setIsRefreshing(true);
    
    if (hapticFeedback) {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    }

    try {
      await onRefresh();
    } finally {
      setIsRefreshing(false);
    }
  };

  const onGestureEvent = Animated.event(
    [{ nativeEvent: { translationY: translateY } }],
    { 
      useNativeDriver: false,
      listener: (event: any) => {
        const { translationY } = event.nativeEvent;
        const progress = Math.min(Math.max(translationY / pullDistance, 0), 1);
        setPullProgress(progress);

        // Update animations based on pull progress
        Animated.timing(scaleAnim, {
          toValue: progress,
          duration: 0,
          useNativeDriver: true,
        }).start();

        Animated.timing(opacityAnim, {
          toValue: progress,
          duration: 0,
          useNativeDriver: true,
        }).start();

        if (animationType === 'arrow') {
          Animated.timing(rotateAnim, {
            toValue: progress >= 1 ? 1 : 0,
            duration: 200,
            useNativeDriver: true,
          }).start();
        }

        // Haptic feedback when reaching pull threshold
        if (progress >= 1 && hapticFeedback) {
          Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
        }
      }
    }
  );

  const onHandlerStateChange = (event: any) => {
    const { state, translationY } = event.nativeEvent;

    if (state === State.END) {
      if (translationY >= pullDistance) {
        handleRefresh();
      }

      // Reset animations
      Animated.parallel([
        Animated.spring(translateY, {
          toValue: 0,
          useNativeDriver: false,
        }),
        Animated.timing(scaleAnim, {
          toValue: 0,
          duration: 200,
          useNativeDriver: true,
        }),
        Animated.timing(opacityAnim, {
          toValue: 0,
          duration: 200,
          useNativeDriver: true,
        }),
      ]).start();

      setPullProgress(0);
    }
  };

  const renderRefreshIndicator = () => {
    if (customRefreshComponent) {
      return (
        <Animated.View
          style={[
            styles.refreshIndicator,
            {
              opacity: opacityAnim,
              transform: [{ scale: scaleAnim }],
            },
          ]}
        >
          {customRefreshComponent}
        </Animated.View>
      );
    }

    switch (animationType) {
      case 'arrow':
        const rotation = rotateAnim.interpolate({
          inputRange: [0, 1],
          outputRange: ['0deg', '180deg'],
        });

        return (
          <Animated.View
            style={[
              styles.refreshIndicator,
              {
                opacity: opacityAnim,
                transform: [{ scale: scaleAnim }, { rotate: rotation }],
              },
            ]}
          >
            <Ionicons
              name="arrow-down"
              size={24}
              color={theme.colors.primary}
            />
          </Animated.View>
        );

      case 'spinner':
      default:
        return (
          <Animated.View
            style={[
              styles.refreshIndicator,
              {
                opacity: opacityAnim,
                transform: [{ scale: scaleAnim }],
              },
            ]}
          >
            <EnhancedLoading
              type="spinner"
              size="small"
              color={theme.colors.primary}
            />
          </Animated.View>
        );
    }
  };

  const renderProgressIndicator = () => {
    const progressWidth = pullProgress * 60; // Max width of 60

    return (
      <View style={styles.progressContainer}>
        <View
          style={[
            styles.progressTrack,
            { backgroundColor: theme.colors.border },
          ]}
        >
          <Animated.View
            style={[
              styles.progressFill,
              {
                width: progressWidth,
                backgroundColor: theme.colors.primary,
              },
            ]}
          />
        </View>
      </View>
    );
  };

  return (
    <View style={[styles.container, style]}>
      {/* Refresh Indicator */}
      <Animated.View
        style={[
          styles.refreshContainer,
          {
            height: translateY.interpolate({
              inputRange: [0, pullDistance],
              outputRange: [0, pullDistance],
              extrapolate: 'clamp',
            }),
          },
        ]}
      >
        {renderRefreshIndicator()}
        {renderProgressIndicator()}
      </Animated.View>

      {/* Scrollable Content */}
      <PanGestureHandler
        onGestureEvent={onGestureEvent}
        onHandlerStateChange={onHandlerStateChange}
        enabled={!isRefreshing && !refreshing}
      >
        <Animated.View style={{ flex: 1 }}>
          <ScrollView
            style={{ flex: 1 }}
            contentContainerStyle={contentContainerStyle}
            refreshControl={
              <RefreshControl
                refreshing={isRefreshing || refreshing}
                onRefresh={handleRefresh}
                tintColor={theme.colors.primary}
                colors={[theme.colors.primary]}
                progressBackgroundColor={theme.colors.surface}
              />
            }
            scrollEventThrottle={16}
            bounces={true}
          >
            {children}
          </ScrollView>
        </Animated.View>
      </PanGestureHandler>
    </View>
  );
}

// Preset pull-to-refresh components
export const SpinnerPullToRefresh = (props: Omit<PullToRefreshProps, 'animationType'>) => (
  <PullToRefresh {...props} animationType="spinner" />
);

export const ArrowPullToRefresh = (props: Omit<PullToRefreshProps, 'animationType'>) => (
  <PullToRefresh {...props} animationType="arrow" />
);

export const CustomPullToRefresh = (
  props: Omit<PullToRefreshProps, 'animationType'> & { 
    customRefreshComponent: React.ReactNode 
  }
) => (
  <PullToRefresh {...props} animationType="custom" />
);

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  refreshContainer: {
    alignItems: 'center',
    justifyContent: 'flex-end',
    paddingBottom: 10,
    overflow: 'hidden',
  },
  refreshIndicator: {
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 8,
  },
  progressContainer: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  progressTrack: {
    width: 60,
    height: 3,
    borderRadius: 1.5,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    borderRadius: 1.5,
  },
});
