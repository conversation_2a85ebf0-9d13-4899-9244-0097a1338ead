'use client'

import { TopHeader } from "@/components/top-header"
import { <PERSON><PERSON> } from "@/components/header"
import { Foot<PERSON> } from "@/components/footer"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Search, Heart, ShoppingCart, Star, Filter, Loader2, Eye,MapPin } from "lucide-react"

import Link from "next/link"
import { ClientRoot, useCart } from "@/components/ClientRoot"
import { useState, useEffect } from "react"
import { useSearchParams } from "next/navigation"
import { shopService, ShopResponse } from "@/lib/services/shopService"
import { useSafeTranslation } from '@/hooks/use-safe-translation'
import { Amplify } from 'aws-amplify'
import awsExports from '@/src/aws-exports'
import FavoriteButton from '@/components/FavoriteButton'
import AddToCartButton from '@/components/AddToCartButton'
import { SimpleSEO, SimpleStructuredData } from '@/components/seo/SimpleSEO'

// Configure Amplify
Amplify.configure(awsExports)

function ShopPageContent() {

  const { t } = useSafeTranslation()
  const { cart } = useCart()
  const searchParams = useSearchParams()
  const [products, setProducts] = useState<ShopResponse[]>([])
  const [loading, setLoading] = useState(true)
  const [nextToken, setNextToken] = useState<string | undefined>(undefined)
  const [hasMoreData, setHasMoreData] = useState(true)
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedCategory, setSelectedCategory] = useState("all")
  const [priceRange, setPriceRange] = useState("all")
  const [selectedBrand, setSelectedBrand] = useState("all")
  const [selectedRating, setSelectedRating] = useState("all")
  const [selectedAvailability, setSelectedAvailability] = useState("all")
  const [selectedDiscount, setSelectedDiscount] = useState("all")
  const [sortBy, setSortBy] = useState("popular")

  const [wishlist, setWishlist] = useState<string[]>([])
  const [showCartPreview, setShowCartPreview] = useState(false)
  const [showFilters, setShowFilters] = useState(false)
  const [imageErrors, setImageErrors] = useState<Set<string>>(new Set())

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1)
  const [itemsPerPage] = useState(10) // 10 products per page
  const [totalServerItems, setTotalServerItems] = useState(0)
  const [serverPages, setServerPages] = useState<{[key: number]: ShopResponse[]}>({})
  const [loadedPages, setLoadedPages] = useState<Set<number>>(new Set([1]))

  // Load products on component mount
  useEffect(() => {
    loadProducts()

    // Handle URL search parameters
    const searchParam = searchParams.get('search')
    const locationParam = searchParams.get('location')
    const dateParam = searchParams.get('date')

    if (searchParam) {
      setSearchTerm(searchParam)
    }
    if (locationParam) {
      // You can add location-based filtering here if needed
      console.log('Location filter:', locationParam)
    }
    if (dateParam) {
      // setSelectedDate(new Date(dateParam)) // This line was commented out in the original file
    }
  }, [searchParams])

  // Helper functions
  const isInCart = (productId: string) => {
    return cart.some(item => item.id === productId)
  }

  const isInWishlist = (productId: string) => {
    return wishlist.includes(productId)
  }

  const toggleWishlist = (productId: string) => {
    setWishlist(prev =>
      prev.includes(productId)
        ? prev.filter(id => id !== productId)
        : [...prev, productId]
    )
  }



  const loadProducts = async (reset: boolean = false, pageToLoad: number = 1) => {
    try {
      setLoading(true)

      if (reset) {
        // Reset all pagination state
        setServerPages({})
        setLoadedPages(new Set())
        setCurrentPage(1)
        setNextToken(undefined)
      }

      // Calculate which page to load from server
      const serverPageToLoad = Math.ceil(pageToLoad / 1); // Since we're loading 10 items per page
      const skipPages = pageToLoad - 1;

      // Load more items initially to enable pagination (load 50 items)
      const response = await shopService.getAllShops(50, undefined)

      // Store all products for client-side pagination
      setProducts(response.shops)
      setTotalServerItems(response.shops.length)

      // Store the current page data
      const pages: {[key: number]: ShopResponse[]} = { ...serverPages }
      pages[pageToLoad] = response.shops
      setServerPages(pages)
      setLoadedPages(prev => new Set([...prev, pageToLoad]))

      setNextToken(response.nextToken)
      setHasMoreData(!!response.nextToken)
    } catch (error) {
      console.error('Error loading products:', error)
    } finally {
      setLoading(false)
    }
  }

  const clearAllFilters = () => {
    setSearchTerm("")
    setSelectedCategory("all")
    setPriceRange("all")
    setSelectedBrand("all")
    setSelectedRating("all")
    setSelectedAvailability("all")
    setSelectedDiscount("all")
    setSortBy("popular")
  }

  const hasActiveFilters = () => {
    return searchTerm || selectedCategory !== "all" || priceRange !== "all" ||
           selectedBrand !== "all" || selectedRating !== "all" ||
           selectedAvailability !== "all" || selectedDiscount !== "all"
  }

  // Helper function to get product image with fallback
  const getProductImage = (product: ShopResponse) => {
    if (imageErrors.has(product.id)) {
      return "/placeholder.svg"
    }

    // Check if product has images and the first image is valid
    const firstImage = product.images?.[0];
    if (firstImage && firstImage.trim() !== '' && firstImage !== 'undefined' && firstImage !== 'null') {
      return firstImage;
    }

    return "/placeholder.svg"
  }

  // Handle image load errors
  const handleImageError = (productId: string) => {
    setImageErrors(prev => new Set([...prev, productId]))
  }

  // Get unique values for filter options
  const getUniqueCategories = () => {
    const categories = products.map(p => p.category).filter(Boolean)
    return [...new Set(categories)]
  }

  const getUniqueBrands = () => {
    const brands = products.map(p => p.brand).filter(Boolean)
    return [...new Set(brands)]
  }

  // Filter and sort products
  const filteredProducts = products.filter(product => {
    const matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         product.category.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (product.brand && product.brand.toLowerCase().includes(searchTerm.toLowerCase()))

    const matchesCategory = selectedCategory === "all" || product.category.toLowerCase() === selectedCategory.toLowerCase()

    const matchesBrand = selectedBrand === "all" || (product.brand && product.brand.toLowerCase() === selectedBrand.toLowerCase())

    let matchesPrice = true
    if (priceRange !== "all") {
      const price = parseInt(product.price.replace(/[^\d]/g, ''))
      switch (priceRange) {
        case "0-5000":
          matchesPrice = price <= 5000
          break
        case "5000-25000":
          matchesPrice = price > 5000 && price <= 25000
          break
        case "25000-50000":
          matchesPrice = price > 25000 && price <= 50000
          break
        case "50000+":
          matchesPrice = price > 50000
          break
      }
    }

    const matchesRating = selectedRating === "all" || (() => {
      const rating = product.rating || 0
      switch (selectedRating) {
        case "4-plus": return rating >= 4
        case "3-plus": return rating >= 3
        case "2-plus": return rating >= 2
        default: return true
      }
    })()

    const matchesAvailability = selectedAvailability === "all" || (() => {
      switch (selectedAvailability) {
        case "in-stock": return product.inStock
        case "out-of-stock": return !product.inStock
        default: return true
      }
    })()

    const matchesDiscount = selectedDiscount === "all" || (() => {
      const discount = product.discount || 0
      switch (selectedDiscount) {
        case "on-sale": return discount > 0
        case "10-plus": return discount >= 10
        case "20-plus": return discount >= 20
        case "50-plus": return discount >= 50
        default: return true
      }
    })()

    return matchesSearch && matchesCategory && matchesBrand && matchesPrice &&
           matchesRating && matchesAvailability && matchesDiscount
  }).sort((a, b) => {
    switch (sortBy) {
      case "price-low":
        return parseInt(a.price.replace(/[^\d]/g, '')) - parseInt(b.price.replace(/[^\d]/g, ''))
      case "price-high":
        return parseInt(b.price.replace(/[^\d]/g, '')) - parseInt(a.price.replace(/[^\d]/g, ''))
      case "rating":
        return (b.rating || 0) - (a.rating || 0)
      default:
        return 0
    }
  })

  // Hybrid pagination logic
  const totalPages = Math.ceil(filteredProducts.length / itemsPerPage)
  const startIndex = (currentPage - 1) * itemsPerPage
  const endIndex = startIndex + itemsPerPage
  const paginatedProducts = filteredProducts.slice(startIndex, endIndex)

  // Reset and reload data when filters change
  useEffect(() => {
    setCurrentPage(1)
    loadProducts(true) // Reset and load first page
  }, [searchTerm, selectedCategory, priceRange, selectedBrand, selectedRating, selectedAvailability, selectedDiscount])

  // Traditional pagination functions
  const goToPage = (page: number) => {
    setCurrentPage(page)
    window.scrollTo({ top: 0, behavior: 'smooth' })
  }

  const goToPrevious = () => {
    if (currentPage > 1) {
      goToPage(currentPage - 1)
    }
  }

  const goToNext = () => {
    if (currentPage < totalPages) {
      goToPage(currentPage + 1)
    }
  }

  const getPageNumbers = () => {
    const pages = []
    const maxVisiblePages = 5
    let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2))
    let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1)

    if (endPage - startPage + 1 < maxVisiblePages) {
      startPage = Math.max(1, endPage - maxVisiblePages + 1)
    }

    for (let i = startPage; i <= endPage; i++) {
      pages.push(i)
    }
    return pages
  }

  // Load more data function for additional server data
  const loadMoreProducts = async () => {
    if (hasMoreData && !loading) {
      await loadProducts(false) // Load next page and append
    }
  }

  const showLoadMore = hasMoreData && !loading && totalPages <= Math.ceil(products.length / itemsPerPage)

  const categories = [
    t('shop.categories.all', 'All Categories'),
    t('shop.categories.bridalWear', 'Bridal Wear'),
    t('shop.categories.groomWear', 'Groom Wear'),
    t('shop.categories.jewelry', 'Jewelry'),
    t('shop.categories.beauty', 'Beauty'),
    t('shop.categories.decor', 'Decorations'),
    t('shop.categories.invitations', 'Invitations'),
    t('shop.categories.accessories', 'Accessories'),
    t('shop.categories.gifts', 'Gifts'),
  ]

  return (
    <>
      <SimpleSEO 
        title="Festive Shopping - Bridal Wear, Jewelry & Wedding Essentials | BookmyFestive"
        description="Shop for wedding essentials including bridal wear, jewelry, decorations, and more. Find everything you need for your perfect wedding day."
        keywords="Festive Shopping, bridal wear, wedding jewelry, wedding decorations, wedding essentials, bridal accessories, wedding shopping India"
        url="/shop"
        type="website"
      />
      <SimpleStructuredData />
      
      <div className="min-h-screen bg-white">
        <TopHeader />
        <Header />

        {/* Hero Section */}
        <section className="relative bg-gradient-to-r from-[#F8F5F0] to-[#F6C244] py-12 md:py-20 overflow-hidden">
          <div className="absolute inset-0 w-full h-full z-0">
            <div className="absolute inset-0 bg-black/60"></div>
            <img src="/shop_hero.webp" alt="Festive Shopping" className="w-full h-full object-cover object-center" />
          </div>
          <div className="relative container mx-auto px-4 z-10">
            <div className="text-center mb-8 md:mb-12">
              <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold text-white mb-4 md:mb-6 drop-shadow-2xl">{t('shop.title', 'Festive Shopping')}</h1>
              <p className="text-lg md:text-xl text-white/90 drop-shadow-lg">{t('shop.subtitle', 'Everything you need for your perfect wedding day')}</p>
            </div>

            {/* Cart Summary Bar */}
            {cart.length > 0 && (
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-3 md:p-4 mb-4 md:mb-6 max-w-2xl mx-auto">
                <div className="flex flex-col md:flex-row items-start md:items-center justify-between gap-3 md:gap-0">
                  <div className="flex items-center gap-3">
                      <ShoppingCart className="h-4 w-4 md:h-5 md:w-5 text-primary" />
                    <div>
                      <p className="font-medium text-gray-900 text-sm md:text-base">
                        {cart.length} item{cart.length !== 1 ? 's' : ''} in cart
                      </p>
                      <p className="text-xs md:text-sm text-gray-600">
                        Total: ₹{cart.reduce((total, item) => total + (item.price * item.quantity), 0).toLocaleString()}
                      </p>
                    </div>
                  </div>
                  <div className="flex gap-2 w-full md:w-auto">
                    <Link href="/cart" className="flex-1 md:flex-none">
                      <Button variant="outline" size="sm" className="w-full md:w-auto text-xs md:text-sm">
                        View Cart
                      </Button>
                    </Link>
                    <Link href="/checkout" className="flex-1 md:flex-none">
                      <Button size="sm" className="bg-primary hover:bg-primary/90 w-full md:w-auto text-xs md:text-sm">
                        Checkout
                      </Button>
                    </Link>
                  </div>
                </div>
              </div>
            )}

            {/* Simple Search and Filters */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 md:p-6 max-w-4xl mx-auto">
              {/* Desktop Search and Filters */}
              <div className="hidden md:grid grid-cols-3 gap-4">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    placeholder="Search products..."
                    className="pl-10 h-11"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </div>
                <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                  <SelectTrigger className="h-11">
                    <SelectValue placeholder="Category" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Categories</SelectItem>
                    {getUniqueCategories().map((category) => (
                      <SelectItem key={category} value={category.toLowerCase()}>
                        {category}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <Select value={sortBy} onValueChange={setSortBy}>
                  <SelectTrigger className="h-11">
                    <SelectValue placeholder="Sort by" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="popular">Most Popular</SelectItem>
                    <SelectItem value="price-low">Price: Low to High</SelectItem>
                    <SelectItem value="price-high">Price: High to Low</SelectItem>
                    <SelectItem value="rating">Highest Rated</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Mobile Search and Filters */}
              <div className="md:hidden">
                <div className="grid grid-cols-2 gap-2 mb-2">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                    <Input
                      placeholder="Search"
                      className="pl-10 h-10 text-sm"
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                    />
                  </div>
                  <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                    <SelectTrigger className="h-10 text-sm">
                      <SelectValue placeholder="Category" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Categories</SelectItem>
                      {getUniqueCategories().map((category) => (
                        <SelectItem key={category} value={category.toLowerCase()}>
                          {category}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="grid grid-cols-2 gap-2">
                  <Select value={sortBy} onValueChange={setSortBy}>
                    <SelectTrigger className="h-10 text-sm">
                      <SelectValue placeholder="Sort" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="popular">Most Popular</SelectItem>
                      <SelectItem value="price-low">Price: Low-High</SelectItem>
                      <SelectItem value="price-high">Price: High-Low</SelectItem>
                      <SelectItem value="rating">Highest Rated</SelectItem>
                    </SelectContent>
                  </Select>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={clearAllFilters}
                    className="h-10 text-sm"
                  >
                    Clear All
                  </Button>
                </div>
              </div>

      {hasActiveFilters() && (
                  <div className="flex items-center justify-between mt-4 pt-4 border-t border-gray-100">
                    <div className="flex flex-wrap gap-2">
              {searchTerm && (
                <Badge variant="secondary" className="rounded-full">
                  Search: "{searchTerm}"
                  <button
                    onClick={() => setSearchTerm("")}
                    className="ml-2 hover:text-red-600"
                  >
                    ×
                  </button>
                </Badge>
              )}
              {selectedCategory !== "all" && (
                <Badge variant="secondary" className="rounded-full">
                      {selectedCategory}
                  <button
                    onClick={() => setSelectedCategory("all")}
                    className="ml-2 hover:text-red-600"
                  >
                    ×
                  </button>
                </Badge>
              )}
                    </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={clearAllFilters}
                      className="text-gray-500 hover:text-gray-700"
                  >
                    Clear All
                  </Button>
                  </div>
                )}
            </div>
          </div>
        </section>



      {/* Products Grid */}
      <section className="py-8 md:py-12">
        <div className="container mx-auto px-4">
          <div className="flex justify-between items-center mb-4 md:mb-6">
            <h2 className="text-lg md:text-xl font-semibold text-gray-900">Products ({filteredProducts.length})</h2>
          </div>

          {loading ? (
            <div className="flex items-center justify-center py-8 md:py-12">
              <Loader2 className="h-5 w-5 md:h-6 md:w-6 animate-spin text-primary" />
              <span className="ml-2 text-gray-600 text-sm md:text-base">Loading products...</span>
            </div>
          ) : filteredProducts.length === 0 ? (
            <div className="text-center py-8 md:py-12">
              <div className="text-gray-500 mb-4 text-sm md:text-base">No products found</div>
              <Button onClick={() => loadProducts(true)} variant="outline" className="text-sm md:text-base">
                Refresh Products
              </Button>
            </div>
          ) : (
            <div className="grid grid-cols-2 md:grid-cols-2 lg:grid-cols-3 gap-2 md:gap-8">
              {paginatedProducts.map((product) => (
                <Link
                  key={product.id}
                  href={`/shop/${product.id}`}
                  className="group block overflow-hidden rounded-2xl md:rounded-3xl shadow-lg hover:shadow-2xl transition-all duration-300 border-0 bg-white hover:-translate-y-1 md:hover:-translate-y-2 focus:outline-none focus:ring-2 focus:ring-primary scale-100 h-full"
                  style={{ width: '100%', margin: '0 auto' }}
                  tabIndex={0}
                  aria-label={`View details for ${product.name}`}
                >
                  <Card className="border-0 bg-white h-full flex flex-col">
                    <div className="relative overflow-hidden flex-shrink-0">
                      <img
                        src={getProductImage(product)}
                        alt={product.name || 'Product Image'}
                        className="w-full h-48 md:h-56 object-cover transition-transform duration-300 group-hover:scale-110"
                        onError={() => handleImageError(product.id)}
                      />
                      <div className="absolute inset-0 bg-gradient-to-t from-black/50 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                      
                      {/* Favorite Button - Only show on hover */}
                      <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                        <FavoriteButton
                          entityId={product.id}
                          entityType="SHOP_ITEM"
                          entityData={{
                            name: product.name,
                            image: product.images?.[0],
                            price: product.price,
                            location: product.brand,
                            city: product.category,
                            state: '',
                            rating: product.rating,
                            reviewCount: product.reviewCount,
                            description: product.description
                          }}
                          variant="secondary"
                          size="sm"
                          className="bg-white/90 hover:bg-white shadow-sm"
                        />
                      </div>
                    </div>
                    <CardContent className="p-4 md:p-6 flex flex-col flex-1">
                      <div className="flex flex-col h-full">
                        <div className="flex-1">
                          <div className="flex items-start justify-between mb-2">
                            <div className="flex-1">
                              <h3 className="font-bold text-lg md:text-xl text-gray-900 mb-1 line-clamp-2 min-h-[3rem]">{product.name}</h3>
                              <p className="text-primary font-medium text-xs md:text-sm uppercase tracking-wide">{product.category}</p>
                            </div>
                          </div>
                          <div className="flex items-center text-gray-500 text-xs md:text-sm mb-2 md:mb-3">
                            <MapPin className="h-3 w-3 md:h-4 md:w-4 mr-1 text-primary" />
                            <span className="line-clamp-1">{product.brand || 'Brand'}</span>
                          </div>
                        </div>
                        <div className="mt-auto">
                          <div className="flex items-center justify-between text-xs md:text-sm">
                            <span className="font-bold text-lg md:text-xl text-primary">
                              {product.discount && product.discount > 0 ? (
                                <>
                                  <span>{product.price}</span>
                                  <span className="text-sm md:text-base text-gray-400 line-through font-medium ml-2">{product.originalPrice || ''}</span>
                                  <span className="text-xs md:text-sm font-bold text-red-600 ml-1">{product.discount}% OFF</span>
                                </>
                              ) : (
                                product.price
                              )}
                            </span>
                            <span className="flex items-center gap-1 bg-yellow-50 px-2 py-1 rounded-full ml-2">
                              <Star className="h-3 w-3 md:h-4 md:w-4 fill-yellow-400 text-yellow-400" />
                              <span className="text-xs md:text-sm font-semibold text-gray-900">{product.rating || "N/A"}</span>
                            </span>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </Link>
              ))}
            </div>
          )}

          {/* Traditional Pagination */}
          {filteredProducts.length > 0 && (
            <div className="flex flex-col items-center mt-12 space-y-4">
              {/* Results info */}
              <div className="text-xs md:text-sm text-gray-600 text-center">
                Showing {startIndex + 1}-{Math.min(endIndex, filteredProducts.length)} of {filteredProducts.length} products
                {hasMoreData && (
                  <span className="ml-2 text-primary">
                    ({products.length} loaded from server)
                  </span>
                )}
              </div>

              {/* Pagination controls */}
              {totalPages > 1 ? (
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  onClick={goToPrevious}
                  disabled={currentPage === 1}
                  className="px-2 md:px-3 py-2 text-xs md:text-sm"
                >
                  Previous
                </Button>

                {getPageNumbers().map((page) => (
                  <Button
                    key={page}
                    variant={currentPage === page ? "default" : "outline"}
                    onClick={() => goToPage(page)}
                    className={`px-2 md:px-3 py-2 text-xs md:text-sm ${
                      currentPage === page
                        ? "bg-primary text-white"
                        : "hover:bg-gray-100"
                    }`}
                  >
                    {page}
                  </Button>
                ))}

                <Button
                  variant="outline"
                  onClick={goToNext}
                  disabled={currentPage === totalPages}
                  className="px-2 md:px-3 py-2 text-xs md:text-sm"
                >
                  Next
                </Button>
              </div>
              ) : (
                <div className="text-center py-4">
                  <span className="text-sm text-gray-500">
                    Showing all {filteredProducts.length} products
                  </span>
                </div>
              )}

              {/* Load More Server Data Button */}
              {showLoadMore && (
                <Button
                  onClick={loadMoreProducts}
                  disabled={loading}
                  variant="outline"
                  className="mt-4 px-4 md:px-6 py-2 text-xs md:text-sm"
                >
                  {loading ? (
                    <>
                      <div className="animate-spin rounded-full h-3 w-3 md:h-4 md:w-4 border-b-2 border-primary mr-2"></div>
                      Loading more...
                    </>
                  ) : (
                    'Load More from Server'
                  )}
                </Button>
              )}
            </div>
          )}
        </div>
      </section>

      <Footer />

      {/* Cart Preview Toast */}
      {showCartPreview && (
        <div className="fixed bottom-4 right-4 z-50 bg-green-600 text-white p-4 rounded-lg shadow-lg transform transition-all duration-300 animate-in slide-in-from-bottom-2">
          <div className="flex items-center gap-3">
            <div className="bg-white/20 rounded-full p-2">
              <ShoppingCart className="h-5 w-5" />
            </div>
            <div>
              <p className="font-medium">Added to cart!</p>
              <p className="text-sm opacity-90">{cart.length} item{cart.length !== 1 ? 's' : ''} in cart</p>
            </div>
            <Link href="/cart">
              <Button variant="secondary" size="sm" className="ml-2">
                View Cart
              </Button>
            </Link>
          </div>
        </div>
      )}
    </div>
    </>
  )
}

export default function ShopPage() {
  return (
    <ClientRoot>
      <ShopPageContent />
    </ClientRoot>
  )
}
