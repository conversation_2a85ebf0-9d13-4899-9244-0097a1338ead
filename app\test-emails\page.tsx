"use client"

import { useState, useEffect } from 'react'

export default function TestEmailsPage() {
  const [email, setEmail] = useState('<EMAIL>')
  const [loading, setLoading] = useState('')
  const [results, setResults] = useState<Record<string, boolean>>({})
  const [error, setError] = useState<string | null>(null)
  const [emailService, setEmailService] = useState<any>(null)

  // Dynamically import emailService to avoid SSR issues
  useEffect(() => {
    const loadEmailService = async () => {
      try {
        const { emailService } = await import('@/lib/services/emailService')
        setEmailService(emailService)
      } catch (err) {
        console.error('Failed to load email service:', err)
        setError('Failed to load email service. Please refresh the page.')
      }
    }
    
    loadEmailService()
  }, [])

  // Check if user is authenticated (basic check)
  const isAuthenticated = () => {
    try {
      // Check if there's a user session in localStorage or sessionStorage
      const hasUserSession = localStorage.getItem('amplify-authenticator-authToken') || 
                           sessionStorage.getItem('amplify-authenticator-authToken') ||
                           localStorage.getItem('CognitoIdentityServiceProvider') ||
                           sessionStorage.getItem('CognitoIdentityServiceProvider');
      return !!hasUserSession;
    } catch {
      return false;
    }
  }

  const testEmail = async (type: string, emailFunction: () => Promise<boolean>) => {
    if (!emailService) {
      setError('Email service not loaded yet. Please wait.')
      return
    }

    setLoading(type)
    try {
      const result = await emailFunction()
      setResults(prev => ({ ...prev, [type]: result }))
      if (result) {
        console.log(`✅ ${type} email test passed`)
        setError(null) // Clear any previous errors
      } else {
        console.log(`❌ ${type} email test failed`)
        setError(`${type} email test failed. Check console for details.`)
      }
    } catch (error: any) {
      console.error(`Error testing ${type}:`, error)
      setResults(prev => ({ ...prev, [type]: false }))
      
      // Handle specific error types
      if (error.message?.includes('Not Authorized')) {
        setError(`Authorization error for ${type}: You need to be logged in to test emails.`)
      } else if (error.message?.includes('Variable') && error.message?.includes('invalid value')) {
        setError(`Invalid data for ${type}: Please check the email format and try again.`)
      } else {
        setError(`Error testing ${type}: ${error.message || 'Unknown error'}`)
      }
    }
    setLoading('')
  }

  const tests = [
    {
      name: 'Welcome Signup',
      key: 'welcome-signup',
      test: () => emailService?.sendUserWelcomeEmail({
        email,
        firstName: 'John',
        lastName: 'Doe',
        type: 'signup',
        isVendor: false
      })
    },
    {
      name: 'Welcome Login',
      key: 'welcome-login', 
      test: () => emailService?.sendUserWelcomeEmail({
        email,
        firstName: 'John',
        lastName: 'Doe',
        type: 'login',
        isVendor: false
      })
    },
    {
      name: 'Vendor Welcome',
      key: 'vendor-welcome',
      test: () => emailService?.sendUserWelcomeEmail({
        email,
        firstName: 'Jane',
        lastName: 'Smith',
        type: 'signup',
        isVendor: true,
        businessName: 'Smith Photography'
      })
    },
    {
      name: 'Newsletter Welcome',
      key: 'newsletter-welcome',
      test: () => emailService?.sendNewsletterWelcomeEmail({
        email,
        firstName: 'John',
        lastName: 'Doe',
        type: 'newsletter',
        interests: ['PHOTOGRAPHY', 'VENUES'],
        preferences: {
          weddingTips: true,
          vendorRecommendations: true,
          frequency: 'WEEKLY'
        }
      })
    },
    {
      name: 'Booking Confirmation',
      key: 'booking-confirmation',
      test: () => emailService?.sendBookingConfirmationEmail({
        email,
        userName: 'There',
        bookingId: 'booking_test_123',
        entityName: 'Dream Venue',
        entityType: 'venue',
        eventDate: '2024-12-25',
        eventTime: '6:00 PM',
        amount: '₹50,000',
        status: 'confirmed'
      })
    }
  ]

  return (
    <div className="container mx-auto p-6 max-w-4xl">
      <div className="bg-white rounded-lg shadow-lg p-6">
        <h1 className="text-2xl font-bold mb-6">Email Testing Dashboard</h1>
        
        {/* Error Display */}
        {error && (
          <div className="p-4 bg-red-50 border border-red-200 rounded-lg mb-4">
            <div className="flex items-center">
              <span className="text-red-600 mr-2">⚠️</span>
              <span className="text-red-800">{error}</span>
              <button 
                className="ml-auto px-3 py-1 text-sm border border-red-300 rounded hover:bg-red-100"
                onClick={() => setError(null)}
              >
                Dismiss
              </button>
            </div>
          </div>
        )}

        {/* Loading State */}
        {!emailService && (
          <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg mb-4">
            <div className="flex items-center">
              <span className="text-blue-600 mr-2">⏳</span>
              <span className="text-blue-800">Loading email service...</span>
            </div>
          </div>
        )}

        {/* Authentication Status */}
        <div className="p-4 bg-gray-50 border border-gray-200 rounded-lg mb-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <span className={`mr-2 ${isAuthenticated() ? 'text-green-600' : 'text-red-600'}`}>
                {isAuthenticated() ? '🔐' : '⚠️'}
              </span>
              <span className="text-sm">
                <strong>Authentication Status:</strong> {isAuthenticated() ? 'Logged In' : 'Not Logged In'}
              </span>
            </div>
            {!isAuthenticated() && (
              <a 
                href="/login" 
                className="px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700"
              >
                Log In
              </a>
            )}
          </div>
          {!isAuthenticated() && (
            <p className="text-xs text-gray-600 mt-2">
              You need to be logged in to test emails. Log in above or use command-line tests instead.
            </p>
          )}
        </div>

        <div className="mb-6">
          <label className="block text-sm font-medium mb-2">Test Email Address</label>
          <input
            type="email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            placeholder="Enter email to test"
            disabled={!emailService}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>

        <div className="grid gap-4 md:grid-cols-2 mb-6">
          {tests.map(({ name, key, test }) => (
            <div key={key} className="border rounded-lg p-4">
              <div className="flex items-center justify-between">
                <span className="font-medium">{name}</span>
                <div className="flex items-center gap-2">
                  {results[key] !== undefined && (
                    <span className={`text-sm ${results[key] ? 'text-green-600' : 'text-red-600'}`}>
                      {results[key] ? '✅' : '❌'}
                    </span>
                  )}
                  <button
                    className="px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50"
                    onClick={() => testEmail(key, test)}
                    disabled={loading === key || !emailService}
                  >
                    {loading === key ? 'Testing...' : 'Test'}
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>

        <div className="p-4 bg-gray-50 rounded-lg mb-6">
          <h3 className="font-medium mb-2">Instructions:</h3>
          <ul className="text-sm space-y-1 text-gray-600">
            <li>• Enter your email address above</li>
            <li>• Click "Test" for any email type</li>
            <li>• Check browser console for email content</li>
            <li>• Green ✅ = Success, Red ❌ = Failed</li>
            <li>• Email logs are stored in database</li>
          </ul>
          
          <div className="mt-3 p-3 bg-yellow-50 border border-yellow-200 rounded">
            <p className="text-sm text-yellow-800">
              <strong>Note:</strong> You must be logged in to test emails. If you see authorization errors, 
              please log in first or use the command-line test scripts instead.
            </p>
          </div>
        </div>

        <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <h3 className="font-medium text-blue-800 mb-2">Alternative Testing Methods:</h3>
          <p className="text-sm text-blue-600 mb-2">
            Instead of the web interface, you can test email services using the command line:
          </p>
          <div className="bg-gray-100 p-3 rounded font-mono text-sm mb-3">
            <div>npm run test:email</div>
            <div>npm run test:booking</div>
          </div>
          <p className="text-xs text-blue-700">
            <strong>💡 Tip:</strong> Command-line tests are more reliable and don't require authentication. 
            They're perfect for development and testing purposes.
          </p>
        </div>
      </div>
    </div>
  )
}