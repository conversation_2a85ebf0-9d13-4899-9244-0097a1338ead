"use client"

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { emailService } from '@/lib/services/emailService'
import { generateClient } from '@aws-amplify/api'
import { listEmailLogs } from '@/src/graphql/queries'

const client = generateClient()

export default function TestEmailsPage() {
  const [email, setEmail] = useState('<EMAIL>')
  const [loading, setLoading] = useState('')
  const [results, setResults] = useState<Record<string, boolean>>({})
  const [emailLogs, setEmailLogs] = useState<any[]>([])
  const [loadingLogs, setLoadingLogs] = useState(false)

  const testEmail = async (type: string, emailFunction: () => Promise<boolean>) => {
    setLoading(type)
    try {
      const result = await emailFunction()
      setResults(prev => ({ ...prev, [type]: result }))
      if (result) {
        await loadEmailLogs() // Refresh logs after successful email
      }
    } catch (error) {
      console.error(`Error testing ${type}:`, error)
      setResults(prev => ({ ...prev, [type]: false }))
    }
    setLoading('')
  }

  const loadEmailLogs = async () => {
    setLoadingLogs(true)
    try {
      const result = await client.graphql({
        query: listEmailLogs,
        variables: {
          limit: 10
        },
        authMode: 'apiKey'
      })
      setEmailLogs(result.data.listEmailLogs.items || [])
    } catch (error) {
      console.error('Error loading email logs:', error)
      setEmailLogs([])
    }
    setLoadingLogs(false)
  }

  useEffect(() => {
    loadEmailLogs()
  }, [])

  const tests = [
    {
      name: 'Welcome Signup',
      key: 'welcome-signup',
      test: () => emailService.sendUserWelcomeEmail({
        email,
        firstName: 'John',
        lastName: 'Doe',
        type: 'signup',
        isVendor: false
      })
    },
    {
      name: 'Welcome Login',
      key: 'welcome-login', 
      test: () => emailService.sendUserWelcomeEmail({
        email,
        firstName: 'John',
        lastName: 'Doe',
        type: 'login',
        isVendor: false
      })
    },
    {
      name: 'Vendor Welcome',
      key: 'vendor-welcome',
      test: () => emailService.sendUserWelcomeEmail({
        email,
        firstName: 'Jane',
        lastName: 'Smith',
        type: 'signup',
        isVendor: true,
        businessName: 'Smith Photography'
      })
    },
    {
      name: 'Newsletter Welcome',
      key: 'newsletter-welcome',
      test: () => emailService.sendNewsletterWelcomeEmail({
        email,
        firstName: 'John',
        lastName: 'Doe',
        type: 'newsletter',
        interests: ['PHOTOGRAPHY', 'VENUES'],
        preferences: {
          weddingTips: true,
          vendorRecommendations: true,
          frequency: 'WEEKLY'
        }
      })
    },
    {
      name: 'Booking Confirmation',
      key: 'booking-confirmation',
      test: () => emailService.sendBookingConfirmationEmail({
        email,
        userName: 'John Doe',
        bookingId: 'BK123456',
        entityName: 'Dream Venue',
        entityType: 'venue',
        eventDate: '2024-06-15',
        eventTime: '6:00 PM',
        amount: '₹50,000',
        status: 'confirmed'
      })
    }
  ]

  return (
    <div className="container mx-auto p-6 max-w-4xl">
      <Card>
        <CardHeader>
          <CardTitle>Email Testing Dashboard</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <label className="block text-sm font-medium mb-2">Test Email Address</label>
            <Input
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              placeholder="Enter email to test"
            />
          </div>

          <div className="grid gap-4 md:grid-cols-2">
            {tests.map(({ name, key, test }) => (
              <Card key={key}>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <span className="font-medium">{name}</span>
                    <div className="flex items-center gap-2">
                      {results[key] !== undefined && (
                        <span className={`text-sm ${results[key] ? 'text-green-600' : 'text-red-600'}`}>
                          {results[key] ? '✅' : '❌'}
                        </span>
                      )}
                      <Button
                        size="sm"
                        onClick={() => testEmail(key, test)}
                        disabled={loading === key}
                      >
                        {loading === key ? 'Testing...' : 'Test'}
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          <div className="mt-6 p-4 bg-gray-50 rounded-lg">
            <h3 className="font-medium mb-2">Instructions:</h3>
            <ul className="text-sm space-y-1 text-gray-600">
              <li>• Enter your email address above</li>
              <li>• Click "Test" for any email type</li>
              <li>• Check browser console for email content</li>
              <li>• Green ✅ = Success, Red ❌ = Failed</li>
              <li>• Email logs are stored in database below</li>
            </ul>
          </div>

          <Card className="mt-6">
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle>Recent Email Logs</CardTitle>
                <Button size="sm" onClick={loadEmailLogs} disabled={loadingLogs}>
                  {loadingLogs ? 'Loading...' : 'Refresh'}
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              {emailLogs.length === 0 ? (
                <p className="text-gray-500 text-center py-4">No email logs found</p>
              ) : (
                <div className="space-y-2 max-h-96 overflow-y-auto">
                  {emailLogs.map((log) => (
                    <div key={log.id} className="border rounded p-3 text-sm">
                      <div className="flex justify-between items-start mb-1">
                        <span className="font-medium">{log.subject}</span>
                        <span className={`px-2 py-1 rounded text-xs ${
                          log.status === 'SENT' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                        }`}>
                          {log.status}
                        </span>
                      </div>
                      <div className="text-gray-600">
                        <div>To: {log.recipient}</div>
                        <div>Type: {log.emailType}</div>
                        <div>Sent: {new Date(log.sentAt).toLocaleString()}</div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </CardContent>
      </Card>
    </div>
  )
}