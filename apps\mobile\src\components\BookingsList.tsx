import React, { useState, useEffect } from 'react';
import { View, Text, ScrollView, TouchableOpacity, Alert, RefreshControl } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { <PERSON>, CardContent, CardHeader, CardTitle } from './ui/Card';
import { Button } from './ui/Button';
import { Badge } from './ui/Badge';
import { Input } from './ui/Input';
import { useAuth } from '../providers/AuthProvider';
import { useTheme } from '../providers/ThemeProvider';

interface Booking {
  id: string;
  customerName: string;
  customerEmail: string;
  customerPhone: string;
  vendorName: string;
  vendorId: string;
  serviceName: string;
  serviceType: string;
  eventDate: string;
  eventTime?: string;
  guestCount?: number;
  location?: string;
  status: 'PENDING' | 'CONFIRMED' | 'CANCELLED' | 'COMPLETED';
  priority: 'LOW' | 'MEDIUM' | 'HIGH';
  totalAmount?: number;
  estimatedCost?: number;
  vendorNotes?: string;
  customerNotes?: string;
  createdAt: string;
  updatedAt: string;
}

interface BookingsListProps {
  isVendor: boolean;
  onBookingPress?: (booking: Booking) => void;
}

export function BookingsList({ isVendor, onBookingPress }: BookingsListProps) {
  const { user, userProfile } = useAuth();
  const { theme } = useTheme();
  const [bookings, setBookings] = useState<Booking[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [selectedStatus, setSelectedStatus] = useState('all');
  const [selectedBooking, setSelectedBooking] = useState<Booking | null>(null);
  const [updating, setUpdating] = useState(false);
  const [updateForm, setUpdateForm] = useState({
    status: '',
    vendorNotes: '',
    estimatedCost: '',
    priority: ''
  });

  // Mock data for demonstration
  const mockBookings: Booking[] = [
    {
      id: '1',
      customerName: 'Priya Sharma',
      customerEmail: '<EMAIL>',
      customerPhone: '+91 8148376909',
      vendorName: 'Royal Photography',
      vendorId: 'vendor1',
      serviceName: 'Wedding Photography Package',
      serviceType: 'PHOTOGRAPHY',
      eventDate: '2024-03-15',
      eventTime: '10:00 AM',
      guestCount: 200,
      location: 'Chennai, Tamil Nadu',
      status: 'PENDING',
      priority: 'HIGH',
      totalAmount: 50000,
      customerNotes: 'Need pre-wedding shoot as well',
      createdAt: '2024-01-15T10:00:00Z',
      updatedAt: '2024-01-15T10:00:00Z'
    },
    {
      id: '2',
      customerName: 'Rahul Kumar',
      customerEmail: '<EMAIL>',
      customerPhone: '+91 9876543211',
      vendorName: 'Elegant Decorations',
      vendorId: 'vendor2',
      serviceName: 'Complete Wedding Decoration',
      serviceType: 'DECORATION',
      eventDate: '2024-03-20',
      eventTime: '6:00 AM',
      guestCount: 300,
      location: 'Bangalore, Karnataka',
      status: 'CONFIRMED',
      priority: 'MEDIUM',
      totalAmount: 75000,
      estimatedCost: 75000,
      vendorNotes: 'Confirmed for March 20th. Setup starts at 6 AM.',
      createdAt: '2024-01-10T14:30:00Z',
      updatedAt: '2024-01-12T09:15:00Z'
    },
    {
      id: '3',
      customerName: 'Anita Reddy',
      customerEmail: '<EMAIL>',
      customerPhone: '+91 9876543212',
      vendorName: 'Spice Garden Catering',
      vendorId: 'vendor3',
      serviceName: 'Traditional South Indian Catering',
      serviceType: 'CATERING',
      eventDate: '2024-04-05',
      guestCount: 150,
      location: 'Hyderabad, Telangana',
      status: 'COMPLETED',
      priority: 'LOW',
      totalAmount: 45000,
      vendorNotes: 'Event completed successfully',
      createdAt: '2024-01-05T11:20:00Z',
      updatedAt: '2024-04-05T20:00:00Z'
    }
  ];

  useEffect(() => {
    if (user) {
      loadBookings();
    }
  }, [user, selectedStatus, isVendor]);

  const loadBookings = async () => {
    try {
      setLoading(true);
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Filter bookings based on user role and status
      let filteredBookings = mockBookings;
      
      if (isVendor) {
        // For vendors, show bookings for their services
        filteredBookings = mockBookings.filter(booking => 
          booking.vendorId === userProfile?.id || booking.vendorName.includes('Royal') // Mock filter
        );
      } else {
        // For customers, show their bookings
        filteredBookings = mockBookings.filter(booking => 
          booking.customerEmail === userProfile?.email
        );
      }
      
      if (selectedStatus !== 'all') {
        filteredBookings = filteredBookings.filter(booking => 
          booking.status.toLowerCase() === selectedStatus.toLowerCase()
        );
      }
      
      setBookings(filteredBookings);
    } catch (error) {
      console.error('Error loading bookings:', error);
      Alert.alert('Error', 'Failed to load bookings');
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadBookings();
    setRefreshing(false);
  };

  const handleStatusUpdate = async (bookingId: string, newStatus: string) => {
    try {
      setUpdating(true);
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500));
      
      setBookings(prev => prev.map(booking => 
        booking.id === bookingId 
          ? { ...booking, status: newStatus as any, updatedAt: new Date().toISOString() }
          : booking
      ));
      
      Alert.alert('Success', 'Booking status updated successfully');
    } catch (error) {
      console.error('Error updating booking:', error);
      Alert.alert('Error', 'Failed to update booking status');
    } finally {
      setUpdating(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'PENDING': return '#F59E0B';
      case 'CONFIRMED': return '#10B981';
      case 'CANCELLED': return '#EF4444';
      case 'COMPLETED': return '#6366F1';
      default: return theme.colors.textSecondary;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'HIGH': return '#EF4444';
      case 'MEDIUM': return '#F59E0B';
      case 'LOW': return '#10B981';
      default: return theme.colors.textSecondary;
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-IN', {
      day: 'numeric',
      month: 'short',
      year: 'numeric'
    });
  };

  const formatAmount = (amount: number) => {
    return `₹${amount.toLocaleString('en-IN')}`;
  };

  const renderBookingCard = (booking: Booking) => (
    <TouchableOpacity
      key={booking.id}
      onPress={() => onBookingPress?.(booking)}
      style={{ marginBottom: 16 }}
    >
      <Card>
        <CardHeader>
          <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'flex-start' }}>
            <View style={{ flex: 1 }}>
              <CardTitle style={{ fontSize: 16 }}>
                {booking.serviceName}
              </CardTitle>
              <Text style={{ fontSize: 14, color: theme.colors.textSecondary, marginTop: 2 }}>
                {isVendor ? `Customer: ${booking.customerName}` : `Vendor: ${booking.vendorName}`}
              </Text>
            </View>
            <View style={{ alignItems: 'flex-end', gap: 4 }}>
              <Badge 
                variant="default" 
                style={{ backgroundColor: getStatusColor(booking.status) }}
              >
                {booking.status}
              </Badge>
              <Badge 
                variant="outline" 
                size="sm"
                style={{ borderColor: getPriorityColor(booking.priority) }}
              >
                {booking.priority}
              </Badge>
            </View>
          </View>
        </CardHeader>

        <CardContent>
          <View style={{ gap: 8 }}>
            {/* Event Details */}
            <View style={{ flexDirection: 'row', alignItems: 'center', gap: 8 }}>
              <Ionicons name="calendar" size={16} color={theme.colors.textSecondary} />
              <Text style={{ fontSize: 14, color: theme.colors.text }}>
                {formatDate(booking.eventDate)}
                {booking.eventTime && ` at ${booking.eventTime}`}
              </Text>
            </View>

            {booking.location && (
              <View style={{ flexDirection: 'row', alignItems: 'center', gap: 8 }}>
                <Ionicons name="location" size={16} color={theme.colors.textSecondary} />
                <Text style={{ fontSize: 14, color: theme.colors.text }}>
                  {booking.location}
                </Text>
              </View>
            )}

            {booking.guestCount && (
              <View style={{ flexDirection: 'row', alignItems: 'center', gap: 8 }}>
                <Ionicons name="people" size={16} color={theme.colors.textSecondary} />
                <Text style={{ fontSize: 14, color: theme.colors.text }}>
                  {booking.guestCount} guests
                </Text>
              </View>
            )}

            {/* Amount */}
            {(booking.totalAmount || booking.estimatedCost) && (
              <View style={{ flexDirection: 'row', alignItems: 'center', gap: 8 }}>
                <Ionicons name="card" size={16} color={theme.colors.textSecondary} />
                <Text style={{ fontSize: 14, fontWeight: '600', color: theme.colors.primary }}>
                  {formatAmount(booking.totalAmount || booking.estimatedCost || 0)}
                </Text>
              </View>
            )}

            {/* Notes */}
            {(booking.vendorNotes || booking.customerNotes) && (
              <View style={{ 
                backgroundColor: theme.colors.muted,
                padding: 8,
                borderRadius: 6,
                marginTop: 4
              }}>
                <Text style={{ fontSize: 12, color: theme.colors.text }}>
                  {booking.vendorNotes || booking.customerNotes}
                </Text>
              </View>
            )}

            {/* Action Buttons for Vendors */}
            {isVendor && booking.status === 'PENDING' && (
              <View style={{ flexDirection: 'row', gap: 8, marginTop: 8 }}>
                <Button
                  title="Confirm"
                  onPress={() => handleStatusUpdate(booking.id, 'CONFIRMED')}
                  variant="default"
                  size="sm"
                  style={{ flex: 1 }}
                  disabled={updating}
                />
                <Button
                  title="Decline"
                  onPress={() => handleStatusUpdate(booking.id, 'CANCELLED')}
                  variant="outline"
                  size="sm"
                  style={{ flex: 1 }}
                  disabled={updating}
                />
              </View>
            )}

            {/* Contact Buttons */}
            <View style={{ flexDirection: 'row', gap: 8, marginTop: 8 }}>
              <Button
                title="Call"
                onPress={() => {/* Handle call */}}
                variant="outline"
                size="sm"
                icon="call"
                style={{ flex: 1 }}
              />
              <Button
                title="Message"
                onPress={() => {/* Handle message */}}
                variant="outline"
                size="sm"
                icon="chatbubble"
                style={{ flex: 1 }}
              />
            </View>
          </View>
        </CardContent>
      </Card>
    </TouchableOpacity>
  );

  const statusFilters = [
    { key: 'all', label: 'All' },
    { key: 'pending', label: 'Pending' },
    { key: 'confirmed', label: 'Confirmed' },
    { key: 'completed', label: 'Completed' },
    { key: 'cancelled', label: 'Cancelled' }
  ];

  return (
    <View style={{ flex: 1 }}>
      {/* Status Filter */}
      <ScrollView 
        horizontal 
        showsHorizontalScrollIndicator={false}
        style={{ flexGrow: 0, paddingVertical: 8 }}
        contentContainerStyle={{ paddingHorizontal: 16, gap: 8 }}
      >
        {statusFilters.map((filter) => (
          <TouchableOpacity
            key={filter.key}
            onPress={() => setSelectedStatus(filter.key)}
            style={{
              paddingHorizontal: 16,
              paddingVertical: 8,
              borderRadius: 20,
              backgroundColor: selectedStatus === filter.key 
                ? theme.colors.primary 
                : theme.colors.muted,
            }}
          >
            <Text style={{
              fontSize: 14,
              fontWeight: '600',
              color: selectedStatus === filter.key 
                ? '#fff' 
                : theme.colors.text,
            }}>
              {filter.label}
            </Text>
          </TouchableOpacity>
        ))}
      </ScrollView>

      {/* Bookings List */}
      <ScrollView
        style={{ flex: 1 }}
        contentContainerStyle={{ padding: 16 }}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
        }
      >
        {loading ? (
          <View style={{ alignItems: 'center', paddingVertical: 32 }}>
            <Text style={{ color: theme.colors.textSecondary }}>Loading bookings...</Text>
          </View>
        ) : bookings.length === 0 ? (
          <View style={{ alignItems: 'center', paddingVertical: 32 }}>
            <Ionicons name="calendar-outline" size={48} color={theme.colors.textSecondary} />
            <Text style={{ 
              fontSize: 16, 
              fontWeight: '600', 
              color: theme.colors.text,
              marginTop: 16 
            }}>
              No Bookings Found
            </Text>
            <Text style={{ 
              fontSize: 14, 
              color: theme.colors.textSecondary,
              textAlign: 'center',
              marginTop: 4 
            }}>
              {isVendor 
                ? 'You don\'t have any bookings yet' 
                : 'You haven\'t made any bookings yet'
              }
            </Text>
          </View>
        ) : (
          bookings.map(renderBookingCard)
        )}
      </ScrollView>
    </View>
  );
}
