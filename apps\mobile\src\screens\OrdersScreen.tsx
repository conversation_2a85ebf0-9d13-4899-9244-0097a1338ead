import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Image,
  RefreshControl,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { useTheme } from '../providers/ThemeProvider';
import { useAuth } from '../providers/AuthProvider';
import LoadingSpinner from '../components/LoadingSpinner';
import EmptyState from '../components/EmptyState';

interface OrderItem {
  id: string;
  name: string;
  price: number;
  quantity: number;
  image?: string;
}

interface Order {
  id: string;
  items: OrderItem[];
  total: number;
  status: 'pending' | 'confirmed' | 'processing' | 'shipped' | 'delivered' | 'cancelled';
  createdAt: string;
  deliveryAddress: {
    fullName: string;
    addressLine1: string;
    city: string;
    state: string;
    pincode: string;
  };
  paymentMethod: string;
  trackingNumber?: string;
  estimatedDelivery?: string;
}

export default function OrdersScreen() {
  const navigation = useNavigation();
  const { theme } = useTheme();
  const { user } = useAuth();
  
  const [orders, setOrders] = useState<Order[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    loadOrders();
  }, []);

  const loadOrders = async () => {
    try {
      setLoading(true);
      
      // TODO: Replace with actual GraphQL query
      // const userOrders = await graphqlService.listOrdersByUser(user?.id);
      
      // Mock data for now
      const mockOrders: Order[] = [
        {
          id: 'ORDER_001',
          items: [
            {
              id: '1',
              name: 'Elegant Wedding Lehenga',
              price: 25000,
              quantity: 1,
              image: 'https://example.com/lehenga.jpg',
            },
            {
              id: '2',
              name: 'Bridal Jewelry Set',
              price: 15000,
              quantity: 1,
              image: 'https://example.com/jewelry.jpg',
            },
          ],
          total: 40000,
          status: 'delivered',
          createdAt: '2024-01-15T10:30:00Z',
          deliveryAddress: {
            fullName: 'Priya Sharma',
            addressLine1: '123 MG Road',
            city: 'Bangalore',
            state: 'Karnataka',
            pincode: '560001',
          },
          paymentMethod: 'card',
          trackingNumber: 'TRK123456789',
          estimatedDelivery: '2024-01-20',
        },
        {
          id: 'ORDER_002',
          items: [
            {
              id: '3',
              name: 'Groom Sherwani',
              price: 18000,
              quantity: 1,
              image: 'https://example.com/sherwani.jpg',
            },
          ],
          total: 18000,
          status: 'shipped',
          createdAt: '2024-01-20T14:15:00Z',
          deliveryAddress: {
            fullName: 'Priya Sharma',
            addressLine1: '123 MG Road',
            city: 'Bangalore',
            state: 'Karnataka',
            pincode: '560001',
          },
          paymentMethod: 'upi',
          trackingNumber: 'TRK987654321',
          estimatedDelivery: '2024-01-25',
        },
      ];
      
      setOrders(mockOrders);
    } catch (error) {
      console.error('Failed to load orders:', error);
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadOrders();
    setRefreshing(false);
  };

  const getStatusColor = (status: Order['status']) => {
    switch (status) {
      case 'pending':
        return theme.colors.warning;
      case 'confirmed':
      case 'processing':
        return theme.colors.info;
      case 'shipped':
        return theme.colors.primary;
      case 'delivered':
        return theme.colors.success;
      case 'cancelled':
        return theme.colors.error;
      default:
        return theme.colors.textSecondary;
    }
  };

  const getStatusText = (status: Order['status']) => {
    switch (status) {
      case 'pending':
        return 'Order Pending';
      case 'confirmed':
        return 'Order Confirmed';
      case 'processing':
        return 'Processing';
      case 'shipped':
        return 'Shipped';
      case 'delivered':
        return 'Delivered';
      case 'cancelled':
        return 'Cancelled';
      default:
        return status;
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-IN', {
      day: 'numeric',
      month: 'short',
      year: 'numeric',
    });
  };

  const handleOrderPress = (order: Order) => {
    navigation.navigate('OrderDetail' as never, { orderId: order.id } as never);
  };

  const renderOrderItem = ({ item: order }: { item: Order }) => (
    <TouchableOpacity
      style={[styles.orderCard, { backgroundColor: theme.colors.surface }]}
      onPress={() => handleOrderPress(order)}
    >
      {/* Order Header */}
      <View style={styles.orderHeader}>
        <View style={styles.orderInfo}>
          <Text style={[styles.orderId, { color: theme.colors.text }]}>
            {order.id}
          </Text>
          <Text style={[styles.orderDate, { color: theme.colors.textSecondary }]}>
            Ordered on {formatDate(order.createdAt)}
          </Text>
        </View>
        
        <View style={[styles.statusBadge, { backgroundColor: getStatusColor(order.status) + '20' }]}>
          <Text style={[styles.statusText, { color: getStatusColor(order.status) }]}>
            {getStatusText(order.status)}
          </Text>
        </View>
      </View>

      {/* Order Items Preview */}
      <View style={styles.itemsPreview}>
        {order.items.slice(0, 2).map((item, index) => (
          <View key={item.id} style={styles.itemPreview}>
            {item.image ? (
              <Image source={{ uri: item.image }} style={styles.itemImage} />
            ) : (
              <View style={[styles.itemImagePlaceholder, { backgroundColor: theme.colors.border }]}>
                <Ionicons name="image-outline" size={16} color={theme.colors.textSecondary} />
              </View>
            )}
            <View style={styles.itemInfo}>
              <Text style={[styles.itemName, { color: theme.colors.text }]} numberOfLines={1}>
                {item.name}
              </Text>
              <Text style={[styles.itemDetails, { color: theme.colors.textSecondary }]}>
                Qty: {item.quantity} • ₹{item.price.toLocaleString()}
              </Text>
            </View>
          </View>
        ))}
        
        {order.items.length > 2 && (
          <Text style={[styles.moreItems, { color: theme.colors.textSecondary }]}>
            +{order.items.length - 2} more items
          </Text>
        )}
      </View>

      {/* Order Footer */}
      <View style={styles.orderFooter}>
        <View style={styles.totalContainer}>
          <Text style={[styles.totalLabel, { color: theme.colors.textSecondary }]}>
            Total Amount
          </Text>
          <Text style={[styles.totalAmount, { color: theme.colors.text }]}>
            ₹{order.total.toLocaleString()}
          </Text>
        </View>
        
        <View style={styles.actionContainer}>
          {order.trackingNumber && (
            <TouchableOpacity style={styles.trackButton}>
              <Ionicons name="location-outline" size={16} color={theme.colors.primary} />
              <Text style={[styles.trackText, { color: theme.colors.primary }]}>
                Track
              </Text>
            </TouchableOpacity>
          )}
          
          <Ionicons name="chevron-forward" size={20} color={theme.colors.textSecondary} />
        </View>
      </View>
    </TouchableOpacity>
  );

  if (loading) {
    return <LoadingSpinner fullScreen text="Loading orders..." />;
  }

  if (orders.length === 0) {
    return (
      <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
        <EmptyState
          icon="bag-outline"
          title="No orders yet"
          message="Start shopping to see your orders here!"
          actionText="Browse Products"
          onAction={() => navigation.navigate('Shop' as never)}
          fullScreen
        />
      </View>
    );
  }

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <FlatList
        data={orders}
        renderItem={renderOrderItem}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.ordersList}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[theme.colors.primary]}
            tintColor={theme.colors.primary}
          />
        }
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  ordersList: {
    padding: 16,
  },
  orderCard: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  orderHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  orderInfo: {
    flex: 1,
  },
  orderId: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  orderDate: {
    fontSize: 12,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '600',
  },
  itemsPreview: {
    marginBottom: 12,
  },
  itemPreview: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  itemImage: {
    width: 40,
    height: 40,
    borderRadius: 6,
    marginRight: 12,
  },
  itemImagePlaceholder: {
    width: 40,
    height: 40,
    borderRadius: 6,
    marginRight: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  itemInfo: {
    flex: 1,
  },
  itemName: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 2,
  },
  itemDetails: {
    fontSize: 12,
  },
  moreItems: {
    fontSize: 12,
    fontStyle: 'italic',
    marginLeft: 52,
  },
  orderFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
    paddingTop: 12,
  },
  totalContainer: {
    flex: 1,
  },
  totalLabel: {
    fontSize: 12,
    marginBottom: 2,
  },
  totalAmount: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  actionContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  trackButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
    paddingHorizontal: 8,
    paddingVertical: 4,
  },
  trackText: {
    fontSize: 12,
    fontWeight: '600',
  },
});
