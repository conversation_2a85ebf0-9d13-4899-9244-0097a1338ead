import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { CartItem, Cart } from '../shared/types';
import { cartService } from '../services/cartService';

interface CartContextType {
  cart: Cart;
  addItem: (item: CartItem) => Promise<void>;
  addToCart: (item: CartItem) => Promise<void>; // Alias for addItem
  removeItem: (itemId: string | number) => Promise<void>;
  updateQuantity: (itemId: string | number, quantity: number) => Promise<void>;
  clearCart: () => Promise<void>;
  getTotalItems: () => number;
  getTotal: () => number;
  isItemInCart: (itemId: string | number) => boolean;
  isLoading: boolean;
}

const CartContext = createContext<CartContextType | undefined>(undefined);

interface CartProviderProps {
  children: ReactNode;
}

export function CartProvider({ children }: CartProviderProps) {
  const [cart, setCart] = useState<Cart>({
    items: [],
    total: 0,
    itemCount: 0,
    lastUpdated: new Date().toISOString(),
  });
  const [isLoading, setIsLoading] = useState(true);

  // Load cart on app start
  useEffect(() => {
    loadCart();
  }, []);

  const loadCart = async () => {
    try {
      setIsLoading(true);
      const loadedCart = await cartService.loadCart();
      setCart(loadedCart);
    } catch (error) {
      console.error('Failed to load cart:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const addItem = async (item: CartItem) => {
    try {
      const updatedCart = await cartService.addItem(item);
      setCart(updatedCart);
    } catch (error) {
      console.error('Failed to add item to cart:', error);
      throw error;
    }
  };

  const removeItem = async (itemId: string | number) => {
    try {
      const updatedCart = await cartService.removeItem(itemId);
      setCart(updatedCart);
    } catch (error) {
      console.error('Failed to remove item from cart:', error);
      throw error;
    }
  };

  const updateQuantity = async (itemId: string | number, quantity: number) => {
    try {
      const updatedCart = await cartService.updateQuantity(itemId, quantity);
      setCart(updatedCart);
    } catch (error) {
      console.error('Failed to update item quantity:', error);
      throw error;
    }
  };

  const clearCart = async () => {
    try {
      const updatedCart = await cartService.clearCart();
      setCart(updatedCart);
    } catch (error) {
      console.error('Failed to clear cart:', error);
      throw error;
    }
  };

  const getTotalItems = () => {
    return cart.itemCount;
  };

  const getTotal = () => {
    return cart.total;
  };

  const isItemInCart = (itemId: string | number) => {
    return cart.items.some(item => item.id === itemId);
  };

  const contextValue: CartContextType = {
    cart,
    addItem,
    addToCart: addItem, // Alias for addItem
    removeItem,
    updateQuantity,
    clearCart,
    getTotalItems,
    getTotal,
    isItemInCart,
    isLoading,
  };

  return (
    <CartContext.Provider value={contextValue}>
      {children}
    </CartContext.Provider>
  );
}

export function useCart(): CartContextType {
  const context = useContext(CartContext);
  if (context === undefined) {
    throw new Error('useCart must be used within a CartProvider');
  }
  return context;
}
