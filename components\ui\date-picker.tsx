"use client"

import * as React from "react"
import { Calendar as CalendarIcon, X } from "lucide-react"
import { format } from "date-fns"

import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import { Calendar } from "@/components/ui/calendar"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"

interface DatePickerProps {
  date?: Date
  onSelect?: (date: Date | undefined) => void
  placeholder?: string
  disabled?: (date: Date) => boolean
  className?: string
  buttonClassName?: string
  calendarClassName?: string
  showClearButton?: boolean
  variant?: "default" | "home" | "search"
}

export function DatePicker({
  date,
  onSelect,
  placeholder = "Select Date",
  disabled,
  className,
  buttonClassName,
  calendarClassName,
  showClearButton = true,
  variant = "default"
}: DatePickerProps) {
  const [open, setOpen] = React.useState(false)

  const handleSelect = (selectedDate: Date | undefined) => {
    onSelect?.(selectedDate)
    setOpen(false) // Auto-close when date is selected
  }

  const handleClear = (e: React.MouseEvent) => {
    e.stopPropagation()
    onSelect?.(undefined)
  }

  // Variant-specific styling
  const getButtonStyles = () => {
    switch (variant) {
      case "home":
        return "w-full h-10 pl-10 pr-8 text-left font-normal justify-start border-2 border-primary/30 bg-white rounded-lg"
      case "search":
        return "w-full h-9 pl-10 pr-8 text-left font-normal justify-start text-sm border-2 border-primary/30 bg-white rounded-lg"
      default:
        return "w-full justify-start text-left font-normal"
    }
  }

  const getIconStyles = () => {
    switch (variant) {
      case "home":
        return "absolute left-3 top-3 h-4 w-4 text-gray-400"
      case "search":
        return "absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"
      default:
        return "mr-2 h-4 w-4"
    }
  }

  const getClearButtonStyles = () => {
    switch (variant) {
      case "home":
        return "absolute right-2 top-3 h-4 w-4 text-gray-400 hover:text-gray-600 cursor-pointer"
      case "search":
        return "absolute right-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 hover:text-gray-600 cursor-pointer"
      default:
        return "ml-auto h-4 w-4 text-gray-400 hover:text-gray-600 cursor-pointer"
    }
  }

  return (
    <div className={cn("relative", className)}>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            className={cn(
              getButtonStyles(),
              !date && "text-muted-foreground",
              buttonClassName
            )}
          >
            {variant === "default" && <CalendarIcon className={getIconStyles()} />}
            {variant !== "default" && (
              <CalendarIcon className={getIconStyles()} />
            )}
            {date ? (
              <>
                {format(date, "PPP")}
                {showClearButton && (
                  <X
                    className={getClearButtonStyles()}
                    onClick={handleClear}
                  />
                )}
              </>
            ) : (
              <span>{placeholder}</span>
            )}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-0" align="start">
          <Calendar
            mode="single"
            selected={date}
            onSelect={handleSelect}
            disabled={disabled}
            initialFocus
            className={calendarClassName}
          />
        </PopoverContent>
      </Popover>
    </div>
  )
}

// Specific variants for common use cases
export function HomeDatePicker(props: Omit<DatePickerProps, "variant">) {
  return <DatePicker {...props} variant="home" />
}

export function SearchDatePicker(props: Omit<DatePickerProps, "variant">) {
  return <DatePicker {...props} variant="search" />
}

export function DefaultDatePicker(props: Omit<DatePickerProps, "variant">) {
  return <DatePicker {...props} variant="search" />
}
