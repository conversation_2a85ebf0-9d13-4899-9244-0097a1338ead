// Google Places API service for location search
export interface GooglePlace {
  place_id: string;
  description: string;
  structured_formatting: {
    main_text: string;
    secondary_text: string;
  };
  types: string[];
}

export interface PlaceDetails {
  name: string;
  formatted_address: string;
  geometry: {
    location: {
      lat: number;
      lng: number;
    };
  };
  address_components: Array<{
    long_name: string;
    short_name: string;
    types: string[];
  }>;
}

class GooglePlacesService {
  private apiKey: string;
  private sessionToken: string;

  constructor() {
    this.apiKey = process.env.NEXT_PUBLIC_GOOGLE_PLACES_API_KEY || 'AIzaSyCWa_K6KugK2B_AU-MB4xsnV548Yp8dEGo';
    this.sessionToken = this.generateSessionToken();
  }

  private generateSessionToken(): string {
    return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
  }

  async searchPlaces(input: string): Promise<GooglePlace[]> {
    if (!input.trim()) return [];

    try {
      const response = await fetch(
        `/api/places?input=${encodeURIComponent(input)}&sessiontoken=${this.sessionToken}`
      );

      const data = await response.json();
      
      if (data.error) {
        console.error('Google Places API error:', data.error);
        return [];
      }
      
      return data.predictions || [];
    } catch (error) {
      console.error('Google Places API error:', error);
      return [];
    }
  }

  async getPlaceDetails(placeId: string): Promise<PlaceDetails | null> {
    if (!placeId || !this.apiKey) return null;

    try {
      const response = await fetch(
        `https://maps.googleapis.com/maps/api/place/details/json?` +
        `place_id=${placeId}&` +
        `fields=name,formatted_address,geometry,address_components&` +
        `sessiontoken=${this.sessionToken}&` +
        `key=${this.apiKey}`
      );

      const data = await response.json();
      return data.result || null;
    } catch (error) {
      console.error('Google Place Details API error:', error);
      return null;
    }
  }
}

export const googlePlacesService = new GooglePlacesService();