'use client'
import React from 'react'
import Link from 'next/link'
import Image from 'next/image'
import { usePathname, useRouter } from 'next/navigation'
import { useAuth } from '@/contexts/AuthContext'
import { useEffect, useState } from 'react'
import { AuthenticatedRoute } from '@/components/RouteProtection'
import { UserTypeWelcome } from '@/components/UserTypeIndicator'

import AuthRoutingService from '@/lib/services/authRouting'
import {
  Home,
  User,
  Users,
  Briefcase,
  ShoppingBag,
  MapPin,
  Calendar,
  Star,
  Package,
  Image as ImageIcon,
  Settings,
  Menu,
  X,
  ArrowLeft,
  Bell,
  LogOut,
  Search,
  Shield,
  MessageSquare,
  Database,
  MessageCircle,
  FileText,
  BarChart,
  Mail,
  CreditCard
} from 'lucide-react'

// Dynamic menu based on user type
const getMenuForUserType = (userType: string | null) => {
  if (process.env.NODE_ENV === 'development') {
    console.log('getMenuForUserType called with userType:', userType);
  }

  // First two items are always Dashboard and Profile for all users
  const primaryItems = [
    { href: '/dashboard', label: 'Dashboard', icon: Home },
    { href: '/dashboard/profile', label: 'Profile', icon: User },
  ];

  const customerItems = [
    { href: '/dashboard/my-inquiries', label: 'My Inquiries', icon: MessageCircle },
    { href: '/dashboard/bookings', label: 'Bookings', icon: Calendar },
    { href: '/dashboard/orders', label: 'My Orders', icon: ShoppingBag },
    { href: '/dashboard/invoices', label: 'My Invoices', icon: FileText },
    { href: '/dashboard/blogs', label: 'My Blogs', icon: FileText },
    { href: '/dashboard/settings', label: 'Settings', icon: Settings },
  ];

  const vendorItems = [
    { href: '/dashboard/vendors', label: 'My Vendors', icon: Briefcase },
    { href: '/dashboard/shop', label: 'Shop Management', icon: ShoppingBag },
    { href: '/dashboard/venues', label: 'Venue Management', icon: MapPin },
    { href: '/dashboard/vendor-orders', label: 'Order Management', icon: Package },
    { href: '/dashboard/inquiries', label: 'Inquiry Management', icon: MessageCircle },
    { href: '/dashboard/invoices', label: 'My Invoices', icon: FileText },
    { href: '/dashboard/pricing', label: 'Subscription & Billing', icon: CreditCard },
    { href: '/dashboard/blogs', label: 'Blog Management', icon: FileText },
    { href: '/dashboard/bookings', label: 'Bookings', icon: Calendar },
    { href: '/dashboard/vendor-reviews', label: 'My Reviews', icon: Star },
    { href: '/dashboard/admin-tools', label: 'Business Tools', icon: Database },
    { href: '/dashboard/settings', label: 'Settings', icon: Settings },
  ];

  const adminItems = [
    { href: '/dashboard/admin-users', label: 'User Management', icon: Users },
    { href: '/dashboard/admin-vendors', label: 'Vendor Management', icon: Briefcase },
    { href: '/dashboard/admin-venues', label: 'Venue Management', icon: MapPin },
    { href: '/dashboard/admin-shops', label: 'Shop Management', icon: ShoppingBag },
    { href: '/dashboard/admin-blogs', label: 'Blog Management', icon: FileText },
    { href: '/dashboard/admin-orders', label: 'Order Management', icon: Package },
    { href: '/dashboard/invoices', label: 'Invoice Management', icon: FileText },
    { href: '/dashboard/admin-inquiries', label: 'Inquiry Management', icon: MessageCircle },
    { href: '/dashboard/admin-reviews', label: 'Review Management', icon: Star },
    { href: '/dashboard/admin-newsletter', label: 'Newsletter Management', icon: Mail },
    { href: '/dashboard/admin-pricing', label: 'Pricing Management', icon: CreditCard },
    { href: '/dashboard/contacts', label: 'Contact Management', icon: MessageSquare },
    { href: '/dashboard/admin-tools', label: 'Admin Tools', icon: Database },
  ];

  const superAdminItems = [
    { href: '/dashboard/super-admin', label: 'Super Admin Dashboard', icon: Bell },
    { href: '/dashboard/super-admin/users', label: 'User Management', icon: User },
    { href: '/dashboard/super-admin/system', label: 'System Management', icon: Database },
    { href: '/dashboard/super-admin/analytics', label: 'Platform Analytics', icon: BarChart },
  ];

  switch (userType) {
    case 'super_admin':
      if (process.env.NODE_ENV === 'development') console.log('Returning super admin menu');
      return [...primaryItems, ...superAdminItems, ...adminItems];
    case 'admin':
      if (process.env.NODE_ENV === 'development') console.log('Returning admin menu');
      return [...primaryItems, ...adminItems];
    case 'vendor':
      if (process.env.NODE_ENV === 'development') console.log('Returning vendor menu');
      return [...primaryItems, ...vendorItems];
    case 'customer':
      if (process.env.NODE_ENV === 'development') console.log('Returning customer menu');
      return [...primaryItems, ...customerItems];
    default:
      if (process.env.NODE_ENV === 'development') console.log('Returning default (customer) menu for userType:', userType);
      return [...primaryItems, ...customerItems];
  }
};

export default function DashboardLayout({ children }: { children: React.ReactNode }) {
  const pathname = usePathname()
  const router = useRouter()
  const { isAuthenticated, isLoading, signOut, userType, userProfile } = useAuth()
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false)
  const [notificationOpen, setNotificationOpen] = useState(false)

  const handleLogout = async () => {
    try {
      await signOut()
      router.push('/')
    } catch (error) {
      console.error('Logout error:', error)
    }
  }

  // Close notification dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element
      if (notificationOpen && !target.closest('.notification-dropdown')) {
        setNotificationOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [notificationOpen])

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push('/login')
    }
  }, [isAuthenticated, isLoading, router])

  // Force re-render when userProfile changes to ensure menu updates
  useEffect(() => {
    if (userProfile && process.env.NODE_ENV === 'development') {
      console.log('User profile updated, forcing menu refresh:', userProfile)
    }
  }, [userProfile])

  // Optimize menu generation by memoizing it with fallback logic
  const menu = React.useMemo(() => {
    if (process.env.NODE_ENV === 'development') {
      console.log('Dashboard Layout - User Type:', userType, 'User Profile:', userProfile)
    }

    // Fallback: if userType is null but userProfile indicates vendor, use vendor menu
    let effectiveUserType = userType
    if (!effectiveUserType && userProfile) {
      if (userProfile.isSuperAdmin || userProfile.role === 'SUPER_ADMIN') {
        effectiveUserType = 'super_admin'
      } else if (userProfile.isAdmin || userProfile.role === 'ADMIN') {
        effectiveUserType = 'admin'
      } else if (userProfile.isVendor || userProfile.role === 'VENDOR' ||
                 (userProfile.businessInfo && userProfile.businessInfo.businessName)) {
        effectiveUserType = 'vendor'
      } else {
        effectiveUserType = 'customer'
      }
      if (process.env.NODE_ENV === 'development') {
        console.log('Fallback user type determined:', effectiveUserType)
      }
    }

    return getMenuForUserType(effectiveUserType)
  }, [userType, userProfile])

  if (isLoading) {
    return (
      <div className="min-h-screen bg-white flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-primary mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading dashboard...</p>
        </div>
      </div>
    )
  }

  if (!isAuthenticated) {
    return null // Will redirect to login
  }

  return (
    <AuthenticatedRoute>
      <div className="min-h-screen bg-gray-50">
      {/* Top Navbar */}
      <nav className="bg-white border-b shadow-sm sticky top-0 z-50">
        <div className="px-3 sm:px-4 lg:px-6">
          <div className="flex justify-between items-center h-14 sm:h-16">
            {/* Left Section */}
            <div className="flex items-center gap-2 sm:gap-4">
              {/* Mobile Menu Button */}
              <button
                onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
                className="md:hidden p-2 rounded-lg hover:bg-gray-100 transition-colors touch-manipulation"
                aria-label="Toggle menu"
              >
                {mobileMenuOpen ? <X className="w-5 h-5 sm:w-6 sm:h-6" /> : <Menu className="w-5 h-5 sm:w-6 sm:h-6" />}
              </button>

              {/* Logo */}
              <Link href="/" className="flex items-center gap-2 group">
                <Image
                  src="/BookmyFestive-logo.png"
                  alt="BookmyFestive"
                  width={120}
                  height={30}
                  className="h-7 sm:h-8 w-auto"
                  priority
                />
              </Link>

              {/* Back to Home Button */}
              <Link
                href="/"
                className="hidden sm:flex items-center gap-2 px-2 sm:px-3 py-1 sm:py-2 text-xs sm:text-sm font-medium text-gray-600 hover:text-primary hover:bg-gray-100 rounded-lg transition-all duration-200"
              >
                <ArrowLeft className="w-3 h-3 sm:w-4 sm:h-4" />
                <span className="hidden md:inline">Back to Home</span>
              </Link>
            </div>

            {/* Center Section - Search */}
            <div className="hidden md:flex flex-1 max-w-md mx-4 lg:mx-8">
              <div className="relative w-full">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search dashboard..."
                  className="w-full pl-10 pr-4 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary transition-colors"
                />
              </div>
            </div>

            {/* Right Section */}
            <div className="flex items-center gap-2 sm:gap-3">
              {/* User Info */}
              <div className="hidden sm:flex items-center gap-3">
                {/* User Name and Type */}
                <div className="text-right">
                  {userProfile && (
                    <div className="text-sm font-medium text-gray-900">
                      {userProfile.firstName && userProfile.lastName
                        ? `${userProfile.firstName} ${userProfile.lastName}`
                        : userProfile.firstName || userProfile.lastName || 'User'
                      }
                    </div>
                  )}
                  {userType && (
                    <div className="flex items-center gap-1 justify-end">
                      {userType === 'super_admin' && (
                        <>
                          <Bell className="w-3 h-3 text-purple-600" />
                          <span className="text-xs text-purple-600">Super Admin</span>
                        </>
                      )}
                      {userType === 'admin' && (
                        <>
                          <Shield className="w-3 h-3 text-orange-600" />
                          <span className="text-xs text-orange-600">Administrator</span>
                        </>
                      )}
                      {userType === 'vendor' && (
                        <>
                          <Briefcase className="w-3 h-3 text-primary" />
                          <span className="text-xs text-primary">Business Account</span>
                        </>
                      )}
                      {userType === 'customer' && (
                        <>
                          <User className="w-3 h-3 text-blue-600" />
                          <span className="text-xs text-blue-600">Customer</span>
                        </>
                      )}
                    </div>
                  )}
                </div>

                {/* User Avatar */}
                <div className="w-7 h-7 sm:w-8 sm:h-8 bg-gray-200 rounded-full flex items-center justify-center">
                  {userProfile?.profilePhoto ? (
                    <Image
                      src={userProfile.profilePhoto}
                      alt="Profile"
                      width={32}
                      height={32}
                      className="w-7 h-7 sm:w-8 sm:h-8 rounded-full object-cover"
                    />
                  ) : (
                    <User className="w-3 h-3 sm:w-4 sm:h-4 text-gray-500" />
                  )}
                </div>
              </div>

              {/* Notifications */}
              <div className="relative notification-dropdown">
                <button 
                  onClick={() => setNotificationOpen(!notificationOpen)}
                  className="relative p-1.5 sm:p-2 text-gray-600 hover:text-primary hover:bg-gray-100 rounded-lg transition-colors touch-manipulation"
                >
                  <Bell className="w-4 h-4 sm:w-5 sm:h-5" />
                  <span className="absolute -top-1 -right-1 h-3 w-3 sm:h-4 sm:w-4 bg-red-500 text-white text-xs rounded-full flex items-center justify-center">
                    2
                  </span>
                </button>
                
                                 {/* Notification Dropdown */}
                 {notificationOpen && (
                   <div className="absolute right-0 mt-2 w-64 sm:w-72 md:w-80 bg-white rounded-lg shadow-lg border border-gray-200 z-50 max-h-[70vh] sm:max-h-[80vh] overflow-hidden transform -translate-x-2 sm:-translate-x-4 md:translate-x-0">
                                         <div className="p-2 sm:p-3 md:p-4 border-b border-gray-100">
                       <div className="flex items-center justify-between">
                         <h3 className="text-xs sm:text-sm font-semibold text-gray-900">Notifications</h3>
                         <button 
                           onClick={() => setNotificationOpen(false)}
                           className="text-gray-400 hover:text-gray-600 p-1 rounded"
                         >
                           <X className="w-3 h-3 sm:w-4 sm:h-4" />
                         </button>
                       </div>
                     </div>
                     <div className="max-h-48 sm:max-h-64 md:max-h-96 overflow-y-auto">
                                             {/* Sample Notifications */}
                       <div className="p-2 sm:p-3 md:p-4 border-b border-gray-100 hover:bg-gray-50 cursor-pointer">
                         <div className="flex items-start gap-1.5 sm:gap-2 md:gap-3">
                           <div className="w-1.5 h-1.5 sm:w-2 sm:h-2 bg-blue-500 rounded-full mt-0.5 sm:mt-1 md:mt-2 flex-shrink-0"></div>
                           <div className="flex-1 min-w-0">
                             <p className="text-xs sm:text-sm font-medium text-gray-900 truncate">New user registration</p>
                             <p className="text-xs text-gray-500 mt-0.5 sm:mt-1 line-clamp-2">John Doe has registered as a new customer</p>
                             <p className="text-xs text-gray-400 mt-0.5 sm:mt-1">2 minutes ago</p>
                           </div>
                         </div>
                       </div>
                       <div className="p-2 sm:p-3 md:p-4 border-b border-gray-100 hover:bg-gray-50 cursor-pointer">
                         <div className="flex items-start gap-1.5 sm:gap-2 md:gap-3">
                           <div className="w-1.5 h-1.5 sm:w-2 sm:h-2 bg-green-500 rounded-full mt-0.5 sm:mt-1 md:mt-2 flex-shrink-0"></div>
                           <div className="flex-1 min-w-0">
                             <p className="text-xs sm:text-sm font-medium text-gray-900 truncate">Order completed</p>
                             <p className="text-xs text-gray-500 mt-0.5 sm:mt-1 line-clamp-2">Order #12345 has been successfully delivered</p>
                             <p className="text-xs text-gray-400 mt-0.5 sm:mt-1">1 hour ago</p>
                           </div>
                         </div>
                       </div>
                       <div className="p-2 sm:p-3 md:p-4 border-b border-gray-100 hover:bg-gray-50 cursor-pointer">
                         <div className="flex items-start gap-1.5 sm:gap-2 md:gap-3">
                           <div className="w-1.5 h-1.5 sm:w-2 sm:h-2 bg-yellow-500 rounded-full mt-0.5 sm:mt-1 md:mt-2 flex-shrink-0"></div>
                           <div className="flex-1 min-w-0">
                             <p className="text-xs sm:text-sm font-medium text-gray-900 truncate">System maintenance</p>
                             <p className="text-xs text-gray-500 mt-0.5 sm:mt-1 line-clamp-2">Scheduled maintenance will begin in 30 minutes</p>
                             <p className="text-xs text-gray-400 mt-0.5 sm:mt-1">3 hours ago</p>
                           </div>
                         </div>
                       </div>
                    </div>
                                         <div className="p-2 sm:p-3 border-t border-gray-100">
                       <Link 
                         href="/dashboard/notifications"
                         onClick={() => setNotificationOpen(false)}
                         className="w-full text-xs sm:text-sm text-primary hover:text-primary/80 font-medium py-1.5 sm:py-2 rounded hover:bg-primary/5 transition-colors block text-center"
                       >
                         View all notifications
                       </Link>
                     </div>
                  </div>
                )}
              </div>

              {/* Logout Button */}
              <button
                onClick={handleLogout}
                className="flex items-center gap-1 sm:gap-2 px-2 sm:px-3 py-1.5 sm:py-2 text-xs sm:text-sm font-medium text-gray-600 hover:text-red-600 hover:bg-red-50 rounded-lg transition-all duration-200 touch-manipulation"
              >
                <LogOut className="w-3 h-3 sm:w-4 sm:h-4" />
                <span className="hidden sm:inline">Logout</span>
              </button>
            </div>
          </div>
        </div>
      </nav>

      <div className="flex">
        {/* Desktop Sidebar */}
        <aside className="w-64 bg-white border-r flex-shrink-0 hidden md:flex flex-col shadow-sm min-h-[calc(100vh-4rem)]">
          <div className="p-4 border-b">
            {/* User Profile Section */}
            <div className="mt-3 p-3 bg-gray-50 rounded-lg">
              {/* User Name */}
              {userProfile && (
                <div className="flex items-center gap-2 mb-2">
                  <div className="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center flex-shrink-0">
                    {userProfile?.profilePhoto ? (
                      <img
                        src={userProfile.profilePhoto}
                        alt="Profile"
                        className="w-8 h-8 rounded-full object-cover"
                      />
                    ) : (
                      <User className="w-4 h-4 text-gray-500" />
                    )}
                  </div>
                  <div className="min-w-0 flex-1">
                    <div className="text-sm font-medium text-gray-900 truncate">
                      {userProfile.firstName && userProfile.lastName
                        ? `${userProfile.firstName} ${userProfile.lastName}`
                        : userProfile.firstName || userProfile.lastName || 'User'
                      }
                    </div>
                    {userProfile.email && (
                      <div className="text-xs text-gray-500 truncate">
                        {userProfile.email}
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* User Type */}
              {userType && (
                <div className="flex items-center gap-2">
                  {userType === 'super_admin' && (
                    <>
                      <Bell className="w-4 h-4 text-purple-600" />
                      <span className="text-xs font-medium text-purple-600">Super Admin</span>
                    </>
                  )}
                  {userType === 'admin' && (
                    <>
                      <Shield className="w-4 h-4 text-orange-600" />
                      <span className="text-xs font-medium text-orange-600">Administrator</span>
                    </>
                  )}
                  {userType === 'vendor' && (
                    <>
                      <Briefcase className="w-4 h-4 text-primary" />
                      <span className="text-xs font-medium text-primary">Business Account</span>
                    </>
                  )}
                  {userType === 'customer' && (
                    <>
                      <User className="w-4 h-4 text-blue-600" />
                      <span className="text-xs font-medium text-blue-600">Customer Account</span>
                    </>
                  )}
                </div>
              )}

              {/* Business Name */}
              {userProfile?.businessInfo?.businessName && (
                <div className="text-xs text-gray-600 mt-1 truncate">
                  {userProfile.businessInfo.businessName}
                </div>
              )}
            </div>
          </div>
          <nav className="flex-1 flex flex-col gap-1 px-3 py-4">
            {menu.map(item => {
              const IconComponent = item.icon;
              return (
                <Link
                  key={item.href}
                  href={item.href}
                  className={`px-4 py-3 rounded-lg font-medium transition-all duration-200 flex items-center gap-3 ${
                    pathname === item.href
                      ? 'bg-primary text-white shadow-md transform scale-[1.02]'
                      : 'text-gray-700 hover:bg-gray-100 hover:text-primary hover:shadow-sm'
                  }`}
                >
                  <IconComponent className="w-5 h-5" />
                  <span>{item.label}</span>
                </Link>
              );
            })}
          </nav>
        </aside>

        {/* Mobile Sidebar */}
        {mobileMenuOpen && (
          <div className="md:hidden fixed inset-0 z-40 flex">
            <div className="fixed inset-0 bg-black opacity-50" onClick={() => setMobileMenuOpen(false)} />
            <aside className="relative w-72 sm:w-80 bg-white border-r flex-shrink-0 flex flex-col shadow-lg">
              <div className="p-4 border-b">
                <div className="flex items-center justify-between">
                  <button
                    onClick={() => setMobileMenuOpen(false)}
                    className="p-1 hover:bg-gray-100 rounded touch-manipulation"
                    aria-label="Close menu"
                  >
                    <X className="w-5 h-5" />
                  </button>
                </div>

                {/* Mobile User Profile Section */}
                <div className="mt-3 p-3 bg-gray-50 rounded-lg">
                  {/* User Name */}
                  {userProfile && (
                    <div className="flex items-center gap-2 mb-2">
                      <div className="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center flex-shrink-0">
                        {userProfile?.profilePhoto ? (
                          <Image
                            src={userProfile.profilePhoto}
                            alt="Profile"
                            width={32}
                            height={32}
                            className="w-8 h-8 rounded-full object-cover"
                          />
                        ) : (
                          <User className="w-4 h-4 text-gray-500" />
                        )}
                      </div>
                      <div className="min-w-0 flex-1">
                        <div className="text-sm font-medium text-gray-900 truncate">
                          {userProfile.firstName && userProfile.lastName
                            ? `${userProfile.firstName} ${userProfile.lastName}`
                            : userProfile.firstName || userProfile.lastName || 'User'
                          }
                        </div>
                        {userProfile.email && (
                          <div className="text-xs text-gray-500 truncate">
                            {userProfile.email}
                          </div>
                        )}
                      </div>
                    </div>
                  )}

                  {/* User Type */}
                  {userType && (
                    <div className="flex items-center gap-2">
                      {userType === 'super_admin' && (
                        <>
                          <Bell className="w-4 h-4 text-purple-600" />
                          <span className="text-xs font-medium text-purple-600">Super Admin</span>
                        </>
                      )}
                      {userType === 'admin' && (
                        <>
                          <Shield className="w-4 h-4 text-orange-600" />
                          <span className="text-xs font-medium text-orange-600">Administrator</span>
                        </>
                      )}
                      {userType === 'vendor' && (
                        <>
                          <Briefcase className="w-4 h-4 text-primary" />
                          <span className="text-xs font-medium text-primary">Business Account</span>
                        </>
                      )}
                      {userType === 'customer' && (
                        <>
                          <User className="w-4 h-4 text-blue-600" />
                          <span className="text-xs font-medium text-blue-600">Customer Account</span>
                        </>
                      )}
                    </div>
                  )}

                  {/* Business Name */}
                  {userProfile?.businessInfo?.businessName && (
                    <div className="text-xs text-gray-600 mt-1 truncate">
                      {userProfile.businessInfo.businessName}
                    </div>
                  )}
                </div>
              </div>

              {/* Mobile Back to Home */}
              <div className="p-3 border-b">
                <Link
                  href="/"
                  onClick={() => setMobileMenuOpen(false)}
                  className="flex items-center gap-2 px-3 py-2 text-sm font-medium text-gray-600 hover:text-primary hover:bg-gray-100 rounded-lg transition-all duration-200"
                >
                  <ArrowLeft className="w-4 h-4" />
                  Back to Home
                </Link>
              </div>

              <nav className="flex-1 flex flex-col gap-1 px-3 py-4 overflow-y-auto">
                {menu.map(item => {
                  const IconComponent = item.icon;
                  return (
                    <Link
                      key={item.href}
                      href={item.href}
                      onClick={() => setMobileMenuOpen(false)}
                      className={`px-4 py-3 rounded-lg font-medium transition-all duration-200 flex items-center gap-3 touch-manipulation ${
                        pathname === item.href
                          ? 'bg-primary text-white shadow-md'
                          : 'text-gray-700 hover:bg-gray-100 hover:text-primary'
                      }`}
                    >
                      <IconComponent className="w-5 h-5" />
                      <span>{item.label}</span>
                    </Link>
                  );
                })}
              </nav>

              {/* Mobile Logout */}
              <div className="p-3 border-t">
                <button
                  onClick={() => {
                    handleLogout();
                    setMobileMenuOpen(false);
                  }}
                  className="w-full flex items-center gap-2 px-3 py-2 text-sm font-medium text-gray-600 hover:text-red-600 hover:bg-red-50 rounded-lg transition-all duration-200 touch-manipulation"
                >
                  <LogOut className="w-4 h-4" />
                  Logout
                </button>
              </div>
            </aside>
          </div>
        )}

        {/* Main Content */}
        <main className="flex-1 min-w-0 p-3 sm:p-4 md:p-6 lg:p-8">
          {children}
        </main>
      </div>
    </div>
    </AuthenticatedRoute>
  )
} 