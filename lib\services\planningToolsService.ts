import { generateClient } from 'aws-amplify/api';
import {
  createWeddingPlan,
  updateWeddingPlan,
  deleteWeddingPlan,
  createBudget,
  updateBudget,
  deleteBudget,
  createBudgetData,
  updateBudgetData,
  deleteBudgetData,
  createGuestListData,
  updateGuestListData,
  deleteGuestListData,
  createPlanningToolsData,
  updatePlanningToolsData,
  deletePlanningToolsData
} from '@/src/graphql/mutations';

import {
  listWeddingPlans,
  getWeddingPlan,
  weddingPlansByUserId,
  listBudgets,
  budgetsByUserId,
  listBudgetData,
  budgetDataByUserId,
  listGuestListData,
  guestListDataByUserId,
  listPlanningToolsData,
  planningToolsDataByUserId
} from '@/src/graphql/queries';

const client = generateClient();

// Types
export interface WeddingPlan {
  id: string;
  userId: string;
  weddingDate?: string;
  venue?: string;
  budget?: number;
  guestCount?: number;
  theme?: string;
  status: 'PLANNING' | 'CONFIRMED' | 'COMPLETED' | 'CANCELLED';
  notes?: string;
  createdAt: string;
  updatedAt: string;
}

export interface Budget {
  id: string;
  userId: string;
  weddingPlanId?: string;
  name: string;
  totalBudget: number;
  spentAmount?: number;
  isTemplate?: boolean;
  templateType?: string;
  categories?: BudgetCategory[];
  createdAt: string;
  updatedAt: string;
}

export interface BudgetCategory {
  id: string;
  userId: string;
  budgetId: string;
  name: string;
  allocatedAmount: number;
  spentAmount?: number;
  percentage?: number;
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  items?: BudgetItem[];
  createdAt: string;
  updatedAt: string;
}

export interface BudgetItem {
  id: string;
  userId: string;
  budgetCategoryId: string;
  name: string;
  estimatedCost?: number;
  actualCost?: number;
  vendor?: string;
  notes?: string;
  isPaid?: boolean;
  dueDate?: string;
  createdAt: string;
  updatedAt: string;
}

export interface GuestList {
  id: string;
  userId: string;
  weddingPlanId?: string;
  name: string;
  totalGuests?: number;
  confirmedGuests?: number;
  guests?: Guest[];
  createdAt: string;
  updatedAt: string;
}

export interface Guest {
  id: string;
  userId: string;
  guestListId: string;
  name: string;
  email?: string;
  phone?: string;
  category: 'FAMILY' | 'FRIENDS' | 'COLLEAGUES' | 'RELATIVES' | 'VIP' | 'CHILDREN';
  side: 'BRIDE' | 'GROOM' | 'BOTH';
  rsvpStatus: 'PENDING' | 'CONFIRMED' | 'DECLINED' | 'MAYBE';
  plusOne?: boolean;
  dietaryRestrictions?: string;
  address?: string;
  notes?: string;
  invitationSent?: boolean;
  invitationSentAt?: string;
  createdAt: string;
  updatedAt: string;
}

export interface Timeline {
  id: string;
  userId: string;
  weddingPlanId?: string;
  name: string;
  weddingDate?: string;
  tasks?: TimelineTask[];
  createdAt: string;
  updatedAt: string;
}

export interface TimelineTask {
  id: string;
  userId: string;
  timelineId: string;
  title: string;
  description?: string;
  dueDate?: string;
  completedDate?: string;
  isCompleted: boolean;
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT';
  category: 'PLANNING' | 'VENUE' | 'CATERING' | 'PHOTOGRAPHY' | 'DECORATION' | 'SHOPPING' | 'INVITATIONS' | 'MUSIC' | 'TRANSPORT' | 'LEGAL' | 'OTHER';
  phase?: string;
  estimatedDuration?: number;
  actualDuration?: number;
  assignedTo?: string;
  notes?: string;
  order?: number;
  createdAt: string;
  updatedAt: string;
}

export interface WeddingIdea {
  id: string;
  userId: string;
  weddingPlanId?: string;
  title: string;
  description?: string;
  category: 'THEME' | 'DECORATION' | 'FLOWERS' | 'CAKE' | 'PHOTOGRAPHY' | 'VENUE_SETUP' | 'LIGHTING' | 'ENTERTAINMENT' | 'FAVORS' | 'INVITATIONS' | 'OUTFITS' | 'JEWELRY' | 'MAKEUP' | 'MEHNDI' | 'FOOD' | 'DRINKS' | 'OTHER';
  imageUrl?: string;
  sourceUrl?: string;
  tags?: string[];
  isFavorite: boolean;
  notes?: string;
  estimatedCost?: number;
  priority?: number;
  createdAt: string;
  updatedAt: string;
}

export interface PlanningProgress {
  id: string;
  userId: string;
  weddingPlanId: string;
  category: 'BUDGET' | 'GUEST_LIST' | 'TIMELINE' | 'CHECKLIST' | 'IDEAS' | 'OVERALL';
  totalTasks: number;
  completedTasks: number;
  percentage: number;
  lastUpdated: string;
  createdAt: string;
  updatedAt: string;
}

class PlanningToolsService {
  // Wedding Plan Methods
  async createWeddingPlan(userId: string, planData: Partial<WeddingPlan>): Promise<WeddingPlan> {
    try {
      const response = await client.graphql({
        query: createWeddingPlan,
        variables: {
          input: {
            userId,
            weddingDate: planData.weddingDate,
            venue: planData.venue,
            budget: planData.budget,
            guestCount: planData.guestCount,
            theme: planData.theme,
            status: planData.status || 'PLANNING',
            notes: planData.notes
          }
        }
      });

      return response.data.createWeddingPlan;
    } catch (error) {
      console.error('Error creating wedding plan:', error);
      throw error;
    }
  }

  async getUserWeddingPlans(userId: string): Promise<WeddingPlan[]> {
    try {
      const response = await client.graphql({
        query: weddingPlansByUserId,
        variables: {
          userId,
          sortDirection: 'DESC'
        }
      });

      return response.data.weddingPlansByUserIdAndCreatedAt.items;
    } catch (error) {
      console.error('Error fetching wedding plans:', error);
      return [];
    }
  }

  async updateWeddingPlan(id: string, updates: Partial<WeddingPlan>): Promise<WeddingPlan> {
    try {
      const response = await client.graphql({
        query: updateWeddingPlan,
        variables: {
          input: {
            id,
            ...updates
          }
        }
      });

      return response.data.updateWeddingPlan;
    } catch (error) {
      console.error('Error updating wedding plan:', error);
      throw error;
    }
  }

  // Budget Methods
  async createBudget(userId: string, budgetData: Partial<Budget>): Promise<Budget> {
    try {
      const response = await client.graphql({
        query: createBudget,
        variables: {
          input: {
            userId,
            weddingPlanId: budgetData.weddingPlanId,
            name: budgetData.name || 'My Wedding Budget',
            totalBudget: budgetData.totalBudget || 0,
            spentAmount: budgetData.spentAmount || 0,
            isTemplate: budgetData.isTemplate || false,
            templateType: budgetData.templateType
          }
        }
      });

      return response.data.createBudget;
    } catch (error) {
      console.error('Error creating budget:', error);
      throw error;
    }
  }

  async getUserBudgets(userId: string): Promise<Budget[]> {
    try {
      const response = await client.graphql({
        query: budgetsByUserId,
        variables: {
          userId,
          sortDirection: 'DESC'
        }
      });

      return response.data.budgetsByUserIdAndCreatedAt.items;
    } catch (error) {
      console.error('Error fetching budgets:', error);
      return [];
    }
  }

  async createBudgetCategory(userId: string, budgetId: string, categoryData: Partial<BudgetCategory>): Promise<BudgetCategory> {
    try {
      const response = await client.graphql({
        query: createBudgetCategory,
        variables: {
          input: {
            userId,
            budgetId,
            name: categoryData.name || 'Category',
            allocatedAmount: categoryData.allocatedAmount || 0,
            spentAmount: categoryData.spentAmount || 0,
            percentage: categoryData.percentage || 0,
            priority: categoryData.priority || 'MEDIUM'
          }
        }
      });

      return response.data.createBudgetCategory;
    } catch (error) {
      console.error('Error creating budget category:', error);
      throw error;
    }
  }

  // Guest List Methods
  async createGuestList(userId: string, guestListData: Partial<GuestList>): Promise<GuestList> {
    try {
      // Use GuestListData model instead of separate GuestList model
      const response = await client.graphql({
        query: createGuestListData,
        variables: {
          input: {
            userId,
            name: guestListData.name || 'My Wedding Guests',
            data: JSON.stringify({
              weddingPlanId: guestListData.weddingPlanId,
              guests: guestListData.guests || []
            }),
            totalGuests: guestListData.totalGuests || 0,
            confirmedGuests: guestListData.confirmedGuests || 0
          }
        }
      });

      const result = response.data.createGuestListData;
      return {
        id: result.id,
        userId: result.userId,
        name: result.name,
        weddingPlanId: JSON.parse(result.data).weddingPlanId,
        totalGuests: result.totalGuests,
        confirmedGuests: result.confirmedGuests,
        guests: JSON.parse(result.data).guests,
        createdAt: result.createdAt,
        updatedAt: result.updatedAt
      };
    } catch (error) {
      console.error('Error creating guest list:', error);
      throw error;
    }
  }

  async getUserGuestLists(userId: string): Promise<GuestList[]> {
    try {
      // Use the existing GuestListData model instead of separate GuestList model
      const response = await client.graphql({
        query: guestListDataByUserId,
        variables: {
          userId,
          sortDirection: 'DESC'
        }
      });

      // Convert GuestListData to GuestList format
      return response.data.guestListDataByUserIdAndCreatedAt.items.map((item: any) => ({
        id: item.id,
        userId: item.userId,
        name: item.name,
        totalGuests: item.totalGuests,
        confirmedGuests: item.confirmedGuests,
        guests: item.data ? JSON.parse(item.data).guests || [] : [],
        createdAt: item.createdAt,
        updatedAt: item.updatedAt
      }));
    } catch (error) {
      console.error('Error fetching guest lists:', error);
      return [];
    }
  }

  // Timeline Methods
  async createTimeline(userId: string, timelineData: Partial<Timeline>): Promise<Timeline> {
    try {
      // Use PlanningToolsData with TIMELINE type instead of separate Timeline model
      const response = await client.graphql({
        query: createPlanningToolsData,
        variables: {
          input: {
            userId,
            toolType: 'TIMELINE',
            name: timelineData.name || 'My Wedding Timeline',
            data: JSON.stringify({
              weddingPlanId: timelineData.weddingPlanId,
              weddingDate: timelineData.weddingDate,
              tasks: timelineData.tasks || []
            })
          }
        }
      });

      const result = response.data.createPlanningToolsData;
      return {
        id: result.id,
        userId: result.userId,
        name: result.name,
        weddingPlanId: JSON.parse(result.data).weddingPlanId,
        weddingDate: JSON.parse(result.data).weddingDate,
        tasks: JSON.parse(result.data).tasks,
        createdAt: result.createdAt,
        updatedAt: result.updatedAt
      };
    } catch (error) {
      console.error('Error creating timeline:', error);
      throw error;
    }
  }

  async getUserTimelines(userId: string): Promise<Timeline[]> {
    try {
      // Use PlanningToolsData with TIMELINE type
      const response = await client.graphql({
        query: planningToolsDataByUserId,
        variables: {
          userId,
          sortDirection: 'DESC'
        }
      });

      // Filter for TIMELINE type and convert to Timeline format
      const timelineItems = response.data.planningToolsDataByUserIdAndCreatedAt.items.filter(
        (item: any) => item.toolType === 'TIMELINE'
      );

      return timelineItems.map((item: any) => {
        const data = JSON.parse(item.data);
        return {
          id: item.id,
          userId: item.userId,
          name: item.name,
          weddingPlanId: data.weddingPlanId,
          weddingDate: data.weddingDate,
          tasks: data.tasks || [],
          createdAt: item.createdAt,
          updatedAt: item.updatedAt
        };
      });
    } catch (error) {
      console.error('Error fetching timelines:', error);
      return [];
    }
  }

  // Wedding Ideas Methods
  async createWeddingIdea(userId: string, ideaData: Partial<WeddingIdea>): Promise<WeddingIdea> {
    try {
      // Use PlanningToolsData with IDEAS type instead of separate WeddingIdea model
      const response = await client.graphql({
        query: createPlanningToolsData,
        variables: {
          input: {
            userId,
            toolType: 'IDEAS',
            name: ideaData.title || 'New Idea',
            data: JSON.stringify({
              weddingPlanId: ideaData.weddingPlanId,
              title: ideaData.title || 'New Idea',
              description: ideaData.description,
              category: ideaData.category || 'OTHER',
              imageUrl: ideaData.imageUrl,
              sourceUrl: ideaData.sourceUrl,
              tags: ideaData.tags,
              isFavorite: ideaData.isFavorite || false,
              notes: ideaData.notes,
              estimatedCost: ideaData.estimatedCost,
              priority: ideaData.priority || 1
            })
          }
        }
      });

      const result = response.data.createPlanningToolsData;
      const data = JSON.parse(result.data);
      return {
        id: result.id,
        userId: result.userId,
        weddingPlanId: data.weddingPlanId,
        title: data.title,
        description: data.description,
        category: data.category,
        imageUrl: data.imageUrl,
        sourceUrl: data.sourceUrl,
        tags: data.tags,
        isFavorite: data.isFavorite,
        notes: data.notes,
        estimatedCost: data.estimatedCost,
        priority: data.priority,
        createdAt: result.createdAt,
        updatedAt: result.updatedAt
      };
    } catch (error) {
      console.error('Error creating wedding idea:', error);
      throw error;
    }
  }

  async getUserWeddingIdeas(userId: string): Promise<WeddingIdea[]> {
    try {
      // Use PlanningToolsData with IDEAS type
      const response = await client.graphql({
        query: planningToolsDataByUserId,
        variables: {
          userId,
          sortDirection: 'DESC'
        }
      });

      // Filter for IDEAS type and convert to WeddingIdea format
      const ideaItems = response.data.planningToolsDataByUserIdAndCreatedAt.items.filter(
        (item: any) => item.toolType === 'IDEAS'
      );

      return ideaItems.map((item: any) => {
        const data = JSON.parse(item.data);
        return {
          id: item.id,
          userId: item.userId,
          weddingPlanId: data.weddingPlanId,
          title: data.title,
          description: data.description,
          category: data.category,
          imageUrl: data.imageUrl,
          sourceUrl: data.sourceUrl,
          tags: data.tags,
          isFavorite: data.isFavorite,
          notes: data.notes,
          estimatedCost: data.estimatedCost,
          priority: data.priority,
          createdAt: item.createdAt,
          updatedAt: item.updatedAt
        };
      });
    } catch (error) {
      console.error('Error fetching wedding ideas:', error);
      return [];
    }
  }

  // Progress Tracking Methods
  async updatePlanningProgress(
    userId: string,
    weddingPlanId: string,
    category: string,
    totalTasks: number,
    completedTasks: number
  ): Promise<PlanningProgress> {
    try {
      // TODO: PlanningProgress model and mutations need to be added to GraphQL schema
      // For now, return a mock object to prevent errors
      console.warn('PlanningProgress functionality not yet implemented in GraphQL schema');

      const percentage = totalTasks > 0 ? (completedTasks / totalTasks) * 100 : 0;
      const mockProgress: PlanningProgress = {
        id: `mock-${Date.now()}`,
        userId,
        weddingPlanId,
        category: category as any,
        totalTasks,
        completedTasks,
        percentage,
        lastUpdated: new Date().toISOString(),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      return mockProgress;

      // Uncomment when PlanningProgress model is added to schema:
      // const response = await client.graphql({
      //   query: createPlanningProgress,
      //   variables: {
      //     input: {
      //       userId,
      //       weddingPlanId,
      //       category,
      //       totalTasks,
      //       completedTasks,
      //       percentage,
      //       lastUpdated: new Date().toISOString()
      //     }
      //   }
      // });
      // return response.data.createPlanningProgress;
    } catch (error) {
      console.error('Error updating planning progress:', error);
      throw error;
    }
  }

  async getUserPlanningProgress(userId: string): Promise<PlanningProgress[]> {
    try {
      // TODO: PlanningProgress model and queries need to be added to GraphQL schema
      // For now, return empty array to prevent errors
      console.warn('PlanningProgress functionality not yet implemented in GraphQL schema');
      return [];

      // Uncomment when PlanningProgress model is added to schema:
      // const response = await client.graphql({
      //   query: planningProgressByUserId,
      //   variables: {
      //     userId,
      //     sortDirection: 'DESC'
      //   }
      // });
      // return response.data.planningProgressByUserId.items;
    } catch (error) {
      console.error('Error fetching planning progress:', error);
      return [];
    }
  }

  // Utility Methods
  async migrateBudgetFromLocalStorage(userId: string, localStorageData: any[]): Promise<Budget[]> {
    try {
      const migratedBudgets: Budget[] = [];

      for (const localBudget of localStorageData) {
        const budget = await this.createBudget(userId, {
          name: localBudget.name || 'Migrated Budget',
          totalBudget: localBudget.total || 0,
          spentAmount: 0
        });

        // Migrate categories if they exist
        if (localBudget.categories && Array.isArray(localBudget.categories)) {
          for (const category of localBudget.categories) {
            await client.graphql({
              query: createBudgetCategory,
              variables: {
                input: {
                  userId,
                  budgetId: budget.id,
                  name: category.name || 'Category',
                  allocatedAmount: category.amount || 0,
                  spentAmount: 0,
                  percentage: category.percentage || 0,
                  priority: 'MEDIUM'
                }
              }
            });
          }
        }

        migratedBudgets.push(budget);
      }

      return migratedBudgets;
    } catch (error) {
      console.error('Error migrating budget data:', error);
      throw error;
    }
  }

  async migrateGuestListFromLocalStorage(userId: string, localStorageData: any[]): Promise<GuestList> {
    try {
      // Convert local storage guests to the format expected by GuestListData
      const guests = localStorageData.map((localGuest, index) => ({
        id: `guest-${index}`,
        userId,
        guestListId: '', // Will be set after creation
        name: localGuest.name || 'Guest',
        email: localGuest.email,
        phone: localGuest.phone,
        category: localGuest.category?.toUpperCase() || 'FAMILY',
        side: localGuest.side?.toUpperCase() || 'BRIDE',
        rsvpStatus: localGuest.rsvpStatus?.toUpperCase() || 'PENDING',
        plusOne: localGuest.plusOne || false,
        dietaryRestrictions: localGuest.dietaryRestrictions,
        address: localGuest.address,
        notes: localGuest.notes,
        invitationSent: false,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }));

      const guestList = await this.createGuestList(userId, {
        name: 'Migrated Guest List',
        totalGuests: localStorageData.length,
        confirmedGuests: localStorageData.filter(g => g.rsvpStatus === 'confirmed').length,
        guests: guests
      });

      return guestList;
    } catch (error) {
      console.error('Error migrating guest list data:', error);
      throw error;
    }
  }
}

export default new PlanningToolsService();
