"use client"

import { generateClient } from '@aws-amplify/api'
import { getCurrentUser } from 'aws-amplify/auth'
import { 
  createPricingPlan, 
  updatePricingPlan, 
  deletePricingPlan,
  createVendorSubscription,
  updateVendorSubscription,
  deleteVendorSubscription
} from '@/src/graphql/mutations'
import { 
  listPricingPlans, 
  getPricingPlan,
  listVendorSubscriptions,
  getVendorSubscription,
  listSubscriptionPayments
} from '@/src/graphql/queries'

const client = generateClient()

export interface AdminPricingPlan {
  id?: string
  name: string
  description: string
  price: number
  currency: string
  duration: 'MONTHLY' | 'QUARTERLY' | 'YEARLY'
  features: string[]
  isActive: boolean
  isPopular: boolean
  discountPercentage?: number
  discountValidUntil?: string
}

export interface VendorSubscriptionData {
  id: string
  vendorId: string
  planId: string
  status: string
  startDate: string
  endDate: string
  autoRenew: boolean
  paymentMethod?: string
  amount: number
  currency: string
  transactionId?: string
  createdAt: string
  updatedAt: string
}

export interface SubscriptionStats {
  totalSubscriptions: number
  activeSubscriptions: number
  cancelledSubscriptions: number
  totalRevenue: number
  monthlyRevenue: number
}

export class AdminPricingService {
  /**
   * Check if user has admin access
   */
  private static async checkAdminAccess(): Promise<boolean> {
    try {
      const user = await getCurrentUser()
      // In a real implementation, you would check user roles/permissions
      // For now, we'll assume all authenticated users can access admin features
      return !!user
    } catch (error) {
      return false
    }
  }

  /**
   * Get all pricing plans (admin view)
   */
  static async getAllPricingPlans(): Promise<AdminPricingPlan[]> {
    try {
      const hasAccess = await this.checkAdminAccess()
      if (!hasAccess) {
        throw new Error('Admin access required')
      }

      const result = await client.graphql({
        query: listPricingPlans,
        authMode: 'userPool'
      })

      return result.data.listPricingPlans.items.map(plan => ({
        id: plan.id,
        name: plan.name,
        description: plan.description,
        price: plan.price,
        currency: plan.currency,
        duration: plan.duration as 'MONTHLY' | 'QUARTERLY' | 'YEARLY',
        features: plan.features || [],
        isActive: plan.isActive,
        isPopular: plan.isPopular,
        discountPercentage: plan.discountPercentage,
        discountValidUntil: plan.discountValidUntil
      }))
    } catch (error) {
      console.error('Error fetching pricing plans:', error)
      throw new Error(`Failed to fetch pricing plans: ${error.message}`)
    }
  }

  /**
   * Create a new pricing plan
   */
  static async createPricingPlan(planData: AdminPricingPlan): Promise<{ success: boolean; plan?: AdminPricingPlan; message: string }> {
    try {
      const hasAccess = await this.checkAdminAccess()
      if (!hasAccess) {
        throw new Error('Admin access required')
      }

      const planInput = {
        name: planData.name,
        description: planData.description,
        price: planData.price,
        currency: planData.currency,
        duration: planData.duration,
        features: planData.features,
        isActive: planData.isActive,
        isPopular: planData.isPopular,
        discountPercentage: planData.discountPercentage,
        discountValidUntil: planData.discountValidUntil
      }

      const result = await client.graphql({
        query: createPricingPlan,
        variables: { input: planInput },
        authMode: 'userPool'
      })

      const createdPlan = result.data.createPricingPlan

      return {
        success: true,
        plan: {
          id: createdPlan.id,
          name: createdPlan.name,
          description: createdPlan.description,
          price: createdPlan.price,
          currency: createdPlan.currency,
          duration: createdPlan.duration,
          features: createdPlan.features || [],
          isActive: createdPlan.isActive,
          isPopular: createdPlan.isPopular,
          discountPercentage: createdPlan.discountPercentage,
          discountValidUntil: createdPlan.discountValidUntil
        },
        message: 'Pricing plan created successfully'
      }
    } catch (error) {
      console.error('Error creating pricing plan:', error)
      return {
        success: false,
        message: error.message || 'Failed to create pricing plan'
      }
    }
  }

  /**
   * Update an existing pricing plan
   */
  static async updatePricingPlan(planData: AdminPricingPlan): Promise<{ success: boolean; plan?: AdminPricingPlan; message: string }> {
    try {
      const hasAccess = await this.checkAdminAccess()
      if (!hasAccess) {
        throw new Error('Admin access required')
      }

      if (!planData.id) {
        throw new Error('Plan ID is required for update')
      }

      const updateInput = {
        id: planData.id,
        name: planData.name,
        description: planData.description,
        price: planData.price,
        currency: planData.currency,
        duration: planData.duration,
        features: planData.features,
        isActive: planData.isActive,
        isPopular: planData.isPopular,
        discountPercentage: planData.discountPercentage,
        discountValidUntil: planData.discountValidUntil
      }

      const result = await client.graphql({
        query: updatePricingPlan,
        variables: { input: updateInput },
        authMode: 'userPool'
      })

      const updatedPlan = result.data.updatePricingPlan

      return {
        success: true,
        plan: {
          id: updatedPlan.id,
          name: updatedPlan.name,
          description: updatedPlan.description,
          price: updatedPlan.price,
          currency: updatedPlan.currency,
          duration: updatedPlan.duration,
          features: updatedPlan.features || [],
          isActive: updatedPlan.isActive,
          isPopular: updatedPlan.isPopular,
          discountPercentage: updatedPlan.discountPercentage,
          discountValidUntil: updatedPlan.discountValidUntil
        },
        message: 'Pricing plan updated successfully'
      }
    } catch (error) {
      console.error('Error updating pricing plan:', error)
      return {
        success: false,
        message: error.message || 'Failed to update pricing plan'
      }
    }
  }

  /**
   * Delete a pricing plan
   */
  static async deletePricingPlan(planId: string): Promise<{ success: boolean; message: string }> {
    try {
      const hasAccess = await this.checkAdminAccess()
      if (!hasAccess) {
        throw new Error('Admin access required')
      }

      await client.graphql({
        query: deletePricingPlan,
        variables: { input: { id: planId } },
        authMode: 'userPool'
      })

      return {
        success: true,
        message: 'Pricing plan deleted successfully'
      }
    } catch (error) {
      console.error('Error deleting pricing plan:', error)
      return {
        success: false,
        message: error.message || 'Failed to delete pricing plan'
      }
    }
  }

  /**
   * Get all vendor subscriptions (admin view)
   */
  static async getAllVendorSubscriptions(): Promise<VendorSubscriptionData[]> {
    try {
      const hasAccess = await this.checkAdminAccess()
      if (!hasAccess) {
        throw new Error('Admin access required')
      }

      // Use userPool auth mode for admin access with private operations
      const result = await client.graphql({
        query: listVendorSubscriptions,
        authMode: 'userPool'
      })

      return result.data.listVendorSubscriptions.items.map(sub => ({
        id: sub.id,
        vendorId: sub.vendorId,
        planId: sub.planId,
        status: sub.status,
        startDate: sub.startDate,
        endDate: sub.endDate,
        autoRenew: sub.autoRenew,
        paymentMethod: sub.paymentMethod,
        amount: sub.amount,
        currency: sub.currency,
        transactionId: sub.transactionId,
        createdAt: sub.createdAt,
        updatedAt: sub.updatedAt
      }))
    } catch (error) {
      console.error('Error fetching vendor subscriptions:', error)
      // Return empty array instead of throwing to prevent stats calculation failure
      return []
    }
  }

  /**
   * Get subscription statistics
   */
  static async getSubscriptionStats(): Promise<SubscriptionStats> {
    try {
      const hasAccess = await this.checkAdminAccess()
      if (!hasAccess) {
        console.warn('Admin access required for subscription stats')
        return {
          totalSubscriptions: 0,
          activeSubscriptions: 0,
          cancelledSubscriptions: 0,
          totalRevenue: 0,
          monthlyRevenue: 0
        }
      }

      const subscriptions = await this.getAllVendorSubscriptions()

      const totalSubscriptions = subscriptions.length
      const activeSubscriptions = subscriptions.filter(sub => sub.status === 'ACTIVE').length
      const cancelledSubscriptions = subscriptions.filter(sub => sub.status === 'CANCELLED').length

      const totalRevenue = subscriptions.reduce((sum, sub) => sum + (sub.amount || 0), 0)

      // Calculate monthly revenue (simplified - last 30 days)
      const thirtyDaysAgo = new Date()
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)

      const monthlyRevenue = subscriptions
        .filter(sub => {
          try {
            return new Date(sub.createdAt) >= thirtyDaysAgo
          } catch {
            return false
          }
        })
        .reduce((sum, sub) => sum + (sub.amount || 0), 0)

      return {
        totalSubscriptions,
        activeSubscriptions,
        cancelledSubscriptions,
        totalRevenue,
        monthlyRevenue
      }
    } catch (error) {
      console.error('Error calculating subscription stats:', error)
      return {
        totalSubscriptions: 0,
        activeSubscriptions: 0,
        cancelledSubscriptions: 0,
        totalRevenue: 0,
        monthlyRevenue: 0
      }
    }
  }
}
