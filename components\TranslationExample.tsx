"use client"

import { useUniversalTranslation, useCartTranslations, useCommonTranslations } from '@/lib/universal-i18n';

// Example component showing how to use translations in any page
export default function TranslationExample() {
  const { t, currentLanguage } = useUniversalTranslation();
  const cartT = useCartTranslations();
  const commonT = useCommonTranslations();

  return (
    <div className="p-4 border rounded-lg bg-gray-50">
      <h3 className="text-lg font-semibold mb-4">Translation Example</h3>
      <p className="mb-2">Current Language: <strong>{currentLanguage}</strong></p>
      
      <div className="space-y-2">
        <p><strong>Common:</strong> {commonT.loading}</p>
        <p><strong>Navigation:</strong> {t('navigation.vendors', 'Vendors')}</p>
        <p><strong>Cart:</strong> {cartT.title}</p>
        <p><strong>Hero:</strong> {t('hero.title', 'Plan Your Dream Celebration with Us!')}</p>
      </div>
    </div>
  );
}