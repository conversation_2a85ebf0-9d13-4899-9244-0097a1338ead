/**
 * <PERSON><PERSON><PERSON>t to Update User Roles
 * Usage: npm run update-user-role <userId> <newRole> [reason]
 * 
 * Example:
 * npm run update-user-role user-123 super_admin "Initial super admin setup"
 */

import UserRoleService, { UserRole } from '../lib/services/userRoleService';

interface ScriptArgs {
  userId: string;
  newRole: UserRole;
  reason?: string;
  updatedBy: string;
}

class UserRoleUpdateScript {
  
  static async updateUserRole(args: ScriptArgs): Promise<void> {
    console.log('🔄 Starting user role update...');
    console.log(`User ID: ${args.userId}`);
    console.log(`New Role: ${args.newRole}`);
    console.log(`Updated By: ${args.updatedBy}`);
    console.log(`Reason: ${args.reason || 'No reason provided'}`);
    console.log('---');

    try {
      let result;

      switch (args.newRole) {
        case 'super_admin':
          console.log('🔐 Updating to Super Administrator...');
          result = await UserRoleService.updateToSuperAdmin(args.userId, args.updatedBy, args.reason);
          break;
        
        case 'admin':
          console.log('🛡️ Updating to Administrator...');
          result = await UserRoleService.updateToAdmin(args.userId, args.updatedBy, args.reason);
          break;
        
        case 'vendor':
          console.log('🏢 Updating to Vendor...');
          result = await UserRoleService.updateToVendor(args.userId, args.updatedBy, args.reason);
          break;
        
        case 'customer':
          console.log('👤 Updating to Customer...');
          result = await UserRoleService.updateToCustomer(args.userId, args.updatedBy, args.reason);
          break;
        
        default:
          throw new Error(`Invalid role: ${args.newRole}`);
      }

      if (result.success) {
        console.log('✅ SUCCESS:', result.message);
        console.log(`Previous Role: ${result.previousRole}`);
        console.log(`New Role: ${result.newRole}`);
      } else {
        console.log('❌ FAILED:', result.message);
        process.exit(1);
      }

    } catch (error) {
      console.error('❌ ERROR:', error instanceof Error ? error.message : 'Unknown error');
      process.exit(1);
    }
  }

  static validateArgs(args: string[]): ScriptArgs {
    if (args.length < 4) {
      console.error('❌ Usage: npm run update-user-role <userId> <newRole> <updatedBy> [reason]');
      console.error('');
      console.error('Valid roles: customer, vendor, admin, super_admin');
      console.error('');
      console.error('Examples:');
      console.error('  npm run update-user-role user-123 super_admin admin-456 "Initial setup"');
      console.error('  npm run update-user-role user-789 admin admin-456 "Promotion to admin"');
      process.exit(1);
    }

    const [, , userId, newRole, updatedBy, ...reasonParts] = args;
    const reason = reasonParts.join(' ');

    const validRoles: UserRole[] = ['customer', 'vendor', 'admin', 'super_admin'];
    if (!validRoles.includes(newRole as UserRole)) {
      console.error(`❌ Invalid role: ${newRole}`);
      console.error(`Valid roles: ${validRoles.join(', ')}`);
      process.exit(1);
    }

    return {
      userId,
      newRole: newRole as UserRole,
      updatedBy,
      reason: reason || undefined
    };
  }

  static async run(): Promise<void> {
    console.log('🚀 User Role Update Script');
    console.log('========================');
    
    const args = this.validateArgs(process.argv);
    await this.updateUserRole(args);
    
    console.log('');
    console.log('✨ Script completed successfully!');
  }
}

// Bulk update script
class BulkUserRoleUpdateScript {
  
  static async bulkUpdate(updates: Array<{
    userId: string;
    newRole: UserRole;
    reason?: string;
  }>, updatedBy: string): Promise<void> {
    console.log('🔄 Starting bulk user role update...');
    console.log(`Number of updates: ${updates.length}`);
    console.log(`Updated By: ${updatedBy}`);
    console.log('---');

    const updateData = updates.map(update => ({
      ...update,
      updatedBy
    }));

    try {
      const results = await UserRoleService.bulkUpdateRoles(updateData);
      
      console.log('📊 Bulk Update Results:');
      results.forEach((result, index) => {
        const update = updates[index];
        if (result.success) {
          console.log(`✅ ${update.userId}: ${result.previousRole} → ${result.newRole}`);
        } else {
          console.log(`❌ ${update.userId}: ${result.message}`);
        }
      });

      const successCount = results.filter(r => r.success).length;
      const failureCount = results.filter(r => !r.success).length;
      
      console.log('---');
      console.log(`✅ Successful updates: ${successCount}`);
      console.log(`❌ Failed updates: ${failureCount}`);

    } catch (error) {
      console.error('❌ Bulk update failed:', error instanceof Error ? error.message : 'Unknown error');
      process.exit(1);
    }
  }
}

// Example usage functions
export const examples = {
  
  // Create first super admin
  async createFirstSuperAdmin(userId: string): Promise<void> {
    console.log('👑 Creating first Super Administrator...');
    
    const result = await UserRoleService.updateToSuperAdmin(
      userId, 
      'system', 
      'Initial super admin setup'
    );
    
    if (result.success) {
      console.log('✅ First Super Admin created successfully!');
      console.log('🔐 They now have full system access.');
    } else {
      console.error('❌ Failed to create Super Admin:', result.message);
    }
  },

  // Promote customer to admin
  async promoteToAdmin(userId: string, promotedBy: string): Promise<void> {
    console.log('🛡️ Promoting user to Administrator...');
    
    const result = await UserRoleService.updateToAdmin(
      userId, 
      promotedBy, 
      'Promoted to platform administrator'
    );
    
    if (result.success) {
      console.log('✅ User promoted to Administrator!');
    } else {
      console.error('❌ Promotion failed:', result.message);
    }
  },

  // Demote admin to customer
  async demoteToCustomer(userId: string, demotedBy: string): Promise<void> {
    console.log('👤 Demoting user to Customer...');
    
    const result = await UserRoleService.updateToCustomer(
      userId, 
      demotedBy, 
      'Demoted due to policy violation'
    );
    
    if (result.success) {
      console.log('✅ User demoted to Customer.');
    } else {
      console.error('❌ Demotion failed:', result.message);
    }
  }
};

// Run the script if called directly
if (require.main === module) {
  UserRoleUpdateScript.run().catch(console.error);
}

export { UserRoleUpdateScript, BulkUserRoleUpdateScript };
