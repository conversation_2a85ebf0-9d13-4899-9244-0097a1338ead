"use client"

import { useTranslation } from 'react-i18next'
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import '@/lib/i18n'

export function LanguageDemo() {
  const { t, i18n } = useTranslation()

  const switchLanguage = (lng: string) => {
    i18n.changeLanguage(lng)
  }

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle>Language Demo</CardTitle>
        <p className="text-sm text-gray-600">
          Current Language: <span className="font-semibold">{i18n.language}</span>
        </p>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Language Switcher */}
        <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 gap-2">
          <Button variant={i18n.language === 'en' ? 'default' : 'outline'} size="sm" onClick={() => switchLanguage('en')}>English</Button>
          <Button variant={i18n.language === 'hi' ? 'default' : 'outline'} size="sm" onClick={() => switchLanguage('hi')}>हिंदी</Button>
          <Button variant={i18n.language === 'ta' ? 'default' : 'outline'} size="sm" onClick={() => switchLanguage('ta')}>தமிழ்</Button>
          <Button variant={i18n.language === 'te' ? 'default' : 'outline'} size="sm" onClick={() => switchLanguage('te')}>తెలుగు</Button>
          <Button variant={i18n.language === 'kn' ? 'default' : 'outline'} size="sm" onClick={() => switchLanguage('kn')}>ಕನ್ನಡ</Button>
          <Button variant={i18n.language === 'ml' ? 'default' : 'outline'} size="sm" onClick={() => switchLanguage('ml')}>മലയാളം</Button>
          <Button variant={i18n.language === 'mr' ? 'default' : 'outline'} size="sm" onClick={() => switchLanguage('mr')}>मराठी</Button>
          <Button variant={i18n.language === 'gu' ? 'default' : 'outline'} size="sm" onClick={() => switchLanguage('gu')}>ગુજરાતી</Button>
          <Button variant={i18n.language === 'pa' ? 'default' : 'outline'} size="sm" onClick={() => switchLanguage('pa')}>ਪੰਜਾਬੀ</Button>
          <Button variant={i18n.language === 'bn' ? 'default' : 'outline'} size="sm" onClick={() => switchLanguage('bn')}>বাংলা</Button>
          <Button variant={i18n.language === 'or' ? 'default' : 'outline'} size="sm" onClick={() => switchLanguage('or')}>ଓଡ଼ିଆ</Button>
          <Button variant={i18n.language === 'as' ? 'default' : 'outline'} size="sm" onClick={() => switchLanguage('as')}>অসমীয়া</Button>
          <Button variant={i18n.language === 'ur' ? 'default' : 'outline'} size="sm" onClick={() => switchLanguage('ur')}>اردو</Button>
          <Button variant={i18n.language === 'ne' ? 'default' : 'outline'} size="sm" onClick={() => switchLanguage('ne')}>नेपाली</Button>
        </div>

        {/* Demo Content */}
        <div className="space-y-3 p-4 bg-gray-50 rounded-lg">
          <h3 className="text-lg font-semibold">{t('hero.title')}</h3>
          <p className="text-gray-700">{t('hero.subtitle')}</p>
          
          <div className="grid grid-cols-2 gap-4 mt-4">
            <div>
              <h4 className="font-medium text-sm text-gray-600 mb-2">Navigation</h4>
              <ul className="text-sm space-y-1">
                <li>• {t('navigation.vendors')}</li>
                <li>• {t('navigation.venues')}</li>
                <li>• {t('navigation.shop')}</li>
                <li>• {t('navigation.planning')}</li>
              </ul>
            </div>
            
            <div>
              <h4 className="font-medium text-sm text-gray-600 mb-2">Services</h4>
              <ul className="text-sm space-y-1">
                <li>• {t('services.photography')}</li>
                <li>• {t('services.music')}</li>
                <li>• {t('services.catering')}</li>
                <li>• {t('services.decoration')}</li>
              </ul>
            </div>
          </div>

          <div className="pt-3 border-t">
            <p className="text-xs text-gray-500">
              {t('topHeader.tagline')}
            </p>
          </div>
        </div>

        {/* Instructions */}
        <div className="text-xs text-gray-500 bg-blue-50 p-3 rounded">
          <strong>How it works:</strong> Click the language buttons above to see the content change in real-time. 
          The TopHeader component automatically switches languages when you select different Indian states.
        </div>
      </CardContent>
    </Card>
  )
}
