import { google } from 'googleapis'

export class GoogleMyBusinessService {
  private static auth = new google.auth.GoogleAuth({
    keyFile: process.env.GOOGLE_APPLICATION_CREDENTIALS,
    scopes: ['https://www.googleapis.com/auth/business.manage']
  })

  static async getBusinessInsights(locationId: string, dateRange: string) {
    try {
      const authClient = await this.auth.getClient()
      const mybusiness = google.mybusinessbusinessinformation({ version: 'v1', auth: authClient })
      
      const [startDate, endDate] = this.getDateRange(dateRange)
      
      // Get location insights
      const insights = await mybusiness.locations.getInsights({
        name: `locations/${locationId}`,
        requestBody: {
          locationNames: [`locations/${locationId}`],
          basicRequest: {
            metricRequests: [
              { metric: 'QUERIES_DIRECT' },
              { metric: 'QUERIES_INDIRECT' },
              { metric: 'VIEWS_MAPS' },
              { metric: 'VIEWS_SEARCH' },
              { metric: 'ACTIONS_WEBSITE' },
              { metric: 'ACTIONS_PHONE' },
              { metric: 'ACTIONS_DRIVING_DIRECTIONS' }
            ],
            timeRange: {
              startTime: startDate,
              endTime: endDate
            }
          }
        }
      })

      return this.parseInsights(insights.data)
    } catch (error) {
      console.error('GMB API Error:', error)
      throw error
    }
  }

  static async getReviews(locationId: string) {
    try {
      const authClient = await this.auth.getClient()
      const mybusiness = google.mybusinessbusinessinformation({ version: 'v1', auth: authClient })
      
      const reviews = await mybusiness.locations.reviews.list({
        parent: `locations/${locationId}`
      })

      return reviews.data.reviews?.map(review => ({
        reviewId: review.name,
        reviewer: review.reviewer?.displayName,
        rating: review.starRating,
        comment: review.comment,
        createTime: review.createTime,
        updateTime: review.updateTime
      })) || []
    } catch (error) {
      console.error('GMB Reviews Error:', error)
      return []
    }
  }

  private static parseInsights(data: any) {
    const metrics = data.locationMetrics?.[0]?.metricValues || []
    
    return {
      searchQueries: {
        direct: this.getMetricValue(metrics, 'QUERIES_DIRECT'),
        indirect: this.getMetricValue(metrics, 'QUERIES_INDIRECT')
      },
      views: {
        maps: this.getMetricValue(metrics, 'VIEWS_MAPS'),
        search: this.getMetricValue(metrics, 'VIEWS_SEARCH')
      },
      actions: {
        website: this.getMetricValue(metrics, 'ACTIONS_WEBSITE'),
        phone: this.getMetricValue(metrics, 'ACTIONS_PHONE'),
        directions: this.getMetricValue(metrics, 'ACTIONS_DRIVING_DIRECTIONS')
      }
    }
  }

  private static getMetricValue(metrics: any[], metricType: string): number {
    const metric = metrics.find(m => m.metric === metricType)
    return parseInt(metric?.totalValue?.value || '0')
  }

  private static getDateRange(range: string): [string, string] {
    const end = new Date()
    const start = new Date()
    
    switch (range) {
      case '7d': start.setDate(end.getDate() - 7); break
      case '30d': start.setDate(end.getDate() - 30); break
      case '90d': start.setDate(end.getDate() - 90); break
      case '1y': start.setFullYear(end.getFullYear() - 1); break
    }
    
    return [start.toISOString(), end.toISOString()]
  }
}