import React, { createContext, useContext, useEffect, useState, useRef } from 'react';
import * as Notifications from 'expo-notifications';
import { Platform } from 'react-native';
import { useAuth } from './AuthProvider';
import { setupNotifications } from '../services/notifications';

export interface NotificationData {
  id: string;
  title: string;
  body: string;
  data?: any;
  timestamp: number;
  read: boolean;
  type: 'order' | 'booking' | 'message' | 'payment' | 'promotion' | 'system' | 'wedding_reminder';
  priority: 'low' | 'normal' | 'high';
}

interface NotificationContextType {
  notifications: NotificationData[];
  unreadCount: number;
  pushToken: string | null;
  isPermissionGranted: boolean;
  markAsRead: (notificationId: string) => void;
  markAllAsRead: () => void;
  clearNotification: (notificationId: string) => void;
  clearAllNotifications: () => void;
  sendTestNotification: () => void;
  requestPermissions: () => Promise<boolean>;
}

const NotificationContext = createContext<NotificationContextType | undefined>(undefined);

export function useNotifications() {
  const context = useContext(NotificationContext);
  if (!context) {
    throw new Error('useNotifications must be used within a NotificationProvider');
  }
  return context;
}

interface NotificationProviderProps {
  children: React.ReactNode;
}

export function NotificationProvider({ children }: NotificationProviderProps) {
  const { user, userProfile } = useAuth();
  const [notifications, setNotifications] = useState<NotificationData[]>([]);
  const [pushToken, setPushToken] = useState<string | null>(null);
  const [isPermissionGranted, setIsPermissionGranted] = useState(false);
  const notificationListener = useRef<Notifications.Subscription>();
  const responseListener = useRef<Notifications.Subscription>();

  useEffect(() => {
    initializeNotifications();
    
    // Set up notification listeners
    notificationListener.current = Notifications.addNotificationReceivedListener(handleNotificationReceived);
    responseListener.current = Notifications.addNotificationResponseReceivedListener(handleNotificationResponse);

    return () => {
      if (notificationListener.current) {
        Notifications.removeNotificationSubscription(notificationListener.current);
      }
      if (responseListener.current) {
        Notifications.removeNotificationSubscription(responseListener.current);
      }
    };
  }, []);

  useEffect(() => {
    // Update push token when user changes
    if (user && pushToken) {
      updateUserPushToken(pushToken);
    }
  }, [user, pushToken]);

  const initializeNotifications = async () => {
    try {
      const token = await setupNotifications();
      if (token) {
        setPushToken(token);
        setIsPermissionGranted(true);
      }
    } catch (error) {
      console.error('Failed to initialize notifications:', error);
    }
  };

  const updateUserPushToken = async (token: string) => {
    try {
      // TODO: Update user's push token in the database
      // await graphqlService.updateUserPushToken(user.id, token);
      console.log('Push token updated for user:', user?.id, token);
    } catch (error) {
      console.error('Failed to update push token:', error);
    }
  };

  const handleNotificationReceived = (notification: Notifications.Notification) => {
    const notificationData: NotificationData = {
      id: notification.request.identifier,
      title: notification.request.content.title || 'Notification',
      body: notification.request.content.body || '',
      data: notification.request.content.data,
      timestamp: Date.now(),
      read: false,
      type: notification.request.content.data?.type || 'system',
      priority: notification.request.content.data?.priority || 'normal',
    };

    setNotifications(prev => [notificationData, ...prev]);
  };

  const handleNotificationResponse = (response: Notifications.NotificationResponse) => {
    const notificationData = response.notification.request.content.data;
    
    // Handle different notification types
    switch (notificationData?.type) {
      case 'order':
        // Navigate to order details
        console.log('Navigate to order:', notificationData.orderId);
        break;
      case 'booking':
        // Navigate to booking details
        console.log('Navigate to booking:', notificationData.bookingId);
        break;
      case 'message':
        // Navigate to chat
        console.log('Navigate to chat:', notificationData.chatId);
        break;
      case 'payment':
        // Navigate to payment screen
        console.log('Navigate to payment:', notificationData.paymentId);
        break;
      default:
        console.log('Notification tapped:', notificationData);
    }

    // Mark notification as read
    markAsRead(response.notification.request.identifier);
  };

  const markAsRead = (notificationId: string) => {
    setNotifications(prev =>
      prev.map(notification =>
        notification.id === notificationId
          ? { ...notification, read: true }
          : notification
      )
    );
  };

  const markAllAsRead = () => {
    setNotifications(prev =>
      prev.map(notification => ({ ...notification, read: true }))
    );
  };

  const clearNotification = (notificationId: string) => {
    setNotifications(prev =>
      prev.filter(notification => notification.id !== notificationId)
    );
  };

  const clearAllNotifications = () => {
    setNotifications([]);
  };

  const requestPermissions = async (): Promise<boolean> => {
    try {
      const { status } = await Notifications.requestPermissionsAsync();
      const granted = status === 'granted';
      setIsPermissionGranted(granted);
      
      if (granted) {
        const token = await setupNotifications();
        if (token) {
          setPushToken(token);
        }
      }
      
      return granted;
    } catch (error) {
      console.error('Failed to request permissions:', error);
      return false;
    }
  };

  const sendTestNotification = async () => {
    try {
      await Notifications.scheduleNotificationAsync({
        content: {
          title: 'Test Notification 🔔',
          body: 'This is a test notification from BookmyFestive!',
          data: { type: 'test' },
        },
        trigger: null, // Send immediately
      });
    } catch (error) {
      console.error('Failed to send test notification:', error);
    }
  };

  const unreadCount = notifications.filter(n => !n.read).length;

  const value: NotificationContextType = {
    notifications,
    unreadCount,
    pushToken,
    isPermissionGranted,
    markAsRead,
    markAllAsRead,
    clearNotification,
    clearAllNotifications,
    sendTestNotification,
    requestPermissions,
  };

  return (
    <NotificationContext.Provider value={value}>
      {children}
    </NotificationContext.Provider>
  );
}

// Notification types for better type safety
export const NotificationTypes = {
  ORDER_PLACED: 'order_placed',
  ORDER_CONFIRMED: 'order_confirmed',
  ORDER_SHIPPED: 'order_shipped',
  ORDER_DELIVERED: 'order_delivered',
  ORDER_CANCELLED: 'order_cancelled',
  BOOKING_CONFIRMED: 'booking_confirmed',
  BOOKING_CANCELLED: 'booking_cancelled',
  PAYMENT_SUCCESS: 'payment_success',
  PAYMENT_FAILED: 'payment_failed',
  PAYMENT_REMINDER: 'payment_reminder',
  NEW_MESSAGE: 'new_message',
  VENDOR_RESPONSE: 'vendor_response',
  WEDDING_REMINDER: 'wedding_reminder',
  PROMOTION: 'promotion',
  SYSTEM_UPDATE: 'system_update',
} as const;

export type NotificationType = typeof NotificationTypes[keyof typeof NotificationTypes];
