import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Alert,
} from 'react-native';
import { useNavigation, useRoute } from '@react-navigation/native';
import { useTheme } from '../../providers/ThemeProvider';
import { Card } from '../../components/ui/Card';
import { Badge } from '../../components/ui/Badge';
import { Button } from '../../components/ui/Button';

export default function BookingDetailScreen() {
  const navigation = useNavigation();
  const route = useRoute();
  const { theme } = useTheme();
  const { bookingId } = (route.params as any) || {};
  
  const [booking, setBooking] = useState({
    id: bookingId,
    serviceName: 'Wedding Photography',
    vendorName: 'Capture Moments Studio',
    vendorPhone: '+91 98765 43210',
    vendorEmail: '<EMAIL>',
    date: '2024-03-15',
    time: '10:00 AM - 6:00 PM',
    status: 'confirmed',
    amount: 15000,
    location: 'Grand Palace Hotel, Chennai',
    address: '123 Anna Salai, Chennai, Tamil Nadu 600002',
    bookingDate: '2024-02-15',
    notes: 'Please arrive 30 minutes early for setup. Bring backup equipment.',
    services: [
      'Pre-wedding photoshoot',
      'Wedding ceremony photography',
      'Reception photography',
      'Photo editing and album creation',
    ],
  });

  const handleCancelBooking = () => {
    Alert.alert(
      'Cancel Booking',
      'Are you sure you want to cancel this booking? This action cannot be undone.',
      [
        { text: 'No', style: 'cancel' },
        {
          text: 'Yes, Cancel',
          style: 'destructive',
          onPress: () => {
            // TODO: Cancel booking API call
            setBooking({ ...booking, status: 'cancelled' });
            Alert.alert('Booking Cancelled', 'Your booking has been cancelled successfully.');
          },
        },
      ]
    );
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'confirmed':
        return theme.colors.secondary;
      case 'pending':
        return theme.colors.accent;
      case 'completed':
        return theme.colors.primary;
      case 'cancelled':
        return theme.colors.destructive;
      default:
        return theme.colors.textSecondary;
    }
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    scrollContainer: {
      padding: 16,
      gap: 16,
    },
    header: {
      alignItems: 'center',
      marginBottom: 8,
    },
    serviceName: {
      fontSize: 24,
      fontWeight: 'bold',
      color: theme.colors.text,
      textAlign: 'center',
      marginBottom: 8,
    },
    vendorName: {
      fontSize: 18,
      color: theme.colors.textSecondary,
      marginBottom: 12,
    },
    sectionTitle: {
      fontSize: 18,
      fontWeight: '600',
      color: theme.colors.text,
      marginBottom: 12,
    },
    detailRow: {
      flexDirection: 'row',
      alignItems: 'flex-start',
      paddingVertical: 8,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border,
    },
    detailIcon: {
      fontSize: 18,
      width: 30,
      marginTop: 2,
    },
    detailContent: {
      flex: 1,
    },
    detailLabel: {
      fontSize: 14,
      color: theme.colors.textSecondary,
      marginBottom: 2,
    },
    detailValue: {
      fontSize: 16,
      color: theme.colors.text,
      fontWeight: '500',
    },
    amountValue: {
      fontSize: 20,
      fontWeight: 'bold',
      color: theme.colors.primary,
    },
    serviceItem: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingVertical: 6,
    },
    serviceIcon: {
      fontSize: 16,
      marginRight: 8,
      color: theme.colors.secondary,
    },
    serviceText: {
      fontSize: 14,
      color: theme.colors.text,
      flex: 1,
    },
    notesText: {
      fontSize: 14,
      color: theme.colors.textSecondary,
      lineHeight: 20,
      fontStyle: 'italic',
    },
    statusContainer: {
      alignItems: 'center',
      marginVertical: 8,
    },
    actionButtons: {
      gap: 12,
      marginTop: 24,
    },
    contactButtons: {
      flexDirection: 'row',
      gap: 8,
    },
    contactButton: {
      flex: 1,
    },
  });

  return (
    <View style={styles.container}>
      <ScrollView contentContainerStyle={styles.scrollContainer}>
        <View style={styles.header}>
          <Text style={styles.serviceName}>{booking.serviceName}</Text>
          <Text style={styles.vendorName}>{booking.vendorName}</Text>
          <Badge
            variant="secondary"
            style={{ backgroundColor: getStatusColor(booking.status) + '20' }}
          >
            <Text style={{ color: getStatusColor(booking.status) }}>
              {booking.status.charAt(0).toUpperCase() + booking.status.slice(1)}
            </Text>
          </Badge>
        </View>

        <Card>
          <Text style={styles.sectionTitle}>Booking Details</Text>
          
          <View style={styles.detailRow}>
            <Text style={styles.detailIcon}>📅</Text>
            <View style={styles.detailContent}>
              <Text style={styles.detailLabel}>Date & Time</Text>
              <Text style={styles.detailValue}>
                {booking.date} • {booking.time}
              </Text>
            </View>
          </View>

          <View style={styles.detailRow}>
            <Text style={styles.detailIcon}>📍</Text>
            <View style={styles.detailContent}>
              <Text style={styles.detailLabel}>Location</Text>
              <Text style={styles.detailValue}>{booking.location}</Text>
              <Text style={styles.detailLabel}>{booking.address}</Text>
            </View>
          </View>

          <View style={styles.detailRow}>
            <Text style={styles.detailIcon}>💰</Text>
            <View style={styles.detailContent}>
              <Text style={styles.detailLabel}>Amount</Text>
              <Text style={styles.amountValue}>
                ₹{booking.amount.toLocaleString()}
              </Text>
            </View>
          </View>

          <View style={styles.detailRow}>
            <Text style={styles.detailIcon}>📋</Text>
            <View style={styles.detailContent}>
              <Text style={styles.detailLabel}>Booking ID</Text>
              <Text style={styles.detailValue}>TM360-{booking.id}</Text>
            </View>
          </View>

          <View style={styles.detailRow}>
            <Text style={styles.detailIcon}>🕒</Text>
            <View style={styles.detailContent}>
              <Text style={styles.detailLabel}>Booked On</Text>
              <Text style={styles.detailValue}>{booking.bookingDate}</Text>
            </View>
          </View>
        </Card>

        <Card>
          <Text style={styles.sectionTitle}>Services Included</Text>
          {booking.services.map((service, index) => (
            <View key={index} style={styles.serviceItem}>
              <Text style={styles.serviceIcon}>✓</Text>
              <Text style={styles.serviceText}>{service}</Text>
            </View>
          ))}
        </Card>

        <Card>
          <Text style={styles.sectionTitle}>Vendor Contact</Text>
          
          <View style={styles.detailRow}>
            <Text style={styles.detailIcon}>📞</Text>
            <View style={styles.detailContent}>
              <Text style={styles.detailLabel}>Phone</Text>
              <Text style={styles.detailValue}>{booking.vendorPhone}</Text>
            </View>
          </View>

          <View style={styles.detailRow}>
            <Text style={styles.detailIcon}>✉️</Text>
            <View style={styles.detailContent}>
              <Text style={styles.detailLabel}>Email</Text>
              <Text style={styles.detailValue}>{booking.vendorEmail}</Text>
            </View>
          </View>
        </Card>

        {booking.notes && (
          <Card>
            <Text style={styles.sectionTitle}>Special Notes</Text>
            <Text style={styles.notesText}>{booking.notes}</Text>
          </Card>
        )}

        <View style={styles.actionButtons}>
          <View style={styles.contactButtons}>
            <Button
              variant="outline"
              style={styles.contactButton}
              onPress={() => {
                // TODO: Implement call functionality
                Alert.alert('Call Vendor', `Calling ${booking.vendorPhone}`);
              }}
            >
              📞 Call
            </Button>
            <Button
              variant="outline"
              style={styles.contactButton}
              onPress={() => {
                // TODO: Implement message functionality
                Alert.alert('Message Vendor', 'Opening message app...');
              }}
            >
              💬 Message
            </Button>
          </View>

          {booking.status === 'pending' && (
            <Button
              variant="destructive"
              onPress={handleCancelBooking}
            >
              Cancel Booking
            </Button>
          )}

          {booking.status === 'completed' && (
            <Button
              onPress={() =>
                navigation.navigate('WriteReview' as never, {
                  entityType: 'vendor',
                  entityId: booking.id,
                })
              }
            >
              Write Review
            </Button>
          )}
        </View>
      </ScrollView>
    </View>
  );
}
