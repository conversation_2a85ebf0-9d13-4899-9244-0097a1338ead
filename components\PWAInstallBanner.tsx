'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { X, Download, Smartphone } from "lucide-react"
import { usePWADetection, triggerPWAInstall } from "@/hooks/use-pwa-detection"

export function PWAInstallBanner() {
  const { canInstall, installPrompt, isStandalone, platform } = usePWADetection()
  const [showBanner, setShowBanner] = useState(false)
  const [dismissed, setDismissed] = useState(false)

  useEffect(() => {
    // Check if user has dismissed the banner before
    const isDismissed = localStorage.getItem('pwa-install-dismissed') === 'true'
    setDismissed(isDismissed)
    
    // Show banner if can install and not dismissed and not already standalone
    setShowBanner(canInstall && !isDismissed && !isStandalone)
  }, [canInstall, isStandalone])

  const handleInstall = async () => {
    const success = await triggerP<PERSON><PERSON>nstall(installPrompt)
    if (success) {
      setShowBanner(false)
    }
  }

  const handleDismiss = () => {
    setShowBanner(false)
    setDismissed(true)
    localStorage.setItem('pwa-install-dismissed', 'true')
  }

  if (!showBanner) return null

  return (
    <Card className="fixed bottom-4 left-4 right-4 z-50 mx-auto max-w-sm bg-white shadow-lg border border-gray-200 md:max-w-md">
      <div className="p-4">
        <div className="flex items-start gap-3">
          <div className="flex-shrink-0">
            <div className="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center">
              <Smartphone className="w-5 h-5 text-primary" />
            </div>
          </div>
          
          <div className="flex-1 min-w-0">
            <h3 className="text-sm font-semibold text-gray-900 mb-1">
              Install BookmyFestive
            </h3>
            <p className="text-xs text-gray-600 mb-3">
              Get the full app experience with faster loading and offline access.
            </p>
            
            <div className="flex gap-2">
              <Button 
                size="sm" 
                onClick={handleInstall}
                className="bg-primary hover:bg-primary/90 text-white text-xs px-3 py-1.5"
              >
                <Download className="w-3 h-3 mr-1" />
                Install
              </Button>
              <Button 
                size="sm" 
                variant="ghost" 
                onClick={handleDismiss}
                className="text-gray-500 hover:text-gray-700 text-xs px-2 py-1.5"
              >
                Not now
              </Button>
            </div>
          </div>
          
          <button
            onClick={handleDismiss}
            className="flex-shrink-0 p-1 text-gray-400 hover:text-gray-600"
          >
            <X className="w-4 h-4" />
          </button>
        </div>
      </div>
    </Card>
  )
}

// iOS-specific install instructions
export function IOSInstallInstructions() {
  const { platform, isStandalone } = usePWADetection()
  const [showInstructions, setShowInstructions] = useState(false)

  useEffect(() => {
    // Show for iOS users who haven't installed the app
    const shouldShow = platform === 'ios' && !isStandalone && 
                      localStorage.getItem('ios-install-instructions-dismissed') !== 'true'
    setShowInstructions(shouldShow)
  }, [platform, isStandalone])

  const handleDismiss = () => {
    setShowInstructions(false)
    localStorage.setItem('ios-install-instructions-dismissed', 'true')
  }

  if (!showInstructions) return null

  return (
    <Card className="fixed bottom-4 left-4 right-4 z-50 mx-auto max-w-sm bg-white shadow-lg border border-gray-200">
      <div className="p-4">
        <div className="flex items-start gap-3">
          <div className="flex-shrink-0">
            <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
              <Smartphone className="w-5 h-5 text-blue-600" />
            </div>
          </div>
          
          <div className="flex-1 min-w-0">
            <h3 className="text-sm font-semibold text-gray-900 mb-1">
              Add to Home Screen
            </h3>
            <p className="text-xs text-gray-600 mb-2">
              Tap the share button <span className="font-mono bg-gray-100 px-1 rounded">⬆️</span> and select "Add to Home Screen" for the best experience.
            </p>
            
            <Button 
              size="sm" 
              variant="ghost" 
              onClick={handleDismiss}
              className="text-gray-500 hover:text-gray-700 text-xs px-2 py-1"
            >
              Got it
            </Button>
          </div>
          
          <button
            onClick={handleDismiss}
            className="flex-shrink-0 p-1 text-gray-400 hover:text-gray-600"
          >
            <X className="w-4 h-4" />
          </button>
        </div>
      </div>
    </Card>
  )
}
