"use client"
import { TopHeader } from "@/components/top-header"
import { <PERSON><PERSON> } from "@/components/header"
import { <PERSON><PERSON> } from "@/components/footer"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Checkbox } from "@/components/ui/checkbox"
import { Separator } from "@/components/ui/separator"
import { Eye, EyeOff, Mail, Lock, User, AlertCircle, Phone, MessageSquare, RefreshCw, CheckCircle, Clock, Building2 } from "lucide-react"
import Link from "next/link"
import { useState, useEffect } from "react"
import { useAuth } from "@/contexts/AuthContext"
import { useRouter } from "next/navigation"
import AuthRoutingService from "@/lib/services/authRouting"
import HybridMobileAuthService from "@/lib/services/hybridMobileAuth"
import MobileAuthService from "@/lib/services/mobileAuthService"
import { RedirectUtils } from "@/lib/utils/redirectUtils"
import { profileService } from '@/lib/services/profileService'
import GoogleAuthService from '@/lib/services/googleAuthService'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import GoogleSignupModal from "@/components/auth/GoogleSignupModal"
// Updated Google Sign-In/Sign-Up handler with Cognito Identity Pool integration
import {
  CognitoIdentityClient,
  GetIdCommand,
  GetCredentialsForIdentityCommand
} from "@aws-sdk/client-cognito-identity";

export default function LoginPage() {
  const [isSignUp, setIsSignUp] = useState(false)
  const [isConfirmation, setIsConfirmation] = useState(false)
  const [isForgotPassword, setIsForgotPassword] = useState(false)
  const [isResetCode, setIsResetCode] = useState(false)
  const [isPhone, setIsPhone] = useState(false)
  const [showPassword, setShowPassword] = useState(false)
  const [showGoogleSignupModal, setShowGoogleSignupModal] = useState(false)

  const [formData, setFormData] = useState({
    fullName: '',
    email: '',
    phone: '',
    emailOrPhone: '',
    password: '',
    confirmPassword: '',
    confirmationCode: '',
    resetCode: '',
    newPassword: '',
    otp: '',
    agreeToTerms: false
  })

  // Authentication states
  const [authMode, setAuthMode] = useState<'login' | 'customer-signup'>('login')
  const [authMethod, setAuthMethod] = useState<'otp' | 'password'>('password')
  const [loginMethod, setLoginMethod] = useState<'phone' | 'email'>('email')
  const [currentStep, setCurrentStep] = useState<'input' | 'otp'>('input')
  const [successMessage, setSuccessMessage] = useState('')

  // Mobile OTP states
  const [signupMethod, setSignupMethod] = useState<'email' | 'mobile'>('email')
  const [isOTPStep, setIsOTPStep] = useState(false)
  const [otpSession, setOtpSession] = useState('')
  const [countryCode, setCountryCode] = useState('+91')
  const [resendTimer, setResendTimer] = useState(0)
  const [error, setError] = useState('')
  const [loading, setLoading] = useState(false)
  const [loginSuccess, setLoginSuccess] = useState(false)
  const [googleLoading, setGoogleLoading] = useState(false); // add

  const { signInWithPassword, signUp, confirmSignUp, resendConfirmationCode, requestPasswordReset, confirmPasswordReset, sendOTP, verifyOTP, refreshUserProfile, userType, userProfile, isAuthenticated, isLoading: authLoading } = useAuth()
  const router = useRouter()

  const exchangeGoogleTokenWithCognito = async (idToken) => {
    const identityClient = new CognitoIdentityClient({ region: "your-region" });
  
    const getIdCmd = new GetIdCommand({
      IdentityPoolId: "your-identity-pool-id",
      Logins: {
        "accounts.google.com": idToken
      }
    });
  
    const { IdentityId } = await identityClient.send(getIdCmd);
  
    const getCredsCmd = new GetCredentialsForIdentityCommand({
      IdentityId,
      Logins: {
        "accounts.google.com": idToken
      }
    });
  
    const creds = await identityClient.send(getCredsCmd);
    console.log("Cognito AWS Credentials: ", creds);
  };

  // Google Sign-In/Sign-Up handler (popup-based, handles both login and account creation)
// Google Sign-In/Sign-Up handler (popup-based, handles both login and account creation)
const handleGoogleSignIn = async () => {
  setGoogleLoading(true); // only Google loading
  setError("");

  try {
    const result = await GoogleAuthService.signInWithGooglePopup();

    if (result.success) {
      console.log("Google authentication successful:", result.user);
      console.log("Google sign-in result:", result);

      // Exchange Google ID token with Cognito identity pool credentials
      if (result.user.idToken) {
        await exchangeGoogleTokenWithCognito(result.user.idToken);
      }

      if (result.isNewUser) {
        console.log("New user created - starting signup flow:", result.userProfile);

        if (result.requiresOTP || result.requiresPassword) {
          console.log("New user requires additional setup - showing signup modal");
          setShowGoogleSignupModal(true);
          return;
        } else {
          console.log("New user setup complete - redirecting to dashboard");
          handlePostLoginRedirect();
        }
      } else {
        console.log("Existing user signed in:", result.userProfile);

        const authContext = {
          user: result.user,
          userProfile: result.userProfile,
          isAuthenticated: true
        };

        localStorage.setItem("authSession", JSON.stringify(authContext));
        window.location.reload();
      }
    } else {
      // Popup closed or real error
      if (result.error && (result.error.includes('popup_closed') || result.error.includes('access_denied') || result.error === 'popup_closed_by_user')) {
        console.log("Google sign-in popup was closed by user");
      } else {
        setError(result.error || "Google authentication failed. Please try again.");
      }
    }
  } catch (error: any) {
    console.error("Google authentication error:", error);
    if (error.message && (error.message.includes('popup_closed') || error.message.includes('access_denied'))) {
      console.log("Google sign-in popup was closed by user");
    } else {
      setError("Google authentication failed. Please try again.");
    }
  } finally {
    setGoogleLoading(false); // don't change normal loading label
  }
};
  // Store redirect URL from query parameters on component mount
  useEffect(() => {
    RedirectUtils.storeRedirectFromQuery();
  }, []);

  // Initialize Google Auth on component mount
  useEffect(() => {
    GoogleAuthService.initializeGoogleAuth().catch(error => {
      console.error('Failed to initialize Google Auth:', error)
    })
  }, [])

  // Redirect already authenticated users to home page
  // useEffect(() => {
  //   if (!authLoading && isAuthenticated) {
  //     // Get the redirect URL from query params or default to home
  //     const searchParams = new URLSearchParams(window.location.search);
  //     const redirectTo = searchParams.get('redirect') || '/';

  //     // Redirect to the intended destination or home page
  //     router.replace(redirectTo);
  //   }
  // }, [isAuthenticated, authLoading, router]);

  // Helper function to handle post-login redirect
  const handlePostLoginRedirect = () => {
    const redirectUrl = RedirectUtils.getAndClearRedirectUrl();
    if (redirectUrl) {
      router.push(redirectUrl);
    } else {
      const dashboardRoute = AuthRoutingService.getDashboardRoute(userType, userProfile);
      router.push(dashboardRoute);
    }
  };

  // Country codes for mobile OTP
  const countryCodes = HybridMobileAuthService.getSupportedCountryCodes()

  // Start resend timer
  const startResendTimer = () => {
    setResendTimer(30)
    const timer = setInterval(() => {
      setResendTimer((prev) => {
        if (prev <= 1) {
          clearInterval(timer)
          return 0
        }
        return prev - 1
      })
    }, 1000)
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    })
  }



  const validateInput = (input: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/

    if (emailRegex.test(input)) {
      setIsPhone(false)
      return true
    } else if (MobileAuthService.validatePhoneNumber(input)) {
      setIsPhone(true)
      return true
    }
    return false
  }

  const handleSignIn = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError('')

    if (!validateInput(formData.emailOrPhone)) {
      setError('Please enter a valid email address or phone number')
      setLoading(false)
      return
    }

    try {
      await signInWithPassword(formData.emailOrPhone, formData.password)

      // Show success state after a brief moment
      setTimeout(() => {
        setLoginSuccess(true)
        setError('')
      }, 300)

      // Keep loading state active during redirect
      // Wait a moment for user profile to be fetched
      setTimeout(() => {
        handlePostLoginRedirect()
        // Loading will be cleared when component unmounts during navigation
      }, 1000)
    } catch (error: any) {
      // Handle unconfirmed user - redirect to OTP verification
      if (error.name === 'UserNotConfirmedException') {
        setIsConfirmation(true)
        setSuccessMessage('Please verify your email address. We\'ve sent a new verification code to your email.')
        // Automatically resend confirmation code
        try {
          await resendConfirmationCode(formData.emailOrPhone)
          startResendTimer()
        } catch (resendError) {
          console.error('Failed to resend confirmation code:', resendError)
        }
        setLoading(false)
        return
      }

      setError(error.message || 'Failed to sign in')
      setLoading(false) // Only clear loading on error
      setLoginSuccess(false)
    }
  }

  // Handle Customer Signup with proper password
  const handleCustomerSignup = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError('')

    // Validate form
    if (!formData.fullName.trim()) {
      setError('Please enter your full name')
      setLoading(false)
      return
    }

    if (!formData.email.trim() || !/\S+@\S+\.\S+/.test(formData.email)) {
      setError('Please enter a valid email address')
      setLoading(false)
      return
    }

    if (!formData.phone.trim() || !/^\d{10}$/.test(formData.phone)) {
      setError('Please enter a valid 10-digit phone number')
      setLoading(false)
      return
    }

    if (formData.password.length < 8) {
      setError('Password must be at least 8 characters long')
      setLoading(false)
      return
    }

    if (formData.password !== formData.confirmPassword) {
      setError('Passwords do not match')
      setLoading(false)
      return
    }

    if (!formData.agreeToTerms) {
      setError('Please agree to the Terms of Service and Privacy Policy')
      setLoading(false)
      return
    }

    try {
      // Store additional customer data for automatic profile creation
      const nameParts = formData.fullName.trim().split(' ')
      const customerData = {
        firstName: nameParts[0] || '',
        lastName: nameParts.slice(1).join(' ') || '',
        email: formData.email,
        phone: formData.phone,
        isVendor: false
      }
      localStorage.setItem('pendingUserData', JSON.stringify(customerData))

      // Create customer account with proper password
      await signUp(formData.email, formData.fullName, formData.password, false)
      setIsConfirmation(true)
      setIsOTPStep(false) // We'll use the confirmation form instead
      setSuccessMessage(`Verification code sent to ${formData.email}`)
      startResendTimer()
    } catch (error: any) {
      setError(error.message || 'Sign up failed')
    } finally {
      setLoading(false)
    }
  }

  // Handle OTP Login (for quick login without password)
  const handleOTPLogin = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError('')

    if (loginMethod === 'phone') {
      if (!formData.phone.trim() || !/^\d{10}$/.test(formData.phone)) {
        setError('Please enter a valid 10-digit phone number')
        setLoading(false)
        return
      }

      try {
        // Send OTP to phone for login
        const result = await sendOTP(formData.phone, countryCode)
        if (result.success) {
          setOtpSession(result.session || '')
          setIsOTPStep(true)
          setSuccessMessage(`OTP sent to ${countryCode}${formData.phone}`)
          startResendTimer()
        } else {
          setError(result.message)
        }
      } catch (error: any) {
        setError(error.message || 'Failed to send OTP')
      }
    } else {
      if (!formData.email.trim() || !/\S+@\S+\.\S+/.test(formData.email)) {
        setError('Please enter a valid email address')
        setLoading(false)
        return
      }

      try {
        // For email OTP login, we'll use password reset flow to send OTP
        await requestPasswordReset(formData.email)
        setIsOTPStep(true)
        setSuccessMessage(`Login code sent to ${formData.email}`)
        startResendTimer()
      } catch (error: any) {
        setError(error.message || 'Failed to send login code')
      }
    }

    setLoading(false)
  }

  // Handle OTP verification for both signup and login
  const handleOTPVerification = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError('')

    if (!formData.otp || formData.otp.length !== 6) {
      setError('Please enter the 6-digit OTP')
      setLoading(false)
      return
    }

    try {
      if (isConfirmation) {
        // Determine which email to use for confirmation
        const emailToConfirm = formData.email || formData.emailOrPhone

        // Customer signup OTP verification or login confirmation
        await confirmSignUp(emailToConfirm, formData.otp)

        // Auto sign in after verification
        try {
          // Use the stored password from either signup or login attempt
          const passwordToUse = formData.password
          await signInWithPassword(emailToConfirm, passwordToUse)

          // Create customer profile after successful authentication (only for new signups)
          try {
            // Get customer data from localStorage (only exists for new signups)
            const pendingUserData = localStorage.getItem('pendingUserData')
            if (pendingUserData) {
              const customerData = JSON.parse(pendingUserData)

              // Create user profile with customer information
              await profileService.createProfile({
                firstName: customerData.firstName,
                lastName: customerData.lastName,
                email: customerData.email,
                phone: customerData.phone,
                isVendor: false
              })

              // Force refresh the auth context to pick up the new customer profile
              await refreshUserProfile()

              // Clear the temporary data
              localStorage.removeItem('pendingUserData')
              console.log('✅ Customer profile created successfully!')
            }
          } catch (profileError) {
            console.error('Error creating customer profile:', profileError)
            // Don't block the user from proceeding even if profile creation fails
          }

          // Redirect after sign-in
          setTimeout(() => {
            handlePostLoginRedirect()
          }, 1000)
        } catch (signInError) {
          console.error('Auto sign-in failed:', signInError)
          setSuccessMessage('Email verified successfully! Please login with your email and password.')
          setIsSignUp(false)
          setIsConfirmation(false)
          setIsOTPStep(false)
        }
      } else {
        // Handle other OTP verification scenarios (mobile login, etc.)
        let result
        if (signupMethod === 'mobile') {
          result = await HybridMobileAuthService.verifyMobileOTP(otpSession, formData.otp)
        } else {
          result = await MobileAuthService.verifyEmailOTP(formData.emailOrPhone, formData.otp)
        }

        if (result.success) {
          setTimeout(() => {
            handlePostLoginRedirect()
          }, 1000)
        } else {
          setError(result.message)
        }
      }
    } catch (error: any) {
      setError(error.message || 'OTP verification failed')
    } finally {
      setLoading(false)
    }
  }

  // Handle resend OTP
  const handleResendOTP = async () => {
    if (resendTimer > 0) return

    setLoading(true)
    setError('')

    try {
      if (isConfirmation) {
        // Resend confirmation code for signup
        await resendConfirmationCode(formData.email)
        setSuccessMessage(`New verification code sent to ${formData.email}`)
        startResendTimer()
      } else {
        // Resend OTP for mobile login
        let result
        if (signupMethod === 'mobile') {
          result = await HybridMobileAuthService.resendOTP(otpSession)
        } else {
          result = await MobileAuthService.sendEmailOTP(formData.emailOrPhone)
        }

        if (result.success) {
          startResendTimer()
        } else {
          setError(result.message)
        }
      }
    } catch (error: any) {
      setError('Failed to resend code. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  const handleForgotPassword = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError('')

    if (!validateInput(formData.emailOrPhone)) {
      setError('Please enter a valid email address or phone number')
      setLoading(false)
      return
    }

    try {
      await requestPasswordReset(formData.emailOrPhone)
      setIsResetCode(true)
    } catch (error: any) {
      setError(error.message || 'Failed to send reset code')
    } finally {
      setLoading(false)
    }
  }

  const handleResetPassword = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError('')

    if (formData.newPassword.length < 8) {
      setError('Password must be at least 8 characters long')
      setLoading(false)
      return
    }

    try {
      await confirmPasswordReset(formData.emailOrPhone, formData.resetCode, formData.newPassword)
      setIsForgotPassword(false)
      setIsResetCode(false)
      setError('')
      // Auto sign in with new password
      await signInWithPassword(formData.emailOrPhone, formData.newPassword)
      setTimeout(() => {
        handlePostLoginRedirect()
      }, 1000)
    } catch (error: any) {
      setError(error.message || 'Failed to reset password')
    } finally {
      setLoading(false)
    }
  }

  const handleConfirmSignUp = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError('')

    try {
      await confirmSignUp(formData.emailOrPhone, formData.confirmationCode)
      // After confirming sign up, automatically sign in
      await signInWithPassword(formData.emailOrPhone, formData.password)
      setTimeout(() => {
        handlePostLoginRedirect()
      }, 1000)
    } catch (error: any) {
      setError(error.message || 'Failed to confirm sign up')
    } finally {
      setLoading(false)
    }
  }

  const handleResendCode = async () => {
    setLoading(true)
    setError('')

    try {
      if (isConfirmation) {
        await resendConfirmationCode(formData.emailOrPhone)
        setError('') // Clear any previous errors
      } else if (isResetCode) {
        await requestPasswordReset(formData.emailOrPhone)
        setError('') // Clear any previous errors
      }
    } catch (error: any) {
      setError(error.message || 'Failed to resend code')
    } finally {
      setLoading(false)
    }
  }

  // Show loading while checking authentication
  if (authLoading) {
    return (
      <div className="min-h-screen bg-white flex flex-col">
        <TopHeader />
        <Header />
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center">
            <RefreshCw className="w-8 h-8 animate-spin text-primary mx-auto mb-4" />
            <p className="text-gray-600">Loading...</p>
          </div>
        </div>
        <Footer />
      </div>
    )
  }

  // Don't render login form if user is already authenticated (will redirect)
  // if (isAuthenticated) {
  //   return (
  //     <div className="min-h-screen bg-white flex flex-col">
  //       <TopHeader />
  //       <Header />
  //       <div className="flex-1 flex items-center justify-center">
  //         <div className="text-center">
  //           <CheckCircle className="w-8 h-8 text-green-500 mx-auto mb-4" />
  //           <p className="text-gray-600">logged in. Redirecting...</p>
  //         </div>
  //       </div>
  //       <Footer />
  //     </div>
  //   )
  // }

  return (
    <div className="min-h-screen bg-white flex flex-col">
      <TopHeader />
      <Header />
      <section className="flex flex-1 items-center justify-center bg-gradient-to-br from-primary/5 to-secondary/5" style={{paddingTop: 80, paddingBottom: 40}}>
        <div className="w-full max-w-6xl mx-auto bg-white rounded-xl shadow-lg overflow-hidden flex flex-col lg:flex-row">
          {/* Login Form - First on mobile, second on desktop */}
          <div className="w-full lg:w-1/2 flex flex-col justify-center p-8 lg:p-12 relative lg:order-2">
            {/* Business Registration Quick Access */}
            {!isConfirmation && !isResetCode && !isForgotPassword && !isOTPStep && !isSignUp && (
              <div className="bg-gradient-to-r from-primary/10 to-primary/5 border border-primary/20 rounded-lg p-3 mb-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Building2 className="w-4 h-4 text-primary" />
                    <span className="text-primary font-medium text-sm">Are you a vendor?</span>
                  </div>
                  <Link href="/vendor-signup">
                    <Button
                      size="sm"
                      className="bg-primary text-white hover:bg-primary/90 text-xs px-3 py-1 h-7"
                    >
                      Register Now
                    </Button>
                  </Link>
                </div>
              </div>
            )}

            {/* Only show titles for specific flows that need explanation */}
            {(isConfirmation || isResetCode || isForgotPassword || isOTPStep || isSignUp) && (
              <div className="text-center mb-6">
                <h1 className="text-2xl md:text-3xl font-bold text-gray-900 mb-2">
                  {isConfirmation ? 'Verify Your Email' :
                   isResetCode ? 'Create New Password' :
                   isForgotPassword ? 'Reset Your Password' :
                   isOTPStep ? 'Enter Verification Code' :
                   'Create Account'}
                </h1>

                {/* Verification/Reset Descriptions */}
                {isConfirmation && (
                  <p className="text-gray-600 text-sm">
                    We've sent a verification code to your email address. Please check your inbox and enter the code below.
                  </p>
                )}

                {isOTPStep && (
                  <p className="text-gray-600 text-sm">
                    Enter the 6-digit verification code we sent to your email to continue.
                  </p>
                )}

                {isForgotPassword && !isResetCode && (
                  <p className="text-gray-600 text-sm">
                    No worries! Enter your email address and we'll send you a reset code.
                  </p>
                )}

                {isResetCode && (
                  <p className="text-gray-600 text-sm">
                    Enter the reset code from your email and create a new secure password.
                  </p>
                )}

                {isSignUp && (
                  <p className="text-gray-600 text-sm">
                    Join our wedding planning community
                  </p>
                )}
              </div>
            )}

            {error && (
              <div className="mb-6 p-4 bg-gradient-to-r from-red-50 to-red-50/50 border border-red-200 rounded-lg flex items-start gap-3 text-red-700 shadow-sm">
                <div className="flex-shrink-0 mt-0.5">
                  <AlertCircle className="w-5 h-5" />
                </div>
                <div>
                  <p className="text-sm font-medium">Oops! Something went wrong</p>
                  <p className="text-sm text-red-600 mt-1">{error}</p>
                </div>
              </div>
            )}

            {loginSuccess && !isSignUp && (
              <div className="mb-6 p-4 bg-gradient-to-r from-green-50 to-green-50/50 border border-green-200 rounded-lg flex items-start gap-3 text-green-700 shadow-sm">
                <div className="flex-shrink-0 mt-0.5">
                  <CheckCircle className="w-5 h-5" />
                </div>
                <div>
                  <p className="text-sm font-medium">Login Successful!</p>
                  <p className="text-sm text-green-600 mt-1">Redirecting you to your destination...</p>
                </div>
              </div>
            )}

            {successMessage && (
              <div className="mb-6 p-4 bg-gradient-to-r from-green-50 to-green-50/50 border border-green-200 rounded-lg flex items-start gap-3 text-green-700 shadow-sm">
                <div className="flex-shrink-0 mt-0.5">
                  <CheckCircle className="w-5 h-5" />
                </div>
                <div>
                  <p className="text-sm font-medium">Success!</p>
                  <p className="text-sm text-green-600 mt-1">{successMessage}</p>
                </div>
              </div>
            )}

            {isConfirmation ? (
              <form onSubmit={handleOTPVerification} className="space-y-4">
                <div className="text-sm text-gray-600 mb-4">
                  We've sent a verification code to {formData.email}. Please enter it below.
                </div>
                <div className="relative">
                  <Input
                    name="otp"
                    type="text"
                    placeholder="Enter 6-digit verification code"
                    className="pl-12 text-center text-lg tracking-widest"
                    value={formData.otp}
                    onChange={(e) => {
                      const value = e.target.value.replace(/\D/g, '').slice(0, 6)
                      setFormData(prev => ({ ...prev, otp: value }))
                      setError('')
                    }}
                    maxLength={6}
                    required
                  />
                  <span className="absolute left-4 top-1/2 -translate-y-1/2 text-primary">
                    <MessageSquare className="h-5 w-5" />
                  </span>
                </div>
                <Button type="submit" className="w-full bg-primary hover:bg-primary/90" disabled={loading}>
                  {loading ? (
                    <>
                      <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                      Verifying...
                    </>
                  ) : (
                    <>
                      <CheckCircle className="w-4 h-4 mr-2" />
                      Verify Account
                    </>
                  )}
                </Button>
                <div className="flex gap-2">
                  <Button
                    type="button"
                    variant="outline"
                    className="flex-1"
                    onClick={() => {
                      setIsConfirmation(false)
                      setFormData(prev => ({ ...prev, otp: '' }))
                    }}
                  >
                    Back to Sign Up
                  </Button>
                  <Button
                    type="button"
                    variant="outline"
                    className="flex-1"
                    onClick={handleResendOTP}
                    disabled={loading || resendTimer > 0}
                  >
                    {resendTimer > 0 ? (
                      <>
                        <Clock className="w-4 h-4 mr-2" />
                        Resend in {resendTimer}s
                      </>
                    ) : (
                      'Resend Code'
                    )}
                  </Button>
                </div>
              </form>
            ) : isResetCode ? (
              <form onSubmit={handleResetPassword} className="space-y-4">
                <div className="text-sm text-gray-600 mb-4">
                  We've sent a reset code to {formData.emailOrPhone}. Enter the code and your new password.
                </div>
                <div className="relative">
                  <Input
                    name="resetCode"
                    type="text"
                    placeholder="Enter reset code"
                    className="pl-12"
                    value={formData.resetCode}
                    onChange={handleInputChange}
                    required
                  />
                  <span className="absolute left-4 top-1/2 -translate-y-1/2 text-primary">
                    <Mail className="h-5 w-5" />
                  </span>
                </div>
                <div className="relative">
                  <Input
                    name="newPassword"
                    type={showPassword ? "text" : "password"}
                    placeholder="New Password*"
                    className="pl-12 pr-12"
                    value={formData.newPassword}
                    onChange={handleInputChange}
                    required
                  />
                  <span className="absolute left-4 top-1/2 -translate-y-1/2 text-primary">
                    <Lock className="h-5 w-5" />
                  </span>
                  <button
                    type="button"
                    className="absolute right-4 top-1/2 -translate-y-1/2 text-gray-500 hover:text-gray-700"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
                  </button>
                </div>
                <Button type="submit" className="w-full bg-primary hover:bg-primary/90" disabled={loading}>
                  {loading ? 'Resetting...' : 'Reset Password'}
                </Button>
                <div className="flex gap-2">
                  <Button
                    type="button"
                    variant="outline"
                    className="flex-1"
                    onClick={() => { setIsResetCode(false); setIsForgotPassword(false); }}
                  >
                    Back to Login
                  </Button>
                  <Button
                    type="button"
                    variant="outline"
                    className="flex-1"
                    onClick={handleResendCode}
                    disabled={loading}
                  >
                    Resend Code
                  </Button>
                </div>
              </form>
            ) : isForgotPassword ? (
              <form onSubmit={handleForgotPassword} className="space-y-4">
                <div className="text-sm text-gray-600 mb-4">
                  Enter your email or phone number to receive a password reset code.
                </div>
                <div className="relative">
                  <Input
                    name="emailOrPhone"
                    type="text"
                    placeholder="Enter email or phone number (e.g., 9876543210)*"
                    className="pl-12"
                    value={formData.emailOrPhone}
                    onChange={handleInputChange}
                    required
                  />
                  <span className="absolute left-4 top-1/2 -translate-y-1/2 text-primary">
                    <Mail className="h-5 w-5" />
                  </span>
                </div>
                <Button type="submit" className="w-full bg-primary hover:bg-primary/90" disabled={loading}>
                  {loading ? 'Sending...' : 'Send Reset Code'}
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  className="w-full"
                  onClick={() => setIsForgotPassword(false)}
                >
                  Back to Login
                </Button>
              </form>
            ) : isOTPStep ? (
              <form onSubmit={handleOTPVerification} className="space-y-4">
                <div className="text-sm text-gray-600 mb-4 text-center">
                  We've sent a 6-digit OTP to your {signupMethod === 'email' ? 'email address' : 'mobile number'}
                  <br />
                  <span className="font-medium">
                    {signupMethod === 'email' ? formData.emailOrPhone : `${countryCode} ${HybridMobileAuthService.formatPhoneNumber(formData.emailOrPhone)}`}
                  </span>
                </div>
                <div className="relative">
                  <Input
                    name="otp"
                    type="text"
                    placeholder="Enter 6-digit OTP"
                    className="pl-12 text-center text-lg tracking-widest"
                    value={formData.otp}
                    onChange={(e) => setFormData({...formData, otp: e.target.value.replace(/\D/g, '').slice(0, 6)})}
                    maxLength={6}
                    required
                  />
                  <span className="absolute left-4 top-1/2 -translate-y-1/2 text-primary">
                    <MessageSquare className="h-5 w-5" />
                  </span>
                </div>
                <Button type="submit" className="w-full bg-primary hover:bg-primary/90" disabled={loading || formData.otp.length !== 6}>
                  {loading ? (
                    <>
                      <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                      Verifying...
                    </>
                  ) : (
                    <>
                      <CheckCircle className="w-4 h-4 mr-2" />
                      Verify & Create Account
                    </>
                  )}
                </Button>
                <div className="flex gap-2">
                  <Button
                    type="button"
                    variant="outline"
                    className="flex-1"
                    onClick={() => {
                      setIsOTPStep(false)
                      setFormData({...formData, otp: ''})
                      setError('')
                    }}
                  >
                    Back
                  </Button>
                  <Button
                    type="button"
                    variant="outline"
                    className="flex-1"
                    onClick={handleResendOTP}
                    disabled={resendTimer > 0 || loading}
                  >
                    {resendTimer > 0 ? (
                      <>
                        <Clock className="w-4 h-4 mr-1" />
                        {resendTimer}s
                      </>
                    ) : (
                      <>
                        <RefreshCw className="w-4 h-4 mr-1" />
                        Resend
                      </>
                    )}
                  </Button>
                </div>
              </form>
            ) : (
              <form onSubmit={isSignUp ? handleCustomerSignup : handleSignIn} className="space-y-4">
                {/* Overlay during success/redirect */}
                {loginSuccess && !isSignUp && (
                  <div className="absolute inset-0 bg-white/80 backdrop-blur-sm rounded-lg flex items-center justify-center z-10">
                    <div className="text-center">
                      <CheckCircle className="w-12 h-12 text-green-500 mx-auto mb-2 animate-pulse" />
                      <p className="text-sm font-medium text-gray-900">Redirecting...</p>
                    </div>
                  </div>
                )}
                {isSignUp && (
                  <>
                    {/* Full Name */}
                    <div className="relative">
                      <Input
                        name="fullName"
                        type="text"
                        placeholder="Full Name*"
                        className="pl-12"
                        value={formData.fullName}
                        onChange={handleInputChange}
                        required
                      />
                      <span className="absolute left-4 top-1/2 -translate-y-1/2 text-primary">
                        <User className="h-5 w-5" />
                      </span>
                    </div>

                    {/* Email */}
                    <div className="relative">
                      <Input
                        name="email"
                        type="email"
                        placeholder="Email Address*"
                        className="pl-12"
                        value={formData.email}
                        onChange={handleInputChange}
                        disabled={loading}
                        required
                      />
                      <span className="absolute left-4 top-1/2 -translate-y-1/2 text-primary">
                        <Mail className="h-5 w-5" />
                      </span>
                    </div>

                    {/* Phone */}
                    <div className="relative">
                      <Input
                        name="phone"
                        type="tel"
                        placeholder="Phone Number (10 digits)*"
                        className="pl-12"
                        value={formData.phone}
                        onChange={(e) => {
                          const value = e.target.value.replace(/\D/g, '').slice(0, 10)
                          setFormData(prev => ({ ...prev, phone: value }))
                          setError('')
                        }}
                        maxLength={10}
                        required
                      />
                      <span className="absolute left-4 top-1/2 -translate-y-1/2 text-primary">
                        <Phone className="h-5 w-5" />
                      </span>
                    </div>
                  </>
                )}

                {/* Login Email/Phone Input - only for login mode */}
                {!isSignUp && (
                  <div className="relative">
                    <Input
                      name="emailOrPhone"
                      type="text"
                      placeholder="Enter email or phone number*"
                      className="pl-12"
                      value={formData.emailOrPhone}
                      onChange={handleInputChange}
                      disabled={loading}
                      required
                    />
                    <span className="absolute left-4 top-1/2 -translate-y-1/2 text-primary">
                      <Mail className="h-5 w-5" />
                    </span>
                  </div>
                )}

                {/* Password field */}
                <div className="relative">
                  <Input
                    name="password"
                    type={showPassword ? "text" : "password"}
                    placeholder={isSignUp ? "Password (min 8 characters)*" : "Password*"}
                    className="pl-12 pr-12"
                    value={formData.password}
                    onChange={handleInputChange}
                    disabled={loading}
                    required
                  />
                  <span className="absolute left-4 top-1/2 -translate-y-1/2 text-primary">
                    <Lock className="h-5 w-5" />
                  </span>
                  <button
                    type="button"
                    className="absolute right-4 top-1/2 -translate-y-1/2 text-gray-500 hover:text-gray-700"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
                  </button>
                </div>

                {/* Confirm Password field - only for signup */}
                {isSignUp && (
                  <div className="relative">
                    <Input
                      name="confirmPassword"
                      type="password"
                      placeholder="Confirm Password*"
                      className="pl-12"
                      value={formData.confirmPassword}
                      onChange={handleInputChange}
                      required
                    />
                    <span className="absolute left-4 top-1/2 -translate-y-1/2 text-primary">
                      <Lock className="h-5 w-5" />
                    </span>
                  </div>
                )}

                {/* Terms and Conditions - only for signup */}
                {isSignUp && (
                  <div className="flex items-start space-x-2">
                    <Checkbox
                      id="agreeToTerms"
                      checked={formData.agreeToTerms}
                      onCheckedChange={(checked) => setFormData(prev => ({ ...prev, agreeToTerms: checked as boolean }))}
                      className="mt-1"
                    />
                    <label htmlFor="agreeToTerms" className="text-sm text-gray-600">
                      I agree to the{" "}
                      <Link href="/terms" className="text-primary hover:text-primary/90">
                        Terms of Service
                      </Link>{" "}
                      and{" "}
                      <Link href="/privacy" className="text-primary hover:text-primary/90">
                        Privacy Policy
                      </Link>
                    </label>
                  </div>
                )}

                {/* Forgot Password */}
                {!isSignUp && (
                  <div className="text-right">
                    <button
                      type="button"
                      className="text-sm text-primary hover:underline"
                      onClick={() => setIsForgotPassword(true)}
                    >
                      Forgot Password?
                    </button>
                  </div>
                )}
                

                <Button type="submit" className="w-full bg-primary hover:bg-primary/90" disabled={loading}>
                  {loading ? (
                    loginSuccess && !isSignUp ? (
                      <>
                        <CheckCircle className="w-4 h-4 mr-2" />
                        Login Successful! Redirecting...
                      </>
                    ) : (
                      <>
                        <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                        {isSignUp ? 'Creating Account...' : 'Signing In...'}
                      </>
                    )
                  ) : (
                    isSignUp ? (
                      <>
                        <User className="w-4 h-4 mr-2" />
                        Create Account
                      </>
                    ) : 'Sign In'
                  )}
                </Button>
                                {/* Google Sign-in */}
                                {!isConfirmation && !isResetCode && !isForgotPassword && !isOTPStep && (
                  <>
                    {/* OR Divider */}
                    <div className="flex items-center my-4">
                      <div className="flex-grow h-px bg-gray-300" />
                      <span className="mx-4 text-gray-500 text-sm font-medium">OR</span>
                      <div className="flex-grow h-px bg-gray-300" />
                    </div>
                    
                    <Button
                      variant="outline"
                      className="w-full flex items-center justify-center gap-3 border-2 border-gray-200 hover:border-gray-300 hover:bg-gray-50 py-3 h-12 font-medium transition-all duration-200 shadow-sm hover:shadow-md disabled:opacity-50 disabled:cursor-not-allowed"
                      onClick={handleGoogleSignIn}
                      disabled={loading || googleLoading}
                    >
                      <svg className="w-5 h-5" viewBox="0 0 24 24">
                        <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                        <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                        <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                        <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                      </svg>
                      {googleLoading ? (
                        <>
                          <RefreshCw className="w-4 h-4 animate-spin" />
                          Connecting...
                        </>
                      ) : (
                        'Sign in or Sign up with Google'
                      )}
                    </Button>
                  </>
                )}
                <div className="text-center">
                  <button
                    type="button"
                    className="text-sm text-primary hover:underline"
                    onClick={() => setIsSignUp(!isSignUp)}
                  >
                    {isSignUp ? 'Already have an account? Sign In' : "Don't have an account? Sign Up"}
                  </button>
                </div>
              </form>
            )}

          </div>

          {/* Wedding Planning Benefits - Second on mobile, first on desktop */}
          <div className="w-full lg:w-1/2 bg-gradient-to-br from-primary to-pink-600 p-8 lg:p-12 text-white lg:order-1">
            <div className="h-full flex flex-col justify-center">
              <div className="mb-8">
                <h1 className="text-3xl lg:text-4xl font-bold mb-4">
                  Plan Your Perfect Wedding
                </h1>
                <p className="text-lg opacity-90 mb-6">
                  Join thousands of couples who trust BookmyFestive to plan their dream wedding with the best vendors and venues.
                </p>
              </div>

              <div className="space-y-4 mb-8">
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center">
                    <span className="text-sm font-bold">✓</span>
                  </div>
                  <span>Discover verified wedding vendors</span>
                </div>
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center">
                    <span className="text-sm font-bold">✓</span>
                  </div>
                  <span>Compare prices and packages</span>
                </div>
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center">
                    <span className="text-sm font-bold">✓</span>
                  </div>
                  <span>Save your favorite vendors</span>
                </div>
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center">
                    <span className="text-sm font-bold">✓</span>
                  </div>
                  <span>Get personalized recommendations</span>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4 text-center">
                <div>
                  <div className="text-2xl font-bold">50,000+</div>
                  <div className="text-sm opacity-80">Happy Couples</div>
                </div>
                <div>
                  <div className="text-2xl font-bold">5,000+</div>
                  <div className="text-sm opacity-80">Trusted Vendors</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
      <Footer />

      {/* Google Signup Modal */}
      <GoogleSignupModal
        isOpen={showGoogleSignupModal}
        onClose={() => setShowGoogleSignupModal(false)}
        onComplete={() => {
          setShowGoogleSignupModal(false)
          handlePostLoginRedirect()
        }}
      />
    </div>
  )
}