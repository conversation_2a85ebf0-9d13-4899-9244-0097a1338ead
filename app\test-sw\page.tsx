'use client'

import { useState } from 'react'
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"

export default function TestServiceWorker() {
  const [logs, setLogs] = useState<string[]>([])
  const [isLoading, setIsLoading] = useState(false)

  const addLog = (message: string) => {
    setLogs(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`])
  }

  const testServiceWorkerFile = async () => {
    setIsLoading(true)
    addLog('Testing service worker file accessibility...')
    
    try {
      const response = await fetch('/sw.js')
      addLog(`Fetch response status: ${response.status} ${response.statusText}`)
      
      if (response.ok) {
        const text = await response.text()
        addLog(`Service worker file size: ${text.length} characters`)
        addLog('Service worker file is accessible ✅')
        
        // Check if it's valid JavaScript
        try {
          new Function(text)
          addLog('Service worker syntax is valid ✅')
        } catch (syntaxError) {
          addLog(`Service worker syntax error: ${syntaxError.message} ❌`)
        }
      } else {
        addLog(`Service worker file not accessible: ${response.status} ❌`)
      }
    } catch (error) {
      addLog(`Error fetching service worker: ${error.message} ❌`)
    }
    
    setIsLoading(false)
  }

  const testServiceWorkerRegistration = async () => {
    setIsLoading(true)
    addLog('Testing service worker registration...')
    
    if (!('serviceWorker' in navigator)) {
      addLog('Service workers not supported in this browser ❌')
      setIsLoading(false)
      return
    }

    try {
      addLog('Attempting to register service worker...')
      const registration = await navigator.serviceWorker.register('/sw.js', {
        scope: '/'
      })
      
      addLog(`Service worker registered successfully ✅`)
      addLog(`Scope: ${registration.scope}`)
      addLog(`Active: ${registration.active ? 'Yes' : 'No'}`)
      addLog(`Installing: ${registration.installing ? 'Yes' : 'No'}`)
      addLog(`Waiting: ${registration.waiting ? 'Yes' : 'No'}`)
      
    } catch (error) {
      addLog(`Service worker registration failed: ${error.message} ❌`)
      addLog(`Error name: ${error.name}`)
      addLog(`Error stack: ${error.stack}`)
    }
    
    setIsLoading(false)
  }

  const clearLogs = () => {
    setLogs([])
  }

  return (
    <div className="container mx-auto p-6 max-w-4xl">
      <Card>
        <CardHeader>
          <CardTitle>Service Worker Debug Tool</CardTitle>
          <CardDescription>
            Test service worker file accessibility and registration
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-2 flex-wrap">
            <Button 
              onClick={testServiceWorkerFile}
              disabled={isLoading}
              variant="outline"
            >
              Test SW File Access
            </Button>
            
            <Button 
              onClick={testServiceWorkerRegistration}
              disabled={isLoading}
              variant="default"
            >
              Test SW Registration
            </Button>
            
            <Button 
              onClick={clearLogs}
              disabled={isLoading}
              variant="secondary"
            >
              Clear Logs
            </Button>
          </div>

          <div className="bg-gray-100 p-4 rounded-lg min-h-[300px] max-h-[500px] overflow-y-auto">
            <h3 className="font-semibold mb-2">Debug Logs:</h3>
            {logs.length === 0 ? (
              <p className="text-gray-500">No logs yet. Click a test button to start.</p>
            ) : (
              <div className="space-y-1">
                {logs.map((log, index) => (
                  <div 
                    key={index} 
                    className={`text-sm font-mono ${
                      log.includes('✅') ? 'text-green-600' : 
                      log.includes('❌') ? 'text-red-600' : 
                      'text-gray-700'
                    }`}
                  >
                    {log}
                  </div>
                ))}
              </div>
            )}
          </div>

          <div className="text-sm text-gray-600">
            <p><strong>Instructions:</strong></p>
            <ul className="list-disc list-inside space-y-1">
              <li>First test if the service worker file is accessible</li>
              <li>Then test the service worker registration</li>
              <li>Check the logs for any errors or issues</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
