import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Image,
  TextInput,
  ScrollView,
  Alert,
  RefreshControl,
  Modal,
  Dimensions,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation, useRoute } from '@react-navigation/native';
import { useTheme } from '../providers/ThemeProvider';
import { useFavorites } from '../providers/FavoritesProvider';
import { useSearch } from '../providers/SearchProvider';
import { GraphQLService } from '../services/graphqlService';
import LoadingSpinner from '../components/LoadingSpinner';
import { Card, CardContent, Button, Input, Badge } from '../components/ui';
import DateTimePicker from '@react-native-community/datetimepicker';

interface Venue {
  id: string;
  name: string;
  type: string;
  capacity: string;
  location: string;
  city: string;
  state: string;
  price: string;
  priceRange: string;
  images: string[];
  amenities: string[];
  rating: number;
  reviewCount: number;
  verified: boolean;
  featured: boolean;
  status: string;
  description: string;
}

const graphqlService = new GraphQLService();

// Venue types matching web application
const venueTypes = [
  { id: 'all', name: 'All Venues', icon: 'business' },
  { id: 'banquet-hall', name: 'Banquet Halls', icon: 'business' },
  { id: 'resort', name: 'Resorts', icon: 'leaf' },
  { id: 'hotel', name: 'Hotels', icon: 'bed' },
  { id: 'farmhouse', name: 'Farmhouses', icon: 'home' },
  { id: 'palace', name: 'Palaces', icon: 'diamond' },
  { id: 'beach', name: 'Beach Venues', icon: 'water' },
  { id: 'garden', name: 'Garden Venues', icon: 'flower' },
  { id: 'temple', name: 'Temple Venues', icon: 'library' },
];

// Sort options matching web application
const sortOptions = [
  { id: 'default', name: 'Default' },
  { id: 'rating', name: 'Highest Rated' },
  { id: 'price-low', name: 'Price: Low to High' },
  { id: 'price-high', name: 'Price: High to Low' },
];

export default function VenuesScreen() {
  const navigation = useNavigation();
  const route = useRoute();
  const { theme } = useTheme();
  const { isFavorite, addToFavorites, removeFromFavorites } = useFavorites();
  const { addToSearchHistory } = useSearch();

  const [venues, setVenues] = useState<Venue[]>([]);
  const [filteredVenues, setFilteredVenues] = useState<Venue[]>([]);
  const [selectedType, setSelectedType] = useState('all');
  const [selectedCity, setSelectedCity] = useState('');
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [sortBy, setSortBy] = useState('default');
  const [showFilters, setShowFilters] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(10);
  const [loading, setLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [searchText, setSearchText] = useState('');

  // Initialize from route params if available
  useEffect(() => {
    if (route.params) {
      const { query, city, venueType } = route.params as any;
      if (query) setSearchText(query);
      if (city) setSelectedCity(city);
      if (venueType) setSelectedType(venueType);
    }
  }, [route.params]);

  useEffect(() => {
    loadVenues();
  }, []);

  useEffect(() => {
    applyFiltersAndSort();
  }, [venues, searchText, selectedType, selectedCity, selectedDate, sortBy]);

  // Apply filters and sorting
  const applyFiltersAndSort = () => {
    let filtered = venues.filter(venue => {
      const matchesSearch = venue.name?.toLowerCase().includes(searchText.toLowerCase()) ||
                           venue.description?.toLowerCase().includes(searchText.toLowerCase());
      const matchesCity = !selectedCity || venue.city?.toLowerCase().includes(selectedCity.toLowerCase());
      const matchesType = selectedType === 'all' || venue.type === selectedType;

      // Date filtering (basic implementation)
      const matchesDate = !selectedDate || true; // Implement actual date availability logic here

      return matchesSearch && matchesCity && matchesType && matchesDate;
    });

    // Sort venues
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'rating':
          return (b.rating || 0) - (a.rating || 0);
        case 'price-low':
          return parseFloat(a.price.replace(/[^\d]/g, '') || '0') - parseFloat(b.price.replace(/[^\d]/g, '') || '0');
        case 'price-high':
          return parseFloat(b.price.replace(/[^\d]/g, '') || '0') - parseFloat(a.price.replace(/[^\d]/g, '') || '0');
        default:
          return a.name.localeCompare(b.name);
      }
    });

    setFilteredVenues(filtered);
    setCurrentPage(1); // Reset to first page when filters change
  };

  const loadVenues = async () => {
    try {
      setLoading(true);
      const result = await graphqlService.listVenues({}, 50);
      setVenues(result.items || []);
    } catch (error) {
      console.error('Error loading venues:', error);
      Alert.alert('Error', 'Failed to load venues');
      setVenues([]);
    } finally {
      setLoading(false);
    }
  };

  const extractPrice = (price: string): number => {
    const match = price?.match(/[\d,]+/);
    return match ? parseInt(match[0].replace(/,/g, '')) : 0;
  };

  const extractCapacity = (capacity: string): number => {
    const match = capacity?.match(/[\d,]+/);
    return match ? parseInt(match[0].replace(/,/g, '')) : 0;
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadVenues();
    setRefreshing(false);
  };

  const handleSearch = () => {
    if (searchText.trim()) {
      addToSearchHistory(searchText.trim());
    }
  };

  const toggleFavorite = async (venue: Venue) => {
    try {
      if (isFavorite(venue.id)) {
        await removeFromFavorites(venue.id);
      } else {
        await addToFavorites({
          id: venue.id,
          name: venue.name,
          type: 'venue',
          image: venue.images?.[0] || '',
          price: venue.price,
          location: venue.location,
          rating: venue.rating,
        });
      }
    } catch (error) {
      console.error('Error toggling favorite:', error);
    }
  };

  // Handle date selection
  const handleDateChange = (event: any, selectedDate?: Date) => {
    setShowDatePicker(false);
    if (selectedDate) {
      setSelectedDate(selectedDate);
    }
  };

  // Get paginated venues
  const getPaginatedVenues = () => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    return filteredVenues.slice(startIndex, endIndex);
  };

  const totalPages = Math.ceil(filteredVenues.length / itemsPerPage);

  // Render venue type filter
  const renderVenueTypeFilter = () => (
    <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.filterScroll}>
      {venueTypes.map((type) => (
        <TouchableOpacity
          key={type.id}
          style={[
            styles.typeFilterButton,
            {
              backgroundColor: selectedType === type.id ? theme.colors.primary : theme.colors.surface,
              borderColor: selectedType === type.id ? theme.colors.primary : theme.colors.border,
            }
          ]}
          onPress={() => setSelectedType(type.id)}
        >
          <Ionicons
            name={type.icon as any}
            size={20}
            color={selectedType === type.id ? 'white' : theme.colors.text}
          />
          <Text style={[
            styles.typeFilterText,
            { color: selectedType === type.id ? 'white' : theme.colors.text }
          ]}>
            {type.name}
          </Text>
        </TouchableOpacity>
      ))}
    </ScrollView>
  );

  if (loading && venues.length === 0) {
    return <LoadingSpinner />;
  }

  const renderVenue = ({ item }: { item: Venue }) => (
    <TouchableOpacity
      style={[styles.venueCard, { backgroundColor: theme.colors.surface }]}
      onPress={() => navigation.navigate('VenueDetail', { venueId: item.id })}
    >
      <Image
        source={{ uri: item.images?.[0] || '/placeholder-image.jpg
        style={styles.venueImage}
        resizeMode="cover"
      />

      {/* Featured Badge */}
      {item.featured && (
        <View style={styles.featuredBadge}>
          <Text style={styles.featuredText}>Featured</Text>
        </View>
      )}

      {/* Verified Badge */}
      {item.verified && (
        <View style={styles.verifiedBadge}>
          <Ionicons name="checkmark-circle" size={16} color="#10B981" />
        </View>
      )}

      <View style={styles.venueInfo}>
        <Text style={[styles.venueName, { color: theme.colors.text }]} numberOfLines={2}>
          {item.name}
        </Text>

        <View style={styles.venueDetails}>
          <Ionicons name="location-outline" size={14} color={theme.colors.textSecondary} />
          <Text style={[styles.venueLocation, { color: theme.colors.textSecondary }]} numberOfLines={1}>
            {item.location}
          </Text>
        </View>

        <View style={styles.venueDetails}>
          <Ionicons name="people-outline" size={14} color={theme.colors.textSecondary} />
          <Text style={[styles.venueCapacity, { color: theme.colors.textSecondary }]}>
            {item.capacity} guests
          </Text>
        </View>

        <View style={styles.ratingContainer}>
          <Ionicons name="star" size={14} color="#F59E0B" />
          <Text style={[styles.rating, { color: theme.colors.text }]}>
            {item.rating?.toFixed(1) || '4.0'}
          </Text>
          <Text style={[styles.reviewCount, { color: theme.colors.textSecondary }]}>
            ({item.reviewCount || 0})
          </Text>
        </View>

        <View style={styles.priceContainer}>
          <Text style={[styles.price, { color: theme.colors.primary }]}>
            ₹{item.price}
          </Text>
          <TouchableOpacity
            style={styles.favoriteButton}
            onPress={() => toggleFavorite(item)}
          >
            <Ionicons
              name={isFavorite(item.id) ? "heart" : "heart-outline"}
              size={20}
              color={isFavorite(item.id) ? "#EF4444" : theme.colors.textSecondary}
            />
          </TouchableOpacity>
        </View>
      </View>
    </TouchableOpacity>
  );

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {/* Search and Filters Header */}
      <View style={[styles.header, { backgroundColor: theme.colors.surface }]}>
        {/* Search Bar */}
        <View style={styles.searchContainer}>
          <View style={[styles.searchBar, { backgroundColor: theme.colors.background }]}>
            <Ionicons name="search-outline" size={20} color={theme.colors.textSecondary} />
            <TextInput
              style={[styles.searchInput, { color: theme.colors.text }]}
              placeholder="Search venues..."
              placeholderTextColor={theme.colors.textSecondary}
              value={searchText}
              onChangeText={setSearchText}
              onSubmitEditing={handleSearch}
              returnKeyType="search"
            />
          </View>

          {/* Filter and Sort Buttons */}
          <View style={styles.actionButtons}>
            <TouchableOpacity
              style={[styles.filterButton, { backgroundColor: theme.colors.primary }]}
              onPress={() => setShowFilters(!showFilters)}
            >
              <Ionicons name="options-outline" size={20} color="white" />
            </TouchableOpacity>
          </View>
        </View>

        {/* Location and Date Filters */}
        <View style={styles.quickFilters}>
          <TouchableOpacity style={[styles.quickFilterButton, { backgroundColor: theme.colors.background }]}>
            <Ionicons name="location-outline" size={16} color={theme.colors.textSecondary} />
            <TextInput
              style={[styles.quickFilterText, { color: theme.colors.text }]}
              placeholder="Enter city"
              placeholderTextColor={theme.colors.textSecondary}
              value={selectedCity}
              onChangeText={setSelectedCity}
            />
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.quickFilterButton, { backgroundColor: theme.colors.background }]}
            onPress={() => setShowDatePicker(true)}
          >
            <Ionicons name="calendar-outline" size={16} color={theme.colors.textSecondary} />
            <Text style={[styles.quickFilterText, { color: theme.colors.text }]}>
              {selectedDate ? selectedDate.toLocaleDateString() : 'Select Date'}
            </Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Venue Types Filter */}
      <View style={styles.venueTypesContainer}>
        {renderVenueTypeFilter()}
      </View>

      {/* Results Summary and Sort */}
      <View style={[styles.resultsHeader, { backgroundColor: theme.colors.surface }]}>
        <Text style={[styles.resultsCount, { color: theme.colors.text }]}>
          {filteredVenues.length} venues found
        </Text>
        <TouchableOpacity
          style={styles.sortButton}
          onPress={() => {
            // Cycle through sort options
            const currentIndex = sortOptions.findIndex(option => option.id === sortBy);
            const nextIndex = (currentIndex + 1) % sortOptions.length;
            setSortBy(sortOptions[nextIndex].id);
          }}
        >
          <Text style={[styles.sortText, { color: theme.colors.primary }]}>
            Sort: {sortOptions.find(option => option.id === sortBy)?.name}
          </Text>
          <Ionicons name="chevron-down" size={16} color={theme.colors.primary} />
        </TouchableOpacity>
      </View>

      {/* Venues List */}
      
      <FlatList
        data={getPaginatedVenues()}
        renderItem={renderVenue}
        keyExtractor={(item) => item.id}
        numColumns={1} // CHANGED from 2 to 1
        // columnWrapperStyle={styles.row} // REMOVE or comment out for single column
        contentContainerStyle={styles.venuesContainer}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Ionicons name="business-outline" size={64} color={theme.colors.textSecondary} />
            <Text style={[styles.emptyText, { color: theme.colors.textSecondary }]}>
              No venues found
            </Text>
            <Text style={[styles.emptySubtext, { color: theme.colors.textSecondary }]}>
              Try adjusting your filters
            </Text>
          </View>
        }
      />

      {/* Pagination */}
      {totalPages > 1 && (
        <View style={[styles.paginationContainer, { backgroundColor: theme.colors.surface }]}>
          <TouchableOpacity
            style={[styles.paginationButton, { opacity: currentPage === 1 ? 0.5 : 1 }]}
            onPress={() => setCurrentPage(Math.max(1, currentPage - 1))}
            disabled={currentPage === 1}
          >
            <Ionicons name="chevron-back" size={20} color={theme.colors.primary} />
          </TouchableOpacity>

          <Text style={[styles.paginationText, { color: theme.colors.text }]}>
            Page {currentPage} of {totalPages}
          </Text>

          <TouchableOpacity
            style={[styles.paginationButton, { opacity: currentPage === totalPages ? 0.5 : 1 }]}
            onPress={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
            disabled={currentPage === totalPages}
          >
            <Ionicons name="chevron-forward" size={20} color={theme.colors.primary} />
          </TouchableOpacity>
        </View>
      )}

      {/* Date Picker Modal */}
      {showDatePicker && (
        <DateTimePicker
          value={selectedDate || new Date()}
          mode="date"
          display="default"
          onChange={handleDateChange}
          minimumDate={new Date()}
        />
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  // Header Styles
  header: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  searchContainer: {
    marginBottom: 12,
  },
  searchBar: {
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
    marginBottom: 8,
  },
  searchInput: {
    flex: 1,
    marginLeft: 8,
    fontSize: 16,
  },
  actionButtons: {
    flexDirection: 'row',
    gap: 8,
    marginTop: 8,
  },
  filterButton: {
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  quickFilters: {
    flexDirection: 'row',
    gap: 8,
  },
  quickFilterButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    padding: 8,
    borderRadius: 6,
    gap: 6,
  },
  quickFilterText: {
    flex: 1,
    fontSize: 14,
  },
  // Venue Types Filter Styles
  venueTypesContainer: {
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  filterScroll: {
    paddingHorizontal: 16,
  },
  typeFilterButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    marginRight: 8,
    borderWidth: 1,
    gap: 6,
  },
  typeFilterText: {
    fontSize: 14,
    fontWeight: '500',
  },
  // Results Header Styles
  resultsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  resultsCount: {
    fontSize: 16,
    fontWeight: '600',
  },
  sortButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  sortText: {
    fontSize: 14,
    fontWeight: '500',
  },
  // Venues List Styles
  venuesContainer: {
    padding: 0, // Optionally set to 0 for true edge-to-edge
  },
  row: {
    justifyContent: 'space-between',
  },
  venueCard: {
    width: '100%', // CHANGED from '48%' to '100%'
    borderRadius: 12,
    marginBottom: 16,
    overflow: 'hidden',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  venueImage: {
    width: '100%',
    height: 120,
  },
  featuredBadge: {
    position: 'absolute',
    top: 8,
    left: 8,
    backgroundColor: '#EF4444',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
  },
  featuredText: {
    color: 'white',
    fontSize: 10,
    fontWeight: 'bold',
  },
  verifiedBadge: {
    position: 'absolute',
    top: 8,
    right: 8,
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 4,
  },
  venueInfo: {
    padding: 12,
  },
  venueName: {
    fontSize: 14,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  venueDetails: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  venueLocation: {
    fontSize: 12,
    marginLeft: 4,
    flex: 1,
  },
  venueCapacity: {
    fontSize: 12,
    marginLeft: 4,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  rating: {
    fontSize: 12,
    fontWeight: '600',
    marginLeft: 2,
  },
  reviewCount: {
    fontSize: 10,
    marginLeft: 4,
  },
  priceContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  price: {
    fontSize: 14,
    fontWeight: 'bold',
  },
  favoriteButton: {
    padding: 4,
  },
  // Pagination Styles
  paginationContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
  },
  paginationButton: {
    padding: 8,
  },
  paginationText: {
    fontSize: 14,
    fontWeight: '500',
  },
  // Empty State Styles
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 64,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: '600',
    marginTop: 16,
    marginBottom: 8,
  },
  emptySubtext: {
    fontSize: 14,
    textAlign: 'center',
  },
});
