import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { useAuth } from '../providers/AuthProvider';
import { useTheme } from '../providers/ThemeProvider';

export type UserRole = 'customer' | 'vendor' | 'admin' | 'super_admin';

interface RoleBasedAccessProps {
  allowedRoles: UserRole[];
  children: React.ReactNode;
  fallbackComponent?: React.ReactNode;
  redirectTo?: string;
  showFallback?: boolean;
}

// Role hierarchy for permission checking
const ROLE_HIERARCHY: Record<UserRole, number> = {
  customer: 1,
  vendor: 2,
  admin: 3,
  super_admin: 4,
};

// Helper function to check if user has required role or higher
export const hasRole = (userRole: UserRole | null, requiredRole: UserRole): boolean => {
  if (!userRole) return false;
  return ROLE_HIERARCHY[userRole] >= ROLE_HIERARCHY[requiredRole];
};

// Helper function to check if user has any of the allowed roles
export const hasAnyRole = (userRole: UserRole | null, allowedRoles: UserRole[]): boolean => {
  if (!userRole) return false;
  return allowedRoles.some(role => hasRole(userRole, role));
};

// Helper function to get user role from profile
export const getUserRole = (userProfile: any): UserRole => {
  // Check various role fields in the user profile
  if (userProfile?.role) {
    return userProfile.role as UserRole;
  }
  
  if (userProfile?.userType) {
    return userProfile.userType as UserRole;
  }
  
  // Check boolean flags
  if (userProfile?.isSuperAdmin) return 'super_admin';
  if (userProfile?.isAdmin) return 'admin';
  if (userProfile?.isVendor) return 'vendor';
  
  // Default to customer
  return 'customer';
};

export default function RoleBasedAccess({
  allowedRoles,
  children,
  fallbackComponent,
  redirectTo,
  showFallback = true,
}: RoleBasedAccessProps) {
  const { userProfile, isAuthenticated } = useAuth();
  const { theme } = useTheme();
  const navigation = useNavigation();

  // If user is not authenticated, don't show anything
  if (!isAuthenticated) {
    return null;
  }

  const userRole = getUserRole(userProfile);
  const hasAccess = hasAnyRole(userRole, allowedRoles);

  // If user has access, render children
  if (hasAccess) {
    return <>{children}</>;
  }

  // If no fallback should be shown, return null
  if (!showFallback) {
    return null;
  }

  // If custom fallback component is provided, use it
  if (fallbackComponent) {
    return <>{fallbackComponent}</>;
  }

  // Default fallback component
  return (
    <View style={[styles.fallbackContainer, { backgroundColor: theme.colors.background }]}>
      <View style={[styles.fallbackContent, { backgroundColor: theme.colors.surface }]}>
        <Ionicons name="lock-closed-outline" size={48} color={theme.colors.textSecondary} />
        
        <Text style={[styles.fallbackTitle, { color: theme.colors.text }]}>
          Access Restricted
        </Text>
        
        <Text style={[styles.fallbackMessage, { color: theme.colors.textSecondary }]}>
          You don't have permission to access this feature.
        </Text>
        
        <Text style={[styles.fallbackRoleInfo, { color: theme.colors.textSecondary }]}>
          Required role: {allowedRoles.join(' or ')}
        </Text>
        
        <Text style={[styles.fallbackCurrentRole, { color: theme.colors.textSecondary }]}>
          Your role: {userRole}
        </Text>

        {redirectTo && (
          <TouchableOpacity
            style={[styles.redirectButton, { backgroundColor: theme.colors.primary }]}
            onPress={() => navigation.navigate(redirectTo as never)}
          >
            <Text style={styles.redirectButtonText}>Go Back</Text>
          </TouchableOpacity>
        )}
      </View>
    </View>
  );
}

// Higher-order component for protecting entire screens
export function withRoleBasedAccess<T extends object>(
  Component: React.ComponentType<T>,
  allowedRoles: UserRole[],
  options?: {
    fallbackComponent?: React.ReactNode;
    redirectTo?: string;
    showFallback?: boolean;
  }
) {
  return function ProtectedComponent(props: T) {
    return (
      <RoleBasedAccess
        allowedRoles={allowedRoles}
        fallbackComponent={options?.fallbackComponent}
        redirectTo={options?.redirectTo}
        showFallback={options?.showFallback}
      >
        <Component {...props} />
      </RoleBasedAccess>
    );
  };
}

// Hook for checking permissions in components
export function useRoleBasedAccess() {
  const { userProfile, isAuthenticated } = useAuth();
  
  const userRole = isAuthenticated ? getUserRole(userProfile) : null;
  
  return {
    userRole,
    hasRole: (requiredRole: UserRole) => hasRole(userRole, requiredRole),
    hasAnyRole: (allowedRoles: UserRole[]) => hasAnyRole(userRole, allowedRoles),
    isCustomer: userRole === 'customer',
    isVendor: hasRole(userRole, 'vendor'),
    isAdmin: hasRole(userRole, 'admin'),
    isSuperAdmin: hasRole(userRole, 'super_admin'),
  };
}

const styles = StyleSheet.create({
  fallbackContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  fallbackContent: {
    padding: 32,
    borderRadius: 16,
    alignItems: 'center',
    maxWidth: 300,
    width: '100%',
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  fallbackTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginTop: 16,
    marginBottom: 8,
    textAlign: 'center',
  },
  fallbackMessage: {
    fontSize: 14,
    textAlign: 'center',
    marginBottom: 16,
    lineHeight: 20,
  },
  fallbackRoleInfo: {
    fontSize: 12,
    textAlign: 'center',
    marginBottom: 4,
  },
  fallbackCurrentRole: {
    fontSize: 12,
    textAlign: 'center',
    marginBottom: 20,
    fontWeight: '600',
  },
  redirectButton: {
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  redirectButtonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '600',
  },
});
