import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Image,
  Alert,
  RefreshControl,
  TextInput,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { useTheme } from '../providers/ThemeProvider';
import { useAuth } from '../providers/AuthProvider';
import LoadingSpinner from '../components/LoadingSpinner';
import EmptyState from '../components/EmptyState';

interface VendorProduct {
  id: string;
  name: string;
  description: string;
  price: number;
  originalPrice?: number;
  category: string;
  images: string[];
  inStock: boolean;
  stockQuantity: number;
  status: 'active' | 'inactive' | 'draft';
  views: number;
  orders: number;
  rating: number;
  reviewCount: number;
  createdAt: string;
  updatedAt: string;
}

export default function VendorProductsScreen() {
  const navigation = useNavigation();
  const { theme } = useTheme();
  const { user } = useAuth();
  
  const [products, setProducts] = useState<VendorProduct[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [filterStatus, setFilterStatus] = useState<'all' | 'active' | 'inactive' | 'draft'>('all');

  useEffect(() => {
    loadProducts();
  }, []);

  const loadProducts = async () => {
    try {
      setLoading(true);
      
      // TODO: Replace with actual GraphQL query
      // const vendorProducts = await graphqlService.getVendorProducts(user?.id);
      
      // Mock data for now
      const mockProducts: VendorProduct[] = [
        {
          id: 'PROD_001',
          name: 'Elegant Wedding Lehenga',
          description: 'Beautiful red and gold wedding lehenga with intricate embroidery',
          price: 25000,
          originalPrice: 30000,
          category: 'Bridal Wear',
          images: ['https://example.com/lehenga1.jpg', 'https://example.com/lehenga2.jpg'],
          inStock: true,
          stockQuantity: 5,
          status: 'active',
          views: 245,
          orders: 12,
          rating: 4.8,
          reviewCount: 15,
          createdAt: '2024-01-15T10:30:00Z',
          updatedAt: '2024-01-20T14:15:00Z',
        },
        {
          id: 'PROD_002',
          name: 'Groom Sherwani Set',
          description: 'Traditional cream colored sherwani with matching accessories',
          price: 18000,
          category: 'Groom Wear',
          images: ['https://example.com/sherwani1.jpg'],
          inStock: true,
          stockQuantity: 8,
          status: 'active',
          views: 189,
          orders: 8,
          rating: 4.6,
          reviewCount: 10,
          createdAt: '2024-01-10T09:15:00Z',
          updatedAt: '2024-01-18T11:30:00Z',
        },
        {
          id: 'PROD_003',
          name: 'Bridal Jewelry Set',
          description: 'Complete jewelry set with necklace, earrings, and maang tikka',
          price: 15000,
          category: 'Jewelry',
          images: ['https://example.com/jewelry1.jpg'],
          inStock: false,
          stockQuantity: 0,
          status: 'inactive',
          views: 156,
          orders: 5,
          rating: 4.4,
          reviewCount: 8,
          createdAt: '2024-01-05T16:45:00Z',
          updatedAt: '2024-01-22T10:20:00Z',
        },
      ];
      
      setProducts(mockProducts);
    } catch (error) {
      console.error('Failed to load products:', error);
      Alert.alert('Error', 'Failed to load products');
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadProducts();
    setRefreshing(false);
  };

  const filteredProducts = products.filter(product => {
    const matchesSearch = product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         product.category.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesFilter = filterStatus === 'all' || product.status === filterStatus;
    return matchesSearch && matchesFilter;
  });

  const handleProductPress = (product: VendorProduct) => {
    navigation.navigate('EditProduct' as never, { productId: product.id } as never);
  };

  const handleToggleStatus = async (productId: string, currentStatus: string) => {
    try {
      const newStatus = currentStatus === 'active' ? 'inactive' : 'active';
      
      // TODO: Update product status via GraphQL
      // await graphqlService.updateProductStatus(productId, newStatus);
      
      setProducts(prev => prev.map(product => 
        product.id === productId 
          ? { ...product, status: newStatus as any }
          : product
      ));
      
      Alert.alert('Success', `Product ${newStatus === 'active' ? 'activated' : 'deactivated'} successfully`);
    } catch (error) {
      console.error('Failed to update product status:', error);
      Alert.alert('Error', 'Failed to update product status');
    }
  };

  const handleDeleteProduct = (productId: string, productName: string) => {
    Alert.alert(
      'Delete Product',
      `Are you sure you want to delete "${productName}"? This action cannot be undone.`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              // TODO: Delete product via GraphQL
              // await graphqlService.deleteProduct(productId);
              
              setProducts(prev => prev.filter(product => product.id !== productId));
              Alert.alert('Success', 'Product deleted successfully');
            } catch (error) {
              console.error('Failed to delete product:', error);
              Alert.alert('Error', 'Failed to delete product');
            }
          },
        },
      ]
    );
  };

  const getStatusColor = (status: VendorProduct['status']) => {
    switch (status) {
      case 'active':
        return theme.colors.success;
      case 'inactive':
        return theme.colors.error;
      case 'draft':
        return theme.colors.warning;
      default:
        return theme.colors.textSecondary;
    }
  };

  const renderProductItem = ({ item: product }: { item: VendorProduct }) => (
    <TouchableOpacity
      style={[styles.productCard, { backgroundColor: theme.colors.surface }]}
      onPress={() => handleProductPress(product)}
    >
      {/* Product Image */}
      <View style={styles.productImageContainer}>
        {product.images.length > 0 ? (
          <Image source={{ uri: product.images[0] }} style={styles.productImage} />
        ) : (
          <View style={[styles.productImagePlaceholder, { backgroundColor: theme.colors.border }]}>
            <Ionicons name="image-outline" size={32} color={theme.colors.textSecondary} />
          </View>
        )}
        
        {/* Status Badge */}
        <View style={[styles.statusBadge, { backgroundColor: getStatusColor(product.status) }]}>
          <Text style={styles.statusText}>{product.status.toUpperCase()}</Text>
        </View>
      </View>

      {/* Product Info */}
      <View style={styles.productInfo}>
        <Text style={[styles.productName, { color: theme.colors.text }]} numberOfLines={2}>
          {product.name}
        </Text>
        
        <Text style={[styles.productCategory, { color: theme.colors.textSecondary }]}>
          {product.category}
        </Text>

        <View style={styles.priceContainer}>
          <Text style={[styles.productPrice, { color: theme.colors.primary }]}>
            ₹{product.price.toLocaleString()}
          </Text>
          {product.originalPrice && product.originalPrice > product.price && (
            <Text style={[styles.originalPrice, { color: theme.colors.textSecondary }]}>
              ₹{product.originalPrice.toLocaleString()}
            </Text>
          )}
        </View>

        {/* Stats */}
        <View style={styles.statsContainer}>
          <View style={styles.statItem}>
            <Ionicons name="eye-outline" size={14} color={theme.colors.textSecondary} />
            <Text style={[styles.statText, { color: theme.colors.textSecondary }]}>
              {product.views}
            </Text>
          </View>
          
          <View style={styles.statItem}>
            <Ionicons name="bag-outline" size={14} color={theme.colors.textSecondary} />
            <Text style={[styles.statText, { color: theme.colors.textSecondary }]}>
              {product.orders}
            </Text>
          </View>
          
          <View style={styles.statItem}>
            <Ionicons name="star" size={14} color="#FFD700" />
            <Text style={[styles.statText, { color: theme.colors.textSecondary }]}>
              {product.rating} ({product.reviewCount})
            </Text>
          </View>
        </View>

        {/* Stock Status */}
        <View style={styles.stockContainer}>
          <Ionicons 
            name={product.inStock ? "checkmark-circle" : "close-circle"} 
            size={16} 
            color={product.inStock ? theme.colors.success : theme.colors.error} 
          />
          <Text style={[styles.stockText, { 
            color: product.inStock ? theme.colors.success : theme.colors.error 
          }]}>
            {product.inStock ? `In Stock (${product.stockQuantity})` : 'Out of Stock'}
          </Text>
        </View>
      </View>

      {/* Action Buttons */}
      <View style={styles.actionButtons}>
        <TouchableOpacity
          style={[styles.actionButton, { backgroundColor: theme.colors.primary + '20' }]}
          onPress={() => handleToggleStatus(product.id, product.status)}
        >
          <Ionicons 
            name={product.status === 'active' ? 'pause-outline' : 'play-outline'} 
            size={16} 
            color={theme.colors.primary} 
          />
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[styles.actionButton, { backgroundColor: theme.colors.error + '20' }]}
          onPress={() => handleDeleteProduct(product.id, product.name)}
        >
          <Ionicons name="trash-outline" size={16} color={theme.colors.error} />
        </TouchableOpacity>
      </View>
    </TouchableOpacity>
  );

  if (loading) {
    return <LoadingSpinner fullScreen text="Loading products..." />;
  }

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {/* Header with Search and Add Button */}
      <View style={[styles.header, { backgroundColor: theme.colors.surface }]}>
        <View style={[styles.searchContainer, { backgroundColor: theme.colors.background }]}>
          <Ionicons name="search-outline" size={20} color={theme.colors.textSecondary} />
          <TextInput
            style={[styles.searchInput, { color: theme.colors.text }]}
            placeholder="Search products..."
            placeholderTextColor={theme.colors.textSecondary}
            value={searchQuery}
            onChangeText={setSearchQuery}
          />
        </View>
        
        <TouchableOpacity
          style={[styles.addButton, { backgroundColor: theme.colors.primary }]}
          onPress={() => navigation.navigate('AddProduct' as never)}
        >
          <Ionicons name="add" size={24} color="white" />
        </TouchableOpacity>
      </View>

      {/* Filter Tabs */}
      <View style={[styles.filterContainer, { backgroundColor: theme.colors.surface }]}>
        {(['all', 'active', 'inactive', 'draft'] as const).map((status) => (
          <TouchableOpacity
            key={status}
            style={[
              styles.filterTab,
              filterStatus === status && { backgroundColor: theme.colors.primary + '20' }
            ]}
            onPress={() => setFilterStatus(status)}
          >
            <Text style={[
              styles.filterText,
              { color: filterStatus === status ? theme.colors.primary : theme.colors.textSecondary }
            ]}>
              {status.charAt(0).toUpperCase() + status.slice(1)}
            </Text>
          </TouchableOpacity>
        ))}
      </View>

      {/* Products List */}
      {filteredProducts.length === 0 ? (
        <EmptyState
          icon="cube-outline"
          title="No products found"
          message={searchQuery ? "Try adjusting your search" : "Start by adding your first product"}
          actionText="Add Product"
          onAction={() => navigation.navigate('AddProduct' as never)}
          fullScreen
        />
      ) : (
        <FlatList
          data={filteredProducts}
          renderItem={renderProductItem}
          keyExtractor={(item) => item.id}
          contentContainerStyle={styles.productsList}
          showsVerticalScrollIndicator={false}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onRefresh}
              colors={[theme.colors.primary]}
              tintColor={theme.colors.primary}
            />
          }
        />
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    gap: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  searchContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    gap: 8,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
  },
  addButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    justifyContent: 'center',
    alignItems: 'center',
  },
  filterContainer: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    paddingVertical: 8,
    gap: 8,
  },
  filterTab: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
  },
  filterText: {
    fontSize: 14,
    fontWeight: '500',
  },
  productsList: {
    padding: 16,
  },
  productCard: {
    flexDirection: 'row',
    padding: 16,
    borderRadius: 12,
    marginBottom: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  productImageContainer: {
    position: 'relative',
    marginRight: 12,
  },
  productImage: {
    width: 80,
    height: 80,
    borderRadius: 8,
  },
  productImagePlaceholder: {
    width: 80,
    height: 80,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  statusBadge: {
    position: 'absolute',
    top: 4,
    right: 4,
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 8,
  },
  statusText: {
    color: 'white',
    fontSize: 8,
    fontWeight: 'bold',
  },
  productInfo: {
    flex: 1,
    gap: 4,
  },
  productName: {
    fontSize: 16,
    fontWeight: '600',
  },
  productCategory: {
    fontSize: 12,
  },
  priceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  productPrice: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  originalPrice: {
    fontSize: 12,
    textDecorationLine: 'line-through',
  },
  statsContainer: {
    flexDirection: 'row',
    gap: 12,
  },
  statItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  statText: {
    fontSize: 12,
  },
  stockContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  stockText: {
    fontSize: 12,
    fontWeight: '500',
  },
  actionButtons: {
    gap: 8,
    marginLeft: 8,
  },
  actionButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
});
