"use client"

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Mail, Phone, MessageSquare, Eye, EyeOff, ArrowRight, 
  CheckCircle, Clock, RefreshCw, AlertCircle 
} from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { useRouter } from 'next/navigation';
import MobileAuthService from '@/lib/services/mobileAuthService';
import toast from 'react-hot-toast';

interface UnifiedLoginFormProps {
  onSuccess?: () => void;
  redirectTo?: string;
}

export default function UnifiedLoginForm({ onSuccess, redirectTo = '/dashboard' }: UnifiedLoginFormProps) {
  const router = useRouter();
  const { signIn } = useAuth();
  
  // Form state
  const [loginMethod, setLoginMethod] = useState<'email' | 'mobile'>('email');
  const [step, setStep] = useState<'input' | 'otp' | 'password'>('input');
  
  // Email login state
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  
  // Mobile login state
  const [countryCode, setCountryCode] = useState('+91');
  const [phoneNumber, setPhoneNumber] = useState('');
  const [otp, setOtp] = useState('');
  
  // UI state
  const [loading, setLoading] = useState(false);
  const [otpSent, setOtpSent] = useState(false);
  const [resendTimer, setResendTimer] = useState(0);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  // Country codes
  const countryCodes = MobileAuthService.getSupportedCountryCodes();

  // Start resend timer
  const startResendTimer = () => {
    setResendTimer(30);
    const timer = setInterval(() => {
      setResendTimer((prev) => {
        if (prev <= 1) {
          clearInterval(timer);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);
  };

  // Handle email/password login
  const handleEmailLogin = async () => {
    if (!email || !password) {
      setError('Please enter both email and password');
      return;
    }

    if (!MobileAuthService.validateEmail(email)) {
      setError('Please enter a valid email address');
      return;
    }

    setLoading(true);
    setError('');

    try {
      await signIn(email, password);
      setSuccess('Login successful!');
      toast.success('Welcome back!');
      onSuccess?.();
      router.push(redirectTo);
    } catch (error: any) {
      console.error('Email login error:', error);
      setError(error.message || 'Login failed. Please check your credentials.');
    } finally {
      setLoading(false);
    }
  };

  // Handle email OTP login
  const handleEmailOTPLogin = async () => {
    if (!email) {
      setError('Please enter your email address');
      return;
    }

    if (!MobileAuthService.validateEmail(email)) {
      setError('Please enter a valid email address');
      return;
    }

    setLoading(true);
    setError('');

    try {
      const result = await MobileAuthService.sendEmailOTP(email);
      
      if (result.success) {
        setOtpSent(true);
        setStep('otp');
        setSuccess(result.message);
        startResendTimer();
        toast.success('OTP sent to your email!');
      } else {
        setError(result.message);
      }
    } catch (error: any) {
      setError('Failed to send OTP. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Handle mobile OTP login
  const handleMobileOTPLogin = async () => {
    if (!phoneNumber) {
      setError('Please enter your mobile number');
      return;
    }

    if (!MobileAuthService.validatePhoneNumber(phoneNumber)) {
      setError('Please enter a valid 10-digit mobile number');
      return;
    }

    setLoading(true);
    setError('');

    try {
      const result = await MobileAuthService.sendMobileOTP(phoneNumber, countryCode);
      
      if (result.success) {
        setOtpSent(true);
        setStep('otp');
        setSuccess(result.message);
        startResendTimer();
        toast.success('OTP sent to your mobile!');
      } else {
        setError(result.message);
      }
    } catch (error: any) {
      setError('Failed to send OTP. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Handle OTP verification
  const handleOTPVerification = async () => {
    if (!otp || otp.length !== 6) {
      setError('Please enter the 6-digit OTP');
      return;
    }

    setLoading(true);
    setError('');

    try {
      let result;
      
      if (loginMethod === 'email') {
        result = await MobileAuthService.verifyEmailOTP(email, otp);
      } else {
        result = await MobileAuthService.verifyMobileOTP(phoneNumber, otp, countryCode);
      }
      
      if (result.success) {
        setSuccess(result.message);
        toast.success(result.isNewUser ? 'Account created successfully!' : 'Welcome back!');
        onSuccess?.();
        router.push(redirectTo);
      } else {
        setError(result.message);
      }
    } catch (error: any) {
      setError('OTP verification failed. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Handle resend OTP
  const handleResendOTP = async () => {
    if (resendTimer > 0) return;

    setLoading(true);
    setError('');

    try {
      const result = await MobileAuthService.resendOTP(
        loginMethod === 'email' ? email : phoneNumber,
        loginMethod,
        countryCode
      );
      
      if (result.success) {
        setSuccess('OTP resent successfully!');
        startResendTimer();
        toast.success('OTP resent!');
      } else {
        setError(result.message);
      }
    } catch (error: any) {
      setError('Failed to resend OTP. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Reset form
  const resetForm = () => {
    setStep('input');
    setOtpSent(false);
    setOtp('');
    setError('');
    setSuccess('');
    setResendTimer(0);
  };

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader className="text-center">
        <CardTitle className="text-2xl font-bold text-gray-900">
          {step === 'otp' ? 'Verify OTP' : 'Welcome Back'}
        </CardTitle>
        <p className="text-gray-600">
          {step === 'otp' 
            ? `Enter the OTP sent to your ${loginMethod === 'email' ? 'email' : 'mobile'}`
            : 'Choose your preferred login method'
          }
        </p>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* Error/Success Messages */}
        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}
        
        {success && (
          <Alert className="border-green-200 bg-green-50">
            <CheckCircle className="h-4 w-4 text-green-600" />
            <AlertDescription className="text-green-800">{success}</AlertDescription>
          </Alert>
        )}

        {step === 'input' && (
          <Tabs value={loginMethod} onValueChange={(value) => setLoginMethod(value as 'email' | 'mobile')}>
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="email" className="flex items-center gap-2">
                <Mail className="w-4 h-4" />
                Email
              </TabsTrigger>
              <TabsTrigger value="mobile" className="flex items-center gap-2">
                <Phone className="w-4 h-4" />
                Mobile
              </TabsTrigger>
            </TabsList>

            {/* Email Login */}
            <TabsContent value="email" className="space-y-4">
              <div>
                <Label htmlFor="email">Email Address</Label>
                <Input
                  id="email"
                  type="email"
                  placeholder="Enter your email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className="mt-1"
                />
              </div>

              <div className="space-y-3">
                <Button
                  onClick={handleEmailOTPLogin}
                  disabled={loading}
                  className="w-full"
                >
                  {loading ? (
                    <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                  ) : (
                    <MessageSquare className="w-4 h-4 mr-2" />
                  )}
                  Login with Email OTP
                </Button>

                <div className="relative">
                  <div className="absolute inset-0 flex items-center">
                    <span className="w-full border-t" />
                  </div>
                  <div className="relative flex justify-center text-xs uppercase">
                    <span className="bg-white px-2 text-gray-500">Or</span>
                  </div>
                </div>

                <Button
                  variant="outline"
                  onClick={() => setStep('password')}
                  className="w-full"
                >
                  Login with Password
                </Button>
              </div>
            </TabsContent>

            {/* Mobile Login */}
            <TabsContent value="mobile" className="space-y-4">
              <div>
                <Label htmlFor="mobile">Mobile Number</Label>
                <div className="flex gap-2 mt-1">
                  <Select value={countryCode} onValueChange={setCountryCode}>
                    <SelectTrigger className="w-24">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {countryCodes.map((country) => (
                        <SelectItem key={country.code} value={country.code}>
                          {country.flag} {country.code}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <Input
                    id="mobile"
                    type="tel"
                    placeholder="Enter mobile number"
                    value={phoneNumber}
                    onChange={(e) => setPhoneNumber(e.target.value.replace(/\D/g, ''))}
                    maxLength={10}
                    className="flex-1"
                  />
                </div>
                {phoneNumber && (
                  <p className="text-xs text-gray-500 mt-1">
                    {countryCode} {MobileAuthService.formatPhoneNumber(phoneNumber)}
                  </p>
                )}
              </div>

              <Button
                onClick={handleMobileOTPLogin}
                disabled={loading}
                className="w-full"
              >
                {loading ? (
                  <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                ) : (
                  <MessageSquare className="w-4 h-4 mr-2" />
                )}
                Send OTP
              </Button>
            </TabsContent>
          </Tabs>
        )}

        {/* Password Login */}
        {step === 'password' && (
          <div className="space-y-4">
            <div>
              <Label htmlFor="password">Password</Label>
              <div className="relative mt-1">
                <Input
                  id="password"
                  type={showPassword ? 'text' : 'password'}
                  placeholder="Enter your password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className="pr-10"
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                >
                  {showPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                </button>
              </div>
            </div>

            <div className="space-y-3">
              <Button
                onClick={handleEmailLogin}
                disabled={loading}
                className="w-full"
              >
                {loading ? (
                  <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                ) : (
                  <ArrowRight className="w-4 h-4 mr-2" />
                )}
                Login
              </Button>

              <Button
                variant="outline"
                onClick={resetForm}
                className="w-full"
              >
                Back to Login Options
              </Button>
            </div>
          </div>
        )}

        {/* OTP Verification */}
        {step === 'otp' && (
          <div className="space-y-4">
            <div>
              <Label htmlFor="otp">Enter OTP</Label>
              <Input
                id="otp"
                type="text"
                placeholder="Enter 6-digit OTP"
                value={otp}
                onChange={(e) => setOtp(e.target.value.replace(/\D/g, '').slice(0, 6))}
                maxLength={6}
                className="mt-1 text-center text-lg tracking-widest"
              />
            </div>

            <Button
              onClick={handleOTPVerification}
              disabled={loading || otp.length !== 6}
              className="w-full"
            >
              {loading ? (
                <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
              ) : (
                <CheckCircle className="w-4 h-4 mr-2" />
              )}
              Verify OTP
            </Button>

            <div className="text-center space-y-2">
              <p className="text-sm text-gray-600">
                Didn't receive the OTP?
              </p>
              <Button
                variant="link"
                onClick={handleResendOTP}
                disabled={resendTimer > 0 || loading}
                className="text-sm"
              >
                {resendTimer > 0 ? (
                  <>
                    <Clock className="w-4 h-4 mr-1" />
                    Resend in {resendTimer}s
                  </>
                ) : (
                  <>
                    <RefreshCw className="w-4 h-4 mr-1" />
                    Resend OTP
                  </>
                )}
              </Button>
            </div>

            <Button
              variant="outline"
              onClick={resetForm}
              className="w-full"
            >
              Change Login Method
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
