"use client"

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { refreshCache, clearCache, getCacheInfo } from '@/lib/pwa-utils'
import { RefreshCw, Trash2, Info, X } from 'lucide-react'
import { showToast } from '@/lib/toast'

interface CacheInfo {
  caches: string[]
  totalSize: number
}

export function DevCacheManager() {
  const [isOpen, setIsOpen] = useState(false)
  const [cacheInfo, setCacheInfo] = useState<CacheInfo>({ caches: [], totalSize: 0 })
  const [loading, setLoading] = useState(false)

  // Only show in development
  if (process.env.NODE_ENV !== 'development') {
    return null
  }

  const loadCacheInfo = async () => {
    try {
      const info = await getCacheInfo()
      setCacheInfo(info)
    } catch (error) {
      console.error('Failed to load cache info:', error)
    }
  }

  const handleRefreshCache = async () => {
    setLoading(true)
    try {
      await refreshCache()
      await loadCacheInfo()
      showToast.success('Cache refreshed! The page will reload.')
      window.location.reload()
    } catch (error) {
      console.error('Failed to refresh cache:', error)
      showToast.error('Failed to refresh cache')
    } finally {
      setLoading(false)
    }
  }

  const handleClearCache = async () => {
    setLoading(true)
    try {
      await clearCache()
      await loadCacheInfo()
      showToast.success('Cache cleared!')
    } catch (error) {
      console.error('Failed to clear cache:', error)
      showToast.error('Failed to clear cache')
    } finally {
      setLoading(false)
    }
  }

  const formatBytes = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  useEffect(() => {
    if (isOpen) {
      loadCacheInfo()
    }
  }, [isOpen])

  return (
    <>
      {/* Floating button */}
      <Button
        onClick={() => setIsOpen(true)}
        className="fixed bottom-4 left-4 z-50 bg-orange-500 hover:bg-orange-600 text-white shadow-lg"
        size="sm"
      >
        <Info className="h-4 w-4 mr-2" />
        Cache
      </Button>

      {/* Modal */}
      {isOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
          <Card className="w-full max-w-md">
            <CardHeader className="flex flex-row items-center justify-between">
              <CardTitle>Cache Manager</CardTitle>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsOpen(false)}
              >
                <X className="h-4 w-4" />
              </Button>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Cache Info */}
              <div className="space-y-2">
                <h4 className="font-medium">Cache Status</h4>
                <div className="text-sm text-gray-600">
                  <p>Total Size: {formatBytes(cacheInfo.totalSize)}</p>
                  <p>Cache Count: {cacheInfo.caches.length}</p>
                </div>
                
                {cacheInfo.caches.length > 0 && (
                  <div className="space-y-1">
                    <p className="text-sm font-medium">Active Caches:</p>
                    {cacheInfo.caches.map((cacheName) => (
                      <Badge key={cacheName} variant="outline" className="text-xs">
                        {cacheName}
                      </Badge>
                    ))}
                  </div>
                )}
              </div>

              {/* Actions */}
              <div className="space-y-2">
                <Button
                  onClick={handleRefreshCache}
                  disabled={loading}
                  className="w-full"
                  variant="default"
                >
                  <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
                  Refresh Cache & Reload
                </Button>
                
                <Button
                  onClick={handleClearCache}
                  disabled={loading}
                  className="w-full"
                  variant="outline"
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  Clear Cache Only
                </Button>
              </div>

              {/* Instructions */}
              <div className="text-xs text-gray-500 space-y-1">
                <p><strong>Refresh Cache:</strong> Clears all caches and reloads the page</p>
                <p><strong>Clear Cache:</strong> Removes cached content but keeps the page</p>
                <p className="text-orange-600">⚠️ Development only - not visible in production</p>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </>
  )
}

export default DevCacheManager
