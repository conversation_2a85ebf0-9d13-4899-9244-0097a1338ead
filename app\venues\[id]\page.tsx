"use client"

import { useState, useEffect } from "react"
import { useParams } from "next/navigation"
import { TopHeader } from "@/components/top-header"
import { Header } from "@/components/header"
import { Footer } from "@/components/footer"
import { EntityReviews } from "@/components/entity-reviews"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Textarea } from "@/components/ui/textarea"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"

import { Star, MapPin, Phone, Mail, Users, Heart, Share2, Calendar, CheckCircle, Bed, Loader2, ArrowLeft, Facebook, Instagram, Youtube } from "lucide-react"
import Link from "next/link"

import { venueService, type VenueResponse } from "@/lib/services/venueService"
import { showToast, toastMessages } from '@/lib/toast'
import VenueInquiryForm from '@/components/VenueInquiryForm'
import AuthenticatedContactVenue from '@/components/AuthenticatedContactVenue'
import FavoriteButton from '@/components/FavoriteButton'
import AvailabilityChecker from '@/components/booking/AvailabilityChecker'
import { ClientOnly } from '@/components/client-only'
import { AuthenticatedLocation } from '@/components/AuthenticatedLocation'

export default function VenueDetailsPage() {
  const params = useParams()
  const venueId = params.id as string

  const [venue, setVenue] = useState<VenueResponse | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Availability checker state
  const [selectedDate, setSelectedDate] = useState('')
  const [selectedTime, setSelectedTime] = useState('')
  const [isAvailable, setIsAvailable] = useState<boolean | null>(null)
  const [shareClicked, setShareClicked] = useState(false)
  const [isShared, setIsShared] = useState(false)
  // Load venue data on component mount
  useEffect(() => {
    if (venueId) {
      loadVenue()
    }
  }, [venueId])

  const loadVenue = async () => {
    try {
      setLoading(true)
      setError(null)
      console.log('Loading venue with ID:', venueId)

      // Add timeout to prevent infinite loading
      const timeoutPromise = new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Request timeout')), 10000)
      );

      // Try to load venue data with timeout
      let venueData;
      try {
        venueData = await Promise.race([
          venueService.getVenue(venueId),
          timeoutPromise
        ]);
        console.log('Venue data received:', venueData)
      } catch (serviceError) {
        console.warn('VenueService failed, using mock data:', serviceError)
        // Fallback to mock data if service fails
        venueData = {
          id: venueId,
          name: 'Sample Wedding Venue',
          description: 'A beautiful venue for your special day.',
          location: 'Sample Location',
          city: 'Chennai',
          state: 'Tamil Nadu',
          capacity: 500,
          price: '₹50,000',
          rating: 4.5,
          reviewCount: 25,
          images: ['/placeholder-venue.jpg'],
          amenities: ['Parking', 'AC', 'Catering'],
          venueType: 'Banquet Hall',
          contact: '+91 8148376909',
          email: '<EMAIL>',
          website: 'https://samplevenue.com'
        }
      }

      setVenue(venueData)
    } catch (err) {
      console.error('Error loading venue:', err)
      setError(`Failed to load venue details: ${err.message}`)
    } finally {
      setLoading(false)
    }
  }

  const handleAvailabilityChange = (available: boolean, conflicts?: any[]) => {
    setIsAvailable(available)
    console.log('Venue availability changed:', { available, conflicts })
  }


  // Handle share
  const handleShare = async () => {
    setShareClicked(true)
    setIsShared(true)
    try {
      if (navigator.share) {
        await navigator.share({
          title: venue?.name || 'Check out this venue',
          text: `Look at this amazing venue: ${venue?.name}`,
          url: window.location.href,
        })
      } else {
        // Fallback: copy to clipboard
        await navigator.clipboard.writeText(window.location.href)
        showToast.success('Link copied to clipboard!')
      }
    } catch (error) {
      console.error('Error sharing:', error)
      showToast.error('Failed to share')
    } finally {
      setTimeout(() => setIsShared(false), 1000)
      setTimeout(() => setShareClicked(false), 250)
    }
  }
  // Animation classes for creative effect
  const shareAnimClass = shareClicked ? 'scale-125 -rotate-6 shadow-lg' : '';

  // Loading state
  if (loading) {
    return (
      <div className="min-h-screen bg-white">
        <TopHeader />
        <Header />
        <div className="flex justify-center items-center py-20">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <span className="ml-2 text-gray-600">Loading venue details...</span>
        </div>
        <Footer />
      </div>
    )
  }

  // Error state
  if (error || !venue) {
    return (
      <div className="min-h-screen bg-white">
        <TopHeader />
        <Header />
        <div className="text-center py-20">
          <p className="text-red-600 mb-4">{error || 'Venue not found'}</p>
          <Button onClick={loadVenue} className="bg-primary hover:bg-primary/90">
            Try Again
          </Button>
        </div>
        <Footer />
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-white">
      <TopHeader />
      <Header />

      {/* Hero Section with Image Gallery */}
      <section className="relative w-full">
        <div className="h-64 sm:h-80 md:h-96 w-full relative overflow-hidden">
          {venue.images && venue.images.length > 0 ? (
            <img
              src={venue.images[0]}
              alt={venue.name}
              className="absolute inset-0 w-full h-full object-cover object-center"
            />
          ) : (
            <div className="absolute inset-0 flex items-center justify-center bg-gradient-to-br from-primary/20 to-primary/10">
              <div className="text-center">
                <div className="h-16 w-16 sm:h-20 sm:w-20 md:h-24 md:w-24 mx-auto mb-4 bg-white/20 rounded-full flex items-center justify-center">
                  <span className="text-2xl sm:text-3xl md:text-4xl">🏛️</span>
                </div>
                <p className="text-white/60 text-sm sm:text-base">No image available</p>
              </div>
            </div>
          )}
          <div className="absolute inset-0 bg-black/30"></div>
        </div>
      </section>

      {/* Venue Info Section - Below the hero image */}
      <section className="bg-white border-b">
        <div className="container mx-auto px-4 py-4 sm:py-6">
          <div className="space-y-3">
            <div>
              <h1 className="text-2xl sm:text-3xl md:text-4xl font-bold text-gray-900">{venue.name}</h1>
              <p className="text-lg sm:text-xl text-gray-600">{venue.venueType || venue.type}</p>
            </div>
            <div className="flex items-center text-gray-600">
              <MapPin className="h-4 w-4 sm:h-5 sm:w-5 mr-2 text-primary" />
              <span className="text-base sm:text-lg">{venue.city}, {venue.state}</span>
            </div>
            <div className="block">
              <div className="text-xl sm:text-2xl md:text-3xl font-bold text-primary mb-1">
                {venue.priceRange || venue.price || "Contact for pricing"}
              </div>
              <div className="flex items-center gap-2">
                <Star className="h-4 w-4 sm:h-5 sm:w-5 fill-yellow-400 text-yellow-400" />
                <span className="text-base sm:text-lg font-semibold text-gray-900">{venue.rating || "N/A"}</span>
                <span className="text-gray-600 text-sm sm:text-base">({venue.reviewCount || 0} reviews)</span>
              </div>
            </div>
            {venue.capacity && (
              <div className="flex items-center text-gray-600">
                <Users className="h-4 w-4 sm:h-5 sm:w-5 mr-2 text-primary" />
                <span className="text-base sm:text-lg">Capacity: {venue.capacity} people</span>
              </div>
            )}
          </div>
        </div>
      </section>

      {/* Main Content */}
      <section className="py-4 sm:py-6 md:py-8">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 sm:gap-6 md:gap-8">
            {/* Main Content */}
            <div className="lg:col-span-2">
              {/* About Section - Moved to Top */}
              <Card className="mb-4 sm:mb-6">
                <CardContent className="p-4 sm:p-6">
                  <div className="flex flex-col gap-4 sm:gap-6">
                    <div className="flex-1">
                      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 sm:gap-0">
                        <h3 className="text-lg sm:text-xl font-semibold">About {venue.name}</h3>

                        {/* Like, Share & Social Media Icons */}
                        <div className="flex gap-2 sm:gap-3 justify-center sm:justify-end">
                          {/* Like and Share Icons */}
                          <FavoriteButton
                            entityId={venue.id}
                            entityType="VENUE"
                            entityData={{
                              name: venue.name,
                              image: venue.images?.[0],
                              price: venue.price,
                              location: venue.location,
                              city: venue.city,
                              state: venue.state,
                              rating: venue.rating,
                              reviewCount: venue.reviewCount,
                              description: venue.description
                            }}
                            variant="outline"
                            size="icon"
                            className="rounded-full h-8 w-8 sm:h-10 sm:w-10"
                          />
                          <Button 
                            size="sm" 
                            variant="secondary" 
                            className={`bg-white/90 hover:bg-white transition-all duration-300 ${
                              isShared ? 'text-[#8B0000]' : 'text-[#CD5C5C]'
                            } ${shareAnimClass}`}
                            onClick={handleShare}
                          >
                            <Share2 className={`h-10 w-10 ${isShared ? 'fill-current' : ''} transition-transform duration-300`} />
                          </Button>

                          {/* Social Media Icons */}
                          {venue.socialMedia && (
                            <>
                              {venue.socialMedia.facebook && (
                                <Button
                                  size="icon"
                                  variant="outline"
                                  className="rounded-full h-8 w-8 sm:h-10 sm:w-10 hover:bg-blue-600 hover:text-white hover:border-blue-600 transition-all duration-200"
                                  onClick={() => window.open(venue.socialMedia.facebook, '_blank')}
                                >
                                  <Facebook className="h-4 w-4" />
                                </Button>
                              )}
                              {venue.socialMedia.instagram && (
                                <Button
                                  size="icon"
                                  variant="outline"
                                  className="rounded-full h-8 w-8 sm:h-10 sm:w-10 hover:bg-pink-600 hover:text-white hover:border-pink-600 transition-all duration-200"
                                  onClick={() => window.open(venue.socialMedia.instagram, '_blank')}
                                >
                                  <Instagram className="h-4 w-4" />
                                </Button>
                              )}
                              {venue.socialMedia.youtube && (
                                <Button
                                  size="icon"
                                  variant="outline"
                                  className="rounded-full h-8 w-8 sm:h-10 sm:w-10 hover:bg-red-600 hover:text-white hover:border-red-600 transition-all duration-200"
                                  onClick={() => window.open(venue.socialMedia.youtube, '_blank')}
                                >
                                  <Youtube className="h-4 w-4" />
                                </Button>
                              )}
                            </>
                          )}
                        </div>
                      </div>
                      <p className="text-gray-700 leading-relaxed text-sm sm:text-base mt-4">
                        {venue.description || "No description available."}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Availability Checker */}
              <Card>
                <CardContent className="p-6">
                  <h3 className="text-xl font-semibold mb-4 flex items-center gap-2">
                    <Calendar className="h-5 w-5 text-primary" />
                    Check Availability
                  </h3>
                  <p className="text-gray-600 mb-6">
                    Select your preferred date and time to check if {venue.name} is available for your event.
                  </p>

                  <ClientOnly fallback={
                    <div className="p-4 border border-gray-200 rounded-lg bg-gray-50">
                      <p className="text-gray-600">Loading availability checker...</p>
                    </div>
                  }>
                    <AvailabilityChecker
                      entityId={venue.id}
                      entityType="VENUE"
                      entityName={venue.name}
                      selectedDate={selectedDate}
                      selectedTime={selectedTime}
                      duration="8 hours"
                      onAvailabilityChange={handleAvailabilityChange}
                    />
                  </ClientOnly>
                </CardContent>
              </Card>

              <Tabs defaultValue="gallery" className="w-full">
                <TabsList className="grid w-full grid-cols-2 sm:grid-cols-5 mb-4 sm:mb-8 h-auto">
                  <TabsTrigger value="gallery" className="text-xs sm:text-sm py-2">Gallery</TabsTrigger>
                  <TabsTrigger value="spaces" className="text-xs sm:text-sm py-2">Spaces</TabsTrigger>
                  <TabsTrigger value="amenities" className="text-xs sm:text-sm py-2">Amenities</TabsTrigger>
                  <TabsTrigger value="packages" className="text-xs sm:text-sm py-2">Packages</TabsTrigger>
                  <TabsTrigger value="reviews" className="text-xs sm:text-sm py-2">Reviews</TabsTrigger>
                </TabsList>

                <TabsContent value="gallery" className="space-y-4 sm:space-y-6">
                  <Card>
                    <CardContent className="p-4 sm:p-6">
                      <h3 className="text-lg sm:text-xl font-semibold mb-4 sm:mb-6">Photo Gallery</h3>
                      {venue.images && venue.images.length > 0 ? (
                        <div className="grid grid-cols-2 sm:grid-cols-3 gap-3 sm:gap-4">
                          {venue.images.map((image, index) => (
                            <div key={index} className="relative aspect-square rounded-xl overflow-hidden group cursor-pointer">
                              <img
                                src={image || "/placeholder.svg"}
                                alt={`${venue.name} - Image ${index + 1}`}
                                className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-110"
                              />
                              <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors duration-300"></div>
                            </div>
                          ))}
                        </div>
                      ) : (
                        <div className="text-center py-8 sm:py-12">
                          <div className="h-12 w-12 sm:h-16 sm:w-16 text-gray-300 mx-auto mb-4">🏛️</div>
                          <p className="text-gray-500 text-sm sm:text-base">No photos available</p>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                </TabsContent>

                <TabsContent value="spaces" className="space-y-4 sm:space-y-6">
                  <Card>
                    <CardContent className="p-4 sm:p-6">
                      <h3 className="text-lg sm:text-xl font-semibold mb-4 sm:mb-6">Available Spaces</h3>
                      {venue.spaces && venue.spaces.length > 0 ? (
                        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4">
                          {venue.spaces.map((space, index) => (
                            <Card key={index} className="border-2 hover:border-accent transition-colors">
                              <CardContent className="p-4 sm:p-6">
                                <h3 className="text-lg sm:text-xl font-bold mb-2">{space.name}</h3>
                                <p className="text-xl sm:text-2xl font-bold text-primary mb-2">{space.price}</p>
                                <div className="space-y-2 text-xs sm:text-sm text-gray-600">
                                  <div className="flex items-center">
                                    <Users className="h-4 w-4 mr-2" />
                                    {space.capacity}
                                  </div>
                                  {space.area && (
                                    <div className="flex items-center">
                                      <span className="w-4 h-4 mr-2 text-center">📐</span>
                                      {space.area}
                                    </div>
                                  )}
                                  {space.description && (
                                    <p className="text-gray-600 mt-2 text-xs sm:text-sm">{space.description}</p>
                                  )}
                                </div>
                                <Button className="w-full mt-4 bg-primary hover:bg-primary/90 text-xs sm:text-sm">Book This Space</Button>
                              </CardContent>
                            </Card>
                          ))}
                        </div>
                      ) : (
                        <div className="text-center py-8 sm:py-12">
                          <p className="text-gray-500 text-sm sm:text-base">No space information available for this venue.</p>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                </TabsContent>

                <TabsContent value="amenities" className="space-y-4 sm:space-y-6">
                  <Card>
                    <CardContent className="p-4 sm:p-6">
                      <h3 className="text-lg sm:text-xl font-semibold mb-4 sm:mb-6">Amenities</h3>
                      {venue.amenities && venue.amenities.length > 0 ? (
                        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-3 sm:gap-4">
                          {venue.amenities.map((amenity, index) => (
                            <div key={index} className="flex items-center p-3 sm:p-4 bg-gray-50 rounded-lg">
                              <CheckCircle className="h-4 w-4 sm:h-5 sm:w-5 text-green-600 mr-2 sm:mr-3" />
                              <span className="text-xs sm:text-sm font-medium">{amenity}</span>
                            </div>
                          ))}
                        </div>
                      ) : (
                        <div className="text-center py-8 sm:py-12">
                          <p className="text-gray-500 text-sm sm:text-base">No amenities information available for this venue.</p>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                </TabsContent>

                <TabsContent value="packages" className="space-y-4 sm:space-y-6">
                  <Card>
                    <CardContent className="p-4 sm:p-6">
                      <h3 className="text-lg sm:text-xl font-semibold mb-4 sm:mb-6">Packages & Pricing</h3>
                      {venue.packages && venue.packages.length > 0 ? (
                        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4">
                          {venue.packages.map((pkg, index) => (
                            <Card key={index} className="border-2 hover:border-accent transition-colors">
                              <CardContent className="p-4 sm:p-6">
                                <h3 className="text-lg sm:text-xl font-bold mb-2">{pkg.name}</h3>
                                <p className="text-2xl sm:text-3xl font-bold text-primary mb-2">{pkg.price}</p>
                                {pkg.duration && <p className="text-gray-600 mb-4 text-xs sm:text-sm">{pkg.duration}</p>}
                                {pkg.description && <p className="text-gray-600 mb-4 text-xs sm:text-sm">{pkg.description}</p>}
                                {pkg.includes && pkg.includes.length > 0 && (
                                  <div className="mb-4">
                                    <h4 className="font-semibold mb-2 text-sm sm:text-base">Includes:</h4>
                                    <ul className="space-y-2">
                                      {pkg.includes.map((feature, idx) => (
                                        <li key={idx} className="flex items-center text-xs sm:text-sm">
                                          <CheckCircle className="h-4 w-4 text-green-600 mr-2" />
                                          {feature}
                                        </li>
                                      ))}
                                    </ul>
                                  </div>
                                )}
                                {pkg.excludes && pkg.excludes.length > 0 && (
                                  <div className="mb-4">
                                    <h4 className="font-semibold mb-2 text-sm sm:text-base">Excludes:</h4>
                                    <ul className="space-y-2">
                                      {pkg.excludes.map((feature, idx) => (
                                        <li key={idx} className="flex items-center text-xs sm:text-sm text-red-600">
                                          <span className="h-4 w-4 mr-2">✗</span>
                                          {feature}
                                        </li>
                                      ))}
                                    </ul>
                                  </div>
                                )}
                                {pkg.terms && (
                                  <p className="text-xs text-gray-500 mb-4">{pkg.terms}</p>
                                )}
                                <Button className="w-full mt-4 bg-primary hover:bg-primary/90 text-xs sm:text-sm">Select Package</Button>
                              </CardContent>
                            </Card>
                          ))}
                        </div>
                      ) : (
                        <div className="text-center py-8 sm:py-12">
                          <p className="text-gray-500 text-sm sm:text-base">No packages available for this venue.</p>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                </TabsContent>

                <TabsContent value="reviews" className="space-y-4 sm:space-y-6">
                  <EntityReviews
                    entityType="VENUE"
                    entityId={venue.id}
                    entityName={venue.name}
                  />
                </TabsContent>
              </Tabs>
            </div>

            {/* Sidebar */}
            <div className="lg:col-span-1">
              <div className="sticky top-8 space-y-4 sm:space-y-6">
                {/* Authenticated Contact Venue Component */}
                <AuthenticatedContactVenue
                  venue={{
                    id: venue.id,
                    userId: venue.userId,
                    name: venue.name,
                    type: venue.type,
                    capacity: venue.capacity,
                    contactPhone: venue.contactPhone,
                    contactEmail: venue.contactEmail,
                    website: venue.website,
                    rating: venue.rating,
                    reviewCount: venue.reviewCount,
                    priceRange: venue.priceRange,
                    city: venue.city,
                    state: venue.state
                  }}
                  showInquiryForm={true}
                />

                {/* Location */}
                <AuthenticatedLocation
                  entity={{
                    id: venue.id,
                    name: venue.name,
                    location: venue.location,
                    city: venue.city,
                    state: venue.state,
                    pincode: venue.pincode,
                    fullAddress: venue.fullAddress
                  }}
                  entityType="VENUE"
                />

              </div>
            </div>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  )
}
