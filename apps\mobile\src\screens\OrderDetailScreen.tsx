import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Image,
  Alert,
  Linking,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { StackNavigationProp } from '@react-navigation/stack';
import { RouteProp } from '@react-navigation/native';
import { AppStackParamList } from '../navigation/AppNavigator';
import { useTheme } from '../providers/ThemeProvider';
import LoadingSpinner from '../components/LoadingSpinner';

type OrderDetailScreenNavigationProp = StackNavigationProp<AppStackParamList, 'OrderDetail'>;
type OrderDetailScreenRouteProp = RouteProp<AppStackParamList, 'OrderDetail'>;

interface Props {
  navigation: OrderDetailScreenNavigationProp;
  route: OrderDetailScreenRouteProp;
}

interface OrderItem {
  id: string;
  name: string;
  price: number;
  quantity: number;
  image?: string;
}

interface Order {
  id: string;
  items: OrderItem[];
  total: number;
  status: 'pending' | 'confirmed' | 'processing' | 'shipped' | 'delivered' | 'cancelled';
  createdAt: string;
  deliveryAddress: {
    fullName: string;
    addressLine1: string;
    addressLine2?: string;
    city: string;
    state: string;
    pincode: string;
  };
  paymentMethod: string;
  transactionId?: string;
  trackingNumber?: string;
  estimatedDelivery?: string;
  deliveryCharge: number;
  tax: number;
  subtotal: number;
}

export default function OrderDetailScreen({ navigation, route }: Props) {
  const { theme } = useTheme();
  const { orderId } = route.params;
  const [order, setOrder] = useState<Order | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadOrderDetails();
  }, [orderId]);

  const loadOrderDetails = async () => {
    try {
      setLoading(true);
      
      // TODO: Replace with actual GraphQL query
      // const orderData = await graphqlService.getOrder(orderId);
      
      // Mock data for now
      const mockOrder: Order = {
        id: orderId,
        items: [
          {
            id: '1',
            name: 'Elegant Wedding Lehenga',
            price: 25000,
            quantity: 1,
            image: 'https://example.com/lehenga.jpg',
          },
          {
            id: '2',
            name: 'Bridal Jewelry Set',
            price: 15000,
            quantity: 1,
            image: 'https://example.com/jewelry.jpg',
          },
        ],
        subtotal: 40000,
        deliveryCharge: 0,
        tax: 7200,
        total: 47200,
        status: 'shipped',
        createdAt: '2024-01-15T10:30:00Z',
        deliveryAddress: {
          fullName: 'Priya Sharma',
          addressLine1: '123 MG Road',
          addressLine2: 'Near City Mall',
          city: 'Bangalore',
          state: 'Karnataka',
          pincode: '560001',
        },
        paymentMethod: 'card',
        transactionId: 'TXN_1234567890',
        trackingNumber: 'TRK123456789',
        estimatedDelivery: '2024-01-25',
      };
      
      setOrder(mockOrder);
    } catch (error) {
      console.error('Failed to load order details:', error);
      Alert.alert('Error', 'Failed to load order details');
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: Order['status']) => {
    switch (status) {
      case 'pending':
        return theme.colors.warning;
      case 'confirmed':
      case 'processing':
        return theme.colors.info;
      case 'shipped':
        return theme.colors.primary;
      case 'delivered':
        return theme.colors.success;
      case 'cancelled':
        return theme.colors.error;
      default:
        return theme.colors.textSecondary;
    }
  };

  const getStatusText = (status: Order['status']) => {
    switch (status) {
      case 'pending':
        return 'Order Pending';
      case 'confirmed':
        return 'Order Confirmed';
      case 'processing':
        return 'Processing';
      case 'shipped':
        return 'Shipped';
      case 'delivered':
        return 'Delivered';
      case 'cancelled':
        return 'Cancelled';
      default:
        return status;
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-IN', {
      day: 'numeric',
      month: 'long',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const handleTrackOrder = () => {
    if (order?.trackingNumber) {
      // In a real app, this would open the courier's tracking page
      Alert.alert(
        'Track Order',
        `Tracking Number: ${order.trackingNumber}\n\nThis would typically open the courier's tracking page.`,
        [
          { text: 'Copy Tracking Number', onPress: () => {
            // Copy to clipboard functionality would go here
            Alert.alert('Copied', 'Tracking number copied to clipboard');
          }},
          { text: 'OK' },
        ]
      );
    }
  };

  const handleCancelOrder = () => {
    if (order?.status === 'pending' || order?.status === 'confirmed') {
      Alert.alert(
        'Cancel Order',
        'Are you sure you want to cancel this order?',
        [
          { text: 'No', style: 'cancel' },
          {
            text: 'Yes, Cancel',
            style: 'destructive',
            onPress: async () => {
              // TODO: Implement order cancellation
              Alert.alert('Order Cancelled', 'Your order has been cancelled successfully.');
            },
          },
        ]
      );
    }
  };

  const handleContactSupport = () => {
    Alert.alert(
      'Contact Support',
      'How would you like to contact our support team?',
      [
        { text: 'Call', onPress: () => Linking.openURL('tel:+911234567890') },
        { text: 'Email', onPress: () => Linking.openURL('mailto:<EMAIL>') },
        { text: 'Cancel', style: 'cancel' },
      ]
    );
  };

  if (loading) {
    return <LoadingSpinner fullScreen text="Loading order details..." />;
  }

  if (!order) {
    return (
      <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
        <View style={styles.errorContainer}>
          <Ionicons name="alert-circle-outline" size={48} color={theme.colors.error} />
          <Text style={[styles.errorText, { color: theme.colors.text }]}>
            Order not found
          </Text>
          <TouchableOpacity
            style={[styles.retryButton, { backgroundColor: theme.colors.primary }]}
            onPress={loadOrderDetails}
          >
            <Text style={styles.retryButtonText}>Retry</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  }

  return (
    <ScrollView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {/* Order Status */}
      <View style={[styles.section, { backgroundColor: theme.colors.surface }]}>
        <View style={styles.statusHeader}>
          <View>
            <Text style={[styles.orderId, { color: theme.colors.text }]}>
              Order #{order.id}
            </Text>
            <Text style={[styles.orderDate, { color: theme.colors.textSecondary }]}>
              Placed on {formatDate(order.createdAt)}
            </Text>
          </View>
          
          <View style={[styles.statusBadge, { backgroundColor: getStatusColor(order.status) + '20' }]}>
            <Text style={[styles.statusText, { color: getStatusColor(order.status) }]}>
              {getStatusText(order.status)}
            </Text>
          </View>
        </View>

        {order.trackingNumber && (
          <TouchableOpacity style={styles.trackingContainer} onPress={handleTrackOrder}>
            <Ionicons name="location-outline" size={20} color={theme.colors.primary} />
            <Text style={[styles.trackingText, { color: theme.colors.primary }]}>
              Track Order: {order.trackingNumber}
            </Text>
            <Ionicons name="chevron-forward" size={16} color={theme.colors.primary} />
          </TouchableOpacity>
        )}

        {order.estimatedDelivery && (
          <View style={styles.deliveryInfo}>
            <Ionicons name="time-outline" size={16} color={theme.colors.textSecondary} />
            <Text style={[styles.deliveryText, { color: theme.colors.textSecondary }]}>
              Estimated delivery: {new Date(order.estimatedDelivery).toLocaleDateString('en-IN')}
            </Text>
          </View>
        )}
      </View>

      {/* Order Items */}
      <View style={[styles.section, { backgroundColor: theme.colors.surface }]}>
        <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
          Items ({order.items.length})
        </Text>
        
        {order.items.map((item) => (
          <View key={item.id} style={styles.orderItem}>
            {item.image ? (
              <Image source={{ uri: item.image }} style={styles.itemImage} />
            ) : (
              <View style={[styles.itemImagePlaceholder, { backgroundColor: theme.colors.border }]}>
                <Ionicons name="image-outline" size={24} color={theme.colors.textSecondary} />
              </View>
            )}
            
            <View style={styles.itemDetails}>
              <Text style={[styles.itemName, { color: theme.colors.text }]} numberOfLines={2}>
                {item.name}
              </Text>
              <Text style={[styles.itemPrice, { color: theme.colors.textSecondary }]}>
                ₹{item.price.toLocaleString()} × {item.quantity}
              </Text>
            </View>
            
            <Text style={[styles.itemTotal, { color: theme.colors.text }]}>
              ₹{(item.price * item.quantity).toLocaleString()}
            </Text>
          </View>
        ))}
      </View>

      {/* Delivery Address */}
      <View style={[styles.section, { backgroundColor: theme.colors.surface }]}>
        <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
          Delivery Address
        </Text>

        <View style={styles.addressContainer}>
          <Text style={[styles.addressName, { color: theme.colors.text }]}>
            {order.deliveryAddress.fullName}
          </Text>
          <Text style={[styles.addressText, { color: theme.colors.textSecondary }]}>
            {order.deliveryAddress.addressLine1}
          </Text>
          {order.deliveryAddress.addressLine2 && (
            <Text style={[styles.addressText, { color: theme.colors.textSecondary }]}>
              {order.deliveryAddress.addressLine2}
            </Text>
          )}
          <Text style={[styles.addressText, { color: theme.colors.textSecondary }]}>
            {order.deliveryAddress.city}, {order.deliveryAddress.state} - {order.deliveryAddress.pincode}
          </Text>
        </View>
      </View>

      {/* Payment Details */}
      <View style={[styles.section, { backgroundColor: theme.colors.surface }]}>
        <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
          Payment Details
        </Text>

        <View style={styles.paymentRow}>
          <Text style={[styles.paymentLabel, { color: theme.colors.textSecondary }]}>
            Payment Method
          </Text>
          <Text style={[styles.paymentValue, { color: theme.colors.text }]}>
            {order.paymentMethod.toUpperCase()}
          </Text>
        </View>

        {order.transactionId && (
          <View style={styles.paymentRow}>
            <Text style={[styles.paymentLabel, { color: theme.colors.textSecondary }]}>
              Transaction ID
            </Text>
            <Text style={[styles.paymentValue, { color: theme.colors.text }]}>
              {order.transactionId}
            </Text>
          </View>
        )}
      </View>

      {/* Price Breakdown */}
      <View style={[styles.section, { backgroundColor: theme.colors.surface }]}>
        <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
          Price Details
        </Text>

        <View style={styles.priceRow}>
          <Text style={[styles.priceLabel, { color: theme.colors.text }]}>
            Subtotal ({order.items.length} items)
          </Text>
          <Text style={[styles.priceValue, { color: theme.colors.text }]}>
            ₹{order.subtotal.toLocaleString()}
          </Text>
        </View>

        <View style={styles.priceRow}>
          <Text style={[styles.priceLabel, { color: theme.colors.text }]}>
            Delivery Charges
          </Text>
          <Text style={[styles.priceValue, { color: order.deliveryCharge === 0 ? theme.colors.success : theme.colors.text }]}>
            {order.deliveryCharge === 0 ? 'FREE' : `₹${order.deliveryCharge}`}
          </Text>
        </View>

        <View style={styles.priceRow}>
          <Text style={[styles.priceLabel, { color: theme.colors.text }]}>
            Tax (GST 18%)
          </Text>
          <Text style={[styles.priceValue, { color: theme.colors.text }]}>
            ₹{order.tax.toLocaleString()}
          </Text>
        </View>

        <View style={[styles.priceRow, styles.totalRow]}>
          <Text style={[styles.totalLabel, { color: theme.colors.text }]}>
            Total Amount
          </Text>
          <Text style={[styles.totalValue, { color: theme.colors.primary }]}>
            ₹{order.total.toLocaleString()}
          </Text>
        </View>
      </View>

      {/* Action Buttons */}
      <View style={styles.actionButtons}>
        {(order.status === 'pending' || order.status === 'confirmed') && (
          <TouchableOpacity
            style={[styles.actionButton, styles.cancelButton, { borderColor: theme.colors.error }]}
            onPress={handleCancelOrder}
          >
            <Text style={[styles.cancelButtonText, { color: theme.colors.error }]}>
              Cancel Order
            </Text>
          </TouchableOpacity>
        )}

        <TouchableOpacity
          style={[styles.actionButton, styles.supportButton, { backgroundColor: theme.colors.primary }]}
          onPress={handleContactSupport}
        >
          <Ionicons name="headset-outline" size={20} color="white" />
          <Text style={styles.supportButtonText}>Contact Support</Text>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  section: {
    margin: 16,
    padding: 16,
    borderRadius: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
  },
  errorText: {
    fontSize: 18,
    fontWeight: '600',
    marginTop: 16,
    marginBottom: 24,
  },
  retryButton: {
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  retryButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  statusHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 16,
  },
  orderId: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  orderDate: {
    fontSize: 14,
  },
  statusBadge: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '600',
  },
  trackingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    backgroundColor: 'rgba(0, 0, 0, 0.05)',
    borderRadius: 8,
    marginBottom: 12,
  },
  trackingText: {
    flex: 1,
    fontSize: 14,
    fontWeight: '500',
    marginLeft: 8,
  },
  deliveryInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  deliveryText: {
    fontSize: 14,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 16,
  },
  orderItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  itemImage: {
    width: 60,
    height: 60,
    borderRadius: 8,
    marginRight: 12,
  },
  itemImagePlaceholder: {
    width: 60,
    height: 60,
    borderRadius: 8,
    marginRight: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  itemDetails: {
    flex: 1,
  },
  itemName: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 4,
  },
  itemPrice: {
    fontSize: 12,
  },
  itemTotal: {
    fontSize: 14,
    fontWeight: 'bold',
  },
  addressContainer: {
    gap: 4,
  },
  addressName: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
  },
  addressText: {
    fontSize: 14,
    lineHeight: 20,
  },
  paymentRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  paymentLabel: {
    fontSize: 14,
  },
  paymentValue: {
    fontSize: 14,
    fontWeight: '500',
  },
  priceRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  priceLabel: {
    fontSize: 14,
  },
  priceValue: {
    fontSize: 14,
    fontWeight: '500',
  },
  totalRow: {
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
    paddingTop: 12,
    marginTop: 8,
  },
  totalLabel: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  totalValue: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  actionButtons: {
    flexDirection: 'row',
    gap: 12,
    padding: 16,
  },
  actionButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    gap: 8,
  },
  cancelButton: {
    borderWidth: 1,
    backgroundColor: 'transparent',
  },
  cancelButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
  supportButton: {
    // backgroundColor set dynamically
  },
  supportButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
});
