const fs = require('fs');
const path = require('path');

// Cart translations for different languages
const cartTranslations = {
  ta: {
    "title": "வணிக வண்டி",
    "items": "பொருட்கள்",
    "item": "பொருள்",
    "total": "மொத்தம்",
    "subtotal": "துணை மொத்தம்",
    "checkout": "பணம் செலுத்த தொடரவும்",
    "continueShopping": "வாங்குதலை தொடரவும்",
    "clearCart": "வண்டியை காலி செய்யவும்",
    "emptyCart": "உங்கள் வண்டி காலியாக உள்ளது",
    "emptyCartMessage": "நீங்கள் இன்னும் உங்கள் வண்டியில் எந்த பொருட்களையும் சேர்க்கவில்லை போல் தெரிகிறது.",
    "startShopping": "வாங்குதலை தொடங்கவும்",
    "addToCart": "வண்டியில் சேர்க்கவும்",
    "removeItem": "பொருளை அகற்றவும்",
    "updateQuantity": "அளவை புதுப்பிக்கவும்",
    "saveForLater": "பின்னர் சேமிக்கவும்",
    "moveToCart": "வண்டிக்கு நகர்த்தவும்",
    "savedForLater": "பின்னர் சேமிக்கப்பட்டது",
    "guestCart": "விருந்தினர் வண்டி",
    "loginToCheckout": "பணம் செலுத்த உள்நுழையவும்",
    "orderSummary": "ஆர்டர் சுருக்கம்",
    "promoCode": "விளம்பர குறியீடு",
    "enterCode": "குறியீட்டை உள்ளிடவும்",
    "apply": "பயன்படுத்தவும்",
    "shipping": "அனுப்புதல்",
    "discount": "தள்ளுபடி",
    "free": "இலவசம்",
    "secure": "பாதுகாப்பான",
    "freeShip": "இலவச அனுப்புதல்",
    "giftWrap": "பரிசு போர்வை",
    "bestPrice": "சிறந்த விலை"
  },
  hi: {
    "title": "शॉपिंग कार्ट",
    "items": "आइटम",
    "item": "आइटम",
    "total": "कुल",
    "subtotal": "उप-योग",
    "checkout": "चेकआउट करें",
    "continueShopping": "खरीदारी जारी रखें",
    "clearCart": "कार्ट साफ़ करें",
    "emptyCart": "आपका कार्ट खाली है",
    "emptyCartMessage": "लगता है आपने अभी तक अपने कार्ट में कोई आइटम नहीं जोड़ा है।",
    "startShopping": "खरीदारी शुरू करें",
    "addToCart": "कार्ट में जोड़ें",
    "removeItem": "आइटम हटाएं",
    "updateQuantity": "मात्रा अपडेट करें",
    "saveForLater": "बाद के लिए सेव करें",
    "moveToCart": "कार्ट में ले जाएं",
    "savedForLater": "बाद के लिए सेव किया गया",
    "guestCart": "गेस्ट कार्ट",
    "loginToCheckout": "चेकआउट के लिए लॉगिन करें",
    "orderSummary": "ऑर्डर सारांश",
    "promoCode": "प्रोमो कोड",
    "enterCode": "कोड दर्ज करें",
    "apply": "लागू करें",
    "shipping": "शिपिंग",
    "discount": "छूट",
    "free": "मुफ्त",
    "secure": "सुरक्षित",
    "freeShip": "मुफ्त शिपिंग",
    "giftWrap": "गिफ्ट रैप",
    "bestPrice": "बेस्ट प्राइस"
  },
  te: {
    "title": "షాపింగ్ కార్ట్",
    "items": "వస్తువులు",
    "item": "వస్తువు",
    "total": "మొత్తం",
    "subtotal": "ఉప మొత్తం",
    "checkout": "చెక్అవుట్ కొనసాగించండి",
    "continueShopping": "షాపింగ్ కొనసాగించండి",
    "clearCart": "కార్ట్ క్లియర్ చేయండి",
    "emptyCart": "మీ కార్ట్ ఖాళీగా ఉంది",
    "emptyCartMessage": "మీరు ఇంకా మీ కార్ట్‌లో ఏ వస్తువులను జోడించలేదు అనిపిస్తుంది.",
    "startShopping": "షాపింగ్ ప్రారంభించండి",
    "addToCart": "కార్ట్‌లో జోడించండి",
    "removeItem": "వస్తువును తీసివేయండి",
    "updateQuantity": "పరిమాణాన్ని అప్‌డేట్ చేయండి",
    "saveForLater": "తర్వాత కోసం సేవ్ చేయండి",
    "moveToCart": "కార్ట్‌కు తరలించండి",
    "savedForLater": "తర్వాత కోసం సేవ్ చేయబడింది",
    "guestCart": "గెస్ట్ కార్ట్",
    "loginToCheckout": "చెక్అవుట్ కోసం లాగిన్ చేయండి",
    "orderSummary": "ఆర్డర్ సారాంశం",
    "promoCode": "ప్రోమో కోడ్",
    "enterCode": "కోడ్ ఎంటర్ చేయండి",
    "apply": "వర్తింపజేయండి",
    "shipping": "షిప్పింగ్",
    "discount": "తగ్గింపు",
    "free": "ఉచితం",
    "secure": "సురక్షితం",
    "freeShip": "ఉచిత షిప్పింగ్",
    "giftWrap": "గిఫ్ట్ ర్యాప్",
    "bestPrice": "బెస్ట్ ప్రైస్"
  },
  kn: {
    "title": "ಶಾಪಿಂಗ್ ಕಾರ್ಟ್",
    "items": "ವಸ್ತುಗಳು",
    "item": "ವಸ್ತು",
    "total": "ಒಟ್ಟು",
    "subtotal": "ಉಪ ಒಟ್ಟು",
    "checkout": "ಚೆಕ್‌ಔಟ್ ಮುಂದುವರಿಸಿ",
    "continueShopping": "ಶಾಪಿಂಗ್ ಮುಂದುವರಿಸಿ",
    "clearCart": "ಕಾರ್ಟ್ ಕ್ಲಿಯರ್ ಮಾಡಿ",
    "emptyCart": "ನಿಮ್ಮ ಕಾರ್ಟ್ ಖಾಲಿಯಾಗಿದೆ",
    "emptyCartMessage": "ನೀವು ಇನ್ನೂ ನಿಮ್ಮ ಕಾರ್ಟ್‌ಗೆ ಯಾವುದೇ ವಸ್ತುಗಳನ್ನು ಸೇರಿಸಿಲ್ಲ ಎಂದು ತೋರುತ್ತದೆ.",
    "startShopping": "ಶಾಪಿಂಗ್ ಪ್ರಾರಂಭಿಸಿ",
    "addToCart": "ಕಾರ್ಟ್‌ಗೆ ಸೇರಿಸಿ",
    "removeItem": "ವಸ್ತುವನ್ನು ತೆಗೆದುಹಾಕಿ",
    "updateQuantity": "ಪ್ರಮಾಣವನ್ನು ಅಪ್‌ಡೇಟ್ ಮಾಡಿ",
    "saveForLater": "ನಂತರಕ್ಕಾಗಿ ಉಳಿಸಿ",
    "moveToCart": "ಕಾರ್ಟ್‌ಗೆ ಸರಿಸಿ",
    "savedForLater": "ನಂತರಕ್ಕಾಗಿ ಉಳಿಸಲಾಗಿದೆ",
    "guestCart": "ಅತಿಥಿ ಕಾರ್ಟ್",
    "loginToCheckout": "ಚೆಕ್‌ಔಟ್‌ಗಾಗಿ ಲಾಗಿನ್ ಮಾಡಿ",
    "orderSummary": "ಆರ್ಡರ್ ಸಾರಾಂಶ",
    "promoCode": "ಪ್ರೋಮೋ ಕೋಡ್",
    "enterCode": "ಕೋಡ್ ನಮೂದಿಸಿ",
    "apply": "ಅನ್ವಯಿಸಿ",
    "shipping": "ಶಿಪ್ಪಿಂಗ್",
    "discount": "ರಿಯಾಯಿತಿ",
    "free": "ಉಚಿತ",
    "secure": "ಸುರಕ್ಷಿತ",
    "freeShip": "ಉಚಿತ ಶಿಪ್ಪಿಂಗ್",
    "giftWrap": "ಗಿಫ್ಟ್ ರ್ಯಾಪ್",
    "bestPrice": "ಬೆಸ್ಟ್ ಪ್ರೈಸ್"
  },
  ml: {
    "title": "ഷോപ്പിംഗ് കാർട്ട്",
    "items": "ഇനങ്ങൾ",
    "item": "ഇനം",
    "total": "ആകെ",
    "subtotal": "ഉപ ആകെ",
    "checkout": "ചെക്ക്ഔട്ട് തുടരുക",
    "continueShopping": "ഷോപ്പിംഗ് തുടരുക",
    "clearCart": "കാർട്ട് ക്ലിയർ ചെയ്യുക",
    "emptyCart": "നിങ്ങളുടെ കാർട്ട് ശൂന്യമാണ്",
    "emptyCartMessage": "നിങ്ങൾ ഇതുവരെ നിങ്ങളുടെ കാർട്ടിൽ ഒരു ഇനവും ചേർത്തിട്ടില്ലെന്ന് തോന്നുന്നു.",
    "startShopping": "ഷോപ്പിംഗ് ആരംഭിക്കുക",
    "addToCart": "കാർട്ടിൽ ചേർക്കുക",
    "removeItem": "ഇനം നീക്കം ചെയ്യുക",
    "updateQuantity": "അളവ് അപ്ഡേറ്റ് ചെയ്യുക",
    "saveForLater": "പിന്നീടിനായി സേവ് ചെയ്യുക",
    "moveToCart": "കാർട്ടിലേക്ക് നീക്കുക",
    "savedForLater": "പിന്നീടിനായി സേവ് ചെയ്തു",
    "guestCart": "ഗസ്റ്റ് കാർട്ട്",
    "loginToCheckout": "ചെക്ക്ഔട്ടിനായി ലോഗിൻ ചെയ്യുക",
    "orderSummary": "ഓർഡർ സംഗ്രഹം",
    "promoCode": "പ്രോമോ കോഡ്",
    "enterCode": "കോഡ് എന്റർ ചെയ്യുക",
    "apply": "പ്രയോഗിക്കുക",
    "shipping": "ഷിപ്പിംഗ്",
    "discount": "കിഴിവ്",
    "free": "സൗജന്യം",
    "secure": "സുരക്ഷിത",
    "freeShip": "സൗജന്യ ഷിപ്പിംഗ്",
    "giftWrap": "ഗിഫ്റ്റ് റാപ്",
    "bestPrice": "ബെസ്റ്റ് പ്രൈസ്"
  }
};

// Create cart.json files for each language
const localesDir = path.join(__dirname, '../public/locales');
const languages = ['ta', 'hi', 'te', 'kn', 'ml', 'gu', 'mr', 'bn', 'or', 'as', 'ur', 'ne', 'pa'];

languages.forEach(lang => {
  const langDir = path.join(localesDir, lang);
  const cartFile = path.join(langDir, 'cart.json');
  
  // Use specific translation if available, otherwise fallback to English
  const translations = cartTranslations[lang] || require('../public/locales/en/cart.json');
  
  try {
    fs.writeFileSync(cartFile, JSON.stringify(translations, null, 2), 'utf8');
    console.log(`✅ Created cart.json for ${lang}`);
  } catch (error) {
    console.error(`❌ Failed to create cart.json for ${lang}:`, error.message);
  }
});

console.log('\n🎉 Cart translations added successfully!');