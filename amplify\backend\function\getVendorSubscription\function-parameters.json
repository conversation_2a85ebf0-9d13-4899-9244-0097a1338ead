{"lambdaLayers": [], "environmentVariableList": [{"cloudFormationParameterName": "storageThirumanam360Name", "environmentVariableName": "STORAGE_THIRUMANAM360_NAME"}, {"cloudFormationParameterName": "storageThirumanam360Arn", "environmentVariableName": "STORAGE_THIRUMANAM360_ARN"}, {"cloudFormationParameterName": "apiThirumanam360GraphQLAPIIdOutput", "environmentVariableName": "API_THIRUMANAM360_GRAPHQLAPIIDOUTPUT"}, {"cloudFormationParameterName": "apiThirumanam360GraphQLAPIEndpointOutput", "environmentVariableName": "API_THIRUMANAM360_GRAPHQLAPIENDPOINTOUTPUT"}, {"cloudFormationParameterName": "apiThirumanam360VendorSubscriptionTableName", "environmentVariableName": "API_THIRUMANAM360_VENDORSUBSCRIPTIONTABLE_NAME"}, {"cloudFormationParameterName": "apiThirumanam360VendorSubscriptionTableArn", "environmentVariableName": "API_THIRUMANAM360_VENDORSUBSCRIPTIONTABLE_ARN"}]}