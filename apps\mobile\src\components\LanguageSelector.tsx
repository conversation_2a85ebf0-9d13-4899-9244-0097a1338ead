import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Modal,
  FlatList,
  StyleSheet,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTranslation } from 'react-i18next';
import { SUPPORTED_LANGUAGES, changeLanguage, getCurrentLanguage } from '../i18n';

interface LanguageSelectorProps {
  visible: boolean;
  onClose: () => void;
  onLanguageChange?: (languageCode: string) => void;
}

interface LanguageItemProps {
  language: {
    code: string;
    name: string;
    nativeName: string;
  };
  isSelected: boolean;
  onSelect: (code: string) => void;
}

const LanguageItem: React.FC<LanguageItemProps> = ({ language, isSelected, onSelect }) => {
  return (
    <TouchableOpacity
      style={[styles.languageItem, isSelected && styles.selectedLanguageItem]}
      onPress={() => onSelect(language.code)}
      activeOpacity={0.7}
    >
      <View style={styles.languageInfo}>
        <Text style={[styles.languageName, isSelected && styles.selectedText]}>
          {language.nativeName}
        </Text>
        <Text style={[styles.languageSubtext, isSelected && styles.selectedSubtext]}>
          {language.name}
        </Text>
      </View>
      {isSelected && (
        <Ionicons
          name="checkmark-circle"
          size={24}
          color="#8B0000"
        />
      )}
    </TouchableOpacity>
  );
};

const LanguageSelector: React.FC<LanguageSelectorProps> = ({
  visible,
  onClose,
  onLanguageChange,
}) => {
  const { t } = useTranslation();
  const [currentLanguage, setCurrentLanguage] = useState(getCurrentLanguage());
  const [isChanging, setIsChanging] = useState(false);

  const handleLanguageSelect = async (languageCode: string) => {
    if (languageCode === currentLanguage || isChanging) {
      return;
    }

    setIsChanging(true);

    try {
      const success = await changeLanguage(languageCode);
      
      if (success) {
        setCurrentLanguage(languageCode);
        onLanguageChange?.(languageCode);
        
        // Show success message
        Alert.alert(
          t('success.updated'),
          t('common.language') + ' ' + t('success.updated').toLowerCase(),
          [
            {
              text: t('common.ok'),
              onPress: onClose,
            },
          ]
        );
      } else {
        // Show error message
        Alert.alert(
          t('error.general'),
          t('error.tryAgain'),
          [
            {
              text: t('common.ok'),
            },
          ]
        );
      }
    } catch (error) {
      console.error('Error changing language:', error);
      Alert.alert(
        t('error.general'),
        t('error.tryAgain'),
        [
          {
            text: t('common.ok'),
          },
        ]
      );
    } finally {
      setIsChanging(false);
    }
  };

  const renderLanguageItem = ({ item }: { item: typeof SUPPORTED_LANGUAGES[0] }) => (
    <LanguageItem
      language={item}
      isSelected={item.code === currentLanguage}
      onSelect={handleLanguageSelect}
    />
  );

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <View style={styles.container}>
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.closeButton}
            onPress={onClose}
            disabled={isChanging}
          >
            <Ionicons name="close" size={24} color="#333" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>{t('common.language')}</Text>
          <View style={styles.placeholder} />
        </View>

        {/* Language List */}
        <FlatList
          data={SUPPORTED_LANGUAGES}
          renderItem={renderLanguageItem}
          keyExtractor={(item) => item.code}
          style={styles.languageList}
          contentContainerStyle={styles.languageListContent}
          showsVerticalScrollIndicator={false}
          scrollEnabled={!isChanging}
        />

        {/* Footer */}
        <View style={styles.footer}>
          <Text style={styles.footerText}>
            {t('common.language')} {t('common.preferences')}
          </Text>
          <Text style={styles.footerSubtext}>
            Choose your preferred language for the app interface
          </Text>
        </View>
      </View>

      {/* Loading Overlay */}
      {isChanging && (
        <View style={styles.loadingOverlay}>
          <View style={styles.loadingContainer}>
            <Text style={styles.loadingText}>{t('common.loading')}</Text>
          </View>
        </View>
      )}
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
    backgroundColor: '#fff',
  },
  closeButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
  },
  placeholder: {
    width: 40,
  },
  languageList: {
    flex: 1,
  },
  languageListContent: {
    paddingVertical: 8,
  },
  languageItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  selectedLanguageItem: {
    backgroundColor: '#f8f8f8',
  },
  languageInfo: {
    flex: 1,
  },
  languageName: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
    marginBottom: 2,
  },
  languageSubtext: {
    fontSize: 14,
    color: '#666',
  },
  selectedText: {
    color: '#8B0000',
    fontWeight: '600',
  },
  selectedSubtext: {
    color: '#8B0000',
  },
  footer: {
    padding: 20,
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
    backgroundColor: '#f8f8f8',
  },
  footerText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#333',
    marginBottom: 4,
  },
  footerSubtext: {
    fontSize: 12,
    color: '#666',
  },
  loadingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingContainer: {
    backgroundColor: '#fff',
    paddingHorizontal: 24,
    paddingVertical: 16,
    borderRadius: 8,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
  },
  loadingText: {
    fontSize: 16,
    color: '#333',
  },
});

export default LanguageSelector;
