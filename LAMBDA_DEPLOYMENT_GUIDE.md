# 🚀 Lambda Deployment Guide - BookmyFestive

## ✅ Issues Fixed

### 1. **GraphQL Schema Conflicts**
- ❌ **Problem**: `getVendorSubscription` was conflicting with auto-generated queries
- ✅ **Solution**: 
  - Changed `type Query` to `extend type Query`
  - Renamed `getVendorSubscription` to `getVendorActiveSubscription`
  - Renamed `getSubscriptionPayments` to `getSubscriptionPaymentHistory`

### 2. **Auth Directive Warnings**
- ❌ **Problem**: `@auth` directives on root type fields causing warnings
- ✅ **Solution**: 
  - Simplified auth rules to use `{ allow: private }` for Lambda functions
  - Removed complex field-level auth on root types
  - Lambda functions handle authorization internally

### 3. **Missing Lambda Functions**
- ❌ **Problem**: Schema referenced non-existent Lambda functions
- ✅ **Solution**: Created proper Lambda functions with CloudFormation templates

## 📁 Lambda Functions Structure

```
amplify/backend/function/
├── sendEmail/                    ✅ Existing
├── generateInvoice/              ✅ Existing  
├── createSubscription/           ✅ Existing
├── getVendorSubscription/        🆕 Created
│   ├── src/
│   │   ├── index.js
│   │   └── package.json
│   ├── function-parameters.json
│   └── getVendorSubscription-cloudformation-template.json
└── getSubscriptionPayments/      🆕 Created
    ├── src/
    │   ├── index.js
    │   └── package.json
    ├── function-parameters.json
    └── getSubscriptionPayments-cloudformation-template.json
```

## 🔧 Deployment Steps

### Step 1: Verify Schema Changes
```bash
# Check for syntax errors
amplify api gql-compile
```

### Step 2: Deploy API Changes
```bash
# Deploy the updated GraphQL schema
amplify push api
```

### Step 3: Add Lambda Functions to Amplify
```bash
# Add the new Lambda functions
amplify add function

# When prompted:
# - Function name: getVendorSubscription
# - Runtime: NodeJS
# - Template: Hello World
# - Advanced settings: Configure environment variables

# Repeat for getSubscriptionPayments
```

### Step 4: Update Function Dependencies
```bash
# Navigate to each function and install dependencies
cd amplify/backend/function/getVendorSubscription/src
npm install

cd ../../../getSubscriptionPayments/src  
npm install
```

### Step 5: Deploy All Changes
```bash
# Deploy everything
amplify push

# Or deploy specific categories
amplify push api function
```

## 🔍 Verification Steps

### 1. Check GraphQL Schema
```bash
# Verify schema compilation
amplify api gql-compile

# Check for warnings/errors
amplify status
```

### 2. Test Lambda Functions
```bash
# Test in AWS Console or use Amplify CLI
amplify function invoke getVendorSubscription

# Test with sample payload:
{
  "arguments": {
    "vendorId": "vendor_123"
  }
}
```

### 3. Verify API Endpoints
```graphql
# Test in GraphQL playground
query GetVendorActiveSubscription {
  getVendorActiveSubscription(vendorId: "vendor_123") {
    id
    status
    planId
    startDate
    endDate
  }
}
```

## 🛠️ Troubleshooting

### Common Issues:

1. **"Function not found" errors**
   ```bash
   # Ensure functions are properly added
   amplify status
   amplify push function
   ```

2. **Permission errors**
   ```bash
   # Check IAM roles in CloudFormation templates
   # Verify DynamoDB table permissions
   ```

3. **Environment variable issues**
   ```bash
   # Check function-parameters.json
   # Verify CloudFormation parameter mapping
   ```

## 📊 Expected Results

After successful deployment:

- ✅ No GraphQL schema conflicts
- ✅ No auth directive warnings  
- ✅ All Lambda functions deployed
- ✅ Proper DynamoDB permissions
- ✅ Working GraphQL queries
- ✅ Invoice and pricing services functional

## 🔄 Service Updates

Updated the following services to use new query names:

### `pricingService.ts`
```typescript
// Old import
import { getVendorSubscription } from '@/src/graphql/queries';

// New import  
import { getVendorActiveSubscription } from '@/src/graphql/queries';
```

### GraphQL Queries
```graphql
# Old query name (conflicted)
getVendorSubscription(vendorId: ID!): VendorSubscription

# New query name (no conflict)
getVendorActiveSubscription(vendorId: ID!): VendorSubscription
```

## 🚀 Next Steps

1. **Deploy to AWS**: Run `amplify push`
2. **Test functionality**: Verify all services work
3. **Monitor logs**: Check CloudWatch for any issues
4. **Update frontend**: Use new query names in React components

The schema is now properly structured for AWS Amplify deployment with no conflicts or warnings.
