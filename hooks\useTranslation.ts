"use client"

import { useEffect, useState } from 'react';
import { useTranslation as useI18nTranslation } from 'react-i18next';
import '@/lib/i18n-enhanced';

export function useTranslation() {
  const { t, i18n, ready } = useI18nTranslation('common');
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  const safeT = (key: string, fallback?: string, options?: any) => {
    if (!mounted || !ready) {
      return fallback || key.split('.').pop() || key;
    }
    
    const translation = t(key, options);
    
    // If translation is the same as key, it means translation is missing
    if (translation === key && fallback) {
      return fallback;
    }
    
    return translation;
  };

  const changeLanguage = async (languageCode: string) => {
    try {
      await i18n.changeLanguage(languageCode);
      if (typeof window !== 'undefined') {
        localStorage.setItem('selectedLanguage', languageCode);
      }
      return true;
    } catch (error) {
      console.error('Error changing language:', error);
      return false;
    }
  };

  return {
    t: safeT,
    i18n,
    ready: mounted && ready,
    mounted,
    changeLanguage,
    currentLanguage: i18n.language || 'en',
  };
}

// Namespace-specific hooks
export function useNavigationTranslation() {
  const { t } = useTranslation();
  return {
    t: (key: string, fallback?: string) => t(`navigation.${key}`, fallback),
  };
}

export function useHeaderTranslation() {
  const { t } = useTranslation();
  return {
    t: (key: string, fallback?: string) => t(`header.${key}`, fallback),
  };
}

export function useHeroTranslation() {
  const { t } = useTranslation();
  return {
    t: (key: string, fallback?: string) => t(`hero.${key}`, fallback),
  };
}

export function useServicesTranslation() {
  const { t } = useTranslation();
  return {
    t: (key: string, fallback?: string) => t(`services.${key}`, fallback),
  };
}

export function useVendorsTranslation() {
  const { t } = useTranslation();
  return {
    t: (key: string, fallback?: string) => t(`featuredVendors.${key}`, fallback),
  };
}

export function useVenuesTranslation() {
  const { t } = useTranslation();
  return {
    t: (key: string, fallback?: string) => t(`venues.${key}`, fallback),
  };
}

export function useShopTranslation() {
  const { t } = useTranslation();
  return {
    t: (key: string, fallback?: string) => t(`shop.${key}`, fallback),
  };
}

export function useFooterTranslation() {
  const { t } = useTranslation();
  return {
    t: (key: string, fallback?: string) => t(`footer.${key}`, fallback),
  };
}