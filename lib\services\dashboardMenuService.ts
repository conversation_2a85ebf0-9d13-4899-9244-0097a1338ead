/**
 * Dashboard Menu Service
 * Provides dynamic menu configurations based on user roles
 */

import {
  Home, User, Briefcase, ShoppingBag, MapPin, Calendar, Star,
  Image as ImageIcon, Settings, Shield, MessageSquare, Database,
  MessageCircle, FileText, BarChart, Users, Bell, Crown,
  TrendingUp, Activity, DollarSign, Clock, AlertTriangle,
  Eye, Heart, Search, Filter, Plus, Edit, Trash2, Download,
  Upload, Mail, Phone, Globe, Award, Target, Zap, Package,
  CreditCard
} from 'lucide-react';

export type UserRole = 'customer' | 'vendor' | 'admin' | 'super_admin';

export interface MenuItem {
  href: string;
  label: string;
  icon: any;
  description?: string;
  badge?: string | number;
  isNew?: boolean;
  permissions?: string[];
  category?: string;
}

export interface MenuCategory {
  name: string;
  items: MenuItem[];
  icon?: any;
  description?: string;
}

export class DashboardMenuService {
  
  /**
   * Get complete menu structure for a user type
   */
  static getMenuForUserType(userType: UserRole | null): MenuItem[] {
    switch (userType) {
      case 'super_admin':
        return this.getSuperAdminMenu();
      case 'admin':
        return this.getAdminMenu();
      case 'vendor':
        return this.getVendorMenu();
      case 'customer':
      default:
        return this.getCustomerMenu();
    }
  }

  /**
   * Get categorized menu for better organization
   */
  static getCategorizedMenu(userType: UserRole | null): MenuCategory[] {
    switch (userType) {
      case 'super_admin':
        return this.getSuperAdminCategorizedMenu();
      case 'admin':
        return this.getAdminCategorizedMenu();
      case 'vendor':
        return this.getVendorCategorizedMenu();
      case 'customer':
      default:
        return this.getCustomerCategorizedMenu();
    }
  }

  /**
   * Customer Menu - Wedding planning focused
   */
  private static getCustomerMenu(): MenuItem[] {
    return [
      { href: '/dashboard', label: 'Dashboard', icon: Home, description: 'Overview of your wedding planning' },
      { href: '/dashboard/profile', label: 'Profile', icon: User, description: 'Personal information and preferences' },
      { href: '/dashboard/bookings', label: 'My Bookings', icon: Calendar, description: 'Manage your vendor bookings' },
      { href: '/dashboard/favorites', label: 'Saved Vendors', icon: Heart, description: 'Your favorite vendors and venues' },
      { href: '/dashboard/reviews', label: 'My Reviews', icon: Star, description: 'Reviews you\'ve written' },
      { href: '/dashboard/gallery', label: 'Wedding Gallery', icon: ImageIcon, description: 'Your wedding inspiration photos' },
      { href: '/dashboard/budget', label: 'Budget Tracker', icon: DollarSign, description: 'Track your wedding expenses', isNew: true },
      { href: '/dashboard/checklist', label: 'Wedding Checklist', icon: Target, description: 'Stay organized with tasks', isNew: true },
      { href: '/dashboard/settings', label: 'Settings', icon: Settings, description: 'Account and notification settings' },
    ];
  }

  /**
   * Vendor Menu - Business management focused
   */
  private static getVendorMenu(): MenuItem[] {
    return [
      { href: '/dashboard', label: 'Dashboard', icon: Home, description: 'Business overview and analytics' },
      { href: '/dashboard/profile', label: 'Profile', icon: User, description: 'Business profile and information' },
      { href: '/dashboard/vendors', label: 'My Vendors', icon: Briefcase, description: 'Manage your vendor service listings' },
      { href: '/dashboard/venues', label: 'Venue Management', icon: MapPin, description: 'Manage your venue listings' },
      { href: '/dashboard/shop', label: 'Shop Management', icon: ShoppingBag, description: 'Manage your product catalog' },
      { href: '/dashboard/inquiries', label: 'Customer Inquiries', icon: MessageCircle, description: 'Respond to customer inquiries' },
      { href: '/dashboard/bookings', label: 'Booking Management', icon: Calendar, description: 'Manage customer bookings' },
      { href: '/dashboard/reviews', label: 'Customer Reviews', icon: Star, description: 'View and respond to reviews' },
      { href: '/dashboard/blogs', label: 'Content Marketing', icon: FileText, description: 'Manage your blog content' },
      { href: '/dashboard/gallery', label: 'Portfolio Gallery', icon: ImageIcon, description: 'Showcase your work' },
      { href: '/dashboard/settings', label: 'Settings', icon: Settings, description: 'Business preferences and settings' },
    ];
  }

  /**
   * Admin Menu - Platform management focused
   */
  private static getAdminMenu(): MenuItem[] {
    return [
      { href: '/dashboard', label: 'Dashboard', icon: Home, description: 'Platform administration overview' },
      { href: '/dashboard/profile', label: 'Profile', icon: User, description: 'Admin profile and information' },
      { href: '/dashboard/admin-users', label: 'User Management', icon: Users, description: 'Manage platform users with full CRUD operations' },
      { href: '/dashboard/admin-vendors', label: 'Vendor Management', icon: Briefcase, description: 'Manage vendor listings and verification' },
      { href: '/dashboard/admin-venues', label: 'Venue Management', icon: MapPin, description: 'Manage venue listings and approval' },
      { href: '/dashboard/admin-newsletter', label: 'Newsletter Management', icon: Mail, description: 'Manage newsletter subscriptions and campaigns' },
      { href: '/dashboard/admin-shops', label: 'Shop Management', icon: ShoppingBag, description: 'Manage shop products and inventory' },
      { href: '/dashboard/admin-reviews', label: 'Review Moderation', icon: Star, description: 'Moderate user reviews' },
      { href: '/dashboard/admin-pricing', label: 'Pricing Management', icon: CreditCard, description: 'Manage vendor subscription pricing plans' },
      { href: '/dashboard/contacts', label: 'Contact Management', icon: MessageSquare, description: 'Handle customer support' },
      { href: '/dashboard/admin-tools', label: 'Admin Tools', icon: Database, description: 'Platform administration tools' },
      { href: '/dashboard/admin/disputes', label: 'Dispute Resolution', icon: AlertTriangle, description: 'Handle user disputes' },
      { href: '/dashboard/admin-tools', label: 'Admin Tools', icon: Database, description: 'Administrative utilities' },
      { href: '/dashboard/settings', label: 'Settings', icon: Settings, description: 'Configure platform settings' },
    ];
  }

  /**
   * Super Admin Menu - System management focused
   */
  private static getSuperAdminMenu(): MenuItem[] {
    return [
      { href: '/dashboard', label: 'Dashboard', icon: Home, description: 'System administration overview' },
      { href: '/dashboard/profile', label: 'Profile', icon: User, description: 'Super admin profile and information' },
      { href: '/dashboard/super-admin', label: 'Super Admin Dashboard', icon: Crown, description: 'Advanced system controls' },
      { href: '/dashboard/super-admin/system', label: 'System Management', icon: Database, description: 'Core system administration' },
      { href: '/dashboard/super-admin/admins', label: 'Admin Management', icon: Shield, description: 'Manage administrator accounts' },
      { href: '/dashboard/super-admin/analytics', label: 'Platform Analytics', icon: TrendingUp, description: 'Comprehensive platform insights' },
      { href: '/dashboard/super-admin/logs', label: 'System Logs', icon: Activity, description: 'Monitor system activity' },
      { href: '/dashboard/super-admin/security', label: 'Security Center', icon: Shield, description: 'Security monitoring and controls' },
      { href: '/dashboard/super-admin/backup', label: 'Backup & Recovery', icon: Download, description: 'Data backup management' },
      { href: '/dashboard/super-admin/integrations', label: 'Integrations', icon: Zap, description: 'Third-party integrations' },
      // Include admin capabilities (excluding dashboard and profile since they're already included)
      ...this.getAdminMenu().filter(item => !item.href.includes('super-admin') && item.href !== '/dashboard' && item.href !== '/dashboard/profile'),
    ];
  }

  /**
   * Customer Categorized Menu
   */
  private static getCustomerCategorizedMenu(): MenuCategory[] {
    return [
      {
        name: 'Planning',
        icon: Target,
        description: 'Wedding planning tools',
        items: [
          { href: '/dashboard', label: 'Dashboard', icon: Home },
          { href: '/dashboard/checklist', label: 'Wedding Checklist', icon: Target, isNew: true },
          { href: '/dashboard/budget', label: 'Budget Tracker', icon: DollarSign, isNew: true },
          { href: '/dashboard/bookings', label: 'My Bookings', icon: Calendar },
          { href: '/dashboard/orders', label: 'My Orders', icon: Package },
        ]
      },
      {
        name: 'Vendors & Services',
        icon: Heart,
        description: 'Manage your vendor relationships',
        items: [
          { href: '/dashboard/favorites', label: 'Saved Vendors', icon: Heart },
          { href: '/dashboard/reviews', label: 'My Reviews', icon: Star },
          { href: '/dashboard/gallery', label: 'Inspiration Gallery', icon: ImageIcon },
        ]
      },
      {
        name: 'Account',
        icon: User,
        description: 'Personal settings',
        items: [
          { href: '/dashboard/profile', label: 'My Profile', icon: User },
          { href: '/dashboard/settings', label: 'Settings', icon: Settings },
        ]
      }
    ];
  }

  /**
   * Vendor Categorized Menu
   */
  private static getVendorCategorizedMenu(): MenuCategory[] {
    return [
      {
        name: 'Business Overview',
        icon: Briefcase,
        description: 'Business performance and analytics',
        items: [
          { href: '/dashboard', label: 'Dashboard Home', icon: Home },
          { href: '/dashboard/inquiries', label: 'Inquiries', icon: MessageCircle },
          { href: '/dashboard/bookings', label: 'Bookings', icon: Calendar },
        ]
      },
      {
        name: 'Listings Management',
        icon: ShoppingBag,
        description: 'Manage your services and products',
        items: [
          { href: '/dashboard/vendors', label: 'My Vendors', icon: Briefcase },
          { href: '/dashboard/venues', label: 'Venues', icon: MapPin },
          { href: '/dashboard/shop', label: 'Products', icon: ShoppingBag },
          { href: '/dashboard/vendor-orders', label: 'Order Management', icon: Package },
          { href: '/dashboard/gallery', label: 'Portfolio', icon: ImageIcon },
        ]
      },
      {
        name: 'Customer Relations',
        icon: Users,
        description: 'Manage customer interactions',
        items: [
          { href: '/dashboard/reviews', label: 'Reviews', icon: Star },
          { href: '/dashboard/blogs', label: 'Content', icon: FileText },
        ]
      },
      {
        name: 'Business Tools',
        icon: Database,
        description: 'Business management tools and utilities',
        items: [
          { href: '/dashboard/admin-tools', label: 'Business Tools', icon: Database },
        ]
      },
      {
        name: 'Business Settings',
        icon: Settings,
        description: 'Configure your business',
        items: [
          { href: '/dashboard/profile', label: 'Business Profile', icon: User },
          { href: '/dashboard/settings', label: 'Settings', icon: Settings },
        ]
      }
    ];
  }

  /**
   * Admin Categorized Menu
   */
  private static getAdminCategorizedMenu(): MenuCategory[] {
    return [
      {
        name: 'Platform Management',
        icon: Shield,
        description: 'Core platform administration',
        items: [
          { href: '/dashboard', label: 'Dashboard', icon: Shield },
          { href: '/dashboard/admin-users', label: 'User Management', icon: Users },
          { href: '/dashboard/admin-tools', label: 'Admin Tools', icon: BarChart },
        ]
      },
      {
        name: 'Content Management',
        icon: Briefcase,
        description: 'Manage all platform content',
        items: [
          { href: '/dashboard/admin-vendors', label: 'Vendor Management', icon: Briefcase },
          { href: '/dashboard/admin-venues', label: 'Venue Management', icon: MapPin },
          { href: '/dashboard/admin-shops', label: 'Shop Management', icon: ShoppingBag },
          { href: '/dashboard/admin-orders', label: 'Order Management', icon: Package },
        ]
      },
      {
        name: 'Quality Control',
        icon: FileText,
        description: 'Content moderation and quality control',
        items: [
          { href: '/dashboard/admin-reviews', label: 'Review Moderation', icon: Star },
          { href: '/dashboard/admin-tools', label: 'Content Tools', icon: FileText },
        ]
      },
      {
        name: 'Support & Tools',
        icon: MessageSquare,
        description: 'Customer support and admin tools',
        items: [
          { href: '/dashboard/contacts', label: 'Customer Support', icon: MessageSquare },
          { href: '/dashboard/admin-newsletter', label: 'Newsletter Management', icon: Mail },
          { href: '/dashboard/admin-pricing', label: 'Pricing Management', icon: CreditCard },
          { href: '/dashboard/admin/disputes', label: 'Disputes', icon: AlertTriangle },
          { href: '/dashboard/admin-tools', label: 'Admin Tools', icon: Database },
        ]
      }
    ];
  }

  /**
   * Super Admin Categorized Menu
   */
  private static getSuperAdminCategorizedMenu(): MenuCategory[] {
    return [
      {
        name: 'System Administration',
        icon: Crown,
        description: 'Core system management',
        items: [
          { href: '/dashboard/super-admin', label: 'Super Admin Dashboard', icon: Crown },
          { href: '/dashboard/super-admin/system', label: 'System Management', icon: Database },
          { href: '/dashboard/super-admin/security', label: 'Security Center', icon: Shield },
          { href: '/dashboard/super-admin/logs', label: 'System Logs', icon: Activity },
        ]
      },
      {
        name: 'Platform Analytics',
        icon: TrendingUp,
        description: 'Comprehensive platform insights',
        items: [
          { href: '/dashboard/super-admin/analytics', label: 'Platform Analytics', icon: TrendingUp },
          { href: '/dashboard/admin-tools', label: 'Reports', icon: BarChart },
        ]
      },
      {
        name: 'Administration',
        icon: Shield,
        description: 'User and admin management',
        items: [
          { href: '/dashboard/super-admin/admins', label: 'Admin Management', icon: Shield },
          { href: '/dashboard/admin-users', label: 'User Management', icon: Users },
        ]
      },
      {
        name: 'System Tools',
        icon: Database,
        description: 'System utilities and integrations',
        items: [
          { href: '/dashboard/super-admin/backup', label: 'Backup & Recovery', icon: Download },
          { href: '/dashboard/super-admin/integrations', label: 'Integrations', icon: Zap },
          { href: '/dashboard/admin-tools', label: 'Admin Tools', icon: Database },
        ]
      }
    ];
  }

  /**
   * Get quick actions based on user type
   */
  static getQuickActions(userType: UserRole | null): MenuItem[] {
    switch (userType) {
      case 'super_admin':
        return [
          { href: '/dashboard/super-admin/users', label: 'Add Admin', icon: Plus },
          { href: '/dashboard/super-admin/backup', label: 'Backup Now', icon: Download },
          { href: '/dashboard/super-admin/logs', label: 'View Logs', icon: Activity },
        ];
      case 'admin':
        return [
          { href: '/dashboard/admin-users', label: 'Manage Users', icon: Users },
          { href: '/dashboard/admin-vendors', label: 'Manage Vendors', icon: Briefcase },
          { href: '/dashboard/admin-venues', label: 'Manage Venues', icon: MapPin },
          { href: '/dashboard/admin-shops', label: 'Manage Products', icon: ShoppingBag },
          { href: '/dashboard/admin-orders', label: 'Manage Orders', icon: Package },
          { href: '/dashboard/admin-reviews', label: 'Review Queue', icon: Clock },
          { href: '/dashboard/admin-newsletter', label: 'Newsletter', icon: Mail },
          { href: '/dashboard/admin-pricing', label: 'Pricing Plans', icon: CreditCard },
          { href: '/dashboard/admin-tools', label: 'Admin Tools', icon: BarChart },
        ];
      case 'vendor':
        return [
          { href: '/dashboard/vendors/new', label: 'Add Service', icon: Plus },
          { href: '/dashboard/vendor-orders', label: 'Manage Orders', icon: Package },
          { href: '/dashboard/inquiries', label: 'View Inquiries', icon: MessageCircle },
          { href: '/dashboard/analytics', label: 'View Analytics', icon: BarChart },
          { href: '/dashboard/admin-tools', label: 'Business Tools', icon: Database },
        ];
      case 'customer':
      default:
        return [
          { href: '/vendors', label: 'Find Vendors', icon: Search },
          { href: '/venues', label: 'Browse Venues', icon: MapPin },
          { href: '/dashboard/checklist', label: 'Wedding Checklist', icon: Target },
        ];
    }
  }
}

export default DashboardMenuService;
