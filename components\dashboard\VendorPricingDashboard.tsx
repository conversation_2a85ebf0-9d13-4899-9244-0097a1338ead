'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  CreditCard,
  Calendar,
  TrendingUp,
  Download,
  Eye,
  AlertCircle,
  CheckCircle,
  Clock,
  Star,
  History,
  Settings,
  RefreshCw,
  Crown
} from 'lucide-react';
import { VendorSubscriptionService, type VendorSubscriptionPlan } from '@/lib/services/vendorSubscriptionService';
import { useAuth } from '@/contexts/AuthContext';
import { useRouter } from 'next/navigation';
import toast from 'react-hot-toast';

interface VendorSubscription {
  id: string;
  vendorId: string;
  planId: string;
  status: 'ACTIVE' | 'CANCELLED' | 'EXPIRED' | 'PENDING';
  startDate: string;
  endDate: string;
  amount: number;
  currency: string;
  autoRenew: boolean;
  transactionId?: string;
  lastPaymentDate?: string;
  nextPaymentDate?: string;
}

interface PaymentRecord {
  id: string;
  subscriptionId: string;
  amount: number;
  currency: string;
  status: 'PAID' | 'PENDING' | 'FAILED';
  paymentDate: string;
  transactionId: string;
  paymentMethod: string;
}

export default function VendorPricingDashboard() {
  const [subscription, setSubscription] = useState<VendorSubscription | null>(null);
  const [availablePlans, setAvailablePlans] = useState<VendorSubscriptionPlan[]>([]);
  const [paymentHistory, setPaymentHistory] = useState<PaymentRecord[]>([]);
  const [loading, setLoading] = useState(true);
  const [processing, setProcessing] = useState(false);
  const [showUpgradeModal, setShowUpgradeModal] = useState(false);
  const { user, userProfile } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (user && userProfile?.isVendor) {
      loadDashboardData();
      loadRazorpaySDK();
    }
  }, [user, userProfile]);

  const loadDashboardData = async () => {
    setLoading(true);
    try {
      const [subscriptionData, plans] = await Promise.all([
        VendorSubscriptionService.getVendorSubscription(userProfile?.id || user!.id),
        VendorSubscriptionService.getPricingPlans()
      ]);

      setSubscription(subscriptionData);
      setAvailablePlans(plans);

      // Mock payment history for now - you can implement this later
      setPaymentHistory([]);

    } catch (error) {
      console.error('Error loading dashboard data:', error);
      toast.error('Failed to load subscription data');
    } finally {
      setLoading(false);
    }
  };

  const loadRazorpaySDK = async () => {
    await VendorSubscriptionService.loadRazorpaySDK();
  };

  const handleUpgrade = async (plan: VendorSubscriptionPlan) => {
    if (!user || !userProfile) return;

    setProcessing(true);
    try {
      const result = await VendorSubscriptionService.processSubscriptionPayment(
        {
          vendorId: userProfile.id || user.id,
          planId: plan.id,
          paymentMethod: 'RAZORPAY',
          autoRenew: true,
          vendorEmail: user.email || '',
          vendorName: userProfile.businessName || userProfile.fullName || ''
        },
        plan,
        {
          name: userProfile.businessName || userProfile.fullName || '',
          email: user.email || '',
          phone: userProfile.phone
        }
      );

      if (result.success) {
        toast.success('Subscription updated successfully!');
        loadDashboardData();
        setShowUpgradeModal(false);
      } else {
        toast.error(result.message || 'Failed to update subscription');
      }
    } catch (error) {
      console.error('Error upgrading subscription:', error);
      toast.error('An error occurred while processing payment');
    } finally {
      setProcessing(false);
    }
  };

  const handleCancelSubscription = async () => {
    if (!subscription) return;

    const confirmed = confirm('Are you sure you want to cancel your subscription? This action cannot be undone.');
    if (!confirmed) return;

    setProcessing(true);
    try {
      // For now, we'll just show a message - implement actual cancellation logic later
      toast.success('Cancellation request submitted. You will receive a confirmation email shortly.');
      // TODO: Implement actual subscription cancellation
    } catch (error) {
      console.error('Error cancelling subscription:', error);
      toast.error('An error occurred while cancelling subscription');
    } finally {
      setProcessing(false);
    }
  };

  const formatPrice = (price: number, currency: string) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 0
    }).format(price);
  };

  const getDurationText = (duration: string) => {
    switch (duration) {
      case 'MONTHLY': return 'month';
      case 'QUARTERLY': return 'quarter';
      case 'YEARLY': return 'year';
      default: return 'month';
    }
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      ACTIVE: { color: 'bg-green-100 text-green-800', icon: CheckCircle },
      PENDING: { color: 'bg-yellow-100 text-yellow-800', icon: Clock },
      CANCELLED: { color: 'bg-red-100 text-red-800', icon: AlertCircle },
      EXPIRED: { color: 'bg-gray-100 text-gray-800', icon: AlertCircle }
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.PENDING;
    const Icon = config.icon;

    return (
      <Badge className={config.color}>
        <Icon className="h-3 w-3 mr-1" />
        {status.charAt(0).toUpperCase() + status.slice(1).toLowerCase()}
      </Badge>
    );
  };

  const getDaysUntilExpiry = () => {
    if (!subscription || subscription.status !== 'ACTIVE') return null;

    const endDate = new Date(subscription.endDate);
    const now = new Date();
    const diffTime = endDate.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    return diffDays;
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center py-12">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-red-600"></div>
        <span className="ml-3 text-gray-600">Loading subscription data...</span>
      </div>
    );
  }

  const daysUntilExpiry = getDaysUntilExpiry();
  const currentPlan = availablePlans.find(plan => plan.id === subscription?.planId);

  return (
    <div className="space-y-4 sm:space-y-6">
      {/* Quick Actions */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4">
        <Card className="hover:shadow-md transition-shadow">
          <CardContent className="p-3 sm:p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-100 rounded-lg">
                <TrendingUp className="h-4 w-4 sm:h-5 sm:w-5 text-blue-600" />
              </div>
              <div>
                <p className="font-medium text-sm sm:text-base">Upgrade Plan</p>
                <p className="text-xs sm:text-sm text-gray-600">Get more features</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="hover:shadow-md transition-shadow">
          <CardContent className="p-3 sm:p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-green-100 rounded-lg">
                <History className="h-4 w-4 sm:h-5 sm:w-5 text-green-600" />
              </div>
              <div>
                <p className="font-medium text-sm sm:text-base">Payment History</p>
                <p className="text-xs sm:text-sm text-gray-600">View past payments</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="hover:shadow-md transition-shadow sm:col-span-2 lg:col-span-1">
          <CardContent className="p-3 sm:p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-purple-100 rounded-lg">
                <Settings className="h-4 w-4 sm:h-5 sm:w-5 text-purple-600" />
              </div>
              <div>
                <p className="font-medium text-sm sm:text-base">Billing Settings</p>
                <p className="text-xs sm:text-sm text-gray-600">Manage preferences</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Current Subscription Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-lg sm:text-xl">
            <CreditCard className="h-5 w-5" />
            Current Subscription
          </CardTitle>
          <CardDescription className="text-sm sm:text-base">
            Manage your vendor subscription and billing
          </CardDescription>
        </CardHeader>
        <CardContent>
          {subscription ? (
            <div className="space-y-4">
              <div className="flex flex-col sm:flex-row sm:justify-between sm:items-start gap-4">
                <div className="flex-1">
                  <h3 className="text-base sm:text-lg font-semibold flex flex-wrap items-center gap-2">
                    {currentPlan?.name || 'Unknown Plan'}
                    {currentPlan?.isPopular && (
                      <Badge className="bg-red-600 text-white">
                        <Crown className="h-3 w-3 mr-1" />
                        Popular
                      </Badge>
                    )}
                  </h3>
                  <p className="text-sm sm:text-base text-gray-600">{currentPlan?.description}</p>
                  <div className="flex flex-wrap items-center gap-2 sm:gap-4 mt-2">
                    {getStatusBadge(subscription.status)}
                    {subscription.status === 'ACTIVE' && daysUntilExpiry !== null && (
                      <span className={`text-xs sm:text-sm ${daysUntilExpiry <= 7 ? 'text-red-600' : 'text-gray-600'}`}>
                        {daysUntilExpiry > 0 ? `${daysUntilExpiry} days remaining` : 'Expired'}
                      </span>
                    )}
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-xl sm:text-2xl font-bold">
                    {currentPlan ? formatPrice(currentPlan.price, currentPlan.currency) : '₹0'}
                  </div>
                  <div className="text-sm sm:text-base text-gray-600">
                    /{currentPlan ? getDurationText(currentPlan.duration) : 'month'}
                  </div>
                </div>
              </div>

              {subscription.status === 'ACTIVE' && daysUntilExpiry !== null && daysUntilExpiry <= 7 && (
                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3 sm:p-4">
                  <div className="flex items-center gap-2 text-yellow-800">
                    <AlertCircle className="h-4 w-4" />
                    <span className="font-medium text-sm sm:text-base">Subscription Expiring Soon</span>
                  </div>
                  <p className="text-yellow-700 mt-1 text-xs sm:text-sm">
                    Your subscription will expire in {daysUntilExpiry} days. Renew now to avoid service interruption.
                  </p>
                </div>
              )}

              {/* Current Plan Features */}
              {currentPlan && (
                <div className="bg-gray-50 rounded-lg p-3 sm:p-4">
                  <h4 className="font-medium mb-3 text-sm sm:text-base">Your Plan Features</h4>
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
                    {currentPlan.features.map((feature, index) => (
                      <div key={index} className="flex items-center gap-2">
                        <CheckCircle className="h-3 w-3 sm:h-4 sm:w-4 text-green-500 flex-shrink-0" />
                        <span className="text-xs sm:text-sm">{feature}</span>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              <div className="flex flex-col sm:flex-row flex-wrap gap-2 sm:gap-3">
                <Button
                  onClick={() => setShowUpgradeModal(true)}
                  className="flex items-center gap-2 bg-red-600 hover:bg-red-700 justify-center"
                  disabled={processing}
                >
                  <TrendingUp className="h-4 w-4" />
                  <span className="hidden sm:inline">{subscription.status === 'ACTIVE' ? 'Upgrade Plan' : 'Reactivate'}</span>
                  <span className="sm:hidden">{subscription.status === 'ACTIVE' ? 'Upgrade' : 'Reactivate'}</span>
                </Button>
                <Button
                  variant="outline"
                  onClick={() => router.push('/pricing')}
                  className="flex items-center gap-2 justify-center"
                >
                  <Eye className="h-4 w-4" />
                  <span className="hidden sm:inline">View All Plans</span>
                  <span className="sm:hidden">View Plans</span>
                </Button>
                <Button
                  variant="outline"
                  onClick={loadDashboardData}
                  className="flex items-center gap-2 justify-center"
                  disabled={loading}
                >
                  <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
                  <span className="hidden sm:inline">Refresh</span>
                  <span className="sm:hidden">Refresh</span>
                </Button>
                {subscription.status === 'ACTIVE' && (
                  <Button
                    variant="outline"
                    onClick={handleCancelSubscription}
                    disabled={processing}
                    className="text-red-600 hover:text-red-700 justify-center"
                  >
                    <span className="hidden sm:inline">Cancel Subscription</span>
                    <span className="sm:hidden">Cancel</span>
                  </Button>
                )}
              </div>
            </div>
          ) : (
            <div className="text-center py-6 sm:py-8">
              <div className="text-gray-500 mb-4 sm:mb-6">
                <CreditCard className="h-12 w-12 sm:h-16 sm:w-16 mx-auto mb-3 sm:mb-4 opacity-50" />
                <h3 className="text-base sm:text-lg font-medium text-gray-900 mb-2">No Active Subscription</h3>
                <p className="text-sm sm:text-base text-gray-600">Choose a subscription plan to start showcasing your business</p>
              </div>
              <div className="flex flex-col sm:flex-row gap-2 sm:gap-3 justify-center">
                <Button
                  onClick={() => setShowUpgradeModal(true)}
                  className="flex items-center gap-2 bg-red-600 hover:bg-red-700 justify-center"
                >
                  <Star className="h-4 w-4" />
                  <span className="hidden sm:inline">Choose a Plan</span>
                  <span className="sm:hidden">Choose Plan</span>
                </Button>
                <Button
                  variant="outline"
                  onClick={() => router.push('/pricing')}
                  className="flex items-center gap-2 justify-center"
                >
                  <Eye className="h-4 w-4" />
                  <span className="hidden sm:inline">View All Plans</span>
                  <span className="sm:hidden">View Plans</span>
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Available Plans */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg sm:text-xl">Available Plans</CardTitle>
          <CardDescription className="text-sm sm:text-base">
            Compare and upgrade to a plan that suits your business needs
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4">
            {availablePlans.map((plan) => (
              <div
                key={plan.id}
                className={`border rounded-lg p-3 sm:p-4 transition-all duration-200 ${
                  plan.id === subscription?.planId
                    ? 'border-red-500 bg-red-50'
                    : 'border-gray-200 hover:border-gray-300'
                } ${plan.isPopular ? 'ring-2 ring-red-500' : ''}`}
              >
                {plan.isPopular && (
                  <div className="text-center mb-2">
                    <Badge className="bg-red-600 text-white text-xs">
                      <Crown className="h-3 w-3 mr-1" />
                      Most Popular
                    </Badge>
                  </div>
                )}

                <div className="text-center">
                  <h3 className="font-semibold text-sm sm:text-base">{plan.name}</h3>
                  <div className="text-xl sm:text-2xl font-bold mt-2">
                    {formatPrice(plan.price, plan.currency)}
                  </div>
                  <div className="text-xs sm:text-sm text-gray-600">/{getDurationText(plan.duration)}</div>

                  {plan.discountPercentage && (
                    <div className="bg-green-100 text-green-800 px-2 py-1 rounded text-xs sm:text-sm mt-2">
                      {plan.discountPercentage}% OFF
                    </div>
                  )}
                </div>

                <ul className="mt-3 sm:mt-4 space-y-1 text-xs sm:text-sm">
                  {plan.features.slice(0, 4).map((feature, index) => (
                    <li key={index} className="flex items-start">
                      <CheckCircle className="h-3 w-3 text-green-500 mt-0.5 mr-2 flex-shrink-0" />
                      <span>{feature}</span>
                    </li>
                  ))}
                  {plan.features.length > 4 && (
                    <li className="text-gray-500">+{plan.features.length - 4} more features</li>
                  )}
                </ul>

                {plan.id === subscription?.planId ? (
                  <div className="mt-3 sm:mt-4 text-center">
                    <Badge className="bg-red-100 text-red-800 text-xs">
                      <CheckCircle className="h-3 w-3 mr-1" />
                      Current Plan
                    </Badge>
                  </div>
                ) : (
                  <Button
                    className={`w-full mt-3 sm:mt-4 ${plan.isPopular ? 'bg-red-600 hover:bg-red-700' : ''}`}
                    variant={plan.isPopular ? "default" : "outline"}
                    onClick={() => handleUpgrade(plan)}
                    disabled={processing}
                    size="sm"
                  >
                    {processing ? 'Processing...' : subscription ? 'Switch to This Plan' : 'Select Plan'}
                  </Button>
                )}
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Payment History */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-lg sm:text-xl">
            <History className="h-5 w-5" />
            Payment History
          </CardTitle>
          <CardDescription className="text-sm sm:text-base">
            View your subscription payment history and transactions
          </CardDescription>
        </CardHeader>
        <CardContent>
          {paymentHistory.length > 0 ? (
            <div className="space-y-3">
              {paymentHistory.slice(0, 5).map((payment) => (
                <div key={payment.id} className="flex flex-col sm:flex-row sm:items-center sm:justify-between p-3 border rounded-lg gap-2 sm:gap-0">
                  <div>
                    <div className="font-medium text-sm sm:text-base">Payment #{payment.transactionId}</div>
                    <div className="text-xs sm:text-sm text-gray-600">
                      {new Date(payment.paymentDate).toLocaleDateString()} • {formatPrice(payment.amount, payment.currency)}
                    </div>
                    <div className="text-xs text-gray-500">
                      {payment.paymentMethod}
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    {getStatusBadge(payment.status)}
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-6 sm:py-8 text-gray-500">
              <History className="h-8 w-8 sm:h-12 sm:w-12 mx-auto mb-2 opacity-50" />
              <p className="text-sm sm:text-base">No payment history available</p>
              <p className="text-xs sm:text-sm">Your payment history will appear here after your first subscription</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Upgrade Modal */}
      {showUpgradeModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-2 sm:p-4">
          <div className="bg-white rounded-lg max-w-4xl w-full max-h-[95vh] sm:max-h-[90vh] overflow-y-auto">
            <div className="p-4 sm:p-6 border-b">
              <div className="flex items-center justify-between">
                <div>
                  <h2 className="text-lg sm:text-2xl font-bold">
                    {subscription ? "Upgrade Your Plan" : "Choose Your Plan"}
                  </h2>
                  <p className="text-sm sm:text-base text-gray-600 mt-1">
                    {subscription ? "Select a new plan to upgrade your subscription" : "Select the perfect plan to grow your wedding business"}
                  </p>
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowUpgradeModal(false)}
                >
                  ✕
                </Button>
              </div>
            </div>

            <div className="p-4 sm:p-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-4 sm:gap-6">
                {availablePlans.map((plan) => (
                  <div
                    key={plan.id}
                    className={`border rounded-lg p-4 sm:p-6 transition-all duration-200 ${
                      plan.id === subscription?.planId
                        ? 'border-red-500 bg-red-50'
                        : 'border-gray-200 hover:border-red-300'
                    } ${plan.isPopular ? 'ring-2 ring-red-500' : ''}`}
                  >
                    {plan.isPopular && (
                      <div className="text-center mb-3 sm:mb-4">
                        <Badge className="bg-red-600 text-white text-xs">
                          <Crown className="h-3 w-3 mr-1" />
                          Most Popular
                        </Badge>
                      </div>
                    )}

                    <div className="text-center mb-3 sm:mb-4">
                      <h3 className="text-lg sm:text-xl font-semibold">{plan.name}</h3>
                      <p className="text-xs sm:text-sm text-gray-600 mt-1">{plan.description}</p>
                      <div className="text-2xl sm:text-3xl font-bold mt-2 sm:mt-3">
                        {formatPrice(plan.price, plan.currency)}
                      </div>
                      <div className="text-xs sm:text-sm text-gray-600">/{getDurationText(plan.duration)}</div>

                      {plan.discountPercentage && (
                        <div className="bg-green-100 text-green-800 px-2 sm:px-3 py-1 rounded-full text-xs sm:text-sm mt-2 inline-block">
                          {plan.discountPercentage}% OFF
                        </div>
                      )}
                    </div>

                    <ul className="space-y-1 sm:space-y-2 text-xs sm:text-sm mb-4 sm:mb-6">
                      {plan.features.map((feature, index) => (
                        <li key={index} className="flex items-start">
                          <CheckCircle className="h-3 w-3 sm:h-4 sm:w-4 text-green-500 mt-0.5 mr-2 flex-shrink-0" />
                          <span>{feature}</span>
                        </li>
                      ))}
                    </ul>

                    {plan.id === subscription?.planId ? (
                      <div className="text-center">
                        <Badge className="bg-red-100 text-red-800 px-3 sm:px-4 py-1 sm:py-2 text-xs">
                          <CheckCircle className="h-3 w-3 sm:h-4 sm:w-4 mr-1" />
                          Current Plan
                        </Badge>
                      </div>
                    ) : (
                      <Button
                        className={`w-full ${plan.isPopular ? 'bg-red-600 hover:bg-red-700' : ''}`}
                        variant={plan.isPopular ? "default" : "outline"}
                        onClick={() => handleUpgrade(plan)}
                        disabled={processing}
                        size="sm"
                      >
                        {processing ? 'Processing...' : subscription ? 'Switch to This Plan' : 'Select Plan'}
                      </Button>
                    )}
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
