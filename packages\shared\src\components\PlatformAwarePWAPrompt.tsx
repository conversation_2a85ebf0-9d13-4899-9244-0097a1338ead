// Platform-aware PWA install prompt that works on both web and mobile

import React from 'react';
import { Platform } from '../utils/platform';

// Props for the PWA prompt component
interface PWAPromptProps {
  onInstall?: () => void;
  onDismiss?: () => void;
  className?: string;
}

// Web-specific PWA prompt component
const WebPWAPrompt: React.FC<PWAPromptProps> = ({ onInstall, onDismiss, className }) => {
  // This would contain the actual PWA prompt logic for web
  // For now, we'll return null since the actual implementation is in the web app
  return null;
};

// Mobile-specific component (PWA doesn't apply to native mobile apps)
const MobilePWAPrompt: React.FC<PWAPromptProps> = () => {
  // PWA prompts don't make sense in React Native apps
  return null;
};

// Platform-aware PWA prompt that renders the appropriate component
const PlatformAwarePWAPrompt: React.FC<PWAPromptProps> = (props) => {
  // Only render PWA prompt on web platforms
  if (Platform.isWeb) {
    return <WebPWAPrompt {...props} />;
  }
  
  // Don't render anything on mobile platforms
  return null;
};

export default PlatformAwarePWAPrompt;
