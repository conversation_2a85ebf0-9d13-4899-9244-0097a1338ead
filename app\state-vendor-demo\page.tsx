'use client'

import { LayoutWrapper } from "@/components/layout-wrapper"
import { StateSpecificVendorDemo } from "@/components/state-specific-vendor-demo"

export default function StateVendorDemoPage() {
  return (
    <LayoutWrapper>
      <div className="min-h-screen bg-gradient-to-br from-background to-muted/20 py-8">
        <div className="container mx-auto px-4">
          <div className="text-center mb-8">
            <h1 className="text-4xl font-bold text-gray-900 mb-4">
              State-Specific Vendor Menu System
            </h1>
            <p className="text-xl text-gray-600 max-w-4xl mx-auto">
              Experience how vendor categories and services dynamically change based on the selected state, 
              showcasing regional specialties, cultural preferences, and traditional services.
            </p>
          </div>
          
          <StateSpecificVendorDemo />
          
          <div className="mt-12 text-center">
            <div className="bg-gradient-to-r from-green-50 to-blue-50 border border-green-200 rounded-lg p-6 max-w-5xl mx-auto">
              <h2 className="text-2xl font-bold text-green-900 mb-4">
                🎯 Smart State-Based Vendor Recommendations
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-left">
                <div>
                  <h3 className="font-semibold text-green-900 mb-3">✨ Key Features:</h3>
                  <ul className="text-sm text-green-800 space-y-2">
                    <li>• <strong>Dynamic Menu Updates:</strong> Vendor categories change based on selected state</li>
                    <li>• <strong>Cultural Specialties:</strong> State-specific traditional services highlighted</li>
                    <li>• <strong>Regional Preferences:</strong> Popular local services prioritized</li>
                    <li>• <strong>Traditional Wear:</strong> State-appropriate bridal and groom wear suggestions</li>
                    <li>• <strong>Cultural Context:</strong> Services relevant to local customs and traditions</li>
                    <li>• <strong>Language Integration:</strong> Works with multi-language support</li>
                  </ul>
                </div>
                <div>
                  <h3 className="font-semibold text-green-900 mb-3">🌟 State-Specific Examples:</h3>
                  <ul className="text-sm text-green-800 space-y-2">
                    <li>• <strong>Tamil Nadu:</strong> Kanchipuram sarees, Temple photography, Carnatic music</li>
                    <li>• <strong>Karnataka:</strong> Mysore silk, Palace venues, Yakshagana performances</li>
                    <li>• <strong>Kerala:</strong> Kasavu sarees, Backwater venues, Traditional boat photography</li>
                    <li>• <strong>Andhra Pradesh:</strong> Pochampally sarees, Temple venues, Telugu traditions</li>
                    <li>• <strong>Maharashtra:</strong> Nauvari sarees, Heritage venues, Marathi customs</li>
                    <li>• <strong>Gujarat:</strong> Traditional Gujarati wear, Heritage venues, Garba photography</li>
                  </ul>
                </div>
              </div>
              
              <div className="mt-6 p-4 bg-white rounded-lg border border-green-300">
                <h3 className="font-semibold text-green-900 mb-2">🔧 Implementation Benefits:</h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-green-800">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-600">6+</div>
                    <div>States Configured</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-600">50+</div>
                    <div>Cultural Specialties</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-600">100%</div>
                    <div>Culturally Relevant</div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="mt-8 text-center">
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 max-w-4xl mx-auto">
              <h2 className="text-xl font-bold text-blue-900 mb-3">
                🚀 How It Works in the Header
              </h2>
              <p className="text-blue-800 mb-4">
                When users select a state from the header dropdown, the vendor submenu automatically updates to show:
              </p>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-blue-800">
                <div>
                  <h4 className="font-semibold mb-2">📍 State-Specific Services:</h4>
                  <ul className="text-left space-y-1">
                    <li>• Traditional photography styles</li>
                    <li>• Regional makeup techniques</li>
                    <li>• Cultural ceremony specialists</li>
                    <li>• Local traditional wear</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-semibold mb-2">🎭 Cultural Integration:</h4>
                  <ul className="text-left space-y-1">
                    <li>• Traditional music & dance</li>
                    <li>• Regional cuisine specialists</li>
                    <li>• Cultural decoration styles</li>
                    <li>• Local customs & rituals</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </LayoutWrapper>
  )
}
