'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { 
  Search, 
  Eye, 
  Calendar, 
  User, 
  Mail, 
  Phone, 
  MessageSquare,
  Clock,
  AlertCircle,
  CheckCircle,
  XCircle,
  Star,
  RefreshCw,
  Loader2,
  Building
} from 'lucide-react'
import { toast } from 'sonner'
import { inquiryService } from '@/lib/services/inquiryService'
import { useAuth } from '@/contexts/AuthContext'

interface UserInquiry {
  id: string
  vendorUserId: string
  vendorId: string
  vendorName: string
  customerUserId?: string
  customerName: string
  customerEmail: string
  customerPhone?: string
  eventDate?: string
  message: string
  inquiryType: 'VENDOR_INQUIRY' | 'VENUE_INQUIRY' | 'SERVICE_QUOTE' | 'AVAILABILITY_CHECK' | 'GENERAL_QUESTION' | 'BOOKING_REQUEST'
  status: 'NEW' | 'CONTACTED' | 'QUOTED' | 'NEGOTIATING' | 'CONFIRMED' | 'COMPLETED' | 'CANCELLED' | 'EXPIRED'
  priority?: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT'
  budget?: string
  guestCount?: string
  venue?: string
  additionalServices?: string[]
  preferredContactTime?: string
  responseMessage?: string
  respondedAt?: string
  createdAt: string
  updatedAt: string
}

export default function MyInquiries() {
  const { user } = useAuth()
  const [inquiries, setInquiries] = useState<UserInquiry[]>([])
  const [filteredInquiries, setFilteredInquiries] = useState<UserInquiry[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState('ALL')
  const [typeFilter, setTypeFilter] = useState('ALL')
  const [selectedInquiry, setSelectedInquiry] = useState<UserInquiry | null>(null)
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false)

  // Load user's inquiries
  const loadMyInquiries = async () => {
    if (!user?.userId) {
      console.log('No user ID available')
      return
    }

    try {
      setLoading(true)
      console.log('Loading inquiries for user:', user.userId)
      
      const result = await inquiryService.listInquiries({ 
        customerUserId: user.userId,
        limit: 100 
      })
      
      console.log('User inquiry loading result:', result)
      
      if (result && result.items) {
        setInquiries(result.items)
        setFilteredInquiries(result.items)
        console.log('Loaded user inquiries:', result.items.length)
      } else {
        console.error('Invalid result format:', result)
        toast.error('Failed to load your inquiries', {
          description: 'Invalid response format'
        })
      }
    } catch (error) {
      console.error('Error loading user inquiries:', error)
      toast.error('Error loading your inquiries', {
        description: error instanceof Error ? error.message : 'An unexpected error occurred'
      })
    } finally {
      setLoading(false)
    }
  }

  // Filter inquiries
  useEffect(() => {
    let filtered = inquiries

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter(inquiry =>
        inquiry.vendorName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        inquiry.message.toLowerCase().includes(searchTerm.toLowerCase()) ||
        inquiry.venue?.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    // Status filter
    if (statusFilter !== 'ALL') {
      filtered = filtered.filter(inquiry => inquiry.status === statusFilter)
    }

    // Type filter
    if (typeFilter !== 'ALL') {
      filtered = filtered.filter(inquiry => inquiry.inquiryType === typeFilter)
    }

    setFilteredInquiries(filtered)
  }, [inquiries, searchTerm, statusFilter, typeFilter])

  // Load inquiries on component mount
  useEffect(() => {
    if (user?.userId) {
      loadMyInquiries()
    }
  }, [user?.userId])

  // Handle view inquiry
  const handleViewInquiry = (inquiry: UserInquiry) => {
    setSelectedInquiry(inquiry)
    setIsViewDialogOpen(true)
  }



  // Get status badge color
  const getStatusBadgeColor = (status: string) => {
    switch (status) {
      case 'NEW': return 'bg-blue-100 text-blue-800'
      case 'CONTACTED': return 'bg-yellow-100 text-yellow-800'
      case 'QUOTED': return 'bg-purple-100 text-purple-800'
      case 'NEGOTIATING': return 'bg-orange-100 text-orange-800'
      case 'CONFIRMED': return 'bg-green-100 text-green-800'
      case 'COMPLETED': return 'bg-gray-100 text-gray-800'
      case 'CANCELLED': return 'bg-red-100 text-red-800'
      case 'EXPIRED': return 'bg-gray-100 text-gray-600'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  // Get status icon
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'NEW': return <AlertCircle className="h-4 w-4" />
      case 'CONTACTED': return <Clock className="h-4 w-4" />
      case 'QUOTED': return <MessageSquare className="h-4 w-4" />
      case 'NEGOTIATING': return <MessageSquare className="h-4 w-4" />
      case 'CONFIRMED': return <CheckCircle className="h-4 w-4" />
      case 'COMPLETED': return <CheckCircle className="h-4 w-4" />
      case 'CANCELLED': return <XCircle className="h-4 w-4" />
      case 'EXPIRED': return <XCircle className="h-4 w-4" />
      default: return <AlertCircle className="h-4 w-4" />
    }
  }

  // Get priority badge color
  const getPriorityBadgeColor = (priority?: string) => {
    switch (priority) {
      case 'URGENT': return 'bg-red-100 text-red-800'
      case 'HIGH': return 'bg-orange-100 text-orange-800'
      case 'MEDIUM': return 'bg-yellow-100 text-yellow-800'
      case 'LOW': return 'bg-green-100 text-green-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  // Calculate stats
  const stats = {
    total: inquiries.length,
    new: inquiries.filter(i => i.status === 'NEW').length,
    inProgress: inquiries.filter(i => ['CONTACTED', 'QUOTED', 'NEGOTIATING'].includes(i.status)).length,
    completed: inquiries.filter(i => ['COMPLETED', 'CONFIRMED'].includes(i.status)).length,
    pending: inquiries.filter(i => ['NEW', 'CONTACTED'].includes(i.status)).length
  }

  if (!user) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Authentication Required</h2>
          <p className="text-gray-600">Please log in to view your inquiries.</p>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8 space-y-8">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">My Inquiries</h1>
          <p className="text-gray-600 mt-1">Track the status of your vendor and venue inquiries</p>
        </div>
        <div className="mt-4 sm:mt-0 flex items-center gap-3">
          <Button
            onClick={loadMyInquiries}
            disabled={loading}
            variant="outline"
            size="sm"
            className="flex items-center gap-2"
          >
            <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
 
          <Badge variant="outline" className="text-sm">
            {filteredInquiries.length} of {inquiries.length} inquiries
          </Badge>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Inquiries</CardTitle>
            <MessageSquare className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.total}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending</CardTitle>
            <Clock className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">{stats.pending}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">In Progress</CardTitle>
            <MessageSquare className="h-4 w-4 text-yellow-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-600">{stats.inProgress}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Completed</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{stats.completed}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">New</CardTitle>
            <AlertCircle className="h-4 w-4 text-orange-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-600">{stats.new}</div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Filters</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <Label htmlFor="search">Search</Label>
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  id="search"
                  placeholder="Search by vendor, message, venue..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div>
              <Label htmlFor="status">Status</Label>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="All Statuses" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="ALL">All Statuses</SelectItem>
                  <SelectItem value="NEW">New</SelectItem>
                  <SelectItem value="CONTACTED">Contacted</SelectItem>
                  <SelectItem value="QUOTED">Quoted</SelectItem>
                  <SelectItem value="NEGOTIATING">Negotiating</SelectItem>
                  <SelectItem value="CONFIRMED">Confirmed</SelectItem>
                  <SelectItem value="COMPLETED">Completed</SelectItem>
                  <SelectItem value="CANCELLED">Cancelled</SelectItem>
                  <SelectItem value="EXPIRED">Expired</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="type">Type</Label>
              <Select value={typeFilter} onValueChange={setTypeFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="All Types" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="ALL">All Types</SelectItem>
                  <SelectItem value="VENDOR_INQUIRY">Vendor Inquiry</SelectItem>
                  <SelectItem value="VENUE_INQUIRY">Venue Inquiry</SelectItem>
                  <SelectItem value="SERVICE_QUOTE">Service Quote</SelectItem>
                  <SelectItem value="AVAILABILITY_CHECK">Availability Check</SelectItem>
                  <SelectItem value="GENERAL_QUESTION">General Question</SelectItem>
                  <SelectItem value="BOOKING_REQUEST">Booking Request</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Inquiries Table */}
      <Card>
        <CardHeader>
          <CardTitle>Your Inquiries</CardTitle>
          <CardDescription>
            Track the status and responses of your vendor and venue inquiries
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="h-8 w-8 animate-spin" />
              <span className="ml-2">Loading your inquiries...</span>
            </div>
          ) : filteredInquiries.length === 0 ? (
            <div className="text-center py-8">
              <MessageSquare className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No inquiries found</h3>
              <p className="text-gray-600">
                {searchTerm || statusFilter !== 'ALL' || typeFilter !== 'ALL'
                  ? 'Try adjusting your filters to see more results.'
                  : 'You haven\'t submitted any inquiries yet. Start by browsing vendors and venues!'}
              </p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Vendor/Venue</TableHead>
                    <TableHead>Type</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Event Date</TableHead>
                    <TableHead>Submitted</TableHead>
                    <TableHead>Last Updated</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredInquiries.map((inquiry) => (
                    <TableRow key={inquiry.id}>
                      <TableCell>
                        <div className="flex items-center">
                          <Building className="h-4 w-4 mr-2 text-gray-500" />
                          <div>
                            <div className="font-medium">{inquiry.vendorName}</div>
                            {inquiry.venue && (
                              <div className="text-sm text-gray-600">{inquiry.venue}</div>
                            )}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline" className="text-xs">
                          {inquiry.inquiryType.replace('_', ' ')}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center">
                          {getStatusIcon(inquiry.status)}
                          <Badge className={`ml-2 text-xs ${getStatusBadgeColor(inquiry.status)}`}>
                            {inquiry.status}
                          </Badge>
                        </div>
                      </TableCell>
                      <TableCell>
                        {inquiry.eventDate ? (
                          <div className="flex items-center text-sm">
                            <Calendar className="h-4 w-4 mr-1" />
                            {new Date(inquiry.eventDate).toLocaleDateString()}
                          </div>
                        ) : (
                          <span className="text-gray-400">-</span>
                        )}
                      </TableCell>
                      <TableCell>
                        <div className="text-sm">
                          {new Date(inquiry.createdAt).toLocaleDateString()}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="text-sm">
                          {new Date(inquiry.updatedAt).toLocaleDateString()}
                        </div>
                      </TableCell>
                      <TableCell>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleViewInquiry(inquiry)}
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* View Inquiry Dialog */}
      <Dialog open={isViewDialogOpen} onOpenChange={setIsViewDialogOpen}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Inquiry Details</DialogTitle>
            <DialogDescription>
              View your inquiry details and vendor response
            </DialogDescription>
          </DialogHeader>
          {selectedInquiry && (
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h3 className="text-lg font-semibold mb-3">Vendor Information</h3>
                  <div className="space-y-2">
                    <div className="flex items-center">
                      <Building className="h-4 w-4 mr-2 text-gray-500" />
                      <span className="font-medium">{selectedInquiry.vendorName}</span>
                    </div>
                    <div>
                      <span className="text-sm text-gray-600">Vendor ID: {selectedInquiry.vendorId}</span>
                    </div>
                  </div>
                </div>
                <div>
                  <h3 className="text-lg font-semibold mb-3">Inquiry Status</h3>
                  <div className="space-y-2">
                    <div className="flex items-center">
                      {getStatusIcon(selectedInquiry.status)}
                      <Badge className={`ml-2 ${getStatusBadgeColor(selectedInquiry.status)}`}>
                        {selectedInquiry.status}
                      </Badge>
                    </div>
                    {selectedInquiry.priority && (
                      <div className="flex items-center">
                        <Star className="h-4 w-4 mr-2 text-gray-500" />
                        <Badge className={getPriorityBadgeColor(selectedInquiry.priority)}>
                          {selectedInquiry.priority} Priority
                        </Badge>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              <div>
                <h3 className="text-lg font-semibold mb-3">Inquiry Details</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label>Type</Label>
                    <Badge variant="outline" className="mt-1">
                      {selectedInquiry.inquiryType.replace('_', ' ')}
                    </Badge>
                  </div>
                  {selectedInquiry.eventDate && (
                    <div>
                      <Label>Event Date</Label>
                      <div className="flex items-center mt-1">
                        <Calendar className="h-4 w-4 mr-2" />
                        {new Date(selectedInquiry.eventDate).toLocaleDateString()}
                      </div>
                    </div>
                  )}
                  {selectedInquiry.budget && (
                    <div>
                      <Label>Budget</Label>
                      <p className="mt-1">{selectedInquiry.budget}</p>
                    </div>
                  )}
                  {selectedInquiry.guestCount && (
                    <div>
                      <Label>Guest Count</Label>
                      <p className="mt-1">{selectedInquiry.guestCount}</p>
                    </div>
                  )}
                  {selectedInquiry.venue && (
                    <div>
                      <Label>Venue</Label>
                      <p className="mt-1">{selectedInquiry.venue}</p>
                    </div>
                  )}
                  {selectedInquiry.preferredContactTime && (
                    <div>
                      <Label>Preferred Contact Time</Label>
                      <p className="mt-1">{selectedInquiry.preferredContactTime}</p>
                    </div>
                  )}
                </div>
              </div>

              <div>
                <Label>Your Message</Label>
                <div className="mt-2 p-3 bg-gray-50 rounded-lg">
                  <p className="whitespace-pre-wrap">{selectedInquiry.message}</p>
                </div>
              </div>

              {selectedInquiry.additionalServices && selectedInquiry.additionalServices.length > 0 && (
                <div>
                  <Label>Additional Services Requested</Label>
                  <div className="mt-2 flex flex-wrap gap-2">
                    {selectedInquiry.additionalServices.map((service, index) => (
                      <Badge key={index} variant="outline">{service}</Badge>
                    ))}
                  </div>
                </div>
              )}

              {selectedInquiry.responseMessage && (
                <div>
                  <Label>Vendor Response</Label>
                  <div className="mt-2 p-3 bg-blue-50 rounded-lg border-l-4 border-blue-400">
                    <p className="whitespace-pre-wrap">{selectedInquiry.responseMessage}</p>
                    {selectedInquiry.respondedAt && (
                      <p className="text-sm text-gray-600 mt-2">
                        Responded on: {new Date(selectedInquiry.respondedAt).toLocaleString()}
                      </p>
                    )}
                  </div>
                </div>
              )}

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-600 pt-4 border-t">
                <div>
                  <Label>Submitted On</Label>
                  <p className="mt-1">{new Date(selectedInquiry.createdAt).toLocaleString()}</p>
                </div>
                <div>
                  <Label>Last Updated</Label>
                  <p className="mt-1">{new Date(selectedInquiry.updatedAt).toLocaleString()}</p>
                </div>
              </div>

              {/* Status Timeline */}
              <div>
                <Label>Status Timeline</Label>
                <div className="mt-3 space-y-2">
                  <div className="flex items-center text-sm">
                    <div className="w-2 h-2 bg-blue-500 rounded-full mr-3"></div>
                    <span className="text-gray-600">Inquiry submitted</span>
                    <span className="ml-auto text-gray-500">
                      {new Date(selectedInquiry.createdAt).toLocaleDateString()}
                    </span>
                  </div>

                  {selectedInquiry.status !== 'NEW' && (
                    <div className="flex items-center text-sm">
                      <div className="w-2 h-2 bg-yellow-500 rounded-full mr-3"></div>
                      <span className="text-gray-600">Status updated to {selectedInquiry.status}</span>
                      <span className="ml-auto text-gray-500">
                        {new Date(selectedInquiry.updatedAt).toLocaleDateString()}
                      </span>
                    </div>
                  )}

                  {selectedInquiry.responseMessage && (
                    <div className="flex items-center text-sm">
                      <div className="w-2 h-2 bg-green-500 rounded-full mr-3"></div>
                      <span className="text-gray-600">Vendor responded</span>
                      <span className="ml-auto text-gray-500">
                        {selectedInquiry.respondedAt ?
                          new Date(selectedInquiry.respondedAt).toLocaleDateString() :
                          new Date(selectedInquiry.updatedAt).toLocaleDateString()
                        }
                      </span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  )
}
