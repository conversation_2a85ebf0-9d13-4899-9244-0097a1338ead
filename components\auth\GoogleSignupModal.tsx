"use client"

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogTitle } from "@/components/ui/dialog"
import { useAuth } from "@/contexts/AuthContext"
import GoogleSignupFlow from "./GoogleSignupFlow"

interface GoogleSignupModalProps {
  isOpen: boolean
  onClose: () => void
  onComplete?: () => void
}

export default function GoogleSignupModal({ isOpen, onClose, onComplete }: GoogleSignupModalProps) {
  const { checkAuthState } = useAuth()

  const handleComplete = async () => {
    try {
      // Refresh the complete authentication state to pick up the new user
      await checkAuthState()

      onClose()
      if (onComplete) {
        onComplete()
      }
    } catch (error) {
      console.error('Error refreshing auth state after Google signup:', error)
      // Still close the modal even if refresh fails
      onClose()
      if (onComplete) {
        onComplete()
      }
    }
  }

  if (!isOpen) return null

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <GoogleSignupFlow onComplete={handleComplete} />
      </DialogContent>
    </Dialog>
  )
}
