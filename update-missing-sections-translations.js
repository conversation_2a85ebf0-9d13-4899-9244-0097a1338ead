const fs = require('fs');
const path = require('path');

// Complete translations for missing sections
const sectionTranslations = {
  en: {
    statistics: {
      title: "Trusted by Thousands of Couples",
      subtitle: "Join the growing community of happy couples who found their perfect wedding vendors through BookmyFestive",
      happyCouples: "Happy Couples",
      verifiedVendors: "Verified Vendors", 
      weddingVenues: "Wedding Venues",
      satisfactionRate: "Satisfaction Rate"
    },
    whyChooseUs: {
      title: "Why Choose BookmyFestive?",
      subtitle: "Experience the difference with India's most trusted wedding planning platform",
      verifiedVendors: {
        title: "Verified Vendors",
        description: "All our vendors are thoroughly verified with genuine reviews and quality assurance"
      },
      bestPrices: {
        title: "Best Prices",
        description: "Get competitive pricing and exclusive deals from top wedding vendors"
      },
      support: {
        title: "24/7 Support", 
        description: "Dedicated wedding planning support team available round the clock for assistance"
      }
    },
    destinations: {
      title: "Popular Festive Destinations",
      subtitle: "Discover the most sought-after wedding destinations across {stateName}",
      exploreAll: "Explore All Cities"
    }
  },
  
  ta: {
    statistics: {
      title: "ஆயிரக்கணக்கான ஜோடிகளின் நம்பிக்கை",
      subtitle: "திருமணம் 360 மூலம் தங்கள் சரியான திருமண விற்பனையாளர்களைக் கண்டறிந்த மகிழ்ச்சியான ஜோடிகளின் வளர்ந்து வரும் சமூகத்தில் சேருங்கள்",
      happyCouples: "மகிழ்ச்சியான ஜோடிகள்",
      verifiedVendors: "சரிபார்க்கப்பட்ட விற்பனையாளர்கள்",
      weddingVenues: "திருமண அரங்குகள்",
      satisfactionRate: "திருப்தி விகிதம்"
    },
    whyChooseUs: {
      title: "ஏன் திருமணம் 360 ஐ தேர்வு செய்ய வேண்டும்?",
      subtitle: "இந்தியாவின் மிகவும் நம்பகமான திருமண திட்டமிடல் தளத்துடன் வித்தியாசத்தை அனுபவிக்கவும்",
      verifiedVendors: {
        title: "சரிபார்க்கப்பட்ட விற்பனையாளர்கள்",
        description: "எங்கள் அனைத்து விற்பனையாளர்களும் உண்மையான மதிப்புரைகள் மற்றும் தர உத்தரவாதத்துடன் முழுமையாக சரிபார்க்கப்படுகிறார்கள்"
      },
      bestPrices: {
        title: "சிறந்த விலைகள்",
        description: "சிறந்த திருமண விற்பனையாளர்களிடமிருந்து போட்டி விலை மற்றும் பிரத்யேக ஒப்பந்தங்களைப் பெறுங்கள்"
      },
      support: {
        title: "24/7 ஆதரவு",
        description: "உதவிக்காக 24 மணி நேரமும் கிடைக்கும் அர்ப்பணிப்புள்ள திருமண திட்டமிடல் ஆதரவு குழு"
      }
    },
    destinations: {
      title: "பிரபலமான திருமண இடங்கள்",
      subtitle: "{stateName} முழுவதும் மிகவும் விரும்பப்படும் திருமண இடங்களைக் கண்டறியுங்கள்",
      exploreAll: "அனைத்து நகரங்களையும் ஆராயுங்கள்"
    }
  },
  
  hi: {
    statistics: {
      title: "हजारों जोड़ों का भरोसा",
      subtitle: "खुश जोड़ों के बढ़ते समुदाय में शामिल हों जिन्होंने तिरुमणम 360 के माध्यम से अपने सही शादी के विक्रेता पाए",
      happyCouples: "खुश जोड़े",
      verifiedVendors: "सत्यापित विक्रेता",
      weddingVenues: "शादी के स्थान",
      satisfactionRate: "संतुष्टि दर"
    },
    whyChooseUs: {
      title: "तिरुमणम 360 क्यों चुनें?",
      subtitle: "भारत के सबसे भरोसेमंद शादी योजना मंच के साथ अंतर का अनुभव करें",
      verifiedVendors: {
        title: "सत्यापित विक्रेता",
        description: "हमारे सभी विक्रेता वास्तविक समीक्षाओं और गुणवत्ता आश्वासन के साथ पूरी तरह से सत्यापित हैं"
      },
      bestPrices: {
        title: "सर्वोत्तम मूल्य",
        description: "शीर्ष शादी विक्रेताओं से प्रतिस्पर्धी मूल्य और विशेष सौदे प्राप्त करें"
      },
      support: {
        title: "24/7 सहायता",
        description: "सहायता के लिए चौबीसों घंटे उपलब्ध समर्पित शादी योजना सहायता टीम"
      }
    },
    destinations: {
      title: "लोकप्रिय शादी गंतव्य",
      subtitle: "{stateName} भर में सबसे अधिक मांग वाले शादी गंतव्यों की खोज करें",
      exploreAll: "सभी शहरों का अन्वेषण करें"
    }
  },
  
  kn: {
    statistics: {
      title: "ಸಾವಿರಾರು ದಂಪತಿಗಳ ನಂಬಿಕೆ",
      subtitle: "ತಿರುಮಣಂ 360 ಮೂಲಕ ತಮ್ಮ ಪರಿಪೂರ್ಣ ಮದುವೆ ಮಾರಾಟಗಾರರನ್ನು ಕಂಡುಕೊಂಡ ಸಂತೋಷದ ದಂಪತಿಗಳ ಬೆಳೆಯುತ್ತಿರುವ ಸಮುದಾಯದಲ್ಲಿ ಸೇರಿ",
      happyCouples: "ಸಂತೋಷದ ದಂಪತಿಗಳು",
      verifiedVendors: "ಪರಿಶೀಲಿತ ಮಾರಾಟಗಾರರು",
      weddingVenues: "ಮದುವೆ ಸ್ಥಳಗಳು",
      satisfactionRate: "ತೃಪ್ತಿ ದರ"
    },
    whyChooseUs: {
      title: "ತಿರುಮಣಂ 360 ಅನ್ನು ಏಕೆ ಆಯ್ಕೆ ಮಾಡಬೇಕು?",
      subtitle: "ಭಾರತದ ಅತ್ಯಂತ ವಿಶ್ವಾಸಾರ್ಹ ಮದುವೆ ಯೋಜನಾ ವೇದಿಕೆಯೊಂದಿಗೆ ವ್ಯತ್ಯಾಸವನ್ನು ಅನುಭವಿಸಿ",
      verifiedVendors: {
        title: "ಪರಿಶೀಲಿತ ಮಾರಾಟಗಾರರು",
        description: "ನಮ್ಮ ಎಲ್ಲಾ ಮಾರಾಟಗಾರರು ನಿಜವಾದ ವಿಮರ್ಶೆಗಳು ಮತ್ತು ಗುಣಮಟ್ಟದ ಭರವಸೆಯೊಂದಿಗೆ ಸಂಪೂರ್ಣವಾಗಿ ಪರಿಶೀಲಿಸಲ್ಪಟ್ಟಿದ್ದಾರೆ"
      },
      bestPrices: {
        title: "ಅತ್ಯುತ್ತಮ ಬೆಲೆಗಳು",
        description: "ಉನ್ನತ ಮದುವೆ ಮಾರಾಟಗಾರರಿಂದ ಸ್ಪರ್ಧಾತ್ಮಕ ಬೆಲೆ ಮತ್ತು ವಿಶೇಷ ಒಪ್ಪಂದಗಳನ್ನು ಪಡೆಯಿರಿ"
      },
      support: {
        title: "24/7 ಬೆಂಬಲ",
        description: "ಸಹಾಯಕ್ಕಾಗಿ ಗಡಿಯಾರದ ಸುತ್ತ ಲಭ್ಯವಿರುವ ಸಮರ್ಪಿತ ಮದುವೆ ಯೋಜನಾ ಬೆಂಬಲ ತಂಡ"
      }
    },
    destinations: {
      title: "ಜನಪ್ರಿಯ ಮದುವೆ ಗಮ್ಯಸ್ಥಾನಗಳು",
      subtitle: "{stateName} ಉದ್ದಕ್ಕೂ ಅತ್ಯಂತ ಬೇಡಿಕೆಯಿರುವ ಮದುವೆ ಗಮ್ಯಸ್ಥಾನಗಳನ್ನು ಅನ್ವೇಷಿಸಿ",
      exploreAll: "ಎಲ್ಲಾ ನಗರಗಳನ್ನು ಅನ್ವೇಷಿಸಿ"
    }
  },
  
  ml: {
    statistics: {
      title: "ആയിരക്കണക്കിന് ദമ്പതികളുടെ വിശ്വാസം",
      subtitle: "തിരുമണം 360 വഴി തങ്ങളുടെ പരിപൂർണ്ണ വിവാഹ വെണ്ടർമാരെ കണ്ടെത്തിയ സന്തുഷ്ട ദമ്പതികളുടെ വളരുന്ന കമ്മ്യൂണിറ്റിയിൽ ചേരുക",
      happyCouples: "സന്തുഷ്ട ദമ്പതികൾ",
      verifiedVendors: "പരിശോധിച്ച വെണ്ടർമാർ",
      weddingVenues: "വിവാഹ വേദികൾ",
      satisfactionRate: "സംതൃപ്തി നിരക്ക്"
    },
    whyChooseUs: {
      title: "എന്തുകൊണ്ട് തിരുമണം 360 തിരഞ്ഞെടുക്കണം?",
      subtitle: "ഇന്ത്യയിലെ ഏറ്റവും വിശ്വസനീയമായ വിവാഹ ആസൂത്രണ പ്ലാറ്റ്‌ഫോമിനൊപ്പം വ്യത്യാസം അനുഭവിക്കുക",
      verifiedVendors: {
        title: "പരിശോധിച്ച വെണ്ടർമാർ",
        description: "ഞങ്ങളുടെ എല്ലാ വെണ്ടർമാരും യഥാർത്ഥ അവലോകനങ്ങളും ഗുണനിലവാര ഉറപ്പും ഉപയോഗിച്ച് പൂർണ്ണമായും പരിശോധിച്ചവരാണ്"
      },
      bestPrices: {
        title: "മികച്ച വിലകൾ",
        description: "മുൻനിര വിവാഹ വെണ്ടർമാരിൽ നിന്ന് മത്സര വിലയും പ്രത്യേക ഡീലുകളും നേടുക"
      },
      support: {
        title: "24/7 പിന്തുണ",
        description: "സഹായത്തിനായി 24 മണിക്കൂറും ലഭ്യമായ സമർപ്പിത വിവാഹ ആസൂത്രണ പിന്തുണ ടീം"
      }
    },
    destinations: {
      title: "ജനപ്രിയ വിവാഹ ലക്ഷ്യസ്ഥാനങ്ങൾ",
      subtitle: "{stateName} ഉടനീളം ഏറ്റവും ആവശ്യമുള്ള വിവാഹ ലക്ഷ്യസ്ഥാനങ്ങൾ കണ്ടെത്തുക",
      exploreAll: "എല്ലാ നഗരങ്ങളും പര്യവേക്ഷണം ചെയ്യുക"
    }
  },
  
  te: {
    statistics: {
      title: "వేలాది జంటల నమ్మకం",
      subtitle: "తిరుమణం 360 ద్వారా తమ పరిపూర్ణ వివాహ విక్రేతలను కనుగొన్న సంతోషకరమైన జంటల పెరుగుతున్న కమ్యూనిటీలో చేరండి",
      happyCouples: "సంతోషకరమైన జంటలు",
      verifiedVendors: "ధృవీకరించబడిన విక్రేతలు",
      weddingVenues: "వివాహ వేదికలు",
      satisfactionRate: "సంతృప్తి రేటు"
    },
    whyChooseUs: {
      title: "తిరుమణం 360ను ఎందుకు ఎంచుకోవాలి?",
      subtitle: "భారతదేశంలోని అత్యంత విశ్వసనీయ వివాహ ప్రణాళిక వేదికతో తేడాను అనుభవించండి",
      verifiedVendors: {
        title: "ధృవీకరించబడిన విక్రేతలు",
        description: "మా విక్రేతలందరూ నిజమైన సమీక్షలు మరియు నాణ్యత హామీతో పూర్తిగా ధృవీకరించబడ్డారు"
      },
      bestPrices: {
        title: "ఉత్తమ ధరలు",
        description: "అగ్రశ్రేణి వివాహ విక్రేతల నుండి పోటీ ధర మరియు ప్రత్యేక డీల్‌లను పొందండి"
      },
      support: {
        title: "24/7 మద్దతు",
        description: "సహాయం కోసం గడియారం చుట్టూ అందుబాటులో ఉన్న అంకితమైన వివాహ ప్రణాళిక మద్దతు బృందం"
      }
    },
    destinations: {
      title: "ప్రసిద్ధ వివాహ గమ్యస్థానాలు",
      subtitle: "{stateName} అంతటా అత్యంత కోరుకునే వివాహ గమ్యస్థానాలను కనుగొనండి",
      exploreAll: "అన్ని నగరాలను అన్వేషించండి"
    }
  },
  
  mr: {
    statistics: {
      title: "हजारो जोडप्यांचा विश्वास",
      subtitle: "तिरुमणम 360 द्वारे त्यांचे परिपूर्ण लग्न विक्रेते शोधणाऱ्या आनंदी जोडप्यांच्या वाढत्या समुदायात सामील व्हा",
      happyCouples: "आनंदी जोडपे",
      verifiedVendors: "सत्यापित विक्रेते",
      weddingVenues: "लग्न स्थळे",
      satisfactionRate: "समाधान दर"
    },
    whyChooseUs: {
      title: "तिरुमणम 360 का निवडावे?",
      subtitle: "भारतातील सर्वात विश्वसनीय लग्न नियोजन व्यासपीठासह फरक अनुभवा",
      verifiedVendors: {
        title: "सत्यापित विक्रेते",
        description: "आमचे सर्व विक्रेते खऱ्या पुनरावलोकने आणि गुणवत्ता हमीसह पूर्णपणे सत्यापित आहेत"
      },
      bestPrices: {
        title: "सर्वोत्तम किंमती",
        description: "शीर्ष लग्न विक्रेत्यांकडून स्पर्धात्मक किंमत आणि विशेष सौदे मिळवा"
      },
      support: {
        title: "24/7 समर्थन",
        description: "मदतीसाठी चोवीस तास उपलब्ध असणारा समर्पित लग्न नियोजन समर्थन संघ"
      }
    },
    destinations: {
      title: "लोकप्रिय लग्न गंतव्ये",
      subtitle: "{stateName} मध्ये सर्वाधिक मागणी असलेली लग्न गंतव्ये शोधा",
      exploreAll: "सर्व शहरे एक्सप्लोर करा"
    }
  },
  
  gu: {
    statistics: {
      title: "હજારો યુગલોનો વિશ્વાસ",
      subtitle: "તિરુમણમ 360 દ્વારા તેમના સંપૂર્ણ લગ્ન વિક્રેતાઓ શોધનારા ખુશ યુગલોના વધતા સમુદાયમાં જોડાઓ",
      happyCouples: "ખુશ યુગલો",
      verifiedVendors: "ચકાસાયેલા વિક્રેતાઓ",
      weddingVenues: "લગ્ન સ્થળો",
      satisfactionRate: "સંતોષ દર"
    },
    whyChooseUs: {
      title: "તિરુમણમ 360 કેમ પસંદ કરવું?",
      subtitle: "ભારતના સૌથી વિશ્વસનીય લગ્ન આયોજન પ્લેટફોર્મ સાથે તફાવત અનુભવો",
      verifiedVendors: {
        title: "ચકાસાયેલા વિક્રેતાઓ",
        description: "અમારા બધા વિક્રેતાઓ વાસ્તવિક સમીક્ષાઓ અને ગુણવત્તા ખાતરી સાથે સંપૂર્ણપણે ચકાસાયેલા છે"
      },
      bestPrices: {
        title: "શ્રેષ્ઠ કિંમતો",
        description: "ટોચના લગ્ન વિક્રેતાઓ પાસેથી સ્પર્ધાત્મક કિંમત અને વિશેષ સોદા મેળવો"
      },
      support: {
        title: "24/7 સહાય",
        description: "સહાય માટે ચોવીસ કલાક ઉપલબ્ધ સમર્પિત લગ્ન આયોજન સહાય ટીમ"
      }
    },
    destinations: {
      title: "લોકપ્રિય લગ્ન ગંતવ્યો",
      subtitle: "{stateName} સમગ્રમાં સૌથી વધુ માંગવાળા લગ્ન ગંતવ્યો શોધો",
      exploreAll: "બધા શહેરો અન્વેષણ કરો"
    }
  }
};

// Languages to update
const languages = ['en', 'ta', 'hi', 'kn', 'ml', 'te', 'mr', 'gu', 'pa', 'bn', 'or', 'as', 'ur', 'ne'];

// Function to update a language file
function updateLanguageFile(lang) {
  const filePath = path.join(__dirname, 'public', 'locales', lang, 'common.json');
  
  try {
    // Read existing file
    const data = fs.readFileSync(filePath, 'utf8');
    const translations = JSON.parse(data);
    
    // Get section translations for this language (fallback to English)
    const sectionData = sectionTranslations[lang] || sectionTranslations.en;
    
    // Update statistics section
    if (sectionData.statistics) {
      translations.statistics = { ...translations.statistics, ...sectionData.statistics };
    }
    
    // Update whyChooseUs section
    if (sectionData.whyChooseUs) {
      translations.whyChooseUs = { ...translations.whyChooseUs, ...sectionData.whyChooseUs };
    }
    
    // Update destinations section
    if (sectionData.destinations) {
      translations.destinations = { ...translations.destinations, ...sectionData.destinations };
    }
    
    // Write back to file
    fs.writeFileSync(filePath, JSON.stringify(translations, null, 2), 'utf8');
    console.log(`Updated ${lang}/common.json with missing sections`);
    
  } catch (error) {
    console.error(`Error updating ${lang}/common.json:`, error.message);
  }
}

// Update all language files
languages.forEach(updateLanguageFile);

console.log('All missing section translations updated successfully!');
