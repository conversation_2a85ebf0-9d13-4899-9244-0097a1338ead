import React from 'react';
import { TouchableOpacity, View, Text, StyleSheet } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { useNotifications } from '../providers/NotificationProvider';
import { useTheme } from '../providers/ThemeProvider';

interface NotificationBellProps {
  size?: number;
  color?: string;
  showBadge?: boolean;
}

export default function NotificationBell({ 
  size = 24, 
  color, 
  showBadge = true 
}: NotificationBellProps) {
  const navigation = useNavigation();
  const { unreadCount } = useNotifications();
  const { theme } = useTheme();

  const iconColor = color || theme.colors.text;

  const handlePress = () => {
    navigation.navigate('Notifications' as never);
  };

  return (
    <TouchableOpacity 
      style={styles.container} 
      onPress={handlePress}
      activeOpacity={0.7}
    >
      <Ionicons 
        name={unreadCount > 0 ? 'notifications' : 'notifications-outline'} 
        size={size} 
        color={iconColor} 
      />
      
      {showBadge && unreadCount > 0 && (
        <View style={[styles.badge, { backgroundColor: theme.colors.error }]}>
          <Text style={styles.badgeText}>
            {unreadCount > 99 ? '99+' : unreadCount.toString()}
          </Text>
        </View>
      )}
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  container: {
    position: 'relative',
    padding: 4,
  },
  badge: {
    position: 'absolute',
    top: 0,
    right: 0,
    minWidth: 18,
    height: 18,
    borderRadius: 9,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 4,
  },
  badgeText: {
    color: 'white',
    fontSize: 10,
    fontWeight: 'bold',
    textAlign: 'center',
  },
});
