"use client"

import { <PERSON>Header } from "@/components/top-header"
import { <PERSON><PERSON> } from "@/components/header"
import { <PERSON><PERSON> } from "@/components/footer"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Checkbox } from "@/components/ui/checkbox"
import { Separator } from "@/components/ui/separator"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Eye, EyeOff, Mail, Lock, User, Phone, AlertCircle, CheckCircle, RefreshCw, MessageSquare, ArrowLeft } from "lucide-react"
import Link from "next/link"
import { useState, useEffect } from "react"
import { useAuth } from "@/contexts/AuthContext"
import { useRouter } from "next/navigation"
import AuthRoutingService from "@/lib/services/authRouting"
import HybridMobileAuthService from "@/lib/services/hybridMobileAuth"
import { RedirectUtils } from "@/lib/utils/redirectUtils"
import { profileService } from '@/lib/services/profileService'
import { motion, AnimatePresence } from "framer-motion"

export default function SignupPage() {
  // Authentication states
  const [authMethod, setAuthMethod] = useState<'otp' | 'password'>('password')
  const [currentStep, setCurrentStep] = useState<'input' | 'otp'>('input')

  // Form data
  const [formData, setFormData] = useState({
    fullName: '',
    email: '',
    mobile: '',
    password: '',
    confirmPassword: '',
    otp: '',
    agreeToTerms: false,
    subscribeNewsletter: false
  })

  // UI states
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  const [countryCode, setCountryCode] = useState('+91')
  const [resendTimer, setResendTimer] = useState(0)
  const [error, setError] = useState('')
  const [loading, setLoading] = useState(false)
  const [otpSession, setOtpSession] = useState('')
  const [successMessage, setSuccessMessage] = useState('')

  const { signUp, confirmSignUp, resendConfirmationCode, userType, userProfile, sendOTP, verifyOTP, isAuthenticated, isLoading: authLoading } = useAuth()
  const router = useRouter()

  // Store redirect URL from query parameters on component mount
  useEffect(() => {
    RedirectUtils.storeRedirectFromQuery();
  }, []);

  // Redirect already authenticated users to home page
  useEffect(() => {
    if (!authLoading && isAuthenticated) {
      // Get the redirect URL from query params or default to home
      const searchParams = new URLSearchParams(window.location.search);
      const redirectTo = searchParams.get('redirect') || '/';

      // Redirect to the intended destination or home page
      router.replace(redirectTo);
    }
  }, [isAuthenticated, authLoading, router]);

  // Helper function to handle post-signup redirect
  const handlePostSignupRedirect = () => {
    const redirectUrl = RedirectUtils.getAndClearRedirectUrl();
    if (redirectUrl) {
      router.push(redirectUrl);
    } else {
      const dashboardRoute = AuthRoutingService.getDashboardRoute(userType, userProfile);
      router.push(dashboardRoute);
    }
  };

  // Country codes for mobile OTP
  const countryCodes = HybridMobileAuthService.getSupportedCountryCodes()

  // Start resend timer
  const startResendTimer = () => {
    setResendTimer(30)
    const timer = setInterval(() => {
      setResendTimer((prev) => {
        if (prev <= 1) {
          clearInterval(timer)
          return 0
        }
        return prev - 1
      })
    }, 1000)
  }

  // Reset form and states
  const resetForm = () => {
    setFormData({
      fullName: '',
      email: '',
      mobile: '',
      password: '',
      confirmPassword: '',
      otp: '',
      agreeToTerms: false,
      subscribeNewsletter: false
    })
    setCurrentStep('input')
    setError('')
    setSuccessMessage('')
    setOtpSession('')
    setResendTimer(0)
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }))
    setError('')
  }

  const validateForm = () => {
    if (!formData.fullName.trim()) {
      setError('Please enter your full name')
      return false
    }

    if (!formData.email.trim() || !/\S+@\S+\.\S+/.test(formData.email)) {
      setError('Please enter a valid email address')
      return false
    }

    if (!formData.mobile.trim() || !/^\d{10}$/.test(formData.mobile)) {
      setError('Please enter a valid 10-digit mobile number')
      return false
    }

    if (authMethod === 'password') {
      if (formData.password.length < 8) {
        setError('Password must be at least 8 characters long')
        return false
      }

      if (formData.password !== formData.confirmPassword) {
        setError('Passwords do not match')
        return false
      }
    }

    if (!formData.agreeToTerms) {
      setError('Please agree to the Terms of Service and Privacy Policy')
      return false
    }

    return true
  }

  // Handle OTP-based signup
  const handleOTPSignup = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!validateForm()) return

    setLoading(true)
    setError('')

    try {
      // Send OTP to mobile
      const result = await sendOTP(formData.mobile, countryCode)
      if (result.success) {
        setOtpSession(result.session || '')
        setCurrentStep('otp')
        setSuccessMessage(`OTP sent to ${countryCode}${formData.mobile}`)
        startResendTimer()
      } else {
        setError(result.message)
      }
    } catch (error: any) {
      setError(error.message || 'Failed to send OTP')
    } finally {
      setLoading(false)
    }
  }

  // Handle password-based signup
  const handlePasswordSignup = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!validateForm()) return

    setLoading(true)
    setError('')

    try {
      await signUp(formData.email, formData.fullName, formData.password, false)
      setCurrentStep('otp')
      setSuccessMessage(`Verification code sent to ${formData.email}`)
      startResendTimer()
    } catch (error: any) {
      setError(error.message || 'Sign up failed')
    } finally {
      setLoading(false)
    }
  }

  // Handle OTP verification
  const handleOTPVerification = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError('')

    if (formData.otp.length !== 6) {
      setError('Please enter a valid 6-digit OTP')
      setLoading(false)
      return
    }

    try {
      if (authMethod === 'otp') {
        // Verify mobile OTP
        const result = await verifyOTP(otpSession, formData.otp)
        if (result.success) {
          // Create user profile
          try {
            const nameParts = formData.fullName.trim().split(' ')
            const firstName = nameParts[0] || ''
            const lastName = nameParts.slice(1).join(' ') || ''
            await profileService.createProfile({
              firstName,
              lastName,
              email: formData.email,
              phone: formData.mobile,
              isVendor: false
            })
          } catch (profileError) {
            console.error('Error creating user profile:', profileError)
          }
          setTimeout(() => {
            handlePostSignupRedirect()
          }, 1000)
        } else {
          setError(result.message)
        }
      } else {
        // Verify email confirmation code
        await confirmSignUp(formData.email, formData.otp)
        // Create user profile
        try {
          const nameParts = formData.fullName.trim().split(' ')
          const firstName = nameParts[0] || ''
          const lastName = nameParts.slice(1).join(' ') || ''
          await profileService.createProfile({
            firstName,
            lastName,
            email: formData.email,
            phone: formData.mobile,
            isVendor: false
          })
        } catch (profileError) {
          console.error('Error creating user profile:', profileError)
        }
        setTimeout(() => {
          handlePostSignupRedirect()
        }, 1000)
      }
    } catch (error: any) {
      setError(error.message || 'OTP verification failed')
    } finally {
      setLoading(false)
    }
  }

  // Handle resend OTP
  const handleResendOTP = async () => {
    if (resendTimer > 0) return

    setLoading(true)
    setError('')

    try {
      if (authMethod === 'otp') {
        const result = await sendOTP(formData.mobile, countryCode)
        if (result.success) {
          setOtpSession(result.session || '')
          setSuccessMessage(`New OTP sent to ${countryCode}${formData.mobile}`)
          startResendTimer()
        } else {
          setError(result.message)
        }
      } else {
        await resendConfirmationCode(formData.email)
        setSuccessMessage(`New verification code sent to ${formData.email}`)
        startResendTimer()
      }
    } catch (error: any) {
      setError(error.message || 'Failed to resend OTP')
    } finally {
      setLoading(false)
    }
  }

  // Show loading while checking authentication
  if (authLoading) {
    return (
      <div className="min-h-screen bg-white flex flex-col">
        <TopHeader />
        <Header />
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center">
            <RefreshCw className="w-8 h-8 animate-spin text-primary mx-auto mb-4" />
            <p className="text-gray-600">Loading...</p>
          </div>
        </div>
        <Footer />
      </div>
    )
  }

  // Don't render signup form if user is already authenticated (will redirect)
  if (isAuthenticated) {
    return (
      <div className="min-h-screen bg-white flex flex-col">
        <TopHeader />
        <Header />
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center">
            <CheckCircle className="w-8 h-8 text-green-500 mx-auto mb-4" />
            <p className="text-gray-600">Already logged in. Redirecting...</p>
          </div>
        </div>
        <Footer />
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-white">
      <TopHeader />
      <Header />

      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-md mx-auto">
            <Card className="shadow-lg">
              <CardHeader className="text-center">
                {/* Header with back button for OTP step */}
                {currentStep === 'otp' && (
                  <div className="flex items-center mb-4">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => {
                        setCurrentStep('input')
                        setError('')
                        setSuccessMessage('')
                      }}
                      className="p-2 hover:bg-gray-100"
                    >
                      <ArrowLeft className="w-4 h-4" />
                    </Button>
                    <h2 className="text-xl font-semibold ml-2">Verify OTP</h2>
                  </div>
                )}

                {currentStep === 'input' && (
                  <>
                    <CardTitle className="text-2xl font-bold text-gray-900">Create Account</CardTitle>
                    <p className="text-gray-600">Join BookmyFestive and start planning Your Dream Celebration</p>

                    {/* Authentication Method Toggle */}
                    <div className="flex justify-center mt-6">
                      <div className="flex border border-gray-200 rounded-lg overflow-hidden bg-gray-50">
                        <button
                          type="button"
                          className={`px-4 py-2 text-sm font-medium transition-colors ${authMethod === 'otp' ? 'bg-white text-primary shadow-sm' : 'text-gray-600 hover:text-gray-800'}`}
                          onClick={() => setAuthMethod('otp')}
                        >
                          OTP Signup
                        </button>
                        <button
                          type="button"
                          className={`px-4 py-2 text-sm font-medium transition-colors ${authMethod === 'password' ? 'bg-white text-primary shadow-sm' : 'text-gray-600 hover:text-gray-800'}`}
                          onClick={() => setAuthMethod('password')}
                        >
                          Password
                        </button>
                      </div>
                    </div>
                  </>
                )}
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Error Message */}
                {error && (
                  <div className="p-3 bg-red-50 border border-red-200 rounded-md flex items-center gap-2 text-red-700">
                    <AlertCircle className="w-4 h-4" />
                    <span className="text-sm">{error}</span>
                  </div>
                )}

                {/* Success Message */}
                {successMessage && (
                  <div className="p-3 bg-green-50 border border-green-200 rounded-md flex items-center gap-2 text-green-700">
                    <CheckCircle className="w-4 h-4" />
                    <span className="text-sm">{successMessage}</span>
                  </div>
                )}

                {/* OTP Verification Form */}
                {currentStep === 'otp' && (
                  <form onSubmit={handleOTPVerification} className="space-y-6">
                    <div className="text-center">
                      <div className="text-sm text-gray-600 mb-4">
                        Enter the 6-digit verification code sent to
                      </div>
                      <div className="font-semibold text-gray-800">
                        {authMethod === 'otp' ? `${countryCode}${formData.mobile}` : formData.email}
                      </div>
                    </div>

                    <div className="relative">
                      <Input
                        name="otp"
                        type="text"
                        placeholder="Enter 6-digit OTP"
                        className="pl-12 text-center text-lg tracking-widest"
                        value={formData.otp}
                        onChange={(e) => {
                          const value = e.target.value.replace(/\D/g, '').slice(0, 6)
                          setFormData(prev => ({ ...prev, otp: value }))
                          setError('')
                        }}
                        maxLength={6}
                        required
                      />
                      <span className="absolute left-4 top-1/2 -translate-y-1/2 text-primary">
                        <MessageSquare className="h-5 w-5" />
                      </span>
                    </div>

                    <Button
                      type="submit"
                      className="w-full bg-primary hover:bg-primary/90"
                      disabled={loading || formData.otp.length !== 6}
                    >
                      {loading ? (
                        <>
                          <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                          Verifying...
                        </>
                      ) : (
                        <>
                          <CheckCircle className="w-4 h-4 mr-2" />
                          Verify & Create Account
                        </>
                      )}
                    </Button>

                    <div className="text-center">
                      <span className="text-sm text-gray-600">Didn't receive the code? </span>
                      {resendTimer > 0 ? (
                        <span className="text-sm text-gray-500">
                          Resend in {resendTimer}s
                        </span>
                      ) : (
                        <button
                          type="button"
                          onClick={handleResendOTP}
                          disabled={loading}
                          className="text-sm text-primary hover:underline font-medium"
                        >
                          Resend OTP
                        </button>
                      )}
                    </div>
                  </form>
                )}

                {/* Main Signup Form */}
                {currentStep === 'input' && (
                  <>
                    {/* Social Signup - only show for password method */}
                    {authMethod === 'password' && (
                      <>
                        <div className="space-y-3">
                          <Button variant="outline" className="w-full bg-transparent" disabled>
                            <svg className="w-5 h-5 mr-2" viewBox="0 0 24 24">
                              <path
                                fill="currentColor"
                                d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
                              />
                              <path
                                fill="currentColor"
                                d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
                              />
                              <path
                                fill="currentColor"
                                d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
                              />
                              <path
                                fill="currentColor"
                                d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
                              />
                            </svg>
                            Continue with Google
                          </Button>
                        </div>

                        <div className="relative">
                          <div className="absolute inset-0 flex items-center">
                            <Separator />
                          </div>
                          <div className="relative flex justify-center text-xs uppercase">
                            <span className="bg-white px-2 text-gray-500">Or create account with email</span>
                          </div>
                        </div>
                      </>
                    )}

                    {/* Signup Form */}
                    <form onSubmit={authMethod === 'otp' ? handleOTPSignup : handlePasswordSignup} className="space-y-4">
                      {/* Full Name */}
                      <div className="space-y-2">
                        <label htmlFor="fullName" className="text-sm font-medium text-gray-700">
                          Full Name *
                        </label>
                        <div className="relative">
                          <User className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                          <Input
                            id="fullName"
                            name="fullName"
                            type="text"
                            placeholder="Enter your full name"
                            className="pl-10"
                            value={formData.fullName}
                            onChange={handleInputChange}
                            required
                          />
                        </div>
                      </div>

                      {/* Email */}
                      <div className="space-y-2">
                        <label htmlFor="email" className="text-sm font-medium text-gray-700">
                          Email Address *
                        </label>
                        <div className="relative">
                          <Mail className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                          <Input
                            id="email"
                            name="email"
                            type="email"
                            placeholder="Enter your email address"
                            className="pl-10"
                            value={formData.email}
                            onChange={handleInputChange}
                            required
                          />
                        </div>
                      </div>

                      {/* Mobile Number */}
                      <div className="space-y-2">
                        <label htmlFor="mobile" className="text-sm font-medium text-gray-700">
                          Mobile Number *
                        </label>
                        <div className="flex gap-2">
                          <Select value={countryCode} onValueChange={setCountryCode}>
                            <SelectTrigger className="w-24">
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              {countryCodes.map((country) => (
                                <SelectItem key={country.code} value={country.code}>
                                  {country.flag} {country.code}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <div className="relative flex-1">
                            <Phone className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                            <Input
                              id="mobile"
                              name="mobile"
                              type="tel"
                              placeholder="Enter 10-digit mobile number"
                              className="pl-10"
                              value={formData.mobile}
                              onChange={(e) => {
                                const value = e.target.value.replace(/\D/g, '').slice(0, 10)
                                setFormData(prev => ({ ...prev, mobile: value }))
                                setError('')
                              }}
                              maxLength={10}
                              required
                            />
                          </div>
                        </div>
                      </div>

                      {/* Password fields - only for password authentication */}
                      {authMethod === 'password' && (
                        <>
                          <div className="space-y-2">
                            <label htmlFor="password" className="text-sm font-medium text-gray-700">
                              Password *
                            </label>
                            <div className="relative">
                              <Lock className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                              <Input
                                id="password"
                                name="password"
                                type={showPassword ? "text" : "password"}
                                placeholder="Create a password (min 8 characters)"
                                className="pl-10 pr-10"
                                value={formData.password}
                                onChange={handleInputChange}
                                required
                              />
                              <button
                                type="button"
                                onClick={() => setShowPassword(!showPassword)}
                                className="absolute right-3 top-3 text-gray-400 hover:text-gray-600"
                              >
                                {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                              </button>
                            </div>
                          </div>

                          <div className="space-y-2">
                            <label htmlFor="confirmPassword" className="text-sm font-medium text-gray-700">
                              Confirm Password *
                            </label>
                            <div className="relative">
                              <Lock className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                              <Input
                                id="confirmPassword"
                                name="confirmPassword"
                                type={showConfirmPassword ? "text" : "password"}
                                placeholder="Confirm your password"
                                className="pl-10 pr-10"
                                value={formData.confirmPassword}
                                onChange={handleInputChange}
                                required
                              />
                              <button
                                type="button"
                                onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                                className="absolute right-3 top-3 text-gray-400 hover:text-gray-600"
                              >
                                {showConfirmPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                              </button>
                            </div>
                          </div>
                        </>
                      )}

                      {/* Terms and Conditions */}
                      <div className="space-y-4">
                        <div className="flex items-start space-x-2">
                          <Checkbox
                            id="terms"
                            className="mt-1"
                            checked={formData.agreeToTerms}
                            onCheckedChange={(checked) => {
                              setFormData(prev => ({ ...prev, agreeToTerms: checked as boolean }))
                              setError('')
                            }}
                          />
                          <label htmlFor="terms" className="text-sm text-gray-600">
                            I agree to the{" "}
                            <Link href="/terms" className="text-primary hover:text-primary/90">
                              Terms of Service
                            </Link>{" "}
                            and{" "}
                            <Link href="/privacy" className="text-primary hover:text-primary/90">
                              Privacy Policy
                            </Link>
                          </label>
                        </div>

                        <div className="flex items-start space-x-2">
                          <Checkbox
                            id="newsletter"
                            className="mt-1"
                            checked={formData.subscribeNewsletter}
                            onCheckedChange={(checked) => {
                              setFormData(prev => ({ ...prev, subscribeNewsletter: checked as boolean }))
                            }}
                          />
                          <label htmlFor="newsletter" className="text-sm text-gray-600">
                            I want to receive wedding tips, trends, and exclusive offers via email
                          </label>
                        </div>
                      </div>

                      {/* Submit Button */}
                      <Button type="submit" className="w-full bg-primary hover:bg-primary/90" disabled={loading}>
                        {loading ? (
                          <>
                            <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                            {authMethod === 'otp' ? 'Sending OTP...' : 'Creating Account...'}
                          </>
                        ) : (
                          <>
                            {authMethod === 'otp' ? (
                              <>
                                <MessageSquare className="w-4 h-4 mr-2" />
                                Sign Up with OTP
                              </>
                            ) : (
                              <>
                                <Lock className="w-4 h-4 mr-2" />
                                Create Account
                              </>
                            )}
                          </>
                        )}
                      </Button>
                    </form>
                  </>
                )}

                {/* Login Link - only show on input step */}
                {currentStep === 'input' && (
                  <div className="text-center">
                    <p className="text-sm text-gray-600">
                      Already have an account?{" "}
                      <Link href="/login" className="text-primary hover:text-primary/90 font-medium">
                        Sign in here
                      </Link>
                    </p>
                    <div className="mt-2">
                      <p className="text-sm text-gray-600">
                        Are you a vendor?{" "}
                        <Link href="/vendor-signup" className="text-primary hover:text-primary/90 font-medium">
                          Create Business Account
                        </Link>
                      </p>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  )
}
