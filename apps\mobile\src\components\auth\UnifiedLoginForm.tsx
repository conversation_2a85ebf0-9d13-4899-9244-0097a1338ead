import React, { useState, useEffect } from 'react';
import { View, Text, Alert, KeyboardAvoidingView, Platform } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/Card';
import { Button } from '../ui/Button';
import { Input } from '../ui/Input';
import { Badge } from '../ui/Badge';
import { useAuth } from '../../providers/AuthProvider';
import { useTheme } from '../../providers/ThemeProvider';
import { useNavigation } from '@react-navigation/native';

interface UnifiedLoginFormProps {
  onSuccess?: () => void;
  redirectTo?: string;
}

export function UnifiedLoginForm({ onSuccess, redirectTo }: UnifiedLoginFormProps) {
  const { login, loginWithOTP, sendOTP } = useAuth();
  const { theme } = useTheme();
  const navigation = useNavigation();

  // Form state
  const [loginMethod, setLoginMethod] = useState<'email' | 'mobile'>('email');
  const [step, setStep] = useState<'input' | 'otp' | 'password'>('input');

  // Email login state
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);

  // Mobile login state
  const [countryCode, setCountryCode] = useState('+91');
  const [phoneNumber, setPhoneNumber] = useState('');
  const [otp, setOtp] = useState('');

  // UI state
  const [loading, setLoading] = useState(false);
  const [otpSent, setOtpSent] = useState(false);
  const [resendTimer, setResendTimer] = useState(0);
  const [error, setError] = useState('');

  // Country codes
  const countryCodes = [
    { code: '+91', country: 'India' },
    { code: '+1', country: 'USA' },
    { code: '+44', country: 'UK' },
    { code: '+971', country: 'UAE' },
  ];

  // Timer effect for OTP resend
  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (resendTimer > 0) {
      interval = setInterval(() => {
        setResendTimer(prev => prev - 1);
      }, 1000);
    }
    return () => clearInterval(interval);
  }, [resendTimer]);

  const validateEmail = (email: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const validatePhone = (phone: string) => {
    const phoneRegex = /^[6-9]\d{9}$/; // Indian mobile number format
    return phoneRegex.test(phone);
  };

  const handleEmailLogin = async () => {
    if (!validateEmail(email)) {
      setError('Please enter a valid email address');
      return;
    }

    if (!password || password.length < 6) {
      setError('Password must be at least 6 characters');
      return;
    }

    try {
      setLoading(true);
      setError('');
      
      await login(email, password);
      
      if (onSuccess) {
        onSuccess();
      } else if (redirectTo) {
        navigation.navigate(redirectTo as never);
      } else {
        navigation.navigate('Home' as never);
      }
    } catch (error: any) {
      setError(error.message || 'Login failed. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleSendOTP = async () => {
    if (!validatePhone(phoneNumber)) {
      setError('Please enter a valid 10-digit mobile number');
      return;
    }

    try {
      setLoading(true);
      setError('');
      
      const fullPhoneNumber = `${countryCode}${phoneNumber}`;
      await sendOTP(fullPhoneNumber);
      
      setOtpSent(true);
      setStep('otp');
      setResendTimer(30);
      
      Alert.alert('OTP Sent', `Verification code sent to ${fullPhoneNumber}`);
    } catch (error: any) {
      setError(error.message || 'Failed to send OTP. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleOTPLogin = async () => {
    if (!otp || otp.length !== 6) {
      setError('Please enter a valid 6-digit OTP');
      return;
    }

    try {
      setLoading(true);
      setError('');
      
      const fullPhoneNumber = `${countryCode}${phoneNumber}`;
      await loginWithOTP(fullPhoneNumber, otp);
      
      if (onSuccess) {
        onSuccess();
      } else if (redirectTo) {
        navigation.navigate(redirectTo as never);
      } else {
        navigation.navigate('Home' as never);
      }
    } catch (error: any) {
      setError(error.message || 'Invalid OTP. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleResendOTP = async () => {
    if (resendTimer > 0) return;
    
    try {
      setLoading(true);
      const fullPhoneNumber = `${countryCode}${phoneNumber}`;
      await sendOTP(fullPhoneNumber);
      setResendTimer(30);
      Alert.alert('OTP Resent', 'New verification code sent');
    } catch (error: any) {
      setError(error.message || 'Failed to resend OTP');
    } finally {
      setLoading(false);
    }
  };

  const renderLoginMethodTabs = () => (
    <View style={{ flexDirection: 'row', marginBottom: 24 }}>
      <Button
        title="Email"
        onPress={() => {
          setLoginMethod('email');
          setStep('input');
          setError('');
        }}
        variant={loginMethod === 'email' ? 'default' : 'outline'}
        style={{ flex: 1, marginRight: 8 }}
        icon="mail"
      />
      <Button
        title="Mobile"
        onPress={() => {
          setLoginMethod('mobile');
          setStep('input');
          setError('');
        }}
        variant={loginMethod === 'mobile' ? 'default' : 'outline'}
        style={{ flex: 1, marginLeft: 8 }}
        icon="phone-portrait"
      />
    </View>
  );

  const renderEmailForm = () => (
    <View style={{ gap: 16 }}>
      <Input
        label="Email Address"
        value={email}
        onChangeText={setEmail}
        placeholder="Enter your email"
        keyboardType="email-address"
        autoCapitalize="none"
        leftIcon="mail"
      />

      <Input
        label="Password"
        value={password}
        onChangeText={setPassword}
        placeholder="Enter your password"
        secureTextEntry={!showPassword}
        leftIcon="lock-closed"
        rightIcon={showPassword ? "eye-off" : "eye"}
        onRightIconPress={() => setShowPassword(!showPassword)}
      />

      <Button
        title={loading ? "Signing In..." : "Sign In"}
        onPress={handleEmailLogin}
        disabled={loading}
        loading={loading}
        fullWidth
      />
    </View>
  );

  const renderMobileForm = () => {
    if (step === 'otp') {
      return (
        <View style={{ gap: 16 }}>
          <View style={{ alignItems: 'center', marginBottom: 16 }}>
            <Ionicons name="phone-portrait" size={48} color={theme.colors.primary} />
            <Text style={{ 
              fontSize: 16, 
              fontWeight: '600', 
              color: theme.colors.text,
              marginTop: 8,
              textAlign: 'center'
            }}>
              Verify Your Mobile Number
            </Text>
            <Text style={{ 
              fontSize: 14, 
              color: theme.colors.textSecondary,
              marginTop: 4,
              textAlign: 'center'
            }}>
              Enter the 6-digit code sent to {countryCode}{phoneNumber}
            </Text>
          </View>

          <Input
            label="Verification Code"
            value={otp}
            onChangeText={setOtp}
            placeholder="Enter 6-digit OTP"
            keyboardType="numeric"
            maxLength={6}
            leftIcon="keypad"
          />

          <Button
            title={loading ? "Verifying..." : "Verify & Sign In"}
            onPress={handleOTPLogin}
            disabled={loading}
            loading={loading}
            fullWidth
          />

          <View style={{ alignItems: 'center', marginTop: 16 }}>
            {resendTimer > 0 ? (
              <Text style={{ fontSize: 14, color: theme.colors.textSecondary }}>
                Resend OTP in {resendTimer}s
              </Text>
            ) : (
              <Button
                title="Resend OTP"
                onPress={handleResendOTP}
                variant="ghost"
                disabled={loading}
              />
            )}
          </View>

          <Button
            title="Change Number"
            onPress={() => {
              setStep('input');
              setOtp('');
              setOtpSent(false);
            }}
            variant="outline"
            fullWidth
          />
        </View>
      );
    }

    return (
      <View style={{ gap: 16 }}>
        <View>
          <Text style={{ 
            fontSize: 14, 
            fontWeight: '500', 
            color: theme.colors.text,
            marginBottom: 8 
          }}>
            Mobile Number
          </Text>
          <View style={{ flexDirection: 'row', gap: 8 }}>
            <View style={{ width: 80 }}>
              <Input
                value={countryCode}
                onChangeText={setCountryCode}
                placeholder="+91"
                keyboardType="phone-pad"
              />
            </View>
            <View style={{ flex: 1 }}>
              <Input
                value={phoneNumber}
                onChangeText={setPhoneNumber}
                placeholder="Enter mobile number"
                keyboardType="phone-pad"
                maxLength={10}
                leftIcon="call"
              />
            </View>
          </View>
        </View>

        <Button
          title={loading ? "Sending OTP..." : "Send OTP"}
          onPress={handleSendOTP}
          disabled={loading}
          loading={loading}
          fullWidth
        />
      </View>
    );
  };

  return (
    <KeyboardAvoidingView 
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      style={{ flex: 1 }}
    >
      <Card style={{ margin: 16 }}>
        <CardHeader>
          <CardTitle>Welcome Back</CardTitle>
          <Text style={{ fontSize: 14, color: theme.colors.textSecondary }}>
            Sign in to your account to continue
          </Text>
        </CardHeader>

        <CardContent>
          {renderLoginMethodTabs()}

          {error ? (
            <View style={{
              backgroundColor: '#FEF2F2',
              borderColor: '#FECACA',
              borderWidth: 1,
              borderRadius: 8,
              padding: 12,
              marginBottom: 16,
              flexDirection: 'row',
              alignItems: 'center',
              gap: 8,
            }}>
              <Ionicons name="alert-circle" size={16} color="#DC2626" />
              <Text style={{ fontSize: 14, color: '#DC2626', flex: 1 }}>
                {error}
              </Text>
            </View>
          ) : null}

          {loginMethod === 'email' ? renderEmailForm() : renderMobileForm()}

          <View style={{ 
            flexDirection: 'row', 
            justifyContent: 'center', 
            alignItems: 'center',
            marginTop: 24,
            gap: 4
          }}>
            <Text style={{ fontSize: 14, color: theme.colors.textSecondary }}>
              Don't have an account?
            </Text>
            <Button
              title="Sign Up"
              onPress={() => navigation.navigate('Signup' as never)}
              variant="ghost"
              size="sm"
            />
          </View>
        </CardContent>
      </Card>
    </KeyboardAvoidingView>
  );
}
