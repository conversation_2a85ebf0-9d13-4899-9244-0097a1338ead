import React from 'react';
import { View, Text, TouchableOpacity, Image, Alert } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Card, CardContent } from './ui/Card';
import { Badge } from './ui/Badge';
import { Button } from './ui/Button';
import { useAuth } from '../providers/AuthProvider';
import { useTheme } from '../providers/ThemeProvider';
import { useNavigation } from '@react-navigation/native';
import { useCart } from '../providers/CartProvider';
import { useFavorites } from '../providers/FavoritesProvider';

interface UserProfileMenuProps {
  onClose?: () => void;
}

export function UserProfileMenu({ onClose }: UserProfileMenuProps) {
  const { userProfile, userType, logout, isAuthenticated } = useAuth();
  const { clearCart } = useCart();
  const { clearFavorites } = useFavorites();
  const { theme } = useTheme();
  const navigation = useNavigation();

  if (!isAuthenticated || !userProfile) {
    return (
      <Card style={{ margin: 16 }}>
        <CardContent style={{ padding: 24, alignItems: 'center' }}>
          <Ionicons name="person-circle" size={64} color={theme.colors.textSecondary} />
          <Text style={{ 
            fontSize: 18, 
            fontWeight: '600', 
            color: theme.colors.text,
            marginTop: 16,
            textAlign: 'center'
          }}>
            Welcome to BookmyFestive
          </Text>
          <Text style={{ 
            fontSize: 14, 
            color: theme.colors.textSecondary,
            marginTop: 8,
            textAlign: 'center'
          }}>
            Sign in to access your profile and preferences
          </Text>
          <View style={{ flexDirection: 'row', gap: 12, marginTop: 16, width: '100%' }}>
            <Button
              title="Sign In"
              onPress={() => {
                onClose?.();
                navigation.navigate('Login' as never);
              }}
              style={{ flex: 1 }}
            />
            <Button
              title="Sign Up"
              onPress={() => {
                onClose?.();
                navigation.navigate('Signup' as never);
              }}
              variant="outline"
              style={{ flex: 1 }}
            />
          </View>
        </CardContent>
      </Card>
    );
  }

  const getUserTypeInfo = () => {
    switch (userType) {
      case 'ADMIN':
        return { label: 'Admin', color: '#EF4444', icon: 'shield' as const };
      case 'VENDOR':
        return { label: 'Vendor', color: '#3B82F6', icon: 'briefcase' as const };
      case 'CUSTOMER':
      default:
        return { label: 'Customer', color: '#10B981', icon: 'person' as const };
    }
  };

  const userTypeInfo = getUserTypeInfo();

  const handleLogout = async () => {
    Alert.alert(
      'Sign Out',
      'Are you sure you want to sign out?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Sign Out',
          style: 'destructive',
          onPress: async () => {
            try {
              await clearCart();
              await clearFavorites();
              await logout();
              onClose?.();
            } catch (error) {
              console.error('Logout failed:', error);
              Alert.alert('Error', 'Failed to sign out. Please try again.');
            }
          },
        },
      ]
    );
  };

  const menuItems = [
    {
      icon: 'person',
      label: 'Profile',
      onPress: () => {
        onClose?.();
        navigation.navigate('Profile' as never);
      },
    },
    {
      icon: 'heart',
      label: 'Favorites',
      onPress: () => {
        onClose?.();
        navigation.navigate('Favorites' as never);
      },
    },
    {
      icon: 'bag',
      label: 'Cart',
      onPress: () => {
        onClose?.();
        navigation.navigate('Cart' as never);
      },
    },
    {
      icon: 'time',
      label: 'Booking History',
      onPress: () => {
        onClose?.();
        navigation.navigate('BookingHistory' as never);
      },
    },
    {
      icon: 'settings',
      label: 'Settings',
      onPress: () => {
        onClose?.();
        navigation.navigate('Settings' as never);
      },
    },
    {
      icon: 'help-circle',
      label: 'Help & Support',
      onPress: () => {
        onClose?.();
        navigation.navigate('Help' as never);
      },
    },
  ];

  // Add admin/vendor specific menu items
  if (userType === 'ADMIN') {
    menuItems.splice(4, 0, {
      icon: 'shield',
      label: 'Admin Dashboard',
      onPress: () => {
        onClose?.();
        navigation.navigate('AdminDashboard' as never);
      },
    });
  } else if (userType === 'VENDOR') {
    menuItems.splice(4, 0, {
      icon: 'briefcase',
      label: 'Vendor Dashboard',
      onPress: () => {
        onClose?.();
        navigation.navigate('VendorDashboard' as never);
      },
    });
  }

  return (
    <View style={{ flex: 1 }}>
      {/* Profile Header */}
      <Card style={{ margin: 16, marginBottom: 8 }}>
        <CardContent style={{ padding: 20 }}>
          <View style={{ flexDirection: 'row', alignItems: 'center', gap: 16 }}>
            {userProfile.profilePicture ? (
              <Image
                source={{ uri: userProfile.profilePicture }}
                style={{
                  width: 60,
                  height: 60,
                  borderRadius: 30,
                  backgroundColor: theme.colors.muted,
                }}
              />
            ) : (
              <View
                style={{
                  width: 60,
                  height: 60,
                  borderRadius: 30,
                  backgroundColor: theme.colors.primary,
                  alignItems: 'center',
                  justifyContent: 'center',
                }}
              >
                <Text style={{ fontSize: 24, color: '#fff', fontWeight: '600' }}>
                  {(userProfile.firstName || userProfile.fullName || 'U').charAt(0).toUpperCase()}
                </Text>
              </View>
            )}
            
            <View style={{ flex: 1 }}>
              <Text style={{
                fontSize: 18,
                fontWeight: '600',
                color: theme.colors.text,
                marginBottom: 4,
              }}>
                {userProfile.fullName || `${userProfile.firstName || ''} ${userProfile.lastName || ''}`.trim() || 'User'}
              </Text>
              
              <Text style={{
                fontSize: 14,
                color: theme.colors.textSecondary,
                marginBottom: 8,
              }}>
                {userProfile.email}
              </Text>
              
              <Badge 
                variant={userType === 'ADMIN' ? 'destructive' : userType === 'VENDOR' ? 'default' : 'success'}
                size="sm"
              >
                <View style={{ flexDirection: 'row', alignItems: 'center', gap: 4 }}>
                  <Ionicons name={userTypeInfo.icon} size={12} color="#fff" />
                  <Text style={{ color: '#fff', fontSize: 10 }}>{userTypeInfo.label}</Text>
                </View>
              </Badge>
            </View>
          </View>
        </CardContent>
      </Card>

      {/* Menu Items */}
      <Card style={{ margin: 16, marginTop: 8 }}>
        <CardContent style={{ padding: 0 }}>
          {menuItems.map((item, index) => (
            <TouchableOpacity
              key={item.label}
              onPress={item.onPress}
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                padding: 16,
                borderBottomWidth: index < menuItems.length - 1 ? 1 : 0,
                borderBottomColor: theme.colors.border,
              }}
            >
              <Ionicons 
                name={item.icon as any} 
                size={20} 
                color={theme.colors.textSecondary} 
                style={{ marginRight: 16 }}
              />
              <Text style={{
                fontSize: 16,
                color: theme.colors.text,
                flex: 1,
              }}>
                {item.label}
              </Text>
              <Ionicons 
                name="chevron-forward" 
                size={16} 
                color={theme.colors.textSecondary} 
              />
            </TouchableOpacity>
          ))}
        </CardContent>
      </Card>

      {/* Logout Button */}
      <View style={{ margin: 16, marginTop: 8 }}>
        <Button
          title="Sign Out"
          onPress={handleLogout}
          variant="destructive"
          icon="log-out"
          iconPosition="left"
          fullWidth
        />
      </View>
    </View>
  );
}
