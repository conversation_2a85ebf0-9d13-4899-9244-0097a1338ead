"use client"

import { useState, useRef, useEffect } from 'react'
import Link from 'next/link'
import Image from 'next/image'
import { useAuth } from '@/contexts/AuthContext'
import { User, Settings, LogOut, ChevronDown, Briefcase, Shield, Bell } from 'lucide-react'
import { cn } from '@/lib/utils'

export default function UserProfileDropdown() {
  const { userProfile, userType, signOut, isAuthenticated } = useAuth()
  const [isOpen, setIsOpen] = useState(false)
  const dropdownRef = useRef<HTMLDivElement>(null)

  // Close dropdown when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [])

  if (!isAuthenticated || !userProfile) {
    return (
      <div className="flex items-center gap-2">
        <Link
          href="/login"
          className="px-4 py-2 text-sm font-medium bg-primary text-white rounded-lg hover:bg-primary/90 transition-colors"
        >
           Sign In
        </Link>
      </div>
    )
  }

  const handleLogout = async () => {
    try {
      await signOut()
      setIsOpen(false)
    } catch (error) {
      console.error('Logout failed:', error)
    }
  }

  const getUserTypeInfo = () => {
    switch (userType) {
      case 'super_admin':
        return { icon: Bell, label: 'Super Admin', color: 'text-purple-600' }
      case 'admin':
        return { icon: Shield, label: 'Administrator', color: 'text-orange-600' }
      case 'vendor':
        return { icon: Briefcase, label: 'Business Account', color: 'text-primary' }
      case 'customer':
        return { icon: User, label: 'Customer', color: 'text-blue-600' }
      default:
        return { icon: User, label: 'User', color: 'text-gray-600' }
    }
  }

  const userTypeInfo = getUserTypeInfo()
  const TypeIcon = userTypeInfo.icon

  return (
    <div className="relative" ref={dropdownRef}>
      {/* Trigger Button */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center gap-2 px-3 py-2 text-sm font-medium text-gray-700 hover:text-primary hover:bg-gray-50 rounded-lg transition-all duration-200"
      >
        {/* User Avatar */}
        <div className="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center">
          {userProfile?.profilePhoto ? (
            <Image
              src={userProfile.profilePhoto}
              alt="Profile"
              width={32}
              height={32}
              className="w-8 h-8 rounded-full object-cover"
            />
          ) : (
            <User className="w-4 h-4 text-gray-500" />
          )}
        </div>
        
        {/* User Name (hidden on mobile) */}
        <div className="hidden sm:block text-left">
          <div className="text-sm font-medium text-gray-900">
            {userProfile.firstName && userProfile.lastName 
              ? `${userProfile.firstName} ${userProfile.lastName}`
              : userProfile.firstName || userProfile.lastName || 'User'
            }
          </div>
          <div className={cn("text-xs", userTypeInfo.color)}>
            {userTypeInfo.label}
          </div>
        </div>
        
        <ChevronDown className={cn(
          "w-4 h-4 text-gray-400 transition-transform duration-200",
          isOpen && "rotate-180"
        )} />
      </button>

      {/* Dropdown Menu */}
      {isOpen && (
        <div className="absolute right-0 mt-2 w-64 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50">
          {/* User Info Header */}
          <div className="px-4 py-3 border-b border-gray-100">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center">
                {userProfile?.profilePhoto ? (
                  <Image
                    src={userProfile.profilePhoto}
                    alt="Profile"
                    width={40}
                    height={40}
                    className="w-10 h-10 rounded-full object-cover"
                  />
                ) : (
                  <User className="w-5 h-5 text-gray-500" />
                )}
              </div>
              <div className="flex-1 min-w-0">
                <div className="text-sm font-medium text-gray-900 truncate">
                  {userProfile.firstName && userProfile.lastName 
                    ? `${userProfile.firstName} ${userProfile.lastName}`
                    : userProfile.firstName || userProfile.lastName || 'User'
                  }
                </div>
                {userProfile.email && (
                  <div className="text-xs text-gray-500 truncate">
                    {userProfile.email}
                  </div>
                )}
                <div className={cn("flex items-center gap-1 mt-1", userTypeInfo.color)}>
                  <TypeIcon className="w-3 h-3" />
                  <span className="text-xs">{userTypeInfo.label}</span>
                </div>
              </div>
            </div>
            
            {/* Business Name */}
            {userProfile?.businessInfo?.businessName && (
              <div className="text-xs text-gray-600 mt-2 truncate">
                <strong>Business:</strong> {userProfile.businessInfo.businessName}
              </div>
            )}
          </div>

          {/* Menu Items */}
          <div className="py-1">
            <Link
              href="/dashboard"
              className="flex items-center gap-3 px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors"
              onClick={() => setIsOpen(false)}
            >
              <User className="w-4 h-4" />
              Dashboard
            </Link>
            
            <Link
              href="/dashboard/profile"
              className="flex items-center gap-3 px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors"
              onClick={() => setIsOpen(false)}
            >
              <Settings className="w-4 h-4" />
              Profile Settings
            </Link>
          </div>

          {/* Logout */}
          <div className="border-t border-gray-100 pt-1">
            <button
              onClick={handleLogout}
              className="flex items-center gap-3 px-4 py-2 text-sm text-red-600 hover:bg-red-50 transition-colors w-full text-left"
            >
              <LogOut className="w-4 h-4" />
              Sign Out
            </button>
          </div>
        </div>
      )}
    </div>
  )
}
