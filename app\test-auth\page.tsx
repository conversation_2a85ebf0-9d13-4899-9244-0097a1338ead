'use client'

import { AuthTest } from '@/components/AuthTest'

export default function TestAuthPage() {
  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <h1 className="text-3xl font-bold text-gray-900 mb-8 text-center">
          🧪 Cognito Authentication Test
        </h1>
        
        <div className="bg-white rounded-lg shadow-lg p-6">
          <AuthTest />
        </div>
        
        <div className="mt-8 bg-blue-50 rounded-lg p-6">
          <h2 className="text-xl font-semibold text-blue-800 mb-4">
            📋 Test Instructions
          </h2>
          <div className="space-y-3 text-blue-700">
            <p><strong>1. Sign Up Test:</strong> Enter a new email, name, and password, then click "Test Sign Up"</p>
            <p><strong>2. Email Verification:</strong> Check your email for verification code</p>
            <p><strong>3. Login Test:</strong> After verification, use the same credentials to test login</p>
            <p><strong>4. Profile Creation:</strong> The system should automatically create a UserProfile entry</p>
            <p><strong>5. Business Users:</strong> Use name format "John Doe (Business Name)" to test vendor profiles</p>
          </div>
        </div>
        
        <div className="mt-6 bg-green-50 rounded-lg p-6">
          <h2 className="text-xl font-semibold text-green-800 mb-4">
            ✅ What Should Happen
          </h2>
          <div className="space-y-2 text-green-700">
            <p>• New users get a UserProfile entry automatically created</p>
            <p>• Business users (with parentheses in name) get vendor profiles</p>
            <p>• Regular users get customer profiles</p>
            <p>• Profile creation happens via Lambda trigger OR client-side fallback</p>
            <p>• User type is determined based on profile data</p>
          </div>
        </div>
      </div>
    </div>
  )
}
