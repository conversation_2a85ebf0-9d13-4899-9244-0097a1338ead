import React, { useState } from 'react';
import { TextInput, View, Text, StyleSheet, ViewStyle, TextStyle, TextInputProps, TouchableOpacity } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../../providers/ThemeProvider';

export interface InputProps extends TextInputProps {
  label?: string;
  error?: string;
  containerStyle?: ViewStyle;
  inputStyle?: TextStyle;
  labelStyle?: TextStyle;
  errorStyle?: TextStyle;
  leftIcon?: keyof typeof Ionicons.glyphMap;
  rightIcon?: keyof typeof Ionicons.glyphMap;
  onRightIconPress?: () => void;
  variant?: 'default' | 'filled' | 'outlined';
  size?: 'default' | 'sm' | 'lg';
  disabled?: boolean;
}

export function Input({
  label,
  error,
  containerStyle,
  inputStyle,
  labelStyle,
  errorStyle,
  leftIcon,
  rightIcon,
  onRightIconPress,
  variant = 'default',
  size = 'default',
  disabled = false,
  ...props
}: InputProps) {
  const { theme } = useTheme();
  const [isFocused, setIsFocused] = useState(false);

  const getContainerStyle = (): ViewStyle => {
    const sizeStyles = {
      default: { height: 40 },
      sm: { height: 36 },
      lg: { height: 44 },
    };

    const variantStyles = {
      default: {
        borderWidth: 1,
        borderColor: error ? theme.colors.error : isFocused ? theme.colors.primary : theme.colors.border,
        backgroundColor: theme.colors.background,
      },
      filled: {
        borderWidth: 0,
        backgroundColor: theme.colors.muted || '#f5f5f5',
      },
      outlined: {
        borderWidth: 2,
        borderColor: error ? theme.colors.error : isFocused ? theme.colors.primary : theme.colors.border,
        backgroundColor: 'transparent',
      },
    };

    return {
      flexDirection: 'row',
      alignItems: 'center',
      borderRadius: theme.borderRadius.md,
      paddingHorizontal: theme.spacing.md,
      opacity: disabled ? 0.6 : 1,
      ...sizeStyles[size],
      ...variantStyles[variant],
    };
  };

  const getInputStyle = (): TextStyle => {
    const sizeStyles = {
      sm: {
        fontSize: theme.typography.bodySmall.fontSize,
        lineHeight: theme.typography.bodySmall.lineHeight,
      },
      md: {
        fontSize: theme.typography.body.fontSize,
        lineHeight: theme.typography.body.lineHeight,
      },
      lg: {
        fontSize: theme.typography.bodyLarge.fontSize,
        lineHeight: theme.typography.bodyLarge.lineHeight,
      },
    };

    return {
      flex: 1,
      ...sizeStyles[size],
      color: theme.colors.foreground,
      paddingLeft: leftIcon ? 8 : 0,
      paddingRight: rightIcon ? 8 : 0,
    };
  };

  const getLabelStyle = (): TextStyle => {
    return {
      fontSize: theme.typography.bodySmall.fontSize,
      fontWeight: '500',
      color: theme.colors.foreground,
      marginBottom: 4, // xs spacing
    };
  };

  const getErrorStyle = (): TextStyle => {
    return {
      fontSize: theme.typography.caption.fontSize,
      color: theme.colors.destructive,
      marginTop: 4, // xs spacing
    };
  };

  const iconSize = size === 'sm' ? 16 : size === 'lg' ? 24 : 20;
  const iconColor = theme.colors.textSecondary || theme.colors.mutedForeground;

  return (
    <View style={containerStyle}>
      {label && (
        <Text style={[getLabelStyle(), labelStyle]}>
          {label}
        </Text>
      )}

      <View style={getContainerStyle()}>
        {leftIcon && (
          <Ionicons name={leftIcon} size={iconSize} color={iconColor} />
        )}

        <TextInput
          style={[getInputStyle(), inputStyle]}
          onFocus={() => setIsFocused(true)}
          onBlur={() => setIsFocused(false)}
          placeholderTextColor={theme.colors.mutedForeground}
          editable={!disabled}
          {...props}
        />

        {rightIcon && (
          <TouchableOpacity onPress={onRightIconPress} disabled={!onRightIconPress}>
            <Ionicons name={rightIcon} size={iconSize} color={iconColor} />
          </TouchableOpacity>
        )}
      </View>

      {error && (
        <Text style={[getErrorStyle(), errorStyle]}>
          {error}
        </Text>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  // Additional styles can be added here if needed
});
