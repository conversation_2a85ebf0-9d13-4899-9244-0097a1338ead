import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Alert,
  Modal,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../providers/ThemeProvider';

interface BudgetCategory {
  id: string;
  name: string;
  budgeted: number;
  spent: number;
  icon: keyof typeof Ionicons.glyphMap;
  color: string;
}

interface Expense {
  id: string;
  categoryId: string;
  description: string;
  amount: number;
  date: Date;
  vendor?: string;
}

export default function BudgetTrackerScreen() {
  const { theme } = useTheme();
  
  const [totalBudget] = useState(500000);
  const [showAddExpense, setShowAddExpense] = useState(false);
  const [newExpense, setNewExpense] = useState({
    categoryId: '',
    description: '',
    amount: '',
    vendor: '',
  });

  const [categories, setCategories] = useState<BudgetCategory[]>([
    {
      id: 'venue',
      name: 'Venue & Catering',
      budgeted: 200000,
      spent: 150000,
      icon: 'business-outline',
      color: '#4CAF50',
    },
    {
      id: 'photography',
      name: 'Photography',
      budgeted: 50000,
      spent: 45000,
      icon: 'camera-outline',
      color: '#2196F3',
    },
    {
      id: 'decoration',
      name: 'Decoration',
      budgeted: 75000,
      spent: 30000,
      icon: 'flower-outline',
      color: '#FF9800',
    },
    {
      id: 'attire',
      name: 'Attire & Jewelry',
      budgeted: 80000,
      spent: 65000,
      icon: 'shirt-outline',
      color: '#9C27B0',
    },
    {
      id: 'music',
      name: 'Music & Entertainment',
      budgeted: 40000,
      spent: 25000,
      icon: 'musical-notes-outline',
      color: '#F44336',
    },
    {
      id: 'transport',
      name: 'Transportation',
      budgeted: 25000,
      spent: 15000,
      icon: 'car-outline',
      color: '#607D8B',
    },
    {
      id: 'misc',
      name: 'Miscellaneous',
      budgeted: 30000,
      spent: 12000,
      icon: 'ellipsis-horizontal-outline',
      color: '#795548',
    },
  ]);

  const [expenses] = useState<Expense[]>([
    {
      id: '1',
      categoryId: 'venue',
      description: 'Venue booking advance',
      amount: 100000,
      date: new Date('2024-01-15'),
      vendor: 'Grand Palace Hotel',
    },
    {
      id: '2',
      categoryId: 'photography',
      description: 'Pre-wedding shoot',
      amount: 25000,
      date: new Date('2024-02-01'),
      vendor: 'Capture Moments Studio',
    },
    {
      id: '3',
      categoryId: 'attire',
      description: 'Wedding lehenga',
      amount: 45000,
      date: new Date('2024-02-10'),
      vendor: 'Sabyasachi',
    },
  ]);

  const getTotalSpent = () => {
    return categories.reduce((total, category) => total + category.spent, 0);
  };

  const getRemainingBudget = () => {
    return totalBudget - getTotalSpent();
  };

  const getBudgetProgress = () => {
    return (getTotalSpent() / totalBudget) * 100;
  };

  const getCategoryProgress = (category: BudgetCategory) => {
    return (category.spent / category.budgeted) * 100;
  };

  const getCategoryStatus = (category: BudgetCategory) => {
    const progress = getCategoryProgress(category);
    if (progress > 100) return 'over';
    if (progress > 80) return 'warning';
    return 'good';
  };

  const handleAddExpense = () => {
    if (!newExpense.categoryId || !newExpense.description || !newExpense.amount) {
      Alert.alert('Error', 'Please fill in all required fields');
      return;
    }

    const amount = parseFloat(newExpense.amount);
    if (isNaN(amount) || amount <= 0) {
      Alert.alert('Error', 'Please enter a valid amount');
      return;
    }

    // Update category spent amount
    setCategories(prev => prev.map(cat => 
      cat.id === newExpense.categoryId 
        ? { ...cat, spent: cat.spent + amount }
        : cat
    ));

    // Reset form
    setNewExpense({
      categoryId: '',
      description: '',
      amount: '',
      vendor: '',
    });
    setShowAddExpense(false);

    Alert.alert('Success', 'Expense added successfully!');
  };

  const renderCategoryCard = (category: BudgetCategory) => {
    const progress = getCategoryProgress(category);
    const status = getCategoryStatus(category);
    const remaining = category.budgeted - category.spent;

    return (
      <View key={category.id} style={[styles.categoryCard, { backgroundColor: theme.colors.surface }]}>
        <View style={styles.categoryHeader}>
          <View style={[styles.categoryIcon, { backgroundColor: category.color + '20' }]}>
            <Ionicons name={category.icon} size={24} color={category.color} />
          </View>
          
          <View style={styles.categoryInfo}>
            <Text style={[styles.categoryName, { color: theme.colors.text }]}>
              {category.name}
            </Text>
            <Text style={[styles.categoryAmount, { color: theme.colors.textSecondary }]}>
              ₹{category.spent.toLocaleString()} / ₹{category.budgeted.toLocaleString()}
            </Text>
          </View>

          <View style={styles.categoryStatus}>
            <Text style={[
              styles.categoryPercentage,
              { color: status === 'over' ? theme.colors.error : category.color }
            ]}>
              {Math.round(progress)}%
            </Text>
            {status === 'over' && (
              <Ionicons name="warning-outline" size={16} color={theme.colors.error} />
            )}
          </View>
        </View>

        <View style={styles.progressContainer}>
          <View style={[styles.progressBar, { backgroundColor: theme.colors.border }]}>
            <View 
              style={[
                styles.progressFill,
                { 
                  backgroundColor: status === 'over' ? theme.colors.error : category.color,
                  width: `${Math.min(progress, 100)}%`
                }
              ]} 
            />
          </View>
        </View>

        <View style={styles.categoryFooter}>
          <Text style={[
            styles.remainingAmount,
            { color: remaining >= 0 ? theme.colors.success : theme.colors.error }
          ]}>
            {remaining >= 0 ? 'Remaining: ' : 'Over budget: '}
            ₹{Math.abs(remaining).toLocaleString()}
          </Text>
        </View>
      </View>
    );
  };

  const totalSpent = getTotalSpent();
  const remainingBudget = getRemainingBudget();
  const budgetProgress = getBudgetProgress();

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {/* Budget Overview */}
      <View style={[styles.overviewCard, { backgroundColor: theme.colors.surface }]}>
        <Text style={[styles.overviewTitle, { color: theme.colors.text }]}>
          Wedding Budget Overview
        </Text>
        
        <View style={styles.budgetStats}>
          <View style={styles.budgetStat}>
            <Text style={[styles.budgetLabel, { color: theme.colors.textSecondary }]}>
              Total Budget
            </Text>
            <Text style={[styles.budgetAmount, { color: theme.colors.text }]}>
              ₹{totalBudget.toLocaleString()}
            </Text>
          </View>
          
          <View style={styles.budgetStat}>
            <Text style={[styles.budgetLabel, { color: theme.colors.textSecondary }]}>
              Spent
            </Text>
            <Text style={[styles.budgetAmount, { color: theme.colors.primary }]}>
              ₹{totalSpent.toLocaleString()}
            </Text>
          </View>
          
          <View style={styles.budgetStat}>
            <Text style={[styles.budgetLabel, { color: theme.colors.textSecondary }]}>
              Remaining
            </Text>
            <Text style={[
              styles.budgetAmount,
              { color: remainingBudget >= 0 ? theme.colors.success : theme.colors.error }
            ]}>
              ₹{Math.abs(remainingBudget).toLocaleString()}
            </Text>
          </View>
        </View>

        <View style={styles.overallProgress}>
          <View style={styles.progressHeader}>
            <Text style={[styles.progressLabel, { color: theme.colors.text }]}>
              Budget Used
            </Text>
            <Text style={[styles.progressPercentage, { color: theme.colors.primary }]}>
              {Math.round(budgetProgress)}%
            </Text>
          </View>
          
          <View style={[styles.progressBar, { backgroundColor: theme.colors.border }]}>
            <View 
              style={[
                styles.progressFill,
                { 
                  backgroundColor: budgetProgress > 100 ? theme.colors.error : theme.colors.primary,
                  width: `${Math.min(budgetProgress, 100)}%`
                }
              ]} 
            />
          </View>
        </View>
      </View>

      {/* Add Expense Button */}
      <View style={styles.actionContainer}>
        <TouchableOpacity
          style={[styles.addButton, { backgroundColor: theme.colors.primary }]}
          onPress={() => setShowAddExpense(true)}
        >
          <Ionicons name="add-outline" size={20} color="white" />
          <Text style={styles.addButtonText}>Add Expense</Text>
        </TouchableOpacity>
      </View>

      {/* Categories List */}
      <ScrollView style={styles.categoriesList} showsVerticalScrollIndicator={false}>
        {categories.map(renderCategoryCard)}
      </ScrollView>

      {/* Add Expense Modal */}
      <Modal
        visible={showAddExpense}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setShowAddExpense(false)}
      >
        <View style={[styles.modalContainer, { backgroundColor: theme.colors.background }]}>
          <View style={[styles.modalHeader, { backgroundColor: theme.colors.surface }]}>
            <TouchableOpacity onPress={() => setShowAddExpense(false)}>
              <Ionicons name="close-outline" size={24} color={theme.colors.text} />
            </TouchableOpacity>
            <Text style={[styles.modalTitle, { color: theme.colors.text }]}>
              Add Expense
            </Text>
            <TouchableOpacity onPress={handleAddExpense}>
              <Text style={[styles.saveButton, { color: theme.colors.primary }]}>
                Save
              </Text>
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.modalContent}>
            {/* Category Selection */}
            <Text style={[styles.fieldLabel, { color: theme.colors.text }]}>
              Category *
            </Text>
            <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.categorySelector}>
              {categories.map((category) => (
                <TouchableOpacity
                  key={category.id}
                  style={[
                    styles.categoryOption,
                    { backgroundColor: theme.colors.surface },
                    newExpense.categoryId === category.id && { backgroundColor: category.color + '20' }
                  ]}
                  onPress={() => setNewExpense(prev => ({ ...prev, categoryId: category.id }))}
                >
                  <Ionicons name={category.icon} size={20} color={category.color} />
                  <Text style={[
                    styles.categoryOptionText,
                    { color: newExpense.categoryId === category.id ? category.color : theme.colors.text }
                  ]}>
                    {category.name}
                  </Text>
                </TouchableOpacity>
              ))}
            </ScrollView>

            {/* Description */}
            <Text style={[styles.fieldLabel, { color: theme.colors.text }]}>
              Description *
            </Text>
            <TextInput
              style={[styles.textInput, { backgroundColor: theme.colors.surface, color: theme.colors.text }]}
              value={newExpense.description}
              onChangeText={(text) => setNewExpense(prev => ({ ...prev, description: text }))}
              placeholder="Enter expense description"
              placeholderTextColor={theme.colors.textSecondary}
            />

            {/* Amount */}
            <Text style={[styles.fieldLabel, { color: theme.colors.text }]}>
              Amount *
            </Text>
            <TextInput
              style={[styles.textInput, { backgroundColor: theme.colors.surface, color: theme.colors.text }]}
              value={newExpense.amount}
              onChangeText={(text) => setNewExpense(prev => ({ ...prev, amount: text }))}
              placeholder="Enter amount"
              placeholderTextColor={theme.colors.textSecondary}
              keyboardType="numeric"
            />

            {/* Vendor */}
            <Text style={[styles.fieldLabel, { color: theme.colors.text }]}>
              Vendor (Optional)
            </Text>
            <TextInput
              style={[styles.textInput, { backgroundColor: theme.colors.surface, color: theme.colors.text }]}
              value={newExpense.vendor}
              onChangeText={(text) => setNewExpense(prev => ({ ...prev, vendor: text }))}
              placeholder="Enter vendor name"
              placeholderTextColor={theme.colors.textSecondary}
            />
          </ScrollView>
        </View>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  overviewCard: {
    margin: 16,
    padding: 20,
    borderRadius: 16,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  overviewTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 16,
    textAlign: 'center',
  },
  budgetStats: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  budgetStat: {
    alignItems: 'center',
    flex: 1,
  },
  budgetLabel: {
    fontSize: 12,
    marginBottom: 4,
  },
  budgetAmount: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  overallProgress: {
    marginTop: 8,
  },
  progressHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  progressLabel: {
    fontSize: 14,
    fontWeight: '600',
  },
  progressPercentage: {
    fontSize: 14,
    fontWeight: 'bold',
  },
  progressContainer: {
    marginVertical: 8,
  },
  progressBar: {
    height: 6,
    borderRadius: 3,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    borderRadius: 3,
  },
  actionContainer: {
    paddingHorizontal: 16,
    marginBottom: 8,
  },
  addButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 12,
    borderRadius: 8,
    gap: 8,
  },
  addButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  categoriesList: {
    flex: 1,
    paddingHorizontal: 16,
  },
  categoryCard: {
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  categoryHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  categoryIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  categoryInfo: {
    flex: 1,
  },
  categoryName: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 2,
  },
  categoryAmount: {
    fontSize: 14,
  },
  categoryStatus: {
    alignItems: 'flex-end',
    gap: 2,
  },
  categoryPercentage: {
    fontSize: 14,
    fontWeight: 'bold',
  },
  categoryFooter: {
    marginTop: 8,
  },
  remainingAmount: {
    fontSize: 12,
    fontWeight: '600',
  },
  modalContainer: {
    flex: 1,
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  saveButton: {
    fontSize: 16,
    fontWeight: '600',
  },
  modalContent: {
    flex: 1,
    padding: 16,
  },
  fieldLabel: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
    marginTop: 16,
  },
  categorySelector: {
    marginBottom: 8,
  },
  categoryOption: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    marginRight: 8,
    gap: 8,
    minWidth: 120,
  },
  categoryOptionText: {
    fontSize: 12,
    fontWeight: '500',
  },
  textInput: {
    padding: 12,
    borderRadius: 8,
    fontSize: 16,
    marginBottom: 8,
  },
});
