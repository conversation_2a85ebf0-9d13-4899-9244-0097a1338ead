/**
 * Simple Performance Fix for First-Click Issues
 * 
 * This provides a lightweight solution to improve first-click performance
 * without causing build issues.
 */

// Simple route prefetching
export function prefetchRoute(href: string) {
  if (typeof window !== 'undefined') {
    const link = document.createElement('link')
    link.rel = 'prefetch'
    link.href = href
    document.head.appendChild(link)
  }
}

// Prefetch critical routes on page load
export function initializePerformanceOptimizations() {
  if (typeof window === 'undefined') return

  // Wait for page to load
  window.addEventListener('load', () => {
    // Prefetch critical routes after a delay
    setTimeout(() => {
      const criticalRoutes = [
        '/vendors',
        '/venues', 
        '/shop',
        '/planning',
        '/dashboard'
      ]

      criticalRoutes.forEach(route => {
        prefetchRoute(route)
      })
    }, 1000)
  })

  // Add hover prefetching to links
  document.addEventListener('mouseover', (e) => {
    const target = e.target as HTMLElement
    const link = target.closest('a[href^="/"]') as HTMLAnchorElement
    
    if (link && link.href) {
      const href = new URL(link.href).pathname
      prefetchRoute(href)
    }
  })
}

// Cache auth state to reduce API calls
export function cacheAuthState(authData: any) {
  if (typeof window !== 'undefined') {
    try {
      sessionStorage.setItem('auth-cache', JSON.stringify({
        ...authData,
        timestamp: Date.now()
      }))
    } catch (error) {
      console.error('Failed to cache auth state:', error)
    }
  }
}

export function getCachedAuthState() {
  if (typeof window === 'undefined') return null

  try {
    const cached = sessionStorage.getItem('auth-cache')
    if (!cached) return null

    const data = JSON.parse(cached)
    const isExpired = Date.now() - data.timestamp > 5 * 60 * 1000 // 5 minutes

    if (isExpired) {
      sessionStorage.removeItem('auth-cache')
      return null
    }

    return data
  } catch (error) {
    console.error('Failed to get cached auth state:', error)
    return null
  }
}

// Initialize on import
if (typeof window !== 'undefined') {
  initializePerformanceOptimizations()
}
