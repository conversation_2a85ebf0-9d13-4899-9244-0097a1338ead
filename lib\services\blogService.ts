import { generateClient } from 'aws-amplify/api';
import { getCurrentUser } from 'aws-amplify/auth';
import {
  createBlog,
  updateBlog,
  deleteBlog
} from '@/src/graphql/mutations';
import {
  getBlog,
  listBlogs,
  blogsByAuthorId
} from '@/src/graphql/queries';
import { BlogCategory, AuthorType, BlogStatus, Blog } from '@/src/graphql';

// Re-export types for convenience
export { BlogCategory, AuthorType, BlogStatus, Blog };

// Create client with user pool authentication for authenticated operations
const authenticatedClient = generateClient({
  authMode: 'userPool'
});

// Create client with API key for public operations
const publicClient = generateClient({
  authMode: 'apiKey'
});

export interface CreateBlogInput {
  title: string;
  content: string;
  excerpt?: string;
  category: BlogCategory;
  authorName: string;
  authorType: AuthorType;
  featuredImage?: string;
  tags?: string[];
  status?: BlogStatus;
  isFeatured?: boolean;
  isPinned?: boolean;
}

export interface UpdateBlogInput {
  id: string;
  title?: string;
  content?: string;
  excerpt?: string;
  category?: BlogCategory;
  featuredImage?: string;
  tags?: string[];
  status?: BlogStatus;
  isPinned?: boolean;
  isFeatured?: boolean;
}

export class BlogService {
  
  // Create a new blog post (all authenticated users)
  static async createBlog(input: CreateBlogInput): Promise<Blog> {
    try {
      const currentUser = await getCurrentUser();

      // Determine author type based on user role
      let authorType = input.authorType || AuthorType.VENDOR;
      if (currentUser.isAdmin || currentUser.role === 'ADMIN') {
        authorType = AuthorType.ADMIN;
      } else if (currentUser.isVendor || currentUser.role === 'VENDOR') {
        authorType = AuthorType.VENDOR;
      } else {
        // Regular users are treated as experts/contributors
        authorType = AuthorType.EXPERT;
      }

      const blogInput = {
        ...input,
        authorId: currentUser.userId,
        authorType,
        status: input.status || BlogStatus.DRAFT,
        views: 0,
        likes: 0,
        comments: 0,
        isPinned: input.isPinned || false,
        isFeatured: input.isFeatured || false,
        publishedAt: input.status === BlogStatus.PUBLISHED ? new Date().toISOString() : null,
      };

      const result = await authenticatedClient.graphql({
        query: createBlog,
        variables: { input: blogInput }
      }) as any;

      return result.data.createBlog;
    } catch (error) {
      console.error('Error creating blog:', error);
      throw error;
    }
  }

  // Update a blog post
  static async updateBlog(input: UpdateBlogInput, isAdmin: boolean = false): Promise<Blog> {
    try {
      const currentUser = await getCurrentUser();
      
      // Get the existing blog to check ownership
      const existingBlog = await this.getBlog(input.id);
      
      // Check if user is the author or admin
      if (!isAdmin && existingBlog.authorId !== currentUser.userId) {
        throw new Error('You can only edit your own blog posts');
      }

      const updateInput = {
        ...input,
        publishedAt: input.status === BlogStatus.PUBLISHED ? new Date().toISOString() : existingBlog.publishedAt,
      };

      const result = await authenticatedClient.graphql({
        query: updateBlog,
        variables: { input: updateInput }
      }) as any;

      return result.data.updateBlog;
    } catch (error) {
      console.error('Error updating blog:', error);
      throw error;
    }
  }

  // Delete a blog post
  static async deleteBlog(id: string, isAdmin: boolean = false): Promise<void> {
    try {
      const currentUser = await getCurrentUser();
      
      // Get the existing blog to check ownership
      const existingBlog = await this.getBlog(id);
      
      // Check if user is the author or admin
      if (!isAdmin && existingBlog.authorId !== currentUser.userId) {
        throw new Error('You can only delete your own blog posts');
      }

      await authenticatedClient.graphql({
        query: deleteBlog,
        variables: { input: { id } }
      }) as any;
    } catch (error) {
      console.error('Error deleting blog:', error);
      throw error;
    }
  }

  // Get a single blog post
  static async getBlog(id: string): Promise<Blog> {
    try {
      const result = await publicClient.graphql({
        query: getBlog,
        variables: { id }
      });

      return result.data.getBlog;
    } catch (error) {
      console.error('Error getting blog:', error);
      throw error;
    }
  }

  // List blogs with filters (admin method)
  static async listBlogs(filters: any = {}, limit: number = 20, nextToken?: string): Promise<{ blogs: Blog[], nextToken?: string }> {
    try {
      // Build filter object
      const filter: any = {};

      if (filters.status) {
        filter.status = { eq: filters.status };
      }

      if (filters.category) {
        filter.category = { eq: filters.category };
      }

      if (filters.authorType) {
        filter.authorType = { eq: filters.authorType };
      }

      if (filters.isFeatured !== undefined) {
        filter.isFeatured = { eq: filters.isFeatured };
      }

      if (filters.isPinned !== undefined) {
        filter.isPinned = { eq: filters.isPinned };
      }

      if (filters.searchTerm) {
        filter.or = [
          { title: { contains: filters.searchTerm } },
          { content: { contains: filters.searchTerm } },
          { excerpt: { contains: filters.searchTerm } }
        ];
      }

      const result = await authenticatedClient.graphql({
        query: listBlogs,
        variables: {
          filter: Object.keys(filter).length > 0 ? filter : undefined,
          limit,
          nextToken
        }
      }) as any;

      return {
        blogs: result.data.listBlogs.items,
        nextToken: result.data.listBlogs.nextToken
      };
    } catch (error) {
      console.error('Error listing blogs:', error);
      throw error;
    }
  }

  // List all published blog posts
  static async listPublishedBlogs(limit: number = 20, nextToken?: string): Promise<{ blogs: Blog[], nextToken?: string }> {
    try {
      const result = await publicClient.graphql({
        query: listBlogs,
        variables: {
          filter: { status: { eq: BlogStatus.PUBLISHED } },
          limit,
          nextToken
        }
      });

      return {
        blogs: result.data.listBlogs.items,
        nextToken: result.data.listBlogs.nextToken
      };
    } catch (error) {
      console.error('Error listing blogs:', error);
      throw error;
    }
  }

  // List blogs by category
  static async listBlogsByCategory(category: BlogCategory, limit: number = 20, nextToken?: string): Promise<{ blogs: Blog[], nextToken?: string }> {
    try {
      const result = await publicClient.graphql({
        query: listBlogs,
        variables: {
          filter: {
            status: { eq: BlogStatus.PUBLISHED },
            category: { eq: category }
          },
          limit,
          nextToken
        }
      });

      return {
        blogs: result.data.listBlogs.items,
        nextToken: result.data.listBlogs.nextToken
      };
    } catch (error) {
      console.error('Error listing blogs by category:', error);
      throw error;
    }
  }

  // List blogs by author (vendor)
  static async listBlogsByAuthor(authorId: string, limit: number = 20, nextToken?: string): Promise<{ blogs: Blog[], nextToken?: string }> {
    try {
      const result = await publicClient.graphql({
        query: blogsByAuthorId,
        variables: {
          authorId,
          limit,
          nextToken
        }
      });

      return {
        blogs: result.data.blogsByAuthorId.items,
        nextToken: result.data.blogsByAuthorId.nextToken
      };
    } catch (error) {
      console.error('Error listing blogs by author:', error);
      throw error;
    }
  }

  // Get current user's blogs
  static async getCurrentUserBlogs(limit: number = 20, nextToken?: string): Promise<{ blogs: Blog[], nextToken?: string }> {
    try {
      const currentUser = await getCurrentUser();
      return await this.listBlogsByAuthor(currentUser.userId, limit, nextToken);
    } catch (error) {
      console.error('Error getting current user blogs:', error);
      throw error;
    }
  }

  // Get featured blogs
  static async getFeaturedBlogs(limit: number = 5): Promise<Blog[]> {
    try {
      const result = await publicClient.graphql({
        query: listBlogs,
        variables: {
          filter: {
            status: { eq: BlogStatus.PUBLISHED },
            isFeatured: { eq: true }
          },
          limit
        }
      });

      return result.data.listBlogs.items;
    } catch (error) {
      console.error('Error getting featured blogs:', error);
      throw error;
    }
  }

  // Get pinned blogs
  static async getPinnedBlogs(limit: number = 3): Promise<Blog[]> {
    try {
      const result = await publicClient.graphql({
        query: listBlogs,
        variables: {
          filter: {
            status: { eq: BlogStatus.PUBLISHED },
            isPinned: { eq: true }
          },
          limit
        }
      });

      return result.data.listBlogs.items;
    } catch (error) {
      console.error('Error getting pinned blogs:', error);
      throw error;
    }
  }

  // Search blogs
  static async searchBlogs(searchTerm: string, limit: number = 20, nextToken?: string): Promise<{ blogs: Blog[], nextToken?: string }> {
    try {
      const result = await publicClient.graphql({
        query: listBlogs,
        variables: {
          filter: {
            status: { eq: BlogStatus.PUBLISHED },
            or: [
              { title: { contains: searchTerm } },
              { content: { contains: searchTerm } },
              { excerpt: { contains: searchTerm } }
            ]
          },
          limit,
          nextToken
        }
      });

      return {
        blogs: result.data.listBlogs.items,
        nextToken: result.data.listBlogs.nextToken
      };
    } catch (error) {
      console.error('Error searching blogs:', error);
      throw error;
    }
  }

  // Increment view count
  static async incrementViews(id: string): Promise<void> {
    try {
      const blog = await this.getBlog(id);
      await this.updateBlog({
        id,
        views: (blog.views || 0) + 1
      });
    } catch (error) {
      console.error('Error incrementing views:', error);
      // Don't throw error for view increment failures
    }
  }

  // Check if user is a vendor
  static async isVendor(): Promise<boolean> {
    try {
      const currentUser = await getCurrentUser();
      // This would need to be implemented based on your user role system
      // For now, we'll assume all authenticated users can create blogs
      return true;
    } catch (error) {
      return false;
    }
  }

  // Admin-specific methods
  static async adminUpdateBlog(input: UpdateBlogInput): Promise<Blog> {
    try {
      const result = await authenticatedClient.graphql({
        query: updateBlog,
        variables: { input }
      }) as any;

      return result.data.updateBlog;
    } catch (error) {
      console.error('Error admin updating blog:', error);
      throw error;
    }
  }

  static async adminDeleteBlog(id: string): Promise<void> {
    try {
      await authenticatedClient.graphql({
        query: deleteBlog,
        variables: { input: { id } }
      });
    } catch (error) {
      console.error('Error admin deleting blog:', error);
      throw error;
    }
  }

  static async bulkUpdateBlogs(blogIds: string[], updates: Partial<UpdateBlogInput>): Promise<void> {
    try {
      const promises = blogIds.map(id =>
        this.adminUpdateBlog({ id, ...updates })
      );
      await Promise.all(promises);
    } catch (error) {
      console.error('Error bulk updating blogs:', error);
      throw error;
    }
  }

  static async bulkDeleteBlogs(blogIds: string[]): Promise<void> {
    try {
      const promises = blogIds.map(id => this.adminDeleteBlog(id));
      await Promise.all(promises);
    } catch (error) {
      console.error('Error bulk deleting blogs:', error);
      throw error;
    }
  }

  // Get blogs by status for admin
  static async getBlogsByStatus(status: BlogStatus, limit: number = 50, nextToken?: string): Promise<{ blogs: Blog[], nextToken?: string }> {
    try {
      return await this.listBlogs({ status }, limit, nextToken);
    } catch (error) {
      console.error('Error getting blogs by status:', error);
      throw error;
    }
  }

  // Get blogs by author type for admin
  static async getBlogsByAuthorType(authorType: AuthorType, limit: number = 50, nextToken?: string): Promise<{ blogs: Blog[], nextToken?: string }> {
    try {
      return await this.listBlogs({ authorType }, limit, nextToken);
    } catch (error) {
      console.error('Error getting blogs by author type:', error);
      throw error;
    }
  }

  // Get recent blogs for admin dashboard
  static async getRecentBlogs(limit: number = 10): Promise<Blog[]> {
    try {
      const result = await this.listBlogs({}, limit);
      return result.blogs;
    } catch (error) {
      console.error('Error getting recent blogs:', error);
      throw error;
    }
  }

  // Get blog statistics
  static async getBlogStatistics(): Promise<{
    totalBlogs: number;
    publishedBlogs: number;
    draftBlogs: number;
    featuredBlogs: number;
    totalViews: number;
    totalLikes: number;
  }> {
    try {
      const [allBlogs, publishedBlogs, draftBlogs, featuredBlogs] = await Promise.all([
        this.listBlogs({}, 1000),
        this.listBlogs({ status: BlogStatus.PUBLISHED }, 1000),
        this.listBlogs({ status: BlogStatus.DRAFT }, 1000),
        this.listBlogs({ isFeatured: true }, 1000)
      ]);

      const totalViews = allBlogs.blogs.reduce((sum, blog) => sum + (blog.views || 0), 0);
      const totalLikes = allBlogs.blogs.reduce((sum, blog) => sum + (blog.likes || 0), 0);

      return {
        totalBlogs: allBlogs.blogs.length,
        publishedBlogs: publishedBlogs.blogs.length,
        draftBlogs: draftBlogs.blogs.length,
        featuredBlogs: featuredBlogs.blogs.length,
        totalViews,
        totalLikes
      };
    } catch (error) {
      console.error('Error getting blog statistics:', error);
      throw error;
    }
  }
}

// Category mapping for display
export const BLOG_CATEGORIES = {
  [BlogCategory.WEDDING_PLANNING]: {
    title: 'Wedding Planning',
    description: 'General wedding planning discussions and tips',
    color: 'bg-pink-100 text-pink-800',
    icon: '🎯'
  },
  [BlogCategory.VENUE_SELECTION]: {
    title: 'Venue Selection',
    description: 'Share and discuss wedding venues',
    color: 'bg-blue-100 text-blue-800',
    icon: '🏛️'
  },
  [BlogCategory.PHOTOGRAPHY_VIDEOGRAPHY]: {
    title: 'Photography & Videography',
    description: 'Tips and recommendations for wedding photography',
    color: 'bg-purple-100 text-purple-800',
    icon: '📸'
  },
  [BlogCategory.CATERING_FOOD]: {
    title: 'Catering & Food',
    description: 'Wedding menu planning and catering discussions',
    color: 'bg-green-100 text-green-800',
    icon: '🍽️'
  },
  [BlogCategory.DECORATIONS_THEMES]: {
    title: 'Decorations & Themes',
    description: 'Wedding decoration ideas and themes',
    color: 'bg-yellow-100 text-yellow-800',
    icon: '🌸'
  },
  [BlogCategory.BUDGET_FINANCE]: {
    title: 'Budget & Finance',
    description: 'Wedding budget planning and cost-saving tips',
    color: 'bg-red-100 text-red-800',
    icon: '💰'
  },
  [BlogCategory.FASHION_STYLE]: {
    title: 'Fashion & Style',
    description: 'Wedding fashion trends and styling tips',
    color: 'bg-indigo-100 text-indigo-800',
    icon: '👗'
  },
  [BlogCategory.REAL_WEDDINGS]: {
    title: 'Real Weddings',
    description: 'Real wedding stories and inspiration',
    color: 'bg-teal-100 text-teal-800',
    icon: '💒'
  },
  [BlogCategory.EXPERT_TIPS]: {
    title: 'Expert Tips',
    description: 'Professional advice from wedding experts',
    color: 'bg-orange-100 text-orange-800',
    icon: '💡'
  },
  [BlogCategory.VENDOR_SPOTLIGHT]: {
    title: 'Vendor Spotlight',
    description: 'Featured vendors and their stories',
    color: 'bg-cyan-100 text-cyan-800',
    icon: '⭐'
  }
}; 