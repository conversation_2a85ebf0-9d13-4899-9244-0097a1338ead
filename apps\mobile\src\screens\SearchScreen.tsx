import React, { useState, useEffect } from 'react';
import { View, Text, TextInput, StyleSheet, TouchableOpacity, FlatList, ScrollView, Modal } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../providers/ThemeProvider';
import { graphqlService } from '../services/graphqlService';
import LoadingSpinner from '../components/LoadingSpinner';
import ErrorMessage from '../components/ErrorMessage';
import EmptyState from '../components/EmptyState';

interface SearchResult {
  id: string;
  name: string;
  type: 'vendor' | 'venue' | 'product';
  location?: string;
  price?: number;
  rating: number;
  image?: string;
  category?: string;
  city?: string;
  state?: string;
  verified?: boolean;
  featured?: boolean;
}

interface SearchFilters {
  type: 'all' | 'vendor' | 'venue' | 'product';
  category: string;
  city: string;
  priceRange: {
    min: number;
    max: number;
  };
  rating: number;
  verified: boolean;
  featured: boolean;
}

const mockResults: SearchResult[] = [
  {
    id: '1',
    name: 'Royal Photography Studio',
    type: 'vendor',
    location: 'Chennai, Tamil Nadu',
    rating: 4.8,
    image: '/placeholder-image.jpg
  },
  {
    id: '2',
    name: 'Grand Palace Wedding Hall',
    type: 'venue',
    location: 'Mumbai, Maharashtra',
    rating: 4.6,
    image: '/placeholder-image.jpg
  },
  {
    id: '3',
    name: 'Designer Bridal Collection',
    type: 'product',
    price: 45000,
    rating: 4.7,
    image: '/placeholder-image.jpg
  },
];

const recentSearches = [
  'Wedding photographers Chennai',
  'Bridal makeup artists',
  'Wedding venues Mumbai',
  'Mehendi artists',
  'Wedding decorators',
];

const popularSearches = [
  'Photographers',
  'Venues',
  'Catering',
  'Decorators',
  'Makeup Artists',
  'Bridal Wear',
  'Jewelry',
  'Invitations',
];

export default function SearchScreen() {
  const { theme } = useTheme();
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<SearchResult[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showFilters, setShowFilters] = useState(false);
  const [filters, setFilters] = useState<SearchFilters>({
    type: 'all',
    category: '',
    city: '',
    priceRange: { min: 0, max: 1000000 },
    rating: 0,
    verified: false,
    featured: false,
  });

  const handleSearch = async (query: string) => {
    setSearchQuery(query);
    setError(null);

    if (query.length > 2) {
      setIsSearching(true);
      try {
        const results = await performSearch(query, filters);
        setSearchResults(results);
      } catch (err) {
        setError('Failed to search. Please try again.');
        setSearchResults([]);
      } finally {
        setIsSearching(false);
      }
    } else {
      setSearchResults([]);
    }
  };

  const performSearch = async (query: string, searchFilters: SearchFilters): Promise<SearchResult[]> => {
    const results: SearchResult[] = [];

    try {
      // Search vendors
      if (searchFilters.type === 'all' || searchFilters.type === 'vendor') {
        const vendorFilter: any = {};

        if (query) {
          vendorFilter.or = [
            { name: { contains: query } },
            { category: { contains: query } },
            { description: { contains: query } }
          ];
        }

        if (searchFilters.city) {
          vendorFilter.city = { eq: searchFilters.city };
        }

        if (searchFilters.category) {
          vendorFilter.category = { eq: searchFilters.category };
        }

        if (searchFilters.verified) {
          vendorFilter.verified = { eq: true };
        }

        if (searchFilters.featured) {
          vendorFilter.featured = { eq: true };
        }

        const vendorResults = await graphqlService.listVendors(vendorFilter, 20);

        vendorResults.items.forEach((vendor: any) => {
          results.push({
            id: vendor.id,
            name: vendor.name,
            type: 'vendor',
            location: `${vendor.city}, ${vendor.state}`,
            rating: vendor.rating || 0,
            image: vendor.profilePhoto,
            category: vendor.category,
            city: vendor.city,
            state: vendor.state,
            verified: vendor.verified,
            featured: vendor.featured,
          });
        });
      }

      // Search venues
      if (searchFilters.type === 'all' || searchFilters.type === 'venue') {
        const venueFilter: any = {};

        if (query) {
          venueFilter.or = [
            { name: { contains: query } },
            { type: { contains: query } },
            { description: { contains: query } }
          ];
        }

        if (searchFilters.city) {
          venueFilter.city = { eq: searchFilters.city };
        }

        const venueResults = await graphqlService.listVenues(venueFilter, 20);

        venueResults.items.forEach((venue: any) => {
          results.push({
            id: venue.id,
            name: venue.name,
            type: 'venue',
            location: `${venue.city}, ${venue.state}`,
            price: venue.price,
            rating: venue.rating || 0,
            image: venue.images ? JSON.parse(venue.images)[0] : undefined,
            city: venue.city,
            state: venue.state,
            verified: venue.verified,
            featured: venue.featured,
          });
        });
      }

      // Search products
      if (searchFilters.type === 'all' || searchFilters.type === 'product') {
        const shopFilter: any = {};

        if (query) {
          shopFilter.or = [
            { name: { contains: query } },
            { category: { contains: query } },
            { description: { contains: query } }
          ];
        }

        if (searchFilters.category) {
          shopFilter.category = { eq: searchFilters.category };
        }

        const shopResults = await graphqlService.listShops(shopFilter, 20);

        shopResults.items.forEach((product: any) => {
          results.push({
            id: product.id,
            name: product.name,
            type: 'product',
            price: product.price,
            rating: product.rating || 0,
            image: product.images ? JSON.parse(product.images)[0] : undefined,
            category: product.category,
            featured: product.featured,
          });
        });
      }

      // Apply additional filters
      let filteredResults = results;

      if (searchFilters.rating > 0) {
        filteredResults = filteredResults.filter(item => item.rating >= searchFilters.rating);
      }

      if (searchFilters.priceRange.min > 0 || searchFilters.priceRange.max < 1000000) {
        filteredResults = filteredResults.filter(item => {
          if (!item.price) return true;
          return item.price >= searchFilters.priceRange.min && item.price <= searchFilters.priceRange.max;
        });
      }

      return filteredResults;
    } catch (error) {
      console.error('Search error:', error);
      throw error;
    }
  };

  const renderSearchResult = ({ item }: { item: SearchResult }) => (
    <TouchableOpacity style={[styles.resultCard, { backgroundColor: theme.colors.surface }]}>
      <View style={styles.resultImage} />
      
      <View style={styles.resultInfo}>
        <Text style={[styles.resultName, { color: theme.colors.text }]} numberOfLines={1}>
          {item.name}
        </Text>
        
        <View style={styles.resultDetails}>
          <Ionicons 
            name={item.type === 'vendor' ? 'people' : item.type === 'venue' ? 'location' : 'bag'} 
            size={14} 
            color={theme.colors.textSecondary} 
          />
          <Text style={[styles.resultType, { color: theme.colors.textSecondary }]}>
            {item.type.charAt(0).toUpperCase() + item.type.slice(1)}
          </Text>
        </View>
        
        {item.location && (
          <Text style={[styles.resultLocation, { color: theme.colors.textSecondary }]}>
            {item.location}
          </Text>
        )}
        
        {item.price && (
          <Text style={[styles.resultPrice, { color: theme.colors.primary }]}>
            ₹{item.price.toLocaleString()}
          </Text>
        )}
        
        <View style={styles.ratingContainer}>
          <Ionicons name="star" size={14} color="#F59E0B" />
          <Text style={[styles.rating, { color: theme.colors.text }]}>
            {item.rating}
          </Text>
        </View>
      </View>
      
      <TouchableOpacity style={styles.favoriteButton}>
        <Ionicons name="heart-outline" size={20} color={theme.colors.textSecondary} />
      </TouchableOpacity>
    </TouchableOpacity>
  );

  const renderSearchSuggestion = (text: string, isRecent: boolean = false) => (
    <TouchableOpacity
      key={text}
      style={styles.suggestionItem}
      onPress={() => handleSearch(text)}
    >
      <Ionicons 
        name={isRecent ? 'time-outline' : 'trending-up-outline'} 
        size={16} 
        color={theme.colors.textSecondary} 
      />
      <Text style={[styles.suggestionText, { color: theme.colors.text }]}>
        {text}
      </Text>
    </TouchableOpacity>
  );

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {/* Search Bar */}
      <View style={[styles.searchContainer, { backgroundColor: theme.colors.surface }]}>
        <Ionicons name="search" size={20} color={theme.colors.textSecondary} />
        <TextInput
          style={[styles.searchInput, { color: theme.colors.text }]}
          placeholder="Search vendors, venues, products..."
          placeholderTextColor={theme.colors.textSecondary}
          value={searchQuery}
          onChangeText={handleSearch}
          autoFocus
        />
        {searchQuery.length > 0 && (
          <TouchableOpacity onPress={() => handleSearch('')}>
            <Ionicons name="close-circle" size={20} color={theme.colors.textSecondary} />
          </TouchableOpacity>
        )}
      </View>

      {/* Search Results */}
      {searchQuery.length > 0 ? (
        <View style={styles.resultsContainer}>
          {isSearching ? (
            <View style={styles.loadingContainer}>
              <Text style={[styles.loadingText, { color: theme.colors.textSecondary }]}>
                Searching...
              </Text>
            </View>
          ) : searchResults.length > 0 ? (
            <FlatList
              data={searchResults}
              renderItem={renderSearchResult}
              keyExtractor={(item) => item.id}
              contentContainerStyle={styles.resultsList}
              showsVerticalScrollIndicator={false}
            />
          ) : (
            <View style={styles.noResultsContainer}>
              <Ionicons name="search-outline" size={48} color={theme.colors.textSecondary} />
              <Text style={[styles.noResultsTitle, { color: theme.colors.text }]}>
                No results found
              </Text>
              <Text style={[styles.noResultsSubtitle, { color: theme.colors.textSecondary }]}>
                Try searching with different keywords
              </Text>
            </View>
          )}
        </View>
      ) : (
        <ScrollView style={styles.suggestionsContainer} showsVerticalScrollIndicator={false}>
          {/* Recent Searches */}
          <View style={styles.suggestionSection}>
            <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
              Recent Searches
            </Text>
            {recentSearches.map(search => renderSearchSuggestion(search, true))}
          </View>

          {/* Popular Searches */}
          <View style={styles.suggestionSection}>
            <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
              Popular Searches
            </Text>
            <View style={styles.popularGrid}>
              {popularSearches.map(search => (
                <TouchableOpacity
                  key={search}
                  style={[styles.popularChip, { backgroundColor: theme.colors.surface }]}
                  onPress={() => handleSearch(search)}
                >
                  <Text style={[styles.popularText, { color: theme.colors.text }]}>
                    {search}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>
        </ScrollView>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    margin: 16,
    paddingHorizontal: 12,
    paddingVertical: 10,
    borderRadius: 8,
    gap: 10,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
  },
  resultsContainer: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
  },
  resultsList: {
    paddingHorizontal: 16,
    paddingBottom: 20,
  },
  resultCard: {
    flexDirection: 'row',
    padding: 12,
    borderRadius: 12,
    marginBottom: 12,
    alignItems: 'center',
  },
  resultImage: {
    width: 60,
    height: 60,
    backgroundColor: '#E5E7EB',
    borderRadius: 8,
    marginRight: 12,
  },
  resultInfo: {
    flex: 1,
    gap: 4,
  },
  resultName: {
    fontSize: 16,
    fontWeight: '600',
  },
  resultDetails: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  resultType: {
    fontSize: 12,
    textTransform: 'capitalize',
  },
  resultLocation: {
    fontSize: 14,
  },
  resultPrice: {
    fontSize: 14,
    fontWeight: 'bold',
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  rating: {
    fontSize: 12,
    fontWeight: '500',
  },
  favoriteButton: {
    padding: 8,
  },
  noResultsContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  noResultsTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginTop: 16,
    marginBottom: 8,
  },
  noResultsSubtitle: {
    fontSize: 14,
    textAlign: 'center',
  },
  suggestionsContainer: {
    flex: 1,
    paddingHorizontal: 16,
  },
  suggestionSection: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 12,
  },
  suggestionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    gap: 12,
  },
  suggestionText: {
    fontSize: 16,
  },
  popularGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  popularChip: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
  },
  popularText: {
    fontSize: 14,
    fontWeight: '500',
  },
});
