const fs = require('fs');
const path = require('path');

// Help translations for different languages
const helpTranslations = {
  ta: {
    "title": "நாங்கள் உங்களுக்கு எவ்வாறு உதவ முடியும்?",
    "subtitle": "பதில்களைக் கண்டறியுங்கள், ஆதரவைப் பெறுங்கள், மற்றும் திருமணம் 360 ஐ எவ்வாறு சிறப்பாகப் பயன்படுத்துவது என்பதைக் கற்றுக்கொள்ளுங்கள்",
    "searchPlaceholder": "உதவி கட்டுரைகள், வழிகாட்டிகள் அல்லது அம்சங்களைத் தேடுங்கள்...",
    "quickHelp": {
      "title": "விரைவு உதவி",
      "videoTutorials": {
        "title": "வீடியோ பயிற்சிகள்",
        "description": "படிப்படியான வழிகாட்டிகளைப் பார்க்கவும்"
      },
      "userManual": {
        "title": "பயனர் கையேடு",
        "description": "முழுமையான ஆவணங்கள்"
      },
      "liveChat": {
        "title": "நேரடி அரட்டை",
        "description": "உடனடி உதவி பெறுங்கள்"
      },
      "contactSupport": {
        "title": "ஆதரவைத் தொடர்பு கொள்ளுங்கள்",
        "description": "எங்களை அழைக்கவும் அல்லது மின்னஞ்சல் அனுப்பவும்"
      },
      "access": "அணுகல்",
      "startChat": "அரட்டையைத் தொடங்கவும்"
    },
    "categories": {
      "title": "வகையின் அடிப்படையில் உலாவவும்",
      "gettingStarted": {
        "title": "தொடங்குதல்",
        "description": "திருமணம் 360 பயன்படுத்துவதற்கான அடிப்படைகளைக் கற்றுக்கொள்ளுங்கள்",
        "articles": [
          "உங்கள் கணக்கை உருவாக்குதல்",
          "உங்கள் சுயவிவரத்தை அமைத்தல்",
          "பயனர் வகைகளைப் புரிந்துகொள்ளுதல்",
          "முதல் படிகள் வழிகாட்டி"
        ]
      },
      "findingVendors": {
        "title": "விற்பனையாளர்களைக் கண்டறிதல்",
        "description": "திருமண விற்பனையாளர்களைத் தேடுவது மற்றும் தொடர்பு கொள்வது எப்படி",
        "articles": [
          "விற்பனையாளர்களைத் தேடுதல்",
          "வடிகட்டிகளை திறம்பட பயன்படுத்துதல்",
          "விற்பனையாளர் சுயவிவரங்களைப் படித்தல்",
          "விற்பனையாளர்களைத் தொடர்பு கொள்ளுதல்"
        ]
      },
      "bookingVenues": {
        "title": "இடங்களை முன்பதிவு செய்தல்",
        "description": "இடங்களைக் கண்டறிதல் மற்றும் முன்பதிவு செய்வது பற்றி அனைத்தும்",
        "articles": [
          "இட தேடல் குறிப்புகள்",
          "இடங்களை ஒப்பிடுதல்",
          "முன்பதிவு செயல்முறை",
          "இட தேவைகள்"
        ]
      },
      "shopping": {
        "title": "வாங்குதல்",
        "description": "திருமண அத்தியாவசியங்களை வாங்குவதற்கான வழிகாட்டி",
        "articles": [
          "பொருட்களை உலாவுதல்",
          "கார்ட்டில் சேர்த்தல்",
          "செக்அவுட் செயல்முறை",
          "ஆர்டர் கண்காணிப்பு"
        ]
      },
      "weddingPlanning": {
        "title": "திருமண திட்டமிடல்",
        "description": "உங்கள் திருமணத்தைத் திட்டமிடுவதற்கான கருவிகள் மற்றும் குறிப்புகள்",
        "articles": [
          "திருமண சரிபார்ப்பு பட்டியல்",
          "பட்ஜெட் திட்டமிடல்",
          "விருந்தினர் பட்டியல் மேலாண்மை",
          "கால அட்டவணை உருவாக்கம்"
        ]
      },
      "reviewsRatings": {
        "title": "மதிப்புரைகள் மற்றும் மதிப்பீடுகள்",
        "description": "மதிப்புரைகளை எழுதுவது மற்றும் படிப்பது எப்படி",
        "articles": [
          "மதிப்புரைகள் எழுதுதல்",
          "மதிப்பீட்டு முறை",
          "மதிப்புரை வழிகாட்டுதல்கள்",
          "உங்கள் மதிப்புரைகளை நிர்வகித்தல்"
        ]
      },
      "viewAllArticles": "அனைத்து கட்டுரைகளையும் பார்க்கவும்"
    },
    "popularArticles": {
      "title": "பிரபலமான கட்டுரைகள்",
      "articles": [
        "விற்பனையாளர் கணக்கை எவ்வாறு உருவாக்குவது",
        "உங்கள் திருமண பட்ஜெட்டை அமைத்தல்",
        "உங்கள் நகரத்தில் புகைப்படக்காரர்களைக் கண்டறிதல்",
        "முன்பதிவு கொள்கைகளைப் புரிந்துகொள்ளுதல்",
        "உங்கள் திருமண சரிபார்ப்பு பட்டியலை நிர்வகித்தல்",
        "பணம் செலுத்துதல் மற்றும் திரும்பப் பெறுதல் கொள்கைகள்"
      ]
    },
    "stillNeedHelp": {
      "title": "இன்னும் உதவி தேவையா?",
      "subtitle": "நீங்கள் தேடுவது கிடைக்கவில்லையா? எங்கள் ஆதரவு குழு உங்களுக்கு உதவ இங்கே உள்ளது.",
      "contactSupport": "ஆதரவைத் தொடர்பு கொள்ளுங்கள்",
      "callUs": "எங்களை அழைக்கவும்: +91 8148376909"
    },
    "faq": {
      "title": "அடிக்கடி கேட்கப்படும் கேள்விகள்",
      "subtitle": "திருமணம் 360 பயன்படுத்துவது பற்றிய மிகவும் பொதுவான கேள்விகளுக்கு விரைவான பதில்களைக் கண்டறியுங்கள். நீங்கள் தேடுவது கிடைக்கவில்லையா? எங்கள் ஆதரவு குழு உங்களுக்கு உதவ இங்கே உள்ளது.",
      "backToHelp": "உதவி மையத்திற்குத் திரும்பவும்",
      "stillHaveQuestions": {
        "title": "இன்னும் கேள்விகள் உள்ளதா?",
        "subtitle": "எங்கள் FAQ இல் உள்ளடக்கப்படாத எந்தவொரு கேள்விகளுக்கும் எங்கள் ஆதரவு குழு உங்களுக்கு உதவ தயாராக உள்ளது.",
        "liveChat": {
          "title": "நேரடி அரட்டை",
          "description": "எங்கள் ஆதரவு குழுவிடமிருந்து உடனடி உதவி பெறுங்கள்"
        },
        "phoneSupport": {
          "title": "தொலைபேசி ஆதரவு",
          "description": "திங்கள் முதல் சனி வரை, காலை 9 மணி முதல் இரவு 8 மணி வரை எங்களை அழைக்கவும்"
        },
        "emailSupport": {
          "title": "மின்னஞ்சல் ஆதரவு",
          "description": "எங்களுக்கு ஒரு மின்னஞ்சல் அனுப்புங்கள், நாங்கள் 24 மணி நேரத்திற்குள் பதிலளிப்போம்",
          "sendEmail": "மின்னஞ்சல் அனுப்பவும்"
        }
      }
    }
  },
  hi: {
    "title": "हम आपकी कैसे मदद कर सकते हैं?",
    "subtitle": "उत्तर खोजें, सहायता प्राप्त करें, और जानें कि BookmyFestive का सबसे अच्छा उपयोग कैसे करें",
    "searchPlaceholder": "सहायता लेख, गाइड या सुविधाओं की खोज करें...",
    "quickHelp": {
      "title": "त्वरित सहायता",
      "videoTutorials": {
        "title": "वीडियो ट्यूटोरियल",
        "description": "चरणबद्ध गाइड देखें"
      },
      "userManual": {
        "title": "उपयोगकर्ता मैनुअल",
        "description": "पूर्ण दस्तावेज़"
      },
      "liveChat": {
        "title": "लाइव चैट",
        "description": "तुरंत सहायता प्राप्त करें"
      },
      "contactSupport": {
        "title": "सहायता से संपर्क करें",
        "description": "हमें कॉल करें या ईमेल करें"
      },
      "access": "पहुंच",
      "startChat": "चैट शुरू करें"
    },
    "categories": {
      "title": "श्रेणी के अनुसार ब्राउज़ करें",
      "gettingStarted": {
        "title": "शुरुआत करना",
        "description": "BookmyFestive का उपयोग करने की मूल बातें सीखें",
        "articles": [
          "अपना खाता बनाना",
          "अपनी प्रोफ़ाइल सेट करना",
          "उपयोगकर्ता प्रकारों को समझना",
          "पहले कदम गाइड"
        ]
      },
      "findingVendors": {
        "title": "विक्रेता खोजना",
        "description": "शादी के विक्रेताओं को कैसे खोजें और उनसे जुड़ें",
        "articles": [
          "विक्रेताओं की खोज",
          "फ़िल्टर का प्रभावी उपयोग",
          "विक्रेता प्रोफ़ाइल पढ़ना",
          "विक्रेताओं से संपर्क करना"
        ]
      },
      "bookingVenues": {
        "title": "स्थान बुकिंग",
        "description": "स्थान खोजने और बुक करने के बारे में सब कुछ",
        "articles": [
          "स्थान खोज टिप्स",
          "स्थानों की तुलना",
          "बुकिंग प्रक्रिया",
          "स्थान आवश्यकताएं"
        ]
      },
      "shopping": {
        "title": "खरीदारी",
        "description": "शादी की आवश्यकताओं की खरीदारी के लिए गाइड",
        "articles": [
          "उत्पादों को ब्राउज़ करना",
          "कार्ट में जोड़ना",
          "चेकआउट प्रक्रिया",
          "ऑर्डर ट्रैकिंग"
        ]
      },
      "weddingPlanning": {
        "title": "शादी की योजना",
        "description": "अपनी शादी की योजना बनाने के लिए उपकरण और टिप्स",
        "articles": [
          "शादी की चेकलिस्ट",
          "बजट योजना",
          "अतिथि सूची प्रबंधन",
          "समयसीमा निर्माण"
        ]
      },
      "reviewsRatings": {
        "title": "समीक्षा और रेटिंग",
        "description": "समीक्षा कैसे लिखें और पढ़ें",
        "articles": [
          "समीक्षा लिखना",
          "रेटिंग सिस्टम",
          "समीक्षा दिशानिर्देश",
          "अपनी समीक्षाओं का प्रबंधन"
        ]
      },
      "viewAllArticles": "सभी लेख देखें"
    },
    "popularArticles": {
      "title": "लोकप्रिय लेख",
      "articles": [
        "विक्रेता खाता कैसे बनाएं",
        "अपना शादी का बजट सेट करना",
        "अपने शहर में फोटोग्राफर खोजना",
        "बुकिंग नीतियों को समझना",
        "अपनी शादी की चेकलिस्ट का प्रबंधन",
        "भुगतान और रिफंड नीतियां"
      ]
    },
    "stillNeedHelp": {
      "title": "अभी भी मदद चाहिए?",
      "subtitle": "आप जो खोज रहे हैं वह नहीं मिल रहा? हमारी सहायता टीम आपकी मदद के लिए यहां है।",
      "contactSupport": "सहायता से संपर्क करें",
      "callUs": "हमें कॉल करें: +91 8148376909"
    },
    "faq": {
      "title": "अक्सर पूछे जाने वाले प्रश्न",
      "subtitle": "BookmyFestive के उपयोग के बारे में सबसे आम प्रश्नों के त्वरित उत्तर खोजें। आप जो खोज रहे हैं वह नहीं मिल रहा? हमारी सहायता टीम आपकी मदद के लिए यहां है।",
      "backToHelp": "सहायता केंद्र पर वापस जाएं",
      "stillHaveQuestions": {
        "title": "अभी भी प्रश्न हैं?",
        "subtitle": "हमारी सहायता टीम हमारे FAQ में शामिल नहीं किए गए किसी भी प्रश्न के साथ आपकी मदद करने के लिए तैयार है।",
        "liveChat": {
          "title": "लाइव चैट",
          "description": "हमारी सहायता टीम से तुरंत सहायता प्राप्त करें"
        },
        "phoneSupport": {
          "title": "फोन सहायता",
          "description": "सोमवार से शनिवार, सुबह 9 बजे से रात 8 बजे तक हमें कॉल करें"
        },
        "emailSupport": {
          "title": "ईमेल सहायता",
          "description": "हमें एक ईमेल भेजें और हम 24 घंटे के भीतर जवाब देंगे",
          "sendEmail": "ईमेल भेजें"
        }
      }
    }
  }
};

// Create help.json files for each language
const localesDir = path.join(__dirname, '../public/locales');
const languages = ['ta', 'hi', 'te', 'kn', 'ml', 'gu', 'mr', 'bn', 'or', 'as', 'ur', 'ne', 'pa'];

languages.forEach(lang => {
  const langDir = path.join(localesDir, lang);
  const helpFile = path.join(langDir, 'help.json');
  
  // Use specific translation if available, otherwise fallback to English
  const translations = helpTranslations[lang] || require('../public/locales/en/help.json');
  
  try {
    fs.writeFileSync(helpFile, JSON.stringify(translations, null, 2), 'utf8');
    console.log(`✅ Created help.json for ${lang}`);
  } catch (error) {
    console.error(`❌ Failed to create help.json for ${lang}:`, error.message);
  }
});

console.log('\n🎉 Help translations added successfully!');