"use client"

import { useTranslation } from '@/hooks/useTranslation';

interface TranslatedTextProps {
  translationKey: string;
  fallback?: string;
  values?: Record<string, any>;
  className?: string;
  as?: keyof JSX.IntrinsicElements;
}

export default function TranslatedText({ 
  translationKey, 
  fallback, 
  values, 
  className,
  as: Component = 'span'
}: TranslatedTextProps) {
  const { t } = useTranslation();
  
  const translatedText = t(translationKey, fallback, values);
  
  return (
    <Component className={className}>
      {translatedText}
    </Component>
  );
}

// Convenience components for common use cases
export function TranslatedHeading({ translationKey, fallback, values, className }: Omit<TranslatedTextProps, 'as'>) {
  return (
    <TranslatedText 
      translationKey={translationKey} 
      fallback={fallback} 
      values={values} 
      className={className} 
      as="h1" 
    />
  );
}

export function TranslatedParagraph({ translationKey, fallback, values, className }: Omit<TranslatedTextProps, 'as'>) {
  return (
    <TranslatedText 
      translationKey={translationKey} 
      fallback={fallback} 
      values={values} 
      className={className} 
      as="p" 
    />
  );
}

export function TranslatedButton({ translationKey, fallback, values, className, onClick }: Omit<TranslatedTextProps, 'as'> & { onClick?: () => void }) {
  return (
    <button className={className} onClick={onClick}>
      <TranslatedText 
        translationKey={translationKey} 
        fallback={fallback} 
        values={values} 
      />
    </button>
  );
}