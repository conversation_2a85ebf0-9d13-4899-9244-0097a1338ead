"use client"

import { TopHeader } from "@/components/top-header"
import { Head<PERSON> } from "@/components/header"
import { Footer } from "@/components/footer"
import Link from "next/link"

export default function PlanningSubmenuPage() {
  return (
    <div className="min-h-screen bg-white">
      <TopHeader />
      <Header />
      <section className="py-16">
        <div className="container mx-auto px-4">
          <h1 className="text-3xl md:text-4xl font-bold mb-6 text-center">திட்டமிடும் கருவிகள் (Planning Tools)</h1>
          <p className="text-lg text-gray-600 mb-10 text-center">உங்கள் திருமணத்தை திட்டமிட உதவும் முக்கியமான கருவிகள்</p>
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-6 max-w-4xl mx-auto">
            <Link href="/planning/budget" className="block border rounded-lg p-6 text-center hover:shadow-lg transition">
              <div className="text-xl font-semibold mb-2">பட்ஜெட் கணிப்பான்</div>
              <div className="text-sm text-gray-500">Budget Calculator</div>
            </Link>
            <Link href="/planning/checklist" className="block border rounded-lg p-6 text-center hover:shadow-lg transition">
              <div className="text-xl font-semibold mb-2">பணிப்பட்டியல்</div>
              <div className="text-sm text-gray-500">Checklist</div>
            </Link>
            <Link href="/planning/timeline" className="block border rounded-lg p-6 text-center hover:shadow-lg transition">
              <div className="text-xl font-semibold mb-2">கால அட்டவணை</div>
              <div className="text-sm text-gray-500">Timeline</div>
            </Link>
            <Link href="/planning/guest-list" className="block border rounded-lg p-6 text-center hover:shadow-lg transition">
              <div className="text-xl font-semibold mb-2">விருந்தினர் பட்டியல்</div>
              <div className="text-sm text-gray-500">Guest List</div>
            </Link>
            <Link href="/planning/ideas" className="block border rounded-lg p-6 text-center hover:shadow-lg transition">
              <div className="text-xl font-semibold mb-2">திருமண யோசனைகள்</div>
              <div className="text-sm text-gray-500">Wedding Ideas</div>
            </Link>
          </div>
        </div>
      </section>
      <Footer />
    </div>
  )
} 