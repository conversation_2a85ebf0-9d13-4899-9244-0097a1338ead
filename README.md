# BookmyFestive - Wedding Planning Platform

> India's Favourite Festive Planning Platform - A comprehensive full-stack wedding planning solution with mobile and web applications.

## 🏗️ Project Architecture

This is a monorepo containing both mobile (React Native) and web (Next.js) applications for the BookmyFestive wedding planning platform.

## 📁 Core Application Files Structure

```
BookmyFestive/
├── 📱 apps/                     # Applications
│   ├── mobile/                  # React Native Mobile App
│   │   ├── src/
│   │   │   ├── components/      # Mobile UI Components
│   │   │   │   ├── ui/          # Design System Components
│   │   │   │   │   ├── AnimatedButton.tsx
│   │   │   │   │   ├── AnimatedCard.tsx
│   │   │   │   │   ├── AnimatedInput.tsx
│   │   │   │   │   ├── FloatingActionButton.tsx
│   │   │   │   │   ├── EnhancedLoading.tsx
│   │   │   │   │   ├── PageTransition.tsx
│   │   │   │   │   ├── PullToRefresh.tsx
│   │   │   │   │   ├── SwipeActions.tsx
│   │   │   │   │   ├── OnboardingCarousel.tsx
│   │   │   │   │   ├── Toast.tsx
│   │   │   │   │   ├── AccessibilityWrapper.tsx
│   │   │   │   │   ├── Button.tsx
│   │   │   │   │   ├── Card.tsx
│   │   │   │   │   ├── Input.tsx
│   │   │   │   │   ├── Badge.tsx
│   │   │   │   │   └── index.ts
│   │   │   │   ├── charts/      # Analytics Charts
│   │   │   │   │   ├── BarChart.tsx
│   │   │   │   │   ├── LineChart.tsx
│   │   │   │   │   └── PieChart.tsx
│   │   │   │   ├── ErrorMessage.tsx
│   │   │   │   ├── LoadingSpinner.tsx
│   │   │   │   ├── EmptyState.tsx
│   │   │   │   ├── OfflineIndicator.tsx
│   │   │   │   ├── NotificationBell.tsx
│   │   │   │   ├── LanguageSelector.tsx
│   │   │   │   ├── RoleBasedAccess.tsx
│   │   │   │   ├── WebSafeWrapper.tsx
│   │   │   │   └── Header.tsx
│   │   │   ├── screens/         # App Screens
│   │   │   │   ├── auth/        # Authentication Screens
│   │   │   │   │   ├── LoginScreen.tsx
│   │   │   │   │   ├── SignupScreen.tsx
│   │   │   │   │   ├── OTPVerificationScreen.tsx
│   │   │   │   │   └── AccountTypeSelectionScreen.tsx
│   │   │   │   ├── details/     # Detail Screens
│   │   │   │   │   ├── VendorDetailScreen.tsx
│   │   │   │   │   ├── VenueDetailScreen.tsx
│   │   │   │   │   └── ProductDetailScreen.tsx
│   │   │   │   ├── HomeScreen.tsx
│   │   │   │   ├── VendorsScreen.tsx
│   │   │   │   ├── VenuesScreen.tsx
│   │   │   │   ├── ShopScreen.tsx
│   │   │   │   ├── CartScreen.tsx
│   │   │   │   ├── CheckoutScreen.tsx
│   │   │   │   ├── OrdersScreen.tsx
│   │   │   │   ├── OrderDetailScreen.tsx
│   │   │   │   ├── BookingScreen.tsx
│   │   │   │   ├── ReviewScreen.tsx
│   │   │   │   ├── VendorDashboardScreen.tsx
│   │   │   │   ├── VendorProductsScreen.tsx
│   │   │   │   ├── VendorOrdersScreen.tsx
│   │   │   │   ├── AdminDashboardScreen.tsx
│   │   │   │   ├── AdminAnalyticsScreen.tsx
│   │   │   │   ├── VendorAnalyticsScreen.tsx
│   │   │   │   ├── NotificationScreen.tsx
│   │   │   │   ├── WeddingPlanningScreen.tsx
│   │   │   │   ├── BudgetTrackerScreen.tsx
│   │   │   │   ├── GuestListManagerScreen.tsx
│   │   │   │   ├── SettingsScreen.tsx
│   │   │   │   ├── ProfileScreen.tsx
│   │   │   │   ├── FavoritesScreen.tsx
│   │   │   │   ├── SearchScreen.tsx
│   │   │   │   ├── SearchResultsScreen.tsx
│   │   │   │   ├── LoadingScreen.tsx
│   │   │   │   ├── ReviewFormScreen.tsx
│   │   │   │   └── UIShowcaseScreen.tsx
│   │   │   ├── navigation/      # Navigation Setup
│   │   │   │   ├── AppNavigator.tsx
│   │   │   │   ├── AuthNavigator.tsx
│   │   │   │   ├── MainNavigator.tsx
│   │   │   │   └── RootNavigator.tsx
│   │   │   ├── providers/       # Context Providers
│   │   │   │   ├── ThemeProvider.tsx
│   │   │   │   ├── AuthProvider.tsx
│   │   │   │   ├── CartProvider.tsx
│   │   │   │   ├── FavoritesProvider.tsx
│   │   │   │   ├── NotificationProvider.tsx
│   │   │   │   ├── OfflineProvider.tsx
│   │   │   │   └── SearchProvider.tsx
│   │   │   ├── services/        # API & Business Logic
│   │   │   │   ├── graphqlService.ts
│   │   │   │   ├── enhancedGraphQLService.ts
│   │   │   │   ├── authService.ts
│   │   │   │   ├── analyticsService.ts
│   │   │   │   ├── notifications.ts
│   │   │   │   ├── notificationIntegration.ts
│   │   │   │   ├── offlineService.ts
│   │   │   │   ├── cacheService.ts
│   │   │   │   ├── networkService.ts
│   │   │   │   ├── cartService.ts
│   │   │   │   ├── paymentService.ts
│   │   │   │   └── amplifyService.ts
│   │   │   ├── hooks/           # Custom Hooks
│   │   │   │   ├── useTranslation.ts
│   │   │   │   └── useOfflineQuery.ts
│   │   │   ├── i18n/            # Internationalization
│   │   │   │   ├── index.ts
│   │   │   │   └── locales/
│   │   │   │       ├── en.json
│   │   │   │       ├── hi.json
│   │   │   │       ├── ta.json
│   │   │   │       ├── te.json
│   │   │   │       ├── kn.json
│   │   │   │       ├── ml.json
│   │   │   │       ├── gu.json
│   │   │   │       ├── mr.json
│   │   │   │       └── bn.json
│   │   │   ├── graphql/         # GraphQL Operations
│   │   │   │   ├── queries.js
│   │   │   │   ├── mutations.js
│   │   │   │   └── subscriptions.js
│   │   │   ├── shared/          # Shared Types & Utils
│   │   │   │   └── types.ts
│   │   │   ├── assets/          # Mobile Assets
│   │   │   └── aws-exports.ts   # AWS Configuration
│   │   ├── App.tsx              # Root Component
│   │   ├── package.json         # Mobile Dependencies
│   │   ├── app.json             # Expo Configuration
│   │   ├── metro.config.js      # Metro Bundler Config
│   │   ├── tsconfig.json        # TypeScript Config
│   │   ├── android/             # Android Build Files
│   │   ├── ios/                 # iOS Build Files
│   │   └── assets/              # App Assets
│   └── web/                     # Web Application (if separate)
├── 🌐 app/                      # Next.js Web Application
│   ├── page.tsx                 # Homepage
│   ├── layout.tsx               # Root Layout
│   ├── globals.css              # Global Styles
│   ├── loading.tsx              # Global Loading
│   ├── error.tsx                # Global Error
│   ├── not-found.tsx            # 404 Page
│   ├── vendors/                 # Vendors Section
│   │   ├── page.tsx
│   │   └── [id]/
│   ├── venues/                  # Venues Section
│   │   ├── page.tsx
│   │   └── [id]/
│   ├── shop/                    # E-commerce Shop
│   │   ├── page.tsx
│   │   └── [id]/
│   ├── blog/                    # Blog System
│   ├── dashboard/               # Admin & User Dashboards
│   ├── login/                   # Authentication
│   ├── register/                # User Registration
│   ├── planning/                # Wedding Planning Tools
│   ├── payment/                 # Payment Processing
│   ├── contact/                 # Contact Forms
│   ├── favorites/               # User Favorites
│   ├── cart/                    # Shopping Cart
│   ├── orders/                  # Order Management
│   └── api/                     # API Routes
├── 🧩 components/               # Shared Web Components
│   ├── ui/                      # Design System Components
│   ├── admin/                   # Admin Components
│   ├── auth/                    # Authentication Components
│   ├── dashboard/               # Dashboard Components
│   ├── booking/                 # Booking Components
│   ├── analytics/               # Analytics Components
│   ├── mobile/                  # Mobile-specific Components
│   ├── lazy/                    # Lazy-loaded Components
│   ├── newsletter/              # Newsletter Components
│   ├── seo/                     # SEO Components
│   ├── performance/             # Performance Components
│   ├── header.tsx               # Main Header
│   ├── footer.tsx               # Main Footer
│   └── theme-provider.tsx       # Theme Provider
├── 📚 src/                      # Core Source Files
│   ├── amplifyconfiguration.json
│   ├── aws-exports.js
│   ├── components/
│   ├── config/
│   ├── graphql/
│   ├── hooks/
│   ├── models/
│   ├── screens/
│   ├── services/
│   └── utils/
├── ⚙️ amplify/                  # AWS Amplify Backend
│   ├── backend/
│   ├── cli.json
│   └── team-provider-info.json
├── 🎨 public/                   # Static Assets
│   ├── hero_image_1.webp
│   ├── hero_image_2.webp
│   ├── hero_image_3.webp
│   ├── BookmyFestive-logo.png
│   ├── manifest.json
│   ├── sw.js
│   └── locales/
├── 📖 docs/                     # Documentation
│   ├── APPLICATION_OVERVIEW.md
│   ├── MOBILE_APP_PHASES_DETAILED_IMPLEMENTATION.md
│   ├── BUSINESS_LOGIC_FLOWS.md
│   └── ...
├── 🔧 lib/                      # Utility Libraries
│   ├── utils.ts
│   ├── aws-config.ts
│   ├── i18n.ts
│   └── services/
├── 🪝 hooks/                    # Custom React Hooks
├── 🌍 contexts/                 # React Contexts
├── 🧭 navigation/               # Navigation Config
├── 📦 packages/                 # Shared Packages
│   └── shared/
├── 🎯 scripts/                  # Build & Utility Scripts
├── 💅 styles/                   # Global Styles
│   ├── globals.css
│   └── nprogress.css
├── 🛠️ utils/                    # Utility Functions
├── package.json                 # Root Dependencies
├── package-lock.json            # Lock File
├── tsconfig.json                # TypeScript Config
├── tailwind.config.ts           # Tailwind Config
├── next.config.js               # Next.js Config
├── postcss.config.mjs           # PostCSS Config
├── components.json              # Shadcn/ui Config
└── README.md                    # This File
```

## 🚀 Quick Start

### Prerequisites
- Node.js 18+
- npm or yarn
- Expo CLI (for mobile development)
- AWS Amplify CLI

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd BookmyFestive
   ```

2. **Install dependencies**
   ```bash
   # Root dependencies
   npm install
   
   # Mobile app dependencies
   cd apps/mobile
   npm install
   ```

3. **Configure AWS Amplify**
   ```bash
   amplify configure
   amplify init
   ```

4. **Start development servers**
   ```bash
   # Web application
   npm run dev
   
   # Mobile application
   cd apps/mobile
   npm start
   ```

## 🏗️ Architecture Overview

- **Frontend**: Next.js 13+ (Web) + React Native with Expo (Mobile)
- **Backend**: AWS Amplify with GraphQL
- **Database**: AWS DynamoDB
- **Authentication**: AWS Cognito
- **Storage**: AWS S3
- **Styling**: Tailwind CSS (Web) + Custom Theme System (Mobile)
- **State Management**: React Context + Custom Providers
- **Internationalization**: i18next with 9 languages

## 📱 Mobile App Features

- **Authentication**: OTP-based login/signup
- **E-commerce**: Shopping cart, checkout, orders
- **Vendor Management**: Listings, details, bookings
- **Wedding Planning**: Budget tracker, guest list, timeline
- **Admin Dashboard**: Analytics, user management
- **Offline Support**: Caching and sync
- **Push Notifications**: Real-time updates
- **Multi-language**: 9 Indian languages

## 🌐 Web Application Features

- **Responsive Design**: Mobile-first approach
- **SEO Optimized**: Next.js SSR/SSG
- **Admin Panel**: Comprehensive management
- **Blog System**: Content management
- **Payment Integration**: Secure checkout
- **Real-time Updates**: GraphQL subscriptions

## 🎨 Design System

Both mobile and web applications share a consistent design system with:
- **Color Palette**: Maroon primary (#610f13), Leaf green secondary, Gold accent (#F6C244)
- **Typography**: Hierarchical text styling
- **Components**: Reusable UI components
- **Spacing**: Consistent spacing system
- **Animations**: Smooth transitions and interactions

## 📄 License

This project is proprietary software for BookmyFestive.

## 🤝 Contributing

Please read the contributing guidelines before making any changes.

---

**BookmyFestive** - Making wedding planning simple and beautiful! 💍✨
