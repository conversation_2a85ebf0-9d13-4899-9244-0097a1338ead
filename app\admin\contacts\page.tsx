"use client"

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { Card, CardContent } from '@/components/ui/card'

export default function AdminContactsPage() {
  const router = useRouter()

  useEffect(() => {
    // Redirect to the new dashboard contacts pages
    router.replace('/dashboard/contacts')
  }, [router])

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center">
      <Card className="w-full max-w-md">
        <CardContent className="pt-6">
          <div className="text-center">
            <h2 className="text-2xl font-bold text-gray-900 mb-2">Redirecting...</h2>
            <p className="text-gray-600">Contact management has moved to the dashboard.</p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
