import { Page, expect } from '@playwright/test';
import { TestHelpers } from './test-helpers';

export class EcommerceHelpers extends TestHelpers {
  constructor(page: Page) {
    super(page);
  }

  /**
   * Navigate to shop/products page
   */
  async navigateToShop() {
    await this.navigateTo('/shop');
    await this.waitForElement('.product-card, [data-testid="product-card"], .shop-item');
  }

  /**
   * Search for products
   */
  async searchProducts(searchTerm: string) {
    const searchSelectors = [
      'input[placeholder*="Search"]',
      'input[name="search"]',
      '[data-testid="search-input"]'
    ];

    for (const selector of searchSelectors) {
      if (await this.elementExists(selector)) {
        await this.fillInput(selector, searchTerm);
        
        // Look for search button or press Enter
        const searchButton = this.page.locator('button:has-text("Search"), [data-testid="search-button"]').first();
        if (await searchButton.isVisible()) {
          await searchButton.click();
        } else {
          await this.page.keyboard.press('Enter');
        }
        
        await this.waitForLoadingToComplete();
        break;
      }
    }
  }

  /**
   * Add product to cart
   */
  async addProductToCart(productIndex: number = 0) {
    await this.navigateToShop();
    
    const productCards = this.page.locator('.product-card, [data-testid="product-card"], .shop-item');
    const productCard = productCards.nth(productIndex);
    
    if (await productCard.isVisible()) {
      // Look for add to cart button on the card
      const addToCartButtons = productCard.locator('button:has-text("Add to Cart"), button:has-text("Add"), [data-testid="add-to-cart"]');
      
      if (await addToCartButtons.count() > 0) {
        await addToCartButtons.first().click();
      } else {
        // Click on product to go to details page
        await productCard.click();
        await this.waitForPageLoad();
        
        // Add to cart from product details page
        await this.clickElement('button:has-text("Add to Cart"), [data-testid="add-to-cart"]');
      }
      
      await this.waitForLoadingToComplete();
    }
  }

  /**
   * View cart
   */
  async viewCart() {
    const cartSelectors = [
      'a[href*="cart"]',
      'button:has-text("Cart")',
      '[data-testid="cart-button"]',
      '.cart-icon'
    ];

    for (const selector of cartSelectors) {
      if (await this.elementExists(selector)) {
        await this.clickElement(selector);
        await this.waitForPageLoad();
        break;
      }
    }
  }

  /**
   * Get cart item count
   */
  async getCartItemCount(): Promise<number> {
    const cartCountSelectors = [
      '.cart-count',
      '[data-testid="cart-count"]',
      '.cart-badge'
    ];

    for (const selector of cartCountSelectors) {
      if (await this.elementExists(selector)) {
        const countText = await this.page.locator(selector).textContent();
        const count = parseInt(countText?.trim() || '0');
        return isNaN(count) ? 0 : count;
      }
    }

    return 0;
  }

  /**
   * Update product quantity in cart
   */
  async updateCartQuantity(productIndex: number, quantity: number) {
    await this.viewCart();
    
    const cartItems = this.page.locator('.cart-item, [data-testid="cart-item"]');
    const cartItem = cartItems.nth(productIndex);
    
    if (await cartItem.isVisible()) {
      const quantityInput = cartItem.locator('input[type="number"], input[name="quantity"]');
      
      if (await quantityInput.isVisible()) {
        await quantityInput.fill(quantity.toString());
        
        // Look for update button or trigger change
        const updateButton = cartItem.locator('button:has-text("Update")');
        if (await updateButton.isVisible()) {
          await updateButton.click();
        } else {
          await quantityInput.blur(); // Trigger change event
        }
        
        await this.waitForLoadingToComplete();
      }
    }
  }

  /**
   * Remove product from cart
   */
  async removeFromCart(productIndex: number) {
    await this.viewCart();
    
    const cartItems = this.page.locator('.cart-item, [data-testid="cart-item"]');
    const cartItem = cartItems.nth(productIndex);
    
    if (await cartItem.isVisible()) {
      const removeButtons = cartItem.locator('button:has-text("Remove"), button:has-text("Delete"), [data-testid="remove-item"]');
      
      if (await removeButtons.count() > 0) {
        await removeButtons.first().click();
        
        // Handle confirmation if present
        const confirmButtons = this.page.locator('button:has-text("Yes"), button:has-text("Confirm")');
        if (await confirmButtons.count() > 0) {
          await confirmButtons.first().click();
        }
        
        await this.waitForLoadingToComplete();
      }
    }
  }

  /**
   * Proceed to checkout
   */
  async proceedToCheckout() {
    await this.viewCart();
    
    const checkoutButtons = [
      'button:has-text("Checkout")',
      'button:has-text("Proceed to Checkout")',
      'a[href*="checkout"]',
      '[data-testid="checkout-button"]'
    ];

    for (const selector of checkoutButtons) {
      if (await this.elementExists(selector)) {
        await this.clickElement(selector);
        await this.waitForPageLoad();
        break;
      }
    }
  }

  /**
   * Fill checkout form
   */
  async fillCheckoutForm(checkoutData: {
    firstName: string;
    lastName: string;
    email: string;
    phone: string;
    address: string;
    city: string;
    state: string;
    pincode: string;
  }) {
    // Fill shipping information
    await this.fillInput('input[name="firstName"], input[placeholder*="First"]', checkoutData.firstName);
    await this.fillInput('input[name="lastName"], input[placeholder*="Last"]', checkoutData.lastName);
    await this.fillInput('input[type="email"], input[name="email"]', checkoutData.email);
    await this.fillInput('input[type="tel"], input[name="phone"]', checkoutData.phone);
    await this.fillInput('input[name="address"], textarea[name="address"]', checkoutData.address);
    await this.fillInput('input[name="city"]', checkoutData.city);
    await this.fillInput('input[name="state"], select[name="state"]', checkoutData.state);
    await this.fillInput('input[name="pincode"], input[name="zipcode"]', checkoutData.pincode);
  }

  /**
   * Select payment method
   */
  async selectPaymentMethod(method: 'razorpay' | 'cod' | 'card') {
    const paymentSelectors = {
      razorpay: 'input[value="razorpay"], label:has-text("Razorpay")',
      cod: 'input[value="cod"], label:has-text("Cash on Delivery")',
      card: 'input[value="card"], label:has-text("Credit Card")'
    };

    const selector = paymentSelectors[method];
    if (await this.elementExists(selector)) {
      await this.clickElement(selector);
    }
  }

  /**
   * Complete order
   */
  async completeOrder() {
    const orderButtons = [
      'button:has-text("Place Order")',
      'button:has-text("Complete Order")',
      'button[type="submit"]',
      '[data-testid="place-order"]'
    ];

    for (const selector of orderButtons) {
      if (await this.elementExists(selector)) {
        await this.clickElement(selector);
        await this.waitForLoadingToComplete();
        break;
      }
    }
  }

  /**
   * Verify order confirmation
   */
  async verifyOrderConfirmation(): Promise<boolean> {
    const confirmationIndicators = [
      'text=Order Confirmed',
      'text=Order Placed Successfully',
      'text=Thank you for your order',
      'text=Order ID',
      '[data-testid="order-confirmation"]'
    ];

    for (const indicator of confirmationIndicators) {
      if (await this.elementExists(indicator)) {
        return true;
      }
    }

    // Check if redirected to confirmation page
    const currentUrl = this.page.url();
    if (currentUrl.includes('/order-confirmation') || currentUrl.includes('/success')) {
      return true;
    }

    return false;
  }

  /**
   * Get order ID from confirmation
   */
  async getOrderId(): Promise<string | null> {
    const orderIdSelectors = [
      '[data-testid="order-id"]',
      'text=Order ID',
      'text=Order Number'
    ];

    for (const selector of orderIdSelectors) {
      if (await this.elementExists(selector)) {
        const element = this.page.locator(selector);
        const text = await element.textContent();
        
        // Extract ID from text like "Order ID: 12345"
        const match = text?.match(/\d+/);
        if (match) {
          return match[0];
        }
      }
    }

    return null;
  }

  /**
   * Filter products by category
   */
  async filterByCategory(category: string) {
    const categorySelectors = [
      `button:has-text("${category}")`,
      `a:has-text("${category}")`,
      `[data-category="${category}"]`,
      `input[value="${category}"]`
    ];

    for (const selector of categorySelectors) {
      if (await this.elementExists(selector)) {
        await this.clickElement(selector);
        await this.waitForLoadingToComplete();
        break;
      }
    }
  }

  /**
   * Sort products
   */
  async sortProducts(sortBy: 'price-low' | 'price-high' | 'name' | 'newest') {
    const sortSelectors = [
      'select[name="sort"]',
      '[data-testid="sort-select"]',
      '.sort-dropdown'
    ];

    for (const selector of sortSelectors) {
      if (await this.elementExists(selector)) {
        const sortValues = {
          'price-low': 'price_asc',
          'price-high': 'price_desc',
          'name': 'name_asc',
          'newest': 'created_desc'
        };

        await this.page.locator(selector).selectOption(sortValues[sortBy]);
        await this.waitForLoadingToComplete();
        break;
      }
    }
  }

  /**
   * Generate test checkout data
   */
  generateCheckoutData() {
    const testData = this.generateTestData();
    
    return {
      firstName: testData.firstName,
      lastName: testData.lastName,
      email: testData.email,
      phone: testData.phone,
      address: '123 Test Street, Test Area',
      city: testData.city,
      state: testData.state,
      pincode: '600001'
    };
  }

  /**
   * Clear cart
   */
  async clearCart() {
    await this.viewCart();
    
    // Look for clear cart button
    const clearButtons = [
      'button:has-text("Clear Cart")',
      'button:has-text("Empty Cart")',
      '[data-testid="clear-cart"]'
    ];

    for (const selector of clearButtons) {
      if (await this.elementExists(selector)) {
        await this.clickElement(selector);
        
        // Handle confirmation
        const confirmButtons = this.page.locator('button:has-text("Yes"), button:has-text("Confirm")');
        if (await confirmButtons.count() > 0) {
          await confirmButtons.first().click();
        }
        
        await this.waitForLoadingToComplete();
        break;
      }
    }
  }

  /**
   * Check if cart is empty
   */
  async isCartEmpty(): Promise<boolean> {
    await this.viewCart();
    
    const emptyCartIndicators = [
      'text=Your cart is empty',
      'text=No items in cart',
      '.empty-cart',
      '[data-testid="empty-cart"]'
    ];

    for (const indicator of emptyCartIndicators) {
      if (await this.elementExists(indicator)) {
        return true;
      }
    }

    // Check if no cart items exist
    const cartItems = this.page.locator('.cart-item, [data-testid="cart-item"]');
    return (await cartItems.count()) === 0;
  }
}
