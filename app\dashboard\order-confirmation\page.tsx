"use client"

import { useState, useEffect } from "react"
import { useRouter, useSearchParams } from "next/navigation"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { CheckCircle, Package, Truck, MapPin, Phone, Mail, Calendar, CreditCard, ArrowLeft, FileText, Download } from "lucide-react"
import Link from "next/link"
import { OrderService, OrderData } from "@/lib/services/orderService"
import { PaymentService } from "@/lib/services/paymentService"
import { invoiceService } from "@/lib/services/invoiceService"
import { useAuth } from "@/contexts/AuthContext"
import { showToast } from "@/lib/toast"


export default function OrderConfirmationPage() {
  const { isAuthenticated, user } = useAuth()
  const router = useRouter()
  const searchParams = useSearchParams()
  const [order, setOrder] = useState<OrderData | null>(null)
  const [payment, setPayment] = useState<any>(null)
  const [loading, setLoading] = useState(true)

  const orderId = searchParams.get('orderId')

  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/login')
      return
    }

    if (!orderId) {
      router.push('/shop')
      return
    }

    loadOrderDetails()
  }, [isAuthenticated, orderId])

  const loadOrderDetails = async () => {
    try {
      setLoading(true)

      // Load order details
      const orderResult = await OrderService.getOrder(orderId!)
      if (orderResult.success && orderResult.order) {
        setOrder(orderResult.order)

        // Load payment details
        try {
          const paymentResult = await PaymentService.getPaymentByOrderId(orderId!)
          if (paymentResult.success && paymentResult.payment) {
            setPayment(paymentResult.payment)
          }
        } catch (paymentError) {
          console.warn('Could not load payment details:', paymentError)
          // Continue without payment details
        }
      } else {
        showToast.error('Order not found')
        router.push('/shop')
      }
    } catch (error) {
      console.error('Error loading order details:', error)
      showToast.error('Failed to load order details')
      router.push('/shop')
    } finally {
      setLoading(false)
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'PENDING': return 'bg-yellow-100 text-yellow-800'
      case 'CONFIRMED': return 'bg-green-100 text-green-800'
      case 'PROCESSING': return 'bg-blue-100 text-blue-800'
      case 'SHIPPED': return 'bg-purple-100 text-purple-800'
      case 'DELIVERED': return 'bg-green-100 text-green-800'
      case 'CANCELLED': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const handleDownloadInvoice = async () => {
    if (!order || !user) return;

    try {
      // Find invoice for this order
      const invoices = await invoiceService.getInvoicesForUser(user.id, 'customer');
      const orderInvoice = invoices.find(invoice =>
        invoice.notes?.includes(order.id) ||
        invoice.transactionId === order.transactionId
      );

      if (orderInvoice) {
        // Download the invoice
        const downloadResult = await invoiceService.downloadInvoice(orderInvoice.id);
        if (downloadResult.success) {
          showToast.success('Invoice downloaded successfully');
        } else {
          showToast.error('Failed to download invoice');
        }
      } else {
        showToast.error('Invoice not found for this order');
      }
    } catch (error) {
      console.error('Error downloading invoice:', error);
      showToast.error('Error downloading invoice');
    }
  }

  const handleViewInvoice = async () => {
    if (!order || !user) return;

    try {
      // Find invoice for this order
      const invoices = await invoiceService.getInvoicesForUser(user.id, 'customer');
      const orderInvoice = invoices.find(invoice =>
        invoice.notes?.includes(order.id) ||
        invoice.transactionId === order.transactionId
      );

      if (orderInvoice) {
        // Navigate to invoice page
        router.push(`/dashboard/invoices?invoiceId=${orderInvoice.id}`);
      } else {
        showToast.error('Invoice not found for this order');
      }
    } catch (error) {
      console.error('Error viewing invoice:', error);
      showToast.error('Error viewing invoice');
    }
  }

  const getPaymentStatusColor = (status: string) => {
    switch (status) {
      case 'PAID': return 'bg-green-100 text-green-800'
      case 'COD_PENDING': return 'bg-yellow-100 text-yellow-800'
      case 'COD_COLLECTED': return 'bg-green-100 text-green-800'
      case 'FAILED': return 'bg-red-100 text-red-800'
      case 'PENDING': return 'bg-yellow-100 text-yellow-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen from-[#fffbe6] via-[#fff] to-[#f6c244]/20">
       
        <div className="container mx-auto px-4 py-8 sm:py-12 md:py-16 text-center">
          <div className="animate-spin rounded-full h-8 w-8 sm:h-12 sm:w-12 border-b-2 border-[#F6C244] mx-auto"></div>
          <p className="mt-3 sm:mt-4 text-gray-600 text-sm sm:text-base">Loading order details...</p>
        </div>
      
      </div>
    )
  }

  if (!order) {
    return (
      <div className="min-h-screen from-[#fffbe6] via-[#fff] to-[#f6c244]/20">
       
        <div className="container mx-auto px-4 py-8 sm:py-12 md:py-16 text-center">
          <h1 className="text-xl sm:text-2xl font-bold text-gray-900 mb-3 sm:mb-4">Order Not Found</h1>
          <p className="text-gray-600 mb-6 sm:mb-8 text-sm sm:text-base">The order you're looking for doesn't exist or you don't have permission to view it.</p>
          <Button onClick={() => router.push('/shop')} className="bg-[#F6C244] hover:bg-[#F6C244]/90 text-black">
            Continue Shopping
          </Button>
        </div>
     
      </div>
    )
  }

  return (
    <div className="min-h-screen from-[#fffbe6] via-[#fff] to-[#f6c244]/20">
    
      
      <main className="container mx-auto px-4 py-4 sm:py-6 md:py-8">
        {/* Success Header */}
        <div className="text-center mb-6 sm:mb-8">
          <div className="inline-flex items-center justify-center w-12 h-12 sm:w-16 sm:h-16 bg-green-100 rounded-full mb-3 sm:mb-4">
            <CheckCircle className="h-6 w-6 sm:h-8 sm:w-8 text-green-600" />
          </div>
          <h1 className="text-2xl sm:text-3xl font-bold text-gray-900 mb-2">Order Confirmed!</h1>
          <p className="text-base sm:text-lg text-gray-600">Thank you for your order. We'll send you updates via email.</p>
        </div>

        <div className="max-w-4xl mx-auto grid grid-cols-1 lg:grid-cols-3 gap-4 sm:gap-6 md:gap-8">
          {/* Order Details */}
          <div className="lg:col-span-2 space-y-4 sm:space-y-6">
            {/* Order Summary */}
            <Card>
              <CardHeader className="pb-3 sm:pb-6">
                <CardTitle className="flex items-center gap-2 text-base sm:text-lg">
                  <Package className="h-4 w-4 sm:h-5 sm:w-5" />
                  Order Summary
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3 sm:space-y-4">
                <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-1 sm:gap-0">
                  <span className="font-medium text-sm sm:text-base">Order Number:</span>
                  <span className="font-mono text-xs sm:text-sm">{order.orderNumber}</span>
                </div>
                <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-1 sm:gap-0">
                  <span className="font-medium text-sm sm:text-base">Order Date:</span>
                  <span className="text-sm sm:text-base">{new Date(order.orderDate).toLocaleDateString()}</span>
                </div>
                <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-1 sm:gap-0">
                  <span className="font-medium text-sm sm:text-base">Status:</span>
                  <Badge className={`${getStatusColor(order.status)} text-xs`}>{order.status}</Badge>
                </div>
                <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-1 sm:gap-0">
                  <span className="font-medium text-sm sm:text-base">Payment Status:</span>
                  <Badge className={`${getPaymentStatusColor(order.paymentStatus)} text-xs`}>{order.paymentStatus.replace('_', ' ')}</Badge>
                </div>
                {order.estimatedDeliveryDate && (
                  <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-1 sm:gap-0">
                    <span className="font-medium text-sm sm:text-base">Estimated Delivery:</span>
                    <span className="text-sm sm:text-base">{new Date(order.estimatedDeliveryDate).toLocaleDateString()}</span>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Order Items */}
            <Card>
              <CardHeader className="pb-3 sm:pb-6">
                <CardTitle className="text-base sm:text-lg">Order Items</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3 sm:space-y-4">
                  {order.items.map((item, index) => (
                    <div key={index} className="flex items-start space-x-3 sm:space-x-4 p-3 sm:p-4 border rounded-lg">
                      {item.productImage && (
                        <img 
                          src={item.productImage} 
                          alt={item.productName}
                          className="w-12 h-12 sm:w-16 sm:h-16 object-cover rounded flex-shrink-0"
                        />
                      )}
                      <div className="flex-1 min-w-0">
                        <h4 className="font-medium text-sm sm:text-base">{item.productName}</h4>
                        {item.selectedSize && <p className="text-xs sm:text-sm text-gray-600">Size: {item.selectedSize}</p>}
                        {item.selectedColor && <p className="text-xs sm:text-sm text-gray-600">Color: {item.selectedColor}</p>}
                        <p className="text-xs sm:text-sm text-gray-600">Quantity: {item.quantity}</p>
                      </div>
                      <div className="text-right flex-shrink-0">
                        <p className="font-medium text-sm sm:text-base">₹{item.subtotal.toLocaleString()}</p>
                        {item.originalPrice && item.originalPrice > item.productPrice && (
                          <p className="text-xs sm:text-sm text-gray-500 line-through">₹{(item.originalPrice * item.quantity).toLocaleString()}</p>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Shipping Address */}
            <Card>
              <CardHeader className="pb-3 sm:pb-6">
                <CardTitle className="flex items-center gap-2 text-base sm:text-lg">
                  <MapPin className="h-4 w-4 sm:h-5 sm:w-5" />
                  Shipping Address
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <p className="font-medium text-sm sm:text-base">{order.shippingAddress.fullName}</p>
                  <p className="text-sm sm:text-base">{order.shippingAddress.addressLine1}</p>
                  {order.shippingAddress.addressLine2 && <p className="text-sm sm:text-base">{order.shippingAddress.addressLine2}</p>}
                  <p className="text-sm sm:text-base">{order.shippingAddress.city}, {order.shippingAddress.state} {order.shippingAddress.pincode}</p>
                  <p className="text-sm sm:text-base">{order.shippingAddress.country}</p>
                  {order.shippingAddress.phone && (
                    <p className="flex items-center gap-2 text-xs sm:text-sm text-gray-600">
                      <Phone className="h-3 w-3 sm:h-4 sm:w-4" />
                      {order.shippingAddress.phone}
                    </p>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Order Total & Actions */}
          <div className="space-y-4 sm:space-y-6">
            {/* Order Total */}
            <Card>
              <CardHeader className="pb-3 sm:pb-6">
                <CardTitle className="text-base sm:text-lg">Order Total</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2 sm:space-y-3">
                <div className="flex justify-between text-sm sm:text-base">
                  <span>Subtotal:</span>
                  <span>₹{order.subtotal.toLocaleString()}</span>
                </div>
                <div className="flex justify-between text-sm sm:text-base">
                  <span>Shipping:</span>
                  <span>₹{order.shippingCost.toLocaleString()}</span>
                </div>
                <div className="flex justify-between text-sm sm:text-base">
                  <span>Tax:</span>
                  <span>₹{order.tax.toLocaleString()}</span>
                </div>
                {order.discount > 0 && (
                  <div className="flex justify-between text-green-600 text-sm sm:text-base">
                    <span>Discount:</span>
                    <span>-₹{order.discount.toLocaleString()}</span>
                  </div>
                )}
                <Separator />
                <div className="flex justify-between font-bold text-base sm:text-lg">
                  <span>Total:</span>
                  <span>₹{order.total.toLocaleString()}</span>
                </div>
              </CardContent>
            </Card>

            {/* Payment Information */}
            {payment && (
              <Card>
                <CardHeader className="pb-3 sm:pb-6">
                  <CardTitle className="flex items-center gap-2 text-base sm:text-lg">
                    <CreditCard className="h-4 w-4 sm:h-5 sm:w-5" />
                    Payment Information
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-2 sm:space-y-3">
                  <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-1 sm:gap-0">
                    <span className="text-sm sm:text-base">Method:</span>
                    <span className="text-sm sm:text-base">{payment.paymentMethod}</span>
                  </div>
                  <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-1 sm:gap-0">
                    <span className="text-sm sm:text-base">Status:</span>
                    <Badge className={`${getPaymentStatusColor(payment.status)} text-xs`}>{payment.status}</Badge>
                  </div>
                  {payment.transactionId && (
                    <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-1 sm:gap-0">
                      <span className="text-sm sm:text-base">Transaction ID:</span>
                      <span className="font-mono text-xs sm:text-sm">{payment.transactionId}</span>
                    </div>
                  )}
                </CardContent>
              </Card>
            )}

            {/* Actions */}
            <div className="space-y-2 sm:space-y-3">
              {/* Invoice Actions */}
              {(order.paymentStatus === 'PAID' || order.status === 'DELIVERED') && (
                <div className="grid grid-cols-2 gap-2">
                  <Button
                    onClick={handleViewInvoice}
                    variant="outline"
                    className="flex items-center gap-2 text-sm"
                  >
                    <FileText className="h-4 w-4" />
                    View Invoice
                  </Button>
                  <Button
                    onClick={handleDownloadInvoice}
                    variant="outline"
                    className="flex items-center gap-2 text-sm"
                  >
                    <Download className="h-4 w-4" />
                    Download Invoice
                  </Button>
                </div>
              )}

              <Button
                onClick={() => router.push('/dashboard/orders')}
                className="w-full bg-[#F6C244] hover:bg-[#F6C244]/90 text-black text-sm sm:text-base"
              >
                View All Orders
              </Button>
              <Button
                onClick={() => router.push('/shop')}
                variant="outline"
                className="w-full text-sm sm:text-base"
              >
                Continue Shopping
              </Button>
            </div>
          </div>
        </div>
      </main>

     
    </div>
  )
}
