"use client"

import { generateClient } from '@aws-amplify/api'
import { updateShop, getShop } from '@/src/graphql/mutations'
import { getShop as getShopQuery } from '@/src/graphql/queries'
import { getCurrentUser } from 'aws-amplify/auth'

const client = generateClient()

export interface InventoryItem {
  productId: string
  quantity: number
  selectedVariant?: string
  selectedSize?: string
  selectedColor?: string
}

export interface InventoryUpdateResult {
  success: boolean
  message: string
  updatedProducts?: any[]
  insufficientStock?: InventoryItem[]
}

export class InventoryService {
  /**
   * Check if sufficient stock is available for order items
   */
  static async checkStockAvailability(items: InventoryItem[]): Promise<{
    success: boolean
    message: string
    insufficientStock: InventoryItem[]
  }> {
    try {
      const insufficientStock: InventoryItem[] = []

      for (const item of items) {
        try {
          const result = await client.graphql({
            query: getShopQuery,
            variables: { id: item.productId },
            authMode: 'apiKey'
          })

          const product = result.data.getShop
          if (!product) {
            insufficientStock.push(item)
            continue
          }

          // Check if product is in stock and has sufficient quantity
          if (!product.inStock || product.stock < item.quantity) {
            insufficientStock.push({
              ...item,
              quantity: product.stock // Show available quantity
            })
          }
        } catch (error) {
          console.error(`Error checking stock for product ${item.productId}:`, error)
          insufficientStock.push(item)
        }
      }

      return {
        success: insufficientStock.length === 0,
        message: insufficientStock.length === 0 
          ? 'All items are in stock' 
          : `${insufficientStock.length} items have insufficient stock`,
        insufficientStock
      }
    } catch (error) {
      console.error('Error checking stock availability:', error)
      return {
        success: false,
        message: 'Failed to check stock availability',
        insufficientStock: items
      }
    }
  }

  /**
   * Reduce stock quantities when order is placed
   */
  static async reduceStock(items: InventoryItem[]): Promise<InventoryUpdateResult> {
    try {
      const user = await getCurrentUser()
      if (!user) {
        throw new Error('Authentication required')
      }

      // First check stock availability
      const stockCheck = await this.checkStockAvailability(items)
      if (!stockCheck.success) {
        return {
          success: false,
          message: 'Insufficient stock for some items',
          insufficientStock: stockCheck.insufficientStock
        }
      }

      const updatedProducts = []
      const failedUpdates = []

      for (const item of items) {
        try {
          // Get current product data
          const productResult = await client.graphql({
            query: getShopQuery,
            variables: { id: item.productId },
            authMode: 'apiKey'
          })

          const product = productResult.data.getShop
          if (!product) {
            failedUpdates.push(item)
            continue
          }

          // Calculate new stock
          const newStock = product.stock - item.quantity
          const newInStock = newStock > 0

          // Update product stock
          const updateResult = await client.graphql({
            query: updateShop,
            variables: {
              input: {
                id: item.productId,
                stock: newStock,
                inStock: newInStock
              }
            },
            authMode: 'userPool'
          })

          updatedProducts.push(updateResult.data.updateShop)

          console.log(`✅ Stock reduced for product ${item.productId}: ${product.stock} → ${newStock}`)
        } catch (error) {
          console.error(`Error reducing stock for product ${item.productId}:`, error)
          failedUpdates.push(item)
        }
      }

      return {
        success: failedUpdates.length === 0,
        message: failedUpdates.length === 0 
          ? 'Stock reduced successfully for all items'
          : `Failed to reduce stock for ${failedUpdates.length} items`,
        updatedProducts
      }
    } catch (error) {
      console.error('Error reducing stock:', error)
      return {
        success: false,
        message: 'Failed to reduce stock quantities'
      }
    }
  }

  /**
   * Restore stock quantities when order is cancelled
   */
  static async restoreStock(items: InventoryItem[]): Promise<InventoryUpdateResult> {
    try {
      const user = await getCurrentUser()
      if (!user) {
        throw new Error('Authentication required')
      }

      const updatedProducts = []
      const failedUpdates = []

      for (const item of items) {
        try {
          // Get current product data
          const productResult = await client.graphql({
            query: getShopQuery,
            variables: { id: item.productId },
            authMode: 'apiKey'
          })

          const product = productResult.data.getShop
          if (!product) {
            failedUpdates.push(item)
            continue
          }

          // Calculate restored stock
          const newStock = product.stock + item.quantity
          const newInStock = true // Product is back in stock

          // Update product stock
          const updateResult = await client.graphql({
            query: updateShop,
            variables: {
              input: {
                id: item.productId,
                stock: newStock,
                inStock: newInStock
              }
            },
            authMode: 'userPool'
          })

          updatedProducts.push(updateResult.data.updateShop)

          console.log(`✅ Stock restored for product ${item.productId}: ${product.stock} → ${newStock}`)
        } catch (error) {
          console.error(`Error restoring stock for product ${item.productId}:`, error)
          failedUpdates.push(item)
        }
      }

      return {
        success: failedUpdates.length === 0,
        message: failedUpdates.length === 0 
          ? 'Stock restored successfully for all items'
          : `Failed to restore stock for ${failedUpdates.length} items`,
        updatedProducts
      }
    } catch (error) {
      console.error('Error restoring stock:', error)
      return {
        success: false,
        message: 'Failed to restore stock quantities'
      }
    }
  }

  /**
   * Reserve stock temporarily (for cart items)
   */
  static async reserveStock(items: InventoryItem[], reservationMinutes: number = 30): Promise<InventoryUpdateResult> {
    // This could be implemented with a reservation system
    // For now, we'll just check availability
    return await this.checkStockAvailability(items)
  }

  /**
   * Get low stock products (for admin alerts)
   */
  static async getLowStockProducts(threshold: number = 5): Promise<{
    success: boolean
    products: any[]
    message: string
  }> {
    try {
      // This would require a custom query or filtering
      // For now, return a placeholder
      return {
        success: true,
        products: [],
        message: 'Low stock check completed'
      }
    } catch (error) {
      console.error('Error checking low stock:', error)
      return {
        success: false,
        products: [],
        message: 'Failed to check low stock products'
      }
    }
  }

  /**
   * Bulk update stock for multiple products
   */
  static async bulkUpdateStock(updates: { productId: string; newStock: number }[]): Promise<InventoryUpdateResult> {
    try {
      const user = await getCurrentUser()
      if (!user) {
        throw new Error('Authentication required')
      }

      const updatedProducts = []
      const failedUpdates = []

      for (const update of updates) {
        try {
          const updateResult = await client.graphql({
            query: updateShop,
            variables: {
              input: {
                id: update.productId,
                stock: update.newStock,
                inStock: update.newStock > 0
              }
            },
            authMode: 'userPool'
          })

          updatedProducts.push(updateResult.data.updateShop)
        } catch (error) {
          console.error(`Error updating stock for product ${update.productId}:`, error)
          failedUpdates.push(update)
        }
      }

      return {
        success: failedUpdates.length === 0,
        message: failedUpdates.length === 0 
          ? 'Bulk stock update completed successfully'
          : `Failed to update ${failedUpdates.length} products`,
        updatedProducts
      }
    } catch (error) {
      console.error('Error in bulk stock update:', error)
      return {
        success: false,
        message: 'Failed to perform bulk stock update'
      }
    }
  }
}
