const AWS = require('aws-sdk');
const { v4: uuidv4 } = require('uuid');

// Initialize AWS services
const dynamodb = new AWS.DynamoDB.DocumentClient();

exports.handler = async (event) => {
  console.log('Event:', JSON.stringify(event, null, 2));
  
  try {
    const input = event.arguments.input;
    
    // Check if vendor already has an active subscription
    const existingSubscription = await getVendorSubscription(input.vendorId);
    if (existingSubscription && existingSubscription.status === 'ACTIVE') {
      return {
        success: false,
        error: 'Vendor already has an active subscription'
      };
    }
    
    // Get pricing plan details
    const plan = await getPricingPlan(input.planId);
    if (!plan) {
      return {
        success: false,
        error: 'Pricing plan not found'
      };
    }
    
    // Calculate subscription dates
    const startDate = new Date();
    const endDate = calculateEndDate(startDate, plan.duration);
    
    // Process payment (integrate with your payment gateway)
    const paymentResult = await processPayment({
      amount: plan.price,
      currency: plan.currency,
      paymentMethod: input.paymentMethod,
      vendorId: input.vendorId,
      planId: input.planId
    });
    
    if (!paymentResult.success) {
      return {
        success: false,
        error: paymentResult.error || 'Payment processing failed'
      };
    }
    
    // Create subscription
    const subscriptionId = uuidv4();
    const subscription = {
      id: subscriptionId,
      vendorId: input.vendorId,
      planId: input.planId,
      status: 'ACTIVE',
      startDate: startDate.toISOString().split('T')[0],
      endDate: endDate.toISOString().split('T')[0],
      autoRenew: input.autoRenew !== false,
      paymentMethod: input.paymentMethod,
      lastPaymentDate: new Date().toISOString(),
      nextPaymentDate: endDate.toISOString(),
      amount: plan.price,
      currency: plan.currency,
      transactionId: paymentResult.transactionId,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    
    // Save subscription to database
    await dynamodb.put({
      TableName: process.env.VENDORSUBSCRIPTION_TABLE,
      Item: subscription
    }).promise();
    
    // Create payment record
    const paymentId = uuidv4();
    const payment = {
      id: paymentId,
      subscriptionId,
      vendorId: input.vendorId,
      amount: plan.price,
      currency: plan.currency,
      paymentMethod: input.paymentMethod,
      transactionId: paymentResult.transactionId,
      status: 'PAID',
      paymentDate: new Date().toISOString(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    
    await dynamodb.put({
      TableName: process.env.SUBSCRIPTIONPAYMENT_TABLE,
      Item: payment
    }).promise();
    
    return {
      success: true,
      subscriptionId,
      paymentUrl: null // For immediate payments, no URL needed
    };
    
  } catch (error) {
    console.error('Error creating subscription:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

async function getVendorSubscription(vendorId) {
  try {
    const result = await dynamodb.query({
      TableName: process.env.VENDORSUBSCRIPTION_TABLE,
      IndexName: 'byVendorId',
      KeyConditionExpression: 'vendorId = :vendorId',
      ExpressionAttributeValues: {
        ':vendorId': vendorId
      },
      ScanIndexForward: false,
      Limit: 1
    }).promise();
    
    return result.Items[0] || null;
  } catch (error) {
    console.error('Error getting vendor subscription:', error);
    return null;
  }
}

async function getPricingPlan(planId) {
  try {
    const result = await dynamodb.get({
      TableName: process.env.PRICINGPLAN_TABLE,
      Key: { id: planId }
    }).promise();
    
    return result.Item || null;
  } catch (error) {
    console.error('Error getting pricing plan:', error);
    return null;
  }
}

function calculateEndDate(startDate, duration) {
  const endDate = new Date(startDate);
  
  switch (duration) {
    case 'MONTHLY':
      endDate.setMonth(endDate.getMonth() + 1);
      break;
    case 'QUARTERLY':
      endDate.setMonth(endDate.getMonth() + 3);
      break;
    case 'YEARLY':
      endDate.setFullYear(endDate.getFullYear() + 1);
      break;
    default:
      endDate.setMonth(endDate.getMonth() + 1); // Default to monthly
  }
  
  return endDate;
}

async function processPayment(paymentData) {
  try {
    // This is where you would integrate with your payment gateway
    // For example: Razorpay, Stripe, PayU, etc.
    
    // Mock payment processing for now
    const transactionId = `txn_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    // Simulate payment processing delay
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Mock success response (90% success rate for testing)
    const isSuccess = Math.random() > 0.1;
    
    if (isSuccess) {
      return {
        success: true,
        transactionId,
        paymentMethod: paymentData.paymentMethod,
        amount: paymentData.amount,
        currency: paymentData.currency
      };
    } else {
      return {
        success: false,
        error: 'Payment declined by bank'
      };
    }
    
    // Example Razorpay integration:
    /*
    const Razorpay = require('razorpay');
    const razorpay = new Razorpay({
      key_id: process.env.RAZORPAY_KEY_ID,
      key_secret: process.env.RAZORPAY_KEY_SECRET,
    });

    const order = await razorpay.orders.create({
      amount: paymentData.amount * 100, // Amount in paise
      currency: paymentData.currency,
      receipt: `receipt_${paymentData.vendorId}_${Date.now()}`,
    });

    return {
      success: true,
      orderId: order.id,
      transactionId: order.id
    };
    */
    
  } catch (error) {
    console.error('Payment processing error:', error);
    return {
      success: false,
      error: 'Payment processing failed'
    };
  }
}
