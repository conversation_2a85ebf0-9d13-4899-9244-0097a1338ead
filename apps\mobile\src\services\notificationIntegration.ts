import { 
  notifyOrderStatusUpdate,
  notifyBookingCon<PERSON>rmation,
  notifyPaymentSuccess,
  notifyPayment<PERSON><PERSON>,
  notifyVendorNewOrder,
  notifyVendorResponse,
  notifyNewMessage,
  scheduleWeddingPlanningReminders,
  notifyPromotionalOffer,
} from './notifications';

// Integration service to connect notifications with business logic
export class NotificationIntegrationService {
  
  /**
   * Handle order creation notifications
   */
  static async handleOrderCreated(orderData: any) {
    try {
      // Notify customer about order confirmation
      await notifyOrderStatusUpdate(
        orderData.orderNumber || orderData.id,
        'confirmed',
        orderData.customerName
      );

      // If there are vendors involved, notify them about new orders
      if (orderData.items && Array.isArray(orderData.items)) {
        const vendorNotifications = new Map();
        
        // Group items by vendor
        orderData.items.forEach((item: any) => {
          if (item.vendorId) {
            if (!vendorNotifications.has(item.vendorId)) {
              vendorNotifications.set(item.vendorId, {
                vendorId: item.vendorId,
                vendorName: item.vendorName || 'Vendor',
                items: [],
                totalAmount: 0,
              });
            }
            
            const vendorData = vendorNotifications.get(item.vendorId);
            vendorData.items.push(item);
            vendorData.totalAmount += item.subtotal || (item.productPrice * item.quantity);
          }
        });

        // Send notifications to each vendor
        for (const [vendorId, vendorData] of vendorNotifications) {
          await notifyVendorNewOrder(
            vendorId,
            orderData.orderNumber || orderData.id,
            orderData.customerName,
            vendorData.totalAmount
          );
        }
      }

      console.log('Order creation notifications sent successfully');
    } catch (error) {
      console.error('Failed to send order creation notifications:', error);
    }
  }

  /**
   * Handle order status update notifications
   */
  static async handleOrderStatusUpdate(
    orderId: string,
    newStatus: 'pending' | 'confirmed' | 'processing' | 'shipped' | 'delivered' | 'cancelled',
    customerName?: string,
    additionalData?: any
  ) {
    try {
      // Map internal status to notification status
      const notificationStatus = this.mapOrderStatusForNotification(newStatus);
      
      if (notificationStatus) {
        await notifyOrderStatusUpdate(orderId, notificationStatus, customerName);
      }

      // Handle special cases
      if (newStatus === 'delivered') {
        // Schedule a follow-up notification for review request
        setTimeout(async () => {
          await notifyPromotionalOffer(
            'How was your experience? 🌟',
            'We\'d love to hear about your recent purchase. Leave a review and help other couples!',
            undefined,
            new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // 7 days from now
          );
        }, 24 * 60 * 60 * 1000); // 24 hours after delivery
      }

      console.log(`Order status update notification sent for ${orderId}: ${newStatus}`);
    } catch (error) {
      console.error('Failed to send order status update notification:', error);
    }
  }

  /**
   * Handle payment notifications
   */
  static async handlePaymentUpdate(
    orderId: string,
    paymentStatus: 'pending' | 'paid' | 'failed' | 'refunded',
    amount: number,
    paymentMethod?: string,
    failureReason?: string
  ) {
    try {
      switch (paymentStatus) {
        case 'paid':
          await notifyPaymentSuccess(orderId, amount, paymentMethod || 'Unknown');
          break;
        case 'failed':
          await notifyPaymentFailed(orderId, amount, failureReason);
          break;
        case 'refunded':
          await notifyPromotionalOffer(
            'Refund Processed 💰',
            `Your refund of ₹${amount.toLocaleString()} for order #${orderId} has been processed.`
          );
          break;
      }

      console.log(`Payment notification sent for ${orderId}: ${paymentStatus}`);
    } catch (error) {
      console.error('Failed to send payment notification:', error);
    }
  }

  /**
   * Handle booking notifications
   */
  static async handleBookingUpdate(
    bookingId: string,
    status: 'pending' | 'confirmed' | 'cancelled' | 'completed',
    vendorName: string,
    serviceName: string,
    serviceDate: Date,
    additionalData?: any
  ) {
    try {
      switch (status) {
        case 'confirmed':
          await notifyBookingConfirmation(bookingId, vendorName, serviceDate, serviceName);
          
          // Schedule reminder notifications
          const reminderDate = new Date(serviceDate.getTime() - 24 * 60 * 60 * 1000); // 1 day before
          if (reminderDate > new Date()) {
            setTimeout(async () => {
              await notifyPromotionalOffer(
                'Booking Reminder 📅',
                `Your ${serviceName} appointment with ${vendorName} is tomorrow at ${serviceDate.toLocaleTimeString()}.`
              );
            }, reminderDate.getTime() - Date.now());
          }
          break;

        case 'cancelled':
          await notifyPromotionalOffer(
            'Booking Cancelled ❌',
            `Your booking with ${vendorName} for ${serviceName} has been cancelled.`
          );
          break;

        case 'completed':
          // Request review after service completion
          setTimeout(async () => {
            await notifyPromotionalOffer(
              'How was your service? ⭐',
              `Please rate your experience with ${vendorName}. Your feedback helps other couples!`
            );
          }, 2 * 60 * 60 * 1000); // 2 hours after completion
          break;
      }

      console.log(`Booking notification sent for ${bookingId}: ${status}`);
    } catch (error) {
      console.error('Failed to send booking notification:', error);
    }
  }

  /**
   * Handle vendor response notifications
   */
  static async handleVendorResponse(
    vendorName: string,
    responseType: 'accepted' | 'declined' | 'counter_offer',
    inquiryId: string,
    message?: string
  ) {
    try {
      if (responseType === 'counter_offer') {
        await notifyPromotionalOffer(
          'New Offer Received! 💼',
          `${vendorName} has sent you a counter offer. ${message || 'Check your messages for details.'}`
        );
      } else {
        await notifyVendorResponse(vendorName, responseType === 'accepted' ? 'accepted' : 'declined');
      }

      console.log(`Vendor response notification sent: ${vendorName} - ${responseType}`);
    } catch (error) {
      console.error('Failed to send vendor response notification:', error);
    }
  }

  /**
   * Handle wedding planning notifications
   */
  static async handleWeddingPlanningSetup(
    weddingDate: Date,
    coupleName: string,
    userId: string
  ) {
    try {
      await scheduleWeddingPlanningReminders(weddingDate, coupleName);
      
      // Send immediate welcome notification
      await notifyPromotionalOffer(
        'Welcome to Your Wedding Journey! 💍',
        `Hi ${coupleName}! We're excited to help you plan your perfect wedding. Your big day is ${weddingDate.toLocaleDateString()}!`
      );

      console.log(`Wedding planning notifications scheduled for ${coupleName}`);
    } catch (error) {
      console.error('Failed to setup wedding planning notifications:', error);
    }
  }

  /**
   * Handle promotional notifications
   */
  static async handlePromotionalCampaign(
    title: string,
    message: string,
    targetAudience: 'all' | 'customers' | 'vendors',
    offerCode?: string,
    validUntil?: Date
  ) {
    try {
      // In a real implementation, this would target specific user segments
      await notifyPromotionalOffer(title, message, offerCode, validUntil);
      
      console.log(`Promotional notification sent: ${title}`);
    } catch (error) {
      console.error('Failed to send promotional notification:', error);
    }
  }

  /**
   * Handle chat/message notifications
   */
  static async handleNewMessage(
    senderName: string,
    message: string,
    chatId: string,
    recipientId: string
  ) {
    try {
      // Truncate long messages for notification
      const truncatedMessage = message.length > 100 
        ? message.substring(0, 100) + '...' 
        : message;
        
      await notifyNewMessage(senderName, truncatedMessage);
      
      console.log(`Message notification sent from ${senderName}`);
    } catch (error) {
      console.error('Failed to send message notification:', error);
    }
  }

  /**
   * Map internal order status to notification status
   */
  private static mapOrderStatusForNotification(
    status: string
  ): 'confirmed' | 'processing' | 'shipped' | 'delivered' | 'cancelled' | null {
    const statusMap: Record<string, 'confirmed' | 'processing' | 'shipped' | 'delivered' | 'cancelled' | null> = {
      'pending': null, // Don't send notification for pending
      'confirmed': 'confirmed',
      'processing': 'processing',
      'shipped': 'shipped',
      'delivered': 'delivered',
      'cancelled': 'cancelled',
    };

    return statusMap[status] || null;
  }

  /**
   * Initialize notification listeners for real-time updates
   */
  static initializeRealtimeListeners() {
    // This would set up GraphQL subscriptions for real-time notifications
    // For now, we'll just log that listeners are initialized
    console.log('Notification integration listeners initialized');
    
    // TODO: Set up GraphQL subscriptions for:
    // - Order status changes
    // - Booking updates
    // - Payment status changes
    // - New messages
    // - Vendor responses
  }
}

export default NotificationIntegrationService;
