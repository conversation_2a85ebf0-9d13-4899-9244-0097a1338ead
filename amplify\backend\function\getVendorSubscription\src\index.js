/**
 * Lambda function to get vendor subscription details
 * <PERSON>les fetching active subscription for a vendor
 */

const AWS = require('aws-sdk');
const dynamodb = new AWS.DynamoDB.DocumentClient();

const TABLE_NAME = process.env.API_THIRUMANAM360_VENDORSUBSCRIPTIONTABLE_NAME;

exports.handler = async (event) => {
  console.log('Event:', JSON.stringify(event, null, 2));
  
  try {
    const { vendorId } = event.arguments;
    
    if (!vendorId) {
      return {
        statusCode: 400,
        body: JSON.stringify({
          error: 'vendorId is required'
        })
      };
    }

    // Query for active subscription by vendorId
    const params = {
      TableName: TABLE_NAME,
      IndexName: 'byVendorId',
      KeyConditionExpression: 'vendorId = :vendorId',
      FilterExpression: '#status = :status',
      ExpressionAttributeNames: {
        '#status': 'status'
      },
      ExpressionAttributeValues: {
        ':vendorId': vendorId,
        ':status': 'ACTIVE'
      },
      ScanIndexForward: false, // Get most recent first
      Limit: 1
    };

    console.log('DynamoDB Query params:', JSON.stringify(params, null, 2));
    
    const result = await dynamodb.query(params).promise();
    
    console.log('DynamoDB result:', JSON.stringify(result, null, 2));
    
    if (result.Items && result.Items.length > 0) {
      return result.Items[0];
    } else {
      return null;
    }
    
  } catch (error) {
    console.error('Error fetching vendor subscription:', error);
    
    return {
      statusCode: 500,
      body: JSON.stringify({
        error: 'Failed to fetch vendor subscription',
        details: error.message
      })
    };
  }
};
