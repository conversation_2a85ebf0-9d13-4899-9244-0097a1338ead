"use client"

import { ChevronDown, MapPin, Globe, Edit, Download, Phone } from 'lucide-react'
import Link from 'next/link'
import { Button } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"

// Static states data for simple implementation
const STATES = [
  { name: 'Tamil Nadu', language: 'Tamil' },
  { name: 'Karnataka', language: 'Kannada' },
  { name: 'Kerala', language: 'Malayalam' },
  { name: 'Andhra Pradesh', language: 'Telugu' },
  { name: 'Maharashtra', language: 'Marathi' },
  { name: 'Gujarat', language: 'Gujarati' },
]

export function TopHeader() {
  return (
    <div className="bg-primary text-white py-2 px-4 fixed top-0 left-0 right-0 z-[60] shadow-sm">
      <div className="container mx-auto flex items-center justify-between">
        {/* Left side - Platform tagline */}
        <div className="text-sm font-medium">
          India's Favourite Festive Planning Platform
        </div>

        {/* Right side - Dropdowns */}
        <div className="flex items-center gap-4">
          {/* State Selector */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <div className="flex items-center cursor-pointer text-white hover:text-accent transition-colors duration-200 px-3 py-2 text-sm">
                <MapPin className="w-4 h-4 mr-2" />
                Tamil Nadu
                <ChevronDown className="w-4 h-4 ml-2" />
              </div>
            </DropdownMenuTrigger>
            <DropdownMenuContent 
              align="end" 
              className="w-64 max-h-80 overflow-y-auto"
            >
              {STATES.map((state, index) => (
                <DropdownMenuItem
                  key={index}
                  className="cursor-pointer"
                >
                  <div className="flex flex-col">
                    <span className="font-medium">{state.name}</span>
                    <span className="text-xs text-gray-500">
                      {state.language}
                    </span>
                  </div>
                </DropdownMenuItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>

          {/* Language Selector */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <div className="flex items-center cursor-pointer text-white hover:text-accent transition-colors duration-200 px-3 py-2 text-sm">
                <Globe className="w-4 h-4 mr-2" />
                English
                <ChevronDown className="w-4 h-4 ml-2" />
              </div>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-48">
              <DropdownMenuItem className="cursor-pointer">
                <div className="flex items-center justify-between w-full">
                  <span>English</span>
                  <span className="text-xs text-gray-500">EN</span>
                </div>
              </DropdownMenuItem>
              <DropdownMenuItem className="cursor-pointer">
                <div className="flex items-center justify-between w-full">
                  <span>Tamil</span>
                  <span className="text-xs text-gray-500">TA</span>
                </div>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>

          {/* Contact, Write Review & Download App buttons */}
          <div className="hidden md:flex items-center gap-2">
            <Link href="/contact" className="flex items-center text-white hover:text-accent transition-colors duration-200 px-3 py-2 text-sm">
              <Phone className="w-4 h-4 mr-2" />
              Contact Us
            </Link>
            <Link href="/reviews" className="flex items-center text-white hover:text-accent transition-colors duration-200 px-3 py-2 text-sm">
              <Edit className="w-4 h-4 mr-2" />
              Write A Review
            </Link>
            <Link href="/download-app" className="flex items-center text-white hover:text-accent transition-colors duration-200 px-3 py-2 text-sm">
              <Download className="w-4 h-4 mr-2" />
              Download App
            </Link>
          </div>
        </div>
      </div>
    </div>
  )
}
