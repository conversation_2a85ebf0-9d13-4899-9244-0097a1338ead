"use client"

import React, { useState } from 'react'
import { ImageUpload } from './ui/image-upload'
import { Button } from './ui/button'
import { Card, CardContent, CardHeader, CardTitle } from './ui/card'

export function ImageUploadDemo() {
  const [productImages, setProductImages] = useState<string[]>([])
  const [venueImages, setVenueImages] = useState<string[]>([])

  const handleReset = () => {
    setProductImages([])
    setVenueImages([])
  }

  const addSampleImages = () => {
    const sampleProductImages = [
      "/api/placeholder/400/400",
      "/api/placeholder/400/400",
      "/api/placeholder/400/400"
    ]
    const sampleVenueImages = [
      "/api/placeholder/600/400",
      "/api/placeholder/600/400",
      "/api/placeholder/600/400",
      "/api/placeholder/600/400"
    ]
    setProductImages(sampleProductImages)
    setVenueImages(sampleVenueImages)
  }

  return (
    <div className="max-w-6xl mx-auto p-6 space-y-8">
      <div className="text-center">
        <h1 className="text-3xl font-bold mb-4">Image Upload Component Demo</h1>
        <p className="text-gray-600 mb-6">
          Demonstration of the image upload functionality for shop products and venue galleries
        </p>
        <div className="flex gap-4 justify-center">
          <Button onClick={addSampleImages} variant="outline">
            Add Sample Images
          </Button>
          <Button onClick={handleReset} variant="outline">
            Reset All
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Shop Product Images */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              🛍️ Shop Product Images
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ImageUpload
              images={productImages}
              onImagesChange={setProductImages}
              maxImages={8}
              label="Product Images"
              description="Upload product images (JPG, PNG, WebP) - Max 5MB each"
            />
            
            {productImages.length > 0 && (
              <div className="mt-4 p-4 bg-gray-50 rounded-lg">
                <h4 className="font-medium mb-2">Image Data:</h4>
                <div className="text-sm text-gray-600">
                  <p>Total Images: {productImages.length}/8</p>
                  <p>Main Image: {productImages[0] ? "Set" : "None"}</p>
                  <p>Status: {productImages.length > 0 ? "Ready for product listing" : "No images"}</p>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Venue Gallery Images */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              🏛️ Venue Gallery Images
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ImageUpload
              images={venueImages}
              onImagesChange={setVenueImages}
              maxImages={15}
              label="Venue Gallery"
              description="Upload venue images (JPG, PNG, WebP) - Max 5MB each"
            />
            
            {venueImages.length > 0 && (
              <div className="mt-4 p-4 bg-gray-50 rounded-lg">
                <h4 className="font-medium mb-2">Gallery Data:</h4>
                <div className="text-sm text-gray-600">
                  <p>Total Images: {venueImages.length}/15</p>
                  <p>Main Image: {venueImages[0] ? "Set" : "None"}</p>
                  <p>Status: {venueImages.length > 0 ? "Ready for venue listing" : "No images"}</p>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Features Overview */}
      <Card>
        <CardHeader>
          <CardTitle>✨ Features Demonstrated</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div className="p-4 border rounded-lg">
              <h4 className="font-medium mb-2">📤 Drag & Drop Upload</h4>
              <p className="text-sm text-gray-600">
                Drag images directly into the upload area for quick uploading
              </p>
            </div>
            <div className="p-4 border rounded-lg">
              <h4 className="font-medium mb-2">🖱️ Click to Browse</h4>
              <p className="text-sm text-gray-600">
                Click the upload area to open file browser and select images
              </p>
            </div>
            <div className="p-4 border rounded-lg">
              <h4 className="font-medium mb-2">🔗 URL Input</h4>
              <p className="text-sm text-gray-600">
                Add images by entering URLs in the alternative input field
              </p>
            </div>
            <div className="p-4 border rounded-lg">
              <h4 className="font-medium mb-2">👁️ Image Preview</h4>
              <p className="text-sm text-gray-600">
                Click the eye icon to view images in full-screen preview
              </p>
            </div>
            <div className="p-4 border rounded-lg">
              <h4 className="font-medium mb-2">🔄 Reorder Images</h4>
              <p className="text-sm text-gray-600">
                Use arrow buttons to reorder images, first image becomes main
              </p>
            </div>
            <div className="p-4 border rounded-lg">
              <h4 className="font-medium mb-2">🗑️ Remove Images</h4>
              <p className="text-sm text-gray-600">
                Click the X button on hover to remove unwanted images
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Technical Details */}
      <Card>
        <CardHeader>
          <CardTitle>⚙️ Technical Specifications</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-medium mb-3">Shop Products</h4>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• Maximum 8 images per product</li>
                <li>• First image becomes main product image</li>
                <li>• Optimized for product showcase</li>
                <li>• Multiple angle support</li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium mb-3">Venue Galleries</h4>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• Maximum 15 images per venue</li>
                <li>• Comprehensive venue showcase</li>
                <li>• Different space documentation</li>
                <li>• Professional gallery presentation</li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium mb-3">File Validation</h4>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• Supported: JPG, PNG, WebP</li>
                <li>• Maximum 5MB per image</li>
                <li>• Real-time validation</li>
                <li>• Error handling & feedback</li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium mb-3">User Experience</h4>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• Drag & drop interface</li>
                <li>• Visual upload progress</li>
                <li>• Image management tools</li>
                <li>• Responsive design</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
