"use client"

import { useEffect, useState } from 'react';
import { I18nextProvider } from 'react-i18next';
import i18nEnhanced from '@/lib/i18n-enhanced';

interface I18nProviderProps {
  children: React.ReactNode;
}

export default function I18nProvider({ children }: I18nProviderProps) {
  const [isReady, setIsReady] = useState(false);

  useEffect(() => {
    const initI18n = async () => {
      try {
        if (i18nEnhanced.isInitialized) {
          setIsReady(true);
          return;
        }
        await i18nEnhanced.init();
        setIsReady(true);
      } catch (error) {
        console.error('Error initializing i18n:', error);
        setIsReady(true);
      }
    };

    initI18n();
  }, []);

  if (!isReady) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <I18nextProvider i18n={i18nEnhanced}>
      {children}
    </I18nextProvider>
  );
}