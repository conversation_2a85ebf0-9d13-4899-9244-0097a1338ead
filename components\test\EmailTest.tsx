"use client"

import React, { useState } from 'react'
import { EmailService } from '@/lib/services/emailService'

export default function EmailTest() {
  const [result, setResult] = useState<string>('')
  const [loading, setLoading] = useState(false)

  const testNewsletterEmail = async () => {
    setLoading(true)
    setResult('')
    
    try {
      const emailService = new EmailService()
      const success = await emailService.sendNewsletterWelcomeEmail({
        email: '<EMAIL>',
        firstName: 'Test',
        lastName: 'User',
        type: 'newsletter',
        interests: ['photography', 'venues'],
        preferences: {
          weddingTips: true,
          vendorRecommendations: true,
          specialOffers: false
        }
      })
      
      setResult(success ? 'Email sent successfully!' : 'Email sending failed')
    } catch (error) {
      setResult(`Error: ${error.message}`)
    } finally {
      setLoading(false)
    }
  }

  const testBookingEmail = async () => {
    setLoading(true)
    setResult('')
    
    try {
      const emailService = new EmailService()
      const success = await emailService.sendBookingConfirmationEmail({
        email: '<EMAIL>',
        userName: 'Test User',
        bookingId: 'booking_123',
        entityName: 'Dream Wedding Venue',
        entityType: 'venue',
        eventDate: '2025-12-25',
        eventTime: '6:00 PM',
        amount: '₹50,000',
        status: 'confirmed'
      })
      
      setResult(success ? 'Booking email sent successfully!' : 'Booking email sending failed')
    } catch (error) {
      setResult(`Error: ${error.message}`)
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="p-6 max-w-md mx-auto bg-white rounded-lg shadow-md">
      <h2 className="text-xl font-bold mb-4">Email Service Test</h2>
      
      <div className="space-y-4">
        <button
          onClick={testNewsletterEmail}
          disabled={loading}
          className="w-full px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50"
        >
          {loading ? 'Sending...' : 'Test Newsletter Email'}
        </button>
        
        <button
          onClick={testBookingEmail}
          disabled={loading}
          className="w-full px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 disabled:opacity-50"
        >
          {loading ? 'Sending...' : 'Test Booking Email'}
        </button>
        
        {result && (
          <div className={`p-3 rounded ${
            result.includes('Error') 
              ? 'bg-red-100 text-red-700' 
              : result.includes('successfully')
              ? 'bg-green-100 text-green-700'
              : 'bg-yellow-100 text-yellow-700'
          }`}>
            {result}
          </div>
        )}
      </div>
      
      <div className="mt-6 text-sm text-gray-600">
        <p><strong>Note:</strong> This test uses the new direct GraphQL email service that creates EmailLog entries instead of using Lambda functions.</p>
      </div>
    </div>
  )
}
