import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Alert,
  Image,
} from 'react-native';
import { RouteProp, useRoute, useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../providers/ThemeProvider';
import { useAuth } from '../providers/AuthProvider';
import { AppStackParamList } from '../navigation/AppNavigator';

type ReviewFormRouteProp = RouteProp<AppStackParamList, 'ReviewForm'>;

interface ReviewForm {
  rating: number;
  title: string;
  comment: string;
  recommend: boolean;
}

export default function ReviewFormScreen() {
  const route = useRoute<ReviewFormRouteProp>();
  const navigation = useNavigation();
  const { theme } = useTheme();
  const { user } = useAuth();
  
  const { type, id, name } = route.params;
  
  const [reviewForm, setReviewForm] = useState<ReviewForm>({
    rating: 0,
    title: '',
    comment: '',
    recommend: true,
  });
  
  const [loading, setLoading] = useState(false);

  const ratingLabels = [
    'Poor',
    'Fair', 
    'Good',
    'Very Good',
    'Excellent'
  ];

  const reviewPrompts = {
    vendor: [
      'How was the quality of service?',
      'Was the vendor professional and punctual?',
      'How was the communication throughout?',
      'Would you recommend this vendor to others?',
    ],
    venue: [
      'How was the venue ambiance and facilities?',
      'Was the staff helpful and accommodating?',
      'How was the food and catering quality?',
      'Would you book this venue again?',
    ],
    product: [
      'How was the product quality?',
      'Was the product as described?',
      'How was the packaging and delivery?',
      'Would you buy this product again?',
    ],
  };

  const handleRatingPress = (rating: number) => {
    setReviewForm(prev => ({ ...prev, rating }));
  };

  const handleSubmitReview = async () => {
    if (reviewForm.rating === 0) {
      Alert.alert('Error', 'Please select a rating');
      return;
    }

    if (!reviewForm.title.trim()) {
      Alert.alert('Error', 'Please enter a review title');
      return;
    }

    if (!reviewForm.comment.trim()) {
      Alert.alert('Error', 'Please write your review');
      return;
    }

    try {
      setLoading(true);

      // Simulate review submission
      await new Promise(resolve => setTimeout(resolve, 2000));

      const review = {
        id: `review_${Date.now()}`,
        userId: user?.id,
        userName: `${user?.firstName} ${user?.lastName}`,
        targetId: id,
        targetType: type,
        rating: reviewForm.rating,
        title: reviewForm.title,
        comment: reviewForm.comment,
        recommend: reviewForm.recommend,
        createdAt: new Date().toISOString(),
      };

      Alert.alert(
        'Review Submitted!',
        'Thank you for your feedback. Your review will be published after moderation.',
        [
          {
            text: 'OK',
            onPress: () => navigation.goBack(),
          },
        ]
      );

    } catch (error) {
      console.error('Error submitting review:', error);
      Alert.alert('Error', 'Failed to submit review. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <ScrollView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <View style={styles.content}>
        {/* Header */}
        <View style={styles.header}>
          <Text style={[styles.title, { color: theme.colors.text }]}>
            Write a Review
          </Text>
          <Text style={[styles.subtitle, { color: theme.colors.textSecondary }]}>
            Share your experience with {name}
          </Text>
        </View>

        {/* Rating Section */}
        <View style={[styles.section, { backgroundColor: theme.colors.surface }]}>
          <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
            Overall Rating
          </Text>
          <View style={styles.ratingContainer}>
            {[1, 2, 3, 4, 5].map((star) => (
              <TouchableOpacity
                key={star}
                style={styles.starButton}
                onPress={() => handleRatingPress(star)}
              >
                <Ionicons
                  name={star <= reviewForm.rating ? 'star' : 'star-outline'}
                  size={40}
                  color={star <= reviewForm.rating ? '#F59E0B' : theme.colors.textSecondary}
                />
              </TouchableOpacity>
            ))}
          </View>
          {reviewForm.rating > 0 && (
            <Text style={[styles.ratingLabel, { color: theme.colors.primary }]}>
              {ratingLabels[reviewForm.rating - 1]}
            </Text>
          )}
        </View>

        {/* Review Title */}
        <View style={[styles.section, { backgroundColor: theme.colors.surface }]}>
          <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
            Review Title
          </Text>
          <TextInput
            style={[styles.titleInput, { backgroundColor: theme.colors.background, color: theme.colors.text }]}
            placeholder="Summarize your experience in a few words"
            placeholderTextColor={theme.colors.textSecondary}
            value={reviewForm.title}
            onChangeText={(text) => setReviewForm(prev => ({ ...prev, title: text }))}
            maxLength={100}
          />
          <Text style={[styles.characterCount, { color: theme.colors.textSecondary }]}>
            {reviewForm.title.length}/100
          </Text>
        </View>

        {/* Review Comment */}
        <View style={[styles.section, { backgroundColor: theme.colors.surface }]}>
          <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
            Your Review
          </Text>
          <Text style={[styles.sectionSubtitle, { color: theme.colors.textSecondary }]}>
            Tell others about your experience
          </Text>
          
          {/* Review Prompts */}
          <View style={styles.promptsContainer}>
            <Text style={[styles.promptsTitle, { color: theme.colors.text }]}>
              Consider mentioning:
            </Text>
            {reviewPrompts[type].map((prompt, index) => (
              <View key={index} style={styles.promptItem}>
                <Ionicons name="checkmark-circle" size={16} color={theme.colors.success} />
                <Text style={[styles.promptText, { color: theme.colors.textSecondary }]}>
                  {prompt}
                </Text>
              </View>
            ))}
          </View>

          <TextInput
            style={[styles.commentInput, { backgroundColor: theme.colors.background, color: theme.colors.text }]}
            placeholder="Write your detailed review here..."
            placeholderTextColor={theme.colors.textSecondary}
            value={reviewForm.comment}
            onChangeText={(text) => setReviewForm(prev => ({ ...prev, comment: text }))}
            multiline
            numberOfLines={6}
            textAlignVertical="top"
            maxLength={1000}
          />
          <Text style={[styles.characterCount, { color: theme.colors.textSecondary }]}>
            {reviewForm.comment.length}/1000
          </Text>
        </View>

        {/* Recommendation */}
        <View style={[styles.section, { backgroundColor: theme.colors.surface }]}>
          <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
            Would you recommend this {type}?
          </Text>
          <View style={styles.recommendContainer}>
            <TouchableOpacity
              style={[
                styles.recommendButton,
                {
                  backgroundColor: reviewForm.recommend ? theme.colors.success : theme.colors.background,
                  borderColor: theme.colors.success,
                }
              ]}
              onPress={() => setReviewForm(prev => ({ ...prev, recommend: true }))}
            >
              <Ionicons
                name="thumbs-up"
                size={20}
                color={reviewForm.recommend ? '#fff' : theme.colors.success}
              />
              <Text style={[
                styles.recommendText,
                { color: reviewForm.recommend ? '#fff' : theme.colors.success }
              ]}>
                Yes, I recommend
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.recommendButton,
                {
                  backgroundColor: !reviewForm.recommend ? theme.colors.error : theme.colors.background,
                  borderColor: theme.colors.error,
                }
              ]}
              onPress={() => setReviewForm(prev => ({ ...prev, recommend: false }))}
            >
              <Ionicons
                name="thumbs-down"
                size={20}
                color={!reviewForm.recommend ? '#fff' : theme.colors.error}
              />
              <Text style={[
                styles.recommendText,
                { color: !reviewForm.recommend ? '#fff' : theme.colors.error }
              ]}>
                No, I don't recommend
              </Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Guidelines */}
        <View style={[styles.section, { backgroundColor: theme.colors.surface }]}>
          <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
            Review Guidelines
          </Text>
          <View style={styles.guidelinesContainer}>
            <View style={styles.guidelineItem}>
              <Ionicons name="checkmark" size={16} color={theme.colors.success} />
              <Text style={[styles.guidelineText, { color: theme.colors.textSecondary }]}>
                Be honest and helpful
              </Text>
            </View>
            <View style={styles.guidelineItem}>
              <Ionicons name="checkmark" size={16} color={theme.colors.success} />
              <Text style={[styles.guidelineText, { color: theme.colors.textSecondary }]}>
                Focus on your experience
              </Text>
            </View>
            <View style={styles.guidelineItem}>
              <Ionicons name="checkmark" size={16} color={theme.colors.success} />
              <Text style={[styles.guidelineText, { color: theme.colors.textSecondary }]}>
                Be respectful and constructive
              </Text>
            </View>
            <View style={styles.guidelineItem}>
              <Ionicons name="close" size={16} color={theme.colors.error} />
              <Text style={[styles.guidelineText, { color: theme.colors.textSecondary }]}>
                No personal information or spam
              </Text>
            </View>
          </View>
        </View>

        {/* Submit Button */}
        <TouchableOpacity
          style={[
            styles.submitButton,
            {
              backgroundColor: loading ? theme.colors.textSecondary : theme.colors.primary
            }
          ]}
          onPress={handleSubmitReview}
          disabled={loading}
        >
          <Text style={styles.submitButtonText}>
            {loading ? 'Submitting Review...' : 'Submit Review'}
          </Text>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    padding: 16,
  },
  header: {
    marginBottom: 24,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 16,
  },
  section: {
    padding: 16,
    borderRadius: 12,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 8,
  },
  sectionSubtitle: {
    fontSize: 14,
    marginBottom: 12,
  },
  ratingContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    gap: 8,
    marginVertical: 16,
  },
  starButton: {
    padding: 4,
  },
  ratingLabel: {
    textAlign: 'center',
    fontSize: 16,
    fontWeight: '600',
  },
  titleInput: {
    paddingHorizontal: 12,
    paddingVertical: 10,
    borderRadius: 8,
    fontSize: 16,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  characterCount: {
    textAlign: 'right',
    fontSize: 12,
    marginTop: 4,
  },
  promptsContainer: {
    marginBottom: 16,
  },
  promptsTitle: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 8,
  },
  promptItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginBottom: 4,
  },
  promptText: {
    fontSize: 14,
    flex: 1,
  },
  commentInput: {
    paddingHorizontal: 12,
    paddingVertical: 10,
    borderRadius: 8,
    fontSize: 16,
    borderWidth: 1,
    borderColor: '#E5E7EB',
    height: 120,
  },
  recommendContainer: {
    gap: 12,
  },
  recommendButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    borderWidth: 2,
    gap: 8,
  },
  recommendText: {
    fontSize: 16,
    fontWeight: '500',
  },
  guidelinesContainer: {
    gap: 8,
  },
  guidelineItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  guidelineText: {
    fontSize: 14,
    flex: 1,
  },
  submitButton: {
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: 'center',
    marginTop: 24,
  },
  submitButtonText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
  },
});
