import React from 'react';
import { <PERSON>ada<PERSON> } from 'next';
import Link from 'next/link';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Calendar, Clock, User, Eye } from 'lucide-react';

export const metadata: Metadata = {
  title: 'Wedding Planning Blog - Expert Tips, Guides & Inspiration | BookmyFestive',
  description: 'Discover expert wedding planning tips, vendor selection guides, budget management advice, and real wedding inspiration. Your complete guide to planning the perfect Indian wedding.',
  keywords: 'wedding planning blog, wedding tips, vendor selection, wedding budget, Indian wedding guide, wedding inspiration',
  openGraph: {
    title: 'Wedding Planning Blog - Expert Tips & Guides | BookmyFestive',
    description: 'Expert wedding planning advice, vendor selection guides, and real wedding inspiration for your perfect Indian wedding.',
    type: 'website',
    url: 'https://bookmyfestive.com/blog',
  },
};

const blogPosts = [
  {
    id: 1,
    title: 'Complete Wedding Planning Checklist: 12 Months to Your Big Day',
    excerpt: 'Master the art of wedding planning with our comprehensive 12-month checklist. From engagement to honeymoon, we\'ve got you covered.',
    slug: 'complete-wedding-planning-checklist',
    category: 'Planning Guide',
    readTime: '8 min read',
    author: 'BookmyFestive Team',
    publishDate: '2024-08-15',
    views: '2.5k',
    featured: true,
    tags: ['wedding planning', 'checklist', 'timeline', 'organization']
  },
  {
    id: 2,
    title: 'How to Choose the Perfect Wedding Photographer: 10 Essential Tips',
    excerpt: 'Your wedding photos are forever. Learn how to select the right photographer who will capture your special moments perfectly.',
    slug: 'choose-perfect-wedding-photographer',
    category: 'Vendor Selection',
    readTime: '6 min read',
    author: 'Wedding Expert',
    publishDate: '2024-08-14',
    views: '1.8k',
    featured: false,
    tags: ['photography', 'vendor selection', 'wedding photos', 'tips']
  },
  {
    id: 3,
    title: 'Wedding Budget Planning: Complete Guide to Managing Your Expenses',
    excerpt: 'Plan your dream wedding without breaking the bank. Our comprehensive budget guide helps you allocate funds wisely.',
    slug: 'wedding-budget-planning-guide',
    category: 'Budget Management',
    readTime: '10 min read',
    author: 'Financial Advisor',
    publishDate: '2024-08-13',
    views: '3.2k',
    featured: true,
    tags: ['budget', 'financial planning', 'cost management', 'wedding expenses']
  },
  {
    id: 4,
    title: 'Monsoon Wedding Ideas: Making Rain Your Wedding\'s Best Friend',
    excerpt: 'Don\'t let the monsoon dampen your spirits! Discover creative ideas for a beautiful rainy season wedding celebration.',
    slug: 'monsoon-wedding-ideas',
    category: 'Seasonal Tips',
    readTime: '5 min read',
    author: 'Wedding Stylist',
    publishDate: '2024-08-12',
    views: '1.5k',
    featured: false,
    tags: ['monsoon wedding', 'seasonal tips', 'rainy day wedding', 'weather']
  },
  {
    id: 5,
    title: 'South Indian Wedding Traditions: A Complete Cultural Guide',
    excerpt: 'Explore the rich traditions and customs of South Indian weddings. From rituals to attire, discover the beauty of cultural celebrations.',
    slug: 'south-indian-wedding-traditions',
    category: 'Cultural Guide',
    readTime: '12 min read',
    author: 'Cultural Expert',
    publishDate: '2024-08-11',
    views: '2.1k',
    featured: false,
    tags: ['South Indian', 'traditions', 'culture', 'wedding customs']
  },
  {
    id: 6,
    title: 'Sustainable Weddings: Eco-Friendly Ideas for Conscious Couples',
    excerpt: 'Plan an environmentally conscious wedding that\'s beautiful and sustainable. Green wedding ideas that make a difference.',
    slug: 'sustainable-eco-friendly-wedding-ideas',
    category: 'Trending Topics',
    readTime: '7 min read',
    author: 'Sustainability Expert',
    publishDate: '2024-08-10',
    views: '1.9k',
    featured: false,
    tags: ['sustainable', 'eco-friendly', 'green wedding', 'environment']
  }
];

const categories = [
  'All',
  'Planning Guide',
  'Vendor Selection',
  'Budget Management',
  'Seasonal Tips',
  'Cultural Guide',
  'Trending Topics'
];

export default function BlogPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <div className="bg-gradient-to-r from-red-800 to-red-600 text-white py-16">
        <div className="container mx-auto px-4 text-center">
          <h1 className="text-4xl md:text-5xl font-bold mb-4">
            Wedding Planning Blog
          </h1>
          <p className="text-xl md:text-2xl mb-8 max-w-3xl mx-auto">
            Expert tips, comprehensive guides, and real wedding inspiration to help you plan your perfect celebration
          </p>
          <div className="flex flex-wrap justify-center gap-4">
            {categories.map((category) => (
              <Badge 
                key={category} 
                variant="secondary" 
                className="text-sm px-4 py-2 cursor-pointer hover:bg-white hover:text-red-800 transition-colors"
              >
                {category}
              </Badge>
            ))}
          </div>
        </div>
      </div>

      {/* Featured Posts */}
      <div className="container mx-auto px-4 py-12">
        <h2 className="text-3xl font-bold text-gray-900 mb-8 text-center">
          Featured Articles
        </h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
          {blogPosts.filter(post => post.featured).map((post) => (
            <Card key={post.id} className="h-full hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="flex items-center gap-2 mb-2">
                  <Badge variant="outline" className="text-xs">
                    {post.category}
                  </Badge>
                  {post.featured && (
                    <Badge variant="default" className="text-xs bg-red-600">
                      Featured
                    </Badge>
                  )}
                </div>
                <CardTitle className="text-lg leading-tight">
                  <Link href={`/blog/${post.slug}`} className="hover:text-red-600 transition-colors">
                    {post.title}
                  </Link>
                </CardTitle>
                <CardDescription className="text-sm leading-relaxed">
                  {post.excerpt}
                </CardDescription>
              </CardHeader>
              <CardContent className="pt-0">
                <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
                  <div className="flex items-center gap-1">
                    <User className="h-4 w-4" />
                    {post.author}
                  </div>
                  <div className="flex items-center gap-1">
                    <Eye className="h-4 w-4" />
                    {post.views}
                  </div>
                </div>
                <div className="flex items-center justify-between text-sm text-gray-500">
                  <div className="flex items-center gap-1">
                    <Calendar className="h-4 w-4" />
                    {new Date(post.publishDate).toLocaleDateString('en-IN', {
                      year: 'numeric',
                      month: 'short',
                      day: 'numeric'
                    })}
                  </div>
                  <div className="flex items-center gap-1">
                    <Clock className="h-4 w-4" />
                    {post.readTime}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* All Posts */}
        <h2 className="text-3xl font-bold text-gray-900 mb-8 text-center">
          Latest Articles
        </h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {blogPosts.map((post) => (
            <Card key={post.id} className="h-full hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="flex items-center gap-2 mb-2">
                  <Badge variant="outline" className="text-xs">
                    {post.category}
                  </Badge>
                  {post.featured && (
                    <Badge variant="default" className="text-xs bg-red-600">
                      Featured
                    </Badge>
                  )}
                </div>
                <CardTitle className="text-lg leading-tight">
                  <Link href={`/blog/${post.slug}`} className="hover:text-red-600 transition-colors">
                    {post.title}
                  </Link>
                </CardTitle>
                <CardDescription className="text-sm leading-relaxed">
                  {post.excerpt}
                </CardDescription>
              </CardHeader>
              <CardContent className="pt-0">
                <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
                  <div className="flex items-center gap-1">
                    <User className="h-4 w-4" />
                    {post.author}
                  </div>
                  <div className="flex items-center gap-1">
                    <Eye className="h-4 w-4" />
                    {post.views}
                  </div>
                </div>
                <div className="flex items-center justify-between text-sm text-gray-500">
                  <div className="flex items-center gap-1">
                    <Calendar className="h-4 w-4" />
                    {new Date(post.publishDate).toLocaleDateString('en-IN', {
                      year: 'numeric',
                      month: 'short',
                      day: 'numeric'
                    })}
                  </div>
                  <div className="flex items-center gap-1">
                    <Clock className="h-4 w-4" />
                    {post.readTime}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Newsletter Signup */}
        <div className="mt-16 bg-white rounded-lg shadow-lg p-8 text-center">
          <h3 className="text-2xl font-bold text-gray-900 mb-4">
            Stay Updated with Wedding Trends
          </h3>
          <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
            Get the latest wedding planning tips, vendor recommendations, and inspiration delivered to your inbox every week.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
            <input
              type="email"
              placeholder="Enter your email address"
              className="flex-1 px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-red-600 focus:border-transparent"
            />
            <button className="px-6 py-3 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors font-medium">
              Subscribe
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
