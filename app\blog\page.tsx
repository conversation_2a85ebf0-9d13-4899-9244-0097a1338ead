import { TopHeader } from "@/components/top-header"
import { <PERSON><PERSON> } from "@/components/header"
import { Footer } from "@/components/footer"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Search, Calendar, User, Clock, ArrowRight } from "lucide-react"
import Image from "next/image"
import Link from "next/link"
import { SimpleSEO } from '@/components/seo/SimpleSEO'

export default function BlogPage() {
  const blogPosts = [
    {
      id: 1,
      title: "10 Essential Wedding Planning Tips for Indian Weddings",
      excerpt:
        "Planning an Indian wedding can be overwhelming. Here are the top 10 tips to make your wedding planning journey smooth and memorable.",
      author: "<PERSON><PERSON> <PERSON>",
      date: "December 15, 2024",
      readTime: "5 min read",
      category: "Planning",
      image: "/placeholder.svg?height=300&width=400",
      featured: true,
    },
    {
      id: 2,
      title: "Latest Bridal Lehenga Trends for 2024",
      excerpt:
        "Discover the hottest bridal lehenga trends that are taking the wedding fashion world by storm this year.",
      author: "Kavya Patel",
      date: "December 12, 2024",
      readTime: "4 min read",
      category: "Fashion",
      image: "/placeholder.svg?height=300&width=400",
      featured: false,
    },
    {
      id: 3,
      title: "Budget-Friendly Wedding Decoration Ideas",
      excerpt:
        "Create stunning wedding decorations without breaking the bank. These creative ideas will transform your venue beautifully.",
      author: "Rohit Gupta",
      date: "December 10, 2024",
      readTime: "6 min read",
      category: "Decor",
      image: "/placeholder.svg?height=300&width=400",
      featured: false,
    },
    {
      id: 4,
      title: "Choosing the Perfect Wedding Photographer",
      excerpt:
        "Your wedding photos will be treasured forever. Learn how to select the right photographer to capture your special moments.",
      author: "Anjali Singh",
      date: "December 8, 2024",
      readTime: "7 min read",
      category: "Photography",
      image: "/placeholder.svg?height=300&width=400",
      featured: true,
    },
    {
      id: 5,
      title: "Traditional vs Modern: Wedding Menu Planning",
      excerpt: "Strike the perfect balance between traditional flavors and modern presentation for your wedding feast.",
      author: "Chef Arjun",
      date: "December 5, 2024",
      readTime: "5 min read",
      category: "Catering",
      image: "/placeholder.svg?height=300&width=400",
      featured: false,
    },
    {
      id: 6,
      title: "Destination Wedding Planning Guide",
      excerpt:
        "Everything you need to know about planning a destination wedding, from legal requirements to logistics.",
      author: "Travel Expert Maya",
      date: "December 3, 2024",
      readTime: "8 min read",
      category: "Planning",
      image: "/placeholder.svg?height=300&width=400",
      featured: false,
    },
  ]

  const categories = ["All", "Planning", "Fashion", "Decor", "Photography", "Catering", "Real Weddings"]

  return (
    <>
      <SimpleSEO
        title="Wedding Blog - Tips, Trends & Inspiration | BookmyFestive"
        description="Get expert wedding planning tips, latest trends, and real wedding inspiration. Read our comprehensive guides for planning your perfect wedding."
        keywords="wedding blog, wedding tips, wedding trends, wedding inspiration, wedding planning guide, marriage advice"
        url="/blog"
      />
      <div className="min-h-screen bg-white">
      <Header />

      {/* Hero Section */}
      <section className="bg-gradient-to-r from-[#F8F5F0] to-[#F6C244] py-16">
        <div className="container mx-auto px-4">
          <div className="text-center mb-8">
            <h1 className="text-4xl font-bold text-gray-900 mb-4">Wedding Blog & Inspiration</h1>
            <p className="text-xl text-gray-600">
              Expert advice, trends, and real wedding stories to inspire your perfect day
            </p>
          </div>

          {/* Search */}
          <div className="bg-white rounded-lg shadow-lg p-6 max-w-2xl mx-auto">
            <div className="flex gap-4">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input placeholder="Search articles..." className="pl-10" />
              </div>
              <Button className="bg-primary hover:bg-primary/90">Search</Button>
            </div>
          </div>
        </div>
      </section>

      {/* Category Tabs */}
      <section className="py-8 border-b">
        <div className="container mx-auto px-4">
          <div className="flex flex-wrap gap-2 justify-center">
            {categories.map((category, index) => (
              <Button
                key={index}
                variant={index === 0 ? "default" : "outline"}
                size="sm"
                className={index === 0 ? "bg-primary hover:bg-primary/90" : ""}
              >
                {category}
              </Button>
            ))}
          </div>
        </div>
      </section>

      {/* Featured Post */}
      <section className="py-12">
        <div className="container mx-auto px-4">
          <h2 className="text-2xl font-bold mb-8">Featured Article</h2>
          <Card className="overflow-hidden hover:shadow-lg transition-shadow">
            <div className="grid grid-cols-1 lg:grid-cols-2">
              <div className="relative">
                <Image
                  src={blogPosts[0].image || "/placeholder.svg"}
                  alt={blogPosts[0].title}
                  width={400}
                  height={300}
                  className="w-full h-64 lg:h-full object-cover"
                />
                <Badge className="absolute top-4 left-4 bg-primary">Featured</Badge>
              </div>
              <CardContent className="p-8 flex flex-col justify-center">
                <Badge variant="secondary" className="w-fit mb-4">
                  {blogPosts[0].category}
                </Badge>
                <h3 className="text-2xl font-bold mb-4">{blogPosts[0].title}</h3>
                <p className="text-gray-600 mb-6">{blogPosts[0].excerpt}</p>
                <div className="flex items-center text-sm text-gray-500 mb-6">
                  <User className="h-4 w-4 mr-1" />
                  <span className="mr-4">{blogPosts[0].author}</span>
                  <Calendar className="h-4 w-4 mr-1" />
                  <span className="mr-4">{blogPosts[0].date}</span>
                  <Clock className="h-4 w-4 mr-1" />
                  <span>{blogPosts[0].readTime}</span>
                </div>
                <Link href={`/blog/${blogPosts[0].id}`}>
                  <Button className="w-fit bg-primary hover:bg-primary/90">
                    Read More <ArrowRight className="h-4 w-4 ml-2" />
                  </Button>
                </Link>
              </CardContent>
            </div>
          </Card>
        </div>
      </section>

      {/* Blog Posts Grid */}
      <section className="py-12 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="flex justify-between items-center mb-8">
            <h2 className="text-2xl font-bold">Latest Articles</h2>
            <Select>
              <SelectTrigger className="w-40">
                <SelectValue placeholder="Sort by" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="recent">Most Recent</SelectItem>
                <SelectItem value="popular">Most Popular</SelectItem>
                <SelectItem value="trending">Trending</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {blogPosts.slice(1).map((post) => (
              <Card key={post.id} className="overflow-hidden hover:shadow-lg transition-shadow bg-white">
                <div className="relative">
                  <Image
                    src={post.image || "/placeholder.svg"}
                    alt={post.title}
                    width={400}
                    height={300}
                    className="w-full h-48 object-cover"
                  />
                  {post.featured && <Badge className="absolute top-4 left-4 bg-primary">Featured</Badge>}
                </div>
                <CardContent className="p-6">
                  <Badge variant="secondary" className="mb-3">
                    {post.category}
                  </Badge>
                  <h3 className="font-bold text-lg mb-3 line-clamp-2">{post.title}</h3>
                  <p className="text-gray-600 text-sm mb-4 line-clamp-3">{post.excerpt}</p>
                  <div className="flex items-center text-xs text-gray-500 mb-4">
                    <User className="h-3 w-3 mr-1" />
                    <span className="mr-3">{post.author}</span>
                    <Calendar className="h-3 w-3 mr-1" />
                    <span className="mr-3">{post.date}</span>
                    <Clock className="h-3 w-3 mr-1" />
                    <span>{post.readTime}</span>
                  </div>
                  <Link href={`/blog/${post.id}`}>
                    <Button variant="outline" className="w-full bg-transparent">
                      Read More <ArrowRight className="h-4 w-4 ml-2" />
                    </Button>
                  </Link>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Pagination */}
          <div className="flex justify-center mt-12">
            <div className="flex space-x-2">
              <Button variant="outline">Previous</Button>
              <Button className="bg-primary">1</Button>
              <Button variant="outline">2</Button>
              <Button variant="outline">3</Button>
              <Button variant="outline">Next</Button>
            </div>
          </div>
        </div>
      </section>

      <Footer />
      </div>
    </>
  )
}
