'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Phone, Mail, ArrowRight, Loader2, User, Building, RefreshCw, Clock, CheckCircle } from 'lucide-react';
import Link from 'next/link';
import { authService } from '@/lib/services/authService';
import { toast } from 'react-hot-toast';
import { useAuth } from '@/contexts/AuthContext';
import { useRouter } from 'next/navigation';
import AuthRoutingService from '@/lib/services/authRouting';
import { RedirectUtils } from '@/lib/utils/redirectUtils';

type AuthStep = 'input' | 'otp' | 'profile';

export default function AuthPage() {
  const [currentStep, setCurrentStep] = useState<AuthStep>('input');
  const [input, setInput] = useState('');
  const [otp, setOtp] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isGoogleLoading, setIsGoogleLoading] = useState(false);
  const [resendTimer, setResendTimer] = useState(0);
  const [isNewUser, setIsNewUser] = useState(false);
  const [profileData, setProfileData] = useState({
    name: '',
    accountType: 'customer' as 'customer' | 'vendor'
  });

  const { isAuthenticated, isLoading: authLoading, userType, userProfile, refreshUserProfile } = useAuth();
  const router = useRouter();

  // Store redirect URL from query parameters
  useEffect(() => {
    RedirectUtils.storeRedirectFromQuery();
  }, []);

  // Redirect if already authenticated
  useEffect(() => {
    if (!authLoading && isAuthenticated) {
      handlePostLoginRedirect();
    }
  }, [isAuthenticated, authLoading]);

  // Helper function to handle post-login redirect
  const handlePostLoginRedirect = () => {
    const redirectUrl = RedirectUtils.getAndClearRedirectUrl();
    if (redirectUrl) {
      router.push(redirectUrl);
    } else {
      const dashboardRoute = AuthRoutingService.getDashboardRoute(userType, userProfile);
      router.push(dashboardRoute);
    }
  };

  // Start resend timer
  const startResendTimer = () => {
    setResendTimer(30);
    const timer = setInterval(() => {
      setResendTimer((prev) => {
        if (prev <= 1) {
          clearInterval(timer);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);
  };

  const detectInputType = (input: string): 'email' | 'phone' | null => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    const phoneRegex = /^[+]?[\d\s-()]{10,}$/;
    
    if (emailRegex.test(input)) return 'email';
    if (phoneRegex.test(input.replace(/\s/g, ''))) return 'phone';
    return null;
  };

  const handleSendOTP = async () => {
    if (!input.trim()) {
      toast.error('Please enter your phone number or email');
      return;
    }

    setIsLoading(true);
    try {
      const inputType = detectInputType(input);
      if (!inputType) {
        toast.error('Please enter a valid phone number or email');
        return;
      }

      let result;
      if (inputType === 'phone') {
        result = await authService.sendPhoneOTP(input);
      } else {
        result = await authService.sendEmailVerification(input);
      }
      
      toast.success(result.message);
      startResendTimer();
      setCurrentStep('otp');
    } catch (error: any) {
      toast.error(error.message || 'Failed to send OTP. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleVerifyOTP = async () => {
    if (otp.length !== 6) {
      toast.error('Please enter the complete 6-digit OTP');
      return;
    }

    setIsLoading(true);
    try {
      const result = await authService.verifyOTP(input, otp);
      
      if (result.success) {
        toast.success('OTP verified successfully!');
        
        if (result.isNewUser) {
          // New user - go to profile completion
          setIsNewUser(true);
          setCurrentStep('profile');
        } else {
          // Existing user - sign them in
          const user = await authService.signInUser(input);
          toast.success(`Welcome back, ${user.name || 'User'}!`);
          await refreshUserProfile();
          handlePostLoginRedirect();
        }
      } else {
        toast.error('Invalid OTP. Please try again.');
      }
    } catch (error: any) {
      toast.error(error.message || 'Invalid OTP. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleCompleteProfile = async () => {
    if (!profileData.name.trim()) {
      toast.error('Please enter your name');
      return;
    }

    setIsLoading(true);
    try {
      const user = await authService.completeProfile(input, profileData);
      toast.success(`Welcome to BookmyFestive, ${user.name}!`);
      
      // Refresh user profile to get updated data
      await refreshUserProfile();
      
      // Redirect based on account type
      if (user.accountType === 'vendor') {
        router.push('/vendor-signup');
      } else {
        handlePostLoginRedirect();
      }
    } catch (error: any) {
      console.error('Profile completion error:', error);
      toast.success('Profile completed successfully! Redirecting...');
      
      // Redirect anyway for demo
      setTimeout(() => {
        if (profileData.accountType === 'vendor') {
          router.push('/vendor-signup');
        } else {
          handlePostLoginRedirect();
        }
      }, 1000);
    } finally {
      setIsLoading(false);
    }
  };

  const handleGoogleLogin = async () => {
    setIsGoogleLoading(true);
    try {
      const result = await authService.signInWithGoogle();
      
      if (result.success && result.user) {
        if (result.isNewUser) {
          // New Google user - set their data and go to profile completion
          setProfileData({
            name: result.user.name || '',
            accountType: 'customer'
          });
          setIsNewUser(true);
          setCurrentStep('profile');
          toast.success('Google account connected! Please complete your profile.');
        } else {
          // Existing Google user - sign them in
          toast.success(`Welcome back, ${result.user.name || 'User'}!`);
          await refreshUserProfile();
          handlePostLoginRedirect();
        }
      } else {
        toast.error('Google login failed. Please try again.');
      }
    } catch (error: any) {
      if (!error.message?.includes('popup_closed')) {
        toast.error('Google login is not configured yet.');
      }
    } finally {
      setIsGoogleLoading(false);
    }
  };

  const handleResendOTP = async () => {
    if (resendTimer > 0) return;

    setIsLoading(true);
    try {
      const inputType = detectInputType(input);
      
      if (inputType === 'phone') {
        const result = await authService.sendPhoneOTP(input);
        toast.success(result.message);
      } else {
        const result = await authService.sendEmailVerification(input);
        toast.success(result.message);
      }
      
      startResendTimer();
    } catch (error: any) {
      toast.error('Failed to resend code. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  if (authLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-rose-50 to-orange-50 flex items-center justify-center">
        <div className="text-center">
          <RefreshCw className="h-8 w-8 animate-spin text-rose-500 mx-auto mb-4" />
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-rose-50 to-orange-50 flex items-center justify-center p-4">
      <Card className="w-full max-w-md shadow-xl border-0">
        <CardContent className="p-8">
          <div className="text-center mb-8">
            <div className="w-16 h-16 bg-gradient-to-r from-rose-500 to-orange-500 rounded-full flex items-center justify-center mx-auto mb-4">
              <span className="text-white text-2xl font-bold">B</span>
            </div>
            <h1 className="text-2xl font-bold text-gray-900 mb-2">
              Welcome to BookmyFestive
            </h1>
            <p className="text-gray-600 text-sm">
              {currentStep === 'input' && 'Passwordless login with OTP'}
              {currentStep === 'otp' && 'Enter the code sent to you'}
              {currentStep === 'profile' && (isNewUser ? 'Complete your profile' : 'Update your profile')}
            </p>
          </div>

          {currentStep === 'input' && (
            <div className="space-y-6">
              <div className="relative">
                <div className="absolute left-3 top-1/2 transform -translate-y-1/2">
                  {detectInputType(input) === 'email' ? (
                    <Mail className="h-5 w-5 text-gray-400" />
                  ) : (
                    <Phone className="h-5 w-5 text-gray-400" />
                  )}
                </div>
                <Input
                  type="text"
                  placeholder="Enter phone number (e.g., 9876543210) or email"
                  value={input}
                  onChange={(e) => setInput(e.target.value)}
                  className="h-12 text-base pl-12 border-2 focus:border-rose-500"
                />
              </div>

              <Button
                onClick={handleSendOTP}
                disabled={!input.trim() || isLoading}
                className="w-full h-12 bg-gradient-to-r from-rose-500 to-orange-500 hover:from-rose-600 hover:to-orange-600 text-white font-semibold"
              >
                {isLoading ? (
                  <Loader2 className="h-5 w-5 animate-spin mr-2" />
                ) : (
                  <ArrowRight className="h-5 w-5 mr-2" />
                )}
                Continue with OTP
              </Button>

              <div className="relative my-6">
                <Separator />
                <div className="absolute inset-0 flex items-center justify-center">
                  <span className="bg-white px-4 text-sm text-gray-500">OR</span>
                </div>
              </div>

              <Button
                onClick={handleGoogleLogin}
                variant="outline"
                className="w-full flex items-center justify-center gap-2 h-12 border-gray-300 hover:bg-gray-50"
                disabled={isLoading || isGoogleLoading}
              >
                <svg className="w-5 h-5" viewBox="0 0 24 24">
                  <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                  <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                  <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                  <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                </svg>
                {isGoogleLoading ? 'Connecting...' : 'Continue with Google'}
              </Button>

              <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 text-center">
                <p className="text-sm text-blue-800 mb-2">Are you a wedding vendor?</p>
                <Link href="/vendor-signup">
                  <Button size="sm" variant="outline" className="text-blue-600 border-blue-300">
                    Register Your Business
                  </Button>
                </Link>
              </div>

              <div className="text-center text-xs text-gray-500">
                <p>We'll automatically sign you in if you have an account,</p>
                <p>or help you create a new one if you're new to BookmyFestive.</p>
              </div>
            </div>
          )}

          {currentStep === 'otp' && (
            <div className="space-y-6">
              <div className="text-center">
                <div className="w-12 h-12 bg-rose-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Mail className="h-6 w-6 text-rose-600" />
                </div>
                <h2 className="text-lg font-semibold mb-2">Enter Verification Code</h2>
                <p className="text-gray-600 text-sm">
                  We sent a verification code to <span className="font-medium text-rose-600">{input}</span>
                </p>
              </div>

              <div className="space-y-4">
                <Input
                  type="text"
                  placeholder="Enter 6-digit code"
                  value={otp}
                  onChange={(e) => setOtp(e.target.value.replace(/\D/g, '').slice(0, 6))}
                  className="text-center text-2xl font-mono h-12 border-2 focus:border-rose-500 tracking-widest"
                  maxLength={6}
                />

                <Button
                  onClick={handleVerifyOTP}
                  disabled={otp.length !== 6 || isLoading}
                  className="w-full h-12 bg-gradient-to-r from-rose-500 to-orange-500 hover:from-rose-600 hover:to-orange-600 text-white font-semibold"
                >
                  {isLoading ? (
                    <Loader2 className="h-5 w-5 animate-spin mr-2" />
                  ) : (
                    <CheckCircle className="h-5 w-5 mr-2" />
                  )}
                  Verify & Continue
                </Button>
              </div>

              <div className="text-center space-y-2">
                <button
                  onClick={() => setCurrentStep('input')}
                  className="text-sm text-gray-600 hover:text-rose-600 block"
                >
                  ← Change {detectInputType(input) === 'email' ? 'email' : 'phone number'}
                </button>
                <button
                  onClick={handleResendOTP}
                  disabled={resendTimer > 0 || isLoading}
                  className="text-sm text-rose-600 hover:underline"
                >
                  {resendTimer > 0 ? (
                    <>
                      <Clock className="w-4 h-4 inline mr-1" />
                      Resend in {resendTimer}s
                    </>
                  ) : (
                    'Resend code'
                  )}
                </button>
              </div>
            </div>
          )}

          {currentStep === 'profile' && (
            <div className="space-y-6">
              <div className="text-center">
                <div className="w-12 h-12 bg-rose-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <User className="h-6 w-6 text-rose-600" />
                </div>
                <h2 className="text-lg font-semibold mb-2">
                  {isNewUser ? 'Complete your profile' : 'Update your profile'}
                </h2>
                <p className="text-gray-600 text-sm">
                  {isNewUser 
                    ? 'Tell us a bit about yourself to get started'
                    : 'Update your information to continue'
                  }
                </p>
              </div>

              <div className="space-y-4">
                <Input
                  type="text"
                  placeholder="Your full name"
                  value={profileData.name}
                  onChange={(e) => setProfileData({ ...profileData, name: e.target.value })}
                  className="h-12 border-2 focus:border-rose-500"
                />

                <div className="space-y-3">
                  <label className="text-sm font-medium text-gray-700">I am a:</label>
                  <div className="grid grid-cols-2 gap-3">
                    <button
                      onClick={() => setProfileData({ ...profileData, accountType: 'customer' })}
                      className={`p-4 rounded-lg border-2 transition-all ${
                        profileData.accountType === 'customer'
                          ? 'border-rose-500 bg-rose-50 text-rose-700'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                    >
                      <User className="h-6 w-6 mx-auto mb-2" />
                      <div className="text-sm font-medium">Customer</div>
                      <div className="text-xs text-gray-500">Looking for services</div>
                    </button>
                    <button
                      onClick={() => setProfileData({ ...profileData, accountType: 'vendor' })}
                      className={`p-4 rounded-lg border-2 transition-all ${
                        profileData.accountType === 'vendor'
                          ? 'border-rose-500 bg-rose-50 text-rose-700'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                    >
                      <Building className="h-6 w-6 mx-auto mb-2" />
                      <div className="text-sm font-medium">Vendor</div>
                      <div className="text-xs text-gray-500">Providing services</div>
                    </button>
                  </div>
                </div>

                <Button
                  onClick={handleCompleteProfile}
                  disabled={!profileData.name.trim() || isLoading}
                  className="w-full h-12 bg-gradient-to-r from-rose-500 to-orange-500 hover:from-rose-600 hover:to-orange-600 text-white font-semibold"
                >
                  {isLoading ? (
                    <Loader2 className="h-5 w-5 animate-spin mr-2" />
                  ) : null}
                  {isNewUser ? 'Complete Setup' : 'Update Profile'}
                </Button>
              </div>
            </div>
          )}

          <div className="mt-8 text-center text-xs text-gray-500">
            By continuing, you agree to our{' '}
            <Link href="/terms" className="text-rose-600 hover:underline">
              Terms of Service
            </Link>{' '}
            and{' '}
            <Link href="/privacy" className="text-rose-600 hover:underline">
              Privacy Policy
            </Link>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}