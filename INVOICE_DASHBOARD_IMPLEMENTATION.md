# 📋 Invoice Dashboard Implementation Guide

## 🎯 Overview
This document outlines what needs to be implemented to add working invoice view and download functionality across all dashboard pages for customers, vendors, and admins.

## 🚧 Current Issues Identified

### **1. Orders Page (`app/dashboard/orders/page.tsx`)**
- ❌ Invoice buttons exist but don't work
- ❌ Missing invoice service integration
- ❌ Linter errors preventing proper functionality

### **2. Bookings Page (`app/dashboard/bookings/page.tsx`)**
- ❌ No invoice functionality implemented
- ❌ Missing invoice buttons and actions

### **3. Admin Dashboard Pages**
- ❌ No invoice management for admin users
- ❌ Missing bulk invoice operations

## 🔧 What Needs to Be Implemented

### **Phase 1: Fix Orders Page Invoice Functionality**

#### **Current Problems:**
```typescript
// ❌ These functions have linter errors
const handleViewInvoice = async (order: OrderData) => { ... }
const handleDownloadInvoice = async (order: OrderData) => { ... }

// ❌ Invoice buttons don't work properly
<Button onClick={() => handleViewInvoice(order)}>
  <Eye className="h-4 w-4" />
  View Invoice
</Button>
```

#### **Required Fixes:**
1. **Fix TypeScript errors** in invoice handler functions
2. **Properly integrate** with invoice service
3. **Add error handling** for missing invoices
4. **Implement proper routing** to invoice details

### **Phase 2: Add Invoice Functionality to Bookings Page**

#### **Current State:**
```typescript
// ❌ No invoice functionality exists
export default function BookingsPage() {
  // Missing invoice integration
  return (
    <div>
      <BookingsList isVendor={isVendor} />
      {/* ❌ No invoice buttons or actions */}
    </div>
  )
}
```

#### **Required Implementation:**
1. **Add invoice buttons** to each booking card
2. **Integrate with invoice service** for vendor and venue bookings
3. **Implement view and download** functionality
4. **Add invoice status** indicators

### **Phase 3: Implement Admin Invoice Dashboard**

#### **Missing Features:**
1. **Admin invoice overview** with all system invoices
2. **Bulk invoice operations** (download, status update)
3. **Invoice analytics** and reporting
4. **User invoice management** (view invoices for any user)

## 📱 Implementation Details

### **1. Orders Page Invoice Integration**

#### **Required Changes:**
```typescript
// Add to imports
import { invoiceService } from "@/lib/services/invoiceService";

// Add invoice handlers
const handleViewInvoice = async (order: OrderData) => {
  try {
    // Find invoice for this order
    const invoices = await invoiceService.getInvoicesForUser(user?.userId || '', 'customer');
    const orderInvoice = invoices.find(invoice =>
      invoice.notes?.includes(order.id) ||
      invoice.transactionId === order.transactionId
    );

    if (orderInvoice) {
      router.push(`/dashboard/invoices?invoiceId=${orderInvoice.invoiceNumber}`);
    } else {
      showToast.error('Invoice not found for this order');
    }
  } catch (error) {
    console.error('Error viewing invoice:', error);
    showToast.error('Failed to view invoice');
  }
};

const handleDownloadInvoice = async (order: OrderData) => {
  try {
    const invoices = await invoiceService.getInvoicesForUser(user?.userId || '', 'customer');
    const orderInvoice = invoices.find(invoice =>
      invoice.notes?.includes(order.id) ||
      invoice.transactionId === order.transactionId
    );

    if (orderInvoice) {
      const downloadResult = await invoiceService.downloadInvoicePDF(orderInvoice.invoiceNumber);
      if (downloadResult.success) {
        showToast.success('Invoice downloaded successfully');
      } else {
        showToast.error('Failed to download invoice');
      }
    } else {
      showToast.error('Invoice not found for this order');
    }
  } catch (error) {
    console.error('Error downloading invoice:', error);
    showToast.error('Failed to download invoice');
  }
};
```

#### **Update OrderCard Component:**
```typescript
function OrderCard({ 
  order, 
  getStatusColor, 
  getPaymentStatusColor,
  handleViewInvoice,
  handleDownloadInvoice 
}: {
  order: OrderData,
  getStatusColor: (status: string) => string,
  getPaymentStatusColor: (status: string) => string,
  handleViewInvoice: (order: OrderData) => Promise<void>,
  handleDownloadInvoice: (order: OrderData) => Promise<void>
}) {
  return (
    <Card>
      {/* ... existing content ... */}
      
      {/* Actions */}
      <div className="flex gap-3">
        <Button onClick={() => router.push(`/dashboard/order-confirmation?orderId=${order.id}`)}>
          <Eye className="h-4 w-4" />
          View Details
        </Button>
        
        {/* Invoice Actions */}
        <Button
          variant="outline"
          onClick={() => handleViewInvoice(order)}
        >
          <Eye className="h-4 w-4" />
          View Invoice
        </Button>
        
        <Button
          variant="outline"
          onClick={() => handleDownloadInvoice(order)}
        >
          <Download className="h-4 w-4" />
          Download Invoice
        </Button>
      </div>
    </Card>
  );
}
```

### **2. Bookings Page Invoice Integration**

#### **Update BookingsList Component:**
```typescript
// Add to imports
import { invoiceService } from "@/lib/services/invoiceService";

// Add invoice handlers
const handleViewInvoice = async (booking: any) => {
  try {
    const invoices = await invoiceService.getInvoicesForUser(user?.userId || '', isVendor ? 'vendor' : 'customer');
    const bookingInvoice = invoices.find(invoice =>
      invoice.notes?.includes(booking.id) ||
      invoice.eventDate === booking.eventDate
    );

    if (bookingInvoice) {
      router.push(`/dashboard/invoices?invoiceId=${bookingInvoice.invoiceNumber}`);
    } else {
      showToast.error('Invoice not found for this booking');
    }
  } catch (error) {
    console.error('Error viewing invoice:', error);
    showToast.error('Failed to view invoice');
  }
};

const handleDownloadInvoice = async (booking: any) => {
  try {
    const invoices = await invoiceService.getInvoicesForUser(user?.userId || '', isVendor ? 'vendor' : 'customer');
    const bookingInvoice = invoices.find(invoice =>
      invoice.notes?.includes(booking.id) ||
      invoice.eventDate === booking.eventDate
    );

    if (bookingInvoice) {
      const downloadResult = await invoiceService.downloadInvoicePDF(bookingInvoice.invoiceNumber);
      if (downloadResult.success) {
        showToast.success('Invoice downloaded successfully');
      } else {
        showToast.error('Failed to download invoice');
      }
    } else {
      showToast.error('Invoice not found for this booking');
    }
  } catch (error) {
    console.error('Error downloading invoice:', error);
    showToast.error('Failed to download invoice');
  }
};
```

#### **Add Invoice Buttons to Booking Cards:**
```typescript
// In the booking card rendering
<div className="flex gap-2 mt-4">
  <Button
    variant="outline"
    size="sm"
    onClick={() => handleViewInvoice(booking)}
  >
    <Eye className="h-4 w-4" />
    View Invoice
  </Button>
  
  <Button
    variant="outline"
    size="sm"
    onClick={() => handleDownloadInvoice(booking)}
  >
    <Download className="h-4 w-4" />
    Download Invoice
  </Button>
</div>
```

### **3. Admin Invoice Dashboard**

#### **Create Admin Invoice Management:**
```typescript
// New component: components/dashboard/AdminInvoiceDashboard.tsx
export default function AdminInvoiceDashboard() {
  const [allInvoices, setAllInvoices] = useState<InvoiceData[]>([]);
  const [selectedUsers, setSelectedUsers] = useState<string[]>([]);
  
  // Load all invoices for admin
  const loadAllInvoices = async () => {
    try {
      const result = await invoiceService.getInvoicesForAdmin(100);
      setAllInvoices(result.invoices);
    } catch (error) {
      console.error('Error loading admin invoices:', error);
    }
  };
  
  // Bulk download invoices
  const handleBulkDownload = async () => {
    // Implementation for bulk download
  };
  
  // Bulk status update
  const handleBulkStatusUpdate = async (status: string) => {
    // Implementation for bulk status update
  };
  
  return (
    <div>
      {/* Admin invoice overview */}
      {/* Bulk operations */}
      {/* User invoice management */}
    </div>
  );
}
```

## 🎨 UI/UX Requirements

### **Invoice Button Design:**
- **Primary Action**: View Invoice (Eye icon)
- **Secondary Action**: Download Invoice (Download icon)
- **Status Indicators**: Payment status badges
- **Type Indicators**: Invoice type badges

### **Invoice Status Colors:**
```typescript
const statusColors = {
  paid: 'bg-green-100 text-green-800',
  pending: 'bg-yellow-100 text-yellow-800',
  overdue: 'bg-red-100 text-red-800',
  cancelled: 'bg-gray-100 text-gray-800'
};
```

### **Invoice Type Icons:**
```typescript
const typeIcons = {
  product_order: Package,
  venue_booking: MapPin,
  vendor_booking: Building,
  subscription: CreditCard
};
```

## 🔐 Security Considerations

### **User Access Control:**
- **Customers**: Can only view/download their own invoices
- **Vendors**: Can view/download invoices for their services
- **Admins**: Can view/download all invoices

### **Data Validation:**
- Verify user permissions before invoice access
- Validate invoice ownership
- Sanitize user inputs

## 📊 Testing Requirements

### **Test Scenarios:**
1. **Customer Invoice Access**: Verify customers can only see their invoices
2. **Vendor Invoice Access**: Verify vendors can see invoices for their services
3. **Admin Invoice Access**: Verify admins can see all invoices
4. **Invoice Download**: Test PDF download functionality
5. **Invoice Viewing**: Test invoice detail pages
6. **Error Handling**: Test scenarios with missing invoices

### **Test Commands:**
```bash
# Test invoice functionality
npm run test:invoice:management

# Test specific invoice operations
npm run test:invoice:download
npm run test:invoice:view
```

## 🚀 Implementation Priority

### **High Priority (Week 1):**
1. Fix orders page invoice functionality
2. Add basic invoice buttons to bookings page
3. Resolve all linter errors

### **Medium Priority (Week 2):**
1. Complete bookings page invoice integration
2. Add invoice status indicators
3. Implement proper error handling

### **Low Priority (Week 3):**
1. Create admin invoice dashboard
2. Add bulk invoice operations
3. Implement advanced invoice analytics

## 📋 Checklist

### **Orders Page:**
- [ ] Fix TypeScript linter errors
- [ ] Implement working invoice view function
- [ ] Implement working invoice download function
- [ ] Add proper error handling
- [ ] Test invoice functionality

### **Bookings Page:**
- [ ] Add invoice service integration
- [ ] Implement invoice view functionality
- [ ] Implement invoice download functionality
- [ ] Add invoice buttons to booking cards
- [ ] Test vendor and customer invoice access

### **Admin Dashboard:**
- [ ] Create admin invoice overview
- [ ] Implement bulk invoice operations
- [ ] Add user invoice management
- [ ] Create invoice analytics
- [ ] Test admin permissions

### **General:**
- [ ] Update invoice service documentation
- [ ] Add proper error messages
- [ ] Implement loading states
- [ ] Add success notifications
- [ ] Test all user types

## 🎯 Success Criteria

### **Functional Requirements:**
- ✅ All invoice buttons work correctly
- ✅ Invoice viewing and downloading functions properly
- ✅ Proper error handling for missing invoices
- ✅ User access control enforced
- ✅ Responsive design for all devices

### **Performance Requirements:**
- ✅ Invoice loading under 2 seconds
- ✅ PDF download under 5 seconds
- ✅ Smooth user experience
- ✅ No blocking operations

### **Quality Requirements:**
- ✅ No TypeScript linter errors
- ✅ Proper error handling
- ✅ User-friendly error messages
- ✅ Consistent UI/UX across pages

## 🔄 Next Steps

1. **Immediate**: Fix linter errors in orders page
2. **Short-term**: Implement invoice functionality in bookings page
3. **Medium-term**: Create admin invoice dashboard
4. **Long-term**: Add advanced invoice analytics and reporting

This implementation will provide a complete invoice management system across all dashboard pages, ensuring customers, vendors, and admins can properly view and manage their invoices. 