import { Amplify } from 'aws-amplify';
import { signIn, signUp, signOut, getCurrentUser, fetchAuthSession, confirmSignUp, resendSignUpCode, resetPassword, confirmResetPassword } from 'aws-amplify/auth';
import { AuthState, LoginCredentials, SignupData, User, UserProfile } from '../shared/types';
import awsmobile from '../aws-exports';
import { graphqlService } from './graphqlService';

// Initialize Amplify with local configuration
Amplify.configure(awsmobile);

class AmplifyAuthService {
  async getCurrentAuthState(): Promise<AuthState> {
    try {
      const user = await getCurrentUser();
      const session = await fetchAuthSession();
      
      if (user && session.tokens) {
        // Fetch user profile from your GraphQL API
        const userProfile = await this.fetchUserProfile(user.userId);
        
        return {
          isAuthenticated: true,
          isLoading: false,
          user: {
            id: user.userId,
            email: user.signInDetails?.loginId || '',
            fullName: userProfile?.fullName || '',
            phone: userProfile?.phone,
            role: userProfile?.role || 'customer',
            createdAt: userProfile?.createdAt || new Date().toISOString(),
            updatedAt: userProfile?.updatedAt || new Date().toISOString(),
          },
          userProfile,
          userType: userProfile?.role || 'customer',
          error: null,
        };
      }
    } catch (error) {
      console.log('No authenticated user:', error);
    }

    return {
      isAuthenticated: false,
      isLoading: false,
      user: null,
      userProfile: null,
      userType: null,
      error: null,
    };
  }

  async login(credentials: LoginCredentials): Promise<AuthState> {
    try {
      const { isSignedIn, nextStep } = await signIn({
        username: credentials.email,
        password: credentials.password,
      });

      if (isSignedIn) {
        return await this.getCurrentAuthState();
      } else if (nextStep.signInStep === 'CONFIRM_SIGN_UP') {
        throw new Error('Please confirm your email address first');
      } else {
        throw new Error('Login failed. Please check your credentials.');
      }
    } catch (error: any) {
      throw new Error(error.message || 'Login failed');
    }
  }

  async signup(signupData: SignupData): Promise<{ needsConfirmation: boolean; authState?: AuthState }> {
    try {
      const { isSignUpComplete, nextStep } = await signUp({
        username: signupData.email,
        password: signupData.password,
        options: {
          userAttributes: {
            email: signupData.email,
            name: signupData.fullName,
            phone_number: signupData.phone || '',
            'custom:userType': signupData.userType || 'customer',
          },
        },
      });

      if (isSignUpComplete) {
        // Auto sign in after successful signup
        const authState = await this.login({
          email: signupData.email,
          password: signupData.password,
        });
        return { needsConfirmation: false, authState };
      } else if (nextStep.signUpStep === 'CONFIRM_SIGN_UP') {
        return { needsConfirmation: true };
      } else {
        throw new Error('Signup failed');
      }
    } catch (error: any) {
      throw new Error(error.message || 'Signup failed');
    }
  }

  async confirmSignup(email: string, confirmationCode: string): Promise<AuthState> {
    try {
      const { isSignUpComplete } = await confirmSignUp({
        username: email,
        confirmationCode,
      });

      if (isSignUpComplete) {
        // You might want to auto-sign in here or redirect to login
        return {
          isAuthenticated: false,
          isLoading: false,
          user: null,
          userProfile: null,
          userType: null,
          error: null,
        };
      } else {
        throw new Error('Confirmation failed');
      }
    } catch (error: any) {
      throw new Error(error.message || 'Confirmation failed');
    }
  }

  async resendConfirmationCode(email: string): Promise<void> {
    try {
      await resendSignUpCode({ username: email });
    } catch (error: any) {
      throw new Error(error.message || 'Failed to resend confirmation code');
    }
  }

  async logout(): Promise<void> {
    try {
      await signOut();
    } catch (error: any) {
      console.error('Logout error:', error);
      throw new Error(error.message || 'Logout failed');
    }
  }

  async forgotPassword(email: string): Promise<void> {
    try {
      await resetPassword({ username: email });
    } catch (error: any) {
      throw new Error(error.message || 'Failed to send reset code');
    }
  }

  async confirmForgotPassword(email: string, confirmationCode: string, newPassword: string): Promise<void> {
    try {
      await confirmResetPassword({
        username: email,
        confirmationCode,
        newPassword,
      });
    } catch (error: any) {
      throw new Error(error.message || 'Failed to reset password');
    }
  }

  async fetchUserProfile(userId: string): Promise<UserProfile | null> {
    try {
      return await graphqlService.getUserProfile(userId);
    } catch (error) {
      console.error('Error fetching user profile:', error);
      return null;
    }
  }

  async updateUserProfile(userId: string, profileData: Partial<UserProfile>): Promise<UserProfile> {
    try {
      return await graphqlService.updateUserProfile({
        id: userId,
        ...profileData,
      });
    } catch (error: any) {
      throw new Error(error.message || 'Failed to update profile');
    }
  }

  // Vendor-specific methods
  async getVendors(filters: any = {}) {
    try {
      const result = await graphqlService.listVendors(filters, 20);
      return result?.items || [];
    } catch (error) {
      console.error('Error fetching vendors:', error);
      return [];
    }
  }

  async getVenues(filters: any = {}) {
    try {
      const result = await graphqlService.listVenues(filters, 20);
      return result?.items || [];
    } catch (error) {
      console.error('Error fetching venues:', error);
      return [];
    }
  }

  async getProducts(filters: any = {}) {
    try {
      const result = await graphqlService.listShops(filters, 20);

      // Extract products from shops
      const shops = result?.items || [];
      const products = shops.flatMap(shop =>
        shop.products?.map(product => ({
          ...product,
          shopId: shop.id,
          shopName: shop.name,
          vendorId: shop.id,
          vendorName: shop.name,
        })) || []
      );

      return products;
    } catch (error) {
      console.error('Error fetching products:', error);
      return [];
    }
  }
}

export const amplifyAuthService = new AmplifyAuthService();
export default AmplifyAuthService;
