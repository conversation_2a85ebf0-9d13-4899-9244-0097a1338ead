import { signIn, signUp, confirmSignUp, resendSignUpCode, resetPassword, confirmResetPassword, getCurrentUser } from 'aws-amplify/auth';
import { HybridMobileAuthService } from '@/lib/services/hybridMobileAuth';

export interface AuthUser {
  id: string;
  email?: string;
  phone?: string;
  name?: string;
  accountType?: 'customer' | 'vendor';
  isEmailVerified?: boolean;
  isPhoneVerified?: boolean;
  isNewUser?: boolean;
}

class AuthService {
  async signInWithPassword(identifier: string, password: string): Promise<any> {
    try {
      const result = await signIn({ username: identifier, password });
      if (result.isSignedIn) {
        return await getCurrentUser();
      }
      return result;
    } catch (error: any) {
      if (error.name === 'UserNotConfirmedException') {
        const confirmError = new Error('Please verify your email address first.');
        (confirmError as any).name = 'UserNotConfirmedException';
        throw confirmError;
      }
      throw error;
    }
  }

  async signUp(email: string, fullName: string, password: string, isPhone: boolean = false): Promise<any> {
    const userAttributes: any = { name: fullName };
    
    if (isPhone) {
      userAttributes.phone_number = email.startsWith('+') ? email : `+91${email}`;
    } else {
      userAttributes.email = email;
    }
    
    return await signUp({
      username: email,
      password,
      options: { userAttributes }
    });
  }

  async confirmSignUp(email: string, code: string): Promise<any> {
    return await confirmSignUp({ username: email, confirmationCode: code });
  }

  async resendConfirmationCode(identifier: string): Promise<any> {
    return await resendSignUpCode({ username: identifier });
  }

  async requestPasswordReset(identifier: string): Promise<any> {
    return await resetPassword({ username: identifier });
  }

  async confirmPasswordReset(identifier: string, code: string, newPassword: string): Promise<any> {
    return await confirmResetPassword({
      username: identifier,
      confirmationCode: code,
      newPassword
    });
  }

  async sendEmailVerification(email: string): Promise<{ success: boolean; message: string; skipOTP?: boolean; user?: AuthUser }> {
    try {
      await resetPassword({ username: email });
      return {
        success: true,
        message: 'Login code sent to your email'
      };
    } catch (error: any) {
      if (error.name === 'UserNotFoundException') {
        throw new Error('Account not found. Please sign up first.');
      }
      throw error;
    }
  }

  async verifyEmailOTP(email: string, code: string): Promise<{ success: boolean; user?: any; isNewUser?: boolean }> {
    try {
      const tempPassword = `Temp${Date.now()}!`;
      
      await confirmResetPassword({
        username: email,
        confirmationCode: code,
        newPassword: tempPassword
      });
      
      const signInResult = await signIn({ 
        username: email, 
        password: tempPassword 
      });
      
      if (signInResult.isSignedIn) {
        const user = await getCurrentUser();
        return {
          success: true,
          user,
          isNewUser: false
        };
      }
      
      throw new Error('Failed to sign in after verification');
    } catch (error: any) {
      if (error.name === 'CodeMismatchException') {
        throw new Error('Invalid OTP. Please try again.');
      }
      if (error.name === 'ExpiredCodeException') {
        throw new Error('OTP has expired. Please request a new one.');
      }
      throw error;
    }
  }

  async sendOTP(phone: string, countryCode: string = '+91'): Promise<{ success: boolean; session?: string; message: string }> {
    return await HybridMobileAuthService.sendMobileOTP(phone, countryCode);
  }

  async verifyOTP(identifier: string, code: string): Promise<{ success: boolean; user?: any; isNewUser?: boolean }> {
    // Check if identifier is email or phone
    const isEmail = identifier.includes('@');
    
    if (isEmail) {
      return await this.verifyEmailOTP(identifier, code);
    } else {
      // For phone, we need the session ID, but since we don't have it,
      // we'll use the HybridMobileAuthService directly
      return await HybridMobileAuthService.verifyMobileOTP(identifier, code);
    }
  }

  async sendPhoneOTP(phoneNumber: string): Promise<{ success: boolean; message: string; skipOTP?: boolean; user?: AuthUser }> {
    try {
      const result = await HybridMobileAuthService.sendMobileOTP(phoneNumber, '+91');
      return {
        success: result.success,
        message: result.message
      };
    } catch (error: any) {
      throw error;
    }
  }

  async checkUserExists(identifier: string): Promise<{ exists: boolean; needsSignup: boolean }> {
    try {
      const inputType = identifier.includes('@') ? 'email' : 'phone';
      
      if (inputType === 'email') {
        // Try password reset to check if user exists
        await resetPassword({ username: identifier });
        return { exists: true, needsSignup: false };
      } else {
        // For phone, try to send OTP to check if user exists
        const result = await HybridMobileAuthService.sendMobileOTP(identifier, '+91');
        return { exists: true, needsSignup: false };
      }
    } catch (error: any) {
      if (error.name === 'UserNotFoundException') {
        return { exists: false, needsSignup: true };
      }
      // If other error, assume user exists
      return { exists: true, needsSignup: false };
    }
  }

  async refreshUserProfile(): Promise<void> {
    // This would be handled by the AuthContext
  }

  async getCurrentUser(): Promise<any> {
    try {
      return await getCurrentUser();
    } catch (error) {
      return null;
    }
  }

  async signOut(): Promise<void> {
    try {
      // Clear all local storage data
      localStorage.removeItem('authSession');
      localStorage.removeItem('googleAuthSession');
      localStorage.removeItem('currentUser');
      localStorage.removeItem('registeredUsers');
      localStorage.removeItem('pendingUserData');
      localStorage.removeItem('pendingBusinessData');
      
      // Clear session storage
      sessionStorage.removeItem('auth-state');
      
      // AWS Cognito signout
      const { signOut } = await import('aws-amplify/auth');
      await signOut();
    } catch (error) {
      console.log('Signout error:', error);
      // Clear data even if AWS signout fails
      localStorage.clear();
      sessionStorage.clear();
    }
  }
}

export const authService = new AuthService();
export default authService;