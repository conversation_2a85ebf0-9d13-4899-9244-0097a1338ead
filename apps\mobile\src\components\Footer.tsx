import React from 'react';
import { View, Text, TouchableOpacity, Linking, ScrollView } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Card, CardContent } from './ui/Card';
import { Button } from './ui/Button';
import { useTheme } from '../providers/ThemeProvider';
import { useNavigation } from '@react-navigation/native';

export function Footer() {
  const { theme } = useTheme();
  const navigation = useNavigation();

  const handleSocialPress = (url: string) => {
    Linking.openURL(url).catch(err => console.error('Failed to open URL:', err));
  };

  const handleNavigation = (screen: string, params?: any) => {
    navigation.navigate(screen as never, params as never);
  };

  const footerSections = [
    {
      title: 'Festive Services',
      links: [
        { label: 'Venues', onPress: () => handleNavigation('Venues') },
        { label: 'Photography', onPress: () => handleNavigation('Vendors', { category: 'photography' }) },
        { label: 'Catering', onPress: () => handleNavigation('Vendors', { category: 'catering' }) },
        { label: 'Decoration', onPress: () => handleNavigation('Vendors', { category: 'decoration' }) },
        { label: 'Music & Entertainment', onPress: () => handleNavigation('Vendors', { category: 'music' }) },
      ],
    },
    {
      title: 'Shopping',
      links: [
        { label: 'Bridal Wear', onPress: () => handleNavigation('Shop', { category: 'bridal-wear' }) },
        { label: 'Groom Wear', onPress: () => handleNavigation('Shop', { category: 'groom-wear' }) },
        { label: 'Jewelry', onPress: () => handleNavigation('Shop', { category: 'jewelry' }) },
        { label: 'Accessories', onPress: () => handleNavigation('Shop', { category: 'accessories' }) },
        { label: 'Invitations', onPress: () => handleNavigation('Shop', { category: 'invitations' }) },
      ],
    },
    {
      title: 'Planning Tools',
      links: [
        { label: 'Budget Calculator', onPress: () => handleNavigation('BudgetCalculator') },
        { label: 'Guest List Manager', onPress: () => handleNavigation('GuestList') },
        { label: 'Wedding Checklist', onPress: () => handleNavigation('Checklist') },
        { label: 'Timeline Planner', onPress: () => handleNavigation('Timeline') },
        { label: 'Vendor Comparison', onPress: () => handleNavigation('VendorComparison') },
      ],
    },
    {
      title: 'Support',
      links: [
        { label: 'Help Center', onPress: () => handleNavigation('Help') },
        { label: 'Contact Us', onPress: () => handleNavigation('Contact') },
        { label: 'FAQs', onPress: () => handleNavigation('FAQ') },
        { label: 'Terms of Service', onPress: () => handleNavigation('Terms') },
        { label: 'Privacy Policy', onPress: () => handleNavigation('Privacy') },
      ],
    },
  ];

  const socialLinks = [
    {
      name: 'Facebook',
      icon: 'logo-facebook',
      url: 'https://facebook.com/BookmyFestive',
      color: '#1877F2',
    },
    {
      name: 'Instagram',
      icon: 'logo-instagram',
      url: 'https://instagram.com/BookmyFestive',
      color: '#E4405F',
    },
    {
      name: 'YouTube',
      icon: 'logo-youtube',
      url: 'https://youtube.com/@BookmyFestive',
      color: '#FF0000',
    },
    {
      name: 'Twitter',
      icon: 'logo-twitter',
      url: 'https://twitter.com/BookmyFestive',
      color: '#1DA1F2',
    },
    {
      name: 'WhatsApp',
      icon: 'logo-whatsapp',
      url: 'https://wa.me/919876543210',
      color: '#25D366',
    },
  ];

  return (
    <View style={{ 
      backgroundColor: '#1F2937', 
      paddingTop: 32,
      paddingBottom: 16,
    }}>
      {/* Company Info */}
      <View style={{ paddingHorizontal: 16, marginBottom: 24 }}>
        <Text style={{
          fontSize: 24,
          fontWeight: '700',
          color: '#fff',
          marginBottom: 8,
        }}>
          BookmyFestive
        </Text>
        
        <Text style={{
          fontSize: 14,
          color: '#9CA3AF',
          lineHeight: 20,
          marginBottom: 16,
        }}>
          Your one-stop destination for all wedding planning needs. Making Your Dream Celebration a reality.
        </Text>

        {/* Social Media Links */}
        <View style={{ flexDirection: 'row', gap: 12, flexWrap: 'wrap' }}>
          {socialLinks.map((social) => (
            <TouchableOpacity
              key={social.name}
              onPress={() => handleSocialPress(social.url)}
              style={{
                width: 40,
                height: 40,
                borderRadius: 20,
                backgroundColor: social.color,
                alignItems: 'center',
                justifyContent: 'center',
              }}
            >
              <Ionicons name={social.icon as any} size={20} color="#fff" />
            </TouchableOpacity>
          ))}
        </View>
      </View>

      {/* Footer Links */}
      <ScrollView 
        horizontal 
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={{ paddingHorizontal: 16 }}
        style={{ marginBottom: 24 }}
      >
        {footerSections.map((section, sectionIndex) => (
          <View 
            key={section.title}
            style={{ 
              width: 200, 
              marginRight: sectionIndex < footerSections.length - 1 ? 16 : 0 
            }}
          >
            <Text style={{
              fontSize: 16,
              fontWeight: '600',
              color: '#fff',
              marginBottom: 12,
            }}>
              {section.title}
            </Text>
            
            {section.links.map((link) => (
              <TouchableOpacity
                key={link.label}
                onPress={link.onPress}
                style={{ marginBottom: 8 }}
              >
                <Text style={{
                  fontSize: 14,
                  color: '#9CA3AF',
                  lineHeight: 20,
                }}>
                  {link.label}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        ))}
      </ScrollView>

      {/* Contact Info */}
      <View style={{ 
        paddingHorizontal: 16, 
        paddingTop: 16,
        borderTopWidth: 1,
        borderTopColor: '#374151',
        marginBottom: 16,
      }}>
        <Text style={{
          fontSize: 16,
          fontWeight: '600',
          color: '#fff',
          marginBottom: 12,
        }}>
          Contact Information
        </Text>
        
        <View style={{ gap: 8 }}>
          <View style={{ flexDirection: 'row', alignItems: 'center', gap: 8 }}>
            <Ionicons name="call" size={16} color="#9CA3AF" />
            <Text style={{ fontSize: 14, color: '#9CA3AF' }}>
              +91 98765 43210
            </Text>
          </View>
          
          <View style={{ flexDirection: 'row', alignItems: 'center', gap: 8 }}>
            <Ionicons name="mail" size={16} color="#9CA3AF" />
            <Text style={{ fontSize: 14, color: '#9CA3AF' }}>
              <EMAIL>
            </Text>
          </View>
          
          <View style={{ flexDirection: 'row', alignItems: 'center', gap: 8 }}>
            <Ionicons name="location" size={16} color="#9CA3AF" />
            <Text style={{ fontSize: 14, color: '#9CA3AF' }}>
              Chennai, Tamil Nadu, India
            </Text>
          </View>
        </View>
      </View>

      {/* App Download */}
      <View style={{ 
        paddingHorizontal: 16,
        paddingTop: 16,
        borderTopWidth: 1,
        borderTopColor: '#374151',
        marginBottom: 16,
      }}>
        <Text style={{
          fontSize: 16,
          fontWeight: '600',
          color: '#fff',
          marginBottom: 12,
        }}>
          Download Our App
        </Text>
        
        <View style={{ flexDirection: 'row', gap: 12 }}>
          <TouchableOpacity
            onPress={() => handleSocialPress('https://play.google.com/store')}
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              backgroundColor: '#000',
              paddingHorizontal: 12,
              paddingVertical: 8,
              borderRadius: 8,
              flex: 1,
            }}
          >
            <Ionicons name="logo-google-playstore" size={20} color="#fff" />
            <View style={{ marginLeft: 8 }}>
              <Text style={{ fontSize: 10, color: '#fff' }}>Get it on</Text>
              <Text style={{ fontSize: 12, fontWeight: '600', color: '#fff' }}>Google Play</Text>
            </View>
          </TouchableOpacity>
          
          <TouchableOpacity
            onPress={() => handleSocialPress('https://apps.apple.com')}
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              backgroundColor: '#000',
              paddingHorizontal: 12,
              paddingVertical: 8,
              borderRadius: 8,
              flex: 1,
            }}
          >
            <Ionicons name="logo-apple" size={20} color="#fff" />
            <View style={{ marginLeft: 8 }}>
              <Text style={{ fontSize: 10, color: '#fff' }}>Download on the</Text>
              <Text style={{ fontSize: 12, fontWeight: '600', color: '#fff' }}>App Store</Text>
            </View>
          </TouchableOpacity>
        </View>
      </View>

      {/* Copyright */}
      <View style={{ 
        paddingHorizontal: 16,
        paddingTop: 16,
        borderTopWidth: 1,
        borderTopColor: '#374151',
      }}>
        <Text style={{
          fontSize: 12,
          color: '#6B7280',
          textAlign: 'center',
        }}>
          © 2025 BookmyFestive. All rights reserved.
        </Text>
      </View>
    </View>
  );
}
