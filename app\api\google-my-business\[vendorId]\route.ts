import { NextRequest, NextResponse } from 'next/server'
import { GoogleMyBusinessService } from '@/lib/services/googleMyBusinessService'

export async function GET(
  request: NextRequest,
  { params }: { params: { vendorId: string } }
) {
  try {
    const { searchParams } = new URL(request.url)
    const dateRange = searchParams.get('range') || '30d'
    
    const data = await GoogleMyBusinessService.getBusinessInsights(
      params.vendorId,
      dateRange
    )
    
    return NextResponse.json(data)
  } catch (error) {
    console.error('GMB API Error:', error)
    return NextResponse.json({ error: 'Failed to fetch GMB data' }, { status: 500 })
  }
}