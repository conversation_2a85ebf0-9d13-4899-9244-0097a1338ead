"use client"
import { useState, useMemo, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { ImageUpload } from "@/components/ui/image-upload";
import { Search, Filter, Eye, Edit, Trash2, Plus, MapPin, Users, Star, Phone, Mail, Globe, CheckCircle, XCircle, Clock } from "lucide-react";
import Link from "next/link";
import { venueService, type VenueResponse, type VenueData, type VenueSpace, type VenuePackage } from "@/lib/services/venueService";
import { useAuth } from "@/contexts/AuthContext";
import { Progress } from "@/components/ui/progress"

const initialVenues = [
  {
    id: 1,
    name: "Royal Palace Banquets",
    location: "Andheri West",
    city: "Mumbai",
    state: "Maharashtra",
    pincode: "400058",
    fullAddress: "123 Wedding Street, Andheri West, Mumbai - 400058",
    type: "Banquet Hall",
    capacity: "200-500 guests",
    totalArea: "15,000 sq ft",
    availableRooms: "20 Rooms",
    price: "₹2,00,000",
    priceRange: "₹2,00,000 - ₹5,00,000",
    featured: true,
    verified: true,
    description: "Royal Palace Banquets is a premium wedding venue offering elegant spaces for your special day. With luxurious interiors, world-class amenities, and exceptional service, we ensure your wedding is nothing short of magical.",
    amenities: ["AC", "Parking", "Catering", "Decoration", "Sound System", "Bridal Room", "Valet Parking"],
    images: ["/api/placeholder/600/400", "/api/placeholder/600/400", "/api/placeholder/600/400"],
    mainImage: "/api/placeholder/600/400",
    status: "active",
    bookings: 12,
    rating: 4.8,
    reviewCount: 124,
    contactPhone: "+91 8148376909",
    contactEmail: "<EMAIL>",
    website: "www.royalpalacebanquets.com",
    socialMedia: {
      facebook: "royalpalacebanquets",
      instagram: "royalpalacebanquets",
      youtube: "royalpalacebanquets"
    },
    policies: {
      cancellation: "Free cancellation up to 30 days before event",
      advance: "50% advance payment required",
      extraGuest: "₹500 per additional guest"
    },
    spaces: [
      { name: "Grand Ballroom", capacity: "300-500 guests", area: "8,000 sq ft", price: "₹3,00,000" },
      { name: "Crystal Hall", capacity: "200-300 guests", area: "5,000 sq ft", price: "₹2,00,000" },
      { name: "Garden Area", capacity: "100-200 guests", area: "2,000 sq ft", price: "₹1,50,000" }
    ],
    packages: [
      {
        name: "Basic Package",
        price: "₹2,00,000",
        duration: "6 hours",
        includes: ["Hall rental", "Basic decoration", "Sound system", "Basic lighting", "Parking"]
      },
      {
        name: "Premium Package",
        price: "₹3,50,000",
        duration: "8 hours",
        includes: ["Hall rental", "Premium decoration", "Sound & lighting", "Bridal room", "Parking", "Basic catering setup"]
      },
      {
        name: "Luxury Package",
        price: "₹5,00,000",
        duration: "Full day",
        includes: ["Hall rental", "Luxury decoration", "Premium sound & lighting", "Bridal room", "Guest rooms (10)", "Parking", "Full catering setup", "Event coordinator"]
      }
    ]
  },
  {
    id: 2,
    name: "Garden Paradise Resort",
    location: "Coimbatore",
    type: "Resort",
    capacity: "100-300 guests",
    price: "₹3,00,000 - ₹8,00,000",
    featured: false,
    description: "Beautiful garden resort perfect for outdoor wedding ceremonies",
    amenities: ["Garden", "Pool", "AC", "Parking", "Catering"],
    image: "/api/placeholder/400/250",
    status: "active",
    bookings: 8
  },
  {
    id: 3,
    name: "Heritage Wedding Hall",
    location: "Madurai",
    type: "Wedding Hall",
    capacity: "300-800 guests",
    price: "₹1,50,000 - ₹4,00,000",
    featured: true,
    description: "Traditional wedding hall with cultural architecture and modern facilities",
    amenities: ["AC", "Parking", "Sound System", "Decoration"],
    image: "/api/placeholder/400/250",
    status: "active",
    bookings: 15
  },
  {
    id: 4,
    name: "Beachside Wedding Venue",
    location: "Pondicherry",
    type: "Beach Resort",
    capacity: "50-200 guests",
    price: "₹4,00,000 - ₹10,00,000",
    featured: false,
    description: "Stunning beachside venue for intimate and destination weddings",
    amenities: ["Beach Access", "AC", "Catering", "Photography"],
    image: "/api/placeholder/400/250",
    status: "active",
    bookings: 6
  }
];

export default function DashboardVenuesPage() {
  const { isAuthenticated, isLoading } = useAuth();
  const [venues, setVenues] = useState<VenueResponse[]>([]);
  const [showModal, setShowModal] = useState(false);
  const [editIdx, setEditIdx] = useState<number | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [filterType, setFilterType] = useState("");
  const [filterStatus, setFilterStatus] = useState("");
  const [sortBy, setSortBy] = useState("name");
  const [selectedVenues, setSelectedVenues] = useState<string[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [form, setForm] = useState({
    name: "",
    location: "",
    city: "",
    state: "",
    pincode: "",
    fullAddress: "",
    type: "",
    capacity: "",
    totalArea: "",
    availableRooms: "",
    price: "",
    priceRange: "",
    featured: false,
    verified: false,
    description: "",
    amenities: [],
    images: [],
    mainImage: "",
    status: "active",
    bookings: 0,
    rating: 0,
    reviewCount: 0,
    contactPhone: "",
    contactEmail: "",
    website: "",
    availability: "",
    socialMedia: {
      facebook: "",
      instagram: "",
      youtube: ""
    },
    policies: {
      cancellation: "",
      advance: "",
      catering: "",
      decoration: "",
      alcohol: "",
      music: "",
      parking: ""
    },
    coordinates: {
      latitude: "",
      longitude: ""
    },
    operatingHours: {
      monday: "",
      tuesday: "",
      wednesday: "",
      thursday: "",
      friday: "",
      saturday: "",
      sunday: ""
    },
    spaces: [],
    packages: []
  });

  const venueTypes = [
    "Banquet Hall",
    "Wedding Hall",
    "Resort",
    "Beach Resort",
    "Garden Venue",
    "Palace",
    "Hotel",
    "Farm House",
    "Convention Center"
  ];

  const availableAmenities = [
    "AC", "Parking", "Catering", "Decoration", "Sound System",
    "Garden", "Pool", "Beach Access", "Photography", "Valet Parking",
    "Bridal Room", "Generator", "WiFi", "Security"
  ];

  const [modalStep, setModalStep] = useState(1); // 1 to 4
  const totalSteps = 4;

  // Validation for each step
  const validateStep = (step: number) => {
    if (step === 1) {
      return form.name.trim() && form.type.trim() && form.capacity.trim();
    }
    if (step === 2) {
      return form.location.trim() && form.city.trim() && form.state.trim() && form.price.trim();
    }
    // Steps 3 and 4 can be less strict, or add more as needed
    return true;
  };

  // Reset modal step when opening/closing modal
  useEffect(() => {
    if (showModal) setModalStep(1);
  }, [showModal]);

  // Load venues on component mount
  useEffect(() => {
    if (isAuthenticated) {
      loadVenues();
    }
  }, [isAuthenticated]);

  // Set up real-time subscriptions
  useEffect(() => {
    if (!isAuthenticated) return;

    let createSubscription: any;
    let updateSubscription: any;
    let deleteSubscription: any;

    try {
      // Subscribe to venue creation
      createSubscription = venueService.subscribeToVenueCreation((newVenue) => {
        setVenues(prev => [...prev, newVenue]);
      });

      // Subscribe to venue updates
      updateSubscription = venueService.subscribeToVenueUpdates((updatedVenue) => {
        setVenues(prev => prev.map(venue =>
          venue.id === updatedVenue.id ? updatedVenue : venue
        ));
      });

      // Subscribe to venue deletion
      deleteSubscription = venueService.subscribeToVenueDeletion((deletedVenue) => {
        setVenues(prev => prev.filter(venue => venue.id !== deletedVenue.id));
        setSelectedVenues(prev => prev.filter(id => id !== deletedVenue.id));
      });
    } catch (error) {
      console.error('Error setting up subscriptions:', error);
    }

    // Cleanup subscriptions on unmount
    return () => {
      if (createSubscription) {
        createSubscription.unsubscribe();
      }
      if (updateSubscription) {
        updateSubscription.unsubscribe();
      }
      if (deleteSubscription) {
        deleteSubscription.unsubscribe();
      }
    };
  }, [isAuthenticated]);

  const loadVenues = async () => {
    try {
      setLoading(true);
      setError(null);
      const venues = await venueService.getUserVenues();
      setVenues(venues);
    } catch (err) {
      console.error('Error loading venues:', err);
      setError('Failed to load venues. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Filter and sort venues
  const filteredAndSortedVenues = useMemo(() => {
    let filtered = venues.filter(venue => {
      const matchesSearch = venue.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           venue.location.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           venue.type.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesType = !filterType || venue.type === filterType;
      const matchesStatus = !filterStatus || venue.status === filterStatus;

      return matchesSearch && matchesType && matchesStatus;
    });

    // Sort venues
    filtered.sort((a, b) => {
      switch (sortBy) {
        case "name":
          return a.name.localeCompare(b.name);
        case "bookings":
          return (b.bookings || 0) - (a.bookings || 0);
        case "rating":
          return (b.rating || 0) - (a.rating || 0);
        case "price":
          const priceA = parseInt(a.price.replace(/[^\d]/g, '')) || 0;
          const priceB = parseInt(b.price.replace(/[^\d]/g, '')) || 0;
          return priceA - priceB;
        default:
          return 0;
      }
    });

    return filtered;
  }, [venues, searchTerm, filterType, filterStatus, sortBy]);

  const resetForm = () => {
    setForm({
      name: "",
      location: "",
      city: "",
      state: "",
      pincode: "",
      fullAddress: "",
      type: "",
      capacity: "",
      totalArea: "",
      availableRooms: "",
      price: "",
      priceRange: "",
      featured: false,
      verified: false,
      description: "",
      amenities: [],
      images: [],
      mainImage: "",
      status: "active",
      bookings: 0,
      rating: 0,
      reviewCount: 0,
      contactPhone: "",
      contactEmail: "",
      website: "",
      availability: "",
      socialMedia: {
        facebook: "",
        instagram: "",
        youtube: ""
      },
      policies: {
        cancellation: "",
        advance: "",
        catering: "",
        decoration: "",
        alcohol: "",
        music: "",
        parking: ""
      },
      coordinates: {
        latitude: "",
        longitude: ""
      },
      operatingHours: {
        monday: "",
        tuesday: "",
        wednesday: "",
        thursday: "",
        friday: "",
        saturday: "",
        sunday: ""
      },
      spaces: [],
      packages: []
    });
    setEditIdx(null);
  };

  const openAdd = () => {
    resetForm();
    setShowModal(true);
  };
  const openEdit = (idx: number) => {
    const venue = venues[idx];
    setForm({
      name: venue.name || "",
      location: venue.location || "",
      city: venue.city || "",
      state: venue.state || "",
      pincode: venue.pincode || "",
      fullAddress: venue.fullAddress || "",
      type: venue.type || "",
      capacity: venue.capacity || "",
      totalArea: venue.totalArea || "",
      availableRooms: venue.availableRooms || "",
      price: venue.price || "",
      priceRange: venue.priceRange || "",
      featured: venue.featured || false,
      verified: venue.verified || false,
      description: venue.description || "",
      amenities: venue.amenities || [],
      images: venue.images || [],
      mainImage: venue.images?.[0] || "",
      status: venue.status || "active",
      bookings: venue.bookings || 0,
      rating: venue.rating || 0,
      reviewCount: venue.reviewCount || 0,
      contactPhone: venue.contactPhone || "",
      contactEmail: venue.contactEmail || "",
      website: venue.website || "",
      availability: venue.availability || "",
      socialMedia: venue.socialMedia || {
        facebook: "",
        instagram: "",
        youtube: ""
      },
      policies: venue.policies || {
        cancellation: "",
        advance: "",
        catering: "",
        decoration: "",
        alcohol: "",
        music: "",
        parking: ""
      },
      coordinates: venue.coordinates || {
        latitude: "",
        longitude: ""
      },
      operatingHours: venue.operatingHours || {
        monday: "",
        tuesday: "",
        wednesday: "",
        thursday: "",
        friday: "",
        saturday: "",
        sunday: ""
      },
      spaces: venue.spaces || [],
      packages: venue.packages || []
    });
    setEditIdx(idx);
    setShowModal(true);
  };
  const handleChange = (e: any) => {
    const { name, value, type, checked } = e.target;

    if (name.includes('.')) {
      // Handle nested objects like socialMedia.facebook
      const [parent, child] = name.split('.');
      setForm((f) => ({
        ...f,
        [parent]: {
          ...f[parent as keyof typeof f],
          [child]: type === "checkbox" ? checked : value
        }
      }));
    } else {
      setForm((f) => ({ ...f, [name]: type === "checkbox" ? checked : value }));
    }
  };

  const handleAmenityChange = (amenity: string) => {
    setForm((f) => ({
      ...f,
      amenities: f.amenities.includes(amenity)
        ? f.amenities.filter(a => a !== amenity)
        : [...f.amenities, amenity]
    }));
  };

  // Space management functions
  const addSpace = () => {
    setForm((f) => ({
      ...f,
      spaces: [...f.spaces, { name: "", capacity: "", area: "", price: "" }]
    }));
  };

  const removeSpace = (index: number) => {
    setForm((f) => ({
      ...f,
      spaces: f.spaces.filter((_, i) => i !== index)
    }));
  };

  const updateSpace = (index: number, field: string, value: string) => {
    setForm((f) => ({
      ...f,
      spaces: f.spaces.map((space, i) =>
        i === index ? { ...space, [field]: value } : space
      )
    }));
  };

  // Package management functions
  const addPackage = () => {
    setForm((f) => ({
      ...f,
      packages: [...f.packages, { name: "", price: "", duration: "", includes: [] }]
    }));
  };

  const removePackage = (index: number) => {
    setForm((f) => ({
      ...f,
      packages: f.packages.filter((_, i) => i !== index)
    }));
  };

  const updatePackage = (index: number, field: string, value: string | string[]) => {
    setForm((f) => ({
      ...f,
      packages: f.packages.map((pkg, i) =>
        i === index ? { ...pkg, [field]: value } : pkg
      )
    }));
  };

  const updatePackageIncludes = (packageIndex: number, includeText: string) => {
    const includesArray = includeText.split(',').map(item => item.trim()).filter(item => item);
    updatePackage(packageIndex, 'includes', includesArray);
  };
  const handleSave = async () => {
    try {
      setLoading(true);

      // Prepare venue data, filtering out empty optional fields
      const venueData: any = {
        name: form.name,
        type: form.type,
        capacity: form.capacity,
        location: form.location,
        city: form.city,
        state: form.state,
        price: form.price,
        status: form.status || 'active'
      };

      // Add optional fields only if they have values
      if (form.fullAddress) venueData.fullAddress = form.fullAddress;
      if (form.pincode) venueData.pincode = form.pincode;
      if (form.priceRange) venueData.priceRange = form.priceRange;
      if (form.description) venueData.description = form.description;
      if (form.contactPhone) venueData.contactPhone = form.contactPhone;
      if (form.contactEmail) venueData.contactEmail = form.contactEmail;
      if (form.website) venueData.website = form.website;
      if (form.availability) venueData.availability = form.availability;

      // Add arrays (always include, even if empty)
      venueData.amenities = form.amenities || [];
      venueData.spaces = form.spaces || [];
      venueData.packages = form.packages || [];
      venueData.images = form.images || [];

      // Add boolean fields
      venueData.featured = form.featured || false;
      venueData.verified = form.verified || false;

      // Add numeric fields
      if (form.rating) venueData.rating = form.rating;
      if (form.reviewCount) venueData.reviewCount = form.reviewCount;
      if (form.bookings) venueData.bookings = form.bookings;

      // Add complex objects only if they have values
      if (form.socialMedia && (form.socialMedia.facebook || form.socialMedia.instagram || form.socialMedia.youtube)) {
        venueData.socialMedia = form.socialMedia;
      }

      if (form.policies && Object.values(form.policies).some(value => value)) {
        venueData.policies = form.policies;
      }

      if (form.coordinates && (form.coordinates.latitude || form.coordinates.longitude)) {
        venueData.coordinates = {
          latitude: form.coordinates.latitude ? parseFloat(form.coordinates.latitude) : null,
          longitude: form.coordinates.longitude ? parseFloat(form.coordinates.longitude) : null
        };
      }

      if (form.operatingHours && Object.values(form.operatingHours).some(value => value)) {
        venueData.operatingHours = form.operatingHours;
      }

      if (editIdx !== null) {
        // Update existing venue
        const venueToUpdate = venues[editIdx];
        if (venueToUpdate?.id) {
          await venueService.updateVenue(venueToUpdate.id, venueData);
        }
      } else {
        // Create new venue
        await venueService.createVenue(venueData);
      }

      // Reload venues to get updated data
      await loadVenues();
      setShowModal(false);
      resetForm();
    } catch (err) {
      console.error('Error saving venue:', err);
      console.error('Venue data that caused error:', JSON.stringify(venueData, null, 2));
      setError(`Failed to save venue: ${err instanceof Error ? err.message : 'Unknown error'}`);
    } finally {
      setLoading(false);
    }
  };
  const handleDelete = async (idx: number) => {
    const venue = venues[idx];
    if (window.confirm(`Are you sure you want to delete "${venue.name}"? This action cannot be undone.`)) {
      try {
        setLoading(true);
        if (venue.id) {
          await venueService.deleteVenue(venue.id);
          await loadVenues(); // Reload venues
          setSelectedVenues(prev => prev.filter(id => id !== venue.id));
        }
      } catch (err) {
        console.error('Error deleting venue:', err);
        setError('Failed to delete venue. Please try again.');
      } finally {
        setLoading(false);
      }
    }
  };

  const handleBulkDelete = async () => {
    if (selectedVenues.length === 0) return;

    if (window.confirm(`Are you sure you want to delete ${selectedVenues.length} selected venue(s)? This action cannot be undone.`)) {
      try {
        setLoading(true);
        await venueService.bulkDeleteVenues(selectedVenues);
        await loadVenues(); // Reload venues
        setSelectedVenues([]);
      } catch (err) {
        console.error('Error bulk deleting venues:', err);
        setError('Failed to delete venues. Please try again.');
      } finally {
        setLoading(false);
      }
    }
  };

  const handleSelectVenue = (venueId: string) => {
    setSelectedVenues(prev =>
      prev.includes(venueId)
        ? prev.filter(id => id !== venueId)
        : [...prev, venueId]
    );
  };

  const handleSelectAll = () => {
    if (selectedVenues.length === filteredAndSortedVenues.length) {
      setSelectedVenues([]);
    } else {
      setSelectedVenues(filteredAndSortedVenues.map(venue => venue.id!).filter(Boolean));
    }
  };

  const toggleVenueStatus = async (idx: number) => {
    const venue = venues[idx];
    if (!venue.id) return;

    try {
      setLoading(true);
      const newStatus = venue.status === 'active' ? 'inactive' : 'active';
      await venueService.updateVenueStatus(venue.id, newStatus);
      await loadVenues(); // Reload venues
    } catch (err) {
      console.error('Error updating venue status:', err);
      setError('Failed to update venue status. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-8">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 mb-1">Manage Venues</h1>
          <p className="text-gray-500 text-base">{filteredAndSortedVenues.length} of {venues.length} venues{selectedVenues.length > 0 && ` • ${selectedVenues.length} selected`}</p>
        </div>
        <div className="flex gap-2">
          {selectedVenues.length > 0 && (
            <Button variant="outline" onClick={handleBulkDelete} className="text-red-600 hover:text-red-700 hover:bg-red-50">Delete Selected ({selectedVenues.length})</Button>
          )}
          <Button className="bg-primary hover:bg-primary/90" onClick={openAdd}>+ Add Venue</Button>
        </div>
      </div>
      <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-4 mb-6">
        <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
          <div className="relative">
            <input type="text" placeholder="Search venues..." value={searchTerm} onChange={e => setSearchTerm(e.target.value)} className="w-full px-3 py-2 pl-10 border border-gray-200 rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary bg-gray-50" />
            <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">🔍</div>
          </div>
          <select value={filterType} onChange={e => setFilterType(e.target.value)} className="px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary bg-gray-50"><option value="">All Types</option>{venueTypes.map(type => (<option key={type} value={type}>{type}</option>))}</select>
          <select value={filterStatus} onChange={e => setFilterStatus(e.target.value)} className="px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary bg-gray-50"><option value="">All Status</option><option value="active">Active</option><option value="inactive">Inactive</option><option value="draft">Draft</option></select>
          <select value={sortBy} onChange={e => setSortBy(e.target.value)} className="px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary bg-gray-50"><option value="name">Sort by Name</option><option value="bookings">Sort by Bookings</option><option value="rating">Sort by Rating</option><option value="price">Sort by Price</option></select>
        </div>
      </div>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredAndSortedVenues.map((venue) => (
          <Card key={venue.id} className="overflow-hidden rounded-2xl shadow border border-gray-100 bg-white hover:shadow-md transition-shadow">
            <CardContent className="p-6 flex flex-col h-full">
              <div className="flex items-start justify-between mb-2">
                <div className="flex-1">
                  <h2 className="font-bold text-xl text-gray-900 mb-1 line-clamp-1">{venue.name}</h2>
                  <p className="text-primary font-medium text-xs uppercase tracking-wide mb-1">{venue.type}</p>
                </div>
                <div className="flex gap-2">
                  {venue.verified && <Badge className="bg-green-600">Verified</Badge>}
                  {venue.featured && <Badge className="bg-primary">Featured</Badge>}
                </div>
              </div>
              <div className="text-gray-500 text-sm mb-2 flex items-center gap-2"><MapPin className="h-4 w-4 text-primary" />{venue.city}, {venue.state}</div>
              <p className="text-sm text-gray-600 mb-3 line-clamp-2">{venue.description}</p>
              <div className="flex items-center justify-between text-sm mb-4">
                <span className="font-bold text-lg text-primary">{venue.priceRange || venue.price || 'Contact for pricing'}</span>
                <span className="flex items-center gap-1 bg-yellow-50 px-2 py-1 rounded-full ml-2"><Star className="h-4 w-4 fill-yellow-400 text-yellow-400" /><span className="text-sm font-semibold text-gray-900">{venue.rating || "N/A"}</span></span>
              </div>
              <div className="flex gap-2 mt-auto">
                <Button variant="outline" size="sm" onClick={() => openEdit(venues.findIndex(v => v.id === venue.id))} className="flex-1">Edit</Button>
                <Button variant="outline" size="sm" onClick={() => handleDelete(venues.findIndex(v => v.id === venue.id))} className="flex-1">Delete</Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
      {/* Add/Edit Venue Modal */}
      {showModal && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/40 p-4">
          <div className="bg-white rounded-2xl shadow-2xl p-12 w-full max-w-6xl relative max-h-[95vh] overflow-y-auto">
            <button className="absolute top-3 right-3 text-gray-500 hover:text-primary text-2xl" onClick={() => { setShowModal(false); resetForm(); }}>&times;</button>
            <h2 className="text-3xl font-bold mb-8">{editIdx === null ? "Add New Venue" : "Edit Venue"}</h2>
            {/* Progress Bar */}
            <div className="mb-8">
              <Progress 
                value={(modalStep / totalSteps) * 100}
                className="bg-gray-200"
                indicatorClassName="bg-[#800000]"
              />
              <div className="flex justify-between text-sm text-gray-500 mt-2">
                <span>Step {modalStep} of {totalSteps}</span>
                <span>{["Basic Info", "Location & Pricing", "Spaces & Packages", "Images & Contact"][modalStep-1]}</span>
              </div>
            </div>
            <form className="space-y-10" onSubmit={e => { e.preventDefault(); handleSave(); }}>
              {/* Step 1: Basic Information */}
              {modalStep === 1 && (
                <div className="bg-gray-50 p-6 rounded-lg">
                  <h3 className="text-lg font-semibold mb-4">Basic Information</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Venue Name *</label>
                      <input className="border rounded-lg px-3 py-2 w-full focus:ring-2 focus:ring-primary focus:border-transparent" name="name" value={form.name} onChange={handleChange} placeholder="Enter venue name" required />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Venue Type *</label>
                      <select className="border rounded-lg px-3 py-2 w-full focus:ring-2 focus:ring-primary focus:border-transparent" name="type" value={form.type} onChange={handleChange} required>
                        <option value="">Select Venue Type</option>
                        {venueTypes.map(type => (<option key={type} value={type}>{type}</option>))}
                      </select>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Guest Capacity *</label>
                      <input className="border rounded-lg px-3 py-2 w-full focus:ring-2 focus:ring-primary focus:border-transparent" name="capacity" value={form.capacity} onChange={handleChange} placeholder="e.g., 200-500 guests" required />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Total Area</label>
                      <input className="border rounded-lg px-3 py-2 w-full focus:ring-2 focus:ring-primary focus:border-transparent" name="totalArea" value={form.totalArea} onChange={handleChange} placeholder="e.g., 15,000 sq ft" />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Available Rooms</label>
                      <input className="border rounded-lg px-3 py-2 w-full focus:ring-2 focus:ring-primary focus:border-transparent" name="availableRooms" value={form.availableRooms} onChange={handleChange} placeholder="e.g., 20 Rooms" />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Status</label>
                      <select className="border rounded-lg px-3 py-2 w-full focus:ring-2 focus:ring-primary focus:border-transparent" name="status" value={form.status} onChange={handleChange}>
                        <option value="active">Active</option>
                        <option value="inactive">Inactive</option>
                        <option value="draft">Draft</option>
                      </select>
                    </div>
                  </div>
                  <div className="mt-4">
                    <label className="block text-sm font-medium text-gray-700 mb-2">Description *</label>
                    <textarea className="border rounded-lg px-3 py-2 w-full h-24 focus:ring-2 focus:ring-primary focus:border-transparent" name="description" value={form.description} onChange={handleChange} placeholder="Enter detailed venue description" required />
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                    <div className="flex items-center pt-2">
                      <label className="flex items-center gap-2 text-sm">
                        <input type="checkbox" name="featured" checked={form.featured} onChange={handleChange} className="rounded border-gray-300 text-primary focus:ring-primary" />
                        <span className="font-medium">Featured Venue</span>
                      </label>
                    </div>
                    <div className="flex items-center pt-2">
                      <label className="flex items-center gap-2 text-sm">
                        <input type="checkbox" name="verified" checked={form.verified} onChange={handleChange} className="rounded border-gray-300 text-primary focus:ring-primary" />
                        <span className="font-medium">Verified Venue</span>
                      </label>
                    </div>
                  </div>
                </div>
              )}
              {/* Step 2: Location & Pricing */}
              {modalStep === 2 && (
                <div className="bg-gray-50 p-6 rounded-lg">
                  <h3 className="text-lg font-semibold mb-4">Location & Pricing</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Area/Locality *</label>
                      <input className="border rounded-lg px-3 py-2 w-full focus:ring-2 focus:ring-primary focus:border-transparent" name="location" value={form.location} onChange={handleChange} placeholder="e.g., Andheri West" required />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">City *</label>
                      <input className="border rounded-lg px-3 py-2 w-full focus:ring-2 focus:ring-primary focus:border-transparent" name="city" value={form.city} onChange={handleChange} placeholder="e.g., Mumbai" required />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">State *</label>
                      <input className="border rounded-lg px-3 py-2 w-full focus:ring-2 focus:ring-primary focus:border-transparent" name="state" value={form.state} onChange={handleChange} placeholder="e.g., Maharashtra" required />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Starting Price *</label>
                      <input className="border rounded-lg px-3 py-2 w-full focus:ring-2 focus:ring-primary focus:border-transparent" name="price" value={form.price} onChange={handleChange} placeholder="₹2,00,000" required />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Price Range</label>
                      <input className="border rounded-lg px-3 py-2 w-full focus:ring-2 focus:ring-primary focus:border-transparent" name="priceRange" value={form.priceRange} onChange={handleChange} placeholder="₹2,00,000 - ₹5,00,000" />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Full Address</label>
                      <input className="border rounded-lg px-3 py-2 w-full focus:ring-2 focus:ring-primary focus:border-transparent" name="fullAddress" value={form.fullAddress} onChange={handleChange} placeholder="123 Wedding Street, Andheri West, Mumbai - 400058" />
                    </div>
                  </div>
                </div>
              )}
              {/* Step 3: Spaces & Packages */}
              {modalStep === 3 && (
                <div className="bg-gray-50 p-6 rounded-lg">
                  <h3 className="text-lg font-semibold mb-4">Spaces & Packages</h3>
                  <div className="mb-6">
                    <div className="flex justify-between items-center mb-4">
                      <h4 className="text-md font-semibold">Venue Spaces</h4>
                      <Button type="button" onClick={addSpace} className="bg-green-600 hover:bg-green-700">+ Add Space</Button>
                    </div>
                    {form.spaces.length === 0 ? (
                      <p className="text-gray-500 text-center py-4">No spaces added yet. Click "Add Space" to get started.</p>
                    ) : (
                      <div className="space-y-4">
                        {form.spaces.map((space, index) => (
                          <div key={index} className="bg-white p-4 rounded-lg border">
                            <div className="flex justify-between items-center mb-3">
                              <h5 className="font-medium text-gray-800">Space {index + 1}</h5>
                              <Button type="button" onClick={() => removeSpace(index)} variant="outline" size="sm" className="text-red-600 hover:text-red-700">Remove</Button>
                            </div>
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                              <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">Space Name</label>
                                <input className="border rounded-lg px-3 py-2 w-full focus:ring-2 focus:ring-primary focus:border-transparent" value={space.name} onChange={e => updateSpace(index, 'name', e.target.value)} placeholder="e.g., Grand Ballroom" />
                              </div>
                              <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">Capacity</label>
                                <input className="border rounded-lg px-3 py-2 w-full focus:ring-2 focus:ring-primary focus:border-transparent" value={space.capacity} onChange={e => updateSpace(index, 'capacity', e.target.value)} placeholder="e.g., 300-500 guests" />
                              </div>
                              <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">Area</label>
                                <input className="border rounded-lg px-3 py-2 w-full focus:ring-2 focus:ring-primary focus:border-transparent" value={space.area} onChange={e => updateSpace(index, 'area', e.target.value)} placeholder="e.g., 8,000 sq ft" />
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                  <div>
                    <div className="flex justify-between items-center mb-4">
                      <h4 className="text-md font-semibold">Venue Packages</h4>
                      <Button type="button" onClick={addPackage} className="bg-green-600 hover:bg-green-700">+ Add Package</Button>
                    </div>
                    {form.packages.length === 0 ? (
                      <p className="text-gray-500 text-center py-4">No packages added yet. Click "Add Package" to get started.</p>
                    ) : (
                      <div className="space-y-4">
                        {form.packages.map((pkg, index) => (
                          <div key={index} className="bg-white p-4 rounded-lg border">
                            <div className="flex justify-between items-center mb-3">
                              <h5 className="font-medium text-gray-800">Package {index + 1}</h5>
                              <Button type="button" onClick={() => removePackage(index)} variant="outline" size="sm" className="text-red-600 hover:text-red-700">Remove</Button>
                            </div>
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-3 mb-3">
                              <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">Package Name</label>
                                <input className="border rounded-lg px-3 py-2 w-full focus:ring-2 focus:ring-primary focus:border-transparent" value={pkg.name} onChange={e => updatePackage(index, 'name', e.target.value)} placeholder="e.g., Basic Package" />
                              </div>
                              <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">Price</label>
                                <input className="border rounded-lg px-3 py-2 w-full focus:ring-2 focus:ring-primary focus:border-transparent" value={pkg.price} onChange={e => updatePackage(index, 'price', e.target.value)} placeholder="e.g., ₹2,00,000" />
                              </div>
                              <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">Duration</label>
                                <input className="border rounded-lg px-3 py-2 w-full focus:ring-2 focus:ring-primary focus:border-transparent" value={pkg.duration} onChange={e => updatePackage(index, 'duration', e.target.value)} placeholder="e.g., 6 hours" />
                              </div>
                            </div>
                            <div>
                              <label className="block text-sm font-medium text-gray-700 mb-1">Includes (comma separated)</label>
                              <input className="border rounded-lg px-3 py-2 w-full focus:ring-2 focus:ring-primary focus:border-transparent" value={pkg.includes?.join(', ') || ''} onChange={e => updatePackageIncludes(index, e.target.value)} placeholder="e.g., Hall, Catering, Decoration" />
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              )}
              {/* Step 4: Images & Contact */}
              {modalStep === 4 && (
                <div className="bg-gray-50 p-6 rounded-lg">
                  <h3 className="text-lg font-semibold mb-4">Images & Contact</h3>
                  <div className="mb-6">
                    <div className="mb-4">
                      <h4 className="text-md font-semibold mb-2">Main Profile Image</h4>
                      <ImageUpload
                        images={form.mainImage ? [form.mainImage] : []}
                        onImagesChange={images => setForm(f => ({ ...f, mainImage: images[0] || "" }))}
                        maxImages={1}
                        label="Upload Main Venue Image"
                        description="Upload main venue image (JPG, PNG, WebP)"
                        compressForDB={true}
                        showSizeWarning={true}
                      />
                    </div>
                    <div className="mb-4">
                      <h4 className="text-md font-semibold mb-2">Gallery Images</h4>
                      <ImageUpload
                        images={form.images}
                        onImagesChange={images => setForm(f => ({ ...f, images }))}
                        maxImages={15}
                        label="Upload Venue Gallery Images"
                        description="Upload venue images (JPG, PNG, WebP) - Images will be compressed for database storage"
                        compressForDB={true}
                        showSizeWarning={true}
                      />
                    </div>
                    <div className="mt-4 pt-4 border-t border-gray-200">
                      <label className="block text-sm font-medium text-gray-700 mb-2">Or Add Image URLs</label>
                      <div className="flex gap-2">
                        <input className="border rounded-lg px-3 py-2 flex-1 focus:ring-2 focus:ring-primary focus:border-transparent" placeholder="https://example.com/venue-image.jpg" onKeyPress={e => { if (e.key === 'Enter') { const input = e.target as HTMLInputElement; if (input.value.trim()) { const newImages = [...form.images, input.value.trim()]; setForm(f => ({ ...f, images: newImages, mainImage: newImages[0] || "" })); input.value = ''; } } }} />
                        <Button type="button" variant="outline" onClick={e => { const input = (e.target as HTMLElement).previousElementSibling as HTMLInputElement; if (input.value.trim()) { const newImages = [...form.images, input.value.trim()]; setForm(f => ({ ...f, images: newImages, mainImage: newImages[0] || "" })); input.value = ''; } }}>Add URL</Button>
                      </div>
                      <p className="text-xs text-gray-500 mt-1">You can also add images by URL. First image will be the main image.</p>
                    </div>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Contact Phone</label>
                      <input className="border rounded-lg px-3 py-2 w-full focus:ring-2 focus:ring-primary focus:border-transparent" name="contactPhone" value={form.contactPhone} onChange={handleChange} placeholder="+91 8148376909" />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Contact Email</label>
                      <input className="border rounded-lg px-3 py-2 w-full focus:ring-2 focus:ring-primary focus:border-transparent" name="contactEmail" type="email" value={form.contactEmail} onChange={handleChange} placeholder="<EMAIL>" />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Website</label>
                      <input className="border rounded-lg px-3 py-2 w-full focus:ring-2 focus:ring-primary focus:border-transparent" name="website" value={form.website} onChange={handleChange} placeholder="www.venue.com" />
                    </div>
                  </div>
                </div>
              )}
              {/* Navigation Buttons */}
              <div className="flex gap-3 justify-between pt-4 border-t mt-6">
                <Button type="button" variant="outline" onClick={() => { setShowModal(false); resetForm(); }} disabled={loading}>Cancel</Button>
                <div className="flex gap-2 ml-auto">
                  {modalStep > 1 && (
                    <Button type="button" variant="outline" onClick={() => setModalStep(modalStep - 1)} disabled={loading}>Back</Button>
                  )}
                  {modalStep < totalSteps && (
                    <Button type="button" className="bg-primary hover:bg-primary/90" onClick={() => validateStep(modalStep) && setModalStep(modalStep + 1)} disabled={!validateStep(modalStep) || loading}>Next</Button>
                  )}
                  {modalStep === totalSteps && (
                    <Button type="submit" className="bg-primary hover:bg-primary/90" disabled={loading}>
                      {loading ? "Saving..." : (editIdx === null ? "Add Venue" : "Save Changes")}
                    </Button>
                  )}
                </div>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
}