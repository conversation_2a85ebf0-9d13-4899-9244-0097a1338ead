import { generateClient } from 'aws-amplify/api';
import { createVendor, createShop, createVenue } from '../src/graphql/mutations.js';
import { showToast } from '../lib/toast.js';

// Configure Amplify client
const client = generateClient({
  authMode: 'userPool'
});

/**
 * Test data seeding script for BookmyFestive
 * Adds 10 different products across vendor, shop, and venue categories
 */

// Test Shop Products (4 items)
const shopProducts = [
  {
    userId: "test-user-1",
    name: "Designer Bridal Lehenga - Royal Red",
    category: "Bridal Wear",
    price: 45000,
    originalPrice: 55000,
    discount: 18,
    stock: 5,
    sku: "BL-RR-001",
    brand: "Royal Threads",
    featured: true,
    description: "Exquisite handcrafted bridal lehenga with intricate zardozi work and premium silk fabric. Perfect for your special day.",
    features: [
      "Hand-embroidered zardozi work",
      "Premium silk fabric",
      "Includes blouse and dupatta",
      "Custom fitting available",
      "Dry clean only"
    ],
    sizes: ["S", "M", "L", "XL", "XXL"],
    colors: ["Red", "Maroon", "Deep Red"],
    images: [
      "/placeholder-image.jpg",
      "/placeholder-image.jpg"
    ],
    specifications: {
      fabric: "Pure Silk with Zardozi Embroidery",
      work: "Hand Embroidered",
      occasion: "Wedding, Reception",
      care: "Dry Clean Only",
      delivery: "7-10 business days",
      returnPolicy: "15 days return policy"
    },
    rating: 4.8,
    reviewCount: 24,
    inStock: true,
    status: "active"
  },
  {
    userId: "test-user-1",
    name: "Groom's Sherwani Set - Golden Elegance",
    category: "Groom Wear",
    price: 25000,
    originalPrice: 30000,
    discount: 17,
    stock: 8,
    sku: "GS-GE-002",
    brand: "Maharaja Collection",
    featured: true,
    description: "Elegant golden sherwani with matching churidar and dupatta. Crafted with premium brocade fabric.",
    features: [
      "Premium brocade fabric",
      "Includes churidar and dupatta",
      "Traditional collar design",
      "Custom tailoring available",
      "Matching mojaris available"
    ],
    sizes: ["S", "M", "L", "XL", "XXL"],
    colors: ["Golden", "Cream Gold", "Antique Gold"],
    images: [
      "/placeholder-image.jpg",
      "/placeholder-image.jpg"
    ],
    specifications: {
      fabric: "Silk Brocade",
      work: "Machine Embroidered",
      occasion: "Wedding, Engagement",
      care: "Dry Clean Only",
      delivery: "5-7 business days",
      returnPolicy: "10 days return policy"
    },
    rating: 4.6,
    reviewCount: 18,
    inStock: true,
    status: "active"
  },
  {
    userId: "test-user-1",
    name: "Wedding Jewelry Set - Kundan Collection",
    category: "Jewelry",
    price: 35000,
    originalPrice: 42000,
    discount: 17,
    stock: 3,
    sku: "WJ-KC-003",
    brand: "Heritage Jewels",
    featured: false,
    description: "Traditional Kundan jewelry set with necklace, earrings, and maang tikka. Perfect for bridal occasions.",
    features: [
      "Authentic Kundan work",
      "Gold-plated base",
      "Includes necklace, earrings, maang tikka",
      "Comes with jewelry box",
      "Certificate of authenticity"
    ],
    sizes: ["One Size"],
    colors: ["Gold", "Antique Gold"],
    images: [
      "/placeholder-image.jpg",
      "/placeholder-image.jpg"
    ],
    specifications: {
      fabric: "Gold Plated Metal",
      work: "Kundan Setting",
      occasion: "Wedding, Festival",
      care: "Store in jewelry box, avoid moisture",
      delivery: "3-5 business days",
      returnPolicy: "7 days return policy"
    },
    rating: 4.9,
    reviewCount: 12,
    inStock: true,
    status: "active"
  },
  {
    userId: "test-user-1",
    name: "Wedding Invitation Cards - Royal Design",
    category: "Invitations",
    price: 150,
    originalPrice: 200,
    discount: 25,
    stock: 500,
    sku: "WI-RD-004",
    brand: "Creative Cards",
    featured: false,
    description: "Elegant wedding invitation cards with royal design and gold foiling. Customizable text and design.",
    features: [
      "Premium cardstock",
      "Gold foil printing",
      "Customizable text",
      "Matching envelopes included",
      "Bulk order discounts available"
    ],
    sizes: ["5x7 inches", "6x8 inches"],
    colors: ["Cream", "Ivory", "Light Pink"],
    images: [
      "/placeholder-image.jpg",
      "/placeholder-image.jpg"
    ],
    specifications: {
      fabric: "Premium Cardstock",
      work: "Digital + Foil Printing",
      occasion: "Wedding Invitation",
      care: "Handle with care",
      delivery: "7-14 business days",
      returnPolicy: "No returns on customized items"
    },
    rating: 4.5,
    reviewCount: 45,
    inStock: true,
    status: "active"
  }
];

// Test Vendor Services (3 items)
const vendorServices = [
  {
    userId: "test-user-2",
    name: "Elegant Moments Photography",
    category: "Wedding Photography",
    description: "Professional wedding photography services with cinematic videography. Capturing your precious moments with artistic excellence.",
    contact: "+91 98765 43210",
    email: "<EMAIL>",
    address: "123 Photography Street",
    city: "Chennai",
    state: "Tamil Nadu",
    pincode: "600001",
    website: "www.elegantmoments.com",
    profilePhoto: "/placeholder-image.jpg",
    gallery: [
      "/placeholder-image.jpg",
      "/placeholder-image.jpg",
      "/placeholder-image.jpg"
    ],
    services: [
      {
        name: "Wedding Photography Package",
        price: "₹75,000",
        description: "Full day wedding coverage with 500+ edited photos"
      },
      {
        name: "Pre-Wedding Shoot",
        price: "₹25,000",
        description: "2-hour outdoor shoot with 50+ edited photos"
      },
      {
        name: "Cinematic Wedding Video",
        price: "₹50,000",
        description: "Professional wedding videography with same-day highlights"
      }
    ],
    socialMedia: {
      facebook: "elegantmomentsphotography",
      instagram: "elegant_moments_photo",
      youtube: "elegantmomentsstudio"
    },
    experience: "8+ years",
    events: "200+ weddings",
    responseTime: "Within 2 hours",
    rating: 4.8,
    reviewCount: 45,
    verified: true,
    featured: true,
    availability: "Available",
    priceRange: "₹25,000 - ₹1,50,000",
    specializations: ["Wedding Photography", "Pre-Wedding Shoots", "Candid Photography"],
    awards: ["Best Wedding Photographer 2023", "Excellence in Photography Award"],
    languages: ["English", "Tamil", "Hindi"],
    coverage: ["Chennai", "Bangalore", "Coimbatore"],
    equipment: ["Canon 5D Mark IV", "Sony A7R IV", "Professional Lighting"],
    status: "active"
  },
  {
    userId: "test-user-2",
    name: "Spice Garden Catering",
    category: "Catering Services",
    description: "Premium catering services specializing in South Indian and North Indian cuisines. From intimate gatherings to grand celebrations.",
    contact: "+91 87654 32109",
    email: "<EMAIL>",
    address: "456 Catering Complex",
    city: "Bangalore",
    state: "Karnataka",
    pincode: "560001",
    website: "www.spicegardencatering.com",
    profilePhoto: "/placeholder-image.jpg",
    gallery: [
      "/placeholder-image.jpg",
      "/placeholder-image.jpg",
      "/placeholder-image.jpg"
    ],
    services: [
      {
        name: "Wedding Feast Package",
        price: "₹800 per person",
        description: "Complete wedding menu with 25+ dishes including desserts"
      },
      {
        name: "Reception Catering",
        price: "₹600 per person",
        description: "Elegant reception menu with live counters"
      },
      {
        name: "Mehendi Function Catering",
        price: "₹400 per person",
        description: "Light snacks and beverages for mehendi ceremony"
      }
    ],
    socialMedia: {
      facebook: "spicegardencatering",
      instagram: "spice_garden_catering",
      youtube: "spicegardenofficial"
    },
    experience: "12+ years",
    events: "500+ events",
    responseTime: "Within 1 hour",
    rating: 4.7,
    reviewCount: 78,
    verified: true,
    featured: true,
    availability: "Available",
    priceRange: "₹400 - ₹1,200 per person",
    specializations: ["South Indian Cuisine", "North Indian Cuisine", "Live Counters"],
    awards: ["Best Catering Service 2022", "Customer Choice Award"],
    languages: ["English", "Kannada", "Tamil", "Hindi"],
    coverage: ["Bangalore", "Mysore", "Chennai"],
    equipment: ["Mobile Kitchen Units", "Serving Equipment", "Decoration Items"],
    status: "active"
  },
  {
    userId: "test-user-2",
    name: "Melodic Beats Entertainment",
    category: "DJ & Music",
    description: "Professional DJ services and live music entertainment for weddings and events. Creating unforgettable musical experiences.",
    contact: "+91 76543 21098",
    email: "<EMAIL>",
    address: "789 Music Street",
    city: "Mumbai",
    state: "Maharashtra",
    pincode: "400001",
    website: "www.melodicbeats.com",
    profilePhoto: "/placeholder-image.jpg",
    gallery: [
      "/placeholder-image.jpg",
      "/placeholder-image.jpg",
      "/placeholder-image.jpg"
    ],
    services: [
      {
        name: "Wedding DJ Package",
        price: "₹35,000",
        description: "Full day DJ services with premium sound system"
      },
      {
        name: "Live Band Performance",
        price: "₹75,000",
        description: "4-piece live band for 3 hours with popular songs"
      },
      {
        name: "Sound & Lighting Setup",
        price: "₹20,000",
        description: "Professional sound system and stage lighting"
      }
    ],
    socialMedia: {
      facebook: "melodicbeatsent",
      instagram: "melodic_beats_dj",
      youtube: "melodicbeatsofficial"
    },
    experience: "6+ years",
    events: "150+ events",
    responseTime: "Within 3 hours",
    rating: 4.6,
    reviewCount: 32,
    verified: true,
    featured: false,
    availability: "Available",
    priceRange: "₹20,000 - ₹1,00,000",
    specializations: ["Wedding DJ", "Live Music", "Sound Engineering"],
    awards: ["Best DJ Service 2023"],
    languages: ["English", "Hindi", "Marathi"],
    coverage: ["Mumbai", "Pune", "Nashik"],
    equipment: ["Pioneer DJ Equipment", "JBL Sound System", "LED Lighting"],
    status: "active"
  }
];

// Test Venue Listings (3 items)
const venueListings = [
  {
    userId: "test-user-3",
    name: "Grand Palace Banquet Hall",
    description: "Luxurious banquet hall perfect for grand wedding celebrations. Features elegant interiors, spacious halls, and premium amenities.",
    type: "Banquet Hall",
    capacity: 500,
    location: "Anna Nagar",
    city: "Chennai",
    state: "Tamil Nadu",
    fullAddress: "123 Grand Palace Complex, Anna Nagar, Chennai - 600040",
    pincode: "600040",
    contactPhone: "+91 98765 12345",
    contactEmail: "<EMAIL>",
    website: "www.grandpalacebanquet.com",
    price: 150000,
    priceRange: "₹1,00,000 - ₹2,50,000",
    images: [
      "/placeholder-image.jpg",
      "/placeholder-image.jpg",
      "/placeholder-image.jpg"
    ],
    amenities: [
      "Air Conditioning",
      "Parking for 200 cars",
      "Bridal Room",
      "Catering Kitchen",
      "Sound System",
      "Stage & Lighting",
      "Generator Backup",
      "Valet Parking"
    ],
    spaces: [
      {
        name: "Main Hall",
        capacity: 500,
        area: "5000 sq ft",
        price: 100000,
        description: "Spacious main hall with elegant chandeliers",
        amenities: ["AC", "Sound System", "Stage"],
        images: ["/placeholder-image.jpg"]
      },
      {
        name: "Pre-Function Area",
        capacity: 100,
        area: "1000 sq ft",
        price: 25000,
        description: "Perfect for cocktail reception",
        amenities: ["AC", "Bar Counter"],
        images: ["/placeholder-image.jpg"]
      }
    ],
    packages: [
      {
        name: "Premium Wedding Package",
        price: 200000,
        duration: "Full Day",
        description: "Complete wedding package with decoration and catering",
        includes: ["Venue", "Decoration", "Catering", "Sound System"],
        excludes: ["Photography", "Transportation"],
        terms: "50% advance required"
      },
      {
        name: "Reception Package",
        price: 120000,
        duration: "4 Hours",
        description: "Evening reception package",
        includes: ["Venue", "Basic Decoration", "Sound System"],
        excludes: ["Catering", "Photography"],
        terms: "30% advance required"
      }
    ],
    socialMedia: {
      facebook: "grandpalacebanquet",
      instagram: "grand_palace_venue",
      youtube: "grandpalaceofficial"
    },
    rating: 4.7,
    reviewCount: 56,
    bookings: 120,
    verified: true,
    featured: true,
    status: "active",
    availability: "Available",
    policies: {
      cancellation: "48 hours notice required",
      advance: "50% advance payment required",
      catering: "Outside catering allowed with permission",
      decoration: "Decoration team available",
      alcohol: "Alcohol permitted with license",
      music: "Music allowed till 11 PM",
      parking: "Complimentary valet parking"
    },
    coordinates: {
      latitude: 13.0827,
      longitude: 80.2707
    },
    operatingHours: {
      monday: "9 AM - 11 PM",
      tuesday: "9 AM - 11 PM",
      wednesday: "9 AM - 11 PM",
      thursday: "9 AM - 11 PM",
      friday: "9 AM - 11 PM",
      saturday: "9 AM - 11 PM",
      sunday: "9 AM - 11 PM"
    }
  },
  {
    userId: "test-user-3",
    name: "Seaside Resort & Convention Center",
    description: "Beautiful beachside resort perfect for destination weddings. Offers stunning ocean views and luxury accommodations.",
    type: "Resort",
    capacity: 300,
    location: "ECR",
    city: "Chennai",
    state: "Tamil Nadu",
    fullAddress: "456 East Coast Road, Mahabalipuram, Chennai - 603104",
    pincode: "603104",
    contactPhone: "+91 87654 32109",
    contactEmail: "<EMAIL>",
    website: "www.seasideresort.com",
    price: 200000,
    priceRange: "₹1,50,000 - ₹3,50,000",
    images: [
      "/placeholder-image.jpg",
      "/placeholder-image.jpg",
      "/placeholder-image.jpg"
    ],
    amenities: [
      "Beach Access",
      "Swimming Pool",
      "Spa Services",
      "Guest Rooms",
      "Restaurant",
      "Bar",
      "Garden Area",
      "Water Sports"
    ],
    spaces: [
      {
        name: "Beachside Pavilion",
        capacity: 200,
        area: "3000 sq ft",
        price: 120000,
        description: "Open-air pavilion with ocean views",
        amenities: ["Ocean View", "Natural Lighting", "Beach Access"],
        images: ["/placeholder-image.jpg"]
      },
      {
        name: "Garden Lawn",
        capacity: 150,
        area: "4000 sq ft",
        price: 80000,
        description: "Lush green lawn perfect for outdoor ceremonies",
        amenities: ["Garden Setting", "Natural Ambiance"],
        images: ["/placeholder-image.jpg"]
      }
    ],
    packages: [
      {
        name: "Destination Wedding Package",
        price: 300000,
        duration: "2 Days",
        description: "Complete destination wedding with accommodation",
        includes: ["Venue", "Accommodation", "Meals", "Decoration"],
        excludes: ["Transportation", "Photography"],
        terms: "60% advance required"
      }
    ],
    socialMedia: {
      facebook: "seasideresortchennai",
      instagram: "seaside_resort_ecr",
      youtube: "seasideresortofficial"
    },
    rating: 4.8,
    reviewCount: 34,
    bookings: 45,
    verified: true,
    featured: true,
    status: "active",
    availability: "Available",
    policies: {
      cancellation: "72 hours notice required",
      advance: "60% advance payment required",
      catering: "In-house catering only",
      decoration: "Decoration team available",
      alcohol: "Alcohol permitted",
      music: "Music allowed till 12 AM",
      parking: "Complimentary parking available"
    },
    coordinates: {
      latitude: 12.7767,
      longitude: 80.1962
    },
    operatingHours: {
      monday: "24 Hours",
      tuesday: "24 Hours",
      wednesday: "24 Hours",
      thursday: "24 Hours",
      friday: "24 Hours",
      saturday: "24 Hours",
      sunday: "24 Hours"
    }
  },
  {
    userId: "test-user-3",
    name: "Heritage Palace Hotel",
    description: "Majestic heritage hotel with royal architecture. Perfect for traditional and royal-themed weddings.",
    type: "Heritage Hotel",
    capacity: 400,
    location: "Rajaji Nagar",
    city: "Bangalore",
    state: "Karnataka",
    fullAddress: "789 Heritage Complex, Rajaji Nagar, Bangalore - 560010",
    pincode: "560010",
    contactPhone: "+91 76543 21098",
    contactEmail: "<EMAIL>",
    website: "www.heritagepalacehotel.com",
    price: 180000,
    priceRange: "₹1,20,000 - ₹3,00,000",
    images: [
      "/placeholder-image.jpg",
      "/placeholder-image.jpg",
      "/placeholder-image.jpg"
    ],
    amenities: [
      "Royal Architecture",
      "Heritage Decor",
      "Luxury Suites",
      "Fine Dining",
      "Spa & Wellness",
      "Courtyard",
      "Royal Gardens",
      "Vintage Cars"
    ],
    spaces: [
      {
        name: "Royal Durbar Hall",
        capacity: 300,
        area: "4000 sq ft",
        price: 150000,
        description: "Grand hall with royal architecture and chandeliers",
        amenities: ["Royal Decor", "Chandeliers", "Stage"],
        images: ["/placeholder-image.jpg"]
      },
      {
        name: "Palace Courtyard",
        capacity: 200,
        area: "3000 sq ft",
        price: 100000,
        description: "Beautiful courtyard with traditional architecture",
        amenities: ["Open Air", "Traditional Decor"],
        images: ["/placeholder-image.jpg"]
      }
    ],
    packages: [
      {
        name: "Royal Wedding Package",
        price: 250000,
        duration: "Full Day",
        description: "Royal themed wedding with traditional elements",
        includes: ["Venue", "Royal Decoration", "Traditional Music"],
        excludes: ["Catering", "Photography"],
        terms: "50% advance required"
      }
    ],
    socialMedia: {
      facebook: "heritagepalacehotel",
      instagram: "heritage_palace_blr",
      youtube: "heritagepalaceofficial"
    },
    rating: 4.9,
    reviewCount: 28,
    bookings: 67,
    verified: true,
    featured: false,
    status: "active",
    availability: "Available",
    policies: {
      cancellation: "48 hours notice required",
      advance: "50% advance payment required",
      catering: "In-house catering preferred",
      decoration: "Traditional decoration available",
      alcohol: "Alcohol permitted with license",
      music: "Traditional music encouraged",
      parking: "Valet parking available"
    },
    coordinates: {
      latitude: 12.9716,
      longitude: 77.5946
    },
    operatingHours: {
      monday: "24 Hours",
      tuesday: "24 Hours",
      wednesday: "24 Hours",
      thursday: "24 Hours",
      friday: "24 Hours",
      saturday: "24 Hours",
      sunday: "24 Hours"
    }
  }
];

/**
 * Seed shop products
 */
async function seedShopProducts() {
  console.log('🛍️ Seeding shop products...');
  const results = [];

  for (const product of shopProducts) {
    try {
      const result = await client.graphql({
        query: createShop,
        variables: { input: product }
      });

      results.push({
        success: true,
        type: 'shop',
        name: product.name,
        id: result.data.createShop.id
      });

      console.log(`✅ Created shop product: ${product.name}`);
    } catch (error) {
      console.error(`❌ Failed to create shop product ${product.name}:`, error);
      results.push({
        success: false,
        type: 'shop',
        name: product.name,
        error: error.message
      });
    }
  }

  return results;
}

/**
 * Seed vendor services
 */
async function seedVendorServices() {
  console.log('🎯 Seeding vendor services...');
  const results = [];

  for (const vendor of vendorServices) {
    try {
      const result = await client.graphql({
        query: createVendor,
        variables: { input: vendor }
      });

      results.push({
        success: true,
        type: 'vendor',
        name: vendor.name,
        id: result.data.createVendor.id
      });

      console.log(`✅ Created vendor service: ${vendor.name}`);
    } catch (error) {
      console.error(`❌ Failed to create vendor service ${vendor.name}:`, error);
      results.push({
        success: false,
        type: 'vendor',
        name: vendor.name,
        error: error.message
      });
    }
  }

  return results;
}

/**
 * Seed venue listings
 */
async function seedVenueListings() {
  console.log('🏛️ Seeding venue listings...');
  const results = [];

  for (const venue of venueListings) {
    try {
      const result = await client.graphql({
        query: createVenue,
        variables: { input: venue }
      });

      results.push({
        success: true,
        type: 'venue',
        name: venue.name,
        id: result.data.createVenue.id
      });

      console.log(`✅ Created venue listing: ${venue.name}`);
    } catch (error) {
      console.error(`❌ Failed to create venue listing ${venue.name}:`, error);
      results.push({
        success: false,
        type: 'venue',
        name: venue.name,
        error: error.message
      });
    }
  }

  return results;
}

/**
 * Main seeding function
 */
async function seedAllTestData() {
  console.log('🚀 Starting test data seeding for BookmyFestive...\n');

  const startTime = Date.now();
  const allResults = [];

  try {
    // Seed shop products
    const shopResults = await seedShopProducts();
    allResults.push(...shopResults);

    console.log('\n');

    // Seed vendor services
    const vendorResults = await seedVendorServices();
    allResults.push(...vendorResults);

    console.log('\n');

    // Seed venue listings
    const venueResults = await seedVenueListings();
    allResults.push(...venueResults);

    // Generate summary
    const endTime = Date.now();
    const duration = (endTime - startTime) / 1000;

    const summary = {
      total: allResults.length,
      successful: allResults.filter(r => r.success).length,
      failed: allResults.filter(r => r.success === false).length,
      byType: {
        shop: allResults.filter(r => r.type === 'shop').length,
        vendor: allResults.filter(r => r.type === 'vendor').length,
        venue: allResults.filter(r => r.type === 'venue').length
      },
      duration: `${duration}s`
    };

    console.log('\n📊 SEEDING SUMMARY:');
    console.log('==================');
    console.log(`Total items: ${summary.total}`);
    console.log(`Successful: ${summary.successful}`);
    console.log(`Failed: ${summary.failed}`);
    console.log(`Shop products: ${summary.byType.shop}`);
    console.log(`Vendor services: ${summary.byType.vendor}`);
    console.log(`Venue listings: ${summary.byType.venue}`);
    console.log(`Duration: ${summary.duration}`);

    if (summary.failed > 0) {
      console.log('\n❌ FAILED ITEMS:');
      allResults.filter(r => !r.success).forEach(item => {
        console.log(`- ${item.type}: ${item.name} - ${item.error}`);
      });
    }

    console.log('\n🎉 Test data seeding completed!');

    return {
      success: summary.failed === 0,
      summary,
      results: allResults
    };

  } catch (error) {
    console.error('💥 Fatal error during seeding:', error);
    return {
      success: false,
      error: error.message,
      results: allResults
    };
  }
}

// Export functions for use in other scripts
export {
  seedShopProducts,
  seedVendorServices,
  seedVenueListings,
  seedAllTestData,
  shopProducts,
  vendorServices,
  venueListings
};

// Run seeding if this script is executed directly
if (typeof window === 'undefined' && import.meta.url === `file://${process.argv[1]}`) {
  seedAllTestData()
    .then(result => {
      if (result.success) {
        console.log('✅ All test data seeded successfully!');
        process.exit(0);
      } else {
        console.error('❌ Seeding failed:', result.error);
        process.exit(1);
      }
    })
    .catch(error => {
      console.error('💥 Unexpected error:', error);
      process.exit(1);
    });
}