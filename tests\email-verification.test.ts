/**
 * Comprehensive Email Functionality Verification Tests
 * Tests all 10 email templates and integration points
 */

import { emailService } from '@/lib/services/emailService';
import { useEmailIntegration } from '@/hooks/useEmailIntegration';

describe('Email Template System Verification', () => {
  const testEmail = '<EMAIL>';
  const testUserName = '<PERSON>';

  beforeAll(() => {
    // Mock console.log to avoid spam during tests
    jest.spyOn(console, 'log').mockImplementation(() => {});
    jest.spyOn(console, 'error').mockImplementation(() => {});
  });

  afterAll(() => {
    jest.restoreAllMocks();
  });

  describe('1. Login OTP Email', () => {
    it('should send login OTP email successfully', async () => {
      const result = await emailService.sendLoginOTPEmail({
        email: testEmail,
        userName: testUserName,
        otp: '123456',
        expiryMinutes: 10
      });

      expect(result).toBe(true);
    });

    it('should handle invalid email format', async () => {
      const result = await emailService.sendLoginOTPEmail({
        email: 'invalid-email',
        userName: testUserName,
        otp: '123456',
        expiryMinutes: 10
      });

      expect(result).toBe(false);
    });
  });

  describe('2. Welcome Signup Email', () => {
    it('should send welcome email for regular user', async () => {
      const result = await emailService.sendWelcomeSignupEmail({
        email: testEmail,
        firstName: 'John',
        lastName: 'Doe',
        type: 'signup',
        isVendor: false
      });

      expect(result).toBe(true);
    });

    it('should send welcome email for vendor user', async () => {
      const result = await emailService.sendWelcomeSignupEmail({
        email: testEmail,
        firstName: 'Jane',
        lastName: 'Smith',
        type: 'signup',
        isVendor: true,
        businessName: 'Dream Weddings Studio'
      });

      expect(result).toBe(true);
    });
  });

  describe('3. Newsletter Subscription Email', () => {
    it('should send newsletter welcome email', async () => {
      const result = await emailService.sendNewsletterWelcomeEmail({
        email: testEmail,
        firstName: 'John',
        lastName: 'Doe',
        type: 'newsletter',
        interests: ['photography', 'venues', 'catering'],
        preferences: {
          weddingTips: true,
          vendorRecommendations: true,
          specialOffers: true,
          weeklyNews: true
        }
      });

      expect(result).toBe(true);
    });
  });

  describe('4. Booking Confirmation Email', () => {
    it('should send venue booking confirmation', async () => {
      const result = await emailService.sendBookingConfirmationEmail({
        email: testEmail,
        userName: testUserName,
        bookingId: 'booking_123',
        entityName: 'Grand Palace Hotel',
        entityType: 'venue',
        eventDate: '2024-12-25',
        eventTime: '18:00',
        amount: '₹50,000',
        status: 'confirmed'
      });

      expect(result).toBe(true);
    });

    it('should send vendor booking confirmation', async () => {
      const result = await emailService.sendBookingConfirmationEmail({
        email: testEmail,
        userName: testUserName,
        bookingId: 'booking_456',
        entityName: 'Dream Photography',
        entityType: 'vendor',
        eventDate: '2024-12-25',
        eventTime: '10:00',
        amount: '₹25,000',
        status: 'confirmed'
      });

      expect(result).toBe(true);
    });
  });

  describe('5. Payment Success Email', () => {
    it('should send payment success email with invoice', async () => {
      const result = await emailService.sendPaymentSuccessEmail({
        email: testEmail,
        userName: testUserName,
        orderId: 'order_789',
        amount: '₹15,000',
        items: [
          { name: 'Wedding Dress', quantity: 1, price: '₹10,000' },
          { name: 'Wedding Shoes', quantity: 1, price: '₹5,000' }
        ],
        invoiceUrl: 'https://BookmyFestive.com/invoices/invoice_789.pdf',
        trackingUrl: 'https://BookmyFestive.com/dashboard/orders/order_789'
      });

      expect(result).toBe(true);
    });
  });

  describe('6. Weekly News Email', () => {
    it('should send weekly news email', async () => {
      const result = await emailService.sendWeeklyNewsEmail({
        email: testEmail,
        userName: testUserName,
        articles: [
          {
            title: 'Top 10 Wedding Trends for 2024',
            excerpt: 'Discover the latest wedding trends that are taking 2024 by storm...',
            url: 'https://BookmyFestive.com/blog/wedding-trends-2024',
            image: 'https://BookmyFestive.com/images/trends-2024.jpg'
          },
          {
            title: 'How to Choose the Perfect Wedding Venue',
            excerpt: 'A comprehensive guide to selecting the ideal venue for your special day...',
            url: 'https://BookmyFestive.com/blog/choose-wedding-venue'
          }
        ],
        offers: [
          {
            title: 'Photography Package Discount',
            discount: '20% OFF',
            url: 'https://BookmyFestive.com/offers/photography-discount'
          }
        ]
      });

      expect(result).toBe(true);
    });
  });

  describe('7. Offers Email', () => {
    it('should send offers email', async () => {
      const result = await emailService.sendOffersEmail({
        email: testEmail,
        userName: testUserName,
        offers: [
          {
            title: 'Exclusive Venue Booking Discount',
            description: 'Book your dream venue with our exclusive discount offer',
            discount: '25% OFF',
            validUntil: '2024-12-31',
            url: 'https://BookmyFestive.com/venues?discount=25off',
            image: 'https://BookmyFestive.com/images/venue-offer.jpg'
          },
          {
            title: 'Photography + Videography Combo',
            description: 'Get both photography and videography services at a special price',
            discount: '30% OFF',
            validUntil: '2024-11-30',
            url: 'https://BookmyFestive.com/vendors/photography?combo=true'
          }
        ]
      });

      expect(result).toBe(true);
    });
  });

  describe('8. Favorites Notification Email', () => {
    it('should send favorites notification email', async () => {
      const result = await emailService.sendFavoritesNotificationEmail({
        email: testEmail,
        userName: testUserName,
        updates: [
          {
            name: 'Dream Photography Studio',
            type: 'vendor',
            updateType: 'price_drop',
            url: 'https://BookmyFestive.com/vendors/dream-photography',
            image: 'https://BookmyFestive.com/images/dream-photography.jpg'
          },
          {
            name: 'Royal Palace Banquet',
            type: 'venue',
            updateType: 'new_photos',
            url: 'https://BookmyFestive.com/venues/royal-palace-banquet'
          },
          {
            name: 'Elegant Decorations',
            type: 'vendor',
            updateType: 'availability',
            url: 'https://BookmyFestive.com/vendors/elegant-decorations'
          }
        ]
      });

      expect(result).toBe(true);
    });
  });

  describe('9. Vendor Launch Email', () => {
    it('should send vendor launch email', async () => {
      const result = await emailService.sendVendorLaunchEmail({
        email: testEmail,
        userName: testUserName,
        newVendors: [
          {
            name: 'Artistic Wedding Films',
            category: 'Videography',
            city: 'Mumbai',
            discount: '20% OFF',
            url: 'https://BookmyFestive.com/vendors/artistic-wedding-films',
            image: 'https://BookmyFestive.com/images/artistic-films.jpg'
          },
          {
            name: 'Floral Paradise',
            category: 'Decoration',
            city: 'Delhi',
            discount: '15% OFF',
            url: 'https://BookmyFestive.com/vendors/floral-paradise'
          }
        ]
      });

      expect(result).toBe(true);
    });
  });

  describe('10. Availability Check Email', () => {
    it('should send availability check email - available', async () => {
      const result = await emailService.sendAvailabilityCheckEmail({
        email: testEmail,
        userName: testUserName,
        entityName: 'Grand Palace Hotel',
        entityType: 'venue',
        requestedDate: '2024-12-25',
        status: 'available',
        url: 'https://BookmyFestive.com/venues/grand-palace-hotel'
      });

      expect(result).toBe(true);
    });

    it('should send availability check email - unavailable with alternatives', async () => {
      const result = await emailService.sendAvailabilityCheckEmail({
        email: testEmail,
        userName: testUserName,
        entityName: 'Dream Photography',
        entityType: 'vendor',
        requestedDate: '2024-12-25',
        status: 'unavailable',
        alternativeDates: ['2024-12-26', '2024-12-27', '2024-12-28'],
        url: 'https://BookmyFestive.com/vendors/dream-photography'
      });

      expect(result).toBe(true);
    });
  });

  describe('Email Integration Hook Tests', () => {
    it('should provide all 10 email methods', () => {
      const emailIntegration = useEmailIntegration();

      expect(typeof emailIntegration.sendLoginOTP).toBe('function');
      expect(typeof emailIntegration.sendWelcomeEmail).toBe('function');
      expect(typeof emailIntegration.sendNewsletterWelcome).toBe('function');
      expect(typeof emailIntegration.sendBookingConfirmation).toBe('function');
      expect(typeof emailIntegration.sendPaymentSuccess).toBe('function');
      expect(typeof emailIntegration.sendWeeklyNews).toBe('function');
      expect(typeof emailIntegration.sendOffers).toBe('function');
      expect(typeof emailIntegration.sendFavoritesNotification).toBe('function');
      expect(typeof emailIntegration.sendVendorLaunch).toBe('function');
      expect(typeof emailIntegration.sendAvailabilityCheck).toBe('function');
    });
  });

  describe('Bulk Email Functionality', () => {
    it('should send bulk emails to multiple recipients', async () => {
      const recipients = [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>'
      ];

      const templateData = {
        userName: 'Valued Customer',
        articles: [
          {
            title: 'Bulk Email Test',
            excerpt: 'Testing bulk email functionality',
            url: 'https://BookmyFestive.com/test'
          }
        ]
      };

      const result = await emailService.sendBulkEmails('WEEKLY_NEWS', recipients, templateData);
      expect(result).toBe(true);
    });
  });

  describe('Error Handling Tests', () => {
    it('should handle network errors gracefully', async () => {
      // Mock network failure
      const originalFetch = global.fetch;
      global.fetch = jest.fn().mockRejectedValue(new Error('Network error'));

      const result = await emailService.sendLoginOTPEmail({
        email: testEmail,
        userName: testUserName,
        otp: '123456',
        expiryMinutes: 10
      });

      expect(result).toBe(false);

      // Restore original fetch
      global.fetch = originalFetch;
    });

    it('should handle invalid template data', async () => {
      const result = await emailService.sendBookingConfirmationEmail({
        email: testEmail,
        userName: '',
        bookingId: '',
        entityName: '',
        entityType: 'venue',
        eventDate: '',
        status: 'confirmed'
      });

      // Should still attempt to send even with empty data
      expect(typeof result).toBe('boolean');
    });
  });
});
