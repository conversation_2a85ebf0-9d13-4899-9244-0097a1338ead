import { generateClient } from 'aws-amplify/api';
import { getCurrentUser } from 'aws-amplify/auth';
import { createUserProfile } from '@/src/graphql/mutations';
import { userProfilesByUserId } from '@/src/graphql/queries';

const client = generateClient();

export interface AutoCreateProfileData {
  firstName?: string;
  lastName?: string;
  email?: string;
  phone?: string;
  isVendor?: boolean;
  businessInfo?: {
    businessName?: string;
    businessType?: string;
    businessEmail?: string;
    businessPhone?: string;
  };
}

/**
 * Automatically creates a UserProfile entry when a user signs up
 * This should be called after successful Cognito signup/confirmation
 */
export async function autoCreateUserProfile(profileData?: AutoCreateProfileData): Promise<any> {
  try {
    // Get current authenticated user
    const user = await getCurrentUser();
    
    if (!user) {
      throw new Error('No authenticated user found');
    }

    // Check if profile already exists
    const existingProfile = await checkExistingProfile(user.userId);
    
    if (existingProfile) {
      console.log('UserProfile already exists for user:', user.userId);
      return existingProfile;
    }

    // Get user attributes
    const userAttributes = user.signInDetails?.loginId ? { email: user.signInDetails.loginId } : {};
    
    // Create profile data
    const profileInput = {
      userId: user.userId,
      firstName: profileData?.firstName || '',
      lastName: profileData?.lastName || '',
      email: profileData?.email || userAttributes.email || '',
      phone: profileData?.phone || '',
      country: 'India',
      isVendor: profileData?.isVendor || false,
      isAdmin: false,
      isSuperAdmin: false,
      role: profileData?.isVendor ? 'VENDOR' : 'CUSTOMER',
      accountType: profileData?.isVendor ? 'BUSINESS' : 'PERSONAL',
      registrationSource: 'CUSTOMER_FORM',
      isVerified: true, // Since they've confirmed their email
      preferences: {
        language: 'en',
        currency: 'INR',
        timezone: 'Asia/Kolkata',
        notifications: {
          email: true,
          sms: true,
          push: true,
          marketing: false
        },
        privacy: {
          profileVisibility: 'PUBLIC',
          contactVisibility: 'PUBLIC',
          showOnlineStatus: true
        }
      },
      businessInfo: profileData?.businessInfo,
      lastLoginAt: new Date().toISOString()
    };

    // Create the profile
    const result = await client.graphql({
      query: createUserProfile,
      variables: { input: profileInput }
    });

    console.log('Successfully created UserProfile for user:', user.userId);
    return result.data?.createUserProfile;

  } catch (error) {
    console.error('Error auto-creating UserProfile:', error);
    // Don't throw error to avoid blocking user flow
    return null;
  }
}

/**
 * Check if a UserProfile already exists for the given userId
 */
async function checkExistingProfile(userId: string): Promise<any> {
  try {
    const result = await client.graphql({
      query: userProfilesByUserId,
      variables: { userId, limit: 1 }
    });

    const profiles = result.data?.userProfilesByUserId?.items || [];
    return profiles.length > 0 ? profiles[0] : null;
  } catch (error) {
    console.error('Error checking existing profile:', error);
    return null;
  }
}

/**
 * Hook to automatically create profile after signup
 * Call this in your signup success handler
 */
export function useAutoCreateProfile() {
  const createProfile = async (profileData?: AutoCreateProfileData) => {
    return await autoCreateUserProfile(profileData);
  };

  return { createProfile };
}

/**
 * Utility to ensure user has a profile
 * Call this when user logs in to ensure they have a profile
 */
export async function ensureUserProfile(): Promise<any> {
  try {
    const user = await getCurrentUser();
    
    if (!user) {
      return null;
    }

    // Check if profile exists
    const existingProfile = await checkExistingProfile(user.userId);
    
    if (existingProfile) {
      // Update last login time
      return existingProfile;
    }

    // Create profile if it doesn't exist
    return await autoCreateUserProfile();
    
  } catch (error) {
    console.error('Error ensuring user profile:', error);
    return null;
  }
}

/**
 * Create profile for business users with additional business info
 */
export async function createBusinessUserProfile(businessData: {
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  businessName: string;
  businessType: string;
  businessEmail?: string;
  businessPhone?: string;
}): Promise<any> {
  return await autoCreateUserProfile({
    firstName: businessData.firstName,
    lastName: businessData.lastName,
    email: businessData.email,
    phone: businessData.phone,
    isVendor: true,
    businessInfo: {
      businessName: businessData.businessName,
      businessType: businessData.businessType,
      businessEmail: businessData.businessEmail || businessData.email,
      businessPhone: businessData.businessPhone || businessData.phone
    }
  });
}

/**
 * Create profile for regular users
 */
export async function createRegularUserProfile(userData: {
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
}): Promise<any> {
  return await autoCreateUserProfile({
    firstName: userData.firstName,
    lastName: userData.lastName,
    email: userData.email,
    phone: userData.phone,
    isVendor: false
  });
}
