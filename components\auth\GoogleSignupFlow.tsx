"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { 
  Mail, 
  Lock, 
  Eye, 
  EyeOff, 
  CheckCircle, 
  Clock,
  AlertCircle,
  ArrowLeft
} from "lucide-react"
import GoogleAuthService from "@/lib/services/googleAuthService"

interface GoogleSignupFlowProps {
  onComplete?: () => void
}

export default function GoogleSignupFlow({ onComplete }: GoogleSignupFlowProps) {
  const router = useRouter()
  
  const [step, setStep] = useState<'otp' | 'password' | 'complete'>('otp')
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const [success, setSuccess] = useState('')
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  const [resendTimer, setResendTimer] = useState(0)
  
  const [formData, setFormData] = useState({
    otp: '',
    password: '',
    confirmPassword: ''
  })

  const [signupData, setSignupData] = useState<any>(null)

  useEffect(() => {
    // Get signup data from localStorage
    const data = GoogleAuthService.getGoogleSignupStatus()
    if (!data) {
      router.push('/login')
      return
    }
    setSignupData(data)

    // Determine current step based on signup data
    if (data.requiresOTP && !data.otpVerified) {
      setStep('otp')
      setResendTimer(60) // Start with 60 second timer for resend
    } else if (data.requiresPassword) {
      setStep('password')
    } else {
      setStep('complete')
    }
  }, [router])

  useEffect(() => {
    // Start resend timer for OTP
    if (step === 'otp' && resendTimer > 0) {
      const timer = setTimeout(() => setResendTimer(resendTimer - 1), 1000)
      return () => clearTimeout(timer)
    }
  }, [step, resendTimer])

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    setError('')
  }

  const validatePassword = () => {
    if (formData.password.length < 8) {
      setError('Password must be at least 8 characters long')
      return false
    }
    if (formData.password !== formData.confirmPassword) {
      setError('Passwords do not match')
      return false
    }
    return true
  }

  const handleOTPVerification = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (formData.otp.length !== 6) {
      setError('Please enter a valid 6-digit OTP')
      return
    }

    setLoading(true)
    setError('')

    try {
      const result = await GoogleAuthService.completeGoogleSignupOTP(
        signupData.googleUser.email, 
        formData.otp
      )

      if (result.success) {
        setSuccess('OTP verified successfully!')
        setStep('password')
        setFormData(prev => ({ ...prev, otp: '' }))
      } else {
        setError(result.error || 'OTP verification failed')
      }
    } catch (error: any) {
      setError(error.message || 'OTP verification failed')
    } finally {
      setLoading(false)
    }
  }

  const handlePasswordSetup = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validatePassword()) return

    setLoading(true)
    setError('')

    try {
      const result = await GoogleAuthService.completeGoogleSignupPassword(
        signupData.googleUser.email,
        formData.password
      )

      if (result.success) {
        setSuccess(result.message || 'Signup completed successfully!')
        setStep('complete')
        
        // Redirect to dashboard after a short delay
        setTimeout(() => {
          onComplete?.()
          router.push(result.redirectTo || '/dashboard')
        })
      } else {
        setError(result.error || 'Password setup failed')
      }
    } catch (error: any) {
      setError(error.message || 'Password setup failed')
    } finally {
      setLoading(false)
    }
  }

  const handleResendOTP = async () => {
    if (!signupData?.googleUser?.email) return

    setLoading(true)
    setError('')

    try {
      const result = await GoogleAuthService.resendGoogleSignupOTP(signupData.googleUser.email)

      if (result.success) {
        setSuccess('Verification code sent successfully!')
        setResendTimer(60)
        // Clear success message after 3 seconds
        setTimeout(() => setSuccess(''), 3000)
      } else {
        setError(result.error || 'Failed to resend verification code')
      }
    } catch (error: any) {
      setError(error.message || 'Failed to resend verification code')
    } finally {
      setLoading(false)
    }
  }

  if (!signupData) {
    return (
      <div className="flex items-center justify-center">
        <div className="text-center">
          <Clock className="h-8 w-8 animate-spin mx-auto mb-4 text-primary" />
          <p>Loading signup flow...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="bg-gray-50 flex items-center justify-center">
      <div className="max-w-md w-full space-y-8">
        <Card className="shadow-lg">
          <CardHeader className="text-center">
            <div className="flex items-center justify-between mb-4">
              {step !== 'otp' && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => {
                    if (step === 'password') setStep('otp')
                    setError('')
                  }}
                  className="p-2 hover:bg-gray-100"
                >
                  <ArrowLeft className="w-4 h-4" />
                </Button>
              )}
              <div className="flex-1">
                <CardTitle className="text-2xl font-bold text-gray-900">
                  {step === 'otp' && 'Verify Your Email'}
                  {step === 'password' && 'Set Your Password'}
                  {step === 'complete' && 'Welcome to BookmyFestive!'}
                </CardTitle>
              </div>
            </div>
            
            <CardDescription>
              {step === 'otp' && `We've sent a verification code to ${signupData.googleUser.email}`}
              {step === 'password' && 'Create a secure password for your account'}
              {step === 'complete' && 'Your account has been created successfully!'}
            </CardDescription>
          </CardHeader>

          <CardContent className="space-y-6">
            {/* Error Alert */}
            {error && (
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}

            {/* Success Alert */}
            {success && (
              <Alert className="border-green-200 bg-green-50">
                <CheckCircle className="h-4 w-4 text-green-600" />
                <AlertDescription className="text-green-800">{success}</AlertDescription>
              </Alert>
            )}

            {/* OTP Verification Step */}
            {step === 'otp' && (
              <form onSubmit={handleOTPVerification} className="space-y-4">
                <div className="space-y-2">
                  <label htmlFor="otp" className="text-sm font-medium text-gray-700">
                    Verification Code
                  </label>
                  <div className="relative">
                    <Mail className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                    <Input
                      id="otp"
                      name="otp"
                      type="text"
                      placeholder="Enter 6-digit code"
                      className="pl-10 text-center text-lg tracking-widest"
                      value={formData.otp}
                      onChange={(e) => handleInputChange('otp', e.target.value.replace(/\D/g, '').slice(0, 6))}
                      maxLength={6}
                      required
                    />
                  </div>
                </div>

                <Button
                  type="submit"
                  className="w-full"
                  disabled={loading || formData.otp.length !== 6}
                >
                  {loading ? 'Verifying...' : 'Verify Code'}
                </Button>

                <div className="text-center">
                  {resendTimer > 0 ? (
                    <p className="text-sm text-gray-600">
                      Resend code in {resendTimer}s
                    </p>
                  ) : (
                    <Button
                      type="button"
                      variant="ghost"
                      onClick={handleResendOTP}
                      disabled={loading}
                      className="text-sm"
                    >
                      Resend verification code
                    </Button>
                  )}
                </div>
              </form>
            )}

            {/* Password Setup Step */}
            {step === 'password' && (
              <form onSubmit={handlePasswordSetup} className="space-y-4">
                <div className="space-y-2">
                  <label htmlFor="password" className="text-sm font-medium text-gray-700">
                    Password
                  </label>
                  <div className="relative">
                    <Lock className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                    <Input
                      id="password"
                      name="password"
                      type={showPassword ? "text" : "password"}
                      placeholder="Create a password (min 8 characters)"
                      className="pl-10 pr-10"
                      value={formData.password}
                      onChange={(e) => handleInputChange('password', e.target.value)}
                      required
                    />
                    <button
                      type="button"
                      onClick={() => setShowPassword(!showPassword)}
                      className="absolute right-3 top-3 text-gray-400 hover:text-gray-600"
                    >
                      {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                    </button>
                  </div>
                </div>

                <div className="space-y-2">
                  <label htmlFor="confirmPassword" className="text-sm font-medium text-gray-700">
                    Confirm Password
                  </label>
                  <div className="relative">
                    <Lock className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                    <Input
                      id="confirmPassword"
                      name="confirmPassword"
                      type={showConfirmPassword ? "text" : "password"}
                      placeholder="Confirm your password"
                      className="pl-10 pr-10"
                      value={formData.confirmPassword}
                      onChange={(e) => handleInputChange('confirmPassword', e.target.value)}
                      required
                    />
                    <button
                      type="button"
                      onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                      className="absolute right-3 top-3 text-gray-400 hover:text-gray-600"
                    >
                      {showConfirmPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                    </button>
                  </div>
                </div>

                <Button
                  type="submit"
                  className="w-full"
                  disabled={loading || !formData.password || !formData.confirmPassword}
                >
                  {loading ? 'Setting up...' : 'Complete Setup'}
                </Button>
              </form>
            )}

            {/* Complete Step */}
            {step === 'complete' && (
              <div className="text-center space-y-4">
                <CheckCircle className="h-16 w-16 text-green-500 mx-auto" />
                <p className="text-lg font-medium text-gray-900">
                  Account Created Successfully!
                </p>
                <p className="text-gray-600">
                  Redirecting you to your dashboard...
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
