"use client"

import { <PERSON>Header } from "@/components/top-header"
import { <PERSON><PERSON> } from "@/components/header"
import { Foot<PERSON> } from "@/components/footer"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible"
import { ChevronDown, HelpCircle } from "lucide-react"
import { useState } from "react"
import { useHelpTranslations } from '@/lib/universal-i18n'

export default function FAQPage() {
  const [openItems, setOpenItems] = useState<string[]>([])
  const helpT = useHelpTranslations()

  const faqs = [
    {
      id: "1",
      question: "How do I create an account?",
      answer: "You can create an account by clicking the 'Sign Up' button and following the registration process."
    },
    {
      id: "2", 
      question: "How do I book a vendor?",
      answer: "Browse vendors, select your preferred one, and click 'Book Now' to start the booking process."
    },
    {
      id: "3",
      question: "What payment methods are accepted?",
      answer: "We accept all major credit cards, debit cards, and digital payment methods."
    },
    {
      id: "4",
      question: "Can I cancel my booking?",
      answer: "Yes, you can cancel bookings according to the vendor's cancellation policy."
    },
    {
      id: "5",
      question: "How do I contact customer support?",
      answer: "You can reach us through the contact form, live chat, or phone support."
    }
  ]

  const toggleItem = (id: string) => {
    setOpenItems(prev => 
      prev.includes(id) 
        ? prev.filter(item => item !== id)
        : [...prev, id]
    )
  }

  return (
    <div className="min-h-screen bg-background">
      <TopHeader />
      <Header />
      
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <HelpCircle className="w-16 h-16 mx-auto mb-4 text-primary" />
            <h1 className="text-4xl font-bold mb-4">{helpT.faq?.title || 'Frequently Asked Questions'}</h1>
            <p className="text-xl text-muted-foreground">{helpT.faq?.subtitle || 'Find answers to common questions'}</p>
          </div>

          <div className="max-w-4xl mx-auto space-y-4">
            {faqs.map((faq) => (
              <Card key={faq.id}>
                <Collapsible>
                  <CollapsibleTrigger 
                    className="w-full"
                    onClick={() => toggleItem(faq.id)}
                  >
                    <CardHeader className="flex flex-row items-center justify-between">
                      <CardTitle className="text-left">{faq.question}</CardTitle>
                      <ChevronDown className={`w-5 h-5 transition-transform ${openItems.includes(faq.id) ? 'rotate-180' : ''}`} />
                    </CardHeader>
                  </CollapsibleTrigger>
                  <CollapsibleContent>
                    <CardContent className="pt-0">
                      <p className="text-muted-foreground">{faq.answer}</p>
                    </CardContent>
                  </CollapsibleContent>
                </Collapsible>
              </Card>
            ))}
          </div>
        </div>
      </section>

      <Footer />
    </div>
  )
}