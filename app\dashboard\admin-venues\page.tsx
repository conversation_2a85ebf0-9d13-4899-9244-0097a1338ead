"use client"

import { useState, useEffect } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Checkbox } from '@/components/ui/checkbox';
import { ImageUpload } from '@/components/ui/image-upload';
import {
  MapPin,
  Search,
  Plus,
  Trash2,
  Eye,
  CheckCircle,
  XCircle,
  Star,
  Building,
  Edit,
  Phone,
  Mail
} from 'lucide-react';
import { AdminOnlyRoute } from '@/components/RouteProtection';
import AdminContentService, { ContentFilters } from '@/lib/services/adminContentService';
import toast from 'react-hot-toast';

export default function AdminVenuesPage() {
  const [venues, setVenues] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedVenues, setSelectedVenues] = useState<string[]>([]);
  const [filters, setFilters] = useState<ContentFilters>({});
  const [showVenueDetails, setShowVenueDetails] = useState(false);
  const [selectedVenue, setSelectedVenue] = useState<any>(null);
  const [nextToken, setNextToken] = useState<string | undefined>();
  const [hasMore, setHasMore] = useState(true);
  const [sortBy, setSortBy] = useState('name');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(10);

  // Add state for multi-step form
  const [editForm, setEditForm] = useState<any>(null);
  const [editStep, setEditStep] = useState(1);
  const totalEditSteps = 4;
  const [saving, setSaving] = useState(false);

  useEffect(() => {
    loadVenues();
  }, []);

  useEffect(() => {
    const delayedSearch = setTimeout(() => {
      loadVenues();
    }, 500);
    return () => clearTimeout(delayedSearch);
  }, [searchTerm, filters, sortBy]);

  const loadVenues = async (loadMore: boolean = false, page: number = 1) => {
    try {
      setLoading(true);
      const searchFilters: ContentFilters = {
        ...filters,
        searchTerm: searchTerm || undefined,
      };
      const result = await AdminContentService.getAllVenues(
        searchFilters,
        itemsPerPage,
        loadMore ? nextToken : undefined
      );
      if (result.success && result.data) {
        if (loadMore) {
          setVenues(prev => [...prev, ...result.data!.venues]);
        } else {
          setVenues(result.data.venues);
          setCurrentPage(page);
        }
        setNextToken(result.data.nextToken);
        setHasMore(!!result.data.nextToken);
      } else {
        toast.error(result.error || 'Failed to load venues');
      }
    } catch (error) {
      console.error('Error loading venues:', error);
      toast.error('Failed to load venues');
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteVenue = async (venueId: string) => {
    if (!confirm('Are you sure you want to delete this venue? This action cannot be undone.')) {
      return;
    }
    try {
      const result = await AdminContentService.deleteVenue(venueId);
      if (result.success) {
        toast.success('Venue deleted successfully');
        loadVenues();
      } else {
        toast.error(result.error || 'Failed to delete venue');
      }
    } catch (error) {
      console.error('Error deleting venue:', error);
      toast.error('Failed to delete venue');
    }
  };

  const handleBulkDelete = async () => {
    if (selectedVenues.length === 0) return;
    if (!confirm(`Delete ${selectedVenues.length} selected venues?`)) return;
    try {
      const result = await AdminContentService.bulkDelete('venue', selectedVenues);
      if (result.success) {
        toast.success(`${selectedVenues.length} venues deleted successfully`);
        setSelectedVenues([]);
        loadVenues();
      } else {
        toast.error(result.error || 'Failed to delete venues');
      }
    } catch (error) {
      console.error('Error deleting venues:', error);
      toast.error('Failed to delete venues');
    }
  };

  const handleSelectAll = () => {
    if (selectedVenues.length === venues.length) {
      setSelectedVenues([]);
    } else {
      setSelectedVenues(venues.map(venue => venue.id));
    }
  };

  const handleVenueSelect = (venueId: string) => {
    setSelectedVenues(prev =>
      prev.includes(venueId)
        ? prev.filter(id => id !== venueId)
        : [...prev, venueId]
    );
  };

  const applyFilters = (key: string, value: any) => {
    setFilters(prev => ({ ...prev, [key]: value === 'all' ? undefined : value }));
    setNextToken(undefined);
  };

  const clearFilters = () => {
    setFilters({});
    setSearchTerm('');
    setSortBy('name');
    setNextToken(undefined);
  };

  // When opening modal, reset step to 1 and prefill form
  const openVenueDetails = (venue: any) => {
    setSelectedVenue(venue);
    setEditForm({
      ...venue,
      spaces: venue.spaces ? [...venue.spaces] : [],
      packages: venue.packages ? [...venue.packages] : [],
      images: venue.images ? [...venue.images] : [],
      amenities: venue.amenities ? [...venue.amenities] : [],
    });
    setEditStep(1);
    setShowVenueDetails(true);
  };

  const handleEditChange = (field: string, value: any) => {
    setEditForm((prev: any) => ({ ...prev, [field]: value }));
  };

  // Spaces/Packages handlers
  const handleSpaceChange = (idx: number, key: string, value: string) => {
    setEditForm((prev: any) => {
      const spaces = [...(prev.spaces || [])];
      spaces[idx] = { ...spaces[idx], [key]: value };
      return { ...prev, spaces };
    });
  };
  const addSpace = () => {
    setEditForm((prev: any) => ({ ...prev, spaces: [...(prev.spaces || []), { name: '', capacity: '', price: '' }] }));
  };
  const removeSpace = (idx: number) => {
    setEditForm((prev: any) => {
      const spaces = [...(prev.spaces || [])];
      spaces.splice(idx, 1);
      return { ...prev, spaces };
    });
  };
  const handlePackageChange = (idx: number, key: string, value: string) => {
    setEditForm((prev: any) => {
      const packages = [...(prev.packages || [])];
      packages[idx] = { ...packages[idx], [key]: value };
      return { ...prev, packages };
    });
  };
  const addPackage = () => {
    setEditForm((prev: any) => ({ ...prev, packages: [...(prev.packages || []), { name: '', price: '', duration: '' }] }));
  };
  const removePackage = (idx: number) => {
    setEditForm((prev: any) => {
      const packages = [...(prev.packages || [])];
      packages.splice(idx, 1);
      return { ...prev, packages };
    });
  };

  // Save handler (sanitize input)
  const handleSaveEdit = async () => {
    if (!editForm) return;
    setSaving(true);
    try {
      // Only send valid fields, remove undefined/null/empty, clean arrays
      const allowedFields = [
        'name', 'category', 'capacity', 'area', 'rooms', 'description', 'featured', 'verified', 'status',
        'locality', 'city', 'state', 'priceRange', 'startingPrice', 'fullAddress', 'images', 'contactPhone', 'contactEmail', 'website', 'spaces', 'packages', 'amenities'
      ];
      const cleaned: any = {};
      for (const key of allowedFields) {
        if (key in editForm && editForm[key] !== undefined && editForm[key] !== null && editForm[key] !== '') {
          if (key === 'spaces' && Array.isArray(editForm.spaces)) {
            cleaned.spaces = editForm.spaces.filter((s: any) => s && s.name && s.name.trim()).map((s: any) => ({ ...s, name: s.name.trim() }));
          } else if (key === 'packages' && Array.isArray(editForm.packages)) {
            cleaned.packages = editForm.packages.filter((p: any) => p && p.name && p.name.trim()).map((p: any) => ({ ...p, name: p.name.trim() }));
          } else if (key === 'images' && Array.isArray(editForm.images)) {
            cleaned.images = editForm.images.filter((img: string) => img && img.trim());
          } else {
            cleaned[key] = editForm[key];
          }
        }
      }
      cleaned.id = editForm.id;
      const result = await AdminContentService.updateVenue(editForm.id, cleaned);
      if (result.success) {
        toast.success('Venue updated successfully');
        setShowVenueDetails(false);
        setEditForm(null);
        loadVenues();
      } else {
        toast.error(result.error || 'Failed to update venue');
      }
    } catch (error) {
      toast.error('Failed to update venue');
    } finally {
      setSaving(false);
    }
  };

  const getStatusBadge = (venue: any) => {
    if (venue.status === 'active') {
      return <Badge className="bg-green-100 text-green-800 text-xs px-2 py-1"><CheckCircle className="w-3 h-3 mr-1" />Active</Badge>;
    }
    return <Badge variant="secondary" className="text-xs px-2 py-1"><XCircle className="w-3 h-3 mr-1" />Inactive</Badge>;
  };
  const getFeaturedBadge = (venue: any) => {
    if (venue.featured) {
      return <Badge className="bg-blue-100 text-blue-800 text-xs px-2 py-1"><Star className="w-3 h-3 mr-1" />Featured</Badge>;
    }
    return null;
  };

  return (
    <AdminOnlyRoute>
      <div className="space-y-4 sm:space-y-8 px-2 sm:px-0">
        {/* Header */}
        <div className="flex flex-col gap-4 mb-4 sm:mb-6">
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-3 sm:gap-4">
            <div>
              <h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold sm:font-extrabold text-gray-900 mb-1 sm:mb-2">Manage Venues</h1>
              <p className="text-gray-500 text-sm sm:text-base lg:text-lg">{venues.length} venue{venues.length !== 1 ? 's' : ''}</p>
            </div>
            <div className="flex flex-col sm:flex-row gap-2 w-full sm:w-auto">
              {selectedVenues.length > 0 && (
                <Button 
                  variant="outline" 
                  onClick={handleBulkDelete} 
                  className="text-red-600 hover:text-red-700 hover:bg-red-50 text-sm"
                >
                  Delete Selected ({selectedVenues.length})
                </Button>
              )}
              <Button className="bg-primary hover:bg-primary/90 text-sm">
                <Plus className="w-4 h-4 mr-2" />
                <span className="hidden sm:inline">Add Venue</span>
                <span className="sm:hidden">Add</span>
              </Button>
            </div>
          </div>
        </div>

        {/* Filters */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-3 sm:p-4 mb-4 sm:mb-6">
          <div className="space-y-3 sm:space-y-0 sm:grid sm:grid-cols-1 md:grid-cols-6 gap-3 sm:gap-4">
            <div className="relative">
              <Input 
                type="text" 
                placeholder="Search venues..." 
                value={searchTerm} 
                onChange={e => setSearchTerm(e.target.value)} 
                className="w-full pl-10 text-sm" 
              />
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            </div>
            <Select value={filters.category || 'all'} onValueChange={value => applyFilters('category', value)}>
              <SelectTrigger className="text-sm">
                <SelectValue placeholder="All Categories" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Categories</SelectItem>
                <SelectItem value="Banquet Hall">Banquet Hall</SelectItem>
                <SelectItem value="Resort">Resort</SelectItem>
                <SelectItem value="Hotel">Hotel</SelectItem>
                <SelectItem value="Garden">Garden</SelectItem>
                <SelectItem value="Beach">Beach</SelectItem>
              </SelectContent>
            </Select>
            <Select value={filters.status || 'all'} onValueChange={value => applyFilters('status', value)}>
              <SelectTrigger className="text-sm">
                <SelectValue placeholder="All Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="inactive">Inactive</SelectItem>
                <SelectItem value="pending">Pending</SelectItem>
              </SelectContent>
            </Select>
            <Select value={filters.featured !== undefined ? String(filters.featured) : 'all'} onValueChange={value => applyFilters('featured', value)}>
              <SelectTrigger className="text-sm">
                <SelectValue placeholder="All Venues" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Venues</SelectItem>
                <SelectItem value="true">Featured Only</SelectItem>
                <SelectItem value="false">Non-Featured</SelectItem>
              </SelectContent>
            </Select>
            <Select value={sortBy} onValueChange={value => setSortBy(value)}>
              <SelectTrigger className="text-sm">
                <SelectValue placeholder="Sort by Name" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="name">Sort by Name</SelectItem>
                <SelectItem value="category">Sort by Category</SelectItem>
                <SelectItem value="city">Sort by City</SelectItem>
                <SelectItem value="status">Sort by Status</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Venue Cards Grid */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-6">
          {loading ? (
            <div className="col-span-full text-center py-8 sm:py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
              <p className="mt-2 text-gray-600 text-sm sm:text-base">Loading venues...</p>
            </div>
          ) : venues.length === 0 ? (
            <div className="col-span-full text-center py-8 sm:py-12">
              <MapPin className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-600 text-sm sm:text-base">No venues found</p>
            </div>
          ) : (
            venues.map((venue) => (
              <Card key={venue.id} className="overflow-hidden rounded-xl sm:rounded-2xl shadow border border-gray-100 bg-white hover:shadow-md transition-shadow relative">
                <CardContent className="p-4 sm:p-6 flex flex-col h-full">
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex-1 min-w-0">
                      <h2 className="font-bold text-lg sm:text-xl text-gray-900 mb-1 line-clamp-1 truncate">
                        {venue.name}
                      </h2>
                      <p className="text-primary font-medium text-xs uppercase tracking-wide mb-1 truncate">
                        {venue.category}
                      </p>
                    </div>
                    <div className="flex flex-col gap-1 ml-2">
                      {getFeaturedBadge(venue)}
                      <Badge variant={venue.status === 'active' ? 'default' : 'secondary'} className="text-xs px-2 py-1">
                        {venue.status || 'pending'}
                      </Badge>
                    </div>
                  </div>
                  
                  <div className="text-gray-500 text-xs sm:text-sm mb-2 flex items-center gap-2">
                    <MapPin className="w-3 h-3" />
                    <span className="truncate">{venue.city}, {venue.state}</span>
                  </div>
                  
                  <div className="flex items-center gap-2 mb-3">
                    {getStatusBadge(venue)}
                  </div>
                  
                  <div className="flex flex-col sm:flex-row gap-2 mt-auto">
                    <Button 
                      variant="outline" 
                      size="sm" 
                      onClick={() => openVenueDetails(venue)} 
                      className="flex-1 text-xs sm:text-sm"
                    >
                      <Edit className="w-3 h-3 mr-1 sm:hidden" />
                      <span className="hidden sm:inline">Edit</span>
                      <span className="sm:hidden">Edit</span>
                    </Button>
                    <Button 
                      variant="outline" 
                      size="sm" 
                      onClick={() => handleDeleteVenue(venue.id)} 
                      className="flex-1 text-red-600 hover:text-red-700 text-xs sm:text-sm"
                    >
                      <Trash2 className="w-3 h-3 mr-1 sm:hidden" />
                      <span className="hidden sm:inline">Delete</span>
                      <span className="sm:hidden">Del</span>
                    </Button>
                    <Checkbox 
                      checked={selectedVenues.includes(venue.id)} 
                      onCheckedChange={() => handleVenueSelect(venue.id)} 
                      className="ml-2 mt-1" 
                    />
                  </div>
                </CardContent>
              </Card>
            ))
          )}
        </div>

        {/* Pagination Controls */}
        {venues.length > 0 && (
          <div className="flex flex-col sm:flex-row justify-center items-center gap-3 sm:gap-4 pt-4 sm:pt-6">
            <Button
              variant="outline"
              onClick={() => {
                if (currentPage > 1) {
                  loadVenues(false, currentPage - 1);
                }
              }}
              disabled={loading || currentPage === 1}
              className="text-sm w-full sm:w-auto"
            >
              Previous
            </Button>
            <span className="text-xs sm:text-sm text-gray-600 text-center">
              Page {currentPage} • {venues.length} items
            </span>
            <Button
              variant="outline"
              onClick={() => {
                if (hasMore) {
                  loadVenues(false, currentPage + 1);
                }
              }}
              disabled={loading || !hasMore}
              className="text-sm w-full sm:w-auto"
            >
              Next
            </Button>
          </div>
        )}

        {/* Venue Details Modal */}
        <Dialog open={showVenueDetails} onOpenChange={setShowVenueDetails}>
          <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto rounded-2xl mx-4 sm:mx-auto">
            <DialogHeader>
              <DialogTitle className="text-lg sm:text-xl">Edit Venue</DialogTitle>
            </DialogHeader>
            {editForm && (
              <form className="space-y-4 sm:space-y-6" onSubmit={e => { e.preventDefault(); handleSaveEdit(); }}>
                {/* Progress Bar */}
                <div className="mb-4">
                  <div className="w-full h-3 rounded-full bg-gray-200 mb-1">
                    <div className="h-3 rounded-full bg-[#800000] transition-all" style={{ width: `${(editStep / totalEditSteps) * 100}%` }} />
                  </div>
                  <div className="flex justify-between text-xs sm:text-sm text-gray-500">
                    <span>Step {editStep} of {totalEditSteps}</span>
                    <span>{['Basic Info', 'Location & Pricing', 'Spaces & Packages', 'Images & Contact'][editStep-1]}</span>
                  </div>
                </div>

                {/* Step 1: Basic Info */}
                {editStep === 1 && (
                  <div className="bg-gray-50 p-4 sm:p-6 rounded-lg">
                    <h3 className="text-lg font-semibold mb-4">Basic Information</h3>
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Venue Name *</label>
                        <Input value={editForm.name || ''} onChange={e => handleEditChange('name', e.target.value)} required className="text-sm" />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Venue Type *</label>
                        <Input value={editForm.category || ''} onChange={e => handleEditChange('category', e.target.value)} required className="text-sm" />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Guest Capacity *</label>
                        <Input value={editForm.capacity || ''} onChange={e => handleEditChange('capacity', e.target.value)} required className="text-sm" />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Total Area</label>
                        <Input value={editForm.area || ''} onChange={e => handleEditChange('area', e.target.value)} className="text-sm" />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Available Rooms</label>
                        <Input value={editForm.rooms || ''} onChange={e => handleEditChange('rooms', e.target.value)} className="text-sm" />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Status</label>
                        <Input value={editForm.status || ''} onChange={e => handleEditChange('status', e.target.value)} className="text-sm" />
                      </div>
                      <div className="col-span-1 sm:col-span-2">
                        <label className="block text-sm font-medium text-gray-700 mb-2">Description *</label>
                        <textarea className="border rounded-lg px-3 py-2 w-full h-24 focus:ring-2 focus:ring-primary focus:border-transparent text-sm" value={editForm.description || ''} onChange={e => handleEditChange('description', e.target.value)} required />
                      </div>
                      <div className="flex items-center gap-4 mt-2 col-span-1 sm:col-span-2">
                        <Checkbox checked={!!editForm.featured} onCheckedChange={v => handleEditChange('featured', v)} />
                        <span className="text-sm">Featured Venue</span>
                        <Checkbox checked={!!editForm.verified} onCheckedChange={v => handleEditChange('verified', v)} />
                        <span className="text-sm">Verified Venue</span>
                      </div>
                    </div>
                  </div>
                )}

                {/* Step 2: Location & Pricing */}
                {editStep === 2 && (
                  <div className="bg-gray-50 p-4 sm:p-6 rounded-lg">
                    <h3 className="text-lg font-semibold mb-4">Location & Pricing</h3>
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Area/Locality *</label>
                        <Input value={editForm.locality || ''} onChange={e => handleEditChange('locality', e.target.value)} required className="text-sm" />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">City *</label>
                        <Input value={editForm.city || ''} onChange={e => handleEditChange('city', e.target.value)} required className="text-sm" />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">State *</label>
                        <Input value={editForm.state || ''} onChange={e => handleEditChange('state', e.target.value)} required className="text-sm" />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Starting Price *</label>
                        <Input value={editForm.startingPrice || ''} onChange={e => handleEditChange('startingPrice', e.target.value)} required className="text-sm" />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Price Range</label>
                        <Input value={editForm.priceRange || ''} onChange={e => handleEditChange('priceRange', e.target.value)} className="text-sm" />
                      </div>
                      <div className="col-span-1 sm:col-span-2">
                        <label className="block text-sm font-medium text-gray-700 mb-2">Full Address</label>
                        <Input value={editForm.fullAddress || ''} onChange={e => handleEditChange('fullAddress', e.target.value)} className="text-sm" />
                      </div>
                    </div>
                  </div>
                )}

                {/* Step 3: Spaces & Packages */}
                {editStep === 3 && (
                  <div className="bg-gray-50 p-4 sm:p-6 rounded-lg">
                    <h3 className="text-lg font-semibold mb-4">Spaces & Packages</h3>
                    <div className="mb-4">
                      <h4 className="font-semibold mb-2 text-sm">Venue Spaces</h4>
                      {(editForm.spaces || []).length === 0 ? (
                        <div className="text-gray-400 mb-2 text-sm">No spaces added yet. Click "Add Space" to get started.</div>
                      ) : (
                        (editForm.spaces || []).map((space: any, idx: number) => (
                          <div key={idx} className="flex flex-col sm:flex-row gap-2 items-center mb-2">
                            <Input className="flex-1 text-sm" placeholder="Space Name" value={space.name} onChange={e => handleSpaceChange(idx, 'name', e.target.value)} />
                            <Input className="w-full sm:w-32 text-sm" placeholder="Capacity" value={space.capacity} onChange={e => handleSpaceChange(idx, 'capacity', e.target.value)} />
                            <Input className="w-full sm:w-32 text-sm" placeholder="Price" value={space.price} onChange={e => handleSpaceChange(idx, 'price', e.target.value)} />
                            <Button type="button" variant="destructive" size="sm" onClick={() => removeSpace(idx)} className="text-xs">Remove</Button>
                          </div>
                        ))
                      )}
                      <Button type="button" variant="outline" size="sm" onClick={addSpace} className="text-sm">+ Add Space</Button>
                    </div>
                    <div className="mb-4">
                      <h4 className="font-semibold mb-2 text-sm">Venue Packages</h4>
                      {(editForm.packages || []).length === 0 ? (
                        <div className="text-gray-400 mb-2 text-sm">No packages added yet. Click "Add Package" to get started.</div>
                      ) : (
                        (editForm.packages || []).map((pkg: any, idx: number) => (
                          <div key={idx} className="flex flex-col sm:flex-row gap-2 items-center mb-2">
                            <Input className="flex-1 text-sm" placeholder="Package Name" value={pkg.name} onChange={e => handlePackageChange(idx, 'name', e.target.value)} />
                            <Input className="w-full sm:w-32 text-sm" placeholder="Price" value={pkg.price} onChange={e => handlePackageChange(idx, 'price', e.target.value)} />
                            <Input className="w-full sm:w-32 text-sm" placeholder="Duration" value={pkg.duration} onChange={e => handlePackageChange(idx, 'duration', e.target.value)} />
                            <Button type="button" variant="destructive" size="sm" onClick={() => removePackage(idx)} className="text-xs">Remove</Button>
                          </div>
                        ))
                      )}
                      <Button type="button" variant="outline" size="sm" onClick={addPackage} className="text-sm">+ Add Package</Button>
                    </div>
                  </div>
                )}

                {/* Step 4: Images & Contact */}
                {editStep === 4 && (
                  <div className="bg-gray-50 p-4 sm:p-6 rounded-lg">
                    <h3 className="text-lg font-semibold mb-4">Images & Contact</h3>
                    <div className="mb-4">
                      <label className="block text-sm font-medium text-gray-700 mb-2">Main Profile Image</label>
                      <ImageUpload
                        images={editForm?.mainImage ? [editForm.mainImage] : []}
                        onImagesChange={images => handleEditChange('mainImage', images[0] || '')}
                        maxImages={1}
                        label="Upload Main Venue Image"
                        description="Upload main venue image (JPG, PNG, WebP)"
                        compressForDB={true}
                        showSizeWarning={true}
                      />
                    </div>
                    <div className="mb-4">
                      <label className="block text-sm font-medium text-gray-700 mb-2">Gallery Images</label>
                      <ImageUpload
                        images={editForm?.images || []}
                        onImagesChange={images => handleEditChange('images', images)}
                        maxImages={15}
                        label="Upload Venue Gallery Images"
                        description="Upload venue images (JPG, PNG, WebP) - Images will be compressed for database storage"
                        compressForDB={true}
                        showSizeWarning={true}
                      />
                    </div>
                    <div className="mb-4">
                      <label className="block text-sm font-medium text-gray-700 mb-2">Or Add Image URLs</label>
                      <div className="flex flex-col sm:flex-row gap-2">
                        <Input className="flex-1 text-sm" placeholder="https://example.com/venue-image.jpg" />
                        <Button type="button" variant="outline" size="sm" className="text-sm">Add URL</Button>
                      </div>
                    </div>
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Contact Phone</label>
                        <Input value={editForm.contactPhone || ''} onChange={e => handleEditChange('contactPhone', e.target.value)} className="text-sm" />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Contact Email</label>
                        <Input value={editForm.contactEmail || ''} onChange={e => handleEditChange('contactEmail', e.target.value)} className="text-sm" />
                      </div>
                      <div className="col-span-1 sm:col-span-2">
                        <label className="block text-sm font-medium text-gray-700 mb-2">Website</label>
                        <Input value={editForm.website || ''} onChange={e => handleEditChange('website', e.target.value)} className="text-sm" />
                      </div>
                    </div>
                  </div>
                )}

                {/* Navigation Buttons */}
                <div className="flex flex-col sm:flex-row gap-3 justify-between pt-4 border-t mt-6">
                  <Button type="button" variant="outline" onClick={() => setShowVenueDetails(false)} disabled={saving} className="text-sm">Cancel</Button>
                  <div className="flex flex-col sm:flex-row gap-2">
                    {editStep > 1 && (
                      <Button type="button" variant="outline" onClick={() => setEditStep(editStep - 1)} disabled={saving} className="text-sm">Back</Button>
                    )}
                    {editStep < totalEditSteps && (
                      <Button type="button" className="bg-primary hover:bg-primary/90 text-sm" onClick={() => setEditStep(editStep + 1)} disabled={saving}>Next</Button>
                    )}
                    {editStep === totalEditSteps && (
                      <Button type="submit" className="bg-primary hover:bg-primary/90 text-sm" disabled={saving}>{saving ? 'Saving...' : 'Save Changes'}</Button>
                    )}
                  </div>
                </div>
              </form>
            )}
          </DialogContent>
        </Dialog>
      </div>
    </AdminOnlyRoute>
  );
}
