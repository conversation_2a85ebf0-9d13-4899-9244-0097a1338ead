"use client"

import { useState, useEffect } from "react"
import { useRout<PERSON> } from "next/navigation"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Package, Search, Calendar, CreditCard, Truck, Eye, Download, Filter, RefreshCw } from "lucide-react"
import Link from "next/link"
import { OrderService, OrderData } from "@/lib/services/orderService"
import { useAuth } from "@/contexts/AuthContext"
import { showToast } from "@/lib/toast"

export default function UserOrdersPage() {
  const { isAuthenticated, user } = useAuth()
  const router = useRouter()
  const [orders, setOrders] = useState<OrderData[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState('all')
  const [dateFilter, setDateFilter] = useState('all')
  const [filteredOrders, setFilteredOrders] = useState<OrderData[]>([])

  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/login?redirect=/dashboard/orders')
      return
    }

    loadOrders()
  }, [isAuthenticated])

  useEffect(() => {
    filterOrders()
  }, [searchTerm, statusFilter, dateFilter, orders])

  const loadOrders = async () => {
    try {
      setLoading(true)
      const result = await OrderService.getUserOrders(100)

      if (result.success) {
        setOrders(result.orders || [])
      } else {
        showToast.error('Failed to load orders')
        setOrders([]) // Set empty array as fallback
      }
    } catch (error) {
      console.error('Error loading orders:', error)
      showToast.error('Failed to load orders')
      setOrders([]) // Set empty array as fallback
    } finally {
      setLoading(false)
    }
  }

  const filterOrders = () => {
    let filtered = orders

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter(order =>
        order.orderNumber?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (order.items && order.items.some(item => item.productName?.toLowerCase().includes(searchTerm.toLowerCase())))
      )
    }

    // Status filter
    if (statusFilter !== 'all') {
      filtered = filtered.filter(order => order.status === statusFilter)
    }

    // Date filter
    if (dateFilter !== 'all') {
      const now = new Date()
      const filterDate = new Date()
      
      switch (dateFilter) {
        case 'week':
          filterDate.setDate(now.getDate() - 7)
          break
        case 'month':
          filterDate.setMonth(now.getMonth() - 1)
          break
        case 'quarter':
          filterDate.setMonth(now.getMonth() - 3)
          break
      }
      
      filtered = filtered.filter(order => new Date(order.orderDate) >= filterDate)
    }

    setFilteredOrders(filtered)
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'PENDING': return 'bg-yellow-100 text-yellow-800'
      case 'CONFIRMED': return 'bg-green-100 text-green-800'
      case 'PROCESSING': return 'bg-blue-100 text-blue-800'
      case 'SHIPPED': return 'bg-purple-100 text-purple-800'
      case 'OUT_FOR_DELIVERY': return 'bg-indigo-100 text-indigo-800'
      case 'DELIVERED': return 'bg-green-100 text-green-800'
      case 'CANCELLED': return 'bg-red-100 text-red-800'
      case 'RETURNED': return 'bg-orange-100 text-orange-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getPaymentStatusColor = (status: string) => {
    switch (status) {
      case 'PAID': return 'bg-green-100 text-green-800'
      case 'COD_PENDING': return 'bg-yellow-100 text-yellow-800'
      case 'COD_COLLECTED': return 'bg-green-100 text-green-800'
      case 'FAILED': return 'bg-red-100 text-red-800'
      case 'PENDING': return 'bg-yellow-100 text-yellow-800'
      case 'REFUNDED': return 'bg-blue-100 text-blue-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getCurrentOrders = () => filteredOrders.filter(order => 
    ['PENDING', 'CONFIRMED', 'PROCESSING', 'SHIPPED', 'OUT_FOR_DELIVERY'].includes(order.status)
  )

  const getPastOrders = () => filteredOrders.filter(order => 
    ['DELIVERED', 'CANCELLED', 'RETURNED'].includes(order.status)
  )

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#F6C244]"></div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-4 sm:p-6 border border-blue-100">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-3 sm:gap-4">
          <div>
            <h1 className="text-2xl sm:text-3xl font-bold text-gray-900 mb-1 flex items-center gap-2">
              <Package className="h-6 w-6 sm:h-8 sm:w-8 text-blue-600" />
              My Orders
            </h1>
            <p className="text-gray-600 text-sm sm:text-base">Track and manage your orders across the platform</p>
          </div>
          <div className="flex gap-2">
            <Button onClick={loadOrders} variant="outline" className="flex items-center gap-2 text-sm hover:bg-blue-50">
              <RefreshCw className="h-4 w-4" />
              <span className="hidden sm:inline">Refresh Data</span>
              <span className="sm:hidden">Refresh</span>
            </Button>
          </div>
        </div>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="Search orders..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger>
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="PENDING">Pending</SelectItem>
                <SelectItem value="CONFIRMED">Confirmed</SelectItem>
                <SelectItem value="PROCESSING">Processing</SelectItem>
                <SelectItem value="SHIPPED">Shipped</SelectItem>
                <SelectItem value="OUT_FOR_DELIVERY">Out for Delivery</SelectItem>
                <SelectItem value="DELIVERED">Delivered</SelectItem>
                <SelectItem value="CANCELLED">Cancelled</SelectItem>
                <SelectItem value="RETURNED">Returned</SelectItem>
              </SelectContent>
            </Select>

            <Select value={dateFilter} onValueChange={setDateFilter}>
              <SelectTrigger>
                <SelectValue placeholder="Filter by date" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Time</SelectItem>
                <SelectItem value="week">Last Week</SelectItem>
                <SelectItem value="month">Last Month</SelectItem>
                <SelectItem value="quarter">Last 3 Months</SelectItem>
              </SelectContent>
            </Select>

            <div className="flex items-center gap-2 text-sm text-gray-600">
              <Filter className="h-4 w-4" />
              <span>{filteredOrders.length} orders found</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Orders Tabs */}
      <Tabs defaultValue="current" className="space-y-6">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="current" className="flex items-center gap-2">
            <Package className="h-4 w-4" />
            Current Orders ({getCurrentOrders().length})
          </TabsTrigger>
          <TabsTrigger value="past" className="flex items-center gap-2">
            <Calendar className="h-4 w-4" />
            Past Orders ({getPastOrders().length})
          </TabsTrigger>
        </TabsList>

        <TabsContent value="current" className="space-y-4">
          {getCurrentOrders().length === 0 ? (
            <Card>
              <CardContent className="text-center py-12">
                <Package className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No current orders</h3>
                <p className="text-gray-600 mb-6">You don't have any active orders at the moment.</p>
                <Button onClick={() => router.push('/shop')} className="bg-[#F6C244] hover:bg-[#F6C244]/90 text-black">
                  Start Shopping
                </Button>
              </CardContent>
            </Card>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {getCurrentOrders().map((order) => (
                <OrderCard key={order.id} order={order} getStatusColor={getStatusColor} getPaymentStatusColor={getPaymentStatusColor} />
              ))}
            </div>
          )}
        </TabsContent>

        <TabsContent value="past" className="space-y-4">
          {getPastOrders().length === 0 ? (
            <Card>
              <CardContent className="text-center py-12">
                <Calendar className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No past orders</h3>
                <p className="text-gray-600">Your completed orders will appear here.</p>
              </CardContent>
            </Card>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {getPastOrders().map((order) => (
                <OrderCard key={order.id} order={order} getStatusColor={getStatusColor} getPaymentStatusColor={getPaymentStatusColor} />
              ))}
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  )
}

// Order Card Component
function OrderCard({ order, getStatusColor, getPaymentStatusColor }: {
  order: OrderData,
  getStatusColor: (status: string) => string,
  getPaymentStatusColor: (status: string) => string
}) {
  const router = useRouter()

  return (
    <Card className="hover:shadow-lg transition-shadow">
      <CardHeader>
        <div className="flex justify-between items-start">
          <div>
            <CardTitle className="text-lg">Order #{order.orderNumber}</CardTitle>
            <div className="flex items-center gap-4 text-sm text-gray-600 mt-1">
              <span className="flex items-center gap-1">
                <Calendar className="h-4 w-4" />
                {order.orderDate ? new Date(order.orderDate).toLocaleDateString() : 'N/A'}
              </span>
              <span className="flex items-center gap-1 min-w-0">
                <CreditCard className="h-4 w-4 flex-shrink-0" />
                <span className="truncate">{order.paymentMethod || 'N/A'}</span>
              </span>
            </div>
          </div>
          <div className="text-right">
            <div className="text-lg font-bold">₹{order.total?.toLocaleString() || '0'}</div>
            <div className="flex gap-2 mt-1">
              <Badge className={getStatusColor(order.status || 'PENDING')}>{order.status || 'PENDING'}</Badge>
              <Badge className={getPaymentStatusColor(order.paymentStatus || 'PENDING')}>
                {(order.paymentStatus || 'PENDING').replace('_', ' ')}
              </Badge>
            </div>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {/* Order Items Preview */}
        <div className="space-y-3 mb-4">
          {order.items && order.items.length > 0 ? (
            <>
              {order.items.slice(0, 2).map((item, index) => (
                <div key={index} className="flex items-center space-x-3">
                  {item.productImage && (
                    <img
                      src={item.productImage}
                      alt={item.productName}
                      className="w-12 h-12 object-cover rounded"
                    />
                  )}
                  <div className="flex-1">
                    <p className="font-medium text-sm">{item.productName}</p>
                    <p className="text-xs text-gray-600">Qty: {item.quantity}</p>
                  </div>
                  <div className="text-sm font-medium">₹{item.subtotal?.toLocaleString() || '0'}</div>
                </div>
              ))}
              {order.items.length > 2 && (
                <p className="text-sm text-gray-600">
                  +{order.items.length - 2} more item{order.items.length - 2 > 1 ? 's' : ''}
                </p>
              )}
            </>
          ) : (
            <p className="text-sm text-gray-500">No items found</p>
          )}
        </div>

        {/* Delivery Info */}
        {order.estimatedDeliveryDate && (
          <div className="flex items-center gap-2 text-sm text-gray-600 mb-4">
            <Truck className="h-4 w-4" />
            <span>
              Estimated delivery: {new Date(order.estimatedDeliveryDate).toLocaleDateString()}
            </span>
          </div>
        )}

        {/* Actions */}
        <div className="flex gap-3">
          <Button
            variant="outline"
            size="sm"
            onClick={() => router.push(`/dashboard/order-confirmation?orderId=${order.id}`)}
            className="flex items-center gap-2"
          >
            <Eye className="h-4 w-4" />
            View Details
          </Button>

          {order.status === 'DELIVERED' && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => router.push(`/shop`)}
            >
              Buy Again
            </Button>
          )}

          {(order.status === 'PENDING' || order.status === 'CONFIRMED') && (
            <Button
              variant="outline"
              size="sm"
              className="text-red-600 hover:text-red-700"
            >
              Cancel Order
            </Button>
          )}

          <Button
            variant="outline"
            size="sm"
            className="flex items-center gap-2"
          >
            <Download className="h-4 w-4" />
            Invoice
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}
