"use client"

import { Suspense } from 'react'
import { useSearchParams } from 'next/navigation'

/**
 * Safe wrapper for useSearchParams that handles Suspense boundaries
 * This fixes the Next.js 14 build error about missing Suspense boundaries
 */

interface SafeSearchParamsProps {
  children: (searchParams: URLSearchParams) => React.ReactNode
  fallback?: React.ReactNode
}

function SearchParamsContent({ children }: { children: (searchParams: URLSearchParams) => React.ReactNode }) {
  const searchParams = useSearchParams()
  return <>{children(searchParams)}</>
}

export function SafeSearchParams({ children, fallback = null }: SafeSearchParamsProps) {
  return (
    <Suspense fallback={fallback}>
      <SearchParamsContent>{children}</SearchParamsContent>
    </Suspense>
  )
}

/**
 * Hook that safely gets search params with a fallback
 */
export function useSafeSearchParams(): URLSearchParams | null {
  try {
    return useSearchParams()
  } catch (error) {
    // Return empty URLSearchParams if not in a Suspense boundary
    return new URLSearchParams()
  }
}

export default SafeSearchParams
