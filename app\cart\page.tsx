"use client"

import React, { useState, useEffect } from 'react'
import { TopHeader } from "@/components/top-header"
import { Head<PERSON> } from "@/components/header"
import { Footer } from "@/components/footer"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Input } from "@/components/ui/input"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import {
  ShoppingCart,
  Minus,
  Plus,
  Trash2,
  Heart,
  ArrowLeft,
  ShoppingBag,
  Loader2,
  Package,
  CreditCard,
  Truck,
  Shield,
  Gift,
  Percent,
  User
} from "lucide-react"
import { useAuth } from '@/contexts/AuthContext'
import { CartService, CartItemData, CartSummary } from '@/lib/services/cartService'
import { showToast } from '@/lib/toast'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { useCart } from '@/components/ClientRoot'
import { useCartTranslations, useCommonTranslations } from '@/lib/universal-i18n'

export default function CartPage() {
  const { isAuthenticated, user, isLoading: authLoading } = useAuth()
  const { cart, removeFromCart, updateQuantity, clearCart, getTotalItems, getTotalPrice } = useCart()
  const router = useRouter()
  const cartT = useCartTranslations()
  const commonT = useCommonTranslations()
  const [cartSummary, setCartSummary] = useState<CartSummary>({
    items: [],
    totalItems: 0,
    totalQuantity: 0,
    subtotal: 0,
    totalDiscount: 0,
    total: 0,
    savedForLaterCount: 0
  })
  const [savedItems, setSavedItems] = useState<CartItemData[]>([])
  const [loading, setLoading] = useState(true)
  const [updating, setUpdating] = useState<string | null>(null)
  const [activeTab, setActiveTab] = useState('cart')
  const [promoCode, setPromoCode] = useState('')

  useEffect(() => {
    if (!authLoading) {
      if (isAuthenticated) {
        loadCartData()
      } else {
        // For guest users, use local cart data
        setLoading(false)
      }
    }
  }, [isAuthenticated, authLoading, cart])

  const loadCartData = async () => {
    try {
      setLoading(true)
      const [summary, savedResult] = await Promise.all([
        CartService.getCartSummary(),
        CartService.getCartItems('SAVED_FOR_LATER')
      ])
      
      setCartSummary(summary)
      setSavedItems(savedResult.success ? savedResult.items : [])
    } catch (error) {
      console.error('Error loading cart data:', error)
      showToast.error('Failed to load cart')
    } finally {
      setLoading(false)
    }
  }

  const handleUpdateQuantity = async (cartItemId: string, newQuantity: number, itemIndex?: number) => {
    setUpdating(cartItemId)
    try {
      if (isAuthenticated) {
        // For authenticated users, update database cart
        const result = await CartService.updateQuantity(cartItemId, newQuantity)
        if (result.success) {
          await loadCartData()
          showToast.success('Quantity updated')
        }
      } else {
        // For guest users, update local cart
        if (itemIndex !== undefined) {
          updateQuantity(itemIndex, newQuantity)
          showToast.success('Quantity updated')
        }
      }
    } catch (error) {
      console.error('Update quantity error:', error)
      showToast.error('Failed to update quantity')
    } finally {
      setUpdating(null)
    }
  }

  const handleRemoveItem = async (cartItemId: string, itemIndex?: number) => {
    setUpdating(cartItemId)
    try {
      if (isAuthenticated) {
        // For authenticated users, remove from database cart
        const result = await CartService.removeFromCart(cartItemId)
        if (result.success) {
          await loadCartData()
          showToast.success('Item removed from cart')
        }
      } else {
        // For guest users, remove from local cart
        if (itemIndex !== undefined) {
          removeFromCart(itemIndex)
          showToast.success('Item removed from cart')
        }
      }
    } catch (error) {
      console.error('Remove item error:', error)
      showToast.error('Failed to remove item')
    } finally {
      setUpdating(null)
    }
  }

  const handleSaveForLater = async (cartItemId: string) => {
    setUpdating(cartItemId)
    try {
      const result = await CartService.saveForLater(cartItemId)
      if (result.success) {
        await loadCartData()
        showToast.success('Item saved for later')
      }
    } catch (error) {
      console.error('Save for later error:', error)
      showToast.error('Failed to save for later')
    } finally {
      setUpdating(null)
    }
  }

  const handleMoveToCart = async (cartItemId: string) => {
    setUpdating(cartItemId)
    try {
      const result = await CartService.moveToCart(cartItemId)
      if (result.success) {
        await loadCartData()
        showToast.success('Item moved to cart')
      }
    } catch (error) {
      console.error('Move to cart error:', error)
      showToast.error('Failed to move to cart')
    } finally {
      setUpdating(null)
    }
  }

  const handleClearCart = async () => {
    try {
      const result = await CartService.clearCart()
      if (result.success) {
        await loadCartData()
        showToast.success('Cart cleared')
      }
    } catch (error) {
      console.error('Clear cart error:', error)
      showToast.error('Failed to clear cart')
    }
  }

  const getProductImage = (item: CartItemData) => {
    if (item.productImage && item.productImage.trim() !== '' && item.productImage !== 'undefined' && item.productImage !== 'null') {
      return item.productImage
    }
    return "/placeholder.svg"
  }

  if (authLoading || loading) {
    return (
      <div className="min-h-screen bg-white">
        <TopHeader />
        <Header />
        <div className="flex items-center justify-center py-20">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </div>
        <Footer />
      </div>
    )
  }

  // Get cart data based on authentication status
  const displayCartItems = isAuthenticated ? cartSummary.items : cart.map(item => ({
    id: item.id,
    productId: item.id,
    productName: item.name,
    productPrice: item.price,
    productImage: item.image,
    quantity: item.quantity,
    size: item.size,
    color: item.color,
    status: 'ACTIVE',
    dateAdded: new Date().toISOString(),
    dateUpdated: new Date().toISOString()
  }))

  const displayTotal = isAuthenticated ? cartSummary.total : getTotalPrice
  const displayTotalItems = isAuthenticated ? cartSummary.totalItems : getTotalItems

  return (
    <div className="min-h-screen bg-gray-50">
      <TopHeader />
      <Header />

      {/* Enhanced Hero Section */}
      <section className="bg-gradient-to-r from-white via-primary/5 to-accent/10 border-b border-gray-100 mt-0">
        <div className="container mx-auto px-3 sm:px-4 py-4 sm:py-12">
          {/* Main Header */}
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
            {/* Left Section - Title and Info */}
            <div className="flex-1">
              <div className="flex items-center gap-2 sm:gap-3 mb-2 sm:mb-3">
                <div className="p-2 sm:p-3 bg-primary/10 rounded-full">
                  <ShoppingCart className="h-5 w-5 sm:h-7 sm:w-7 text-primary" />
                </div>
                <div>
                  <h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-gray-900 mb-1">
                    {cartT.title}
                  </h1>
                  <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-4 text-gray-600">
                    <span className="flex items-center gap-2 text-sm sm:text-base">
                      <Package className="h-4 w-4" />
                      {displayTotalItems} {displayTotalItems === 1 ? cartT.item : cartT.items}
                    </span>
                    {displayTotal > 0 && (
                      <span className="flex items-center gap-2 font-semibold text-primary text-sm sm:text-base">
                        <span>{cartT.total}: ₹{displayTotal.toLocaleString()}</span>
                      </span>
                    )}
                    {!isAuthenticated && displayTotalItems > 0 && (
                      <span className="text-xs sm:text-sm bg-blue-100 text-blue-800 px-2 py-1 rounded-full">
                        {cartT.guestCart}
                      </span>
                    )}
                  </div>
                </div>
              </div>
            </div>

            {/* Right Section - Actions */}
            <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-2 sm:gap-3">
              <Link href="/shop" className="w-full sm:w-auto">
                <Button variant="outline" className="flex items-center gap-2 hover:bg-primary/5 hover:border-primary/30 w-full sm:w-auto text-sm sm:text-base">
                  <ArrowLeft className="h-4 w-4" />
                  {cartT.continueShopping}
                </Button>
              </Link>
              {displayTotalItems > 0 && (
                <Button
                  variant="outline"
                  onClick={isAuthenticated ? handleClearCart : () => clearCart()}
                  className="flex items-center gap-2 text-red-600 hover:text-red-700 hover:bg-red-50 hover:border-red-200 w-full sm:w-auto text-sm sm:text-base"
                >
                  <Trash2 className="h-4 w-4" />
                  {cartT.clearCart}
                </Button>
              )}
            </div>
          </div>

          {/* Cart Status Bar */}
          {displayTotalItems > 0 && (
            <div className="mt-4 sm:mt-6 p-3 sm:p-4 bg-white rounded-lg border border-gray-200 shadow-sm">
              <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-3 sm:gap-4">
                <div className="flex flex-col sm:flex-row items-start sm:items-center gap-2 sm:gap-4">
                  <div className="flex items-center gap-2 text-green-600">
                    <Shield className="h-4 w-4 sm:h-5 sm:w-5" />
                    <span className="font-medium text-sm sm:text-base">{cartT.secureCheckout}</span>
                  </div>
                  <div className="flex items-center gap-2 text-blue-600">
                    <Truck className="h-4 w-4 sm:h-5 sm:w-5" />
                    <span className="font-medium text-sm sm:text-base">{cartT.freeDelivery}</span>
                  </div>
                </div>
                <div className="text-left sm:text-right">
                  <p className="text-xs sm:text-sm text-gray-600">{cartT.subtotal}</p>
                  <p className="text-lg sm:text-xl font-bold text-gray-900">₹{displayTotal.toLocaleString()}</p>
                </div>
              </div>
            </div>
          )}
        </div>
      </section>

      {/* Main Content */}
      <section className="py-4 sm:py-6 md:py-8">
        <div className="container mx-auto px-4">
          {loading ? (
            <div className="flex items-center justify-center py-12 sm:py-20">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
            </div>
          ) : cartSummary.totalItems === 0 && savedItems.length === 0 ? (
            <div className="text-center py-12 sm:py-20">
              <ShoppingBag className="h-16 w-16 sm:h-24 sm:w-24 text-gray-300 mx-auto mb-4 sm:mb-6" />
              <h2 className="text-xl sm:text-2xl font-bold text-gray-600 mb-3 sm:mb-4">{cartT.emptyCart}</h2>
              <p className="text-gray-500 mb-6 sm:mb-8 text-sm sm:text-base">
                {cartT.emptyCartMessage}
              </p>
              <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 justify-center">
                <Link href="/shop" className="w-full sm:w-auto">
                  <Button size="lg" className="w-full sm:w-auto">
                    <ShoppingBag className="h-5 w-5 mr-2" />
                    {cartT.startShopping}
                  </Button>
                </Link>
                <Link href="/favorites" className="w-full sm:w-auto">
                  <Button variant="outline" size="lg" className="w-full sm:w-auto">
                    <Heart className="h-5 w-5 mr-2" />
                    {commonT.viewAll} {commonT.favorites || 'Favorites'}
                  </Button>
                </Link>
              </div>
            </div>
          ) : (
            <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
              {/* Custom Pill-Shaped Toggle */}
              <div className="mb-4 sm:mb-6 md:mb-8">
                <div className="relative inline-flex bg-gray-100 border border-gray-200 rounded-full p-1 w-full sm:w-auto lg:p-2">
                  <button
                    onClick={() => setActiveTab('cart')}
                    className={`relative flex-1 sm:flex-none px-3 sm:px-6 lg:px-8 py-2 sm:py-2 lg:py-3 rounded-full text-sm sm:text-sm lg:text-base font-medium transition-all duration-200 ${
                      activeTab === 'cart'
                        ? 'bg-primary text-white shadow-sm'
                        : 'text-gray-700 hover:text-gray-900'
                    }`}
                  >
                    {cartT.title} ({displayTotalItems})
                  </button>
                  <button
                    onClick={() => setActiveTab('saved')}
                    className={`relative flex-1 sm:flex-none px-3 sm:px-6 lg:px-8 py-2 sm:py-2 lg:py-3 rounded-full text-sm sm:text-sm lg:text-base font-medium transition-all duration-200 ${
                      activeTab === 'saved'
                        ? 'bg-primary text-white shadow-sm'
                        : 'text-gray-700 hover:text-gray-900'
                    }`}
                  >
                    {cartT.savedForLater} ({savedItems.length})
                  </button>
                </div>
              </div>

              <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 sm:gap-6 md:gap-8">
                {/* Cart Items */}
                <div className="lg:col-span-2">
                  <TabsContent value="cart">
                    {displayCartItems.length === 0 ? (
                      <Card>
                        <CardContent className="text-center py-8 sm:py-12">
                          <ShoppingCart className="h-12 w-12 sm:h-16 sm:w-16 text-gray-300 mx-auto mb-3 sm:mb-4" />
                          <h3 className="text-base sm:text-lg font-semibold text-gray-600 mb-2">{cartT.emptyCart}</h3>
                          <p className="text-gray-500 mb-4 sm:mb-6 text-sm sm:text-base">{cartT.emptyCartMessage}</p>
                          <Link href="/shop">
                            <Button className="bg-primary text-white hover:bg-primary/90">
                              {cartT.continueShopping}
                            </Button>
                          </Link>
                        </CardContent>
                      </Card>
                    ) : (
                      <div className="space-y-3 sm:space-y-4">
                        {displayCartItems.map((item, index) => (
                          <Card key={item.id} className="overflow-hidden">
                            <CardContent className="p-4 sm:p-6">
                              <div className="flex gap-3 sm:gap-4">
                                {/* Product Image */}
                                <div className="flex-shrink-0">
                                  <Link href={`/shop/${item.productId}`}>
                                    <img
                                      src={getProductImage(item)}
                                      alt={item.productName}
                                      className="w-16 h-16 sm:w-20 sm:h-20 lg:w-24 lg:h-24 object-cover rounded-lg border"
                                    />
                                  </Link>
                                </div>

                                {/* Product Details */}
                                <div className="flex-1 min-w-0">
                                  <div className="flex justify-between items-start mb-2">
                                    <div className="flex-1 min-w-0 pr-2">
                                      <Link href={`/shop/${item.productId}`}>
                                        <h3 className="font-semibold text-sm sm:text-base lg:text-lg hover:text-primary transition-colors line-clamp-1 lg:line-clamp-2">
                                          {item.productName}
                                        </h3>
                                      </Link>
                                      {item.productBrand && (
                                        <p className="text-xs sm:text-sm text-gray-500 mt-1">{item.productBrand}</p>
                                      )}
                                      {item.productCategory && (
                                        <Badge variant="outline" className="mt-1 text-xs">
                                          {item.productCategory}
                                        </Badge>
                                      )}
                                    </div>
                                    <Button
                                      variant="ghost"
                                      size="sm"
                                      onClick={() => handleRemoveItem(item.id, index)}
                                      disabled={updating === item.id}
                                      className="text-red-600 hover:text-red-700 hover:bg-red-50 p-1 h-8 w-8"
                                    >
                                      {updating === item.id ? (
                                        <Loader2 className="h-3 w-3 lg:h-4 lg:w-4 animate-spin" />
                                      ) : (
                                        <Trash2 className="h-3 w-3 lg:h-4 lg:w-4" />
                                      )}
                                    </Button>
                                  </div>

                                  {/* Variants */}
                                  {(item.selectedSize || item.selectedColor || item.selectedVariant) && (
                                    <div className="flex flex-wrap gap-2 lg:gap-4 text-xs sm:text-sm text-gray-600 mb-2 lg:mb-3">
                                      {item.selectedSize && <span>{cartT.size}: {item.selectedSize}</span>}
                                      {item.selectedColor && <span>{cartT.color}: {item.selectedColor}</span>}
                                      {item.selectedVariant && <span>{cartT.variant}: {item.selectedVariant}</span>}
                                    </div>
                                  )}

                                  {/* Price and Quantity */}
                                  <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-2 lg:gap-4">
                                    {/* Price */}
                                    <div className="flex items-center gap-2 flex-wrap">
                                      <span className="text-lg lg:text-xl font-bold text-primary">
                                        ₹{item.productPrice.toLocaleString()}
                                      </span>
                                      {item.originalPrice && item.originalPrice > item.productPrice && (
                                        <>
                                          <span className="text-xs lg:text-sm text-gray-500 line-through">
                                            ₹{item.originalPrice.toLocaleString()}
                                          </span>
                                          <span className="text-xs font-medium text-red-600 bg-red-50 px-1 lg:px-2 py-0.5 lg:py-1 rounded">
                                            {Math.round(((item.originalPrice - item.productPrice) / item.originalPrice) * 100)}% {cartT.off}
                                          </span>
                                        </>
                                      )}
                                    </div>

                                    {/* Quantity Controls */}
                                    <div className="flex items-center border rounded-lg w-fit">
                                      <Button
                                        variant="ghost"
                                        size="sm"
                                        onClick={() => handleUpdateQuantity(item.id, item.quantity - 1, index)}
                                        disabled={updating === item.id || item.quantity <= 1}
                                        className="h-7 w-7 lg:h-8 lg:w-8 p-0"
                                      >
                                        <Minus className="h-3 w-3" />
                                      </Button>
                                      <span className="px-2 lg:px-3 py-1 text-sm font-medium min-w-[30px] lg:min-w-[40px] text-center">
                                        {item.quantity}
                                      </span>
                                      <Button
                                        variant="ghost"
                                        size="sm"
                                        onClick={() => handleUpdateQuantity(item.id, item.quantity + 1, index)}
                                        disabled={updating === item.id}
                                        className="h-7 w-7 lg:h-8 lg:w-8 p-0"
                                      >
                                        <Plus className="h-3 w-3" />
                                      </Button>
                                    </div>
                                  </div>

                                  {/* Actions */}
                                  <div className="mt-3 lg:mt-4">
                                    <Button
                                      variant="outline"
                                      size="sm"
                                      onClick={() => handleSaveForLater(item.id)}
                                      disabled={updating === item.id}
                                      className="text-xs lg:text-sm h-7 lg:h-8"
                                    >
                                      <Heart className="h-3 w-3 lg:h-4 lg:w-4 mr-1 lg:mr-2" />
                                      {cartT.saveForLater}
                                    </Button>
                                  </div>
                                </div>
                              </div>
                            </CardContent>
                          </Card>
                        ))}
                      </div>
                    )}
                  </TabsContent>

                  <TabsContent value="saved">
                    {savedItems.length === 0 ? (
                      <Card>
                        <CardContent className="text-center py-12">
                          <Heart className="h-16 w-16 text-gray-300 mx-auto mb-4" />
                          <h3 className="text-lg font-semibold text-gray-600 mb-2">{cartT.noSavedItems}</h3>
                          <p className="text-gray-500">{cartT.savedItemsMessage}</p>
                        </CardContent>
                      </Card>
                    ) : (
                      <div className="space-y-4">
                        {savedItems.map((item) => (
                          <Card key={item.id} className="overflow-hidden">
                            <CardContent className="p-6">
                              <div className="flex gap-4">
                                {/* Product Image */}
                                <div className="flex-shrink-0">
                                  <Link href={`/shop/${item.productId}`}>
                                    <img
                                      src={getProductImage(item)}
                                      alt={item.productName}
                                      className="w-24 h-24 object-cover rounded-lg border"
                                    />
                                  </Link>
                                </div>

                                {/* Product Details */}
                                <div className="flex-1 min-w-0">
                                  <div className="flex justify-between items-start mb-2">
                                    <div>
                                      <Link href={`/shop/${item.productId}`}>
                                        <h3 className="font-semibold text-lg hover:text-primary transition-colors line-clamp-1">
                                          {item.productName}
                                        </h3>
                                      </Link>
                                      {item.productBrand && (
                                        <p className="text-sm text-gray-500">{item.productBrand}</p>
                                      )}
                                    </div>
                                    <Button
                                      variant="ghost"
                                      size="sm"
                                      onClick={() => handleRemoveItem(item.id)}
                                      disabled={updating === item.id}
                                      className="text-red-600 hover:text-red-700 hover:bg-red-50"
                                    >
                                      {updating === item.id ? (
                                        <Loader2 className="h-4 w-4 animate-spin" />
                                      ) : (
                                        <Trash2 className="h-4 w-4" />
                                      )}
                                    </Button>
                                  </div>

                                  {/* Price */}
                                  <div className="flex items-center gap-2 mb-4">
                                    <span className="text-xl font-bold text-primary">
                                      ₹{item.productPrice.toLocaleString()}
                                    </span>
                                    {item.originalPrice && item.originalPrice > item.productPrice && (
                                      <span className="text-sm text-gray-500 line-through">
                                        ₹{item.originalPrice.toLocaleString()}
                                      </span>
                                    )}
                                  </div>

                                  {/* Actions */}
                                  <div className="flex gap-2">
                                    <Button
                                      onClick={() => handleMoveToCart(item.id)}
                                      disabled={updating === item.id}
                                      size="sm"
                                    >
                                      <ShoppingCart className="h-4 w-4 mr-2" />
                                      {cartT.moveToCart}
                                    </Button>
                                  </div>
                                </div>
                              </div>
                            </CardContent>
                          </Card>
                        ))}
                      </div>
                    )}
                  </TabsContent>
                </div>

                {/* Order Summary */}
                {displayTotalItems > 0 && activeTab === 'cart' && (
                  <div className="lg:col-span-1">
                    <Card className="sticky top-4">
                      <CardHeader className="pb-3">
                        <CardTitle className="flex items-center gap-2 text-base">
                          <Package className="h-4 w-4" />
                          {cartT.orderSummary}
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-3 pt-0">
                        {/* Promo Code */}
                        <div>
                          <label className="text-sm font-medium text-gray-700 mb-2 block">
                            {cartT.promoCode}
                          </label>
                          <div className="flex gap-2">
                            <Input
                              placeholder={cartT.enterCode}
                              value={promoCode}
                              onChange={(e) => setPromoCode(e.target.value)}
                              className="flex-1 text-sm h-9"
                            />
                            <Button variant="outline" size="sm" className="h-9 px-3 text-sm">
                              {cartT.apply}
                            </Button>
                          </div>
                        </div>

                        <Separator />

                        {/* Summary Details */}
                        <div className="space-y-2">
                          <div className="flex justify-between text-sm">
                            <span>{cartT.subtotal} ({displayTotalItems} {displayTotalItems === 1 ? cartT.item : cartT.items})</span>
                            <span>₹{displayTotal.toLocaleString()}</span>
                          </div>

                          {isAuthenticated && cartSummary.totalDiscount > 0 && (
                            <div className="flex justify-between text-sm text-green-600">
                              <span>{cartT.discount}</span>
                              <span>-₹{cartSummary.totalDiscount.toLocaleString()}</span>
                            </div>
                          )}

                          <div className="flex justify-between text-sm">
                            <span>{cartT.shipping}</span>
                            <span className="text-green-600">{cartT.free}</span>
                          </div>

                          <Separator />

                          <div className="flex justify-between text-base font-bold">
                            <span>{cartT.total}</span>
                            <span className="text-primary">₹{displayTotal.toLocaleString()}</span>
                          </div>
                        </div>

                        {/* Guest User Info */}
                        {!isAuthenticated && (
                          <div className="p-2 bg-blue-50 border border-blue-200 rounded-lg">
                            <p className="text-xs text-blue-800">
                              <strong>{cartT.guestCart}:</strong> {cartT.guestCartNote}
                            </p>
                          </div>
                        )}

                        {/* Checkout Button */}
                        {isAuthenticated ? (
                          <Link href="/checkout">
                            <Button className="w-full h-10">
                              <CreditCard className="h-4 w-4 mr-2" />
                              {cartT.checkout}
                            </Button>
                          </Link>
                        ) : (
                          <Link href="/login?redirect=/cart">
                            <Button className="w-full h-10">
                              <User className="h-4 w-4 mr-2" />
                              {cartT.loginToCheckout}
                            </Button>
                          </Link>
                        )}

                        {/* Trust Badges */}
                        <div className="grid grid-cols-2 gap-2 pt-3 border-t">
                          <div className="flex items-center gap-1 text-xs text-gray-600">
                            <Shield className="h-3 w-3 text-green-600" />
                            <span>{cartT.secure}</span>
                          </div>
                          <div className="flex items-center gap-1 text-xs text-gray-600">
                            <Truck className="h-3 w-3 text-blue-600" />
                            <span>{cartT.freeShip}</span>
                          </div>
                          <div className="flex items-center gap-1 text-xs text-gray-600">
                            <Gift className="h-3 w-3 text-purple-600" />
                            <span>{cartT.giftWrap}</span>
                          </div>
                          <div className="flex items-center gap-1 text-xs text-gray-600">
                            <Percent className="h-3 w-3 text-orange-600" />
                            <span>{cartT.bestPrice}</span>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                )}
              </div>
            </Tabs>
          )}
        </div>
      </section>

      <Footer />
    </div>
  )
}
