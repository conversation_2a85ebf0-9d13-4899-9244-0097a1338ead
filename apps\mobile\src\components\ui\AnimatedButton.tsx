import React, { useRef } from 'react';
import {
  TouchableOpacity,
  Text,
  StyleSheet,
  Animated,
  ViewStyle,
  TextStyle,
  ActivityIndicator,
  View,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import * as Haptics from 'expo-haptics';
import { useTheme } from '../../providers/ThemeProvider';

interface AnimatedButtonProps {
  title: string;
  onPress: () => void;
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'danger';
  size?: 'small' | 'medium' | 'large';
  disabled?: boolean;
  loading?: boolean;
  icon?: string;
  iconPosition?: 'left' | 'right';
  fullWidth?: boolean;
  style?: ViewStyle;
  textStyle?: TextStyle;
  hapticFeedback?: boolean;
  animationType?: 'scale' | 'bounce' | 'pulse' | 'slide';
}

export default function AnimatedButton({
  title,
  onPress,
  variant = 'primary',
  size = 'medium',
  disabled = false,
  loading = false,
  icon,
  iconPosition = 'left',
  fullWidth = false,
  style,
  textStyle,
  hapticFeedback = true,
  animationType = 'scale',
}: AnimatedButtonProps) {
  const { theme } = useTheme();
  const scaleAnim = useRef(new Animated.Value(1)).current;
  const pulseAnim = useRef(new Animated.Value(1)).current;
  const slideAnim = useRef(new Animated.Value(0)).current;

  const getButtonStyle = () => {
    const baseStyle: ViewStyle = {
      borderRadius: theme.borderRadius.md,
      alignItems: 'center',
      justifyContent: 'center',
      flexDirection: 'row',
    };

    // Size variations
    switch (size) {
      case 'small':
        baseStyle.paddingHorizontal = theme.spacing.sm;
        baseStyle.paddingVertical = theme.spacing.xs;
        baseStyle.minHeight = 32;
        break;
      case 'large':
        baseStyle.paddingHorizontal = theme.spacing.xl;
        baseStyle.paddingVertical = theme.spacing.md;
        baseStyle.minHeight = 56;
        break;
      default: // medium
        baseStyle.paddingHorizontal = theme.spacing.lg;
        baseStyle.paddingVertical = theme.spacing.sm;
        baseStyle.minHeight = 44;
    }

    // Variant styles
    switch (variant) {
      case 'primary':
        baseStyle.backgroundColor = disabled ? theme.colors.border : theme.colors.primary;
        break;
      case 'secondary':
        baseStyle.backgroundColor = disabled ? theme.colors.border : theme.colors.secondary;
        break;
      case 'outline':
        baseStyle.backgroundColor = 'transparent';
        baseStyle.borderWidth = 1;
        baseStyle.borderColor = disabled ? theme.colors.border : theme.colors.primary;
        break;
      case 'ghost':
        baseStyle.backgroundColor = 'transparent';
        break;
      case 'danger':
        baseStyle.backgroundColor = disabled ? theme.colors.border : theme.colors.error;
        break;
    }

    if (fullWidth) {
      baseStyle.width = '100%';
    }

    return baseStyle;
  };

  const getTextStyle = () => {
    const baseTextStyle: TextStyle = {
      fontWeight: '600',
    };

    // Size variations
    switch (size) {
      case 'small':
        baseTextStyle.fontSize = 14;
        break;
      case 'large':
        baseTextStyle.fontSize = 18;
        break;
      default: // medium
        baseTextStyle.fontSize = 16;
    }

    // Variant text colors
    switch (variant) {
      case 'primary':
      case 'secondary':
      case 'danger':
        baseTextStyle.color = disabled ? theme.colors.textSecondary : '#FFFFFF';
        break;
      case 'outline':
        baseTextStyle.color = disabled ? theme.colors.textSecondary : theme.colors.primary;
        break;
      case 'ghost':
        baseTextStyle.color = disabled ? theme.colors.textSecondary : theme.colors.text;
        break;
    }

    return baseTextStyle;
  };

  const getIconSize = () => {
    switch (size) {
      case 'small': return 16;
      case 'large': return 24;
      default: return 20;
    }
  };

  const getIconColor = () => {
    switch (variant) {
      case 'primary':
      case 'secondary':
      case 'danger':
        return disabled ? theme.colors.textSecondary : '#FFFFFF';
      case 'outline':
        return disabled ? theme.colors.textSecondary : theme.colors.primary;
      case 'ghost':
        return disabled ? theme.colors.textSecondary : theme.colors.text;
      default:
        return theme.colors.text;
    }
  };

  const handlePressIn = () => {
    if (disabled || loading) return;

    if (hapticFeedback) {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }

    switch (animationType) {
      case 'scale':
        Animated.spring(scaleAnim, {
          toValue: 0.95,
          useNativeDriver: true,
        }).start();
        break;
      case 'bounce':
        Animated.spring(scaleAnim, {
          toValue: 0.9,
          tension: 300,
          friction: 10,
          useNativeDriver: true,
        }).start();
        break;
      case 'pulse':
        Animated.sequence([
          Animated.timing(pulseAnim, {
            toValue: 1.05,
            duration: 100,
            useNativeDriver: true,
          }),
          Animated.timing(pulseAnim, {
            toValue: 1,
            duration: 100,
            useNativeDriver: true,
          }),
        ]).start();
        break;
      case 'slide':
        Animated.timing(slideAnim, {
          toValue: 2,
          duration: 100,
          useNativeDriver: true,
        }).start();
        break;
    }
  };

  const handlePressOut = () => {
    if (disabled || loading) return;

    switch (animationType) {
      case 'scale':
      case 'bounce':
        Animated.spring(scaleAnim, {
          toValue: 1,
          useNativeDriver: true,
        }).start();
        break;
      case 'slide':
        Animated.timing(slideAnim, {
          toValue: 0,
          duration: 100,
          useNativeDriver: true,
        }).start();
        break;
    }
  };

  const handlePress = () => {
    if (disabled || loading) return;
    
    if (hapticFeedback) {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    }
    
    onPress();
  };

  const getAnimatedStyle = () => {
    switch (animationType) {
      case 'scale':
      case 'bounce':
        return { transform: [{ scale: scaleAnim }] };
      case 'pulse':
        return { transform: [{ scale: pulseAnim }] };
      case 'slide':
        return { transform: [{ translateX: slideAnim }] };
      default:
        return {};
    }
  };

  const renderContent = () => {
    if (loading) {
      return (
        <View style={styles.loadingContainer}>
          <ActivityIndicator 
            size="small" 
            color={getIconColor()} 
            style={styles.loadingSpinner}
          />
          <Text style={[getTextStyle(), textStyle, styles.loadingText]}>
            Loading...
          </Text>
        </View>
      );
    }

    return (
      <>
        {icon && iconPosition === 'left' && (
          <Ionicons 
            name={icon as any} 
            size={getIconSize()} 
            color={getIconColor()} 
            style={styles.iconLeft}
          />
        )}
        <Text style={[getTextStyle(), textStyle]}>
          {title}
        </Text>
        {icon && iconPosition === 'right' && (
          <Ionicons 
            name={icon as any} 
            size={getIconSize()} 
            color={getIconColor()} 
            style={styles.iconRight}
          />
        )}
      </>
    );
  };

  return (
    <Animated.View style={[getAnimatedStyle(), style]}>
      <TouchableOpacity
        style={[getButtonStyle()]}
        onPress={handlePress}
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
        disabled={disabled || loading}
        activeOpacity={0.8}
      >
        {renderContent()}
      </TouchableOpacity>
    </Animated.View>
  );
}

const styles = StyleSheet.create({
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  loadingSpinner: {
    marginRight: 8,
  },
  loadingText: {
    opacity: 0.8,
  },
  iconLeft: {
    marginRight: 8,
  },
  iconRight: {
    marginLeft: 8,
  },
});
