"use client"
import { <PERSON><PERSON> } from "@/components/ui/button";
import { TopHeader } from "@/components/top-header";
import { Head<PERSON> } from "@/components/header";
import { Footer } from "@/components/footer";
import Link from "next/link";
import { useEffect, useState } from "react";

export default function OrderPlacedPage() {
  const [tickOffset, setTickOffset] = useState(60);

  useEffect(() => {
    let frame: number;
    let start: number | null = null;
    const duration = 1000;
    function animate(now: number) {
      if (!start) start = now;
      const elapsed = now - start;
      const progress = Math.min(elapsed / duration, 1);
      setTickOffset(60 - 60 * progress);
      if (progress < 1) {
        frame = requestAnimationFrame(animate);
      }
    }
    frame = requestAnimationFrame(animate);
    return () => cancelAnimationFrame(frame);
  }, []);

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 via-white to-green-100 flex flex-col">
      <TopHeader />
      <Header />
      <main className="flex-1 flex flex-col items-center justify-center px-4 py-12">
        <div className="bg-white rounded-2xl shadow-lg p-8 max-w-lg w-full flex flex-col items-center relative overflow-hidden">
          {/* Green animated checkmark */}
          <div className="mb-6 flex flex-col items-center">
            <span className="relative inline-block">
              <svg className="w-24 h-24 text-green-500" viewBox="0 0 72 72" fill="none">
                <circle cx="36" cy="36" r="34" stroke="currentColor" strokeWidth="4" className="opacity-20 animate-ping" />
                <circle cx="36" cy="36" r="34" stroke="currentColor" strokeWidth="4" className="opacity-30" />
                <path
                  d="M22 38L32 48L50 28"
                  stroke="currentColor"
                  strokeWidth="5"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeDasharray={60}
                  strokeDashoffset={tickOffset}
                />
              </svg>
              <style>{`
                @keyframes draw-tick {
                  from { stroke-dashoffset: 60; }
                  to { stroke-dashoffset: 0; }
                }
              `}</style>
            </span>
            <h1 className="text-3xl font-extrabold text-green-700 mt-4 mb-2 text-center">Order Placed</h1>
            <p className="text-lg text-gray-700 text-center">Thank you for your order. Your Festive Shopping is on its way!</p>
          </div>
          {/* Festive confetti animation */}
          <div className="absolute inset-0 pointer-events-none z-0">
            <div className="absolute top-0 left-1/4 w-2 h-2 bg-green-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }} />
            <div className="absolute top-2 right-1/4 w-3 h-3 bg-yellow-400 rounded-full animate-bounce" style={{ animationDelay: '0.3s' }} />
            <div className="absolute bottom-4 left-1/3 w-2 h-2 bg-pink-400 rounded-full animate-bounce" style={{ animationDelay: '0.5s' }} />
            <div className="absolute bottom-2 right-1/3 w-3 h-3 bg-green-300 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }} />
            <div className="absolute top-1/2 left-1/2 w-2 h-2 bg-yellow-300 rounded-full animate-bounce" style={{ animationDelay: '0.4s' }} />
          </div>
          <div className="mt-8 w-full flex flex-col gap-3 z-10">
            <Link href="/shop" className="w-full">
              <Button className="w-full bg-green-600 hover:bg-green-700 text-white text-lg py-4 rounded-lg font-semibold shadow-sm">Continue Shopping</Button>
            </Link>
            <Link href="/" className="w-full">
              <Button variant="outline" className="w-full">Back to Home</Button>
            </Link>
          </div>
        </div>
      </main>
      <Footer />
    </div>
  );
} 