{"compilerOptions": {"target": "ES2020", "lib": ["ES2020", "DOM"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "module": "ESNext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "noEmit": false, "declaration": true, "declarationMap": true, "outDir": "dist", "rootDir": "src", "jsx": "react-jsx"}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "**/*.test.ts", "**/*.test.tsx"]}