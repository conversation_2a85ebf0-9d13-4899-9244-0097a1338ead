import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useTheme } from '../providers/ThemeProvider';
import {
  AnimatedButton,
  AnimatedCard,
  SlideUpCard,
  ScaleCard,
  FloatingActionButton,
  EnhancedLoading,
  PageTransition,
  AnimatedInput,
  PullToRefresh,
  SwipeActions,
  createDeleteAction,
  createEditAction,
  createShareAction,
  Toast,
  toastManager,
  AccessibleButton,
} from '../components/ui';

export default function UIShowcaseScreen() {
  const { theme } = useTheme();
  const [loading, setLoading] = useState(false);
  const [inputValue, setInputValue] = useState('');
  const [showToast, setShowToast] = useState(false);
  const [toastType, setToastType] = useState<'success' | 'error' | 'warning' | 'info'>('success');

  const handleRefresh = async () => {
    setLoading(true);
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 2000));
    setLoading(false);
    toastManager.success('Data refreshed successfully!');
  };

  const showToastDemo = (type: 'success' | 'error' | 'warning' | 'info') => {
    setToastType(type);
    setShowToast(true);
  };

  const fabActions = [
    {
      icon: 'add',
      label: 'Add Item',
      onPress: () => toastManager.info('Add item pressed'),
    },
    {
      icon: 'camera',
      label: 'Take Photo',
      onPress: () => toastManager.info('Camera pressed'),
    },
    {
      icon: 'document',
      label: 'Add Document',
      onPress: () => toastManager.info('Document pressed'),
    },
  ];

  const renderSection = (title: string, children: React.ReactNode) => (
    <AnimatedCard style={styles.section} animationType="slideUp">
      <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
        {title}
      </Text>
      {children}
    </AnimatedCard>
  );

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <PullToRefresh onRefresh={handleRefresh} refreshing={loading}>
        <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
          <PageTransition type="fade" duration={500}>
            <View style={styles.content}>
              {/* Header */}
              <Text style={[styles.title, { color: theme.colors.text }]}>
                UI Components Showcase
              </Text>
              <Text style={[styles.subtitle, { color: theme.colors.textSecondary }]}>
                Interactive demo of enhanced UI components
              </Text>

              {/* Buttons Section */}
              {renderSection('Animated Buttons', (
                <View style={styles.buttonGrid}>
                  <AnimatedButton
                    title="Primary"
                    onPress={() => toastManager.success('Primary button pressed!')}
                    variant="primary"
                    animationType="scale"
                  />
                  <AnimatedButton
                    title="Secondary"
                    onPress={() => toastManager.info('Secondary button pressed!')}
                    variant="secondary"
                    animationType="bounce"
                  />
                  <AnimatedButton
                    title="Outline"
                    onPress={() => toastManager.warning('Outline button pressed!')}
                    variant="outline"
                    animationType="pulse"
                  />
                  <AnimatedButton
                    title="With Icon"
                    onPress={() => toastManager.info('Icon button pressed!')}
                    variant="primary"
                    icon="heart"
                    iconPosition="left"
                  />
                  <AnimatedButton
                    title="Loading"
                    onPress={() => {}}
                    variant="primary"
                    loading={true}
                  />
                  <AnimatedButton
                    title="Disabled"
                    onPress={() => {}}
                    variant="primary"
                    disabled={true}
                  />
                </View>
              ))}

              {/* Cards Section */}
              {renderSection('Animated Cards', (
                <View style={styles.cardGrid}>
                  <SlideUpCard animationDelay={0}>
                    <Text style={[styles.cardText, { color: theme.colors.text }]}>
                      Slide Up Card
                    </Text>
                  </SlideUpCard>
                  <ScaleCard animationDelay={200}>
                    <Text style={[styles.cardText, { color: theme.colors.text }]}>
                      Scale Card
                    </Text>
                  </ScaleCard>
                  <AnimatedCard 
                    animationType="flip" 
                    animationDelay={400}
                    onPress={() => toastManager.info('Card pressed!')}
                  >
                    <Text style={[styles.cardText, { color: theme.colors.text }]}>
                      Flip Card (Tap me!)
                    </Text>
                  </AnimatedCard>
                </View>
              ))}

              {/* Loading Section */}
              {renderSection('Loading Animations', (
                <View style={styles.loadingGrid}>
                  <EnhancedLoading type="spinner" size="small" text="Spinner" />
                  <EnhancedLoading type="dots" size="small" text="Dots" />
                  <EnhancedLoading type="pulse" size="small" text="Pulse" />
                  <EnhancedLoading type="wave" size="small" text="Wave" />
                </View>
              ))}

              {/* Input Section */}
              {renderSection('Animated Inputs', (
                <View style={styles.inputContainer}>
                  <AnimatedInput
                    label="Floating Label"
                    placeholder="Type something..."
                    value={inputValue}
                    onChangeText={setInputValue}
                    animationType="float"
                    leftIcon="person"
                  />
                  <AnimatedInput
                    label="Email Address"
                    placeholder="Enter your email"
                    variant="filled"
                    leftIcon="mail"
                    keyboardType="email-address"
                  />
                  <AnimatedInput
                    label="Password"
                    placeholder="Enter password"
                    variant="underlined"
                    leftIcon="lock-closed"
                    rightIcon="eye"
                    secureTextEntry
                  />
                </View>
              ))}

              {/* Swipe Actions Section */}
              {renderSection('Swipe Actions', (
                <View style={styles.swipeContainer}>
                  <SwipeActions
                    leftActions={[createEditAction(() => toastManager.info('Edit pressed'))]}
                    rightActions={[
                      createShareAction(() => toastManager.info('Share pressed')),
                      createDeleteAction(() => toastManager.error('Delete pressed')),
                    ]}
                  >
                    <View style={[styles.swipeItem, { backgroundColor: theme.colors.surface }]}>
                      <Text style={[styles.swipeText, { color: theme.colors.text }]}>
                        Swipe left or right to see actions
                      </Text>
                    </View>
                  </SwipeActions>
                </View>
              ))}

              {/* Toast Section */}
              {renderSection('Toast Notifications', (
                <View style={styles.buttonGrid}>
                  <AnimatedButton
                    title="Success"
                    onPress={() => showToastDemo('success')}
                    variant="primary"
                    size="small"
                  />
                  <AnimatedButton
                    title="Error"
                    onPress={() => showToastDemo('error')}
                    variant="danger"
                    size="small"
                  />
                  <AnimatedButton
                    title="Warning"
                    onPress={() => showToastDemo('warning')}
                    variant="secondary"
                    size="small"
                  />
                  <AnimatedButton
                    title="Info"
                    onPress={() => showToastDemo('info')}
                    variant="outline"
                    size="small"
                  />
                </View>
              ))}

              {/* Accessibility Section */}
              {renderSection('Accessibility', (
                <View style={styles.accessibilityContainer}>
                  <AccessibleButton
                    accessibilityLabel="Accessible button example"
                    accessibilityHint="This button demonstrates accessibility features"
                    onPress={() => toastManager.success('Accessible button pressed!')}
                  >
                    <View style={[styles.accessibleButton, { backgroundColor: theme.colors.primary }]}>
                      <Text style={styles.accessibleButtonText}>
                        Accessible Button
                      </Text>
                    </View>
                  </AccessibleButton>
                </View>
              ))}

              {/* Spacer for FAB */}
              <View style={styles.fabSpacer} />
            </View>
          </PageTransition>
        </ScrollView>
      </PullToRefresh>

      {/* Floating Action Button */}
      <FloatingActionButton
        icon="add"
        actions={fabActions}
        position="bottom-right"
        animationType="scale"
      />

      {/* Toast Component */}
      <Toast
        visible={showToast}
        type={toastType}
        title="Demo Toast"
        message={`This is a ${toastType} toast notification!`}
        onHide={() => setShowToast(false)}
        position="top"
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  content: {
    padding: 16,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 24,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '600',
    marginBottom: 16,
  },
  buttonGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  cardGrid: {
    gap: 12,
  },
  cardText: {
    fontSize: 16,
    textAlign: 'center',
    padding: 8,
  },
  loadingGrid: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    paddingVertical: 20,
  },
  inputContainer: {
    gap: 16,
  },
  swipeContainer: {
    marginVertical: 8,
  },
  swipeItem: {
    padding: 20,
    borderRadius: 8,
  },
  swipeText: {
    fontSize: 16,
    textAlign: 'center',
  },
  accessibilityContainer: {
    alignItems: 'center',
  },
  accessibleButton: {
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
    minHeight: 44, // Meets accessibility guidelines
  },
  accessibleButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  fabSpacer: {
    height: 80,
  },
});
