const fs = require('fs');
const path = require('path');

// Add missing common translations
const commonTranslations = {
  en: {
    "save": "Save",
    "cancel": "Cancel",
    "delete": "Delete",
    "edit": "Edit",
    "update": "Update",
    "add": "Add",
    "remove": "Remove",
    "search": "Search",
    "filter": "Filter",
    "sort": "Sort",
    "next": "Next",
    "previous": "Previous",
    "close": "Close",
    "open": "Open",
    "yes": "Yes",
    "no": "No",
    "ok": "OK",
    "favorites": "Favorites"
  },
  ta: {
    "save": "சேமிக்கவும்",
    "cancel": "ரத்து செய்யவும்",
    "delete": "நீக்கவும்",
    "edit": "திருத்தவும்",
    "update": "புதுப்பிக்கவும்",
    "add": "சேர்க்கவும்",
    "remove": "அகற்றவும்",
    "search": "தேடவும்",
    "filter": "வடிகட்டவும்",
    "sort": "வரிசைப்படுத்தவும்",
    "next": "அடுத்து",
    "previous": "முந்தைய",
    "close": "மூடவும்",
    "open": "திறக்கவும்",
    "yes": "ஆம்",
    "no": "இல்லை",
    "ok": "சரி",
    "favorites": "விருப்பங்கள்"
  },
  hi: {
    "save": "सेव करें",
    "cancel": "रद्द करें",
    "delete": "डिलीट करें",
    "edit": "एडिट करें",
    "update": "अपडेट करें",
    "add": "जोड़ें",
    "remove": "हटाएं",
    "search": "खोजें",
    "filter": "फिल्टर करें",
    "sort": "सॉर्ट करें",
    "next": "अगला",
    "previous": "पिछला",
    "close": "बंद करें",
    "open": "खोलें",
    "yes": "हां",
    "no": "नहीं",
    "ok": "ठीक है",
    "favorites": "पसंदीदा"
  },
  te: {
    "save": "సేవ్ చేయండి",
    "cancel": "రద్దు చేయండి",
    "delete": "డిలీట్ చేయండి",
    "edit": "ఎడిట్ చేయండి",
    "update": "అప్‌డేట్ చేయండి",
    "add": "జోడించండి",
    "remove": "తీసివేయండి",
    "search": "వెతకండి",
    "filter": "ఫిల్టర్ చేయండి",
    "sort": "సార్ట్ చేయండి",
    "next": "తదుపరి",
    "previous": "మునుపటి",
    "close": "మూసివేయండి",
    "open": "తెరవండి",
    "yes": "అవును",
    "no": "లేదు",
    "ok": "సరే",
    "favorites": "ఇష్టమైనవి"
  },
  kn: {
    "save": "ಸೇವ್ ಮಾಡಿ",
    "cancel": "ರದ್ದುಮಾಡಿ",
    "delete": "ಡಿಲೀಟ್ ಮಾಡಿ",
    "edit": "ಎಡಿಟ್ ಮಾಡಿ",
    "update": "ಅಪ್‌ಡೇಟ್ ಮಾಡಿ",
    "add": "ಸೇರಿಸಿ",
    "remove": "ತೆಗೆದುಹಾಕಿ",
    "search": "ಹುಡುಕಿ",
    "filter": "ಫಿಲ್ಟರ್ ಮಾಡಿ",
    "sort": "ಸಾರ್ಟ್ ಮಾಡಿ",
    "next": "ಮುಂದೆ",
    "previous": "ಹಿಂದೆ",
    "close": "ಮುಚ್ಚಿ",
    "open": "ತೆರೆಯಿರಿ",
    "yes": "ಹೌದು",
    "no": "ಇಲ್ಲ",
    "ok": "ಸರಿ",
    "favorites": "ಮೆಚ್ಚಿನವುಗಳು"
  },
  ml: {
    "save": "സേവ് ചെയ്യുക",
    "cancel": "റദ്ദാക്കുക",
    "delete": "ഡിലീറ്റ് ചെയ്യുക",
    "edit": "എഡിറ്റ് ചെയ്യുക",
    "update": "അപ്ഡേറ്റ് ചെയ്യുക",
    "add": "ചേർക്കുക",
    "remove": "നീക്കം ചെയ്യുക",
    "search": "തിരയുക",
    "filter": "ഫിൽട്ടർ ചെയ്യുക",
    "sort": "സോർട്ട് ചെയ്യുക",
    "next": "അടുത്തത്",
    "previous": "മുമ്പത്തെ",
    "close": "അടയ്ക്കുക",
    "open": "തുറക്കുക",
    "yes": "അതെ",
    "no": "ഇല്ല",
    "ok": "ശരി",
    "favorites": "പ്രിയങ്കരങ്ങൾ"
  }
};

// Update common.json files
const localesDir = path.join(__dirname, '../public/locales');
const languages = ['en', 'ta', 'hi', 'te', 'kn', 'ml', 'gu', 'mr', 'bn', 'or', 'as', 'ur', 'ne', 'pa'];

languages.forEach(lang => {
  const langDir = path.join(localesDir, lang);
  const commonFile = path.join(langDir, 'common.json');
  
  try {
    // Read existing common.json
    let existingCommon = {};
    if (fs.existsSync(commonFile)) {
      existingCommon = JSON.parse(fs.readFileSync(commonFile, 'utf8'));
    }
    
    // Add missing common translations
    const newTranslations = commonTranslations[lang] || commonTranslations.en;
    if (!existingCommon.common) {
      existingCommon.common = {};
    }
    
    // Merge new translations
    Object.keys(newTranslations).forEach(key => {
      if (!existingCommon.common[key]) {
        existingCommon.common[key] = newTranslations[key];
      }
    });
    
    // Write updated file
    fs.writeFileSync(commonFile, JSON.stringify(existingCommon, null, 2), 'utf8');
    console.log(`✅ Updated common.json for ${lang}`);
  } catch (error) {
    console.error(`❌ Failed to update common.json for ${lang}:`, error.message);
  }
});

console.log('\n🎉 All translations updated successfully!');