const withBundleAnalyzer = require('@next/bundle-analyzer')({
  enabled: process.env.ANALYZE === 'true',
})

// Temporarily disable PWA to avoid build issues
// We'll implement manual service worker instead
const withPWA = (config) => config

// Log build configuration
if (process.env.NODE_ENV === 'production' && process.env.KEEP_CONSOLE !== 'true') {
  console.log('🔧 Console removal active for production build (SWC)')
  console.log('   • console.log, console.info, console.debug, console.warn will be removed')
  console.log('   • console.error will be preserved')
  console.log('   • Set KEEP_CONSOLE=true to disable console removal')
} else {
  console.log('🔧 Console removal disabled (development mode or KEEP_CONSOLE=true)')
}

/** @type {import('next').NextConfig} */
const nextConfig = {
  eslint: {
    ignoreDuringBuilds: true,
  },
  typescript: {
    ignoreBuildErrors: true,
  },
  images: {
    remotePatterns: [

      {
        protocol: 'https',
        hostname: 'via.placeholder.com',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'picsum.photos',
        port: '',
        pathname: '/**',
      },

      {
        protocol: 'http',
        hostname: 'localhost',
        port: '3000',
        pathname: '/**',
      },
      // Google profile images
      {
        protocol: 'https',
        hostname: 'lh3.googleusercontent.com',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'lh4.googleusercontent.com',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'lh5.googleusercontent.com',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'lh6.googleusercontent.com',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'images.unsplash.com',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'images.unsplash.com',
        port: '',
        pathname: '/**',
      }
    ],
    // Optimize image loading
    deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048],
    imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
    formats: ['image/webp', 'image/avif'],
    minimumCacheTTL: 60 * 60 * 24 * 30, // 30 days cache
    dangerouslyAllowSVG: false,
    contentSecurityPolicy: "default-src 'self'; script-src 'none'; sandbox;",
    // Enable Next.js image optimization
    unoptimized: false,
  },
  // Enable compression for better performance
  compress: true,
  // Optimize production builds
  swcMinify: true,
  // Configure SWC to remove console logs in production (disabled for Turbopack compatibility)
  compiler: process.env.NODE_ENV === 'production' && process.env.KEEP_CONSOLE !== 'true' ? {
    removeConsole: {
      exclude: ['error'] // Keep console.error for debugging
    }
  } : {},
  // Reduce the impact of third-party scripts and optimize performance
  experimental: {
    optimizeCss: process.env.NODE_ENV === 'production',
    scrollRestoration: true,
    // Enable faster development builds
    turbo: {
      // Enable Turbopack for faster development
      rules: {
        '*.svg': {
          loaders: ['@svgr/webpack'],
          as: '*.js',
        },
      },
    },
    // Optimize development builds
    ...(process.env.NODE_ENV === 'development' && {
      // Faster development builds
      optimizePackageImports: ['lucide-react', '@radix-ui/react-icons'],
      // Reduce memory usage in development
      memoryBasedWorkers: true,
    }),
  },
  // Improve page load performance
  reactStrictMode: true,
  poweredByHeader: false,
  // Optimize webpack for better performance
  webpack: (config, { isServer, dev }) => {
    // Development optimizations
    if (dev) {
      // Faster development builds
      config.optimization.removeAvailableModules = false;
      config.optimization.removeEmptyChunks = false;
      config.optimization.splitChunks = false;

      // Reduce memory usage
      config.cache = {
        type: 'memory',
      };

      // Faster rebuilds
      config.watchOptions = {
        poll: false,
        aggregateTimeout: 300,
        ignored: /node_modules/,
      };
    } else {
      // Production optimizations
      config.optimization.splitChunks = {
        chunks: 'all',
        cacheGroups: {
          vendor: {
            test: /[\\/]node_modules[\\/]/,
            name: 'vendors',
            chunks: 'all',
          },
          aws: {
            test: /[\\/]node_modules[\\/](@aws-amplify|aws-amplify)[\\/]/,
            name: 'aws',
            chunks: 'all',
          },
          ui: {
            test: /[\\/]node_modules[\\/](@radix-ui|@mui)[\\/]/,
            name: 'ui',
            chunks: 'all',
          },
        },
      };
    }

    return config;
  },
}

module.exports = withBundleAnalyzer(withPWA(nextConfig))