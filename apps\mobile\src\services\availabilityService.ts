import { generateClient } from 'aws-amplify/api';
import { getCurrentUser } from 'aws-amplify/auth';
import { listBookings } from '../graphql/queries';

const client = generateClient();

export interface AvailabilityCheck {
  entityId: string;
  entityType: 'VENDOR' | 'VENUE';
  eventDate: string;
  eventTime?: string;
  duration?: string;
}

export interface AvailabilityResult {
  isAvailable: boolean;
  conflictingBookings: BookingConflict[];
  message: string;
  suggestedDates?: string[];
}

export interface BookingConflict {
  bookingId: string;
  customerName: string;
  eventDate: string;
  eventTime: string;
  status: string;
  duration?: string;
}

export class AvailabilityService {
  /**
   * Check if vendor/venue is available for a specific date and time
   */
  static async checkAvailability(params: AvailabilityCheck): Promise<AvailabilityResult> {
    try {
      // Check if user is authenticated
      const user = await getCurrentUser().catch(() => null);

      if (!user) {
        // For non-authenticated users, return mock availability
        console.log('User not authenticated, using mock availability');
        return this.getMockAvailability(params);
      }

      // Try to get all confirmed bookings for the entity on the requested date
      // Use apiKey auth mode as fallback if userPool fails
      let bookingsResult;
      try {
        bookingsResult = await client.graphql({
          query: listBookings,
          variables: {
            filter: {
              entityId: { eq: params.entityId },
              eventDate: { eq: params.eventDate },
              or: [
                { status: { eq: 'CONFIRMED' } },
                { status: { eq: 'PENDING' } },
                { status: { eq: 'RESERVED' } }
              ]
            }
          },
          authMode: 'userPool'
        });
      } catch (authError: any) {
        console.warn('UserPool auth failed, trying with apiKey:', authError.message);

        // Fallback to apiKey auth mode
        try {
          bookingsResult = await client.graphql({
            query: listBookings,
            variables: {
              filter: {
                entityId: { eq: params.entityId },
                eventDate: { eq: params.eventDate },
                or: [
                  { status: { eq: 'CONFIRMED' } },
                  { status: { eq: 'PENDING' } },
                  { status: { eq: 'RESERVED' } }
                ]
              }
            },
            authMode: 'apiKey'
          });
        } catch (apiKeyError: any) {
          console.warn('ApiKey auth also failed, using mock data:', apiKeyError.message);
          return this.getMockAvailability(params);
        }
      }

      const existingBookings = bookingsResult.data.listBookings.items || [];

      // Check for time conflicts if time is specified
      const conflictingBookings = this.findTimeConflicts(
        existingBookings,
        params.eventTime,
        params.duration
      );

      const isAvailable = conflictingBookings.length === 0;

      return {
        isAvailable,
        conflictingBookings,
        message: isAvailable 
          ? `Available for booking on ${params.eventDate}${params.eventTime ? ` at ${params.eventTime}` : ''}`
          : `Not available - ${conflictingBookings.length} conflicting booking(s) found`,
        suggestedDates: isAvailable ? [] : this.generateSuggestedDates(params.eventDate)
      };

    } catch (error: any) {
      console.error('Error checking availability:', error);
      
      // If there's an authorization error, fall back to mock data
      if (error.message?.includes('authorization') || error.message?.includes('Unauthorized')) {
        console.warn('Authorization error, falling back to mock availability check');
        return this.getMockAvailability(params);
      }
      
      throw new Error(`Failed to check availability: ${error.message}`);
    }
  }

  /**
   * Find time conflicts between existing bookings and requested time
   */
  private static findTimeConflicts(
    existingBookings: any[],
    requestedTime?: string,
    requestedDuration?: string
  ): BookingConflict[] {
    if (!requestedTime) {
      // If no specific time requested, just return any existing bookings as potential conflicts
      return existingBookings.map(booking => ({
        bookingId: booking.id,
        customerName: booking.customerName || 'Customer',
        eventDate: booking.eventDate,
        eventTime: booking.eventTime || 'All day',
        status: booking.status,
        duration: booking.duration
      }));
    }

    const conflicts: BookingConflict[] = [];
    const requestedStart = this.parseTime(requestedTime);
    const requestedEnd = this.addDuration(requestedStart, requestedDuration || '4 hours');

    for (const booking of existingBookings) {
      if (!booking.eventTime) continue;

      const bookingStart = this.parseTime(booking.eventTime);
      const bookingEnd = this.addDuration(bookingStart, booking.duration || '4 hours');

      // Check for overlap
      if (this.timesOverlap(requestedStart, requestedEnd, bookingStart, bookingEnd)) {
        conflicts.push({
          bookingId: booking.id,
          customerName: booking.customerName || 'Customer',
          eventDate: booking.eventDate,
          eventTime: booking.eventTime,
          status: booking.status,
          duration: booking.duration
        });
      }
    }

    return conflicts;
  }

  /**
   * Parse time string to minutes since midnight
   */
  private static parseTime(timeStr: string): number {
    const [hours, minutes] = timeStr.split(':').map(Number);
    return hours * 60 + (minutes || 0);
  }

  /**
   * Add duration to time in minutes
   */
  private static addDuration(startMinutes: number, duration: string): number {
    const hours = parseInt(duration.match(/(\d+)\s*h/)?.[1] || '4');
    return startMinutes + (hours * 60);
  }

  /**
   * Check if two time ranges overlap
   */
  private static timesOverlap(
    start1: number, end1: number,
    start2: number, end2: number
  ): boolean {
    return start1 < end2 && start2 < end1;
  }

  /**
   * Generate suggested alternative dates
   */
  private static generateSuggestedDates(requestedDate: string): string[] {
    const date = new Date(requestedDate);
    const suggestions: string[] = [];

    // Suggest next 5 days (excluding the requested date)
    for (let i = 1; i <= 5; i++) {
      const nextDate = new Date(date);
      nextDate.setDate(date.getDate() + i);
      suggestions.push(nextDate.toISOString().split('T')[0]);
    }

    return suggestions;
  }

  /**
   * Mock availability check for non-authenticated users or fallback
   */
  private static getMockAvailability(params: AvailabilityCheck): AvailabilityResult {
    // Simulate realistic availability based on date and time
    const date = new Date(params.eventDate);
    const isWeekend = date.getDay() === 0 || date.getDay() === 6;
    const isPopularTime = params.eventTime && 
      params.eventTime >= '10:00' && params.eventTime <= '18:00';

    // Higher chance of conflicts on weekends and popular times
    const conflictChance = isWeekend && isPopularTime ? 0.4 : 0.2;
    const hasConflict = Math.random() < conflictChance;

    const conflictingBookings: BookingConflict[] = hasConflict ? [{
      bookingId: 'mock-booking-' + Date.now(),
      customerName: 'Another Customer',
      eventDate: params.eventDate,
      eventTime: params.eventTime || '14:00',
      status: 'CONFIRMED',
      duration: '4 hours'
    }] : [];

    return {
      isAvailable: !hasConflict,
      conflictingBookings,
      message: hasConflict 
        ? `Not available at the selected time - conflicting booking exists`
        : `Available for booking on ${params.eventDate}${params.eventTime ? ` at ${params.eventTime}` : ''}`,
      suggestedDates: hasConflict ? this.generateSuggestedDates(params.eventDate) : []
    };
  }

  /**
   * Check general availability for a date (without specific time)
   */
  static async checkGeneralAvailability(
    entityId: string,
    entityType: 'VENDOR' | 'VENUE',
    eventDate: string
  ): Promise<AvailabilityResult> {
    return this.checkAvailability({
      entityId,
      entityType,
      eventDate
    });
  }

  /**
   * Check if a user can book (considering existing bookings)
   */
  static async canUserBook(
    entityId: string,
    entityType: 'VENDOR' | 'VENUE',
    eventDate: string,
    userId: string
  ): Promise<{
    canBook: boolean;
    reason: string;
    message: string;
    bookingId?: string;
    status?: string;
    conflictingBookings?: BookingConflict[];
  }> {
    try {
      const user = await getCurrentUser().catch(() => null);
      
      if (!user) {
        return {
          canBook: false,
          reason: 'AUTHENTICATION_REQUIRED',
          message: 'Please log in to check booking availability'
        };
      }

      // Check if user already has a booking for this entity on this date
      const userBookingsResult = await client.graphql({
        query: listBookings,
        variables: {
          filter: {
            customerId: { eq: userId },
            entityId: { eq: entityId },
            eventDate: { eq: eventDate },
            status: { ne: 'CANCELLED' }
          }
        },
        authMode: 'userPool'
      });

      const existingUserBooking = userBookingsResult.data.listBookings.items?.[0];

      if (existingUserBooking) {
        return {
          canBook: false,
          reason: 'EXISTING_BOOKING',
          message: `You already have a ${existingUserBooking.status.toLowerCase()} booking for this date`,
          bookingId: existingUserBooking.id,
          status: existingUserBooking.status
        };
      }

      // Check general availability
      const availability = await this.checkGeneralAvailability(entityId, entityType, eventDate);

      return {
        canBook: availability.isAvailable,
        reason: availability.isAvailable ? 'AVAILABLE' : 'NOT_AVAILABLE',
        message: availability.message,
        conflictingBookings: availability.conflictingBookings
      };

    } catch (error: any) {
      console.error('Error checking user booking status:', error);
      
      // Fallback for authorization errors
      if (error.message?.includes('authorization') || error.message?.includes('Unauthorized')) {
        return {
          canBook: true,
          reason: 'MOCK_AVAILABLE',
          message: 'Availability check unavailable - please proceed with booking'
        };
      }
      
      return {
        canBook: false,
        reason: 'ERROR',
        message: 'Unable to check booking status. Please try again.'
      };
    }
  }
}
