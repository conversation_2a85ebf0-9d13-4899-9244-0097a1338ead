import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Alert,
  Image,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../providers/ThemeProvider';
import { useCart } from '../providers/CartProvider';
import { useAuth } from '../providers/AuthProvider';
import { paymentService, PaymentDetails } from '../services/paymentService';

interface ShippingAddress {
  fullName: string;
  phone: string;
  addressLine1: string;
  addressLine2: string;
  city: string;
  state: string;
  pincode: string;
}

export default function CheckoutScreen() {
  const navigation = useNavigation();
  const { theme } = useTheme();
  const { cart, clearCart } = useCart();
  const { user } = useAuth();

  const [shippingAddress, setShippingAddress] = useState<ShippingAddress>({
    fullName: `${user?.firstName || ''} ${user?.lastName || ''}`.trim(),
    phone: user?.phone || '',
    addressLine1: '',
    addressLine2: '',
    city: '',
    state: '',
    pincode: '',
  });

  const [paymentMethod, setPaymentMethod] = useState<'card' | 'upi' | 'netbanking' | 'cod'>('card');
  const [loading, setLoading] = useState(false);

  const deliveryCharge = cart.total > 1000 ? 0 : 50;
  const tax = Math.round(cart.total * 0.18); // 18% GST
  const finalTotal = cart.total + deliveryCharge + tax;

  const paymentMethods = [
    { id: 'card', name: 'Credit/Debit Card', icon: 'card-outline' },
    { id: 'upi', name: 'UPI Payment', icon: 'phone-portrait-outline' },
    { id: 'netbanking', name: 'Net Banking', icon: 'globe-outline' },
    { id: 'cod', name: 'Cash on Delivery', icon: 'cash-outline' },
  ];

  const handleAddressChange = (field: keyof ShippingAddress, value: string) => {
    setShippingAddress(prev => ({ ...prev, [field]: value }));
  };

  const validateAddress = (): boolean => {
    const required = ['fullName', 'phone', 'addressLine1', 'city', 'state', 'pincode'];
    for (const field of required) {
      if (!shippingAddress[field as keyof ShippingAddress].trim()) {
        Alert.alert('Error', `Please fill in ${field.replace(/([A-Z])/g, ' $1').toLowerCase()}`);
        return false;
      }
    }
    return true;
  };

  const handlePlaceOrder = async () => {
    if (!validateAddress()) return;

    try {
      setLoading(true);

      // Prepare payment details
      const paymentDetails: PaymentDetails = {
        amount: finalTotal,
        currency: 'INR',
        orderId: `ORDER_${Date.now()}`,
        customerEmail: user?.email || '',
        customerPhone: shippingAddress.phone,
        description: `Wedding items order - ${cart.items.length} items`,
      };

      let paymentResult;

      // Process payment based on selected method
      if (paymentMethod === 'card') {
        // For demo purposes, we'll simulate card payment
        // In a real app, you'd collect card details from user
        paymentResult = await paymentService.processCardPayment(paymentDetails, {
          cardNumber: '****************', // Test card
          expiryMonth: '12',
          expiryYear: '2025',
          cvv: '123',
          cardholderName: shippingAddress.fullName,
        });
      } else if (paymentMethod === 'upi') {
        paymentResult = await paymentService.processUPIPayment(paymentDetails, {
          vpa: 'user@paytm', // Demo UPI ID
        });
      } else if (paymentMethod === 'netbanking') {
        paymentResult = await paymentService.processNetBankingPayment(paymentDetails, 'SBI');
      } else {
        // Cash on Delivery
        paymentResult = await paymentService.processCODPayment(paymentDetails);
      }

      if (!paymentResult.success) {
        Alert.alert('Payment Failed', paymentResult.error || 'Payment processing failed');
        return;
      }

      // Create order object
      const order = {
        id: paymentDetails.orderId,
        items: cart.items,
        shippingAddress,
        paymentMethod,
        subtotal: cart.total,
        deliveryCharge,
        tax,
        total: finalTotal,
        status: 'confirmed',
        transactionId: paymentResult.transactionId,
        createdAt: new Date().toISOString(),
      };

      // TODO: Save order to database via GraphQL
      console.log('Order created:', order);

      // Clear cart
      await clearCart();

      Alert.alert(
        'Order Placed Successfully!',
        `Your order #${order.id} has been placed successfully. Transaction ID: ${paymentResult.transactionId}`,
        [
          {
            text: 'View Orders',
            onPress: () => {
              navigation.reset({
                index: 0,
                routes: [{ name: 'MainTabs' as never }],
              });
              navigation.navigate('Orders' as never);
            },
          },
          {
            text: 'Continue Shopping',
            onPress: () => {
              navigation.reset({
                index: 0,
                routes: [{ name: 'MainTabs' as never }],
              });
            },
          },
        ]
      );

    } catch (error) {
      console.error('Error placing order:', error);
      Alert.alert('Error', 'Failed to place order. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <ScrollView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {/* Order Summary */}
      <View style={[styles.section, { backgroundColor: theme.colors.surface }]}>
        <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
          Order Summary ({cart.items.length} items)
        </Text>
        {cart.items.map((item) => (
          <View key={item.id} style={styles.orderItem}>
            <Image
              source={{ uri: item.image || 'https://via.placeholder.com/60x60' }}
              style={styles.itemImage}
            />
            <View style={styles.itemDetails}>
              <Text style={[styles.itemName, { color: theme.colors.text }]} numberOfLines={2}>
                {item.name}
              </Text>
              <Text style={[styles.itemPrice, { color: theme.colors.textSecondary }]}>
                ₹{item.price.toLocaleString()} × {item.quantity}
              </Text>
            </View>
            <Text style={[styles.itemTotal, { color: theme.colors.text }]}>
              ₹{(item.price * item.quantity).toLocaleString()}
            </Text>
          </View>
        ))}
      </View>

      {/* Shipping Address */}
      <View style={[styles.section, { backgroundColor: theme.colors.surface }]}>
        <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
          Shipping Address
        </Text>
        
        <View style={styles.inputGroup}>
          <Text style={[styles.inputLabel, { color: theme.colors.text }]}>Full Name *</Text>
          <TextInput
            style={[styles.input, { backgroundColor: theme.colors.background, color: theme.colors.text }]}
            value={shippingAddress.fullName}
            onChangeText={(text) => handleAddressChange('fullName', text)}
            placeholder="Enter full name"
            placeholderTextColor={theme.colors.textSecondary}
          />
        </View>

        <View style={styles.inputGroup}>
          <Text style={[styles.inputLabel, { color: theme.colors.text }]}>Phone Number *</Text>
          <TextInput
            style={[styles.input, { backgroundColor: theme.colors.background, color: theme.colors.text }]}
            value={shippingAddress.phone}
            onChangeText={(text) => handleAddressChange('phone', text)}
            placeholder="Enter phone number"
            placeholderTextColor={theme.colors.textSecondary}
            keyboardType="phone-pad"
          />
        </View>

        <View style={styles.inputGroup}>
          <Text style={[styles.inputLabel, { color: theme.colors.text }]}>Address Line 1 *</Text>
          <TextInput
            style={[styles.input, { backgroundColor: theme.colors.background, color: theme.colors.text }]}
            value={shippingAddress.addressLine1}
            onChangeText={(text) => handleAddressChange('addressLine1', text)}
            placeholder="House/Flat number, Street name"
            placeholderTextColor={theme.colors.textSecondary}
          />
        </View>

        <View style={styles.inputGroup}>
          <Text style={[styles.inputLabel, { color: theme.colors.text }]}>Address Line 2</Text>
          <TextInput
            style={[styles.input, { backgroundColor: theme.colors.background, color: theme.colors.text }]}
            value={shippingAddress.addressLine2}
            onChangeText={(text) => handleAddressChange('addressLine2', text)}
            placeholder="Landmark, Area (Optional)"
            placeholderTextColor={theme.colors.textSecondary}
          />
        </View>

        <View style={styles.row}>
          <View style={[styles.inputGroup, { flex: 1, marginRight: 8 }]}>
            <Text style={[styles.inputLabel, { color: theme.colors.text }]}>City *</Text>
            <TextInput
              style={[styles.input, { backgroundColor: theme.colors.background, color: theme.colors.text }]}
              value={shippingAddress.city}
              onChangeText={(text) => handleAddressChange('city', text)}
              placeholder="City"
              placeholderTextColor={theme.colors.textSecondary}
            />
          </View>
          <View style={[styles.inputGroup, { flex: 1, marginLeft: 8 }]}>
            <Text style={[styles.inputLabel, { color: theme.colors.text }]}>State *</Text>
            <TextInput
              style={[styles.input, { backgroundColor: theme.colors.background, color: theme.colors.text }]}
              value={shippingAddress.state}
              onChangeText={(text) => handleAddressChange('state', text)}
              placeholder="State"
              placeholderTextColor={theme.colors.textSecondary}
            />
          </View>
        </View>

        <View style={styles.inputGroup}>
          <Text style={[styles.inputLabel, { color: theme.colors.text }]}>Pincode *</Text>
          <TextInput
            style={[styles.input, { backgroundColor: theme.colors.background, color: theme.colors.text }]}
            value={shippingAddress.pincode}
            onChangeText={(text) => handleAddressChange('pincode', text)}
            placeholder="Enter pincode"
            placeholderTextColor={theme.colors.textSecondary}
            keyboardType="numeric"
            maxLength={6}
          />
        </View>
      </View>

      {/* Payment Method */}
      <View style={[styles.section, { backgroundColor: theme.colors.surface }]}>
        <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
          Payment Method
        </Text>
        {paymentMethods.map((method) => (
          <TouchableOpacity
            key={method.id}
            style={[
              styles.paymentMethod,
              {
                borderColor: paymentMethod === method.id ? theme.colors.primary : theme.colors.border,
                backgroundColor: paymentMethod === method.id ? theme.colors.primary + '10' : 'transparent',
              }
            ]}
            onPress={() => setPaymentMethod(method.id as any)}
          >
            <View style={styles.paymentMethodLeft}>
              <Ionicons
                name={method.icon as any}
                size={24}
                color={paymentMethod === method.id ? theme.colors.primary : theme.colors.textSecondary}
              />
              <Text style={[
                styles.paymentMethodText,
                {
                  color: paymentMethod === method.id ? theme.colors.primary : theme.colors.text
                }
              ]}>
                {method.name}
              </Text>
            </View>
            <View style={[
              styles.radioButton,
              {
                borderColor: paymentMethod === method.id ? theme.colors.primary : theme.colors.border,
                backgroundColor: paymentMethod === method.id ? theme.colors.primary : 'transparent',
              }
            ]}>
              {paymentMethod === method.id && (
                <View style={styles.radioButtonInner} />
              )}
            </View>
          </TouchableOpacity>
        ))}
      </View>

      {/* Price Breakdown */}
      <View style={[styles.section, { backgroundColor: theme.colors.surface }]}>
        <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
          Price Details
        </Text>
        <View style={styles.priceRow}>
          <Text style={[styles.priceLabel, { color: theme.colors.text }]}>
            Subtotal ({cart.items.length} items)
          </Text>
          <Text style={[styles.priceValue, { color: theme.colors.text }]}>
            ₹{cart.total.toLocaleString()}
          </Text>
        </View>
        <View style={styles.priceRow}>
          <Text style={[styles.priceLabel, { color: theme.colors.text }]}>
            Delivery Charges
          </Text>
          <Text style={[styles.priceValue, { color: deliveryCharge === 0 ? theme.colors.success : theme.colors.text }]}>
            {deliveryCharge === 0 ? 'FREE' : `₹${deliveryCharge}`}
          </Text>
        </View>
        <View style={styles.priceRow}>
          <Text style={[styles.priceLabel, { color: theme.colors.text }]}>
            Tax (GST 18%)
          </Text>
          <Text style={[styles.priceValue, { color: theme.colors.text }]}>
            ₹{tax.toLocaleString()}
          </Text>
        </View>
        <View style={[styles.priceRow, styles.totalRow]}>
          <Text style={[styles.totalLabel, { color: theme.colors.text }]}>
            Total Amount
          </Text>
          <Text style={[styles.totalValue, { color: theme.colors.primary }]}>
            ₹{finalTotal.toLocaleString()}
          </Text>
        </View>
      </View>

      {/* Place Order Button */}
      <View style={styles.footer}>
        <TouchableOpacity
          style={[styles.placeOrderButton, { backgroundColor: theme.colors.primary }]}
          onPress={handlePlaceOrder}
          disabled={loading}
        >
          <Text style={styles.placeOrderText}>
            {loading ? 'Placing Order...' : `Place Order - ₹${finalTotal.toLocaleString()}`}
          </Text>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  section: {
    margin: 16,
    padding: 16,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
  },
  orderItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  itemImage: {
    width: 60,
    height: 60,
    borderRadius: 8,
    marginRight: 12,
  },
  itemDetails: {
    flex: 1,
  },
  itemName: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 4,
  },
  itemPrice: {
    fontSize: 12,
  },
  itemTotal: {
    fontSize: 14,
    fontWeight: '600',
  },
  inputGroup: {
    marginBottom: 16,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 8,
  },
  input: {
    paddingHorizontal: 12,
    paddingVertical: 10,
    borderRadius: 8,
    fontSize: 16,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  row: {
    flexDirection: 'row',
  },
  paymentMethod: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    borderRadius: 8,
    borderWidth: 2,
    marginBottom: 12,
  },
  paymentMethodLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  paymentMethodText: {
    fontSize: 16,
    fontWeight: '500',
  },
  radioButton: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 2,
    justifyContent: 'center',
    alignItems: 'center',
  },
  radioButtonInner: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#fff',
  },
  priceRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  priceLabel: {
    fontSize: 14,
  },
  priceValue: {
    fontSize: 14,
    fontWeight: '500',
  },
  totalRow: {
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
    paddingTop: 12,
    marginTop: 8,
  },
  totalLabel: {
    fontSize: 16,
    fontWeight: '600',
  },
  totalValue: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  footer: {
    padding: 16,
  },
  placeOrderButton: {
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: 'center',
  },
  placeOrderText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
  },
});
