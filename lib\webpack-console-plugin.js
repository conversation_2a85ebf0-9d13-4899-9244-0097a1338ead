/**
 * Webpack Plugin to Remove Console Logs
 * 
 * This plugin removes console.log statements during the build process
 * for better performance and security in production builds.
 */

class RemoveConsolePlugin {
  constructor(options = {}) {
    this.options = {
      exclude: ['error', 'warn'], // Keep these console methods
      ...options
    }
  }

  apply(compiler) {
    const pluginName = 'RemoveConsolePlugin'

    compiler.hooks.compilation.tap(pluginName, (compilation) => {
      compilation.hooks.optimizeChunkAssets.tapAsync(pluginName, (chunks, callback) => {
        chunks.forEach((chunk) => {
          chunk.files.forEach((file) => {
            if (file.endsWith('.js')) {
              const asset = compilation.assets[file]
              let source = asset.source()

              // Remove console.log statements but keep specified exclusions
              const consoleRegex = new RegExp(
                `console\\.((?!${this.options.exclude.join('|')})\\w+)\\s*\\([^)]*\\)\\s*;?`,
                'g'
              )

              source = source.replace(consoleRegex, '')

              // Update the asset
              compilation.assets[file] = {
                source: () => source,
                size: () => source.length
              }
            }
          })
        })
        callback()
      })
    })
  }
}

module.exports = RemoveConsolePlugin

/**
 * Usage in next.config.js:
 * 
 * const RemoveConsolePlugin = require('./lib/webpack-console-plugin')
 * 
 * module.exports = {
 *   webpack: (config, { dev, isServer }) => {
 *     if (!dev && !isServer) {
 *       config.plugins.push(new RemoveConsolePlugin({
 *         exclude: ['error', 'warn'] // Keep these methods
 *       }))
 *     }
 *     return config
 *   }
 * }
 */
