import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Alert,
  ScrollView,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../../../providers/ThemeProvider';
import { useAuth } from '../../../providers/AuthProvider';
import { Header } from '../../../components/Header';
import { Card, Input, Button, Badge } from '../../../components/ui';
import type { StackNavigationProp } from '@react-navigation/stack';
import type { AppStackParamList } from '../../../navigation/AppNavigator';

type NavigationProp = StackNavigationProp<AppStackParamList>;

interface Newsletter {
  id: string;
  subject: string;
  content: string;
  status: 'draft' | 'scheduled' | 'sent' | 'failed';
  recipientCount: number;
  openRate?: number;
  clickRate?: number;
  createdAt: string;
  scheduledAt?: string;
  sentAt?: string;
  template: string;
  tags: string[];
}

interface Subscriber {
  id: string;
  email: string;
  name?: string;
  status: 'active' | 'unsubscribed' | 'bounced';
  subscribedAt: string;
  lastActivity?: string;
  preferences: string[];
}

export default function AdminNewsletterManagementScreen() {
  const { theme } = useTheme();
  const navigation = useNavigation<NavigationProp>();
  const { user } = useAuth();
  const [newsletters, setNewsletters] = useState<Newsletter[]>([]);
  const [subscribers, setSubscribers] = useState<Subscriber[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'newsletters' | 'subscribers'>('newsletters');
  const [searchQuery, setSearchQuery] = useState('');

  useEffect(() => {
    loadNewsletterData();
  }, []);

  const loadNewsletterData = async () => {
    try {
      setLoading(true);
      // TODO: Replace with actual API calls
      const mockNewsletters: Newsletter[] = [
        {
          id: '1',
          subject: 'Wedding Season Special Offers - 2024',
          content: 'Discover amazing deals for Your Dream Celebration...',
          status: 'sent',
          recipientCount: 2450,
          openRate: 68.5,
          clickRate: 12.3,
          createdAt: '2024-01-20T10:00:00Z',
          sentAt: '2024-01-22T09:00:00Z',
          template: 'promotional',
          tags: ['offers', 'wedding', '2024'],
        },
        {
          id: '2',
          subject: 'New Vendor Spotlight: Elegant Decorations',
          content: 'Meet our featured vendor of the month...',
          status: 'scheduled',
          recipientCount: 2450,
          createdAt: '2024-01-25T14:30:00Z',
          scheduledAt: '2024-01-28T10:00:00Z',
          template: 'vendor_spotlight',
          tags: ['vendor', 'spotlight', 'decorations'],
        },
        {
          id: '3',
          subject: 'Wedding Planning Tips for February',
          content: 'Essential tips for planning your February wedding...',
          status: 'draft',
          recipientCount: 0,
          createdAt: '2024-01-25T16:45:00Z',
          template: 'tips',
          tags: ['tips', 'planning', 'february'],
        },
      ];

      const mockSubscribers: Subscriber[] = [
        {
          id: '1',
          email: '<EMAIL>',
          name: 'Priya Sharma',
          status: 'active',
          subscribedAt: '2024-01-15T10:00:00Z',
          lastActivity: '2024-01-25T14:30:00Z',
          preferences: ['wedding_tips', 'vendor_updates', 'offers'],
        },
        {
          id: '2',
          email: '<EMAIL>',
          name: 'Rahul Kumar',
          status: 'active',
          subscribedAt: '2024-01-10T09:00:00Z',
          lastActivity: '2024-01-24T11:15:00Z',
          preferences: ['offers', 'new_features'],
        },
        {
          id: '3',
          email: '<EMAIL>',
          status: 'unsubscribed',
          subscribedAt: '2024-01-05T12:00:00Z',
          lastActivity: '2024-01-20T16:00:00Z',
          preferences: [],
        },
      ];
      
      setNewsletters(mockNewsletters);
      setSubscribers(mockSubscribers);
    } catch (error) {
      console.error('Error loading newsletter data:', error);
      Alert.alert('Error', 'Failed to load newsletter data');
    } finally {
      setLoading(false);
    }
  };

  const handleCreateNewsletter = () => {
    // TODO: Navigate to newsletter creation screen
    Alert.alert('Create Newsletter', 'Navigate to newsletter creation screen');
  };

  const handleEditNewsletter = (newsletter: Newsletter) => {
    // TODO: Navigate to newsletter edit screen
    Alert.alert('Edit Newsletter', `Edit newsletter: ${newsletter.subject}`);
  };

  const handleSendNewsletter = (newsletter: Newsletter) => {
    Alert.alert(
      'Send Newsletter',
      `Are you sure you want to send "${newsletter.subject}" to ${newsletter.recipientCount} subscribers?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Send',
          onPress: () => {
            setNewsletters(prevNewsletters =>
              prevNewsletters.map(n =>
                n.id === newsletter.id
                  ? { ...n, status: 'sent' as const, sentAt: new Date().toISOString() }
                  : n
              )
            );
          },
        },
      ]
    );
  };

  const handleDeleteNewsletter = (newsletterId: string) => {
    Alert.alert(
      'Delete Newsletter',
      'Are you sure you want to delete this newsletter?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: () => {
            setNewsletters(prevNewsletters =>
              prevNewsletters.filter(n => n.id !== newsletterId)
            );
          },
        },
      ]
    );
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-IN', {
      day: 'numeric',
      month: 'short',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getStatusColor = (status: Newsletter['status']) => {
    switch (status) {
      case 'draft': return theme.colors.textSecondary;
      case 'scheduled': return theme.colors.warning;
      case 'sent': return theme.colors.success;
      case 'failed': return theme.colors.destructive;
      default: return theme.colors.textSecondary;
    }
  };

  const getSubscriberStatusColor = (status: Subscriber['status']) => {
    switch (status) {
      case 'active': return theme.colors.success;
      case 'unsubscribed': return theme.colors.warning;
      case 'bounced': return theme.colors.destructive;
      default: return theme.colors.textSecondary;
    }
  };

  const filteredNewsletters = newsletters.filter(newsletter =>
    newsletter.subject.toLowerCase().includes(searchQuery.toLowerCase()) ||
    newsletter.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))
  );

  const filteredSubscribers = subscribers.filter(subscriber =>
    subscriber.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
    (subscriber.name && subscriber.name.toLowerCase().includes(searchQuery.toLowerCase()))
  );

  const renderNewsletter = ({ item }: { item: Newsletter }) => (
    <Card style={styles.newsletterCard}>
      <View style={styles.newsletterHeader}>
        <View style={styles.newsletterTitleRow}>
          <Text style={[styles.newsletterSubject, { color: theme.colors.text }]} numberOfLines={2}>
            {item.subject}
          </Text>
          <Badge 
            style={[styles.statusBadge, { backgroundColor: getStatusColor(item.status) }]}
          >
            <Text style={styles.badgeText}>{item.status}</Text>
          </Badge>
        </View>
        
        <Text style={[styles.newsletterContent, { color: theme.colors.textSecondary }]} numberOfLines={2}>
          {item.content}
        </Text>
        
        <View style={styles.newsletterMeta}>
          <View style={styles.metaRow}>
            <Ionicons name="people" size={14} color={theme.colors.textSecondary} />
            <Text style={[styles.metaText, { color: theme.colors.textSecondary }]}>
              {item.recipientCount} recipients
            </Text>
          </View>
          
          {item.openRate && (
            <View style={styles.metaRow}>
              <Ionicons name="mail-open" size={14} color={theme.colors.textSecondary} />
              <Text style={[styles.metaText, { color: theme.colors.textSecondary }]}>
                {item.openRate}% open rate
              </Text>
            </View>
          )}
          
          <View style={styles.metaRow}>
            <Ionicons name="calendar" size={14} color={theme.colors.textSecondary} />
            <Text style={[styles.metaText, { color: theme.colors.textSecondary }]}>
              {item.sentAt ? `Sent: ${formatDate(item.sentAt)}` : 
               item.scheduledAt ? `Scheduled: ${formatDate(item.scheduledAt)}` :
               `Created: ${formatDate(item.createdAt)}`}
            </Text>
          </View>
        </View>
        
        <View style={styles.tagContainer}>
          {item.tags.map((tag, index) => (
            <Badge key={index} variant="outline" style={styles.tag}>
              <Text style={[styles.tagText, { color: theme.colors.primary }]}>{tag}</Text>
            </Badge>
          ))}
        </View>
      </View>
      
      <View style={styles.newsletterActions}>
        <TouchableOpacity
          style={[styles.actionButton, { backgroundColor: theme.colors.info }]}
          onPress={() => handleEditNewsletter(item)}
        >
          <Ionicons name="pencil" size={16} color="white" />
          <Text style={styles.actionButtonText}>Edit</Text>
        </TouchableOpacity>
        
        {item.status === 'draft' && (
          <TouchableOpacity
            style={[styles.actionButton, { backgroundColor: theme.colors.success }]}
            onPress={() => handleSendNewsletter(item)}
          >
            <Ionicons name="send" size={16} color="white" />
            <Text style={styles.actionButtonText}>Send</Text>
          </TouchableOpacity>
        )}
        
        <TouchableOpacity
          style={[styles.actionButton, { backgroundColor: theme.colors.destructive }]}
          onPress={() => handleDeleteNewsletter(item.id)}
        >
          <Ionicons name="trash" size={16} color="white" />
          <Text style={styles.actionButtonText}>Delete</Text>
        </TouchableOpacity>
      </View>
    </Card>
  );

  const renderSubscriber = ({ item }: { item: Subscriber }) => (
    <Card style={styles.subscriberCard}>
      <View style={styles.subscriberInfo}>
        <View style={styles.subscriberHeader}>
          <Text style={[styles.subscriberEmail, { color: theme.colors.text }]}>
            {item.email}
          </Text>
          <Badge 
            style={[styles.statusBadge, { backgroundColor: getSubscriberStatusColor(item.status) }]}
          >
            <Text style={styles.badgeText}>{item.status}</Text>
          </Badge>
        </View>
        
        {item.name && (
          <Text style={[styles.subscriberName, { color: theme.colors.primary }]}>
            {item.name}
          </Text>
        )}
        
        <View style={styles.subscriberMeta}>
          <Text style={[styles.metaText, { color: theme.colors.textSecondary }]}>
            Subscribed: {formatDate(item.subscribedAt)}
          </Text>
          {item.lastActivity && (
            <Text style={[styles.metaText, { color: theme.colors.textSecondary }]}>
              Last activity: {formatDate(item.lastActivity)}
            </Text>
          )}
        </View>
        
        {item.preferences.length > 0 && (
          <View style={styles.preferencesContainer}>
            <Text style={[styles.preferencesLabel, { color: theme.colors.text }]}>
              Preferences:
            </Text>
            <View style={styles.preferencesList}>
              {item.preferences.map((pref, index) => (
                <Badge key={index} variant="outline" style={styles.preference}>
                  <Text style={[styles.preferenceText, { color: theme.colors.secondary }]}>
                    {pref.replace('_', ' ')}
                  </Text>
                </Badge>
              ))}
            </View>
          </View>
        )}
      </View>
    </Card>
  );

  const renderTabContent = () => {
    if (activeTab === 'newsletters') {
      return (
        <FlatList
          data={filteredNewsletters}
          renderItem={renderNewsletter}
          keyExtractor={(item) => item.id}
          contentContainerStyle={styles.contentList}
          showsVerticalScrollIndicator={false}
        />
      );
    } else {
      return (
        <FlatList
          data={filteredSubscribers}
          renderItem={renderSubscriber}
          keyExtractor={(item) => item.id}
          contentContainerStyle={styles.contentList}
          showsVerticalScrollIndicator={false}
        />
      );
    }
  };

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <Header title="Newsletter Management" showBack />
      
      {/* Tab Navigation */}
      <View style={styles.tabContainer}>
        <TouchableOpacity
          style={[
            styles.tab,
            activeTab === 'newsletters' && styles.activeTab,
            { borderBottomColor: theme.colors.primary }
          ]}
          onPress={() => setActiveTab('newsletters')}
        >
          <Text
            style={[
              styles.tabText,
              { color: activeTab === 'newsletters' ? theme.colors.primary : theme.colors.textSecondary }
            ]}
          >
            Newsletters ({newsletters.length})
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[
            styles.tab,
            activeTab === 'subscribers' && styles.activeTab,
            { borderBottomColor: theme.colors.primary }
          ]}
          onPress={() => setActiveTab('subscribers')}
        >
          <Text
            style={[
              styles.tabText,
              { color: activeTab === 'subscribers' ? theme.colors.primary : theme.colors.textSecondary }
            ]}
          >
            Subscribers ({subscribers.filter(s => s.status === 'active').length})
          </Text>
        </TouchableOpacity>
      </View>

      {/* Search Bar */}
      <View style={styles.searchContainer}>
        <Input
          placeholder={`Search ${activeTab}...`}
          value={searchQuery}
          onChangeText={setSearchQuery}
          leftIcon={<Ionicons name="search" size={20} color={theme.colors.textSecondary} />}
        />
      </View>

      {/* Create Newsletter Button */}
      {activeTab === 'newsletters' && (
        <View style={styles.createButtonContainer}>
          <Button
            onPress={handleCreateNewsletter}
            style={styles.createButton}
          >
            <Ionicons name="add" size={20} color="white" />
            <Text style={styles.createButtonText}>Create Newsletter</Text>
          </Button>
        </View>
      )}

      {/* Content */}
      {renderTabContent()}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  tabContainer: {
    flexDirection: 'row',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5E5',
  },
  tab: {
    flex: 1,
    paddingVertical: 16,
    alignItems: 'center',
    borderBottomWidth: 2,
    borderBottomColor: 'transparent',
  },
  activeTab: {
    borderBottomWidth: 2,
  },
  tabText: {
    fontSize: 16,
    fontWeight: '500',
  },
  searchContainer: {
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  createButtonContainer: {
    paddingHorizontal: 16,
    marginBottom: 16,
  },
  createButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    gap: 8,
  },
  createButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  contentList: {
    paddingHorizontal: 16,
    paddingBottom: 20,
  },
  newsletterCard: {
    marginBottom: 16,
    padding: 16,
  },
  newsletterHeader: {
    marginBottom: 12,
  },
  newsletterTitleRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  newsletterSubject: {
    fontSize: 16,
    fontWeight: '600',
    flex: 1,
    marginRight: 8,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
  },
  badgeText: {
    color: 'white',
    fontSize: 10,
    fontWeight: '500',
  },
  newsletterContent: {
    fontSize: 14,
    lineHeight: 18,
    marginBottom: 12,
  },
  newsletterMeta: {
    gap: 4,
    marginBottom: 12,
  },
  metaRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  metaText: {
    fontSize: 12,
  },
  tagContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 4,
  },
  tag: {
    paddingHorizontal: 6,
    paddingVertical: 2,
  },
  tagText: {
    fontSize: 10,
  },
  newsletterActions: {
    flexDirection: 'row',
    gap: 8,
  },
  actionButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 6,
    gap: 4,
  },
  actionButtonText: {
    color: 'white',
    fontSize: 12,
    fontWeight: '500',
  },
  subscriberCard: {
    marginBottom: 12,
    padding: 16,
  },
  subscriberInfo: {
    gap: 8,
  },
  subscriberHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  subscriberEmail: {
    fontSize: 14,
    fontWeight: '500',
    flex: 1,
  },
  subscriberName: {
    fontSize: 12,
    fontWeight: '500',
  },
  subscriberMeta: {
    gap: 2,
  },
  preferencesContainer: {
    gap: 4,
  },
  preferencesLabel: {
    fontSize: 12,
    fontWeight: '500',
  },
  preferencesList: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 4,
  },
  preference: {
    paddingHorizontal: 6,
    paddingVertical: 2,
  },
  preferenceText: {
    fontSize: 10,
    textTransform: 'capitalize',
  },
});
