/**
 * Pricing Service for Vendor Subscription Management using AWS Amplify
 * Handles vendor pricing templates, subscription plans, and payment processing
 */

import { generateClient } from '@aws-amplify/api';
import {
  createSubscription,
  cancelSubscription,
  skipPricingDuringSignup
} from '@/src/graphql/mutations';
import {
  listPricingPlans,
  getVendorSubscription,
  listSubscriptionPayments
} from '@/src/graphql/queries';

const client = generateClient();

export interface PricingPlan {
  id: string;
  name: string;
  description: string;
  price: number;
  currency: string;
  duration: 'monthly' | 'quarterly' | 'yearly';
  features: string[];
  isPopular?: boolean;
  discount?: {
    percentage: number;
    validUntil?: string;
  };
}

export interface VendorSubscription {
  id: string;
  vendorId: string;
  planId: string;
  status: 'active' | 'inactive' | 'pending' | 'cancelled' | 'expired';
  startDate: string;
  endDate: string;
  autoRenew: boolean;
  paymentMethod?: string;
  lastPaymentDate?: string;
  nextPaymentDate?: string;
}

export interface PaymentData {
  vendorId: string;
  planId: string;
  amount: number;
  currency: string;
  paymentMethod: 'card' | 'upi' | 'netbanking' | 'wallet';
  billingAddress?: {
    name: string;
    address: string;
    city: string;
    state: string;
    pincode: string;
    country: string;
  };
}

class PricingService {
  /**
   * Get all available pricing plans
   */
  async getPricingPlans(): Promise<PricingPlan[]> {
    try {
      // Try GraphQL first
      try {
        const result = await client.graphql({
          query: listPricingPlans,
          variables: {
            filter: { isActive: { eq: true } }
          }
        });

        const plans = result.data.listPricingPlans.items;

        // If no plans in database, return default plans
        if (plans.length === 0) {
          console.log('No pricing plans found in database, using default plans');
          return this.getDefaultPricingPlans();
        }

        return plans;
      } catch (graphqlError) {
        console.log('GraphQL query failed, using default plans:', graphqlError);
        return this.getDefaultPricingPlans();
      }
    } catch (error) {
      console.error('Error fetching pricing plans:', error);
      return this.getDefaultPricingPlans();
    }
  }

  /**
   * Get vendor's current subscription
   */
  async getVendorSubscription(vendorId: string): Promise<VendorSubscription | null> {
    try {
      // For now, return mock subscription data since GraphQL queries are not set up
      console.log('Fetching subscription for vendor:', vendorId);
      return this.getMockVendorSubscription(vendorId);
    } catch (error) {
      console.error('Error fetching vendor subscription:', error);
      return this.getMockVendorSubscription(vendorId);
    }
  }

  /**
   * Create new subscription for vendor
   */
  async createSubscription(paymentData: PaymentData): Promise<{ success: boolean; subscriptionId?: string; error?: string }> {
    try {
      console.log('Creating subscription for vendor:', paymentData.vendorId, 'with plan:', paymentData.planId);

      // For now, simulate successful subscription creation since GraphQL mutations are not set up
      const subscriptionId = `sub_${Date.now()}_${paymentData.vendorId}`;

      // Simulate processing delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      return {
        success: true,
        subscriptionId: subscriptionId
      };
    } catch (error) {
      console.error('Error creating subscription:', error);
      return { success: false, error: 'Failed to create subscription' };
    }
  }

  /**
   * Update subscription (upgrade/downgrade)
   */
  async updateSubscription(vendorId: string, newPlanId: string): Promise<{ success: boolean; error?: string }> {
    try {
      // For now, return success as this would require implementing the GraphQL mutation
      console.log('Subscription update requested for vendor:', vendorId, 'to plan:', newPlanId);
      return { success: true };
    } catch (error) {
      console.error('Error updating subscription:', error);
      return { success: false, error: 'Network error occurred' };
    }
  }

  /**
   * Cancel subscription
   */
  async cancelSubscription(vendorId: string, reason?: string): Promise<{ success: boolean; error?: string }> {
    try {
      console.log('Cancelling subscription for vendor:', vendorId, 'reason:', reason);

      // For now, simulate successful cancellation since GraphQL mutations are not set up
      await new Promise(resolve => setTimeout(resolve, 500));

      return { success: true };
    } catch (error) {
      console.error('Error cancelling subscription:', error);
      return { success: false, error: 'Network error occurred' };
    }
  }

  /**
   * Skip pricing during signup (allow later payment)
   */
  async skipPricingDuringSignup(vendorId: string): Promise<{ success: boolean; error?: string }> {
    try {
      console.log('Skipping pricing for vendor:', vendorId);

      // For now, simulate successful skip since GraphQL mutations are not set up
      await new Promise(resolve => setTimeout(resolve, 300));

      return { success: true };
    } catch (error) {
      console.error('Error skipping pricing:', error);
      return { success: false, error: 'Network error occurred' };
    }
  }

  /**
   * Get payment history for vendor
   */
  async getPaymentHistory(vendorId: string): Promise<any[]> {
    try {
      // Try to use the auto-generated GraphQL query first
      const result = await client.graphql({
        query: listSubscriptionPayments,
        variables: {
          filter: {
            subscriptionId: { eq: `sub_${vendorId}` }
          },
          limit: 50
        }
      });

      return result.data.listSubscriptionPayments?.items || this.getMockPaymentHistory();
    } catch (error) {
      console.error('Error fetching payment history:', error);
      return this.getMockPaymentHistory();
    }
  }

  /**
   * Calculate prorated amount for plan changes
   */
  calculateProratedAmount(currentPlan: PricingPlan, newPlan: PricingPlan, daysRemaining: number): number {
    const currentDailyRate = currentPlan.price / this.getDaysInPeriod(currentPlan.duration);
    const newDailyRate = newPlan.price / this.getDaysInPeriod(newPlan.duration);
    
    const refund = currentDailyRate * daysRemaining;
    const newCharge = newDailyRate * daysRemaining;
    
    return Math.max(0, newCharge - refund);
  }

  /**
   * Get default pricing plans (fallback)
   */
  private getDefaultPricingPlans(): PricingPlan[] {
    return [
      {
        id: 'basic',
        name: 'Basic Plan',
        description: 'Perfect for new vendors starting their journey',
        price: 199,
        currency: 'INR',
        duration: 'monthly',
        features: [
          'Basic vendor profile',
          'Up to 10 photos',
          'Contact form inquiries',
          'Basic analytics',
          'Email support'
        ]
      },
      {
        id: 'professional',
        name: 'Professional Plan',
        description: 'Ideal for established vendors looking to grow',
        price: 249,
        currency: 'INR',
        duration: 'monthly',
        isPopular: true,
        features: [
          'Enhanced vendor profile',
          'Unlimited photos & videos',
          'Priority listing',
          'Advanced analytics',
          'Lead management',
          'Phone & email support',
          'Social media integration'
        ]
      },
      {
        id: 'premium',
        name: 'Premium Plan',
        description: 'For top vendors who want maximum visibility',
        price: 499,
        currency: 'INR',
        duration: 'monthly',
        features: [
          'Premium vendor profile',
          'Unlimited media',
          'Top search ranking',
          'Detailed analytics & insights',
          'CRM integration',
          'Dedicated account manager',
          'Custom branding options',
          'Featured vendor badge'
        ]
      },
      {
        id: 'yearly-professional',
        name: 'Professional Yearly',
        description: 'Professional plan with 2 months free',
        price: 4999,
        currency: 'INR',
        duration: 'yearly',
        discount: {
          percentage: 17,
          validUntil: '2024-12-31'
        },
        features: [
          'All Professional Plan features',
          '2 months free',
          'Priority customer support',
          'Quarterly business reviews'
        ]
      }
    ];
  }

  /**
   * Get number of days in a billing period
   */
  private getDaysInPeriod(duration: 'monthly' | 'quarterly' | 'yearly'): number {
    switch (duration) {
      case 'monthly':
        return 30;
      case 'quarterly':
        return 90;
      case 'yearly':
        return 365;
      default:
        return 30;
    }
  }

  /**
   * Check if vendor needs to see pricing prompt
   */
  async shouldShowPricingPrompt(vendorId: string): Promise<boolean> {
    try {
      const subscription = await this.getVendorSubscription(vendorId);
      
      // Show prompt if no active subscription
      if (!subscription || subscription.status !== 'active') {
        return true;
      }
      
      // Show prompt if subscription is expiring soon (within 7 days)
      const endDate = new Date(subscription.endDate);
      const now = new Date();
      const daysUntilExpiry = Math.ceil((endDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
      
      return daysUntilExpiry <= 7;
    } catch (error) {
      console.error('Error checking pricing prompt status:', error);
      return true; // Show prompt on error to be safe
    }
  }

  /**
   * Get mock payment history for testing
   */
  private getMockPaymentHistory(): any[] {
    return [
      {
        id: 'payment_001',
        subscriptionId: 'sub_001',
        amount: 249,
        currency: 'INR',
        paymentMethod: 'UPI',
        status: 'completed',
        transactionId: 'TXN123456789',
        paymentDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
        description: 'Professional Plan - Monthly Subscription',
        invoiceId: 'inv_001'
      },
      {
        id: 'payment_002',
        subscriptionId: 'sub_001',
        amount: 249,
        currency: 'INR',
        paymentMethod: 'Credit Card',
        status: 'completed',
        transactionId: 'TXN987654321',
        paymentDate: new Date(Date.now() - 60 * 24 * 60 * 60 * 1000).toISOString(),
        description: 'Professional Plan - Monthly Subscription',
        invoiceId: 'inv_002'
      },
      {
        id: 'payment_003',
        subscriptionId: 'sub_001',
        amount: 249,
        currency: 'INR',
        paymentMethod: 'Net Banking',
        status: 'completed',
        transactionId: 'TXN456789123',
        paymentDate: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000).toISOString(),
        description: 'Professional Plan - Monthly Subscription',
        invoiceId: 'inv_003'
      }
    ];
  }

  /**
   * Get mock vendor subscription for testing
   */
  private getMockVendorSubscription(vendorId: string): VendorSubscription | null {
    return {
      id: 'sub_001',
      vendorId: vendorId,
      planId: 'professional',
      status: 'active',
      startDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
      endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
      autoRenew: true,
      paymentMethod: 'UPI',
      lastPaymentDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
      nextPaymentDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
      amount: 249,
      currency: 'INR',
      transactionId: 'TXN123456789',
      createdAt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
      updatedAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString()
    };
  }
}

export const pricingService = new PricingService();
export default pricingService;
