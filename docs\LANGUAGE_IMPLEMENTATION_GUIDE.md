# Language Support Implementation Guide

## 🌍 Overview

This guide provides comprehensive language support implementation for the BookmyFestive wedding planning platform, supporting 9 Indian languages with proper fallbacks and enhanced user experience.

## 📋 Supported Languages

| Language | Code | Native Name | Status |
|----------|------|-------------|---------|
| English | `en` | English | ✅ Complete |
| Hindi | `hi` | हिन्दी | ✅ Complete |
| Tamil | `ta` | தமிழ் | ✅ Complete |
| Telugu | `te` | తెలుగు | ✅ Complete |
| Kannada | `kn` | ಕನ್ನಡ | ✅ Complete |
| Malayalam | `ml` | മലയാളം | ✅ Complete |
| Gujarati | `gu` | ગુજરાતી | ✅ Complete |
| Marathi | `mr` | मराठी | ✅ Complete |
| Bengali | `bn` | বাংলা | ✅ Complete |

## 🏗️ Implementation Structure

### Core Files

```
lib/
├── i18n-enhanced.ts          # Enhanced i18n configuration
└── i18n.ts                   # Original i18n (legacy)

components/
├── LanguageSelector.tsx      # Language switcher component
├── TranslatedText.tsx        # Reusable translation component
└── I18nProvider.tsx          # i18n context provider

hooks/
├── useTranslation.ts         # Enhanced translation hook
└── use-safe-translation.ts   # Safe translation hook (updated)

public/locales/
├── en/common.json            # English translations
├── hi/common.json            # Hindi translations
├── ta/common.json            # Tamil translations
├── te/common.json            # Telugu translations
├── kn/common.json            # Kannada translations
├── ml/common.json            # Malayalam translations
├── gu/common.json            # Gujarati translations
├── mr/common.json            # Marathi translations
└── bn/common.json            # Bengali translations
```

## 🚀 Quick Setup

### 1. Install Dependencies

```bash
npm install i18next react-i18next i18next-browser-languagedetector
```

### 2. Import Enhanced i18n

```typescript
// In your main app file or layout
import '@/lib/i18n-enhanced'
```

### 3. Use Translation Hook

```typescript
import { useTranslation } from '@/hooks/useTranslation'

function MyComponent() {
  const { t } = useTranslation()
  
  return (
    <h1>{t('hero.title', 'Plan Your Dream Celebration!')}</h1>
  )
}
```

### 4. Add Language Selector

```typescript
import LanguageSelector from '@/components/LanguageSelector'

function Header() {
  return (
    <header>
      {/* Other header content */}
      <LanguageSelector />
    </header>
  )
}
```

## 🔧 Usage Examples

### Basic Translation

```typescript
import { useTranslation } from '@/hooks/useTranslation'

function Component() {
  const { t } = useTranslation()
  
  return (
    <div>
      <h1>{t('navigation.vendors', 'Vendors')}</h1>
      <p>{t('hero.subtitle', 'Plan Your Dream Celebration')}</p>
    </div>
  )
}
```

### Namespace-Specific Hooks

```typescript
import { useHeaderTranslation, useHeroTranslation } from '@/hooks/useTranslation'

function Header() {
  const { t: tHeader } = useHeaderTranslation()
  const { t: tHero } = useHeroTranslation()
  
  return (
    <div>
      <nav>{tHeader('vendors.title', 'Vendors')}</nav>
      <h1>{tHero('title', 'Plan Your Dream Celebration!')}</h1>
    </div>
  )
}
```

### Using TranslatedText Component

```typescript
import TranslatedText, { TranslatedHeading } from '@/components/TranslatedText'

function Component() {
  return (
    <div>
      <TranslatedHeading 
        translationKey="hero.title" 
        fallback="Plan Your Dream Celebration!"
        className="text-3xl font-bold"
      />
      <TranslatedText 
        translationKey="hero.subtitle" 
        fallback="Discover amazing vendors"
        as="p"
        className="text-gray-600"
      />
    </div>
  )
}
```

### Formatting Helpers

```typescript
import { formatCurrency, formatDate, formatNumber } from '@/lib/i18n-enhanced'

function PriceDisplay({ amount, date, count }) {
  return (
    <div>
      <p>Price: {formatCurrency(amount)}</p>
      <p>Date: {formatDate(date)}</p>
      <p>Items: {formatNumber(count)}</p>
    </div>
  )
}
```

## 📝 Translation Key Structure

### Hierarchical Organization

```json
{
  "navigation": {
    "vendors": "Vendors",
    "venues": "Venues",
    "shop": "Shop"
  },
  "header": {
    "vendors": {
      "title": "Vendors",
      "photography": {
        "title": "Photography",
        "weddingPhotographers": "Wedding Photographers"
      }
    }
  },
  "hero": {
    "title": "Plan Your Dream Celebration!",
    "subtitle": "Discover amazing vendors and venues"
  },
  "common": {
    "loading": "Loading...",
    "error": "Something went wrong",
    "viewAll": "View All"
  }
}
```

### Key Naming Conventions

- Use camelCase for keys: `weddingPhotographers`
- Group related translations: `header.vendors.photography.title`
- Keep common translations in `common` namespace
- Use descriptive names: `startingFrom` instead of `from`

## 🎯 Best Practices

### 1. Always Provide Fallbacks

```typescript
// Good
const title = t('hero.title', 'Plan Your Dream Celebration!')

// Bad
const title = t('hero.title')
```

### 2. Use Namespace-Specific Hooks

```typescript
// Good - Clear namespace separation
const { t: tHeader } = useHeaderTranslation()
const { t: tHero } = useHeroTranslation()

// Acceptable - General hook with namespace
const { t } = useTranslation()
const title = t('header.vendors.title', 'Vendors')
```

### 3. Handle Interpolation

```typescript
// With variables
const message = t('welcome.message', 'Welcome {{name}}!', { name: 'John' })

// With formatting
const price = t('product.price', 'Price: {{amount, currency}}', { amount: 1500 })
```

### 4. Lazy Load Translations

```typescript
// For large translation files
const { t } = useTranslation()

// Load translations on demand
useEffect(() => {
  i18n.loadNamespaces(['shop', 'vendors'])
}, [])
```

## 🔄 Language Switching

### Programmatic Language Change

```typescript
import { changeLanguage } from '@/lib/i18n-enhanced'

async function switchLanguage(languageCode: string) {
  const success = await changeLanguage(languageCode)
  if (success) {
    // Language changed successfully
    window.location.reload() // Optional: reload to apply changes
  }
}
```

### Listening to Language Changes

```typescript
useEffect(() => {
  const handleLanguageChange = (event: CustomEvent) => {
    console.log('Language changed to:', event.detail.language)
    // Update UI or reload data
  }
  
  window.addEventListener('languageChanged', handleLanguageChange)
  
  return () => {
    window.removeEventListener('languageChanged', handleLanguageChange)
  }
}, [])
```

## 🌐 Regional Customization

### State-Specific Content

```typescript
// Tamil Nadu specific content
const { t } = useTranslation()
const stateContent = t('header.stateSpecific.tamilNadu.photography.traditionalTamilWedding', 
  'Traditional Tamil Wedding Photography')
```

### Cultural Adaptations

```json
{
  "header": {
    "stateSpecific": {
      "tamilNadu": {
        "photography": {
          "traditionalTamilWedding": "பாரம்பரிய தமிழ் திருமண புகைப்படம்",
          "templePhotography": "கோயில் புகைப்படம்"
        },
        "music": {
          "nadaswaramArtists": "நாதஸ்வரம் கலைஞர்கள்",
          "carnaticMusic": "கர்நாடக இசை"
        }
      }
    }
  }
}
```

## 🚨 Error Handling

### Safe Translation with Fallbacks

```typescript
function SafeComponent() {
  const { t, mounted } = useTranslation()
  
  if (!mounted) {
    return <div>Loading...</div>
  }
  
  return (
    <div>
      {t('some.key', 'Default text if translation missing')}
    </div>
  )
}
```

### Missing Translation Detection

```typescript
// In development, log missing translations
const translation = t('some.key', 'Fallback')
if (process.env.NODE_ENV === 'development' && translation === 'some.key') {
  console.warn('Missing translation for key:', 'some.key')
}
```

## 📱 Mobile App Integration

### React Native Setup

```typescript
// apps/mobile/src/i18n/index.ts
import i18n from 'i18next'
import { initReactI18next } from 'react-i18next'
import AsyncStorage from '@react-native-async-storage/async-storage'

// Language detection for React Native
const LANGUAGE_DETECTOR = {
  type: 'languageDetector',
  async: true,
  detect: async (callback) => {
    const savedLanguage = await AsyncStorage.getItem('user-language')
    callback(savedLanguage || 'en')
  },
  cacheUserLanguage: async (lng) => {
    await AsyncStorage.setItem('user-language', lng)
  }
}

i18n
  .use(LANGUAGE_DETECTOR)
  .use(initReactI18next)
  .init({
    // Configuration
  })
```

## 🔍 Testing

### Translation Testing

```typescript
// Test translation keys exist
describe('Translations', () => {
  test('should have all required keys', () => {
    const requiredKeys = [
      'navigation.vendors',
      'hero.title',
      'common.loading'
    ]
    
    requiredKeys.forEach(key => {
      expect(t(key)).not.toBe(key) // Should not return the key itself
    })
  })
})
```

### Language Switching Testing

```typescript
// Test language switching
test('should change language', async () => {
  const success = await changeLanguage('ta')
  expect(success).toBe(true)
  expect(getCurrentLanguage()).toBe('ta')
})
```

## 📊 Performance Optimization

### Bundle Splitting

```typescript
// Load only required translations
const loadLanguageAsync = async (language: string) => {
  const translations = await import(`../public/locales/${language}/common.json`)
  i18n.addResourceBundle(language, 'common', translations.default)
}
```

### Caching Strategy

```typescript
// Cache translations in localStorage
const cacheTranslations = (language: string, translations: any) => {
  localStorage.setItem(`translations_${language}`, JSON.stringify(translations))
}

const getCachedTranslations = (language: string) => {
  const cached = localStorage.getItem(`translations_${language}`)
  return cached ? JSON.parse(cached) : null
}
```

## 🔧 Maintenance

### Adding New Languages

1. Create translation file: `public/locales/{code}/common.json`
2. Add to `SUPPORTED_LANGUAGES` in `lib/i18n-enhanced.ts`
3. Import in resources configuration
4. Test all key translations

### Updating Translations

1. Update base English file first
2. Update all other language files
3. Test with fallbacks
4. Verify UI layout with longer text

### Translation Management

```bash
# Extract translation keys (if using extraction tools)
npm run extract-translations

# Validate all translation files
npm run validate-translations

# Generate missing translation reports
npm run translation-report
```

## 🎉 Conclusion

This comprehensive language support implementation provides:

- ✅ 9 Indian languages with complete translations
- ✅ Fallback system for missing translations
- ✅ Enhanced user experience with language switching
- ✅ Regional and cultural customization
- ✅ Performance optimized with lazy loading
- ✅ Developer-friendly hooks and components
- ✅ Mobile app compatibility
- ✅ Proper error handling and testing

The implementation ensures that users across India can access the BookmyFestive platform in their preferred language, enhancing user engagement and accessibility.