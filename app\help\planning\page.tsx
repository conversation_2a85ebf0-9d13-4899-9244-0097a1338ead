"use client"

import { <PERSON>Header } from "@/components/top-header"
import { <PERSON><PERSON> } from "@/components/header"
import { Foot<PERSON> } from "@/components/footer"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { SimpleSEO } from "@/components/seo/SimpleSEO"
import { 
  ArrowLeft,
  Calendar,
  CheckSquare,
  Calculator,
  Users,
  Clock,
  MapPin,
  Camera,
  Music,
  Utensils,
  Flower,
  Car,
  Gift,
  Heart,
  Star,
  AlertCircle,
  Download,
  FileText,
  Lightbulb
} from "lucide-react"
import Link from "next/link"
import { useHelpTranslations } from '@/lib/universal-i18n'

export default function WeddingPlanningHelpPage() {
  const helpT = useHelpTranslations()
  const planningSteps = [
    {
      icon: Calendar,
      title: "Set Your Wedding Date",
      description: "Choose your perfect wedding date and start planning backwards",
      tips: [
        "Consider weather and season for your preferred style",
        "Check availability of must-have vendors and venues",
        "Avoid major holidays unless intentional",
        "Give yourself 6-12 months for planning"
      ],
      timeline: "12 months before"
    },
    {
      icon: Calculator,
      title: "Create Your Budget",
      description: "Establish a realistic budget and allocate funds wisely",
      tips: [
        "Venue typically takes 40-50% of budget",
        "Photography/videography: 10-15%",
        "Catering: 25-35%",
        "Keep 5-10% as emergency buffer"
      ],
      timeline: "10-12 months before"
    },
    {
      icon: Users,
      title: "Build Your Guest List",
      description: "Determine your guest count to guide venue and catering decisions",
      tips: [
        "Start with immediate family and close friends",
        "Consider venue capacity constraints",
        "Account for plus-ones and children",
        "Create A-list and B-list if needed"
      ],
      timeline: "8-10 months before"
    },
    {
      icon: MapPin,
      title: "Book Your Venue",
      description: "Secure your dream venue early as they book up quickly",
      tips: [
        "Visit venues in person when possible",
        "Ask about included services and restrictions",
        "Understand cancellation and payment policies",
        "Consider backup indoor options for outdoor venues"
      ],
      timeline: "6-8 months before"
    },
    {
      icon: Camera,
      title: "Hire Key Vendors",
      description: "Book essential vendors like photographer, caterer, and entertainment",
      tips: [
        "Photographer and videographer book earliest",
        "Read reviews and check portfolios",
        "Meet vendors in person or via video call",
        "Get everything in writing with contracts"
      ],
      timeline: "4-6 months before"
    },
    {
      icon: Flower,
      title: "Plan Decorations & Details",
      description: "Design your wedding aesthetic and book decorators",
      tips: [
        "Create a mood board for your theme",
        "Consider seasonal flower availability",
        "Plan lighting for evening events",
        "Don't forget ceremony and reception differences"
      ],
      timeline: "2-4 months before"
    }
  ]

  const planningTools = [
    {
      icon: CheckSquare,
      title: "Wedding Checklist",
      description: "Complete timeline with all tasks organized by month",
      features: [
        "12-month planning timeline",
        "Task prioritization",
        "Vendor booking reminders",
        "Legal requirements checklist"
      ],
      link: "/dashboard/planning"
    },
    {
      icon: Calculator,
      title: "Budget Tracker",
      description: "Track expenses and stay within your budget",
      features: [
        "Category-wise budget allocation",
        "Expense tracking",
        "Vendor payment schedules",
        "Budget vs actual reports"
      ],
      link: "/dashboard/planning"
    },
    {
      icon: Users,
      title: "Guest List Manager",
      description: "Organize your guest list and track RSVPs",
      features: [
        "Contact management",
        "RSVP tracking",
        "Dietary restrictions",
        "Seating arrangement planner"
      ],
      link: "/dashboard/planning"
    },
    {
      icon: Clock,
      title: "Timeline Creator",
      description: "Create detailed day-of wedding timeline",
      features: [
        "Hour-by-hour schedule",
        "Vendor coordination",
        "Transportation planning",
        "Emergency contact list"
      ],
      link: "/dashboard/planning"
    }
  ]

  const budgetBreakdown = [
    { category: "Venue", percentage: "40-50%", description: "Reception and ceremony venues" },
    { category: "Catering", percentage: "25-35%", description: "Food, drinks, and service" },
    { category: "Photography/Video", percentage: "10-15%", description: "Professional documentation" },
    { category: "Attire", percentage: "8-10%", description: "Outfits for couple and family" },
    { category: "Decorations", percentage: "8-10%", description: "Flowers, lighting, and decor" },
    { category: "Entertainment", percentage: "5-8%", description: "Music, DJ, or live performers" },
    { category: "Transportation", percentage: "3-5%", description: "Cars, decorations, and logistics" },
    { category: "Miscellaneous", percentage: "5-10%", description: "Invitations, favors, and extras" }
  ]

  const commonMistakes = [
    {
      mistake: "Not setting a realistic budget",
      solution: "Research actual costs in your area and add 10-20% buffer"
    },
    {
      mistake: "Booking vendors without contracts",
      solution: "Always get detailed contracts with payment schedules and cancellation terms"
    },
    {
      mistake: "Leaving everything to the last minute",
      solution: "Start planning 6-12 months ahead and book popular vendors early"
    },
    {
      mistake: "Not considering guest experience",
      solution: "Think about transportation, accommodation, and comfort for your guests"
    },
    {
      mistake: "Ignoring weather contingencies",
      solution: "Always have backup plans for outdoor events"
    }
  ]

  return (
    <>
      <SimpleSEO
        title="Wedding Planning Guide - BookmyFestive Help"
        description="Complete guide to wedding planning with tools, tips, and timeline. Learn how to Plan Your Dream Celebration step by step with BookmyFestive."
        keywords="wedding planning, wedding checklist, wedding budget, wedding timeline, wedding planning guide"
        url="/help/planning"
      />
      
      <div className="min-h-screen bg-background">
        <TopHeader />
        <Header />

        {/* Header */}
        <section className="py-12">
          <div className="container mx-auto px-4">
            <Link href="/help" className="inline-flex items-center gap-2 text-primary hover:underline mb-6">
              <ArrowLeft className="w-4 h-4" />
              Back to Help Center
            </Link>
            
            <div className="text-center mb-12">
              <Calendar className="w-16 h-16 mx-auto mb-6 text-primary" />
              <h1 className="text-4xl font-bold mb-4">{helpT.planning?.title || 'Wedding Planning Guide'}</h1>
              <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
                {helpT.planning?.subtitle || 'Your complete guide to planning the perfect wedding. From setting your date to the big day, we\'ll help you stay organized and stress-free throughout your wedding planning journey.'}
              </p>
            </div>
          </div>
        </section>

        {/* Planning Steps */}
        <section className="py-16">
          <div className="container mx-auto px-4">
            <h2 className="text-3xl font-bold text-center mb-12">Wedding Planning Timeline</h2>
            <div className="space-y-8">
              {planningSteps.map((step, index) => (
                <Card key={index} className="hover:shadow-lg transition-shadow">
                  <CardHeader>
                    <div className="flex items-start gap-4">
                      <div className="flex-shrink-0">
                        <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center">
                          <step.icon className="w-6 h-6 text-primary" />
                        </div>
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center gap-3 mb-2">
                          <CardTitle className="text-xl">{step.title}</CardTitle>
                          <Badge variant="outline">{step.timeline}</Badge>
                        </div>
                        <p className="text-muted-foreground mb-4">{step.description}</p>
                        <ul className="space-y-2">
                          {step.tips.map((tip, tipIndex) => (
                            <li key={tipIndex} className="flex items-start gap-2">
                              <Lightbulb className="w-4 h-4 text-yellow-500 mt-0.5 flex-shrink-0" />
                              <span className="text-sm">{tip}</span>
                            </li>
                          ))}
                        </ul>
                      </div>
                    </div>
                  </CardHeader>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Planning Tools */}
        <section className="py-16 bg-muted/30">
          <div className="container mx-auto px-4">
            <h2 className="text-3xl font-bold text-center mb-12">Planning Tools</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {planningTools.map((tool, index) => (
                <Card key={index} className="hover:shadow-lg transition-shadow">
                  <CardHeader className="text-center">
                    <tool.icon className="w-12 h-12 mx-auto mb-4 text-primary" />
                    <CardTitle className="text-lg">{tool.title}</CardTitle>
                    <p className="text-sm text-muted-foreground">{tool.description}</p>
                  </CardHeader>
                  <CardContent>
                    <ul className="space-y-2 mb-4">
                      {tool.features.map((feature, idx) => (
                        <li key={idx} className="flex items-center gap-2">
                          <CheckSquare className="w-4 h-4 text-green-500" />
                          <span className="text-sm">{feature}</span>
                        </li>
                      ))}
                    </ul>
                    <Link href={tool.link}>
                      <Button className="w-full">Use Tool</Button>
                    </Link>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Budget Breakdown */}
        <section className="py-16">
          <div className="container mx-auto px-4">
            <h2 className="text-3xl font-bold text-center mb-12">Typical Budget Breakdown</h2>
            <div className="max-w-4xl mx-auto">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {budgetBreakdown.map((item, index) => (
                  <Card key={index}>
                    <CardContent className="pt-6">
                      <div className="flex justify-between items-center mb-2">
                        <h3 className="font-semibold">{item.category}</h3>
                        <Badge variant="secondary">{item.percentage}</Badge>
                      </div>
                      <p className="text-sm text-muted-foreground">{item.description}</p>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          </div>
        </section>

        {/* Common Mistakes */}
        <section className="py-16 bg-muted/30">
          <div className="container mx-auto px-4">
            <h2 className="text-3xl font-bold text-center mb-12">Avoid Common Mistakes</h2>
            <div className="max-w-4xl mx-auto space-y-6">
              {commonMistakes.map((item, index) => (
                <Card key={index}>
                  <CardContent className="pt-6">
                    <div className="flex items-start gap-4">
                      <AlertCircle className="w-6 h-6 text-red-500 mt-1 flex-shrink-0" />
                      <div>
                        <h3 className="font-semibold text-red-700 mb-2">{item.mistake}</h3>
                        <p className="text-sm text-muted-foreground">{item.solution}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Quick Actions */}
        <section className="py-16">
          <div className="container mx-auto px-4 text-center">
            <h2 className="text-3xl font-bold mb-8">Start Planning Today</h2>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/dashboard/planning">
                <Button size="lg" className="px-8">
                  <CheckSquare className="w-5 h-5 mr-2" />
                  Access Planning Tools
                </Button>
              </Link>
              <Link href="/vendors">
                <Button variant="outline" size="lg" className="px-8">
                  <Users className="w-5 h-5 mr-2" />
                  Find Vendors
                </Button>
              </Link>
              <Link href="/venues">
                <Button variant="outline" size="lg" className="px-8">
                  <MapPin className="w-5 h-5 mr-2" />
                  Browse Venues
                </Button>
              </Link>
            </div>
          </div>
        </section>

        <Footer />
      </div>
    </>
  )
}
