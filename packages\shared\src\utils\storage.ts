// Platform-specific storage utility that works for both web and mobile

interface StorageInterface {
  getItem(key: string): Promise<string | null>;
  setItem(key: string, value: string): Promise<void>;
  removeItem(key: string): Promise<void>;
  clear(): Promise<void>;
}

class WebStorage implements StorageInterface {
  async getItem(key: string): Promise<string | null> {
    try {
      return sessionStorage.getItem(key);
    } catch (error) {
      console.warn('SessionStorage not available:', error);
      return null;
    }
  }

  async setItem(key: string, value: string): Promise<void> {
    try {
      sessionStorage.setItem(key, value);
    } catch (error) {
      console.warn('SessionStorage not available:', error);
    }
  }

  async removeItem(key: string): Promise<void> {
    try {
      sessionStorage.removeItem(key);
    } catch (error) {
      console.warn('SessionStorage not available:', error);
    }
  }

  async clear(): Promise<void> {
    try {
      sessionStorage.clear();
    } catch (error) {
      console.warn('SessionStorage not available:', error);
    }
  }
}

class MobileStorage implements StorageInterface {
  private AsyncStorage: any;

  constructor() {
    // Dynamically import AsyncStorage only in React Native environment
    try {
      this.AsyncStorage = require('@react-native-async-storage/async-storage').default;
    } catch (error) {
      console.warn('AsyncStorage not available:', error);
      this.AsyncStorage = null;
    }
  }

  async getItem(key: string): Promise<string | null> {
    try {
      if (!this.AsyncStorage) return null;
      return await this.AsyncStorage.getItem(key);
    } catch (error) {
      console.warn('AsyncStorage getItem failed:', error);
      return null;
    }
  }

  async setItem(key: string, value: string): Promise<void> {
    try {
      if (!this.AsyncStorage) return;
      await this.AsyncStorage.setItem(key, value);
    } catch (error) {
      console.warn('AsyncStorage setItem failed:', error);
    }
  }

  async removeItem(key: string): Promise<void> {
    try {
      if (!this.AsyncStorage) return;
      await this.AsyncStorage.removeItem(key);
    } catch (error) {
      console.warn('AsyncStorage removeItem failed:', error);
    }
  }

  async clear(): Promise<void> {
    try {
      if (!this.AsyncStorage) return;
      await this.AsyncStorage.clear();
    } catch (error) {
      console.warn('AsyncStorage clear failed:', error);
    }
  }
}

// Fallback storage for environments where neither sessionStorage nor AsyncStorage is available
class FallbackStorage implements StorageInterface {
  private storage: Map<string, string> = new Map();

  async getItem(key: string): Promise<string | null> {
    return this.storage.get(key) || null;
  }

  async setItem(key: string, value: string): Promise<void> {
    this.storage.set(key, value);
  }

  async removeItem(key: string): Promise<void> {
    this.storage.delete(key);
  }

  async clear(): Promise<void> {
    this.storage.clear();
  }
}

// Detect platform and create appropriate storage instance
function createStorage(): StorageInterface {
  // Check if we're in a React Native environment
  if (typeof navigator !== 'undefined' && navigator.product === 'ReactNative') {
    return new MobileStorage();
  }
  
  // Check if we're in a web environment with sessionStorage
  if (typeof window !== 'undefined' && typeof sessionStorage !== 'undefined') {
    return new WebStorage();
  }
  
  // Fallback for other environments (SSR, Node.js, etc.)
  return new FallbackStorage();
}

// Export singleton storage instance
export const platformStorage = createStorage();

// Convenience functions for common operations
export const storage = {
  // Session storage (temporary, cleared when app/tab closes)
  session: {
    async get(key: string): Promise<string | null> {
      return await platformStorage.getItem(`session_${key}`);
    },
    
    async set(key: string, value: string): Promise<void> {
      await platformStorage.setItem(`session_${key}`, value);
    },
    
    async remove(key: string): Promise<void> {
      await platformStorage.removeItem(`session_${key}`);
    },
    
    async clear(): Promise<void> {
      // Only clear session keys, not all storage
      // This is a simplified implementation - in a real app you might want to track session keys
      await platformStorage.clear();
    }
  },

  // Local storage (persistent across app restarts)
  local: {
    async get(key: string): Promise<string | null> {
      return await platformStorage.getItem(`local_${key}`);
    },
    
    async set(key: string, value: string): Promise<void> {
      await platformStorage.setItem(`local_${key}`, value);
    },
    
    async remove(key: string): Promise<void> {
      await platformStorage.removeItem(`local_${key}`);
    }
  },

  // JSON helpers
  json: {
    async get<T>(key: string, isSession: boolean = false): Promise<T | null> {
      try {
        const storageType = isSession ? storage.session : storage.local;
        const value = await storageType.get(key);
        return value ? JSON.parse(value) : null;
      } catch (error) {
        console.warn('Failed to parse JSON from storage:', error);
        return null;
      }
    },
    
    async set<T>(key: string, value: T, isSession: boolean = false): Promise<void> {
      try {
        const storageType = isSession ? storage.session : storage.local;
        await storageType.set(key, JSON.stringify(value));
      } catch (error) {
        console.warn('Failed to stringify JSON for storage:', error);
      }
    }
  }
};

export default storage;
