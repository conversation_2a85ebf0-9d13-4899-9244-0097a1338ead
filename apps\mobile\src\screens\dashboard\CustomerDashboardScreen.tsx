import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  RefreshControl,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { useTheme } from '../../providers/ThemeProvider';
import { Button } from '../../components/ui/Button';
import { Card } from '../../components/ui/Card';
import { useAuth } from '../../providers/AuthProvider';

export default function CustomerDashboardScreen() {
  const navigation = useNavigation();
  const { theme } = useTheme();
  const { user } = useAuth();
  const [refreshing, setRefreshing] = useState(false);
  const [stats, setStats] = useState({
    totalBookings: 0,
    totalOrders: 0,
    totalFavorites: 0,
    totalReviews: 0,
  });

  const onRefresh = async () => {
    setRefreshing(true);
    // TODO: Fetch latest data
    setTimeout(() => setRefreshing(false), 1000);
  };

  const dashboardItems = [
    {
      title: 'My Bookings',
      count: stats.totalBookings,
      icon: '📅',
      onPress: () => navigation.navigate('BookingHistory' as never),
    },
    {
      title: 'My Orders',
      count: stats.totalOrders,
      icon: '🛍️',
      onPress: () => navigation.navigate('Orders' as never),
    },
    {
      title: 'Favorites',
      count: stats.totalFavorites,
      icon: '❤️',
      onPress: () => navigation.navigate('Favorites' as never),
    },
    {
      title: 'My Reviews',
      count: stats.totalReviews,
      icon: '⭐',
      onPress: () => navigation.navigate('Reviews' as never, { userId: user?.id }),
    },
  ];

  const quickActions = [
    {
      title: 'Wedding Planning',
      description: 'Plan your perfect wedding',
      icon: '💒',
      onPress: () => navigation.navigate('WeddingPlanning' as never),
    },
    {
      title: 'Budget Tracker',
      description: 'Track your wedding expenses',
      icon: '💰',
      onPress: () => navigation.navigate('BudgetTracker' as never),
    },
    {
      title: 'Guest List',
      description: 'Manage your guest list',
      icon: '👥',
      onPress: () => navigation.navigate('GuestListManager' as never),
    },
    {
      title: 'Timeline',
      description: 'Wedding timeline planner',
      icon: '⏰',
      onPress: () => navigation.navigate('Timeline' as never),
    },
  ];

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    scrollContainer: {
      padding: 16,
      gap: 20,
    },
    header: {
      marginBottom: 8,
    },
    welcomeText: {
      fontSize: 24,
      fontWeight: 'bold',
      color: theme.colors.text,
      marginBottom: 4,
    },
    subtitleText: {
      fontSize: 16,
      color: theme.colors.textSecondary,
    },
    statsGrid: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      gap: 12,
    },
    statCard: {
      flex: 1,
      minWidth: '45%',
      alignItems: 'center',
      padding: 16,
    },
    statIcon: {
      fontSize: 32,
      marginBottom: 8,
    },
    statCount: {
      fontSize: 24,
      fontWeight: 'bold',
      color: theme.colors.primary,
      marginBottom: 4,
    },
    statLabel: {
      fontSize: 14,
      color: theme.colors.textSecondary,
      textAlign: 'center',
    },
    sectionTitle: {
      fontSize: 20,
      fontWeight: 'bold',
      color: theme.colors.text,
      marginBottom: 12,
    },
    actionCard: {
      flexDirection: 'row',
      alignItems: 'center',
      padding: 16,
      marginBottom: 12,
    },
    actionIcon: {
      fontSize: 32,
      marginRight: 16,
    },
    actionContent: {
      flex: 1,
    },
    actionTitle: {
      fontSize: 16,
      fontWeight: '600',
      color: theme.colors.text,
      marginBottom: 4,
    },
    actionDescription: {
      fontSize: 14,
      color: theme.colors.textSecondary,
    },
    actionButton: {
      paddingHorizontal: 16,
      paddingVertical: 8,
    },
  });

  return (
    <View style={styles.container}>
      <ScrollView
        contentContainerStyle={styles.scrollContainer}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        <View style={styles.header}>
          <Text style={styles.welcomeText}>
            Welcome back, {user?.name || 'User'}!
          </Text>
          <Text style={styles.subtitleText}>
            Manage your wedding planning journey
          </Text>
        </View>

        <Card>
          <Text style={styles.sectionTitle}>Overview</Text>
          <View style={styles.statsGrid}>
            {dashboardItems.map((item, index) => (
              <Card key={index} style={styles.statCard} onPress={item.onPress}>
                <Text style={styles.statIcon}>{item.icon}</Text>
                <Text style={styles.statCount}>{item.count}</Text>
                <Text style={styles.statLabel}>{item.title}</Text>
              </Card>
            ))}
          </View>
        </Card>

        <Card>
          <Text style={styles.sectionTitle}>Quick Actions</Text>
          {quickActions.map((action, index) => (
            <Card key={index} style={styles.actionCard} onPress={action.onPress}>
              <Text style={styles.actionIcon}>{action.icon}</Text>
              <View style={styles.actionContent}>
                <Text style={styles.actionTitle}>{action.title}</Text>
                <Text style={styles.actionDescription}>
                  {action.description}
                </Text>
              </View>
              <Button
                variant="outline"
                size="sm"
                style={styles.actionButton}
                onPress={action.onPress}
              >
                Open
              </Button>
            </Card>
          ))}
        </Card>
      </ScrollView>
    </View>
  );
}
