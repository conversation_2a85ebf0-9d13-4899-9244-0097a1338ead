import React, { useRef, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Dimensions,
  Animated,
  PanGestureHandler,
  State,
  Image,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import * as Haptics from 'expo-haptics';
import { useTheme } from '../../providers/ThemeProvider';
import { AnimatedButton } from './index';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

interface OnboardingSlide {
  id: string;
  title: string;
  subtitle: string;
  description: string;
  image?: any;
  icon?: string;
  backgroundColor?: string;
}

interface OnboardingCarouselProps {
  slides: OnboardingSlide[];
  onComplete: () => void;
  onSkip?: () => void;
  showSkipButton?: boolean;
  autoPlay?: boolean;
  autoPlayInterval?: number;
  hapticFeedback?: boolean;
}

export default function OnboardingCarousel({
  slides,
  onComplete,
  onSkip,
  showSkipButton = true,
  autoPlay = false,
  autoPlayInterval = 3000,
  hapticFeedback = true,
}: OnboardingCarouselProps) {
  const { theme } = useTheme();
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isAutoPlaying, setIsAutoPlaying] = useState(autoPlay);
  
  // Animation values
  const translateX = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(0)).current;
  const fadeAnim = useRef(new Animated.Value(1)).current;
  const scaleAnim = useRef(new Animated.Value(1)).current;

  // Auto-play timer
  const autoPlayTimer = useRef<NodeJS.Timeout | null>(null);

  React.useEffect(() => {
    if (isAutoPlaying) {
      startAutoPlay();
    } else {
      stopAutoPlay();
    }

    return () => stopAutoPlay();
  }, [isAutoPlaying, currentIndex]);

  const startAutoPlay = () => {
    stopAutoPlay();
    autoPlayTimer.current = setTimeout(() => {
      if (currentIndex < slides.length - 1) {
        goToNext();
      } else {
        setIsAutoPlaying(false);
      }
    }, autoPlayInterval);
  };

  const stopAutoPlay = () => {
    if (autoPlayTimer.current) {
      clearTimeout(autoPlayTimer.current);
      autoPlayTimer.current = null;
    }
  };

  const goToNext = () => {
    if (currentIndex < slides.length - 1) {
      animateToSlide(currentIndex + 1);
    }
  };

  const goToPrevious = () => {
    if (currentIndex > 0) {
      animateToSlide(currentIndex - 1);
    }
  };

  const goToSlide = (index: number) => {
    if (index >= 0 && index < slides.length) {
      animateToSlide(index);
    }
  };

  const animateToSlide = (index: number) => {
    if (hapticFeedback) {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }

    setCurrentIndex(index);

    // Animate slide transition
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 0,
        duration: 150,
        useNativeDriver: true,
      }),
      Animated.timing(scaleAnim, {
        toValue: 0.9,
        duration: 150,
        useNativeDriver: true,
      }),
    ]).start(() => {
      // Update content and animate back in
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 200,
          useNativeDriver: true,
        }),
        Animated.spring(scaleAnim, {
          toValue: 1,
          tension: 100,
          friction: 8,
          useNativeDriver: true,
        }),
      ]).start();
    });

    // Animate slide indicator
    Animated.timing(slideAnim, {
      toValue: index,
      duration: 300,
      useNativeDriver: true,
    }).start();
  };

  const onGestureEvent = Animated.event(
    [{ nativeEvent: { translationX: translateX } }],
    { useNativeDriver: true }
  );

  const onHandlerStateChange = (event: any) => {
    const { state, translationX } = event.nativeEvent;

    if (state === State.END) {
      const threshold = screenWidth * 0.3;

      if (translationX > threshold && currentIndex > 0) {
        // Swipe right - go to previous
        goToPrevious();
      } else if (translationX < -threshold && currentIndex < slides.length - 1) {
        // Swipe left - go to next
        goToNext();
      }

      // Reset translation
      Animated.spring(translateX, {
        toValue: 0,
        useNativeDriver: true,
      }).start();
    }
  };

  const handleSkip = () => {
    if (hapticFeedback) {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    }
    
    if (onSkip) {
      onSkip();
    } else {
      onComplete();
    }
  };

  const handleComplete = () => {
    if (hapticFeedback) {
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
    }
    
    onComplete();
  };

  const currentSlide = slides[currentIndex];
  const isLastSlide = currentIndex === slides.length - 1;

  return (
    <View style={[styles.container, { backgroundColor: currentSlide.backgroundColor || theme.colors.background }]}>
      {/* Skip Button */}
      {showSkipButton && !isLastSlide && (
        <AnimatedButton
          title="Skip"
          onPress={handleSkip}
          variant="ghost"
          size="small"
          style={styles.skipButton}
          hapticFeedback={hapticFeedback}
        />
      )}

      {/* Main Content */}
      <PanGestureHandler
        onGestureEvent={onGestureEvent}
        onHandlerStateChange={onHandlerStateChange}
      >
        <Animated.View
          style={[
            styles.content,
            {
              opacity: fadeAnim,
              transform: [
                { scale: scaleAnim },
                { translateX },
              ],
            },
          ]}
        >
          {/* Image or Icon */}
          <View style={styles.imageContainer}>
            {currentSlide.image ? (
              <Image source={currentSlide.image} style={styles.image} resizeMode="contain" />
            ) : currentSlide.icon ? (
              <View style={[styles.iconContainer, { backgroundColor: theme.colors.primary }]}>
                <Ionicons
                  name={currentSlide.icon as any}
                  size={80}
                  color="#FFFFFF"
                />
              </View>
            ) : null}
          </View>

          {/* Text Content */}
          <View style={styles.textContainer}>
            <Text style={[styles.title, { color: theme.colors.text }]}>
              {currentSlide.title}
            </Text>
            
            <Text style={[styles.subtitle, { color: theme.colors.primary }]}>
              {currentSlide.subtitle}
            </Text>
            
            <Text style={[styles.description, { color: theme.colors.textSecondary }]}>
              {currentSlide.description}
            </Text>
          </View>
        </Animated.View>
      </PanGestureHandler>

      {/* Slide Indicators */}
      <View style={styles.indicatorContainer}>
        {slides.map((_, index) => {
          const isActive = index === currentIndex;
          return (
            <Animated.View
              key={index}
              style={[
                styles.indicator,
                {
                  backgroundColor: isActive ? theme.colors.primary : theme.colors.border,
                  transform: [
                    {
                      scale: isActive ? 1.2 : 1,
                    },
                  ],
                },
              ]}
            />
          );
        })}
      </View>

      {/* Navigation Buttons */}
      <View style={styles.buttonContainer}>
        {currentIndex > 0 && (
          <AnimatedButton
            title="Previous"
            onPress={goToPrevious}
            variant="outline"
            icon="chevron-back"
            style={styles.navButton}
            hapticFeedback={hapticFeedback}
          />
        )}
        
        <View style={styles.buttonSpacer} />
        
        {isLastSlide ? (
          <AnimatedButton
            title="Get Started"
            onPress={handleComplete}
            variant="primary"
            icon="checkmark"
            iconPosition="right"
            style={styles.navButton}
            hapticFeedback={hapticFeedback}
          />
        ) : (
          <AnimatedButton
            title="Next"
            onPress={goToNext}
            variant="primary"
            icon="chevron-forward"
            iconPosition="right"
            style={styles.navButton}
            hapticFeedback={hapticFeedback}
          />
        )}
      </View>

      {/* Auto-play Control */}
      {autoPlay && (
        <AnimatedButton
          title={isAutoPlaying ? "Pause" : "Play"}
          onPress={() => setIsAutoPlaying(!isAutoPlaying)}
          variant="ghost"
          icon={isAutoPlaying ? "pause" : "play"}
          size="small"
          style={styles.autoPlayButton}
          hapticFeedback={hapticFeedback}
        />
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingTop: 60,
    paddingBottom: 40,
    paddingHorizontal: 20,
  },
  skipButton: {
    position: 'absolute',
    top: 60,
    right: 20,
    zIndex: 1,
  },
  content: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  imageContainer: {
    flex: 0.5,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 40,
  },
  image: {
    width: screenWidth * 0.8,
    height: screenHeight * 0.3,
  },
  iconContainer: {
    width: 160,
    height: 160,
    borderRadius: 80,
    alignItems: 'center',
    justifyContent: 'center',
  },
  textContainer: {
    flex: 0.4,
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 16,
  },
  subtitle: {
    fontSize: 18,
    fontWeight: '600',
    textAlign: 'center',
    marginBottom: 16,
  },
  description: {
    fontSize: 16,
    textAlign: 'center',
    lineHeight: 24,
  },
  indicatorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginVertical: 30,
  },
  indicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginHorizontal: 4,
  },
  buttonContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
  },
  navButton: {
    minWidth: 120,
  },
  buttonSpacer: {
    flex: 1,
  },
  autoPlayButton: {
    position: 'absolute',
    bottom: 100,
    left: 20,
  },
});
