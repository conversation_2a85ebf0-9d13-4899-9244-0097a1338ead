import { ApiResponse, SearchFilters, Vendor, Venue, Product, Review } from '../types';

export class ApiService {
  private baseUrl: string;
  private platform: 'web' | 'mobile';

  constructor(baseUrl: string, platform: 'web' | 'mobile' = 'web') {
    this.baseUrl = baseUrl;
    this.platform = platform;
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    const url = `${this.baseUrl}${endpoint}`;
    
    const defaultHeaders = {
      'Content-Type': 'application/json',
      'X-Platform': this.platform,
    };

    try {
      const response = await fetch(url, {
        ...options,
        headers: {
          ...defaultHeaders,
          ...options.headers,
        },
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || `HTTP error! status: ${response.status}`);
      }

      return data;
    } catch (error) {
      console.error(`API request failed: ${endpoint}`, error);
      throw error;
    }
  }

  // Auth endpoints
  async login(email: string, password: string) {
    return this.request('/auth/login', {
      method: 'POST',
      body: JSON.stringify({ email, password }),
    });
  }

  async signup(userData: any) {
    return this.request('/auth/signup', {
      method: 'POST',
      body: JSON.stringify(userData),
    });
  }

  async logout() {
    return this.request('/auth/logout', {
      method: 'POST',
    });
  }

  async refreshToken() {
    return this.request('/auth/refresh', {
      method: 'POST',
    });
  }

  // Vendor endpoints
  async getVendors(filters: SearchFilters = {}): Promise<ApiResponse<Vendor[]>> {
    const queryParams = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        queryParams.append(key, value.toString());
      }
    });

    return this.request(`/vendors?${queryParams.toString()}`);
  }

  async getVendor(id: string): Promise<ApiResponse<Vendor>> {
    return this.request(`/vendors/${id}`);
  }

  async createVendor(vendorData: Partial<Vendor>) {
    return this.request('/vendors', {
      method: 'POST',
      body: JSON.stringify(vendorData),
    });
  }

  async updateVendor(id: string, vendorData: Partial<Vendor>) {
    return this.request(`/vendors/${id}`, {
      method: 'PUT',
      body: JSON.stringify(vendorData),
    });
  }

  // Venue endpoints
  async getVenues(filters: SearchFilters = {}): Promise<ApiResponse<Venue[]>> {
    const queryParams = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        queryParams.append(key, value.toString());
      }
    });

    return this.request(`/venues?${queryParams.toString()}`);
  }

  async getVenue(id: string): Promise<ApiResponse<Venue>> {
    return this.request(`/venues/${id}`);
  }

  // Product endpoints
  async getProducts(filters: SearchFilters = {}): Promise<ApiResponse<Product[]>> {
    const queryParams = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        queryParams.append(key, value.toString());
      }
    });

    return this.request(`/products?${queryParams.toString()}`);
  }

  async getProduct(id: string): Promise<ApiResponse<Product>> {
    return this.request(`/products/${id}`);
  }

  // Review endpoints
  async getReviews(targetId: string, targetType: string): Promise<ApiResponse<Review[]>> {
    return this.request(`/reviews?targetId=${targetId}&targetType=${targetType}`);
  }

  async createReview(reviewData: Partial<Review>) {
    return this.request('/reviews', {
      method: 'POST',
      body: JSON.stringify(reviewData),
    });
  }

  // Search endpoints
  async search(query: string, filters: SearchFilters = {}) {
    const searchData = { query, ...filters };
    return this.request('/search', {
      method: 'POST',
      body: JSON.stringify(searchData),
    });
  }

  // Favorites endpoints
  async getFavorites() {
    return this.request('/favorites');
  }

  async addToFavorites(itemId: string, itemType: string) {
    return this.request('/favorites', {
      method: 'POST',
      body: JSON.stringify({ itemId, itemType }),
    });
  }

  async removeFromFavorites(itemId: string) {
    return this.request(`/favorites/${itemId}`, {
      method: 'DELETE',
    });
  }

  // User profile endpoints
  async getUserProfile() {
    return this.request('/user/profile');
  }

  async updateUserProfile(profileData: any) {
    return this.request('/user/profile', {
      method: 'PUT',
      body: JSON.stringify(profileData),
    });
  }

  // Inquiry endpoints
  async createInquiry(inquiryData: any) {
    return this.request('/inquiries', {
      method: 'POST',
      body: JSON.stringify(inquiryData),
    });
  }

  async getInquiries() {
    return this.request('/inquiries');
  }

  // Newsletter endpoints
  async subscribeNewsletter(email: string) {
    return this.request('/newsletter/subscribe', {
      method: 'POST',
      body: JSON.stringify({ email }),
    });
  }

  // File upload endpoints
  async uploadFile(file: File | any, type: string = 'image') {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('type', type);

    return this.request('/upload', {
      method: 'POST',
      body: formData,
      headers: {
        // Don't set Content-Type for FormData, let browser set it
      },
    });
  }

  // Location endpoints
  async getCities() {
    return this.request('/locations/cities');
  }

  async getStates() {
    return this.request('/locations/states');
  }

  // Analytics endpoints (for mobile app)
  async trackEvent(eventName: string, properties: any = {}) {
    if (this.platform === 'mobile') {
      return this.request('/analytics/track', {
        method: 'POST',
        body: JSON.stringify({ eventName, properties, platform: 'mobile' }),
      });
    }
  }
}

// DEPRECATED: These API services are deprecated in favor of GraphQL
// Use the GraphQL client from graphqlClient.ts instead

// Create singleton instances (DEPRECATED - Use GraphQL instead)
export const webApiService = new ApiService(
  process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000/api',
  'web'
);

export const mobileApiService = new ApiService(
  process.env.EXPO_PUBLIC_API_URL || 'http://localhost:3000/api',
  'mobile'
);

export default ApiService;
