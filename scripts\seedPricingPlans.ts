/**
 * <PERSON>ript to seed pricing plans for vendor subscriptions
 * Run this script to populate the database with default pricing plans
 */

import { generateClient } from '@aws-amplify/api'
import { createPricingPlan } from '@/src/graphql/mutations'

const client = generateClient()

const defaultPricingPlans = [
  {
    id: 'plan_basic_monthly',
    name: 'Basic',
    description: 'Perfect for new vendors getting started',
    price: 999,
    currency: 'INR',
    duration: 'MONTHLY',
    features: [
      'Basic business profile',
      'Up to 10 photos',
      'Contact form inquiries',
      'Basic analytics',
      'Email support'
    ],
    isActive: true,
    isPopular: false
  },
  {
    id: 'plan_professional_monthly',
    name: 'Professional',
    description: 'Most popular choice for growing businesses',
    price: 1999,
    currency: 'INR',
    duration: 'MONTHLY',
    features: [
      'Enhanced business profile',
      'Up to 50 photos',
      'Priority listing',
      'Advanced analytics',
      'Lead management',
      'Phone & email support',
      'Social media integration'
    ],
    isActive: true,
    isPopular: true
  },
  {
    id: 'plan_premium_monthly',
    name: 'Premium',
    description: 'For established vendors who want maximum exposure',
    price: 3999,
    currency: 'INR',
    duration: 'MONTHLY',
    features: [
      'Premium business profile',
      'Unlimited photos',
      'Featured listing',
      'Comprehensive analytics',
      'Advanced lead management',
      'Priority support',
      'Custom branding',
      'Marketing tools',
      'API access'
    ],
    isActive: true,
    isPopular: false
  },
  {
    id: 'plan_professional_yearly',
    name: 'Professional Annual',
    description: 'Save 20% with annual billing',
    price: 19990, // 20% discount from monthly
    currency: 'INR',
    duration: 'YEARLY',
    features: [
      'Enhanced business profile',
      'Up to 50 photos',
      'Priority listing',
      'Advanced analytics',
      'Lead management',
      'Phone & email support',
      'Social media integration',
      '20% savings vs monthly'
    ],
    isActive: true,
    isPopular: false,
    discountPercentage: 20
  },
  {
    id: 'plan_premium_yearly',
    name: 'Premium Annual',
    description: 'Maximum value with annual commitment',
    price: 38390, // 20% discount from monthly
    currency: 'INR',
    duration: 'YEARLY',
    features: [
      'Premium business profile',
      'Unlimited photos',
      'Featured listing',
      'Comprehensive analytics',
      'Advanced lead management',
      'Priority support',
      'Custom branding',
      'Marketing tools',
      'API access',
      '20% savings vs monthly'
    ],
    isActive: true,
    isPopular: false,
    discountPercentage: 20
  }
]

export async function seedPricingPlans() {
  console.log('Starting to seed pricing plans...')
  
  for (const plan of defaultPricingPlans) {
    try {
      const result = await client.graphql({
        query: createPricingPlan,
        variables: {
          input: {
            ...plan,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
          }
        }
      })
      
      console.log(`✅ Created pricing plan: ${plan.name}`)
    } catch (error) {
      console.error(`❌ Failed to create pricing plan ${plan.name}:`, error)
    }
  }
  
  console.log('Pricing plans seeding completed!')
}

// Run the seeding function if this script is executed directly
if (require.main === module) {
  seedPricingPlans()
    .then(() => {
      console.log('Seeding completed successfully!')
      process.exit(0)
    })
    .catch((error) => {
      console.error('Seeding failed:', error)
      process.exit(1)
    })
}
