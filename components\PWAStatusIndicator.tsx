'use client'

import { usePWADetection } from '@/hooks/use-pwa-detection'
import { Smartphone, Wifi, WifiOff } from 'lucide-react'
import { useState, useEffect } from 'react'

export function PWAStatusIndicator() {
  const { isStandalone } = usePWADetection()
  const [isOnline, setIsOnline] = useState(true)
  const [showIndicator, setShowIndicator] = useState(false)

  useEffect(() => {
    setIsOnline(navigator.onLine)

    const handleOnline = () => {
      setIsOnline(true)
      // Show briefly when coming back online, then hide
      setShowIndicator(true)
      setTimeout(() => setShowIndicator(false), 3000)
    }

    const handleOffline = () => {
      setIsOnline(false)
      // Show when offline
      setShowIndicator(true)
    }

    window.addEventListener('online', handleOnline)
    window.addEventListener('offline', handleOffline)

    return () => {
      window.removeEventListener('online', handleOnline)
      window.removeEventListener('offline', handleOffline)
    }
  }, [])

  // Only show in PWA mode when offline or briefly when coming back online
  if (!isStandalone || !showIndicator) return null

  return (
    <div className="fixed top-4 right-4 z-50 flex items-center gap-2 bg-black/90 text-white px-3 py-2 rounded-lg text-sm shadow-lg transition-all duration-300">
      {isOnline ? (
        <>
          <Wifi className="w-4 h-4 text-green-400" />
          <span>Back Online</span>
        </>
      ) : (
        <>
          <WifiOff className="w-4 h-4 text-red-400" />
          <span>You're Offline</span>
        </>
      )}
    </div>
  )
}
