// Email validation
export const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

// Phone validation (Indian format)
export const isValidPhone = (phone: string): boolean => {
  const phoneRegex = /^[6-9]\d{9}$/;
  return phoneRegex.test(phone.replace(/\D/g, ''));
};

// Password validation
export const isValidPassword = (password: string): boolean => {
  return password.length >= 8;
};

// Strong password validation
export const isStrongPassword = (password: string): boolean => {
  const strongPasswordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/;
  return strongPasswordRegex.test(password);
};

// Name validation
export const isValidName = (name: string): boolean => {
  return name.trim().length >= 2 && name.trim().length <= 50;
};

// Pincode validation (Indian format)
export const isValidPincode = (pincode: string): boolean => {
  const pincodeRegex = /^[1-9][0-9]{5}$/;
  return pincodeRegex.test(pincode);
};

// Price validation
export const isValidPrice = (price: number): boolean => {
  return price > 0 && price <= 10000000; // Max 1 crore
};

// Rating validation
export const isValidRating = (rating: number): boolean => {
  return rating >= 1 && rating <= 5;
};

// URL validation
export const isValidUrl = (url: string): boolean => {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
};

// Image file validation
export const isValidImageFile = (file: File): boolean => {
  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
  const maxSize = 5 * 1024 * 1024; // 5MB
  
  return allowedTypes.includes(file.type) && file.size <= maxSize;
};

// Form validation helpers
export const validateLoginForm = (email: string, password: string) => {
  const errors: Record<string, string> = {};
  
  if (!email) {
    errors.email = 'Email is required';
  } else if (!isValidEmail(email)) {
    errors.email = 'Please enter a valid email address';
  }
  
  if (!password) {
    errors.password = 'Password is required';
  } else if (!isValidPassword(password)) {
    errors.password = 'Password must be at least 8 characters long';
  }
  
  return {
    isValid: Object.keys(errors).length === 0,
    errors,
  };
};

export const validateSignupForm = (data: {
  fullName: string;
  email: string;
  phone?: string;
  password: string;
  confirmPassword: string;
}) => {
  const errors: Record<string, string> = {};
  
  if (!data.fullName) {
    errors.fullName = 'Full name is required';
  } else if (!isValidName(data.fullName)) {
    errors.fullName = 'Please enter a valid name (2-50 characters)';
  }
  
  if (!data.email) {
    errors.email = 'Email is required';
  } else if (!isValidEmail(data.email)) {
    errors.email = 'Please enter a valid email address';
  }
  
  if (data.phone && !isValidPhone(data.phone)) {
    errors.phone = 'Please enter a valid 10-digit phone number';
  }
  
  if (!data.password) {
    errors.password = 'Password is required';
  } else if (!isValidPassword(data.password)) {
    errors.password = 'Password must be at least 8 characters long';
  }
  
  if (!data.confirmPassword) {
    errors.confirmPassword = 'Please confirm your password';
  } else if (data.password !== data.confirmPassword) {
    errors.confirmPassword = 'Passwords do not match';
  }
  
  return {
    isValid: Object.keys(errors).length === 0,
    errors,
  };
};

export const validateProfileForm = (data: {
  fullName: string;
  email: string;
  phone?: string;
  city?: string;
  pincode?: string;
}) => {
  const errors: Record<string, string> = {};
  
  if (!data.fullName) {
    errors.fullName = 'Full name is required';
  } else if (!isValidName(data.fullName)) {
    errors.fullName = 'Please enter a valid name (2-50 characters)';
  }
  
  if (!data.email) {
    errors.email = 'Email is required';
  } else if (!isValidEmail(data.email)) {
    errors.email = 'Please enter a valid email address';
  }
  
  if (data.phone && !isValidPhone(data.phone)) {
    errors.phone = 'Please enter a valid 10-digit phone number';
  }
  
  if (data.pincode && !isValidPincode(data.pincode)) {
    errors.pincode = 'Please enter a valid 6-digit pincode';
  }
  
  return {
    isValid: Object.keys(errors).length === 0,
    errors,
  };
};

export const validateReviewForm = (data: {
  rating: number;
  title: string;
  comment: string;
}) => {
  const errors: Record<string, string> = {};
  
  if (!data.rating) {
    errors.rating = 'Rating is required';
  } else if (!isValidRating(data.rating)) {
    errors.rating = 'Please select a rating between 1 and 5';
  }
  
  if (!data.title) {
    errors.title = 'Review title is required';
  } else if (data.title.length < 5 || data.title.length > 100) {
    errors.title = 'Title must be between 5 and 100 characters';
  }
  
  if (!data.comment) {
    errors.comment = 'Review comment is required';
  } else if (data.comment.length < 10 || data.comment.length > 1000) {
    errors.comment = 'Comment must be between 10 and 1000 characters';
  }
  
  return {
    isValid: Object.keys(errors).length === 0,
    errors,
  };
};

// Sanitization helpers
export const sanitizeInput = (input: string): string => {
  return input.trim().replace(/[<>]/g, '');
};

export const sanitizeHtml = (html: string): string => {
  return html.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '');
};

// Input formatting helpers
export const formatPhoneInput = (phone: string): string => {
  const cleaned = phone.replace(/\D/g, '');
  if (cleaned.length <= 10) {
    return cleaned;
  }
  return cleaned.slice(0, 10);
};

export const formatPincodeInput = (pincode: string): string => {
  const cleaned = pincode.replace(/\D/g, '');
  if (cleaned.length <= 6) {
    return cleaned;
  }
  return cleaned.slice(0, 6);
};
