import AsyncStorage from '@react-native-async-storage/async-storage';
import { networkService, NetworkState } from './networkService';
import { cacheService } from './cacheService';

export interface OfflineAction {
  id: string;
  type: 'CREATE' | 'UPDATE' | 'DELETE';
  entity: string; // 'vendor', 'venue', 'shop', 'order', 'review', etc.
  data: any;
  timestamp: number;
  retryCount: number;
  maxRetries: number;
  priority: 'low' | 'normal' | 'high';
}

export interface SyncResult {
  success: boolean;
  syncedActions: number;
  failedActions: number;
  errors: Array<{ actionId: string; error: string }>;
}

export interface OfflineData {
  vendors: any[];
  venues: any[];
  shops: any[];
  categories: any[];
  cities: any[];
  userProfile: any;
  favorites: any[];
  searchHistory: any[];
  cart: any;
  lastSync: number;
}

class OfflineService {
  private readonly OFFLINE_ACTIONS_KEY = 'offline_actions';
  private readonly OFFLINE_DATA_KEY = 'offline_data';
  private readonly SYNC_INTERVAL = 30000; // 30 seconds
  private syncTimer: NodeJS.Timeout | null = null;
  private isSyncing = false;
  private listeners: Array<(isOffline: boolean) => void> = [];

  constructor() {
    this.initialize();
  }

  private initialize() {
    // Listen to network changes
    networkService.addListener(this.handleNetworkChange);
    
    // Start periodic sync when online
    this.startPeriodicSync();
  }

  private handleNetworkChange = (networkState: NetworkState) => {
    const isOffline = !networkState.isConnected || !networkState.isInternetReachable;
    
    // Notify listeners about offline state change
    this.notifyListeners(isOffline);
    
    if (!isOffline && networkService.shouldSyncData()) {
      // Network is back, start syncing
      this.syncPendingActions();
    }
  };

  private notifyListeners(isOffline: boolean) {
    this.listeners.forEach(listener => {
      try {
        listener(isOffline);
      } catch (error) {
        console.error('Error in offline listener:', error);
      }
    });
  }

  private startPeriodicSync() {
    if (this.syncTimer) {
      clearInterval(this.syncTimer);
    }
    
    this.syncTimer = setInterval(() => {
      if (networkService.isOnline() && !this.isSyncing) {
        this.syncPendingActions();
      }
    }, this.SYNC_INTERVAL);
  }

  // Queue offline actions
  public async queueAction(action: Omit<OfflineAction, 'id' | 'timestamp' | 'retryCount'>): Promise<void> {
    try {
      const offlineAction: OfflineAction = {
        ...action,
        id: `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        timestamp: Date.now(),
        retryCount: 0,
        maxRetries: action.maxRetries || 3,
      };

      const existingActions = await this.getPendingActions();
      existingActions.push(offlineAction);
      
      await AsyncStorage.setItem(this.OFFLINE_ACTIONS_KEY, JSON.stringify(existingActions));
      
      console.log(`Queued offline action: ${action.type} ${action.entity}`);
    } catch (error) {
      console.error('Error queueing offline action:', error);
      throw error;
    }
  }

  // Get pending actions
  public async getPendingActions(): Promise<OfflineAction[]> {
    try {
      const actionsData = await AsyncStorage.getItem(this.OFFLINE_ACTIONS_KEY);
      return actionsData ? JSON.parse(actionsData) : [];
    } catch (error) {
      console.error('Error getting pending actions:', error);
      return [];
    }
  }

  // Sync pending actions with server
  public async syncPendingActions(): Promise<SyncResult> {
    if (this.isSyncing || networkService.isOffline()) {
      return {
        success: false,
        syncedActions: 0,
        failedActions: 0,
        errors: [{ actionId: 'sync', error: 'Already syncing or offline' }],
      };
    }

    this.isSyncing = true;
    const result: SyncResult = {
      success: true,
      syncedActions: 0,
      failedActions: 0,
      errors: [],
    };

    try {
      const pendingActions = await this.getPendingActions();
      
      if (pendingActions.length === 0) {
        this.isSyncing = false;
        return result;
      }

      console.log(`Syncing ${pendingActions.length} pending actions...`);

      // Sort by priority and timestamp
      const sortedActions = pendingActions.sort((a, b) => {
        const priorityOrder = { high: 3, normal: 2, low: 1 };
        const priorityDiff = priorityOrder[b.priority] - priorityOrder[a.priority];
        if (priorityDiff !== 0) return priorityDiff;
        return a.timestamp - b.timestamp;
      });

      const remainingActions: OfflineAction[] = [];

      for (const action of sortedActions) {
        try {
          await this.executeAction(action);
          result.syncedActions++;
          console.log(`Synced action: ${action.type} ${action.entity} (${action.id})`);
        } catch (error) {
          action.retryCount++;
          
          if (action.retryCount < action.maxRetries) {
            remainingActions.push(action);
          } else {
            result.failedActions++;
            result.errors.push({
              actionId: action.id,
              error: error instanceof Error ? error.message : 'Unknown error',
            });
            console.error(`Failed to sync action after ${action.maxRetries} retries:`, action.id, error);
          }
        }
      }

      // Update pending actions
      await AsyncStorage.setItem(this.OFFLINE_ACTIONS_KEY, JSON.stringify(remainingActions));
      
      result.success = result.failedActions === 0;
      
    } catch (error) {
      console.error('Error during sync:', error);
      result.success = false;
      result.errors.push({
        actionId: 'sync',
        error: error instanceof Error ? error.message : 'Unknown sync error',
      });
    } finally {
      this.isSyncing = false;
    }

    return result;
  }

  private async executeAction(action: OfflineAction): Promise<void> {
    // This would integrate with your GraphQL service
    // For now, we'll simulate the API calls
    
    switch (action.entity) {
      case 'vendor':
        await this.syncVendorAction(action);
        break;
      case 'venue':
        await this.syncVenueAction(action);
        break;
      case 'shop':
        await this.syncShopAction(action);
        break;
      case 'order':
        await this.syncOrderAction(action);
        break;
      case 'review':
        await this.syncReviewAction(action);
        break;
      case 'favorite':
        await this.syncFavoriteAction(action);
        break;
      default:
        throw new Error(`Unknown entity type: ${action.entity}`);
    }
  }

  private async syncVendorAction(action: OfflineAction): Promise<void> {
    // Simulate API call - replace with actual GraphQL mutations
    console.log(`Syncing vendor ${action.type}:`, action.data);
    
    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Simulate occasional failures for testing
    if (Math.random() < 0.1) {
      throw new Error('Simulated network error');
    }
  }

  private async syncVenueAction(action: OfflineAction): Promise<void> {
    console.log(`Syncing venue ${action.type}:`, action.data);
    await new Promise(resolve => setTimeout(resolve, 1000));
    if (Math.random() < 0.1) throw new Error('Simulated network error');
  }

  private async syncShopAction(action: OfflineAction): Promise<void> {
    console.log(`Syncing shop ${action.type}:`, action.data);
    await new Promise(resolve => setTimeout(resolve, 1000));
    if (Math.random() < 0.1) throw new Error('Simulated network error');
  }

  private async syncOrderAction(action: OfflineAction): Promise<void> {
    console.log(`Syncing order ${action.type}:`, action.data);
    await new Promise(resolve => setTimeout(resolve, 1000));
    if (Math.random() < 0.1) throw new Error('Simulated network error');
  }

  private async syncReviewAction(action: OfflineAction): Promise<void> {
    console.log(`Syncing review ${action.type}:`, action.data);
    await new Promise(resolve => setTimeout(resolve, 1000));
    if (Math.random() < 0.1) throw new Error('Simulated network error');
  }

  private async syncFavoriteAction(action: OfflineAction): Promise<void> {
    console.log(`Syncing favorite ${action.type}:`, action.data);
    await new Promise(resolve => setTimeout(resolve, 1000));
    if (Math.random() < 0.1) throw new Error('Simulated network error');
  }

  // Cache essential data for offline use
  public async cacheEssentialData(data: Partial<OfflineData>): Promise<void> {
    try {
      const existingData = await this.getOfflineData();
      const updatedData: OfflineData = {
        ...existingData,
        ...data,
        lastSync: Date.now(),
      };

      await AsyncStorage.setItem(this.OFFLINE_DATA_KEY, JSON.stringify(updatedData));
      
      // Also cache individual items for faster access
      if (data.vendors) {
        await cacheService.set('vendors', data.vendors, { ttl: 24 * 60 * 60 * 1000 }); // 24 hours
      }
      if (data.venues) {
        await cacheService.set('venues', data.venues, { ttl: 24 * 60 * 60 * 1000 });
      }
      if (data.shops) {
        await cacheService.set('shops', data.shops, { ttl: 24 * 60 * 60 * 1000 });
      }
      
    } catch (error) {
      console.error('Error caching essential data:', error);
      throw error;
    }
  }

  // Get offline data
  public async getOfflineData(): Promise<OfflineData> {
    try {
      const dataString = await AsyncStorage.getItem(this.OFFLINE_DATA_KEY);
      if (dataString) {
        return JSON.parse(dataString);
      }
    } catch (error) {
      console.error('Error getting offline data:', error);
    }

    // Return default structure
    return {
      vendors: [],
      venues: [],
      shops: [],
      categories: [],
      cities: [],
      userProfile: null,
      favorites: [],
      searchHistory: [],
      cart: null,
      lastSync: 0,
    };
  }

  // Check if data is stale and needs refresh
  public async isDataStale(maxAge: number = 24 * 60 * 60 * 1000): Promise<boolean> {
    try {
      const offlineData = await this.getOfflineData();
      return Date.now() - offlineData.lastSync > maxAge;
    } catch (error) {
      console.error('Error checking data staleness:', error);
      return true;
    }
  }

  // Clear all offline data
  public async clearOfflineData(): Promise<void> {
    try {
      await AsyncStorage.removeItem(this.OFFLINE_DATA_KEY);
      await AsyncStorage.removeItem(this.OFFLINE_ACTIONS_KEY);
      await cacheService.clear();
    } catch (error) {
      console.error('Error clearing offline data:', error);
      throw error;
    }
  }

  // Listeners for offline state changes
  public addOfflineListener(listener: (isOffline: boolean) => void): () => void {
    this.listeners.push(listener);
    
    return () => {
      const index = this.listeners.indexOf(listener);
      if (index > -1) {
        this.listeners.splice(index, 1);
      }
    };
  }

  // Get current offline status
  public isOffline(): boolean {
    return networkService.isOffline();
  }

  // Get sync status
  public isSyncInProgress(): boolean {
    return this.isSyncing;
  }

  // Manual sync trigger
  public async forceSync(): Promise<SyncResult> {
    return this.syncPendingActions();
  }

  // Cleanup
  public destroy(): void {
    if (this.syncTimer) {
      clearInterval(this.syncTimer);
      this.syncTimer = null;
    }
    this.listeners = [];
  }
}

// Singleton instance
export const offlineService = new OfflineService();
export default OfflineService;
