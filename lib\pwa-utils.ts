'use client'

// Simple PWA utilities without complex dependencies
export const registerServiceWorker = async () => {
  if (typeof window !== 'undefined' && 'serviceWorker' in navigator) {
    try {
      // First, test if the service worker file is accessible
      console.log('Testing service worker file accessibility...')
      const swResponse = await fetch('/sw.js');
      if (!swResponse.ok) {
        throw new Error(`Service worker file not accessible: ${swResponse.status} ${swResponse.statusText}`);
      }
      console.log('Service worker file is accessible')

      // Try to register the service worker
      console.log('Registering service worker...')
      const registration = await navigator.serviceWorker.register('/sw.js', {
        scope: '/',
        updateViaCache: 'none' // Always check for updates
      })

      console.log('Service Worker registered successfully:', registration)
      console.log('Service Worker scope:', registration.scope)
      console.log('Service Worker active:', registration.active)

      // Check for updates immediately
      registration.update()

      // Listen for updates
      registration.addEventListener('updatefound', () => {
        const newWorker = registration.installing
        if (newWorker) {
          newWorker.addEventListener('statechange', () => {
            if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
              // New content is available, show update notification
              showUpdateNotification()
            }
          })
        }
      })

      // Check for updates more frequently in development, less in production
      const updateInterval = process.env.NODE_ENV === 'development' ? 10000 : 60000 // 10s dev, 1min prod
      setInterval(() => {
        registration.update()
      }, updateInterval)

      // Also check for updates when page becomes visible
      document.addEventListener('visibilitychange', () => {
        if (!document.hidden) {
          registration.update()
        }
      })

      return registration
    } catch (error) {
      console.error('Service Worker registration failed:', error)
      console.error('Error details:', {
        message: error.message,
        stack: error.stack,
        name: error.name
      })

      // Try to provide more specific error information
      if (error.message.includes('fetch')) {
        console.error('This appears to be a fetch error. Check if /sw.js is accessible.')
      }
      if (error.message.includes('script')) {
        console.error('This appears to be a script error. Check sw.js for syntax errors.')
      }

      // If the above fails, try a simpler registration without pre-fetch check
      console.log('Trying fallback service worker registration...')
      try {
        const fallbackRegistration = await navigator.serviceWorker.register('/sw.js')
        console.log('Fallback service worker registration successful:', fallbackRegistration)
        return fallbackRegistration
      } catch (fallbackError) {
        console.error('Fallback service worker registration also failed:', fallbackError)
      }
    }
  }
}

// Simple service worker registration without pre-checks (fallback)
export const registerServiceWorkerSimple = async () => {
  if (typeof window !== 'undefined' && 'serviceWorker' in navigator) {
    try {
      console.log('Simple service worker registration...')
      const registration = await navigator.serviceWorker.register('/sw.js')
      console.log('Simple service worker registered:', registration)
      return registration
    } catch (error) {
      console.error('Simple service worker registration failed:', error)
      return null
    }
  }
  return null
}

// Show update notification to user
const showUpdateNotification = () => {
  if (typeof window !== 'undefined') {
    // Create a simple notification
    const notification = document.createElement('div')
    notification.innerHTML = `
      <div style="
        position: fixed;
        top: 20px;
        right: 20px;
        background: #4f46e5;
        color: white;
        padding: 16px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        z-index: 10000;
        max-width: 300px;
        font-family: system-ui, -apple-system, sans-serif;
      ">
        <div style="font-weight: 600; margin-bottom: 8px;">Update Available</div>
        <div style="font-size: 14px; margin-bottom: 12px;">New content is available. Refresh to get the latest version.</div>
        <button onclick="
          if ('serviceWorker' in navigator && navigator.serviceWorker.controller) {
            navigator.serviceWorker.controller.postMessage({type: 'SKIP_WAITING'});
            navigator.serviceWorker.controller.postMessage({type: 'CLEAR_CACHE'});
          }
          setTimeout(() => window.location.reload(true), 100);
        " style="
          background: white;
          color: #4f46e5;
          border: none;
          padding: 8px 16px;
          border-radius: 4px;
          font-weight: 600;
          cursor: pointer;
          margin-right: 8px;
        ">Refresh</button>
        <button onclick="this.parentElement.parentElement.remove()" style="
          background: transparent;
          color: white;
          border: 1px solid rgba(255,255,255,0.3);
          padding: 8px 16px;
          border-radius: 4px;
          cursor: pointer;
        ">Later</button>
      </div>
    `
    document.body.appendChild(notification)

    // Auto-remove after 10 seconds
    setTimeout(() => {
      if (notification.parentElement) {
        notification.remove()
      }
    }, 10000)
  }
}

// Utility function to clear all caches manually
export const clearAllCaches = async () => {
  if ('caches' in window) {
    try {
      const cacheNames = await caches.keys()
      await Promise.all(
        cacheNames.map(cacheName => caches.delete(cacheName))
      )
      console.log('All caches cleared successfully')
      return true
    } catch (error) {
      console.error('Failed to clear caches:', error)
      return false
    }
  }
  return false
}

// Utility function to force service worker update
export const forceServiceWorkerUpdate = async () => {
  if ('serviceWorker' in navigator) {
    try {
      const registration = await navigator.serviceWorker.getRegistration()
      if (registration) {
        await registration.update()
        console.log('Service worker update forced')
        return true
      }
    } catch (error) {
      console.error('Failed to force service worker update:', error)
    }
  }
  return false
}

// Utility function to completely refresh the app (clear cache + reload)
export const forceAppRefresh = async () => {
  try {
    // Clear all caches
    await clearAllCaches()

    // Force service worker update
    await forceServiceWorkerUpdate()

    // Send message to service worker to skip waiting
    if (navigator.serviceWorker.controller) {
      navigator.serviceWorker.controller.postMessage({type: 'SKIP_WAITING'})
      navigator.serviceWorker.controller.postMessage({type: 'CLEAR_CACHE'})
    }

    // Wait a bit then reload
    setTimeout(() => {
      window.location.reload(true)
    }, 200)

    return true
  } catch (error) {
    console.error('Failed to force app refresh:', error)
    // Fallback to simple reload
    window.location.reload(true)
    return false
  }
}

// Check if app is running in standalone mode (PWA)
export const isPWAStandalone = (): boolean => {
  if (typeof window === 'undefined') return false
  
  return (
    window.matchMedia('(display-mode: standalone)').matches ||
    (window.navigator as any).standalone === true ||
    document.referrer.includes('android-app://') ||
    window.location.search.includes('utm_source=homescreen')
  )
}

// Check if PWA can be installed
export const canInstallPWA = (): boolean => {
  if (typeof window === 'undefined') return false
  return 'serviceWorker' in navigator && !isPWAStandalone()
}

// Force cache refresh
export const refreshCache = async (): Promise<void> => {
  if (typeof window !== 'undefined' && 'serviceWorker' in navigator) {
    try {
      // Clear all caches
      const cacheNames = await caches.keys()
      await Promise.all(
        cacheNames.map(cacheName => caches.delete(cacheName))
      )

      // Unregister service worker
      const registrations = await navigator.serviceWorker.getRegistrations()
      await Promise.all(
        registrations.map(registration => registration.unregister())
      )

      // Re-register service worker
      await registerServiceWorker()

      console.log('Cache refreshed successfully')
    } catch (error) {
      console.error('Failed to refresh cache:', error)
    }
  }
}

// Clear specific cache
export const clearCache = async (cacheName?: string): Promise<void> => {
  if (typeof window !== 'undefined' && 'caches' in window) {
    try {
      if (cacheName) {
        await caches.delete(cacheName)
        console.log(`Cache ${cacheName} cleared`)
      } else {
        const cacheNames = await caches.keys()
        await Promise.all(
          cacheNames.map(name => caches.delete(name))
        )
        console.log('All caches cleared')
      }
    } catch (error) {
      console.error('Failed to clear cache:', error)
    }
  }
}

// Check cache status
export const getCacheInfo = async (): Promise<{
  caches: string[];
  totalSize: number;
}> => {
  if (typeof window === 'undefined' || !('caches' in window)) {
    return { caches: [], totalSize: 0 }
  }

  try {
    const cacheNames = await caches.keys()
    let totalSize = 0

    for (const cacheName of cacheNames) {
      const cache = await caches.open(cacheName)
      const requests = await cache.keys()

      for (const request of requests) {
        const response = await cache.match(request)
        if (response) {
          const blob = await response.blob()
          totalSize += blob.size
        }
      }
    }

    return {
      caches: cacheNames,
      totalSize
    }
  } catch (error) {
    console.error('Failed to get cache info:', error)
    return { caches: [], totalSize: 0 }
  }
}
