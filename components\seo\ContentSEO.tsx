'use client';

import { NextSeo, ArticleJsonLd, LocalBusinessJsonLd, ProductJsonLd } from 'next-seo';
import { SITE_URL, businessInfo } from '@/lib/config/seo';

// Blog Post SEO Component
interface BlogPostSEOProps {
  title: string;
  description: string;
  content: string;
  image?: string;
  slug: string;
  publishedAt: string;
  updatedAt?: string;
  author: {
    name: string;
    image?: string;
  };
  tags?: string[];
  category?: string;
}

export const BlogPostSEO: React.FC<BlogPostSEOProps> = ({
  title,
  description,
  content,
  image,
  slug,
  publishedAt,
  updatedAt,
  author,
  tags = [],
  category,
}) => {
  const url = `${SITE_URL}/blog/${slug}`;
  const imageUrl = image?.startsWith('http') ? image : `${SITE_URL}${image}`;
  
  // Extract first 160 characters for meta description if not provided
  const metaDescription = description || content.replace(/<[^>]*>/g, '').substring(0, 160) + '...';
  
  const keywords = [
    'wedding blog',
    'wedding tips',
    'wedding planning',
    ...(tags || []),
    ...(category ? [category] : []),
  ].join(', ');

  return (
    <>
      <NextSeo
        title={`${title} | BookmyFestive Blog`}
        description={metaDescription}
        canonical={url}
        openGraph={{
          type: 'article',
          article: {
            publishedTime: publishedAt,
            modifiedTime: updatedAt || publishedAt,
            authors: [author.name],
            tags: tags,
            section: category,
          },
          url,
          title: `${title} | BookmyFestive Blog`,
          description: metaDescription,
          images: imageUrl ? [
            {
              url: imageUrl,
              width: 1200,
              height: 630,
              alt: title,
            },
          ] : undefined,
        }}
        twitter={{
          handle: '@BookmyFestive',
          site: '@BookmyFestive',
          cardType: 'summary_large_image',
        }}
        additionalMetaTags={[
          {
            name: 'keywords',
            content: keywords,
          },
          {
            name: 'author',
            content: author.name,
          },
        ]}
      />
      <ArticleJsonLd
        url={url}
        title={title}
        images={imageUrl ? [imageUrl] : []}
        datePublished={publishedAt}
        dateModified={updatedAt || publishedAt}
        authorName={[author.name]}
        publisherName="BookmyFestive"
        publisherLogo={`${SITE_URL}/BookmyFestive-logo.png`}
        description={metaDescription}
      />
    </>
  );
};

// Vendor SEO Component
interface VendorSEOProps {
  name: string;
  description: string;
  category: string;
  location: string;
  image?: string;
  slug: string;
  rating?: number;
  reviewCount?: number;
  priceRange?: string;
  services?: string[];
  phone?: string;
  email?: string;
  website?: string;
  address?: {
    street: string;
    city: string;
    state: string;
    postalCode: string;
  };
}

export const VendorSEO: React.FC<VendorSEOProps> = ({
  name,
  description,
  category,
  location,
  image,
  slug,
  rating,
  reviewCount,
  priceRange,
  services = [],
  phone,
  email,
  website,
  address,
}) => {
  const url = `${SITE_URL}/vendors/${slug}`;
  const imageUrl = image?.startsWith('http') ? image : `${SITE_URL}${image}`;
  
  const keywords = [
    `${category} ${location}`,
    `wedding ${category}`,
    `${category} services`,
    name,
    ...services,
    location,
  ].join(', ');

  const metaDescription = `${name} - Professional ${category} in ${location}. ${description.substring(0, 120)}...`;

  return (
    <>
      <NextSeo
        title={`${name} - ${category} in ${location} | BookmyFestive`}
        description={metaDescription}
        canonical={url}
        openGraph={{
          type: 'business.business',
          url,
          title: `${name} - ${category} in ${location}`,
          description: metaDescription,
          images: imageUrl ? [
            {
              url: imageUrl,
              width: 1200,
              height: 630,
              alt: `${name} - ${category}`,
            },
          ] : undefined,
        }}
        additionalMetaTags={[
          {
            name: 'keywords',
            content: keywords,
          },
        ]}
      />
      <LocalBusinessJsonLd
        type="ProfessionalService"
        id={url}
        name={name}
        description={description}
        url={url}
        telephone={phone}
        address={{
          streetAddress: address?.street || '',
          addressLocality: address?.city || location,
          addressRegion: address?.state || '',
          postalCode: address?.postalCode || '',
          addressCountry: 'IN',
        }}
        geo={{
          latitude: '',
          longitude: '',
        }}
        images={imageUrl ? [imageUrl] : []}
        sameAs={website ? [website] : []}
        openingHours={[
          {
            opens: '09:00',
            closes: '18:00',
            dayOfWeek: ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'],
          },
        ]}
        rating={rating ? {
          ratingValue: rating.toString(),
          ratingCount: reviewCount?.toString() || '1',
        } : undefined}
        review={rating ? [{
          author: 'BookmyFestive Users',
          datePublished: new Date().toISOString(),
          reviewBody: `Highly rated ${category} service provider`,
          name: `${name} Review`,
          reviewRating: {
            bestRating: '5',
            ratingValue: rating.toString(),
            worstRating: '1',
          },
        }] : undefined}
        makesOffer={services.map(service => ({
          priceSpecification: {
            type: 'UnitPriceSpecification',
            priceCurrency: 'INR',
            price: priceRange || 'Contact for pricing',
          },
          itemOffered: {
            name: service,
            description: `Professional ${service} services`,
          },
        }))}
      />
    </>
  );
};

// Venue SEO Component
interface VenueSEOProps {
  name: string;
  description: string;
  location: string;
  image?: string;
  slug: string;
  capacity?: number;
  priceRange?: string;
  amenities?: string[];
  venueType?: string;
  rating?: number;
  reviewCount?: number;
  phone?: string;
  email?: string;
  website?: string;
  address?: {
    street: string;
    city: string;
    state: string;
    postalCode: string;
  };
}

export const VenueSEO: React.FC<VenueSEOProps> = ({
  name,
  description,
  location,
  image,
  slug,
  capacity,
  priceRange,
  amenities = [],
  venueType = 'Wedding Venue',
  rating,
  reviewCount,
  phone,
  email,
  website,
  address,
}) => {
  const url = `${SITE_URL}/venues/${slug}`;
  const imageUrl = image?.startsWith('http') ? image : `${SITE_URL}${image}`;
  
  const keywords = [
    `wedding venue ${location}`,
    `${venueType} ${location}`,
    'marriage hall',
    'wedding hall',
    name,
    ...amenities,
    location,
  ].join(', ');

  const metaDescription = `${name} - Beautiful ${venueType} in ${location}${capacity ? ` for up to ${capacity} guests` : ''}. ${description.substring(0, 100)}...`;

  return (
    <>
      <NextSeo
        title={`${name} - ${venueType} in ${location} | BookmyFestive`}
        description={metaDescription}
        canonical={url}
        openGraph={{
          type: 'business.business',
          url,
          title: `${name} - ${venueType} in ${location}`,
          description: metaDescription,
          images: imageUrl ? [
            {
              url: imageUrl,
              width: 1200,
              height: 630,
              alt: `${name} - ${venueType}`,
            },
          ] : undefined,
        }}
        additionalMetaTags={[
          {
            name: 'keywords',
            content: keywords,
          },
        ]}
      />
      <LocalBusinessJsonLd
        type="EventVenue"
        id={url}
        name={name}
        description={description}
        url={url}
        telephone={phone}
        address={{
          streetAddress: address?.street || '',
          addressLocality: address?.city || location,
          addressRegion: address?.state || '',
          postalCode: address?.postalCode || '',
          addressCountry: 'IN',
        }}
        geo={{
          latitude: '',
          longitude: '',
        }}
        images={imageUrl ? [imageUrl] : []}
        sameAs={website ? [website] : []}
        openingHours={[
          {
            opens: '09:00',
            closes: '22:00',
            dayOfWeek: ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'],
          },
        ]}
        rating={rating ? {
          ratingValue: rating.toString(),
          ratingCount: reviewCount?.toString() || '1',
        } : undefined}
        maximumAttendeeCapacity={capacity}
      />
    </>
  );
};

// Shop Product SEO Component
interface ShopProductSEOProps {
  name: string;
  description: string;
  category: string;
  image?: string;
  slug: string;
  price?: number;
  originalPrice?: number;
  brand?: string;
  availability?: 'InStock' | 'OutOfStock' | 'PreOrder';
  rating?: number;
  reviewCount?: number;
  sku?: string;
  tags?: string[];
}

export const ShopProductSEO: React.FC<ShopProductSEOProps> = ({
  name,
  description,
  category,
  image,
  slug,
  price,
  originalPrice,
  brand,
  availability = 'InStock',
  rating,
  reviewCount,
  sku,
  tags = [],
}) => {
  const url = `${SITE_URL}/shop/${slug}`;
  const imageUrl = image?.startsWith('http') ? image : `${SITE_URL}${image}`;

  const keywords = [
    `wedding ${category}`,
    `bridal ${category}`,
    name,
    ...(brand ? [brand] : []),
    ...tags,
    'Festive Shopping',
    'bridal wear',
  ].join(', ');

  const metaDescription = `${name}${brand ? ` by ${brand}` : ''} - ${category} for weddings. ${description.substring(0, 120)}...`;

  return (
    <>
      <NextSeo
        title={`${name} - ${category} | BookmyFestive Shop`}
        description={metaDescription}
        canonical={url}
        openGraph={{
          type: 'product',
          url,
          title: `${name} - ${category}`,
          description: metaDescription,
          images: imageUrl ? [
            {
              url: imageUrl,
              width: 1200,
              height: 630,
              alt: name,
            },
          ] : undefined,
          product: {
            priceAmount: price,
            priceCurrency: 'INR',
            availability: availability,
            condition: 'new',
            brand: brand,
          },
        }}
        additionalMetaTags={[
          {
            name: 'keywords',
            content: keywords,
          },
        ]}
      />
      <ProductJsonLd
        productName={name}
        images={imageUrl ? [imageUrl] : []}
        description={description}
        brand={brand}
        manufacturerName={brand}
        manufacturerLogo={brand ? `${SITE_URL}/brands/${brand.toLowerCase()}-logo.png` : undefined}
        slogan={`Beautiful ${category} for your special day`}
        disambiguatingDescription={`Premium ${category} available at BookmyFestive`}
        releaseDate={new Date().toISOString()}
        productionDate={new Date().toISOString()}
        purchaseDate={new Date().toISOString()}
        award="Best Wedding Product"
        reviews={rating && reviewCount ? [{
          author: 'BookmyFestive Customer',
          datePublished: new Date().toISOString(),
          reviewBody: `Excellent ${category} with great quality and design`,
          name: `${name} Review`,
          reviewRating: {
            bestRating: '5',
            ratingValue: rating.toString(),
            worstRating: '1',
          },
        }] : []}
        aggregateRating={rating && reviewCount ? {
          ratingValue: rating.toString(),
          reviewCount: reviewCount.toString(),
        } : undefined}
        offers={[
          {
            price: price?.toString() || '0',
            priceCurrency: 'INR',
            priceValidUntil: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
            itemCondition: 'https://schema.org/NewCondition',
            availability: `https://schema.org/${availability}`,
            url,
            seller: {
              name: 'BookmyFestive',
            },
          },
        ]}
        mpn={sku}
        sku={sku}
      />
    </>
  );
};
