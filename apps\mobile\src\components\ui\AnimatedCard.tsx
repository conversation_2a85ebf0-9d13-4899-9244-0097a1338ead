import React, { useRef, useEffect } from 'react';
import {
  View,
  TouchableOpacity,
  StyleSheet,
  Animated,
  ViewStyle,
  Dimensions,
} from 'react-native';
import * as Haptics from 'expo-haptics';
import { useTheme } from '../../providers/ThemeProvider';

const { width: screenWidth } = Dimensions.get('window');

interface AnimatedCardProps {
  children: React.ReactNode;
  onPress?: () => void;
  style?: ViewStyle;
  elevation?: number;
  borderRadius?: number;
  padding?: number;
  margin?: number;
  animationType?: 'fadeIn' | 'slideUp' | 'slideLeft' | 'scale' | 'flip';
  animationDelay?: number;
  animationDuration?: number;
  hapticFeedback?: boolean;
  pressAnimation?: boolean;
  shadowColor?: string;
  backgroundColor?: string;
}

export default function AnimatedCard({
  children,
  onPress,
  style,
  elevation = 2,
  borderRadius,
  padding,
  margin,
  animationType = 'fadeIn',
  animationDelay = 0,
  animationDuration = 300,
  hapticFeedback = true,
  pressAnimation = true,
  shadowColor,
  backgroundColor,
}: AnimatedCardProps) {
  const { theme } = useTheme();
  
  // Animation values
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(50)).current;
  const scaleAnim = useRef(new Animated.Value(0.8)).current;
  const flipAnim = useRef(new Animated.Value(0)).current;
  const pressScaleAnim = useRef(new Animated.Value(1)).current;

  useEffect(() => {
    const startAnimation = () => {
      switch (animationType) {
        case 'fadeIn':
          Animated.timing(fadeAnim, {
            toValue: 1,
            duration: animationDuration,
            useNativeDriver: true,
          }).start();
          break;
        
        case 'slideUp':
          Animated.parallel([
            Animated.timing(fadeAnim, {
              toValue: 1,
              duration: animationDuration,
              useNativeDriver: true,
            }),
            Animated.timing(slideAnim, {
              toValue: 0,
              duration: animationDuration,
              useNativeDriver: true,
            }),
          ]).start();
          break;
        
        case 'slideLeft':
          Animated.parallel([
            Animated.timing(fadeAnim, {
              toValue: 1,
              duration: animationDuration,
              useNativeDriver: true,
            }),
            Animated.timing(slideAnim, {
              toValue: 0,
              duration: animationDuration,
              useNativeDriver: true,
            }),
          ]).start();
          break;
        
        case 'scale':
          Animated.parallel([
            Animated.timing(fadeAnim, {
              toValue: 1,
              duration: animationDuration,
              useNativeDriver: true,
            }),
            Animated.spring(scaleAnim, {
              toValue: 1,
              tension: 100,
              friction: 8,
              useNativeDriver: true,
            }),
          ]).start();
          break;
        
        case 'flip':
          Animated.sequence([
            Animated.timing(flipAnim, {
              toValue: 0.5,
              duration: animationDuration / 2,
              useNativeDriver: true,
            }),
            Animated.timing(fadeAnim, {
              toValue: 1,
              duration: 0,
              useNativeDriver: true,
            }),
            Animated.timing(flipAnim, {
              toValue: 1,
              duration: animationDuration / 2,
              useNativeDriver: true,
            }),
          ]).start();
          break;
      }
    };

    const timer = setTimeout(startAnimation, animationDelay);
    return () => clearTimeout(timer);
  }, [animationType, animationDelay, animationDuration]);

  const getCardStyle = (): ViewStyle => {
    const baseStyle: ViewStyle = {
      backgroundColor: backgroundColor || theme.colors.surface,
      borderRadius: borderRadius || theme.borderRadius.lg,
      padding: padding || theme.spacing.md,
      margin: margin || theme.spacing.xs,
    };

    // Add shadow/elevation
    if (elevation > 0) {
      baseStyle.shadowColor = shadowColor || theme.colors.text;
      baseStyle.shadowOffset = {
        width: 0,
        height: elevation,
      };
      baseStyle.shadowOpacity = 0.1;
      baseStyle.shadowRadius = elevation * 2;
      baseStyle.elevation = elevation;
    }

    return baseStyle;
  };

  const getAnimatedStyle = () => {
    const baseTransform = [];

    switch (animationType) {
      case 'fadeIn':
        return {
          opacity: fadeAnim,
        };
      
      case 'slideUp':
        return {
          opacity: fadeAnim,
          transform: [
            { translateY: slideAnim },
            { scale: pressScaleAnim },
          ],
        };
      
      case 'slideLeft':
        return {
          opacity: fadeAnim,
          transform: [
            { translateX: slideAnim },
            { scale: pressScaleAnim },
          ],
        };
      
      case 'scale':
        return {
          opacity: fadeAnim,
          transform: [
            { scale: Animated.multiply(scaleAnim, pressScaleAnim) },
          ],
        };
      
      case 'flip':
        const rotateY = flipAnim.interpolate({
          inputRange: [0, 0.5, 1],
          outputRange: ['0deg', '90deg', '0deg'],
        });
        
        return {
          opacity: fadeAnim,
          transform: [
            { rotateY },
            { scale: pressScaleAnim },
          ],
        };
      
      default:
        return {
          transform: [{ scale: pressScaleAnim }],
        };
    }
  };

  const handlePressIn = () => {
    if (!pressAnimation) return;
    
    if (hapticFeedback) {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }

    Animated.spring(pressScaleAnim, {
      toValue: 0.98,
      useNativeDriver: true,
    }).start();
  };

  const handlePressOut = () => {
    if (!pressAnimation) return;

    Animated.spring(pressScaleAnim, {
      toValue: 1,
      useNativeDriver: true,
    }).start();
  };

  const handlePress = () => {
    if (hapticFeedback) {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    }
    
    if (onPress) {
      onPress();
    }
  };

  const CardComponent = onPress ? TouchableOpacity : View;

  return (
    <Animated.View style={[getAnimatedStyle(), style]}>
      <CardComponent
        style={getCardStyle()}
        onPress={onPress ? handlePress : undefined}
        onPressIn={onPress ? handlePressIn : undefined}
        onPressOut={onPress ? handlePressOut : undefined}
        activeOpacity={onPress ? 0.9 : 1}
      >
        {children}
      </CardComponent>
    </Animated.View>
  );
}

// Preset card variants
export const FadeInCard = (props: Omit<AnimatedCardProps, 'animationType'>) => (
  <AnimatedCard {...props} animationType="fadeIn" />
);

export const SlideUpCard = (props: Omit<AnimatedCardProps, 'animationType'>) => (
  <AnimatedCard {...props} animationType="slideUp" />
);

export const SlideLeftCard = (props: Omit<AnimatedCardProps, 'animationType'>) => (
  <AnimatedCard {...props} animationType="slideLeft" />
);

export const ScaleCard = (props: Omit<AnimatedCardProps, 'animationType'>) => (
  <AnimatedCard {...props} animationType="scale" />
);

export const FlipCard = (props: Omit<AnimatedCardProps, 'animationType'>) => (
  <AnimatedCard {...props} animationType="flip" />
);

// Staggered animation helper
export const createStaggeredCards = (
  items: any[],
  renderCard: (item: any, index: number) => React.ReactNode,
  staggerDelay: number = 100
) => {
  return items.map((item, index) => (
    <AnimatedCard
      key={index}
      animationType="slideUp"
      animationDelay={index * staggerDelay}
    >
      {renderCard(item, index)}
    </AnimatedCard>
  ));
};
