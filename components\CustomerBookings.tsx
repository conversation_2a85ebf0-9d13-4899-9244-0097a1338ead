"use client"

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs'
import { 
  Calendar, 
  Clock, 
  Users, 
  MapPin, 
  Phone, 
  Mail,
  MessageCircle,
  Star,
  AlertCircle,
  CheckCircle,
  XCircle,
  Loader2,
  Eye,
  Filter
} from 'lucide-react'
import { useAuth } from '@/contexts/AuthContext'
import { format } from 'date-fns'

interface Booking {
  id: string
  entityName: string
  entityType: string
  eventDate: string
  eventTime: string
  guestCount: number
  eventType: string
  status: string
  priority: string
  budget: string
  specialRequests: string
  vendorNotes: string
  estimatedCost: string
  createdAt: string
  updatedAt: string
}

export default function CustomerBookings() {
  const { user } = useAuth()
  const [bookings, setBookings] = useState<Booking[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [selectedStatus, setSelectedStatus] = useState<string>('all')

  useEffect(() => {
    if (user) {
      loadBookings()
    }
  }, [user])

  const loadBookings = async () => {
    try {
      setLoading(true)
      setError(null)

      // Import the booking service
      const { BookingService } = await import('@/lib/services/bookingService')

      // Prepare filters
      const filters = {
        customerId: user?.userId || '',
        limit: 50,
        ...(selectedStatus !== 'all' && { status: selectedStatus })
      }

      const result = await BookingService.getBookings(filters)

      if (!result.success) {
        throw new Error(result.message || 'Failed to load bookings')
      }

      setBookings(result.bookings || [])
    } catch (err) {
      console.error('Error loading bookings:', err)
      setError(err.message)
    } finally {
      setLoading(false)
    }
  }

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'pending': return 'bg-yellow-100 text-yellow-800'
      case 'confirmed': return 'bg-blue-100 text-blue-800'
      case 'in_progress': return 'bg-purple-100 text-purple-800'
      case 'completed': return 'bg-green-100 text-green-800'
      case 'cancelled': return 'bg-red-100 text-red-800'
      case 'rejected': return 'bg-gray-100 text-gray-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status.toLowerCase()) {
      case 'pending': return <Clock className="h-4 w-4" />
      case 'confirmed': return <CheckCircle className="h-4 w-4" />
      case 'in_progress': return <Loader2 className="h-4 w-4 animate-spin" />
      case 'completed': return <CheckCircle className="h-4 w-4" />
      case 'cancelled': return <XCircle className="h-4 w-4" />
      case 'rejected': return <XCircle className="h-4 w-4" />
      default: return <AlertCircle className="h-4 w-4" />
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority?.toLowerCase()) {
      case 'high': return 'bg-red-100 text-red-800'
      case 'medium': return 'bg-yellow-100 text-yellow-800'
      case 'low': return 'bg-green-100 text-green-800'
      case 'urgent': return 'bg-red-200 text-red-900'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const filteredBookings = bookings.filter(booking => 
    selectedStatus === 'all' || booking.status.toLowerCase() === selectedStatus.toLowerCase()
  )

  const bookingCounts = {
    all: bookings.length,
    pending: bookings.filter(b => b.status.toLowerCase() === 'pending').length,
    confirmed: bookings.filter(b => b.status.toLowerCase() === 'confirmed').length,
    completed: bookings.filter(b => b.status.toLowerCase() === 'completed').length,
    cancelled: bookings.filter(b => b.status.toLowerCase() === 'cancelled').length
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-primary" />
          <p>Loading your bookings...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
        <p className="text-red-600 mb-4">{error}</p>
        <Button onClick={loadBookings}>Try Again</Button>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold">My Bookings</h2>
        <Button onClick={loadBookings} variant="outline" size="sm">
          <Loader2 className="h-4 w-4 mr-2" />
          Refresh
        </Button>
      </div>

      {/* Status Tabs */}
      <Tabs value={selectedStatus} onValueChange={setSelectedStatus}>
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="all">All ({bookingCounts.all})</TabsTrigger>
          <TabsTrigger value="pending">Pending ({bookingCounts.pending})</TabsTrigger>
          <TabsTrigger value="confirmed">Confirmed ({bookingCounts.confirmed})</TabsTrigger>
          <TabsTrigger value="completed">Completed ({bookingCounts.completed})</TabsTrigger>
          <TabsTrigger value="cancelled">Cancelled ({bookingCounts.cancelled})</TabsTrigger>
        </TabsList>

        <TabsContent value={selectedStatus} className="mt-6">
          {filteredBookings.length === 0 ? (
            <Card>
              <CardContent className="py-12 text-center">
                <Calendar className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  No bookings found
                </h3>
                <p className="text-gray-600 mb-4">
                  {selectedStatus === 'all' 
                    ? "You haven't made any bookings yet." 
                    : `No ${selectedStatus} bookings found.`
                  }
                </p>
                <Button onClick={() => window.location.href = '/vendors'}>
                  Browse Vendors
                </Button>
              </CardContent>
            </Card>
          ) : (
            <div className="grid gap-6">
              {filteredBookings.map((booking) => (
                <Card key={booking.id} className="hover:shadow-lg transition-shadow">
                  <CardHeader>
                    <div className="flex items-start justify-between">
                      <div>
                        <CardTitle className="text-xl">{booking.entityName}</CardTitle>
                        <p className="text-gray-600 capitalize">{booking.entityType.toLowerCase()}</p>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge className={getPriorityColor(booking.priority)}>
                          {booking.priority}
                        </Badge>
                        <Badge className={getStatusColor(booking.status)}>
                          {getStatusIcon(booking.status)}
                          <span className="ml-1 capitalize">{booking.status.replace('_', ' ')}</span>
                        </Badge>
                      </div>
                    </div>
                  </CardHeader>
                  
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-4">
                      <div className="flex items-center gap-2">
                        <Calendar className="h-4 w-4 text-gray-500" />
                        <span className="text-sm">
                          {format(new Date(booking.eventDate), 'PPP')}
                        </span>
                      </div>
                      
                      <div className="flex items-center gap-2">
                        <Clock className="h-4 w-4 text-gray-500" />
                        <span className="text-sm">{booking.eventTime}</span>
                      </div>
                      
                      <div className="flex items-center gap-2">
                        <Users className="h-4 w-4 text-gray-500" />
                        <span className="text-sm">{booking.guestCount} guests</span>
                      </div>
                      
                      <div className="flex items-center gap-2">
                        <Star className="h-4 w-4 text-gray-500" />
                        <span className="text-sm capitalize">{booking.eventType}</span>
                      </div>
                      
                      {booking.budget && (
                        <div className="flex items-center gap-2">
                          <span className="text-sm font-medium">Budget: {booking.budget}</span>
                        </div>
                      )}
                      
                      {booking.estimatedCost && (
                        <div className="flex items-center gap-2">
                          <span className="text-sm font-medium text-green-600">
                            Estimated: {booking.estimatedCost}
                          </span>
                        </div>
                      )}
                    </div>

                    {booking.specialRequests && (
                      <div className="mb-4">
                        <h4 className="text-sm font-medium text-gray-900 mb-1">Special Requests:</h4>
                        <p className="text-sm text-gray-600">{booking.specialRequests}</p>
                      </div>
                    )}

                    {booking.vendorNotes && (
                      <div className="mb-4 p-3 bg-blue-50 rounded-lg">
                        <h4 className="text-sm font-medium text-blue-900 mb-1">Vendor Notes:</h4>
                        <p className="text-sm text-blue-800">{booking.vendorNotes}</p>
                      </div>
                    )}

                    <div className="flex items-center justify-between pt-4 border-t">
                      <div className="text-xs text-gray-500">
                        Booked on {format(new Date(booking.createdAt), 'PPp')}
                      </div>
                      
                      <div className="flex items-center gap-2">
                        <Button variant="outline" size="sm">
                          <Eye className="h-4 w-4 mr-1" />
                          View Details
                        </Button>
                        
                        {booking.status.toLowerCase() === 'pending' && (
                          <Button variant="outline" size="sm">
                            <MessageCircle className="h-4 w-4 mr-1" />
                            Contact Vendor
                          </Button>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  )
}
