import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../../../providers/ThemeProvider';
import { useAuth } from '../../../providers/AuthProvider';
import { Header } from '../../../components/Header';
import { Card, Input, Button, Badge } from '../../../components/ui';
import type { StackNavigationProp } from '@react-navigation/stack';
import type { AppStackParamList } from '../../../navigation/AppNavigator';

type NavigationProp = StackNavigationProp<AppStackParamList>;

interface Order {
  id: string;
  orderNumber: string;
  customerName: string;
  customerEmail: string;
  vendorName: string;
  vendorId: string;
  items: {
    id: string;
    name: string;
    quantity: number;
    price: number;
    image?: string;
  }[];
  totalAmount: number;
  status: 'pending' | 'confirmed' | 'processing' | 'shipped' | 'delivered' | 'cancelled' | 'refunded';
  paymentStatus: 'pending' | 'paid' | 'failed' | 'refunded';
  paymentMethod: string;
  shippingAddress: {
    street: string;
    city: string;
    state: string;
    pincode: string;
  };
  createdAt: string;
  updatedAt: string;
  deliveryDate?: string;
  trackingNumber?: string;
  notes?: string;
}

export default function AdminOrderManagementScreen() {
  const { theme } = useTheme();
  const navigation = useNavigation<NavigationProp>();
  const { user } = useAuth();
  const [orders, setOrders] = useState<Order[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedStatus, setSelectedStatus] = useState('All');
  const [selectedPaymentStatus, setSelectedPaymentStatus] = useState('All');

  const statuses = ['All', 'pending', 'confirmed', 'processing', 'shipped', 'delivered', 'cancelled', 'refunded'];
  const paymentStatuses = ['All', 'pending', 'paid', 'failed', 'refunded'];

  useEffect(() => {
    loadOrders();
  }, []);

  const loadOrders = async () => {
    try {
      setLoading(true);
      // TODO: Replace with actual API call
      const mockOrders: Order[] = [
        {
          id: '1',
          orderNumber: 'ORD-2024-001',
          customerName: 'Priya Sharma',
          customerEmail: '<EMAIL>',
          vendorName: 'Elegant Decorations',
          vendorId: 'vendor-1',
          items: [
            {
              id: 'item-1',
              name: 'Wedding Decoration Package',
              quantity: 1,
              price: 125000,
              image: '/placeholder-image.jpg
            },
            {
              id: 'item-2',
              name: 'Floral Arrangements',
              quantity: 5,
              price: 15000,
            },
          ],
          totalAmount: 200000,
          status: 'confirmed',
          paymentStatus: 'paid',
          paymentMethod: 'Razorpay',
          shippingAddress: {
            street: '123 MG Road',
            city: 'Bangalore',
            state: 'Karnataka',
            pincode: '560001',
          },
          createdAt: '2024-01-25T10:30:00Z',
          updatedAt: '2024-01-25T14:15:00Z',
          deliveryDate: '2024-02-15T00:00:00Z',
          notes: 'Wedding on February 15th, 2024',
        },
        {
          id: '2',
          orderNumber: 'ORD-2024-002',
          customerName: 'Rahul Kumar',
          customerEmail: '<EMAIL>',
          vendorName: 'Golden Jewelry',
          vendorId: 'vendor-2',
          items: [
            {
              id: 'item-3',
              name: 'Bridal Gold Necklace Set',
              quantity: 1,
              price: 85000,
              image: '/placeholder-image.jpg
            },
          ],
          totalAmount: 85000,
          status: 'processing',
          paymentStatus: 'paid',
          paymentMethod: 'Credit Card',
          shippingAddress: {
            street: '456 Brigade Road',
            city: 'Bangalore',
            state: 'Karnataka',
            pincode: '560025',
          },
          createdAt: '2024-01-24T16:20:00Z',
          updatedAt: '2024-01-25T09:30:00Z',
          trackingNumber: 'TRK123456789',
        },
        {
          id: '3',
          orderNumber: 'ORD-2024-003',
          customerName: 'Anita Patel',
          customerEmail: '<EMAIL>',
          vendorName: 'Designer Lehengas',
          vendorId: 'vendor-3',
          items: [
            {
              id: 'item-4',
              name: 'Designer Bridal Lehenga',
              quantity: 1,
              price: 45000,
              image: '/placeholder-image.jpg
            },
          ],
          totalAmount: 45000,
          status: 'pending',
          paymentStatus: 'pending',
          paymentMethod: 'UPI',
          shippingAddress: {
            street: '789 Commercial Street',
            city: 'Mumbai',
            state: 'Maharashtra',
            pincode: '400001',
          },
          createdAt: '2024-01-25T18:45:00Z',
          updatedAt: '2024-01-25T18:45:00Z',
        },
      ];
      
      setOrders(mockOrders);
    } catch (error) {
      console.error('Error loading orders:', error);
      Alert.alert('Error', 'Failed to load orders');
    } finally {
      setLoading(false);
    }
  };

  const filteredOrders = orders.filter(order => {
    const matchesSearch = order.orderNumber.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         order.customerName.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         order.vendorName.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         order.customerEmail.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesStatus = selectedStatus === 'All' || order.status === selectedStatus;
    const matchesPaymentStatus = selectedPaymentStatus === 'All' || order.paymentStatus === selectedPaymentStatus;
    return matchesSearch && matchesStatus && matchesPaymentStatus;
  });

  const handleOrderPress = (order: Order) => {
    navigation.navigate('OrderDetail', { orderId: order.id });
  };

  const handleUpdateOrderStatus = (orderId: string, newStatus: Order['status']) => {
    Alert.alert(
      'Update Order Status',
      `Are you sure you want to change the order status to ${newStatus}?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Confirm',
          onPress: () => {
            setOrders(prevOrders =>
              prevOrders.map(order =>
                order.id === orderId
                  ? { ...order, status: newStatus, updatedAt: new Date().toISOString() }
                  : order
              )
            );
          },
        },
      ]
    );
  };

  const handleRefundOrder = (orderId: string) => {
    Alert.alert(
      'Refund Order',
      'Are you sure you want to process a refund for this order?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Refund',
          style: 'destructive',
          onPress: () => {
            setOrders(prevOrders =>
              prevOrders.map(order =>
                order.id === orderId
                  ? { 
                      ...order, 
                      status: 'refunded' as const, 
                      paymentStatus: 'refunded' as const,
                      updatedAt: new Date().toISOString() 
                    }
                  : order
              )
            );
          },
        },
      ]
    );
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-IN', {
      day: 'numeric',
      month: 'short',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const formatCurrency = (amount: number) => {
    return `₹${amount.toLocaleString('en-IN')}`;
  };

  const getStatusColor = (status: Order['status']) => {
    switch (status) {
      case 'pending': return theme.colors.warning;
      case 'confirmed': return theme.colors.info;
      case 'processing': return theme.colors.primary;
      case 'shipped': return theme.colors.secondary;
      case 'delivered': return theme.colors.success;
      case 'cancelled': return theme.colors.destructive;
      case 'refunded': return theme.colors.textSecondary;
      default: return theme.colors.textSecondary;
    }
  };

  const getPaymentStatusColor = (status: Order['paymentStatus']) => {
    switch (status) {
      case 'paid': return theme.colors.success;
      case 'pending': return theme.colors.warning;
      case 'failed': return theme.colors.destructive;
      case 'refunded': return theme.colors.textSecondary;
      default: return theme.colors.textSecondary;
    }
  };

  const renderFilterRow = () => (
    <View style={styles.filterContainer}>
      <View style={styles.filterSection}>
        <Text style={[styles.filterLabel, { color: theme.colors.text }]}>Order Status:</Text>
        <FlatList
          data={statuses}
          horizontal
          showsHorizontalScrollIndicator={false}
          keyExtractor={(item) => item}
          renderItem={({ item }) => (
            <TouchableOpacity
              style={[
                styles.filterButton,
                selectedStatus === item && styles.filterButtonActive,
                { borderColor: theme.colors.border }
              ]}
              onPress={() => setSelectedStatus(item)}
            >
              <Text
                style={[
                  styles.filterButtonText,
                  { color: selectedStatus === item ? theme.colors.primary : theme.colors.textSecondary }
                ]}
              >
                {item}
              </Text>
            </TouchableOpacity>
          )}
        />
      </View>
      
      <View style={styles.filterSection}>
        <Text style={[styles.filterLabel, { color: theme.colors.text }]}>Payment:</Text>
        <FlatList
          data={paymentStatuses}
          horizontal
          showsHorizontalScrollIndicator={false}
          keyExtractor={(item) => item}
          renderItem={({ item }) => (
            <TouchableOpacity
              style={[
                styles.filterButton,
                selectedPaymentStatus === item && styles.filterButtonActive,
                { borderColor: theme.colors.border }
              ]}
              onPress={() => setSelectedPaymentStatus(item)}
            >
              <Text
                style={[
                  styles.filterButtonText,
                  { color: selectedPaymentStatus === item ? theme.colors.primary : theme.colors.textSecondary }
                ]}
              >
                {item}
              </Text>
            </TouchableOpacity>
          )}
        />
      </View>
    </View>
  );

  const renderOrder = ({ item }: { item: Order }) => (
    <Card style={styles.orderCard}>
      <TouchableOpacity onPress={() => handleOrderPress(item)}>
        <View style={styles.orderHeader}>
          <View style={styles.orderTitleRow}>
            <Text style={[styles.orderNumber, { color: theme.colors.text }]}>
              {item.orderNumber}
            </Text>
            <View style={styles.badgeContainer}>
              <Badge 
                style={[styles.statusBadge, { backgroundColor: getStatusColor(item.status) }]}
              >
                <Text style={styles.badgeText}>{item.status}</Text>
              </Badge>
              <Badge 
                style={[styles.paymentBadge, { backgroundColor: getPaymentStatusColor(item.paymentStatus) }]}
              >
                <Text style={styles.badgeText}>{item.paymentStatus}</Text>
              </Badge>
            </View>
          </View>
          
          <View style={styles.orderInfo}>
            <Text style={[styles.customerName, { color: theme.colors.primary }]}>
              {item.customerName}
            </Text>
            <Text style={[styles.vendorName, { color: theme.colors.secondary }]}>
              Vendor: {item.vendorName}
            </Text>
          </View>
        </View>
        
        <View style={styles.orderDetails}>
          <View style={styles.orderMeta}>
            <View style={styles.metaItem}>
              <Ionicons name="cube" size={14} color={theme.colors.textSecondary} />
              <Text style={[styles.metaText, { color: theme.colors.textSecondary }]}>
                {item.items.length} item{item.items.length > 1 ? 's' : ''}
              </Text>
            </View>
            
            <View style={styles.metaItem}>
              <Ionicons name="wallet" size={14} color={theme.colors.textSecondary} />
              <Text style={[styles.metaText, { color: theme.colors.textSecondary }]}>
                {formatCurrency(item.totalAmount)}
              </Text>
            </View>
            
            <View style={styles.metaItem}>
              <Ionicons name="card" size={14} color={theme.colors.textSecondary} />
              <Text style={[styles.metaText, { color: theme.colors.textSecondary }]}>
                {item.paymentMethod}
              </Text>
            </View>
          </View>
          
          <View style={styles.addressInfo}>
            <Ionicons name="location" size={14} color={theme.colors.textSecondary} />
            <Text style={[styles.addressText, { color: theme.colors.textSecondary }]} numberOfLines={1}>
              {item.shippingAddress.city}, {item.shippingAddress.state} - {item.shippingAddress.pincode}
            </Text>
          </View>
          
          <View style={styles.dateInfo}>
            <Text style={[styles.dateText, { color: theme.colors.textSecondary }]}>
              Created: {formatDate(item.createdAt)}
            </Text>
            {item.deliveryDate && (
              <Text style={[styles.dateText, { color: theme.colors.textSecondary }]}>
                Delivery: {formatDate(item.deliveryDate)}
              </Text>
            )}
          </View>
          
          {item.trackingNumber && (
            <View style={styles.trackingInfo}>
              <Ionicons name="navigate" size={14} color={theme.colors.info} />
              <Text style={[styles.trackingText, { color: theme.colors.info }]}>
                Tracking: {item.trackingNumber}
              </Text>
            </View>
          )}
        </View>
      </TouchableOpacity>
      
      <View style={styles.orderActions}>
        <TouchableOpacity
          style={[styles.actionButton, { backgroundColor: theme.colors.info }]}
          onPress={() => handleOrderPress(item)}
        >
          <Ionicons name="eye" size={16} color="white" />
          <Text style={styles.actionButtonText}>View</Text>
        </TouchableOpacity>
        
        {item.status === 'pending' && (
          <TouchableOpacity
            style={[styles.actionButton, { backgroundColor: theme.colors.success }]}
            onPress={() => handleUpdateOrderStatus(item.id, 'confirmed')}
          >
            <Ionicons name="checkmark" size={16} color="white" />
            <Text style={styles.actionButtonText}>Confirm</Text>
          </TouchableOpacity>
        )}
        
        {(item.status === 'confirmed' || item.status === 'processing') && (
          <TouchableOpacity
            style={[styles.actionButton, { backgroundColor: theme.colors.primary }]}
            onPress={() => handleUpdateOrderStatus(item.id, 'shipped')}
          >
            <Ionicons name="airplane" size={16} color="white" />
            <Text style={styles.actionButtonText}>Ship</Text>
          </TouchableOpacity>
        )}
        
        {item.paymentStatus === 'paid' && item.status !== 'refunded' && (
          <TouchableOpacity
            style={[styles.actionButton, { backgroundColor: theme.colors.destructive }]}
            onPress={() => handleRefundOrder(item.id)}
          >
            <Ionicons name="return-down-back" size={16} color="white" />
            <Text style={styles.actionButtonText}>Refund</Text>
          </TouchableOpacity>
        )}
      </View>
    </Card>
  );

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <Header title="Order Management" showBack />
      
      {/* Search Bar */}
      <View style={styles.searchContainer}>
        <Input
          placeholder="Search orders..."
          value={searchQuery}
          onChangeText={setSearchQuery}
          leftIcon={<Ionicons name="search" size={20} color={theme.colors.textSecondary} />}
        />
      </View>

      {/* Filters */}
      {renderFilterRow()}

      {/* Orders List */}
      <FlatList
        data={filteredOrders}
        renderItem={renderOrder}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.ordersList}
        showsVerticalScrollIndicator={false}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  searchContainer: {
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  filterContainer: {
    paddingHorizontal: 16,
    marginBottom: 16,
    gap: 12,
  },
  filterSection: {
    gap: 8,
  },
  filterLabel: {
    fontSize: 14,
    fontWeight: '500',
  },
  filterButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    borderWidth: 1,
    marginRight: 8,
  },
  filterButtonActive: {
    backgroundColor: 'rgba(97, 15, 19, 0.1)',
  },
  filterButtonText: {
    fontSize: 12,
    fontWeight: '500',
  },
  ordersList: {
    paddingHorizontal: 16,
    paddingBottom: 20,
  },
  orderCard: {
    marginBottom: 16,
    padding: 16,
  },
  orderHeader: {
    marginBottom: 12,
  },
  orderTitleRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  orderNumber: {
    fontSize: 16,
    fontWeight: '600',
    flex: 1,
    marginRight: 8,
  },
  badgeContainer: {
    flexDirection: 'row',
    gap: 4,
  },
  statusBadge: {
    paddingHorizontal: 6,
    paddingVertical: 2,
  },
  paymentBadge: {
    paddingHorizontal: 6,
    paddingVertical: 2,
  },
  badgeText: {
    color: 'white',
    fontSize: 10,
    fontWeight: '500',
  },
  orderInfo: {
    gap: 2,
  },
  customerName: {
    fontSize: 14,
    fontWeight: '500',
  },
  vendorName: {
    fontSize: 12,
    fontWeight: '500',
  },
  orderDetails: {
    gap: 8,
    marginBottom: 12,
  },
  orderMeta: {
    flexDirection: 'row',
    gap: 16,
  },
  metaItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  metaText: {
    fontSize: 12,
  },
  addressInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  addressText: {
    fontSize: 12,
    flex: 1,
  },
  dateInfo: {
    gap: 2,
  },
  dateText: {
    fontSize: 11,
  },
  trackingInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  trackingText: {
    fontSize: 12,
    fontWeight: '500',
  },
  orderActions: {
    flexDirection: 'row',
    gap: 8,
  },
  actionButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 6,
    gap: 4,
  },
  actionButtonText: {
    color: 'white',
    fontSize: 12,
    fontWeight: '500',
  },
});
