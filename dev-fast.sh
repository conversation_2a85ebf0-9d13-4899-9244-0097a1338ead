#!/bin/bash

# Optimized Development Script for BookmyFestive
echo "🚀 Starting optimized development server..."

# Set Node.js options for better performance
export NODE_OPTIONS="--max-old-space-size=4096 --experimental-vm-modules"

# Set environment variables
export NODE_ENV=development
export NEXT_TELEMETRY_DISABLED=1

# Clean cache if requested
if [ "$1" = "--clean" ]; then
  echo "🧹 Cleaning cache..."
  rm -rf .next node_modules/.cache
fi

# Start development server with optimizations
echo "🔥 Starting Next.js with Turbo..."
next dev --turbo --port 3000
