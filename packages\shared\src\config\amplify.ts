/* eslint-disable */
// WARNING: DO NOT EDIT. This file is automatically generated by AWS Amplify. It will be overwritten.
// Shared AWS Amplify configuration for both web and mobile

export const amplifyConfig = {
  "aws_project_region": "ap-south-1",
  "aws_appsync_graphqlEndpoint": "https://5t2x2j2x4bhezesdxq5afnrtoq.appsync-api.ap-south-1.amazonaws.com/graphql",
  "aws_appsync_region": "ap-south-1",
  "aws_appsync_authenticationType": "API_KEY",
  "aws_appsync_apiKey": "da2-qpguodx4arbxna3avkjy3j6qm4",
  "aws_cognito_identity_pool_id": "ap-south-1:89d59375-ee6a-4a61-a10c-3f461ada3810",
  "aws_cognito_region": "ap-south-1",
  "aws_user_pools_id": "ap-south-1_0aauegFcl",
  "aws_user_pools_web_client_id": "2579oud11eb8qfvirij72m9eti",
  "oauth": {},
  "aws_cognito_username_attributes": [
    "EMAIL",
    "PHONE_NUMBER"
  ],
  "aws_cognito_social_providers": [],
  "aws_cognito_signup_attributes": [
    "EMAIL"
  ],
  "aws_cognito_mfa_configuration": "OFF",
  "aws_cognito_mfa_types": [
    "SMS"
  ],
  "aws_cognito_password_protection_settings": {
    "passwordPolicyMinLength": 8,
    "passwordPolicyCharacters": []
  },
  "aws_cognito_verification_mechanisms": [
    "EMAIL"
  ]
};

// Platform-specific configurations
export const webConfig = {
  ...amplifyConfig,
  // Web-specific overrides if needed
};

export const mobileConfig = {
  ...amplifyConfig,
  // Mobile-specific overrides if needed
  // For React Native, we might need different client IDs
};

export default amplifyConfig;
