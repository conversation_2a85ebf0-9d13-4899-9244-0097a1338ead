import React, { useState, useEffect } from 'react';
import { View, Text, ScrollView, StyleSheet, TouchableOpacity, Image, Linking, Alert, FlatList } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { StackNavigationProp } from '@react-navigation/stack';
import { RouteProp } from '@react-navigation/native';
import { AppStackParamList } from '../../navigation/AppNavigator';
import { useTheme } from '../../providers/ThemeProvider';
import { amplifyAuthService } from '../../services/amplifyService';
import { graphqlService } from '../../services/graphqlService';
import { Badge } from '../../components/ui/Badge';
import { Card, CardContent } from '../../components/ui/Card';
import { AvailabilityChecker } from '../../components/booking/AvailabilityChecker';

type VendorDetailScreenNavigationProp = StackNavigationProp<AppStackParamList, 'VendorDetail'>;
type VendorDetailScreenRouteProp = RouteProp<AppStackParamList, 'VendorDetail'>;

interface Props {
  navigation: VendorDetailScreenNavigationProp;
  route: VendorDetailScreenRouteProp;
}

interface Vendor {
  id: string;
  name: string;
  description: string;
  contact: string;
  email: string;
  address: string;
  city: string;
  state: string;
  website?: string;
  category: string;
  profilePhoto?: string;
  gallery?: string[];
  services?: Array<{
    name: string;
    price: number;
    description: string;
  }>;
  socialMedia?: {
    facebook?: string;
    instagram?: string;
    youtube?: string;
  };
  experience: number;
  events: number;
  rating: number;
  reviewCount: number;
  verified: boolean;
  featured: boolean;
  priceRange: string;
  specializations?: string[];
  languages?: string[];
  pincode?: string; // Added pincode to the interface
}

// Helper function for safe JSON parsing
const safeJSONParse = (jsonString: string, fallback: any) => {
  try {
    return JSON.parse(jsonString);
  } catch (error) {
    console.warn('JSON parse error:', error, 'Input:', jsonString);
    return fallback;
  }
};

// Helper for rendering images with fallback
interface RenderImageWithFallbackProps {
  uri?: string;
  style?: any;
}
const RenderImageWithFallback = (props: RenderImageWithFallbackProps) => {
  const { uri, style } = props;
  if (uri) {
    return <Image source={{ uri }} style={style} resizeMode="cover" />;
  }
  return (
    <View style={[style, { backgroundColor: '#E5E7EB', justifyContent: 'center', alignItems: 'center' }]}> 
      <Text style={{ color: '#9CA3AF', fontSize: 16 }}>No Image</Text>
    </View>
  );
};

export default function VendorDetailScreen({ navigation, route }: Props) {
  const { theme } = useTheme();
  const { vendorId } = route.params;
  const [vendor, setVendor] = useState<Vendor | null>(null);
  const [loading, setLoading] = useState(true);
  const [isFavorite, setIsFavorite] = useState(false);
  const [activeTab, setActiveTab] = useState<'Overview' | 'Services' | 'Gallery' | 'Reviews'>('Overview');

  // Availability checker state
  const [selectedDate, setSelectedDate] = useState('');
  const [selectedTime, setSelectedTime] = useState('');
  const [isAvailable, setIsAvailable] = useState<boolean | null>(null);

  useEffect(() => {
    loadVendorDetails();
  }, [vendorId]);

  const loadVendorDetails = async () => {
    try {
      setLoading(true);

      // Fetch vendor data from GraphQL API
      const vendorData = await graphqlService.getVendor(vendorId);

      if (vendorData) {
        // Transform GraphQL data to component interface
        const transformedVendor: Vendor = {
          id: vendorData.id,
          name: vendorData.name,
          description: vendorData.description || '',
          contact: vendorData.contact || '',
          email: vendorData.email || '',
          address: vendorData.address || '',
          city: vendorData.city || '',
          state: vendorData.state || '',
          website: vendorData.website,
          category: vendorData.category || '',
          profilePhoto: vendorData.profilePhoto,
          gallery: vendorData.gallery ? (
            typeof vendorData.gallery === 'string' && vendorData.gallery.startsWith('[')
              ? safeJSONParse(vendorData.gallery, [])
              : Array.isArray(vendorData.gallery)
                ? vendorData.gallery
                : [vendorData.gallery]
          ) : [],
          services: vendorData.services ? (
            typeof vendorData.services === 'string' && vendorData.services.startsWith('[')
              ? safeJSONParse(vendorData.services, [])
              : Array.isArray(vendorData.services)
                ? vendorData.services
                : [vendorData.services]
          ) : [],
          socialMedia: vendorData.socialMedia ? (
            typeof vendorData.socialMedia === 'string' && vendorData.socialMedia.startsWith('{')
              ? safeJSONParse(vendorData.socialMedia, {})
              : typeof vendorData.socialMedia === 'object'
                ? vendorData.socialMedia
                : {}
          ) : {},
          experience: vendorData.experience || 0,
          events: vendorData.events || 0,
          rating: vendorData.rating || 0,
          reviewCount: vendorData.reviewCount || 0,
          verified: vendorData.verified || false,
          featured: vendorData.featured || false,
          priceRange: vendorData.priceRange || '',
          specializations: vendorData.specializations ? (
            typeof vendorData.specializations === 'string' && vendorData.specializations.startsWith('[')
              ? safeJSONParse(vendorData.specializations, [])
              : Array.isArray(vendorData.specializations)
                ? vendorData.specializations
                : [vendorData.specializations]
          ) : [],
          languages: vendorData.languages ? (
            typeof vendorData.languages === 'string' && vendorData.languages.startsWith('[')
              ? safeJSONParse(vendorData.languages, [])
              : Array.isArray(vendorData.languages)
                ? vendorData.languages
                : [vendorData.languages]
          ) : [],
          pincode: vendorData.pincode, // Add pincode to transformed vendor
        };

        setVendor(transformedVendor);
      } else {
        Alert.alert('Error', 'Vendor not found');
      }
    } catch (error) {
      console.error('Error loading vendor details:', error);
      Alert.alert('Error', 'Failed to load vendor details. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleCall = () => {
    if (vendor?.contact) {
      Linking.openURL(`tel:${vendor.contact}`);
    }
  };

  const handleEmail = () => {
    if (vendor?.email) {
      Linking.openURL(`mailto:${vendor.email}`);
    }
  };

  const handleWebsite = () => {
    if (vendor?.website) {
      Linking.openURL(vendor.website);
    }
  };

  const handleBooking = () => {
    if (vendor) {
      navigation.navigate('Booking', {
        type: 'vendor',
        id: vendor.id,
        name: vendor.name,
        vendorId: vendor.id,
        serviceType: vendor.category,
      });
    }
  };

  const handleWriteReview = () => {
    if (vendor) {
      navigation.navigate('Review', {
        vendorId: vendor.id,
        serviceName: vendor.name,
      });
    }
  };

  const handleSocialMedia = (url?: string) => {
    if (url) {
      Linking.openURL(url);
    }
  };

  const handleAvailabilityChange = (available: boolean, conflicts?: any[]) => {
    setIsAvailable(available);
    console.log('Availability changed:', { available, conflicts });
  };

  const toggleFavorite = () => {
    setIsFavorite(!isFavorite);
    // TODO: Implement favorite functionality with backend
  };

  const handleInquiry = () => {
    // TODO: Navigate to inquiry form or implement inquiry functionality
    Alert.alert('Inquiry', 'Inquiry functionality will be implemented soon');
  };

  if (loading) {
    return (
      <View style={[styles.container, styles.centered, { backgroundColor: theme.colors.background }]}>
        <Text style={[styles.loadingText, { color: theme.colors.text }]}>Loading...</Text>
      </View>
    );
  }

  if (!vendor) {
    return (
      <View style={[styles.container, styles.centered, { backgroundColor: theme.colors.background }]}>
        <Text style={[styles.errorText, { color: theme.colors.error }]}>Vendor not found</Text>
      </View>
    );
  }

  return (
    <View style={{ flex: 1, backgroundColor: theme.colors.background }}>
      {/* Hero Section - Matching Web App */}
      <View style={styles.heroContainer}>
        <RenderImageWithFallback uri={vendor.profilePhoto} style={styles.heroImage} />
        <View style={styles.heroOverlay}>
          {/* Vendor Info Overlay - Matching Web App */}
          <View style={styles.heroContent}>
            <View style={styles.heroHeader}>
              <View style={styles.heroTitleSection}>
                <Text style={[styles.heroName, { color: theme.colors.primaryForeground }]}>
                  {vendor.name}
                </Text>
                {vendor.featured && (
                  <Badge variant="secondary" style={styles.featuredBadge}>
                    Featured
                  </Badge>
                )}
              </View>
              <Text style={[styles.heroCategory, { color: theme.colors.primaryForeground }]}>
                {vendor.category}
              </Text>
              <View style={styles.heroLocationRow}>
                <Ionicons name="location-outline" size={20} color={theme.colors.primaryForeground} />
                <Text style={[styles.heroLocation, { color: theme.colors.primaryForeground }]}>
                  {vendor.city}, {vendor.state}
                </Text>
              </View>
            </View>
            <View style={styles.heroPriceSection}>
              <Text style={[styles.heroPrice, { color: theme.colors.primaryForeground }]}>
                {vendor.priceRange || "Contact for pricing"}
              </Text>
              <View style={styles.heroRatingRow}>
                <Ionicons name="star" size={16} color="#FFD700" />
                <Text style={[styles.heroRating, { color: theme.colors.primaryForeground }]}>
                  {vendor.rating}
                </Text>
                <Text style={[styles.heroReviewCount, { color: theme.colors.primaryForeground }]}>
                  ({vendor.reviewCount || 0} reviews)
                </Text>
              </View>
            </View>
          </View>
        </View>

        {/* Floating Action Buttons - Matching Web App */}
        <View style={styles.floatingActions}>
          <TouchableOpacity style={styles.actionButton} onPress={toggleFavorite}>
            <Ionicons name={isFavorite ? 'heart' : 'heart-outline'} size={24} color={isFavorite ? '#EF4444' : theme.colors.foreground} />
          </TouchableOpacity>
          <TouchableOpacity style={styles.actionButton} onPress={() => {/* Share functionality */}}>
            <Ionicons name="share-outline" size={24} color={theme.colors.foreground} />
          </TouchableOpacity>
        </View>
      </View>

      {/* Tab Bar - always visible */}
      <View style={{ flexDirection: 'row', justifyContent: 'space-around', marginVertical: 12 }}>
        {['Overview', 'Services', 'Gallery', 'Reviews'].map(tab => (
          <TouchableOpacity
            key={tab}
            onPress={() => setActiveTab(tab as any)}
            style={{
              borderBottomWidth: activeTab === tab ? 2 : 0,
              borderBottomColor: activeTab === tab ? theme.colors.primary : 'transparent',
              paddingVertical: 8,
              flex: 1,
              alignItems: 'center',
            }}
          >
            <Text style={{ color: activeTab === tab ? theme.colors.primary : theme.colors.textSecondary, fontWeight: 'bold' }}>{tab}</Text>
          </TouchableOpacity>
        ))}
      </View>

      {/* Tab Content - scrollable */}
      <ScrollView style={{ flex: 1 }}>
        {activeTab === 'Overview' && (
          <View>
            {/* About Section - Moved to Top like Web App */}
            <Card style={[styles.section, { marginBottom: 24 }]}>
              <CardContent style={styles.cardContent}>
                <View style={styles.aboutHeader}>
                  <View style={styles.aboutTitleSection}>
                    <Text style={[styles.sectionTitle, { color: theme.colors.foreground }]}>
                      About {vendor.name}
                    </Text>
                    <View style={styles.aboutActions}>
                      <TouchableOpacity onPress={toggleFavorite} style={styles.actionIconButton}>
                        <Ionicons
                          name={isFavorite ? 'heart' : 'heart-outline'}
                          size={24}
                          color={isFavorite ? '#EF4444' : theme.colors.foreground}
                        />
                      </TouchableOpacity>
                      <TouchableOpacity onPress={() => {/* Share functionality */}} style={styles.actionIconButton}>
                        <Ionicons name="share-outline" size={24} color={theme.colors.foreground} />
                      </TouchableOpacity>
                    </View>
                  </View>
                </View>

                <Text style={[styles.description, { color: theme.colors.mutedForeground }]}>
                  {vendor.description}
                </Text>

                {/* Stats Section - Matching Web App */}
                <View style={styles.statsContainer}>
                  <View style={styles.statItem}>
                    <Ionicons name="star" size={20} color="#FFD700" />
                    <Text style={[styles.statValue, { color: theme.colors.foreground }]}>
                      {vendor.rating}
                    </Text>
                    <Text style={[styles.statLabel, { color: theme.colors.mutedForeground }]}>
                      Rating
                    </Text>
                  </View>
                  <View style={styles.statItem}>
                    <Ionicons name="people" size={20} color={theme.colors.primary} />
                    <Text style={[styles.statValue, { color: theme.colors.foreground }]}>
                      {vendor.reviewCount || 0}
                    </Text>
                    <Text style={[styles.statLabel, { color: theme.colors.mutedForeground }]}>
                      Reviews
                    </Text>
                  </View>
                  <View style={styles.statItem}>
                    <Ionicons name="calendar" size={20} color={theme.colors.secondary} />
                    <Text style={[styles.statValue, { color: theme.colors.foreground }]}>
                      {vendor.experience || 0}+
                    </Text>
                    <Text style={[styles.statLabel, { color: theme.colors.mutedForeground }]}>
                      Years
                    </Text>
                  </View>
                  <View style={styles.statItem}>
                    <Ionicons name="trophy" size={20} color={theme.colors.accent} />
                    <Text style={[styles.statValue, { color: theme.colors.foreground }]}>
                      {vendor.events || 0}+
                    </Text>
                    <Text style={[styles.statLabel, { color: theme.colors.mutedForeground }]}>
                      Events
                    </Text>
                  </View>
                </View>

                {/* Follow Us Section - Matching Web App */}
                {(vendor.socialMedia?.facebook || vendor.socialMedia?.instagram || vendor.socialMedia?.youtube) && (
                  <View style={styles.socialSection}>
                    <Text style={[styles.socialTitle, { color: theme.colors.foreground }]}>
                      Follow Us
                    </Text>
                    <View style={styles.socialButtons}>
                      {vendor.socialMedia?.facebook && (
                        <TouchableOpacity
                          onPress={() => handleSocialMedia(vendor.socialMedia?.facebook)}
                          style={[styles.socialButton, { backgroundColor: '#1877F2' }]}
                        >
                          <Ionicons name="logo-facebook" size={20} color="white" />
                        </TouchableOpacity>
                      )}
                      {vendor.socialMedia?.instagram && (
                        <TouchableOpacity
                          onPress={() => handleSocialMedia(vendor.socialMedia?.instagram)}
                          style={[styles.socialButton, { backgroundColor: '#E4405F' }]}
                        >
                          <Ionicons name="logo-instagram" size={20} color="white" />
                        </TouchableOpacity>
                      )}
                      {vendor.socialMedia?.youtube && (
                        <TouchableOpacity
                          onPress={() => handleSocialMedia(vendor.socialMedia?.youtube)}
                          style={[styles.socialButton, { backgroundColor: '#FF0000' }]}
                        >
                          <Ionicons name="logo-youtube" size={20} color="white" />
                        </TouchableOpacity>
                      )}
                    </View>
                  </View>
                )}
              </CardContent>
            </Card>

            {/* Quick Stats */}
            <View style={{ flexDirection: 'row', flexWrap: 'wrap', justifyContent: 'space-between', marginHorizontal: 16, marginBottom: 16 }}>
              <View style={{ width: '48%', backgroundColor: '#FEF9C3', borderRadius: 12, padding: 16, marginBottom: 12, alignItems: 'center' }}>
                <Ionicons name="star" size={28} color="#F59E0B" />
                <Text style={{ fontWeight: 'bold', fontSize: 18, color: theme.colors.text }}>{vendor.rating}</Text>
                <Text style={{ color: theme.colors.textSecondary }}>Rating</Text>
              </View>
              <View style={{ width: '48%', backgroundColor: '#DBEAFE', borderRadius: 12, padding: 16, marginBottom: 12, alignItems: 'center' }}>
                <Ionicons name="ribbon-outline" size={28} color="#2563EB" />
                <Text style={{ fontWeight: 'bold', fontSize: 18, color: theme.colors.text }}>{vendor.experience}+</Text>
                <Text style={{ color: theme.colors.textSecondary }}>Years Experience</Text>
              </View>
              <View style={{ width: '48%', backgroundColor: '#DCFCE7', borderRadius: 12, padding: 16, marginBottom: 12, alignItems: 'center' }}>
                <Ionicons name="people-outline" size={28} color="#22C55E" />
                <Text style={{ fontWeight: 'bold', fontSize: 18, color: theme.colors.text }}>{vendor.events}+</Text>
                <Text style={{ color: theme.colors.textSecondary }}>Events</Text>
              </View>
              <View style={{ width: '48%', backgroundColor: '#E0E7FF', borderRadius: 12, padding: 16, marginBottom: 12, alignItems: 'center' }}>
                <Ionicons name="time-outline" size={28} color="#6366F1" />
                <Text style={{ fontWeight: 'bold', fontSize: 18, color: theme.colors.text }}>Within 4 hours</Text>
                <Text style={{ color: theme.colors.textSecondary }}>Response</Text>
              </View>
            </View>

            {/* Specializations */}
            {vendor.specializations && vendor.specializations.length > 0 && (
              <View style={[styles.section, { backgroundColor: theme.colors.surface }]}> 
                <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>Specializations</Text>
                <View style={{ flexDirection: 'row', flexWrap: 'wrap', gap: 8 }}>
                  {vendor.specializations.map((spec, idx) => (
                    <View key={idx} style={{ backgroundColor: '#D1FAE5', borderRadius: 16, paddingHorizontal: 12, paddingVertical: 6, marginRight: 8, marginBottom: 8 }}>
                      <Text style={{ color: '#047857', fontWeight: '600' }}>{spec}</Text>
                    </View>
                  ))}
                </View>
              </View>
            )}

            {/* Languages Spoken */}
            {vendor.languages && vendor.languages.length > 0 && (
              <View style={[styles.section, { backgroundColor: theme.colors.surface }]}> 
                <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>Languages Spoken</Text>
                <View style={{ flexDirection: 'row', flexWrap: 'wrap', gap: 8 }}>
                  {vendor.languages.map((lang, idx) => (
                    <View key={idx} style={{ backgroundColor: '#F3F4F6', borderRadius: 16, paddingHorizontal: 12, paddingVertical: 6, marginRight: 8, marginBottom: 8 }}>
                      <Text style={{ color: theme.colors.primary, fontWeight: '600' }}>{lang}</Text>
                    </View>
                  ))}
                </View>
              </View>
            )}
          </View>
        )}
        {activeTab === 'Services' && (
          <View>
            <View style={[styles.section, { backgroundColor: theme.colors.surface }]}> 
              <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>Services & Pricing</Text>
              {vendor.services && vendor.services.length > 0 ? (
                vendor.services.map((service, index) => (
                  <View key={index} style={styles.serviceItem}>
                    <View style={styles.serviceInfo}>
                      <Text style={[styles.serviceName, { color: theme.colors.text }]}>{service.name}</Text>
                      {service.description ? (
                        <Text style={[styles.serviceDescription, { color: theme.colors.textSecondary }]}>{service.description}</Text>
                      ) : null}
                    </View>
                    <Text style={[styles.servicePrice, { color: theme.colors.primary }]}>₹{service.price.toLocaleString()}</Text>
                  </View>
                ))
              ) : (
                <Text style={{ color: theme.colors.textSecondary, marginTop: 12 }}>No services listed. Contact vendor for details.</Text>
              )}
            </View>
          </View>
        )}
        {activeTab === 'Gallery' && (
          <View>
            <View style={[styles.section, { backgroundColor: theme.colors.surface }]}> 
              <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>Photo Gallery</Text>
              {vendor.gallery && vendor.gallery.length > 0 ? (
                <FlatList
                  data={vendor.gallery}
                  keyExtractor={(item, idx) => idx.toString()}
                  horizontal
                  showsHorizontalScrollIndicator={false}
                  style={{ marginTop: 12 }}
                  contentContainerStyle={{ gap: 12 }}
                  renderItem={({ item }) => (
                    <RenderImageWithFallback uri={item} style={{ width: 220, height: 180, borderRadius: 12, backgroundColor: '#E5E7EB' }} />
                  )}
                />
              ) : (
                <View style={{ alignItems: 'center', padding: 32 }}>
                  <Ionicons name="images-outline" size={48} color={theme.colors.textSecondary} />
                  <Text style={{ color: theme.colors.textSecondary, marginTop: 12 }}>No photos available</Text>
                </View>
              )}
            </View>
          </View>
        )}
        {activeTab === 'Reviews' && (
          <View>
            <View style={[styles.section, { backgroundColor: theme.colors.surface }]}> 
              <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>Reviews</Text>
              {/* Placeholder for reviews list */}
              <View style={{ marginBottom: 24 }}>
                <Text style={{ color: theme.colors.textSecondary, marginBottom: 8 }}>
                  {/* TODO: Replace with actual reviews list */}
                  No reviews yet. Be the first to write a review!
                </Text>
              </View>
              <TouchableOpacity
                style={{ backgroundColor: theme.colors.primary, borderRadius: 8, paddingVertical: 12, alignItems: 'center' }}
                onPress={handleWriteReview}
              >
                <Text style={{ color: 'white', fontWeight: 'bold', fontSize: 16 }}>Write a Review</Text>
              </TouchableOpacity>
            </View>
          </View>
        )}

        {/* Quick Inquiry Form */}
        <View style={[styles.section, { backgroundColor: theme.colors.surface, marginTop: 16 }]}> 
          <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>Quick Inquiry</Text>
          {/* Placeholder for user info - replace with actual user context if available */}
          <View style={{ marginBottom: 8 }}>
            <Text style={{ color: theme.colors.textSecondary, fontSize: 13 }}>
              Welcome back, User! Your contact details have been pre-filled below.
            </Text>
          </View>
          <View style={{ gap: 10 }}>
            <View style={{ flexDirection: 'row', gap: 8 }}>
              <View style={{ flex: 1 }}>
                <Text style={{ color: theme.colors.textSecondary, fontSize: 13 }}>Your Name *</Text>
                <View style={{ backgroundColor: '#F3F4F6', borderRadius: 8, padding: 8 }}>
                  <Text style={{ color: theme.colors.text }}>John Doe</Text>
                </View>
              </View>
              <View style={{ flex: 1 }}>
                <Text style={{ color: theme.colors.textSecondary, fontSize: 13 }}>Email *</Text>
                <View style={{ backgroundColor: '#F3F4F6', borderRadius: 8, padding: 8 }}>
                  <Text style={{ color: theme.colors.text }}><EMAIL></Text>
                </View>
              </View>
            </View>
            <View>
              <Text style={{ color: theme.colors.textSecondary, fontSize: 13 }}>Your Phone</Text>
              <View style={{ backgroundColor: '#F3F4F6', borderRadius: 8, padding: 8 }}>
                <Text style={{ color: theme.colors.text }}>+91 9876543210</Text>
              </View>
            </View>
            <View style={{ flexDirection: 'row', gap: 8 }}>
              <View style={{ flex: 1 }}>
                <Text style={{ color: theme.colors.textSecondary, fontSize: 13 }}>Date</Text>
                <View style={{ backgroundColor: '#F3F4F6', borderRadius: 8, padding: 8 }}>
                  <Text style={{ color: theme.colors.text }}>dd/mm/yyyy</Text>
                </View>
              </View>
              <View style={{ flex: 1, flexDirection: 'row', gap: 8 }}>
                <View style={{ flex: 1 }}>
                  <Text style={{ color: theme.colors.textSecondary, fontSize: 13 }}>Guest Count</Text>
                  <View style={{ backgroundColor: '#F3F4F6', borderRadius: 8, padding: 8 }}>
                    <Text style={{ color: theme.colors.text }}>100</Text>
                  </View>
                </View>
                <View style={{ flex: 1 }}>
                  <Text style={{ color: theme.colors.textSecondary, fontSize: 13 }}>Budget</Text>
                  <View style={{ backgroundColor: '#F3F4F6', borderRadius: 8, padding: 8 }}>
                    <Text style={{ color: theme.colors.text }}>₹2,00,000</Text>
                  </View>
                </View>
              </View>
            </View>
            <View>
              <Text style={{ color: theme.colors.textSecondary, fontSize: 13 }}>Venue/Location</Text>
              <View style={{ backgroundColor: '#F3F4F6', borderRadius: 8, padding: 8 }}>
                <Text style={{ color: theme.colors.text }}>Delhi</Text>
              </View>
            </View>
            <View>
              <Text style={{ color: theme.colors.textSecondary, fontSize: 13 }}>Inquiry Type</Text>
              <View style={{ backgroundColor: '#F3F4F6', borderRadius: 8, padding: 8 }}>
                <Text style={{ color: theme.colors.text }}>General Inquiry</Text>
              </View>
            </View>
            <View>
              <Text style={{ color: theme.colors.textSecondary, fontSize: 13 }}>Preferred Contact Time</Text>
              <View style={{ backgroundColor: '#F3F4F6', borderRadius: 8, padding: 8 }}>
                <Text style={{ color: theme.colors.text }}>Anytime</Text>
              </View>
            </View>
            <View>
              <Text style={{ color: theme.colors.textSecondary, fontSize: 13 }}>Your Message *</Text>
              <View style={{ backgroundColor: '#F3F4F6', borderRadius: 8, padding: 8, minHeight: 60 }}>
                <Text style={{ color: theme.colors.text }}>I am interested in your services. Please contact me.</Text>
              </View>
            </View>
            <TouchableOpacity style={{ backgroundColor: theme.colors.primary, borderRadius: 8, paddingVertical: 14, alignItems: 'center', marginTop: 8 }} onPress={() => Alert.alert('Inquiry', 'Inquiry submitted!')}>
              <Text style={{ color: 'white', fontWeight: 'bold', fontSize: 16 }}>Send Inquiry</Text>
            </TouchableOpacity>
            <View style={{ backgroundColor: '#F0F9FF', borderRadius: 8, padding: 12, marginTop: 12 }}>
              <Text style={{ color: '#2563EB', fontWeight: 'bold' }}>Quick Response Guaranteed</Text>
              <Text style={{ color: theme.colors.textSecondary, marginTop: 2, fontSize: 13 }}>The vendor typically responds within 2-4 hours during business hours.</Text>
            </View>
          </View>
        </View>

        {/* Availability Checker */}
        <Card style={[styles.section, { marginBottom: 24 }]}>
          <CardContent style={styles.cardContent}>
            <Text style={[styles.sectionTitle, { color: theme.colors.foreground }]}>
              Check Availability
            </Text>
            <Text style={[styles.sectionSubtitle, { color: theme.colors.mutedForeground, marginBottom: 16 }]}>
              Select your preferred date and time to check if {vendor?.name} is available
            </Text>

            <AvailabilityChecker
              entityId={vendor?.id || ''}
              entityType="VENDOR"
              entityName={vendor?.name || ''}
              selectedDate={selectedDate}
              selectedTime={selectedTime}
              duration="4 hours"
              onAvailabilityChange={handleAvailabilityChange}
            />
          </CardContent>
        </Card>

        {/* Contact & Quick Actions */}
        <View style={[styles.section, { backgroundColor: theme.colors.surface, marginTop: 16 }]}>
          <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>Contact Vendor</Text>
          <View style={{ flexDirection: 'row', flexWrap: 'wrap', gap: 8, marginBottom: 12 }}>
            <TouchableOpacity style={{ flex: 1, backgroundColor: '#22C55E', borderRadius: 8, paddingVertical: 12, alignItems: 'center', marginBottom: 8 }} onPress={handleBooking}>
              <Ionicons name="calendar-outline" size={20} color="white" />
              <Text style={{ color: 'white', fontWeight: 'bold', fontSize: 16 }}>Book Now</Text>
            </TouchableOpacity>
            <TouchableOpacity style={{ flex: 1, backgroundColor: '#EF4444', borderRadius: 8, paddingVertical: 12, alignItems: 'center', marginBottom: 8 }} onPress={handleCall}>
              <Ionicons name="call-outline" size={20} color="white" />
              <Text style={{ color: 'white', fontWeight: 'bold', fontSize: 16 }}>Call Now</Text>
            </TouchableOpacity>
          </View>
          <TouchableOpacity style={{ backgroundColor: 'white', borderWidth: 1, borderColor: theme.colors.primary, borderRadius: 8, paddingVertical: 12, alignItems: 'center', marginBottom: 8 }} onPress={handleEmail}>
            <Ionicons name="mail-outline" size={20} color={theme.colors.primary} />
            <Text style={{ color: theme.colors.primary, fontWeight: 'bold', fontSize: 16 }}>Send Message</Text>
          </TouchableOpacity>
          {vendor.website && (
            <TouchableOpacity style={{ backgroundColor: 'white', borderWidth: 1, borderColor: theme.colors.primary, borderRadius: 8, paddingVertical: 12, alignItems: 'center', marginBottom: 8 }} onPress={handleWebsite}>
              <Ionicons name="globe-outline" size={20} color={theme.colors.primary} />
              <Text style={{ color: theme.colors.primary, fontWeight: 'bold', fontSize: 16 }}>Visit Website</Text>
            </TouchableOpacity>
          )}
          <View style={{ flexDirection: 'row', gap: 8, marginBottom: 8 }}>
            <TouchableOpacity style={{ flex: 1, backgroundColor: '#F3F4F6', borderRadius: 8, paddingVertical: 12, alignItems: 'center' }} onPress={toggleFavorite}>
              <Ionicons name={isFavorite ? 'heart' : 'heart-outline'} size={20} color={isFavorite ? '#EF4444' : theme.colors.primary} />
              <Text style={{ color: theme.colors.primary, fontWeight: 'bold', fontSize: 16 }}>Save to Favorites</Text>
            </TouchableOpacity>
            <TouchableOpacity style={{ flex: 1, backgroundColor: '#F3F4F6', borderRadius: 8, paddingVertical: 12, alignItems: 'center' }} onPress={() => { /* TODO: Share logic */ }}>
              <Ionicons name="share-social-outline" size={20} color={theme.colors.primary} />
              <Text style={{ color: theme.colors.primary, fontWeight: 'bold', fontSize: 16 }}>Share Vendor</Text>
            </TouchableOpacity>
          </View>
          {/* Contact Info */}
          <View style={{ marginTop: 12 }}>
            <Text style={{ color: theme.colors.textSecondary, fontWeight: 'bold', fontSize: 15, marginBottom: 4 }}>Contact Info</Text>
            <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 4 }}>
              <Ionicons name="call-outline" size={16} color={theme.colors.textSecondary} />
              <Text style={{ color: theme.colors.text, marginLeft: 6 }}>{vendor.contact}</Text>
            </View>
            <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 4 }}>
              <Ionicons name="mail-outline" size={16} color={theme.colors.textSecondary} />
              <Text style={{ color: theme.colors.text, marginLeft: 6 }}>{vendor.email}</Text>
            </View>
            {vendor.website && (
              <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 4 }}>
                <Ionicons name="globe-outline" size={16} color={theme.colors.textSecondary} />
                <Text style={{ color: theme.colors.text, marginLeft: 6 }}>{vendor.website}</Text>
              </View>
            )}
          </View>
        </View>

        {/* Location/Map View */}
        <View style={[styles.section, { backgroundColor: theme.colors.surface, marginTop: 16 }]}> 
          <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>Location</Text>
          <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 8 }}>
            <Ionicons name="location-outline" size={18} color={theme.colors.primary} />
            <Text style={{ color: theme.colors.text, marginLeft: 6 }}>
              {vendor.address ? `${vendor.address}, ` : ''}{vendor.city}, {vendor.state}{vendor.pincode ? ` ${vendor.pincode}` : ''}
            </Text>
          </View>
          <View style={{ backgroundColor: '#F3F4F6', borderRadius: 12, height: 120, alignItems: 'center', justifyContent: 'center', marginTop: 8 }}>
            <Text style={{ color: theme.colors.textSecondary, fontSize: 16 }}>Map View</Text>
          </View>
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  centered: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
  },
  errorText: {
    fontSize: 16,
  },
  // Hero Section Styles - Matching Web App
  heroContainer: {
    position: 'relative',
    height: 384, // h-96 equivalent (24rem)
    overflow: 'hidden',
  },
  heroImage: {
    width: '100%',
    height: '100%',
    position: 'absolute',
    top: 0,
    left: 0,
  },
  heroOverlay: {
    position: 'absolute',
    left: 0,
    right: 0,
    bottom: 0,
    padding: 32, // p-8 equivalent
    backgroundColor: 'rgba(0,0,0,0.5)', // Darker overlay for better text readability
  },
  heroContent: {
    flex: 1,
    justifyContent: 'flex-end',
  },
  heroHeader: {
    marginBottom: 16,
  },
  heroTitleSection: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
    marginBottom: 8,
  },
  heroName: {
    fontSize: 32, // text-4xl equivalent
    fontWeight: 'bold',
    flex: 1,
  },
  featuredBadge: {
    marginLeft: 8,
  },
  heroCategory: {
    fontSize: 20, // text-xl equivalent
    marginBottom: 8,
    opacity: 0.9,
  },
  heroLocationRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  heroLocation: {
    fontSize: 18, // text-lg equivalent
    marginLeft: 8,
    opacity: 0.8,
  },
  heroPriceSection: {
    alignItems: 'flex-end',
  },
  heroPrice: {
    fontSize: 24, // text-3xl equivalent
    fontWeight: 'bold',
    marginBottom: 4,
  },
  heroRatingRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  heroRating: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  heroReviewCount: {
    fontSize: 16,
    opacity: 0.8,
  },
  // Floating Action Buttons - Matching Web App
  floatingActions: {
    position: 'absolute',
    top: 16,
    right: 16,
    flexDirection: 'row',
    gap: 8,
  },
  actionButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(255,255,255,0.9)',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  infoContainer: {
    padding: 16,
    marginTop: -20,
    marginHorizontal: 16,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  titleRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    gap: 8,
  },
  vendorName: {
    fontSize: 24,
    fontWeight: 'bold',
    flex: 1,
  },
  featuredBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  featuredText: {
    fontSize: 12,
    fontWeight: '600',
    color: 'white',
  },
  category: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
    marginBottom: 16,
  },
  rating: {
    fontSize: 14,
    fontWeight: '500',
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginTop: 24,
    paddingTop: 24,
    borderTopWidth: 1,
    borderTopColor: '#E2E8F0',
  },
  statItem: {
    alignItems: 'center',
    gap: 4,
  },
  statValue: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  statLabel: {
    fontSize: 12,
    textAlign: 'center',
  },
  priceRange: {
    fontSize: 18,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  section: {
    margin: 16,
  },
  // About Section Styles - Matching Web App
  cardContent: {
    padding: 24, // p-6 equivalent
  },
  aboutHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 16,
  },
  aboutTitleSection: {
    flex: 1,
  },
  aboutActions: {
    flexDirection: 'row',
    gap: 8,
  },
  actionIconButton: {
    padding: 8,
    borderRadius: 8,
    backgroundColor: 'rgba(0,0,0,0.05)',
  },
  // Social Section Styles - Matching Web App
  socialSection: {
    marginTop: 24,
    paddingTop: 24,
    borderTopWidth: 1,
    borderTopColor: '#E2E8F0',
  },
  socialTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 12,
  },
  socialButtons: {
    flexDirection: 'row',
    gap: 12,
  },
  socialButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    justifyContent: 'center',
    alignItems: 'center',
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 12,
  },
  description: {
    fontSize: 14,
    lineHeight: 20,
  },
  serviceItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  serviceInfo: {
    flex: 1,
    marginRight: 12,
  },
  serviceName: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  serviceDescription: {
    fontSize: 14,
  },
  servicePrice: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  contactItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
    paddingVertical: 8,
  },
  contactText: {
    fontSize: 14,
    flex: 1,
  },
  contactLink: {
    textDecorationLine: 'underline',
  },
  actionButtons: {
    flexDirection: 'row',
    gap: 12,
  },
  actionButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    gap: 8,
  },
  primaryButton: {
    // backgroundColor set dynamically
  },
  secondaryButton: {
    borderWidth: 1,
    backgroundColor: 'transparent',
  },
  primaryButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  secondaryButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
  socialContainer: {
    flexDirection: 'row',
    gap: 12,
  },
  socialButton: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
  },
  actionContainer: {
    padding: 16,
    paddingBottom: 32,
  },
  inquiryButton: {
    flex: 1,
  },
  actionButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
});
