import React, { useRef, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Animated,
  Dimensions,
  ActivityIndicator,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../../providers/ThemeProvider';

const { width: screenWidth } = Dimensions.get('window');

interface EnhancedLoadingProps {
  type?: 'spinner' | 'dots' | 'pulse' | 'wave' | 'skeleton' | 'progress';
  size?: 'small' | 'medium' | 'large';
  color?: string;
  text?: string;
  fullScreen?: boolean;
  overlay?: boolean;
  progress?: number; // 0-100 for progress type
  style?: any;
}

export default function EnhancedLoading({
  type = 'spinner',
  size = 'medium',
  color,
  text,
  fullScreen = false,
  overlay = false,
  progress = 0,
  style,
}: EnhancedLoadingProps) {
  const { theme } = useTheme();
  
  // Animation values
  const spinAnim = useRef(new Animated.Value(0)).current;
  const pulseAnim = useRef(new Animated.Value(1)).current;
  const waveAnim = useRef(new Animated.Value(0)).current;
  const dotsAnim = useRef([
    new Animated.Value(0),
    new Animated.Value(0),
    new Animated.Value(0),
  ]).current;
  const skeletonAnim = useRef(new Animated.Value(0)).current;
  const progressAnim = useRef(new Animated.Value(0)).current;

  const loadingColor = color || theme.colors.primary;

  useEffect(() => {
    startAnimations();
    return () => {
      // Cleanup animations
    };
  }, [type]);

  useEffect(() => {
    if (type === 'progress') {
      Animated.timing(progressAnim, {
        toValue: progress / 100,
        duration: 300,
        useNativeDriver: false,
      }).start();
    }
  }, [progress, type]);

  const startAnimations = () => {
    switch (type) {
      case 'spinner':
        startSpinAnimation();
        break;
      case 'dots':
        startDotsAnimation();
        break;
      case 'pulse':
        startPulseAnimation();
        break;
      case 'wave':
        startWaveAnimation();
        break;
      case 'skeleton':
        startSkeletonAnimation();
        break;
    }
  };

  const startSpinAnimation = () => {
    Animated.loop(
      Animated.timing(spinAnim, {
        toValue: 1,
        duration: 1000,
        useNativeDriver: true,
      })
    ).start();
  };

  const startDotsAnimation = () => {
    const animations = dotsAnim.map((anim, index) =>
      Animated.loop(
        Animated.sequence([
          Animated.delay(index * 200),
          Animated.timing(anim, {
            toValue: 1,
            duration: 600,
            useNativeDriver: true,
          }),
          Animated.timing(anim, {
            toValue: 0,
            duration: 600,
            useNativeDriver: true,
          }),
        ])
      )
    );

    Animated.parallel(animations).start();
  };

  const startPulseAnimation = () => {
    Animated.loop(
      Animated.sequence([
        Animated.timing(pulseAnim, {
          toValue: 1.2,
          duration: 800,
          useNativeDriver: true,
        }),
        Animated.timing(pulseAnim, {
          toValue: 1,
          duration: 800,
          useNativeDriver: true,
        }),
      ])
    ).start();
  };

  const startWaveAnimation = () => {
    Animated.loop(
      Animated.timing(waveAnim, {
        toValue: 1,
        duration: 1500,
        useNativeDriver: true,
      })
    ).start();
  };

  const startSkeletonAnimation = () => {
    Animated.loop(
      Animated.sequence([
        Animated.timing(skeletonAnim, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: true,
        }),
        Animated.timing(skeletonAnim, {
          toValue: 0,
          duration: 1000,
          useNativeDriver: true,
        }),
      ])
    ).start();
  };

  const getSizeValues = () => {
    switch (size) {
      case 'small':
        return { containerSize: 40, dotSize: 6, iconSize: 20 };
      case 'large':
        return { containerSize: 80, dotSize: 12, iconSize: 40 };
      default: // medium
        return { containerSize: 60, dotSize: 8, iconSize: 30 };
    }
  };

  const renderSpinner = () => {
    const { iconSize } = getSizeValues();
    const spin = spinAnim.interpolate({
      inputRange: [0, 1],
      outputRange: ['0deg', '360deg'],
    });

    return (
      <Animated.View style={{ transform: [{ rotate: spin }] }}>
        <ActivityIndicator size={size === 'small' ? 'small' : 'large'} color={loadingColor} />
      </Animated.View>
    );
  };

  const renderDots = () => {
    const { dotSize } = getSizeValues();
    
    return (
      <View style={styles.dotsContainer}>
        {dotsAnim.map((anim, index) => {
          const scale = anim.interpolate({
            inputRange: [0, 1],
            outputRange: [0.5, 1.5],
          });

          return (
            <Animated.View
              key={index}
              style={[
                styles.dot,
                {
                  width: dotSize,
                  height: dotSize,
                  backgroundColor: loadingColor,
                  transform: [{ scale }],
                },
              ]}
            />
          );
        })}
      </View>
    );
  };

  const renderPulse = () => {
    const { containerSize } = getSizeValues();
    
    return (
      <Animated.View
        style={[
          styles.pulseContainer,
          {
            width: containerSize,
            height: containerSize,
            borderColor: loadingColor,
            transform: [{ scale: pulseAnim }],
          },
        ]}
      >
        <View
          style={[
            styles.pulseInner,
            {
              backgroundColor: loadingColor,
            },
          ]}
        />
      </Animated.View>
    );
  };

  const renderWave = () => {
    const { containerSize } = getSizeValues();
    const bars = [0.2, 0.4, 0.6, 0.8, 1.0, 0.8, 0.6, 0.4, 0.2];
    
    return (
      <View style={styles.waveContainer}>
        {bars.map((height, index) => {
          const animatedHeight = waveAnim.interpolate({
            inputRange: [0, 1],
            outputRange: [containerSize * 0.2, containerSize * height],
          });

          return (
            <Animated.View
              key={index}
              style={[
                styles.waveBar,
                {
                  height: animatedHeight,
                  backgroundColor: loadingColor,
                },
              ]}
            />
          );
        })}
      </View>
    );
  };

  const renderSkeleton = () => {
    const opacity = skeletonAnim.interpolate({
      inputRange: [0, 1],
      outputRange: [0.3, 0.7],
    });

    return (
      <View style={styles.skeletonContainer}>
        <Animated.View
          style={[
            styles.skeletonLine,
            styles.skeletonTitle,
            { opacity, backgroundColor: loadingColor },
          ]}
        />
        <Animated.View
          style={[
            styles.skeletonLine,
            styles.skeletonSubtitle,
            { opacity, backgroundColor: loadingColor },
          ]}
        />
        <Animated.View
          style={[
            styles.skeletonLine,
            styles.skeletonText,
            { opacity, backgroundColor: loadingColor },
          ]}
        />
      </View>
    );
  };

  const renderProgress = () => {
    const progressWidth = progressAnim.interpolate({
      inputRange: [0, 1],
      outputRange: [0, screenWidth * 0.8],
    });

    return (
      <View style={styles.progressContainer}>
        <View style={[styles.progressTrack, { backgroundColor: theme.colors.border }]}>
          <Animated.View
            style={[
              styles.progressFill,
              {
                width: progressWidth,
                backgroundColor: loadingColor,
              },
            ]}
          />
        </View>
        <Text style={[styles.progressText, { color: theme.colors.text }]}>
          {Math.round(progress)}%
        </Text>
      </View>
    );
  };

  const renderLoadingContent = () => {
    switch (type) {
      case 'dots':
        return renderDots();
      case 'pulse':
        return renderPulse();
      case 'wave':
        return renderWave();
      case 'skeleton':
        return renderSkeleton();
      case 'progress':
        return renderProgress();
      default:
        return renderSpinner();
    }
  };

  const containerStyle = [
    fullScreen ? styles.fullScreenContainer : styles.container,
    overlay && styles.overlay,
    { backgroundColor: overlay ? 'rgba(0,0,0,0.5)' : 'transparent' },
    style,
  ];

  return (
    <View style={containerStyle}>
      <View style={styles.content}>
        {renderLoadingContent()}
        {text && type !== 'skeleton' && (
          <Text style={[styles.text, { color: theme.colors.text }]}>
            {text}
          </Text>
        )}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20,
  },
  fullScreenContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20,
  },
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 1000,
  },
  content: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  text: {
    marginTop: 16,
    fontSize: 16,
    textAlign: 'center',
  },
  dotsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  dot: {
    borderRadius: 50,
    marginHorizontal: 4,
  },
  pulseContainer: {
    borderRadius: 50,
    borderWidth: 2,
    alignItems: 'center',
    justifyContent: 'center',
  },
  pulseInner: {
    width: '60%',
    height: '60%',
    borderRadius: 50,
  },
  waveContainer: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    justifyContent: 'center',
    height: 60,
  },
  waveBar: {
    width: 4,
    marginHorizontal: 1,
    borderRadius: 2,
  },
  skeletonContainer: {
    width: screenWidth * 0.8,
  },
  skeletonLine: {
    borderRadius: 4,
    marginBottom: 8,
  },
  skeletonTitle: {
    height: 20,
    width: '70%',
  },
  skeletonSubtitle: {
    height: 16,
    width: '50%',
  },
  skeletonText: {
    height: 14,
    width: '90%',
  },
  progressContainer: {
    width: screenWidth * 0.8,
    alignItems: 'center',
  },
  progressTrack: {
    width: '100%',
    height: 8,
    borderRadius: 4,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    borderRadius: 4,
  },
  progressText: {
    marginTop: 8,
    fontSize: 14,
    fontWeight: '600',
  },
});
