{"folders": [{"path": ".github"}, {"path": ".next"}, {"path": "amplify"}, {"path": "app"}, {"path": "apps"}, {"path": "blog-content-admin"}, {"path": "components"}, {"path": "contexts"}, {"path": "docs"}, {"path": "hooks"}, {"path": "lib"}, {"path": "node_modules"}, {"path": "packages"}, {"path": "playwright-report"}, {"path": "public"}, {"path": "scripts"}, {"path": "src"}, {"path": "styles"}, {"path": "test-results"}, {"path": "tests"}, {"path": "utils"}]}