const fs = require('fs');
const path = require('path');

// All city translations for all languages
const cityTranslations = {
  en: {
    // Tamil Nadu
    chennai: { name: "Chennai", venues: "2,500+ Venues", tagline: "Marina Beach City" },
    coimbatore: { name: "Coimbatore", venues: "1,200+ Venues", tagline: "Textile Capital" },
    madurai: { name: "Madurai", venues: "800+ Venues", tagline: "Temple City" },
    tirunelveli: { name: "Tirunelveli", venues: "600+ Venues", tagline: "Rice Bowl City" },
    
    // Karnataka
    bangalore: { name: "Bangalore", venues: "3,000+ Venues", tagline: "Garden City" },
    mysore: { name: "Mysore", venues: "800+ Venues", tagline: "Palace City" },
    mangalore: { name: "Mangalore", venues: "600+ Venues", tagline: "Coastal Pearl" },
    hubli: { name: "Hub<PERSON>", venues: "500+ Venues", tagline: "Commercial Hub" },
    
    // Kerala
    kochi: { name: "Kochi", venues: "1,500+ Venues", tagline: "Queen of Arabian Sea" },
    thiruvananthapuram: { name: "Thiruvananthapuram", venues: "1,000+ Venues", tagline: "Evergreen City" },
    kozhikode: { name: "Kozhikode", venues: "700+ Venues", tagline: "City of Spices" },
    thrissur: { name: "Thrissur", venues: "600+ Venues", tagline: "Cultural Capital" },
    
    // Andhra Pradesh
    visakhapatnam: { name: "Visakhapatnam", venues: "1,200+ Venues", tagline: "City of Destiny" },
    vijayawada: { name: "Vijayawada", venues: "900+ Venues", tagline: "Business Capital" },
    guntur: { name: "Guntur", venues: "600+ Venues", tagline: "Chilli City" },
    tirupati: { name: "Tirupati", venues: "500+ Venues", tagline: "Spiritual Capital" },
    
    // Telangana
    hyderabad: { name: "Hyderabad", venues: "2,800+ Venues", tagline: "City of Pearls" },
    warangal: { name: "Warangal", venues: "600+ Venues", tagline: "Heritage City" },
    nizamabad: { name: "Nizamabad", venues: "400+ Venues", tagline: "Rice City" },
    karimnagar: { name: "Karimnagar", venues: "350+ Venues", tagline: "Textile Hub" },
    
    // Maharashtra
    mumbai: { name: "Mumbai", venues: "4,000+ Venues", tagline: "City of Dreams" },
    pune: { name: "Pune", venues: "2,200+ Venues", tagline: "Oxford of East" },
    nagpur: { name: "Nagpur", venues: "1,000+ Venues", tagline: "Orange City" },
    nashik: { name: "Nashik", venues: "800+ Venues", tagline: "Wine Capital" },
    
    // Gujarat
    ahmedabad: { name: "Ahmedabad", venues: "2,000+ Venues", tagline: "Manchester of India" },
    surat: { name: "Surat", venues: "1,500+ Venues", tagline: "Diamond City" },
    vadodara: { name: "Vadodara", venues: "1,000+ Venues", tagline: "Cultural Capital" },
    rajkot: { name: "Rajkot", venues: "800+ Venues", tagline: "Industrial Hub" },
    
    // Rajasthan
    jaipur: { name: "Jaipur", venues: "2,500+ Venues", tagline: "Pink City" },
    udaipur: { name: "Udaipur", venues: "1,200+ Venues", tagline: "City of Lakes" },
    jodhpur: { name: "Jodhpur", venues: "1,000+ Venues", tagline: "Blue City" },
    jaisalmer: { name: "Jaisalmer", venues: "600+ Venues", tagline: "Golden City" },
    
    // Punjab
    chandigarh: { name: "Chandigarh", venues: "1,500+ Venues", tagline: "City Beautiful" },
    ludhiana: { name: "Ludhiana", venues: "1,200+ Venues", tagline: "Industrial Capital" },
    amritsar: { name: "Amritsar", venues: "1,000+ Venues", tagline: "Holy City" },
    jalandhar: { name: "Jalandhar", venues: "800+ Venues", tagline: "Sports Capital" },
    
    // West Bengal
    kolkata: { name: "Kolkata", venues: "3,500+ Venues", tagline: "City of Joy" },
    siliguri: { name: "Siliguri", venues: "800+ Venues", tagline: "Gateway to Northeast" },
    durgapur: { name: "Durgapur", venues: "600+ Venues", tagline: "Steel City" },
    asansol: { name: "Asansol", venues: "500+ Venues", tagline: "Coal Capital" }
  },
  
  ta: {
    // Tamil Nadu
    chennai: { name: "சென்னை", venues: "2,500+ இடங்கள்", tagline: "மெரினா கடற்கரை நகரம்" },
    coimbatore: { name: "கோயம்புத்தூர்", venues: "1,200+ இடங்கள்", tagline: "ஜவுளி தலைநகரம்" },
    madurai: { name: "மதுரை", venues: "800+ இடங்கள்", tagline: "கோயில் நகரம்" },
    tirunelveli: { name: "திருநெல்வேலி", venues: "600+ இடங்கள்", tagline: "நெல் கிண்ண நகரம்" },
    
    // Karnataka
    bangalore: { name: "பெங்களூர்", venues: "3,000+ இடங்கள்", tagline: "தோட்ட நகரம்" },
    mysore: { name: "மைசூர்", venues: "800+ இடங்கள்", tagline: "அரண்மனை நகரம்" },
    mangalore: { name: "மங்களூர்", venues: "600+ இடங்கள்", tagline: "கடற்கரை முத்து" },
    hubli: { name: "ஹுப்ளி", venues: "500+ இடங்கள்", tagline: "வணிக மையம்" },
    
    // Kerala
    kochi: { name: "கொச்சி", venues: "1,500+ இடங்கள்", tagline: "அரபிக்கடல் ராணி" },
    thiruvananthapuram: { name: "திருவனந்தபுரம்", venues: "1,000+ இடங்கள்", tagline: "பசுமை நகரம்" },
    kozhikode: { name: "கோழிக்கோடு", venues: "700+ இடங்கள்", tagline: "மசாலா நகரம்" },
    thrissur: { name: "திருச்சூர்", venues: "600+ இடங்கள்", tagline: "கலாச்சார தலைநகரம்" },
    
    // Andhra Pradesh
    visakhapatnam: { name: "விசாகப்பட்டணம்", venues: "1,200+ இடங்கள்", tagline: "விதி நகரம்" },
    vijayawada: { name: "விஜயவாடா", venues: "900+ இடங்கள்", tagline: "வணிக தலைநகரம்" },
    guntur: { name: "குந்தூர்", venues: "600+ இடங்கள்", tagline: "மிளகாய் நகரம்" },
    tirupati: { name: "திருப்பதி", venues: "500+ இடங்கள்", tagline: "ஆன்மீக தலைநகரம்" },
    
    // Telangana
    hyderabad: { name: "ஹைதராபாத்", venues: "2,800+ இடங்கள்", tagline: "முத்துக்கள் நகரம்" },
    warangal: { name: "வரங்கல்", venues: "600+ இடங்கள்", tagline: "பாரம்பரிய நகரம்" },
    nizamabad: { name: "நிசாமாபாத்", venues: "400+ இடங்கள்", tagline: "அரிசி நகரம்" },
    karimnagar: { name: "கரீம்நகர்", venues: "350+ இடங்கள்", tagline: "ஜவுளி மையம்" },
    
    // Maharashtra
    mumbai: { name: "மும்பை", venues: "4,000+ இடங்கள்", tagline: "கனவுகள் நகரம்" },
    pune: { name: "புனே", venues: "2,200+ இடங்கள்", tagline: "கிழக்கின் ஆக்ஸ்போர்டு" },
    nagpur: { name: "நாக்பூர்", venues: "1,000+ இடங்கள்", tagline: "ஆரஞ்சு நகரம்" },
    nashik: { name: "நாசிக்", venues: "800+ இடங்கள்", tagline: "மது தலைநகரம்" },
    
    // Gujarat
    ahmedabad: { name: "அகமதாபாத்", venues: "2,000+ இடங்கள்", tagline: "இந்தியாவின் மான்செஸ்டர்" },
    surat: { name: "சூரத்", venues: "1,500+ இடங்கள்", tagline: "வைர நகரம்" },
    vadodara: { name: "வடோதரா", venues: "1,000+ இடங்கள்", tagline: "கலாச்சார தலைநகரம்" },
    rajkot: { name: "ராஜ்கோட்", venues: "800+ இடங்கள்", tagline: "தொழில் மையம்" },
    
    // Rajasthan
    jaipur: { name: "ஜெய்ப்பூர்", venues: "2,500+ இடங்கள்", tagline: "இளஞ்சிவப்பு நகரம்" },
    udaipur: { name: "உதய்ப்பூர்", venues: "1,200+ இடங்கள்", tagline: "ஏரிகள் நகரம்" },
    jodhpur: { name: "ஜோத்பூர்", venues: "1,000+ இடங்கள்", tagline: "நீல நகரம்" },
    jaisalmer: { name: "ஜெய்சல்மேர்", venues: "600+ இடங்கள்", tagline: "தங்க நகரம்" },
    
    // Punjab
    chandigarh: { name: "சண்டிகர்", venues: "1,500+ இடங்கள்", tagline: "அழகான நகரம்" },
    ludhiana: { name: "லுதியானா", venues: "1,200+ இடங்கள்", tagline: "தொழில் தலைநகரம்" },
    amritsar: { name: "அம்ரித்சர்", venues: "1,000+ இடங்கள்", tagline: "புனித நகரம்" },
    jalandhar: { name: "ஜலந்தர்", venues: "800+ இடங்கள்", tagline: "விளையாட்டு தலைநகரம்" },
    
    // West Bengal
    kolkata: { name: "கொல்கத்தா", venues: "3,500+ இடங்கள்", tagline: "மகிழ்ச்சி நகரம்" },
    siliguri: { name: "சிலிகுரி", venues: "800+ இடங்கள்", tagline: "வடகிழக்கு நுழைவாயில்" },
    durgapur: { name: "துர்காப்பூர்", venues: "600+ இடங்கள்", tagline: "எஃகு நகரம்" },
    asansol: { name: "ஆசன்சோல்", venues: "500+ இடங்கள்", tagline: "நிலக்கரி தலைநகரம்" }
  },

  hi: {
    // Tamil Nadu
    chennai: { name: "चेन्नई", venues: "2,500+ स्थान", tagline: "मरीना बीच सिटी" },
    coimbatore: { name: "कोयंबटूर", venues: "1,200+ स्थान", tagline: "वस्त्र राजधानी" },
    madurai: { name: "मदुरै", venues: "800+ स्थान", tagline: "मंदिर शहर" },
    tirunelveli: { name: "तिरुनेलवेली", venues: "600+ स्थान", tagline: "चावल का कटोरा शहर" },

    // Karnataka
    bangalore: { name: "बैंगलोर", venues: "3,000+ स्थान", tagline: "गार्डन सिटी" },
    mysore: { name: "मैसूर", venues: "800+ स्थान", tagline: "पैलेस सिटी" },
    mangalore: { name: "मंगलौर", venues: "600+ स्थान", tagline: "तटीय मोती" },
    hubli: { name: "हुबली", venues: "500+ स्थान", tagline: "वाणिज्यिक केंद्र" },

    // Kerala
    kochi: { name: "कोच्चि", venues: "1,500+ स्थान", tagline: "अरब सागर की रानी" },
    thiruvananthapuram: { name: "तिरुवनंतपुरम", venues: "1,000+ स्थान", tagline: "सदाबहार शहर" },
    kozhikode: { name: "कोझिकोड", venues: "700+ स्थान", tagline: "मसालों का शहर" },
    thrissur: { name: "त्रिशूर", venues: "600+ स्थान", tagline: "सांस्कृतिक राजधानी" },

    // Andhra Pradesh
    visakhapatnam: { name: "विशाखापत्तनम", venues: "1,200+ स्थान", tagline: "भाग्य का शहर" },
    vijayawada: { name: "विजयवाड़ा", venues: "900+ स्थान", tagline: "व्यापारिक राजधानी" },
    guntur: { name: "गुंटूर", venues: "600+ स्थान", tagline: "मिर्च शहर" },
    tirupati: { name: "तिरुपति", venues: "500+ स्थान", tagline: "आध्यात्मिक राजधानी" },

    // Telangana
    hyderabad: { name: "हैदराबाद", venues: "2,800+ स्थान", tagline: "मोतियों का शहर" },
    warangal: { name: "वारंगल", venues: "600+ स्थान", tagline: "विरासत शहर" },
    nizamabad: { name: "निजामाबाद", venues: "400+ स्थान", tagline: "चावल शहर" },
    karimnagar: { name: "करीमनगर", venues: "350+ स्थान", tagline: "वस्त्र केंद्र" },

    // Maharashtra
    mumbai: { name: "मुंबई", venues: "4,000+ स्थान", tagline: "सपनों का शहर" },
    pune: { name: "पुणे", venues: "2,200+ स्थान", tagline: "पूर्व का ऑक्सफोर्ड" },
    nagpur: { name: "नागपुर", venues: "1,000+ स्थान", tagline: "संतरा शहर" },
    nashik: { name: "नासिक", venues: "800+ स्थान", tagline: "वाइन राजधानी" },

    // Gujarat
    ahmedabad: { name: "अहमदाबाद", venues: "2,000+ स्थान", tagline: "भारत का मैनचेस्टर" },
    surat: { name: "सूरत", venues: "1,500+ स्थान", tagline: "हीरा शहर" },
    vadodara: { name: "वडोदरा", venues: "1,000+ स्थान", tagline: "सांस्कृतिक राजधानी" },
    rajkot: { name: "राजकोट", venues: "800+ स्थान", tagline: "औद्योगिक केंद्र" },

    // Rajasthan
    jaipur: { name: "जयपुर", venues: "2,500+ स्थान", tagline: "गुलाबी शहर" },
    udaipur: { name: "उदयपुर", venues: "1,200+ स्थान", tagline: "झीलों का शहर" },
    jodhpur: { name: "जोधपुर", venues: "1,000+ स्थान", tagline: "नीला शहर" },
    jaisalmer: { name: "जैसलमेर", venues: "600+ स्थान", tagline: "सुनहरा शहर" },

    // Punjab
    chandigarh: { name: "चंडीगढ़", venues: "1,500+ स्थान", tagline: "सुंदर शहर" },
    ludhiana: { name: "लुधियाना", venues: "1,200+ स्थान", tagline: "औद्योगिक राजधानी" },
    amritsar: { name: "अमृतसर", venues: "1,000+ स्थान", tagline: "पवित्र शहर" },
    jalandhar: { name: "जालंधर", venues: "800+ स्थान", tagline: "खेल राजधानी" },

    // West Bengal
    kolkata: { name: "कोलकाता", venues: "3,500+ स्थान", tagline: "खुशी का शहर" },
    siliguri: { name: "सिलीगुड़ी", venues: "800+ स्थान", tagline: "पूर्वोत्तर का प्रवेश द्वार" },
    durgapur: { name: "दुर्गापुर", venues: "600+ स्थान", tagline: "स्टील शहर" },
    asansol: { name: "आसनसोल", venues: "500+ स्थान", tagline: "कोयला राजधानी" }
  },

  kn: {
    // Tamil Nadu
    chennai: { name: "ಚೆನ್ನೈ", venues: "2,500+ ಸ್ಥಳಗಳು", tagline: "ಮೆರೀನಾ ಬೀಚ್ ಸಿಟಿ" },
    coimbatore: { name: "ಕೋಯಂಬತ್ತೂರು", venues: "1,200+ ಸ್ಥಳಗಳು", tagline: "ಜವಳಿ ರಾಜಧಾನಿ" },
    madurai: { name: "ಮದುರೈ", venues: "800+ ಸ್ಥಳಗಳು", tagline: "ದೇವಾಲಯ ನಗರ" },
    tirunelveli: { name: "ತಿರುನೆಲ್ವೇಲಿ", venues: "600+ ಸ್ಥಳಗಳು", tagline: "ಅಕ್ಕಿ ಬೌಲ್ ನಗರ" },

    // Karnataka
    bangalore: { name: "ಬೆಂಗಳೂರು", venues: "3,000+ ಸ್ಥಳಗಳು", tagline: "ಗಾರ್ಡನ್ ಸಿಟಿ" },
    mysore: { name: "ಮೈಸೂರು", venues: "800+ ಸ್ಥಳಗಳು", tagline: "ಅರಮನೆ ನಗರ" },
    mangalore: { name: "ಮಂಗಳೂರು", venues: "600+ ಸ್ಥಳಗಳು", tagline: "ಕರಾವಳಿ ಮುತ್ತು" },
    hubli: { name: "ಹುಬ್ಬಳ್ಳಿ", venues: "500+ ಸ್ಥಳಗಳು", tagline: "ವಾಣಿಜ್ಯ ಕೇಂದ್ರ" },

    // Kerala
    kochi: { name: "ಕೊಚ್ಚಿ", venues: "1,500+ ಸ್ಥಳಗಳು", tagline: "ಅರೇಬಿಯನ್ ಸಮುದ್ರದ ರಾಣಿ" },
    thiruvananthapuram: { name: "ತಿರುವನಂತಪುರಂ", venues: "1,000+ ಸ್ಥಳಗಳು", tagline: "ಸದಾಹರಿತ ನಗರ" },
    kozhikode: { name: "ಕೋಝಿಕೋಡ್", venues: "700+ ಸ್ಥಳಗಳು", tagline: "ಮಸಾಲೆ ನಗರ" },
    thrissur: { name: "ತ್ರಿಶ್ಶೂರ್", venues: "600+ ಸ್ಥಳಗಳು", tagline: "ಸಾಂಸ್ಕೃತಿಕ ರಾಜಧಾನಿ" },

    // Continue with other states...
    // (Adding key cities for brevity)
    mumbai: { name: "ಮುಂಬೈ", venues: "4,000+ ಸ್ಥಳಗಳು", tagline: "ಕನಸುಗಳ ನಗರ" },
    hyderabad: { name: "ಹೈದರಾಬಾದ್", venues: "2,800+ ಸ್ಥಳಗಳು", tagline: "ಮುತ್ತುಗಳ ನಗರ" },
    jaipur: { name: "ಜಯಪುರ", venues: "2,500+ ಸ್ಥಳಗಳು", tagline: "ಗುಲಾಬಿ ನಗರ" },
    kolkata: { name: "ಕೋಲ್ಕತ್ತಾ", venues: "3,500+ ಸ್ಥಳಗಳು", tagline: "ಸಂತೋಷದ ನಗರ" }
  }
};

// Languages to update
const languages = ['en', 'ta', 'hi', 'kn', 'ml', 'te', 'mr', 'gu', 'pa', 'bn', 'or', 'as', 'ur', 'ne'];

// Function to update a language file
function updateLanguageFile(lang) {
  const filePath = path.join(__dirname, 'public', 'locales', lang, 'common.json');
  
  try {
    // Read existing file
    const data = fs.readFileSync(filePath, 'utf8');
    const translations = JSON.parse(data);
    
    // Initialize destinations.cities if it doesn't exist
    if (!translations.destinations) {
      translations.destinations = {};
    }
    if (!translations.destinations.cities) {
      translations.destinations.cities = {};
    }
    
    // Get city translations for this language (fallback to English)
    const cityData = cityTranslations[lang] || cityTranslations.en;
    
    // Add all city translations
    Object.keys(cityData).forEach(cityKey => {
      translations.destinations.cities[cityKey] = cityData[cityKey];
    });
    
    // Update subtitle to be dynamic
    translations.destinations.subtitle = "Discover the most sought-after wedding destinations across {stateName}";
    
    // Write back to file
    fs.writeFileSync(filePath, JSON.stringify(translations, null, 2), 'utf8');
    console.log(`Updated ${lang}/common.json`);
    
  } catch (error) {
    console.error(`Error updating ${lang}/common.json:`, error.message);
  }
}

// Update all language files
languages.forEach(updateLanguageFile);

console.log('All destination translations updated successfully!');
