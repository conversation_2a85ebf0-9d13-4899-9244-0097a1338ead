'use client'
import { TopHeader } from "@/components/top-header"
import { Head<PERSON> } from "@/components/header"
import { Footer } from "@/components/footer"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Heart, Share2, Download, Eye, Camera, MapPin, Calendar, MessageCircle } from "lucide-react"
import Image from "next/image"

export default function PhotoDetailsPage() {
  const photo = {
    id: 1,
    title: "Elegant Bridal Portrait",
    description:
      "A stunning bridal portrait captured during the golden hour, showcasing the intricate details of the lehenga and jewelry. The soft lighting creates a dreamy atmosphere perfect for this special moment.",
    category: "Bridal Portraits",
    photographer: {
      name: "Elegant Photography",
      avatar: "/placeholder.svg?height=40&width=40",
      verified: true,
    },
    location: "Udaipur, Rajasthan",
    date: "December 15, 2024",
    likes: 234,
    views: 1200,
    downloads: 45,
    image: "/placeholder.svg?height=800&width=600",
    tags: ["bridal", "portrait", "lehenga", "jewelry", "golden hour", "traditional"],
    camera: {
      model: "Canon EOS R5",
      lens: "85mm f/1.4",
      settings: "f/2.8, 1/200s, ISO 400",
    },
    relatedPhotos: [
      {
        id: 2,
        title: "Couple Moment",
        image: "/placeholder.svg?height=300&width=400",
        photographer: "Elegant Photography",
      },
      {
        id: 3,
        title: "Ceremony Shot",
        image: "/placeholder.svg?height=300&width=400",
        photographer: "Elegant Photography",
      },
      {
        id: 4,
        title: "Reception Dance",
        image: "/placeholder.svg?height=300&width=400",
        photographer: "Elegant Photography",
      },
      {
        id: 5,
        title: "Mehendi Design",
        image: "/placeholder.svg?height=300&width=400",
        photographer: "Elegant Photography",
      },
    ],
    comments: [
      {
        id: 1,
        user: "Priya Sharma",
        avatar: "/placeholder.svg?height=32&width=32",
        comment: "Absolutely stunning! The lighting and composition are perfect.",
        date: "2 hours ago",
      },
      {
        id: 2,
        user: "Rahul Patel",
        avatar: "/placeholder.svg?height=32&width=32",
        comment: "Beautiful capture! Can you share the camera settings used?",
        date: "5 hours ago",
      },
      {
        id: 3,
        user: "Meera Singh",
        avatar: "/placeholder.svg?height=32&width=32",
        comment: "This is giving me so much inspiration for my own wedding shoot!",
        date: "1 day ago",
      },
    ],
  }

  return (
    <div className="min-h-screen bg-white">
      <Header />

      <section className="py-8">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Main Image */}
            <div className="lg:col-span-2">
              <Card className="overflow-hidden">
                <div className="relative">
                  <Image
                    src={photo.image || "/placeholder.svg"}
                    alt={photo.title}
                    width={600}
                    height={800}
                    className="w-full h-auto object-cover"
                  />

                  {/* Action Buttons Overlay */}
                  <div className="absolute top-4 right-4 flex space-x-2">
                    <Button size="sm" variant="secondary" className="bg-white/90 hover:bg-white">
                      <Heart className="h-4 w-4" />
                    </Button>
                    <Button size="sm" variant="secondary" className="bg-white/90 hover:bg-white">
                      <Share2 className="h-4 w-4" />
                    </Button>
                    <Button size="sm" variant="secondary" className="bg-white/90 hover:bg-white">
                      <Download className="h-4 w-4" />
                    </Button>
                  </div>

                  {/* Stats Overlay */}
                  <div className="absolute bottom-4 left-4 flex space-x-4 text-white">
                    <div className="flex items-center bg-black/50 rounded-full px-3 py-1">
                      <Heart className="h-4 w-4 mr-1" />
                      <span className="text-sm">{photo.likes}</span>
                    </div>
                    <div className="flex items-center bg-black/50 rounded-full px-3 py-1">
                      <Eye className="h-4 w-4 mr-1" />
                      <span className="text-sm">{photo.views}</span>
                    </div>
                    <div className="flex items-center bg-black/50 rounded-full px-3 py-1">
                      <Download className="h-4 w-4 mr-1" />
                      <span className="text-sm">{photo.downloads}</span>
                    </div>
                  </div>
                </div>
              </Card>

              {/* Photo Details */}
              <Card className="mt-6">
                <CardContent className="p-6">
                  <div className="flex items-start justify-between mb-4">
                    <div>
                      <h1 className="text-2xl font-bold mb-2">{photo.title}</h1>
                      <Badge variant="secondary" className="mb-3">
                        {photo.category}
                      </Badge>
                      <p className="text-gray-700 mb-4">{photo.description}</p>
                    </div>
                  </div>

                  {/* Photographer Info */}
                  <div className="flex items-center space-x-4 mb-6 p-4 bg-gray-50 rounded-lg">
                    <Avatar>
                      <AvatarImage src={photo.photographer.avatar || "/placeholder.svg"} />
                      <AvatarFallback>{photo.photographer.name.charAt(0)}</AvatarFallback>
                    </Avatar>
                    <div className="flex-1">
                      <div className="flex items-center space-x-2">
                        <h3 className="font-semibold">{photo.photographer.name}</h3>
                        {photo.photographer.verified && <Badge className="bg-green-600 text-xs">Verified</Badge>}
                      </div>
                      <p className="text-sm text-gray-600">Professional Wedding Photographer</p>
                    </div>
                    <Button variant="outline" size="sm">
                      View Profile
                    </Button>
                  </div>

                  {/* Photo Meta */}
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                    <div className="flex items-center text-sm text-gray-600">
                      <MapPin className="h-4 w-4 mr-2" />
                      {photo.location}
                    </div>
                    <div className="flex items-center text-sm text-gray-600">
                      <Calendar className="h-4 w-4 mr-2" />
                      {photo.date}
                    </div>
                    <div className="flex items-center text-sm text-gray-600">
                      <Camera className="h-4 w-4 mr-2" />
                      {photo.camera.model}
                    </div>
                    <div className="flex items-center text-sm text-gray-600">
                      <span className="mr-2">📷</span>
                      {photo.camera.settings}
                    </div>
                  </div>

                  {/* Tags */}
                  <div className="mb-6">
                    <h3 className="font-semibold mb-3">Tags</h3>
                    <div className="flex flex-wrap gap-2">
                      {photo.tags.map((tag, index) => (
                        <Badge key={index} variant="outline" className="text-xs">
                          #{tag}
                        </Badge>
                      ))}
                    </div>
                  </div>

                  {/* Camera Details */}
                  <div className="border-t pt-6">
                    <h3 className="font-semibold mb-3">Camera Details</h3>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                      <div>
                        <span className="text-gray-600">Camera:</span>
                        <p className="font-medium">{photo.camera.model}</p>
                      </div>
                      <div>
                        <span className="text-gray-600">Lens:</span>
                        <p className="font-medium">{photo.camera.lens}</p>
                      </div>
                      <div>
                        <span className="text-gray-600">Settings:</span>
                        <p className="font-medium">{photo.camera.settings}</p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Comments Section */}
              <Card className="mt-6">
                <CardContent className="p-6">
                  <h3 className="text-lg font-semibold mb-4 flex items-center">
                    <MessageCircle className="h-5 w-5 mr-2" />
                    Comments ({photo.comments.length})
                  </h3>

                  <div className="space-y-4">
                    {photo.comments.map((comment) => (
                      <div key={comment.id} className="flex items-start space-x-3">
                        <Avatar className="w-8 h-8">
                          <AvatarImage src={comment.avatar || "/placeholder.svg"} />
                          <AvatarFallback>{comment.user.charAt(0)}</AvatarFallback>
                        </Avatar>
                        <div className="flex-1">
                          <div className="flex items-center space-x-2 mb-1">
                            <span className="font-medium text-sm">{comment.user}</span>
                            <span className="text-xs text-gray-500">{comment.date}</span>
                          </div>
                          <p className="text-sm text-gray-700">{comment.comment}</p>
                        </div>
                      </div>
                    ))}
                  </div>

                  <div className="mt-6 pt-4 border-t">
                    <div className="flex space-x-3">
                      <Avatar className="w-8 h-8">
                        <AvatarFallback>You</AvatarFallback>
                      </Avatar>
                      <div className="flex-1">
                        <textarea
                          placeholder="Add a comment..."
                          className="w-full p-3 border rounded-lg resize-none focus:outline-none focus:ring-2 focus:ring-pink-500"
                          rows={3}
                        />
                        <div className="flex justify-end mt-2">
                          <Button size="sm" className="bg-primary hover:bg-primary/90">
                            Post Comment
                          </Button>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Sidebar */}
            <div className="space-y-6">
              {/* Action Buttons */}
              <Card>
                <CardContent className="p-6">
                  <h3 className="text-lg font-bold mb-4">Actions</h3>
                  <div className="space-y-3">
                    <Button className="w-full bg-primary hover:bg-primary/90">
                      <Heart className="h-4 w-4 mr-2" />
                      Like Photo
                    </Button>
                    <Button variant="outline" className="w-full bg-transparent">
                      <Download className="h-4 w-4 mr-2" />
                      Download HD
                    </Button>
                    <Button variant="outline" className="w-full bg-transparent">
                      <Share2 className="h-4 w-4 mr-2" />
                      Share Photo
                    </Button>
                  </div>
                </CardContent>
              </Card>

              {/* Contact Photographer */}
              <Card>
                <CardContent className="p-6">
                  <h3 className="text-lg font-bold mb-4">Contact Photographer</h3>
                  <div className="space-y-3">
                    <Button variant="outline" className="w-full bg-transparent">
                      View Portfolio
                    </Button>
                    <Button variant="outline" className="w-full bg-transparent">
                      Send Message
                    </Button>
                    <Button variant="outline" className="w-full bg-transparent">
                      Book Session
                    </Button>
                  </div>
                </CardContent>
              </Card>

              {/* Photo Stats */}
              <Card>
                <CardContent className="p-6">
                  <h3 className="text-lg font-bold mb-4">Photo Stats</h3>
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Views</span>
                      <span className="font-semibold">{photo.views.toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Likes</span>
                      <span className="font-semibold">{photo.likes}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Downloads</span>
                      <span className="font-semibold">{photo.downloads}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Comments</span>
                      <span className="font-semibold">{photo.comments.length}</span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Related Photos */}
              <Card>
                <CardContent className="p-6">
                  <h3 className="text-lg font-bold mb-4">More from this Photographer</h3>
                  <div className="grid grid-cols-2 gap-3">
                    {photo.relatedPhotos.map((relatedPhoto) => (
                      <div key={relatedPhoto.id} className="relative group cursor-pointer">
                        <Image
                          src={relatedPhoto.image || "/placeholder.svg"}
                          alt={relatedPhoto.title}
                          width={400}
                          height={300}
                          className="w-full h-24 object-cover rounded-lg"
                        />
                        <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity rounded-lg flex items-center justify-center">
                          <Eye className="h-4 w-4 text-white" />
                        </div>
                      </div>
                    ))}
                  </div>
                  <Button variant="outline" className="w-full mt-4 bg-transparent">
                    View All Photos
                  </Button>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  )
}
