import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Image,
  Alert,
  Linking,
} from 'react-native';
import { RouteProp, useRoute, useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../providers/ThemeProvider';
import { AppStackParamList } from '../navigation/AppNavigator';

type OrderDetailsRouteProp = RouteProp<AppStackParamList, 'OrderDetails'>;

interface OrderItem {
  id: string;
  name: string;
  price: number;
  quantity: number;
  image?: string;
}

interface Order {
  id: string;
  items: OrderItem[];
  status: 'confirmed' | 'processing' | 'shipped' | 'delivered' | 'cancelled';
  total: number;
  subtotal: number;
  deliveryCharge: number;
  tax: number;
  paymentMethod: string;
  shippingAddress: {
    fullName: string;
    phone: string;
    addressLine1: string;
    addressLine2?: string;
    city: string;
    state: string;
    pincode: string;
  };
  trackingNumber?: string;
  estimatedDelivery?: string;
  createdAt: string;
  updatedAt: string;
}

export default function OrderDetailsScreen() {
  const route = useRoute<OrderDetailsRouteProp>();
  const navigation = useNavigation();
  const { theme } = useTheme();
  const { orderId } = route.params;

  const [order, setOrder] = useState<Order | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadOrderDetails();
  }, [orderId]);

  const loadOrderDetails = async () => {
    try {
      setLoading(true);
      
      // Mock order data - in real app, fetch from API
      const mockOrder: Order = {
        id: orderId,
        items: [
          {
            id: '1',
            name: 'Bridal Lehenga - Royal Red',
            price: 45000,
            quantity: 1,
            image: '/placeholder-image.jpg
          },
          {
            id: '2',
            name: 'Wedding Jewelry Set',
            price: 25000,
            quantity: 1,
            image: '/placeholder-image.jpg
          },
        ],
        status: 'processing',
        subtotal: 70000,
        deliveryCharge: 0,
        tax: 12600,
        total: 82600,
        paymentMethod: 'Credit Card',
        shippingAddress: {
          fullName: 'Priya Sharma',
          phone: '+91 8148376909',
          addressLine1: '123 MG Road',
          addressLine2: 'Near City Mall',
          city: 'Chennai',
          state: 'Tamil Nadu',
          pincode: '600001',
        },
        trackingNumber: 'TM123456789IN',
        estimatedDelivery: '2024-01-15',
        createdAt: '2024-01-10T10:30:00Z',
        updatedAt: '2024-01-11T14:20:00Z',
      };

      setOrder(mockOrder);
    } catch (error) {
      console.error('Error loading order details:', error);
      Alert.alert('Error', 'Failed to load order details');
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'confirmed':
        return theme.colors.success;
      case 'processing':
        return theme.colors.warning;
      case 'shipped':
        return theme.colors.info;
      case 'delivered':
        return theme.colors.success;
      case 'cancelled':
        return theme.colors.error;
      default:
        return theme.colors.textSecondary;
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'confirmed':
        return 'checkmark-circle';
      case 'processing':
        return 'time';
      case 'shipped':
        return 'car';
      case 'delivered':
        return 'checkmark-done-circle';
      case 'cancelled':
        return 'close-circle';
      default:
        return 'help-circle';
    }
  };

  const handleTrackOrder = () => {
    if (order?.trackingNumber) {
      // Open tracking URL or show tracking info
      Alert.alert('Track Order', `Tracking Number: ${order.trackingNumber}`);
    }
  };

  const handleCancelOrder = () => {
    Alert.alert(
      'Cancel Order',
      'Are you sure you want to cancel this order?',
      [
        { text: 'No', style: 'cancel' },
        {
          text: 'Yes, Cancel',
          style: 'destructive',
          onPress: () => {
            // Handle order cancellation
            Alert.alert('Order Cancelled', 'Your order has been cancelled successfully.');
          },
        },
      ]
    );
  };

  const handleContactSupport = () => {
    Alert.alert(
      'Contact Support',
      'How would you like to contact us?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Call',
          onPress: () => Linking.openURL('tel:+911234567890'),
        },
        {
          text: 'Email',
          onPress: () => Linking.openURL('mailto:<EMAIL>'),
        },
      ]
    );
  };

  if (loading) {
    return (
      <View style={[styles.container, styles.centered, { backgroundColor: theme.colors.background }]}>
        <Text style={{ color: theme.colors.text }}>Loading order details...</Text>
      </View>
    );
  }

  if (!order) {
    return (
      <View style={[styles.container, styles.centered, { backgroundColor: theme.colors.background }]}>
        <Text style={{ color: theme.colors.text }}>Order not found</Text>
      </View>
    );
  }

  return (
    <ScrollView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {/* Order Header */}
      <View style={[styles.section, { backgroundColor: theme.colors.surface }]}>
        <View style={styles.orderHeader}>
          <Text style={[styles.orderId, { color: theme.colors.text }]}>
            Order #{order.id}
          </Text>
          <View style={[styles.statusBadge, { backgroundColor: getStatusColor(order.status) + '20' }]}>
            <Ionicons
              name={getStatusIcon(order.status) as any}
              size={16}
              color={getStatusColor(order.status)}
            />
            <Text style={[styles.statusText, { color: getStatusColor(order.status) }]}>
              {order.status.charAt(0).toUpperCase() + order.status.slice(1)}
            </Text>
          </View>
        </View>
        
        <Text style={[styles.orderDate, { color: theme.colors.textSecondary }]}>
          Placed on {new Date(order.createdAt).toLocaleDateString()}
        </Text>

        {order.estimatedDelivery && (
          <Text style={[styles.deliveryDate, { color: theme.colors.text }]}>
            Estimated delivery: {new Date(order.estimatedDelivery).toLocaleDateString()}
          </Text>
        )}
      </View>

      {/* Order Items */}
      <View style={[styles.section, { backgroundColor: theme.colors.surface }]}>
        <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
          Items ({order.items.length})
        </Text>
        {order.items.map((item) => (
          <View key={item.id} style={styles.orderItem}>
            <Image
              source={{ uri: item.image || '/placeholder-image.jpg
              style={styles.itemImage}
            />
            <View style={styles.itemDetails}>
              <Text style={[styles.itemName, { color: theme.colors.text }]} numberOfLines={2}>
                {item.name}
              </Text>
              <Text style={[styles.itemPrice, { color: theme.colors.textSecondary }]}>
                ₹{item.price.toLocaleString()} × {item.quantity}
              </Text>
            </View>
            <Text style={[styles.itemTotal, { color: theme.colors.text }]}>
              ₹{(item.price * item.quantity).toLocaleString()}
            </Text>
          </View>
        ))}
      </View>

      {/* Shipping Address */}
      <View style={[styles.section, { backgroundColor: theme.colors.surface }]}>
        <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
          Shipping Address
        </Text>
        <View style={styles.addressContainer}>
          <Text style={[styles.addressName, { color: theme.colors.text }]}>
            {order.shippingAddress.fullName}
          </Text>
          <Text style={[styles.addressPhone, { color: theme.colors.textSecondary }]}>
            {order.shippingAddress.phone}
          </Text>
          <Text style={[styles.addressText, { color: theme.colors.textSecondary }]}>
            {order.shippingAddress.addressLine1}
          </Text>
          {order.shippingAddress.addressLine2 && (
            <Text style={[styles.addressText, { color: theme.colors.textSecondary }]}>
              {order.shippingAddress.addressLine2}
            </Text>
          )}
          <Text style={[styles.addressText, { color: theme.colors.textSecondary }]}>
            {order.shippingAddress.city}, {order.shippingAddress.state} - {order.shippingAddress.pincode}
          </Text>
        </View>
      </View>

      {/* Payment Details */}
      <View style={[styles.section, { backgroundColor: theme.colors.surface }]}>
        <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
          Payment Details
        </Text>
        <View style={styles.paymentRow}>
          <Text style={[styles.paymentLabel, { color: theme.colors.text }]}>
            Subtotal ({order.items.length} items)
          </Text>
          <Text style={[styles.paymentValue, { color: theme.colors.text }]}>
            ₹{order.subtotal.toLocaleString()}
          </Text>
        </View>
        <View style={styles.paymentRow}>
          <Text style={[styles.paymentLabel, { color: theme.colors.text }]}>
            Delivery Charges
          </Text>
          <Text style={[styles.paymentValue, { color: order.deliveryCharge === 0 ? theme.colors.success : theme.colors.text }]}>
            {order.deliveryCharge === 0 ? 'FREE' : `₹${order.deliveryCharge}`}
          </Text>
        </View>
        <View style={styles.paymentRow}>
          <Text style={[styles.paymentLabel, { color: theme.colors.text }]}>
            Tax (GST)
          </Text>
          <Text style={[styles.paymentValue, { color: theme.colors.text }]}>
            ₹{order.tax.toLocaleString()}
          </Text>
        </View>
        <View style={[styles.paymentRow, styles.totalRow]}>
          <Text style={[styles.totalLabel, { color: theme.colors.text }]}>
            Total Paid
          </Text>
          <Text style={[styles.totalValue, { color: theme.colors.primary }]}>
            ₹{order.total.toLocaleString()}
          </Text>
        </View>
        <View style={styles.paymentMethodContainer}>
          <Text style={[styles.paymentMethodLabel, { color: theme.colors.textSecondary }]}>
            Payment Method: {order.paymentMethod}
          </Text>
        </View>
      </View>

      {/* Action Buttons */}
      <View style={styles.actionsContainer}>
        {order.trackingNumber && order.status !== 'delivered' && (
          <TouchableOpacity
            style={[styles.actionButton, { backgroundColor: theme.colors.primary }]}
            onPress={handleTrackOrder}
          >
            <Ionicons name="location-outline" size={20} color="#fff" />
            <Text style={styles.actionButtonText}>Track Order</Text>
          </TouchableOpacity>
        )}

        {order.status === 'confirmed' && (
          <TouchableOpacity
            style={[styles.actionButton, { backgroundColor: theme.colors.error }]}
            onPress={handleCancelOrder}
          >
            <Ionicons name="close-circle-outline" size={20} color="#fff" />
            <Text style={styles.actionButtonText}>Cancel Order</Text>
          </TouchableOpacity>
        )}

        <TouchableOpacity
          style={[styles.actionButton, { backgroundColor: theme.colors.secondary }]}
          onPress={handleContactSupport}
        >
          <Ionicons name="headset-outline" size={20} color="#fff" />
          <Text style={styles.actionButtonText}>Contact Support</Text>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  centered: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  section: {
    margin: 16,
    padding: 16,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  orderHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  orderId: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  statusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    gap: 4,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '600',
  },
  orderDate: {
    fontSize: 14,
    marginBottom: 4,
  },
  deliveryDate: {
    fontSize: 14,
    fontWeight: '500',
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
  },
  orderItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  itemImage: {
    width: 80,
    height: 80,
    borderRadius: 8,
    marginRight: 12,
  },
  itemDetails: {
    flex: 1,
  },
  itemName: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 4,
  },
  itemPrice: {
    fontSize: 12,
  },
  itemTotal: {
    fontSize: 14,
    fontWeight: '600',
  },
  addressContainer: {
    gap: 4,
  },
  addressName: {
    fontSize: 16,
    fontWeight: '600',
  },
  addressPhone: {
    fontSize: 14,
  },
  addressText: {
    fontSize: 14,
    lineHeight: 20,
  },
  paymentRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  paymentLabel: {
    fontSize: 14,
  },
  paymentValue: {
    fontSize: 14,
    fontWeight: '500',
  },
  totalRow: {
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
    paddingTop: 12,
    marginTop: 8,
  },
  totalLabel: {
    fontSize: 16,
    fontWeight: '600',
  },
  totalValue: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  paymentMethodContainer: {
    marginTop: 12,
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
  },
  paymentMethodLabel: {
    fontSize: 14,
  },
  actionsContainer: {
    padding: 16,
    gap: 12,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    borderRadius: 8,
    gap: 8,
  },
  actionButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
});
