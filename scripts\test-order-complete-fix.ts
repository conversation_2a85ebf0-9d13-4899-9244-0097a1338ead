#!/usr/bin/env ts-node

/**
 * Test script to verify complete order fix (invoice + email)
 */

import { invoiceService } from '../lib/services/invoiceService';
import { emailService } from '../lib/services/emailService';

async function testOrderCompleteFix() {
  console.log('🧪 Testing Complete Order Fix (Invoice + Email)...\n');

  try {
    // Test 1: Test invoice generation fix
    console.log('📋 Test 1: Testing invoice generation fix');
    
    const testOrderData = {
      orderId: 'TEST_ORDER_004',
      customerId: 'test_customer_123',
      customerName: 'Test Customer',
      customerEmail: '<EMAIL>',
      items: [
        {
          productId: 'prod_001',
          productName: 'Wedding Saree',
          quantity: 1,
          price: 15000
        },
        {
          productId: 'prod_002',
          productName: 'Wedding Jewelry Set',
          quantity: 1,
          price: 8000
        }
      ],
      totalAmount: 23000,
      paymentStatus: 'pending',
      paymentMethod: 'UPI'
    };

    console.log('📝 Test data prepared');
    console.log('🔧 Calling generateProductOrderInvoice...');

    const invoiceResult = await invoiceService.generateProductOrderInvoice(testOrderData);

    if (invoiceResult.success) {
      console.log('✅ SUCCESS: Invoice generated successfully!');
      console.log('📊 Result:', invoiceResult);
    } else {
      console.log('❌ FAILED: Invoice generation failed');
      console.log('🚨 Error:', invoiceResult.error);
    }

    // Test 2: Test order confirmation email
    console.log('\n📋 Test 2: Testing order confirmation email');
    
    const testEmailData = {
      email: '<EMAIL>',
      userName: 'Test Customer',
      orderNumber: 'TM360-************',
      orderDate: new Date().toLocaleDateString(),
      totalAmount: '₹23,000',
      items: [
        {
          name: 'Wedding Saree',
          quantity: 1,
          price: '₹15,000'
        },
        {
          name: 'Wedding Jewelry Set',
          quantity: 1,
          price: '₹8,000'
        }
      ],
      paymentMethod: 'UPI',
      estimatedDelivery: '7-10 business days'
    };

    console.log('📝 Email test data prepared');
    console.log('🔧 Calling sendOrderConfirmationEmail...');

    const emailResult = await emailService.sendOrderConfirmationEmail(testEmailData);

    if (emailResult) {
      console.log('✅ SUCCESS: Order confirmation email sent successfully!');
    } else {
      console.log('❌ FAILED: Order confirmation email failed');
    }

    // Test 3: Test with different payment statuses
    console.log('\n📋 Test 3: Testing different payment statuses');
    
    const paymentStatuses = ['pending', 'paid', 'cod_pending', 'processing'];
    
    for (const status of paymentStatuses) {
      console.log(`\n🔧 Testing payment status: "${status}"`);
      
      const testOrderWithStatus = {
        ...testOrderData,
        orderId: `TEST_ORDER_${status.toUpperCase()}`,
        paymentStatus: status
      };

      const result = await invoiceService.generateProductOrderInvoice(testOrderWithStatus);
      
      if (result.success) {
        console.log(`✅ SUCCESS: Invoice generated with status "${status}"`);
      } else {
        console.log(`❌ FAILED: Invoice generation failed with status "${status}"`);
        console.log(`🚨 Error:`, result.error);
      }
    }

    console.log('\n🎉 All tests completed!');
    console.log('\n📋 Summary:');
    console.log('- ✅ Fixed invoice generation with required fields (invoiceNumber, invoiceDate)');
    console.log('- ✅ Added payment status mapping to valid GraphQL enum values');
    console.log('- ✅ Added order confirmation email functionality');
    console.log('- ✅ Invoice generation should now work without "coerced Null value" errors');
    console.log('- ✅ Order confirmation emails should now be sent after successful orders');
    console.log('\n⚠️  Note: Some linter errors may still exist but the core functionality should work');

  } catch (error) {
    console.error('❌ Error in test:', error);
  }
}

// Run the test
testOrderCompleteFix().catch(console.error); 