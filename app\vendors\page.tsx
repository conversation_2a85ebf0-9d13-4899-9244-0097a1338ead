"use client"
import { LayoutWrapper } from "@/components/layout-wrapper";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Search, MapPin, Star, Filter, Camera, Music, Utensils, Palette, Users, Heart, Loader2, CheckCircle, Share2, Calendar, X } from "lucide-react";

import { useSafeTranslation } from '@/hooks/use-safe-translation';

import Link from "next/link";
import { useEffect, useState } from "react";
import { useSearchParams } from "next/navigation";
import { vendorService, VendorResponse } from "@/lib/services/vendorService";
import { SimpleSEO } from '@/components/seo/SimpleSEO';

const categories = [
  { icon: Camera, name: "Photography", value: "photography" },
  { icon: Music, name: "Music", value: "music" },
  { icon: Utensils, name: "Catering", value: "catering" },
  { icon: Palette, name: "Decoration", value: "decoration" },
  { icon: Heart, name: "Bridal Wear", value: "bridal-wear" },
  { icon: Users, name: "Jewelry", value: "jewelry" },
  { icon: Users, name: "Marriage Mahal", value: "marriage-mahal" },
  { icon: Heart, name: "Bridal Makeup", value: "bridal-makeup" },
  { icon: Users, name: "Invitations", value: "invitations" },
  { icon: Users, name: "Astrologer", value: "astrologer" },
  { icon: Users, name: "Event Planning", value: "event-planning" },
  { icon: Users, name: "Religious Services", value: "religious-services" },
  { icon: Users, name: "Gifts", value: "gifts" },
  { icon: Users, name: "Transportation", value: "transportation" },
];

// Map URL category params to internal values
const categoryMapping: { [key: string]: string } = {
  'marriage-mahal': 'marriage-mahal',
  'photo-videographers': 'photography',
  'photographers': 'photography',
  'photography': 'photography',
  'cooks-caterings': 'catering',
  'catering': 'catering',
  'bridal-makeup-artists': 'bridal-makeup',
  'makeup': 'bridal-makeup',
  'musical-artists': 'music',
  'music': 'music',
  'invitations': 'invitations',
  'wedding-jewellery-sets': 'jewelry',
  'jewelry': 'jewelry',
  'marriage-outfits': 'bridal-wear',
  'bridal-wear': 'bridal-wear',
  'astrologer': 'astrologer',
  'dj-music': 'music',
  'decorators': 'decoration',
  'decoration': 'decoration',
  'snacks-shops': 'catering',
  'event-organizers': 'event-planning',
  'iyer-pandit': 'religious-services',
  'return-gift': 'gifts',
  'flower-shops': 'decoration',
  'travels': 'transportation'
};

export default function VendorsPage() {
  const searchParams = useSearchParams();
  const { t } = useSafeTranslation();
  const [vendors, setVendors] = useState<VendorResponse[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [nextToken, setNextToken] = useState<string | undefined>(undefined);
  const [hasMoreData, setHasMoreData] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCity, setSelectedCity] = useState("");

  const [selectedCategory, setSelectedCategory] = useState("all");
  const [selectedRating, setSelectedRating] = useState("all");
  const [selectedPriceRange, setSelectedPriceRange] = useState("all");
  const [selectedExperience, setSelectedExperience] = useState("all");
  const [selectedAvailability, setSelectedAvailability] = useState("all");
  const [sortBy, setSortBy] = useState("default");
  const [showFilters, setShowFilters] = useState(false);
  const [imageErrors, setImageErrors] = useState<Set<string>>(new Set());

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(10); // 10 vendors per page
  const [totalServerItems, setTotalServerItems] = useState(0);
  const [serverPages, setServerPages] = useState<{[key: number]: VendorResponse[]}>({});
  const [loadedPages, setLoadedPages] = useState<Set<number>>(new Set([1]));

  // Get category from URL params
  const categoryParam = searchParams.get('category');

  useEffect(() => {
    if (categoryParam) {
      // Map URL parameter to internal category value
      const mappedCategory = categoryMapping[categoryParam.toLowerCase()] || categoryParam;
      setSelectedCategory(mappedCategory);
    }

    // Handle search parameters from home page and destinations ad
    const searchParam = searchParams.get('search');
    const locationParam = searchParams.get('location');
    const cityParam = searchParams.get('city');
    const stateParam = searchParams.get('state');
    const sourceParam = searchParams.get('source');

    if (searchParam) {
      setSearchTerm(searchParam);
      setSearchInput(searchParam); // Also set search input for display
    }

    // Handle location from different sources
    if (cityParam && stateParam) {
      // From destinations ad - combine city and state
      const locationString = `${cityParam}, ${stateParam}`;
      setSelectedCity(locationString);
      setCityInput(locationString); // Also set city input for display
    } else if (locationParam) {
      // From home page search
      setSelectedCity(locationParam);
      setCityInput(locationParam); // Also set city input for display
    }

    // Log source for analytics
    if (sourceParam) {
      console.log('Vendors page accessed from:', sourceParam);
    }
  }, [categoryParam, searchParams]);

  useEffect(() => {
    loadVendors(true); // Reset and reload when category changes
  }, [selectedCategory]);

  const loadVendors = async (reset: boolean = false) => {
    try {
      setLoading(true);
      setError(null);

      if (reset) {
        // Reset all pagination state
        setServerPages({});
        setLoadedPages(new Set());
        setCurrentPage(1);
        setNextToken(undefined);
      }

      let vendorList: VendorResponse[];
      let nextTokenResult: string | undefined;

      // Load more items initially to enable pagination (load 50 items)
      const result = await vendorService.getAllVendors(50); // Load 50 vendors
      vendorList = result.vendors;
      nextTokenResult = result.nextToken;

      // Store all vendors for client-side pagination and filtering
      setVendors(vendorList);
      setTotalServerItems(vendorList.length);



      // Organize vendors into pages for traditional pagination
      const pages: {[key: number]: VendorResponse[]} = {};
      for (let i = 0; i < vendorList.length; i += itemsPerPage) {
        const pageNumber = Math.floor(i / itemsPerPage) + 1;
        pages[pageNumber] = vendorList.slice(i, i + itemsPerPage);
      }
      setServerPages(pages);
      setLoadedPages(new Set(Object.keys(pages).map(Number)));

      setNextToken(nextTokenResult);
      setHasMoreData(!!nextTokenResult);
    } catch (err: any) {
      setError(err.message || 'Failed to load vendors');
      console.error('Error loading vendors:', err);
    } finally {
      setLoading(false);
    }
  };

  // Get unique values for filter options
  const getUniqueCities = () => {
    const cities = vendors.map(v => v.city).filter(Boolean)
    return [...new Set(cities)]
  }

  const clearAllFilters = () => {
    setSearchTerm("")
    setSelectedCity("")
    setSelectedCategory("all")
    setSelectedRating("all")
    setSelectedPriceRange("all")
    setSelectedExperience("all")
    setSelectedAvailability("all")
    setSortBy("default")
  }

  const hasActiveFilters = () => {
    return searchTerm || selectedCity || selectedCategory !== "all" || selectedRating !== "all" ||
           selectedPriceRange !== "all" || selectedExperience !== "all" ||
           selectedAvailability !== "all"
  }

  // Helper function to get vendor image with fallback
  const getVendorImage = (vendor: VendorResponse) => {
    if (imageErrors.has(vendor.id!)) {
      return "/placeholder.svg"
    }

    // Check if vendor has a valid profile photo
    const profilePhoto = vendor.profilePhoto;
    if (profilePhoto && profilePhoto.trim() !== '' && profilePhoto !== 'undefined' && profilePhoto !== 'null') {
      return profilePhoto;
    }

    return "/placeholder.svg"
  }

  // Handle image load errors
  const handleImageError = (vendorId: string) => {
    setImageErrors(prev => new Set([...prev, vendorId]))
  }

  // Filter vendors based on all criteria
  const filteredVendors = vendors.filter(vendor => {
    const matchesSearch = !searchTerm ||
      vendor.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      vendor.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      vendor.category?.toLowerCase().includes(searchTerm.toLowerCase());



    const matchesCity = !selectedCity ||
      vendor.city.toLowerCase().includes(selectedCity.toLowerCase());

    const matchesCategory = !selectedCategory || selectedCategory === "all" || (() => {
      const vendorCat = vendor.category?.toLowerCase() || '';
      const selectedCat = selectedCategory.toLowerCase();

      // Direct match
      if (vendorCat === selectedCat) {
        return true;
      }

      // Handle photography/photographers variations
      if (selectedCat === 'photography' && (vendorCat.includes('photo') || vendorCat.includes('camera'))) {
        return true;
      }
      if (selectedCat === 'photographers' && (vendorCat.includes('photo') || vendorCat.includes('camera'))) {
        return true;
      }

      // Handle other category variations
      if (selectedCat === 'music' && (vendorCat.includes('music') || vendorCat.includes('dj') || vendorCat.includes('band'))) return true;
      if (selectedCat === 'catering' && (vendorCat.includes('cater') || vendorCat.includes('food'))) return true;
      if (selectedCat === 'decoration' && (vendorCat.includes('decor') || vendorCat.includes('flower'))) return true;
      if (selectedCat === 'bridal-wear' && (vendorCat.includes('bridal') || vendorCat.includes('dress'))) return true;
      if (selectedCat === 'jewelry' && (vendorCat.includes('jewel') || vendorCat.includes('ornament'))) return true;

      return false;
    })();

    const matchesRating = !selectedRating || selectedRating === "all" || (() => {
      const rating = vendor.rating || 0
      switch (selectedRating) {
        case "4.5": return rating >= 4.5
        case "4.0": return rating >= 4.0
        case "3.5": return rating >= 3.5
        case "3.0": return rating >= 3.0
        default: return true
      }
    })()

    const matchesPriceRange = !selectedPriceRange || selectedPriceRange === "all" || (() => {
      const price = vendor.startingPrice || 0
      switch (selectedPriceRange) {
        case "budget": return price < 25000
        case "mid": return price >= 25000 && price <= 75000
        case "premium": return price > 75000 && price <= 150000
        case "luxury": return price > 150000
        default: return true
      }
    })()

    const matchesExperience = selectedExperience === "all" || (() => {
      const experience = vendor.experience || 0
      switch (selectedExperience) {
        case "1-3": return experience >= 1 && experience <= 3
        case "4-7": return experience >= 4 && experience <= 7
        case "8-plus": return experience >= 8
        default: return true
      }
    })()

    const matchesAvailability = selectedAvailability === "all" || (() => {
      switch (selectedAvailability) {
        case "available": return vendor.isAvailable
        case "busy": return !vendor.isAvailable
        default: return true
      }
    })()

    return matchesSearch && matchesCity && matchesCategory && matchesRating &&
           matchesPriceRange && matchesExperience && matchesAvailability
  }).sort((a, b) => {
    switch (sortBy) {
      case 'rating':
        return (b.rating || 0) - (a.rating || 0)
      case 'price-low':
        return (a.startingPrice || 0) - (b.startingPrice || 0)
      case 'price-high':
        return (b.startingPrice || 0) - (a.startingPrice || 0)
      case 'experience':
        return (b.experience || 0) - (a.experience || 0)
      case 'name':
        return a.name.localeCompare(b.name)
      case 'default':
      default:
        // Default sorting by rating when no sort is selected
        return (b.rating || 0) - (a.rating || 0)
    }
  });



  // Hybrid pagination logic
  const totalPages = Math.ceil(filteredVendors.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const paginatedVendors = filteredVendors.slice(startIndex, endIndex);

  // Reset and reload data when filters change
  useEffect(() => {
    setCurrentPage(1);
    loadVendors(true); // Reset and load first page
  }, [searchTerm, selectedCity, selectedCategory, selectedRating, selectedPriceRange, selectedExperience, selectedAvailability]);

  // Traditional pagination functions
  const goToPage = (page: number) => {
    setCurrentPage(page);
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const goToPrevious = () => {
    if (currentPage > 1) {
      goToPage(currentPage - 1);
    }
  };

  const goToNext = () => {
    if (currentPage < totalPages) {
      goToPage(currentPage + 1);
    }
  };

  const getPageNumbers = () => {
    const pages = [];
    const maxVisiblePages = 5;
    let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
    let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);

    if (endPage - startPage + 1 < maxVisiblePages) {
      startPage = Math.max(1, endPage - maxVisiblePages + 1);
    }

    for (let i = startPage; i <= endPage; i++) {
      pages.push(i);
    }
    return pages;
  };

  // Load more data function for additional server data
  const loadMoreVendors = async () => {
    if (hasMoreData && !loading) { // Allow load more for all cases
      await loadVendors(false); // Load next page and append
    }
  };

  const showLoadMore = hasMoreData && !loading && totalPages <= Math.ceil(vendors.length / itemsPerPage);

  // Remove current search and city filter logic for the filter bar
  // Add state for suggestions based on paginatedVendors
  const [searchInput, setSearchInput] = useState("");
  const [cityInput, setCityInput] = useState("");
  const [showSearchSuggestions, setShowSearchSuggestions] = useState(false);
  const [showCitySuggestionsBox, setShowCitySuggestionsBox] = useState(false);

  // Suggestions based on paginatedVendors
  const searchSuggestions = Array.from(new Set(paginatedVendors.map(v => v.name).filter(Boolean)));
  const citySuggestionsBox = Array.from(new Set(paginatedVendors.map(v => v.city).filter(Boolean)));
  const filteredSearchSuggestions = searchInput ? searchSuggestions.filter(name => name.toLowerCase().includes(searchInput.toLowerCase())) : searchSuggestions;
  const filteredCitySuggestionsBox = cityInput ? citySuggestionsBox.filter(city => city.toLowerCase().includes(cityInput.toLowerCase())) : citySuggestionsBox;

  // Add state for vendor category suggestions
  const [showCategorySuggestions, setShowCategorySuggestions] = useState(false);
  const [filteredCategorySuggestions, setFilteredCategorySuggestions] = useState<string[]>([]);

  useEffect(() => {
    if (searchTerm) {
      const suggestions = categories
        .map(c => c.name)
        .filter(name => name.toLowerCase().includes(searchTerm.toLowerCase()));
      setFilteredCategorySuggestions(suggestions);
    } else {
      setFilteredCategorySuggestions([]);
    }
  }, [searchTerm]);

  return (
    <>
      <SimpleSEO
        title="Wedding Vendors - Find the Best Wedding Service Providers | BookmyFestive"
        description="Discover top-rated wedding vendors including photographers, decorators, caterers, and more. Compare prices, read reviews, and book the perfect vendors for Your Dream Celebration."
        keywords="wedding vendors, wedding photographers, wedding decorators, wedding caterers, Festive Services, wedding professionals"
        url="/vendors"
      />
      <LayoutWrapper>
      <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="relative bg-primary text-primary-foreground py-12 md:py-20 overflow-hidden">
        <div className="absolute inset-0 w-full h-full z-0">
          <div className="absolute inset-0 bg-black/60"></div>
          <img src="/mahal_hero.webp" alt="Wedding Vendors" className="w-full h-full object-cover object-center" />
        </div>
        <div className="relative container mx-auto px-4 z-10">
          <div className="text-center mb-8 md:mb-12">
            <h1 className="text-3xl md:text-5xl lg:text-6xl font-bold text-white mb-4 md:mb-6 font-sans tracking-tight drop-shadow-2xl">
              Find Your Perfect Festive Vendors
            </h1>
          </div>
          {/* Enhanced Search and Filters - responsive for mobile */}
          <div className="bg-white/95 backdrop-blur-sm rounded-2xl shadow-lg p-4 md:p-3 max-w-3xl mx-auto border border-white/20 w-full">
            {/* Desktop search form */}
            <div className="hidden md:flex flex-row gap-2 w-full flex-nowrap">
              {/* Search Vendor Input with suggestions based on paginatedVendors */}
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search Vendor Name"
                  className="pl-10 pr-2 h-10 rounded-xl border border-gray-200 focus:border-primary text-base w-full bg-white text-gray-900 placeholder:text-gray-500"
                  value={searchInput}
                  onChange={e => setSearchInput(e.target.value)}
                  style={{ minWidth: 0 }}
                  autoComplete="off"
                  onFocus={() => setShowSearchSuggestions(true)}
                  onBlur={() => setTimeout(() => setShowSearchSuggestions(false), 100)}
                />
                {showSearchSuggestions && filteredSearchSuggestions.length > 0 && (
                  <div className="absolute z-10 mt-1 w-full bg-white border border-gray-200 rounded-xl shadow-lg max-h-48 overflow-y-auto">
                    {filteredSearchSuggestions.map((name) => (
                      <div
                        key={name}
                        className="px-4 py-2 cursor-pointer hover:bg-primary/10 text-sm text-gray-600"
                        onMouseDown={() => { setSearchInput(name); setShowSearchSuggestions(false); }}
                      >
                        {name}
                      </div>
                    ))}
                  </div>
                )}
              </div>
              {/* City Autocomplete Input with suggestions based on paginatedVendors */}
              <div className="relative flex-1">
                <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  type="text"
                  placeholder="Enter City Name"
                  className="pl-10 pr-2 h-10 rounded-xl border border-gray-200 focus:border-primary text-base w-full bg-white text-gray-900 placeholder:text-gray-500"
                  value={cityInput}
                  onChange={e => setCityInput(e.target.value)}
                  autoComplete="off"
                  style={{ minWidth: 0 }}
                  onFocus={() => setShowCitySuggestionsBox(true)}
                  onBlur={() => setTimeout(() => setShowCitySuggestionsBox(false), 100)}
                />
                {showCitySuggestionsBox && filteredCitySuggestionsBox.length > 0 && (
                  <div className="absolute z-10 mt-1 w-full bg-white border border-gray-200 rounded-xl shadow-lg max-h-48 overflow-y-auto">
                    {filteredCitySuggestionsBox.map(city => (
                      <div
                        key={city}
                        className="px-4 py-2 cursor-pointer hover:bg-primary/10 text-sm text-gray-600"
                        onMouseDown={() => { setCityInput(city); setShowCitySuggestionsBox(false); }}
                      >
                        {city}
                      </div>
                    ))}
                  </div>
                )}
              </div>


              <div className="w-auto">
                <Button
                  className="h-10 bg-primary hover:bg-primary/90 rounded-xl text-base font-semibold shadow-lg hover:shadow-xl transition-all duration-200 px-6"
                  onClick={loadVendors}
                  disabled={loading}
                >
                  {loading ? (
                    <Loader2 className="h-5 w-5 animate-spin mr-2" />
                  ) : (
                    <Search className="h-5 w-5 mr-2" />
                  )}
                  Search
                </Button>
              </div>
            </div>
            
            {/* Mobile search form */}
            <div className="md:hidden">
              <div className="grid grid-cols-2 gap-2 mb-2">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <input
                    type="text"
                    placeholder="Search"
                    className="pl-10 pr-2 h-10 rounded-lg border border-gray-200 focus:border-primary text-sm w-full bg-white text-gray-900 placeholder:text-gray-500"
                    value={searchTerm}
                    onChange={e => setSearchTerm(e.target.value)}
                    autoComplete="off"
                  />
                </div>
                <div className="relative">
                  <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <input
                    type="text"
                    placeholder="City"
                    className="pl-10 pr-2 h-10 rounded-lg border border-gray-200 focus:border-primary text-sm w-full bg-white text-gray-900 placeholder:text-gray-500"
                    value={selectedCity}
                    onChange={e => setSelectedCity(e.target.value)}
                    autoComplete="off"
                  />
                </div>
              </div>
              <div className="w-full">
                <Button
                  className="h-10 bg-primary hover:bg-primary/90 rounded-lg text-sm font-semibold shadow-lg hover:shadow-xl transition-all duration-200 w-full"
                  onClick={loadVendors}
                  disabled={loading}
                >
                  {loading ? (
                    <Loader2 className="h-4 w-4 animate-spin mr-1" />
                  ) : (
                    <Search className="h-4 w-4 mr-1" />
                  )}
                  Search
                </Button>
              </div>
            </div>

            {/* Desktop filters */}
            <div className="hidden md:flex flex-row gap-3 mt-2 items-center w-full">
              <div className="w-full sm:w-60">
                <Select value={selectedRating} onValueChange={setSelectedRating}>
                  <SelectTrigger className="h-9 rounded-lg border border-gray-200 focus:border-primary text-sm w-full text-gray-900">
                    <SelectValue placeholder="All Ratings" className="text-gray-900" />
                  </SelectTrigger>
                  <SelectContent className="rounded-lg">
                    <SelectItem value="all" className="text-gray-900">All Ratings</SelectItem>
                    <SelectItem value="4.5" className="text-gray-900">4.5+ Stars</SelectItem>
                    <SelectItem value="4.0" className="text-gray-900">4.0+ Stars</SelectItem>
                    <SelectItem value="3.5" className="text-gray-900">3.5+ Stars</SelectItem>
                    <SelectItem value="3.0" className="text-gray-900">3.0+ Stars</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="w-60">
                <Select value={selectedPriceRange} onValueChange={setSelectedPriceRange}>
                  <SelectTrigger className="h-9 rounded-lg border border-gray-200 focus:border-primary text-sm w-full text-gray-900">
                    <SelectValue placeholder="Prices" className="text-gray-900" />
                  </SelectTrigger>
                  <SelectContent className="rounded-lg">
                    <SelectItem value="all" className="text-gray-900">All Prices</SelectItem>
                    <SelectItem value="budget" className="text-gray-900">Budget</SelectItem>
                    <SelectItem value="mid" className="text-gray-900">Mid-Range</SelectItem>
                    <SelectItem value="premium" className="text-gray-900">Premium</SelectItem>
                    <SelectItem value="luxury" className="text-gray-900">Luxury</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="w-60">
                <Select value={sortBy} onValueChange={setSortBy}>
                  <SelectTrigger className="h-9 rounded-lg border border-gray-200 focus:border-primary text-sm w-full text-gray-900">
                    <SelectValue placeholder="Sort By" className="text-gray-900" />
                  </SelectTrigger>
                  <SelectContent className="rounded-lg">
                    <SelectItem value="default" className="text-gray-900">Default</SelectItem>
                    <SelectItem value="rating" className="text-gray-900">Highest Rated</SelectItem>
                    <SelectItem value="price-low" className="text-gray-900">Price: Low-High</SelectItem>
                    <SelectItem value="price-high" className="text-gray-900">Price: High-Low</SelectItem>
                    <SelectItem value="experience" className="text-gray-900">Most Experienced</SelectItem>
                    <SelectItem value="name" className="text-gray-900">Name A-Z</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Mobile-only simplified filters - 3 per row */}
            <div className="md:hidden mt-3">
              <div className="grid grid-cols-3 gap-2">
                <div>
                  <Select value={selectedRating} onValueChange={setSelectedRating}>
                    <SelectTrigger className="h-10 rounded-lg border border-gray-200 focus:border-primary text-xs w-full text-gray-900">
                      <SelectValue placeholder="Rating" className="text-gray-900" />
                    </SelectTrigger>
                    <SelectContent className="rounded-lg">
                      <SelectItem value="all" className="text-gray-900">All Ratings</SelectItem>
                      <SelectItem value="4.5" className="text-gray-900">4.5+ Stars</SelectItem>
                      <SelectItem value="4.0" className="text-gray-900">4.0+ Stars</SelectItem>
                      <SelectItem value="3.5" className="text-gray-900">3.5+ Stars</SelectItem>
                      <SelectItem value="3.0" className="text-gray-900">3.0+ Stars</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Select value={selectedPriceRange} onValueChange={setSelectedPriceRange}>
                    <SelectTrigger className="h-10 rounded-lg border border-gray-200 focus:border-primary text-xs w-full text-gray-900">
                      <SelectValue placeholder="Price" className="text-gray-900" />
                    </SelectTrigger>
                    <SelectContent className="rounded-lg">
                      <SelectItem value="all" className="text-gray-900">All Prices</SelectItem>
                      <SelectItem value="budget" className="text-gray-900">Budget</SelectItem>
                      <SelectItem value="mid" className="text-gray-900">Mid-Range</SelectItem>
                      <SelectItem value="premium" className="text-gray-900">Premium</SelectItem>
                      <SelectItem value="luxury" className="text-gray-900">Luxury</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Select value={sortBy} onValueChange={setSortBy}>
                    <SelectTrigger className="h-10 rounded-lg border border-gray-200 focus:border-primary text-xs w-full text-gray-900">
                      <SelectValue placeholder="Sort" className="text-gray-900" />
                    </SelectTrigger>
                    <SelectContent className="rounded-lg">
                      <SelectItem value="default" className="text-gray-900">Default</SelectItem>
                      <SelectItem value="rating" className="text-gray-900">Highest Rated</SelectItem>
                      <SelectItem value="price-low" className="text-gray-900">Price: Low-High</SelectItem>
                      <SelectItem value="price-high" className="text-gray-900">Price: High-Low</SelectItem>
                      <SelectItem value="experience" className="text-gray-900">Most Experienced</SelectItem>
                      <SelectItem value="name" className="text-gray-900">Name A-Z</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>

          </div>
        </div>
      </section>

      {/* Active Filters Summary */}
      {hasActiveFilters() && (
        <section className="py-3 md:py-4 bg-gray-50">
          <div className="container mx-auto px-4">
            <div className="flex flex-wrap items-center gap-1 md:gap-2">
              <span className="text-xs md:text-sm font-medium text-gray-700">Active Filters:</span>
              {searchTerm && (
                <Badge variant="secondary" className="rounded-full text-xs px-2 py-1 h-6">
                  Search: "{searchTerm}"
                  <button
                    onClick={() => setSearchTerm("")}
                    className="ml-1 hover:text-red-600"
                  >
                    ×
                  </button>
                </Badge>
              )}
              {selectedCity && (
                <Badge variant="secondary" className="rounded-full text-xs px-2 py-1 h-6">
                  City: {selectedCity}
                  <button
                    onClick={() => setSelectedCity("")}
                    className="ml-1 hover:text-red-600"
                  >
                    ×
                  </button>
                </Badge>
              )}
              {selectedCategory && selectedCategory !== "all" && (
                <Badge variant="secondary" className="rounded-full text-xs px-2 py-1 h-6">
                  Category: {categories.find(c => c.value === selectedCategory)?.name}
                  <button
                    onClick={() => setSelectedCategory("all")}
                    className="ml-1 hover:text-red-600"
                  >
                    ×
                  </button>
                </Badge>
              )}
              {selectedRating !== "all" && (
                <Badge variant="secondary" className="rounded-full text-xs px-2 py-1 h-6">
                  Rating: {selectedRating}
                  <button
                    onClick={() => setSelectedRating("all")}
                    className="ml-1 hover:text-red-600"
                  >
                    ×
                  </button>
                </Badge>
              )}
              {selectedPriceRange !== "all" && (
                <Badge variant="secondary" className="rounded-full text-xs px-2 py-1 h-6">
                  Price: {selectedPriceRange === "budget" ? "Budget" :
                          selectedPriceRange === "mid-range" ? "Mid-range" : "Premium"}
                  <button
                    onClick={() => setSelectedPriceRange("all")}
                    className="ml-1 hover:text-red-600"
                  >
                    ×
                  </button>
                </Badge>
              )}
              {selectedExperience !== "all" && (
                <Badge variant="secondary" className="rounded-full text-xs px-2 py-1 h-6">
                  Experience: {selectedExperience === "1-3" ? "1-3 Years" :
                              selectedExperience === "4-7" ? "4-7 Years" : "8+ Years"}
                  <button
                    onClick={() => setSelectedExperience("all")}
                    className="ml-1 hover:text-red-600"
                  >
                    ×
                  </button>
                </Badge>
              )}
              {selectedAvailability !== "all" && (
                <Badge variant="secondary" className="rounded-full text-xs px-2 py-1 h-6">
                  {selectedAvailability === "available" ? "Available" : "Busy"}
                  <button
                    onClick={() => setSelectedAvailability("all")}
                    className="ml-1 hover:text-red-600"
                  >
                    ×
                  </button>
                </Badge>
              )}
              <Button
                variant="ghost"
                size="sm"
                onClick={clearAllFilters}
                className="text-red-600 hover:text-red-700 hover:bg-red-50 text-xs px-2 py-1 h-6"
              >
                Clear All
              </Button>
            </div>
          </div>
        </section>
      )}

      {/* Vendors Grid */}
      <section className="py-8 md:py-16">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 md:mb-10 gap-4">
            <div>
              <h2 className="text-2xl font-bold">
                {selectedCategory && selectedCategory !== "all"
                  ? `${categories.find(c => c.value === selectedCategory)?.name || 'Category'} Vendors`
                  : 'All Vendors'
                } ({filteredVendors.length})
              </h2>
              {selectedCategory && selectedCategory !== "all" && (
                <p className="text-gray-600 mt-1">
                  Showing vendors in {categories.find(c => c.value === selectedCategory)?.name} category
                </p>
              )}
            </div>

          </div>

          {/* Error State */}
          {error && (
            <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-6 text-sm md:text-base">
              {error}
              <button
                onClick={loadVendors}
                className="float-right text-red-500 hover:text-red-700 underline text-xs md:text-sm"
              >
                Retry
              </button>
            </div>
          )}

          {/* Loading State */}
          {loading ? (
            <div className="flex items-center justify-center min-h-[300px] md:min-h-[400px]">
              <div className="text-center">
                <Loader2 className="h-6 w-6 md:h-8 md:w-8 animate-spin mx-auto mb-3 md:mb-4" />
                <p className="text-gray-600 text-sm md:text-base">Loading vendors...</p>
              </div>
            </div>
          ) : filteredVendors.length === 0 ? (
            <div className="text-center py-12">
              <div className="bg-gray-50 rounded-lg p-8">
                <h3 className="text-lg font-medium text-gray-900 mb-2">No vendors found</h3>
                <p className="text-gray-600 mb-4">
                  {selectedCategory && selectedCategory !== "all"
                    ? `No vendors found in ${categories.find(c => c.value === selectedCategory)?.name} category.`
                    : 'No vendors match your search criteria.'
                  }
                </p>
                {(selectedCategory && selectedCategory !== "all" || searchTerm || selectedCity) && (
                  <Button
                    onClick={() => {
                      setSelectedCategory("all");
                      setSearchTerm("");
                      setSelectedCity("");
                    }}
                    variant="outline"
                    className="text-sm md:text-base"
                  >
                    Clear Filters
                  </Button>
                )}
              </div>
            </div>
          ) : (
            <div className="grid grid-cols-2 md:grid-cols-2 lg:grid-cols-3 gap-2 md:gap-8">
              {paginatedVendors.map((vendor: VendorResponse) => (
              <Link
                key={vendor.id}
                href={`/vendors/${vendor.id}`}
                className="group block overflow-hidden rounded-2xl md:rounded-3xl shadow-lg hover:shadow-2xl transition-all duration-300 border-0 bg-white hover:-translate-y-1 md:hover:-translate-y-2 focus:outline-none focus:ring-2 focus:ring-primary scale-100"
                style={{ width: '100%', margin: '0 auto' }}
                tabIndex={0}
                aria-label={`View details for ${vendor.name}`}
              >
                <Card className="border-0 bg-white">
                  <div className="relative overflow-hidden">
                    <img
                      src={getVendorImage(vendor)}
                      alt={vendor.name || "Vendor"}
                      className="w-full h-48 md:h-56 object-cover transition-transform duration-300 group-hover:scale-110"
                      onError={() => handleImageError(vendor.id!)}
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/50 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    {/* Badges removed */}
                    {/* Action Buttons removed */}
                    {/* Quick Stats Overlay removed */}
                  </div>
                  <CardContent className="p-4 md:p-6 flex flex-col h-full">
                    <div className="mb-3 md:mb-4">
                      <div className="flex items-start justify-between mb-2">
                        <div className="flex-1">
                          <h3 className="font-bold text-lg md:text-xl text-gray-900 mb-1 line-clamp-1">{vendor.name}</h3>
                          <p className="text-primary font-medium text-xs md:text-sm uppercase tracking-wide">{vendor.category}</p>
                        </div>
                      </div>
                      <div className="flex items-center text-gray-500 text-xs md:text-sm mb-2 md:mb-3">
                        <MapPin className="h-3 w-3 md:h-4 md:w-4 mr-1 text-primary" />
                        <span>{vendor.city}, {vendor.state}</span>
                      </div>
                      <div className="flex items-center justify-between text-xs md:text-sm mb-3 md:mb-4">
                        <span className="font-bold text-lg md:text-xl text-primary">{vendor.priceRange || "Contact for pricing"}</span>
                        <span className="flex items-center gap-1 bg-yellow-50 px-2 py-1 rounded-full ml-2">
                          <Star className="h-3 w-3 md:h-4 md:w-4 fill-yellow-400 text-yellow-400" />
                          <span className="text-xs md:text-sm font-semibold text-gray-900">{vendor.rating || "N/A"}</span>
                        </span>
                      </div>
                    </div>
                    {/* Buttons removed */}
                  </CardContent>
                </Card>
              </Link>
            ))}
            </div>
          )}
          {/* Traditional Pagination */}
          {filteredVendors.length > 0 && (
            <div className="flex flex-col items-center mt-16 space-y-4">
              {/* Results info */}
              <div className="text-xs md:text-sm text-gray-600 text-center">
                Showing {startIndex + 1}-{Math.min(endIndex, filteredVendors.length)} of {filteredVendors.length} vendors
                {hasMoreData && (
                  <span className="ml-2 text-primary">
                    ({vendors.length} loaded from server)
                  </span>
                )}
              </div>

              {/* Pagination controls */}
              {totalPages > 1 ? (
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  onClick={goToPrevious}
                  disabled={currentPage === 1}
                  className="rounded-full px-3 md:px-4 text-xs md:text-sm"
                >
                  Previous
                </Button>

                {getPageNumbers().map((page) => (
                  <Button
                    key={page}
                    variant={currentPage === page ? "default" : "outline"}
                    onClick={() => goToPage(page)}
                    className={`rounded-full px-3 md:px-4 text-xs md:text-sm ${
                      currentPage === page
                        ? "bg-primary text-white shadow"
                        : "hover:bg-gray-100"
                    }`}
                  >
                    {page}
                  </Button>
                ))}

                <Button
                  variant="outline"
                  onClick={goToNext}
                  disabled={currentPage === totalPages}
                  className="rounded-full px-3 md:px-4 text-xs md:text-sm"
                >
                  Next
                </Button>
              </div>
              ) : (
                <div className="text-center py-4">
                  <span className="text-sm text-gray-500">
                    Showing all {filteredVendors.length} vendors
                  </span>
                </div>
              )}

              {/* Load More Server Data Button */}
              {showLoadMore && (
                <Button
                  onClick={loadMoreVendors}
                  disabled={loading}
                  variant="outline"
                  className="mt-4 px-4 md:px-6 py-2 text-xs md:text-sm"
                >
                  {loading ? (
                    <>
                      <div className="animate-spin rounded-full h-3 w-3 md:h-4 md:w-4 border-b-2 border-primary mr-2"></div>
                      Loading more...
                    </>
                  ) : (
                    'Load More from Server'
                  )}
                </Button>
              )}
            </div>
          )}
        </div>
      </section>
        </div>
      </LayoutWrapper>
    </>
  );
}
