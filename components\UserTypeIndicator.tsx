"use client"

import { useAuth } from '@/contexts/AuthContext';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Building2, User, Crown, Shield, Settings } from 'lucide-react';
import AuthRoutingService from '@/lib/services/authRouting';
import Link from 'next/link';

interface UserTypeIndicatorProps {
  showSwitchOption?: boolean;
  className?: string;
}

export default function UserTypeIndicator({ 
  showSwitchOption = false, 
  className = "" 
}: UserTypeIndicatorProps) {
  const { userType, userProfile, isAuthenticated } = useAuth();

  if (!isAuthenticated) {
    return null;
  }

  const getUserTypeIcon = () => {
    switch (userType) {
      case 'super_admin':
        return <Crown className="w-4 h-4" />;
      case 'admin':
        return <Shield className="w-4 h-4" />;
      case 'vendor':
        return <Building2 className="w-4 h-4" />;
      case 'customer':
        return <User className="w-4 h-4" />;
      default:
        return <User className="w-4 h-4" />;
    }
  };

  const getUserTypeBadge = () => {
    switch (userType) {
      case 'super_admin':
        return (
          <Badge variant="secondary" className="bg-purple-100 text-purple-800 border-purple-200">
            <Crown className="w-3 h-3 mr-1" />
            Super Administrator
          </Badge>
        );
      case 'admin':
        return (
          <Badge variant="secondary" className="bg-orange-100 text-orange-800 border-orange-200">
            <Shield className="w-3 h-3 mr-1" />
            Administrator
          </Badge>
        );
      case 'vendor':
        return (
          <Badge variant="secondary" className="bg-primary/10 text-primary border-primary/20">
            <Building2 className="w-3 h-3 mr-1" />
            Business Account
          </Badge>
        );
      case 'customer':
        return (
          <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
            <User className="w-3 h-3 mr-1" />
            Customer Account
          </Badge>
        );
      default:
        return (
          <Badge variant="outline">
            <User className="w-3 h-3 mr-1" />
            Account
          </Badge>
        );
    }
  };

  const getBusinessInfo = () => {
    if (userType === 'vendor' && userProfile?.businessInfo?.businessName) {
      return (
        <div className="text-xs text-gray-600 mt-1">
          {userProfile.businessInfo.businessName}
        </div>
      );
    }
    return null;
  };

  const getDashboardLink = () => {
    return AuthRoutingService.getDashboardRoute(userType, userProfile);
  };

  return (
    <div className={`flex items-center gap-2 ${className}`}>
      {/* User Type Badge */}
      <Link href={getDashboardLink()}>
        <div className="cursor-pointer hover:opacity-80 transition-opacity">
          {getUserTypeBadge()}
          {getBusinessInfo()}
        </div>
      </Link>

      {/* Switch Account Type Option */}
      {showSwitchOption && (
        <div className="flex items-center gap-1">
          {userType === 'customer' && (
            <Link href="/vendor-login">
              <Button
                variant="ghost"
                size="sm"
                className="text-xs h-7 px-2 text-primary hover:bg-primary/10"
              >
                <Building2 className="w-3 h-3 mr-1" />
                Switch to Business
              </Button>
            </Link>
          )}
          {userType === 'vendor' && (
            <Link href="/dashboard">
              <Button
                variant="ghost"
                size="sm"
                className="text-xs h-7 px-2 text-blue-600 hover:bg-blue-50"
              >
                <User className="w-3 h-3 mr-1" />
                Customer View
              </Button>
            </Link>
          )}
          {(userType === 'admin' || userType === 'super_admin') && (
            <>
              <Link href="/dashboard">
                <Button
                  variant="ghost"
                  size="sm"
                  className="text-xs h-7 px-2 text-blue-600 hover:bg-blue-50"
                >
                  <User className="w-3 h-3 mr-1" />
                  Customer View
                </Button>
              </Link>
              <Link href="/dashboard/vendor">
                <Button
                  variant="ghost"
                  size="sm"
                  className="text-xs h-7 px-2 text-primary hover:bg-primary/10"
                >
                  <Building2 className="w-3 h-3 mr-1" />
                  Business View
                </Button>
              </Link>
            </>
          )}
        </div>
      )}
    </div>
  );
}

// Compact version for mobile or small spaces
export function CompactUserTypeIndicator() {
  const { userType, isAuthenticated } = useAuth();

  if (!isAuthenticated) {
    return null;
  }

  const getCompactDisplay = () => {
    switch (userType) {
      case 'super_admin':
        return (
          <div className="flex items-center gap-1 text-purple-600">
            <Crown className="w-4 h-4" />
            <span className="text-xs font-medium">Super Admin</span>
          </div>
        );
      case 'admin':
        return (
          <div className="flex items-center gap-1 text-orange-600">
            <Shield className="w-4 h-4" />
            <span className="text-xs font-medium">Admin</span>
          </div>
        );
      case 'vendor':
        return (
          <div className="flex items-center gap-1 text-primary">
            <Building2 className="w-4 h-4" />
            <span className="text-xs font-medium">Business</span>
          </div>
        );
      default:
        return (
          <div className="flex items-center gap-1 text-blue-600">
            <User className="w-4 h-4" />
            <span className="text-xs font-medium">Customer</span>
          </div>
        );
    }
  };

  return (
    <div className="flex items-center">
      {getCompactDisplay()}
    </div>
  );
}

// User type selector for switching between account types
export function UserTypeSwitcher() {
  const { userType, userProfile } = useAuth();

  return (
    <div className="flex items-center gap-2 p-3 bg-gray-50 rounded-lg">
      <div className="text-sm font-medium text-gray-700">Account Type:</div>
      <div className="flex items-center gap-2 flex-wrap">
        <Link href="/dashboard">
          <Button
            variant={userType === 'customer' ? 'default' : 'outline'}
            size="sm"
            className="h-8"
          >
            <User className="w-3 h-3 mr-1" />
            Customer
          </Button>
        </Link>
        <Link href="/dashboard/vendor">
          <Button
            variant={userType === 'vendor' ? 'default' : 'outline'}
            size="sm"
            className="h-8"
          >
            <Building2 className="w-3 h-3 mr-1" />
            Business
          </Button>
        </Link>
        {(userType === 'admin' || userType === 'super_admin') && (
          <>
            <Link href="/dashboard/admin">
              <Button
                variant={userType === 'admin' ? 'default' : 'outline'}
                size="sm"
                className="h-8"
              >
                <Shield className="w-3 h-3 mr-1" />
                Admin
              </Button>
            </Link>
            {userType === 'super_admin' && (
              <Link href="/dashboard/super-admin">
                <Button
                  variant="default"
                  size="sm"
                  className="h-8 bg-purple-600 hover:bg-purple-700"
                >
                  <Crown className="w-3 h-3 mr-1" />
                  Super Admin
                </Button>
              </Link>
            )}
          </>
        )}
      </div>
    </div>
  );
}

// Welcome message component with user type context
export function UserTypeWelcome() {
  const { userType, userProfile } = useAuth();

  const welcomeMessage = AuthRoutingService.getWelcomeMessage(userType, userProfile);

  const getWelcomeIcon = () => {
    switch (userType) {
      case 'super_admin':
        return <Crown className="w-6 h-6 text-purple-600" />;
      case 'admin':
        return <Shield className="w-6 h-6 text-orange-600" />;
      case 'vendor':
        return <Building2 className="w-6 h-6 text-primary" />;
      default:
        return <User className="w-6 h-6 text-blue-600" />;
    }
  };

  const getGradientClass = () => {
    switch (userType) {
      case 'super_admin':
        return 'bg-gradient-to-r from-purple-50 to-purple-100';
      case 'admin':
        return 'bg-gradient-to-r from-orange-50 to-orange-100';
      case 'vendor':
        return 'bg-gradient-to-r from-primary/5 to-secondary/5';
      default:
        return 'bg-gradient-to-r from-blue-50 to-blue-100';
    }
  };

  return (
    <div className={`flex items-center gap-3 p-4 rounded-lg ${getGradientClass()}`}>
      <div className="p-2 bg-white rounded-full shadow-sm">
        {getWelcomeIcon()}
      </div>
      <div>
        <h2 className="text-lg font-semibold text-gray-900">{welcomeMessage}</h2>
        <p className="text-sm text-gray-600">
          {AuthRoutingService.getUserTypeDisplayName(userType)}
        </p>
      </div>
    </div>
  );
}
