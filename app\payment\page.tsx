declare global {
  interface Window {
    Razorpay?: any;
  }
}

'use client'
import { useCart } from "@/components/ClientRoot";
import { Button } from "@/components/ui/button";
import { useEffect } from "react";
import { showToast, toastMessages } from '@/lib/toast';

export default function PaymentPage() {
  const { cart, clearCart } = useCart();
  const total = cart.reduce((sum, item) => sum + item.price * item.quantity, 0);

  // Razorpay script loader
  useEffect(() => {
    if (!window.Razorpay) {
      const script = document.createElement("script");
      script.src = "https://checkout.razorpay.com/v1/checkout.js";
      script.async = true;
      document.body.appendChild(script);
    }
  }, []);

  const handlePayment = () => {
    if (!window.Razorpay) {
      showToast.error("Razorpay SDK not loaded. Please refresh the page and try again.");
      return;
    }
    const options = {
      key: "rzp_test_YourKeyHere", // Replace with your Razorpay key
      amount: total * 100, // in paise
      currency: "INR",
      name: "BookmyFestive",
      description: "Wedding Shop Payment",
      image: "/logo.svg",
      handler: function (response: { razorpay_payment_id: string }) {
        showToast.success("Payment successful! Payment ID: " + response.razorpay_payment_id);
        clearCart();
        setTimeout(() => {
          window.location.href = "/shop";
        }, 2000); // Give time for toast to show
      },
      prefill: {
        name: "",
        email: "",
        contact: "",
      },
      theme: {
        color: "#610f13",
      },
    };
    const rzp = new window.Razorpay(options);
    rzp.open();
  };

  return (
    <div className="min-h-screen bg-white flex items-center justify-center">
      <div className="max-w-md w-full p-8 border rounded-lg shadow-lg">
        <h1 className="text-2xl font-bold mb-6 text-center">Payment</h1>
        <div className="text-lg mb-4 text-center">Total Amount: <span className="font-bold text-primary">₹{total.toLocaleString()}</span></div>
        <Button className="w-full bg-primary hover:bg-primary/90 text-lg py-4" onClick={handlePayment} disabled={cart.length === 0}>
          Pay with Razorpay
        </Button>
        <div className="text-xs text-gray-500 mt-4 text-center">
          (This is a demo. Replace the Razorpay key in code for real payments.)
        </div>
      </div>
    </div>
  );
} 