import React from 'react';
import { Platform } from 'react-native';

interface WebSafeWrapperProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
  webOnly?: boolean;
  mobileOnly?: boolean;
}

/**
 * Wrapper component that safely renders children based on platform
 * Prevents web-specific components from causing errors in React Native
 */
const WebSafeWrapper: React.FC<WebSafeWrapperProps> = ({ 
  children, 
  fallback = null, 
  webOnly = false, 
  mobileOnly = false 
}) => {
  // If webOnly is true, only render on web platforms
  if (webOnly && Platform.OS !== 'web') {
    return <>{fallback}</>;
  }

  // If mobileOnly is true, only render on mobile platforms
  if (mobileOnly && Platform.OS === 'web') {
    return <>{fallback}</>;
  }
  
  // Try to render children, but catch any platform-specific errors
  try {
    return <>{children}</>;
  } catch (error) {
    console.warn('WebSafeWrapper caught platform-specific error:', error);
    return <>{fallback}</>;
  }
};

export default WebSafeWrapper;
