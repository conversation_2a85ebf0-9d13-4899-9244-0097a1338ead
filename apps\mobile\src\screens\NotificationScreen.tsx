import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Alert,
  RefreshControl,
  Switch,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { useTheme } from '../providers/ThemeProvider';
import { useNotifications, NotificationData } from '../providers/NotificationProvider';
import EmptyState from '../components/EmptyState';

export default function NotificationScreen() {
  const navigation = useNavigation();
  const { theme } = useTheme();
  const {
    notifications,
    unreadCount,
    isPermissionGranted,
    markAsRead,
    markAllAsRead,
    clearNotification,
    clearAllNotifications,
    sendTestNotification,
    requestPermissions,
  } = useNotifications();

  const [refreshing, setRefreshing] = useState(false);
  const [filterType, setFilterType] = useState<'all' | 'unread'>('all');

  const onRefresh = async () => {
    setRefreshing(true);
    // Simulate refresh delay
    setTimeout(() => setRefreshing(false), 1000);
  };

  const filteredNotifications = notifications.filter(notification => {
    if (filterType === 'unread') {
      return !notification.read;
    }
    return true;
  });

  const getNotificationIcon = (type: NotificationData['type']) => {
    switch (type) {
      case 'order':
        return 'bag-outline';
      case 'booking':
        return 'calendar-outline';
      case 'message':
        return 'chatbubble-outline';
      case 'payment':
        return 'card-outline';
      case 'promotion':
        return 'gift-outline';
      case 'wedding_reminder':
        return 'heart-outline';
      case 'system':
        return 'settings-outline';
      default:
        return 'notifications-outline';
    }
  };

  const getNotificationColor = (type: NotificationData['type'], priority: NotificationData['priority']) => {
    if (priority === 'high') return theme.colors.error;
    
    switch (type) {
      case 'order':
        return theme.colors.primary;
      case 'booking':
        return theme.colors.success;
      case 'message':
        return theme.colors.info;
      case 'payment':
        return theme.colors.warning;
      case 'promotion':
        return theme.colors.secondary;
      case 'wedding_reminder':
        return '#FF69B4';
      default:
        return theme.colors.textSecondary;
    }
  };

  const formatTimestamp = (timestamp: number) => {
    const now = Date.now();
    const diff = now - timestamp;
    const minutes = Math.floor(diff / (1000 * 60));
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));

    if (minutes < 1) return 'Just now';
    if (minutes < 60) return `${minutes}m ago`;
    if (hours < 24) return `${hours}h ago`;
    if (days < 7) return `${days}d ago`;
    
    return new Date(timestamp).toLocaleDateString();
  };

  const handleNotificationPress = (notification: NotificationData) => {
    // Mark as read
    if (!notification.read) {
      markAsRead(notification.id);
    }

    // Handle navigation based on notification type
    const { data } = notification;
    switch (notification.type) {
      case 'order':
        if (data?.orderId) {
          navigation.navigate('OrderDetail' as never, { orderId: data.orderId } as never);
        }
        break;
      case 'booking':
        if (data?.bookingId) {
          navigation.navigate('BookingDetail' as never, { bookingId: data.bookingId } as never);
        }
        break;
      case 'message':
        if (data?.chatId) {
          navigation.navigate('Chat' as never, { chatId: data.chatId } as never);
        }
        break;
      case 'payment':
        if (data?.orderId) {
          navigation.navigate('OrderDetail' as never, { orderId: data.orderId } as never);
        }
        break;
      default:
        // For other types, just mark as read
        break;
    }
  };

  const handleClearAll = () => {
    Alert.alert(
      'Clear All Notifications',
      'Are you sure you want to clear all notifications? This action cannot be undone.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Clear All',
          style: 'destructive',
          onPress: clearAllNotifications,
        },
      ]
    );
  };

  const handleRequestPermissions = async () => {
    const granted = await requestPermissions();
    if (granted) {
      Alert.alert('Success', 'Notification permissions granted!');
    } else {
      Alert.alert('Permission Denied', 'Please enable notifications in your device settings.');
    }
  };

  const renderNotificationItem = ({ item: notification }: { item: NotificationData }) => (
    <TouchableOpacity
      style={[
        styles.notificationCard,
        { backgroundColor: theme.colors.surface },
        !notification.read && { borderLeftWidth: 4, borderLeftColor: theme.colors.primary }
      ]}
      onPress={() => handleNotificationPress(notification)}
    >
      <View style={styles.notificationHeader}>
        <View style={[
          styles.iconContainer,
          { backgroundColor: getNotificationColor(notification.type, notification.priority) + '20' }
        ]}>
          <Ionicons
            name={getNotificationIcon(notification.type) as any}
            size={20}
            color={getNotificationColor(notification.type, notification.priority)}
          />
        </View>
        
        <View style={styles.notificationContent}>
          <Text style={[
            styles.notificationTitle,
            { color: theme.colors.text },
            !notification.read && { fontWeight: 'bold' }
          ]}>
            {notification.title}
          </Text>
          <Text style={[
            styles.notificationBody,
            { color: theme.colors.textSecondary }
          ]} numberOfLines={2}>
            {notification.body}
          </Text>
          <Text style={[styles.notificationTime, { color: theme.colors.textSecondary }]}>
            {formatTimestamp(notification.timestamp)}
          </Text>
        </View>

        <View style={styles.notificationActions}>
          {notification.priority === 'high' && (
            <View style={[styles.priorityBadge, { backgroundColor: theme.colors.error }]}>
              <Text style={styles.priorityText}>!</Text>
            </View>
          )}
          
          <TouchableOpacity
            style={styles.deleteButton}
            onPress={() => clearNotification(notification.id)}
          >
            <Ionicons name="close-outline" size={16} color={theme.colors.textSecondary} />
          </TouchableOpacity>
        </View>
      </View>
    </TouchableOpacity>
  );

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {/* Header */}
      <View style={[styles.header, { backgroundColor: theme.colors.surface }]}>
        <View style={styles.headerContent}>
          <Text style={[styles.headerTitle, { color: theme.colors.text }]}>
            Notifications
          </Text>
          {unreadCount > 0 && (
            <View style={[styles.unreadBadge, { backgroundColor: theme.colors.primary }]}>
              <Text style={styles.unreadText}>{unreadCount}</Text>
            </View>
          )}
        </View>

        <View style={styles.headerActions}>
          {notifications.length > 0 && (
            <>
              <TouchableOpacity
                style={styles.headerButton}
                onPress={markAllAsRead}
              >
                <Text style={[styles.headerButtonText, { color: theme.colors.primary }]}>
                  Mark All Read
                </Text>
              </TouchableOpacity>
              
              <TouchableOpacity
                style={styles.headerButton}
                onPress={handleClearAll}
              >
                <Ionicons name="trash-outline" size={20} color={theme.colors.error} />
              </TouchableOpacity>
            </>
          )}
        </View>
      </View>

      {/* Permission Request */}
      {!isPermissionGranted && (
        <View style={[styles.permissionCard, { backgroundColor: theme.colors.warning + '20' }]}>
          <Ionicons name="notifications-off-outline" size={24} color={theme.colors.warning} />
          <View style={styles.permissionContent}>
            <Text style={[styles.permissionTitle, { color: theme.colors.text }]}>
              Enable Notifications
            </Text>
            <Text style={[styles.permissionMessage, { color: theme.colors.textSecondary }]}>
              Get notified about order updates, bookings, and important messages.
            </Text>
          </View>
          <TouchableOpacity
            style={[styles.permissionButton, { backgroundColor: theme.colors.primary }]}
            onPress={handleRequestPermissions}
          >
            <Text style={styles.permissionButtonText}>Enable</Text>
          </TouchableOpacity>
        </View>
      )}

      {/* Filter Tabs */}
      <View style={[styles.filterContainer, { backgroundColor: theme.colors.surface }]}>
        <TouchableOpacity
          style={[
            styles.filterTab,
            filterType === 'all' && { backgroundColor: theme.colors.primary + '20' }
          ]}
          onPress={() => setFilterType('all')}
        >
          <Text style={[
            styles.filterText,
            { color: filterType === 'all' ? theme.colors.primary : theme.colors.textSecondary }
          ]}>
            All ({notifications.length})
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[
            styles.filterTab,
            filterType === 'unread' && { backgroundColor: theme.colors.primary + '20' }
          ]}
          onPress={() => setFilterType('unread')}
        >
          <Text style={[
            styles.filterText,
            { color: filterType === 'unread' ? theme.colors.primary : theme.colors.textSecondary }
          ]}>
            Unread ({unreadCount})
          </Text>
        </TouchableOpacity>

        {__DEV__ && (
          <TouchableOpacity
            style={[styles.filterTab, { backgroundColor: theme.colors.secondary + '20' }]}
            onPress={sendTestNotification}
          >
            <Text style={[styles.filterText, { color: theme.colors.secondary }]}>
              Test
            </Text>
          </TouchableOpacity>
        )}
      </View>

      {/* Notifications List */}
      {filteredNotifications.length === 0 ? (
        <EmptyState
          icon="notifications-outline"
          title={filterType === 'unread' ? 'No unread notifications' : 'No notifications'}
          message={filterType === 'unread' 
            ? 'All caught up! No unread notifications.' 
            : 'You\'ll see your notifications here when you receive them.'
          }
          fullScreen
        />
      ) : (
        <FlatList
          data={filteredNotifications}
          renderItem={renderNotificationItem}
          keyExtractor={(item) => item.id}
          contentContainerStyle={styles.notificationsList}
          showsVerticalScrollIndicator={false}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onRefresh}
              colors={[theme.colors.primary]}
              tintColor={theme.colors.primary}
            />
          }
        />
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    marginRight: 8,
  },
  unreadBadge: {
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 12,
    minWidth: 24,
    alignItems: 'center',
  },
  unreadText: {
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold',
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  headerButton: {
    padding: 4,
  },
  headerButtonText: {
    fontSize: 14,
    fontWeight: '600',
  },
  permissionCard: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    margin: 16,
    borderRadius: 12,
    gap: 12,
  },
  permissionContent: {
    flex: 1,
  },
  permissionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  permissionMessage: {
    fontSize: 14,
    lineHeight: 20,
  },
  permissionButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
  },
  permissionButtonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '600',
  },
  filterContainer: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    paddingVertical: 8,
    gap: 8,
  },
  filterTab: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  filterText: {
    fontSize: 14,
    fontWeight: '500',
  },
  notificationsList: {
    padding: 16,
  },
  notificationCard: {
    borderRadius: 12,
    marginBottom: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  notificationHeader: {
    flexDirection: 'row',
    padding: 16,
    alignItems: 'flex-start',
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  notificationContent: {
    flex: 1,
  },
  notificationTitle: {
    fontSize: 16,
    marginBottom: 4,
  },
  notificationBody: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 8,
  },
  notificationTime: {
    fontSize: 12,
  },
  notificationActions: {
    alignItems: 'center',
    gap: 8,
  },
  priorityBadge: {
    width: 20,
    height: 20,
    borderRadius: 10,
    alignItems: 'center',
    justifyContent: 'center',
  },
  priorityText: {
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold',
  },
  deleteButton: {
    padding: 4,
  },
});
