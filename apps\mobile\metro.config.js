const { getDefaultConfig } = require('expo/metro-config');
const path = require('path');

const config = getDefaultConfig(__dirname);

// Add resolver configuration for AWS Amplify compatibility
config.resolver.alias = {
  ...config.resolver.alias,
  'crypto': 'react-native-crypto',
  'stream': 'stream-browserify',
  'buffer': '@craftzdog/react-native-buffer',
  'uuid': 'uuid',
};

// Add node modules that should be resolved
config.resolver.nodeModulesPaths = [
  ...config.resolver.nodeModulesPaths,
  './node_modules',
];

// Allow importing from root src directory
config.watchFolders = [
  path.resolve(__dirname, '../../src'),
];

// Exclude problematic shared packages from resolution
config.resolver.blockList = [
  /packages\/shared\/.*/,
];

// Configure transformer for better compatibility
config.transformer = {
  ...config.transformer,
  minifierConfig: {
    ...config.transformer.minifierConfig,
    keep_fnames: true,
    mangle: {
      keep_fnames: true,
    },
  },
};

// Disable web platform for now to focus on mobile
config.resolver.platforms = ['ios', 'android', 'native'];

module.exports = config;
