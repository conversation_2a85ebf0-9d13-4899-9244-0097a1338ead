import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TextInput,
  TouchableOpacity,
  Alert,
  Platform,
} from 'react-native';
import { RouteProp, useRoute, useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import DateTimePicker from '@react-native-community/datetimepicker';
import { useTheme } from '../providers/ThemeProvider';
import { useAuth } from '../providers/AuthProvider';
import { AppStackParamList } from '../navigation/AppNavigator';
import { graphqlService } from '../services/graphqlService';
import LoadingSpinner from '../components/LoadingSpinner';
import ErrorMessage from '../components/ErrorMessage';

type BookingRouteProp = RouteProp<AppStackParamList, 'Booking'>;

interface BookingForm {
  eventDate: Date;
  eventTime: Date;
  guestCount: string;
  eventType: string;
  duration: string;
  specialRequests: string;
  budget: string;
  contactPreference: 'phone' | 'email' | 'whatsapp';
}

export default function BookingScreen() {
  const route = useRoute<BookingRouteProp>();
  const navigation = useNavigation();
  const { theme } = useTheme();
  const { user } = useAuth();

  const { type, id, name } = route.params;

  const [error, setError] = useState<string | null>(null);
  const [availabilityChecked, setAvailabilityChecked] = useState(false);
  
  const [formData, setFormData] = useState<BookingForm>({
    eventDate: new Date(),
    eventTime: new Date(),
    guestCount: '',
    eventType: '',
    duration: '',
    specialRequests: '',
    budget: '',
    contactPreference: 'phone',
  });
  
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [showTimePicker, setShowTimePicker] = useState(false);
  const [loading, setLoading] = useState(false);
  const [availability, setAvailability] = useState<{
    isAvailable: boolean;
    message: string;
    conflicts?: any[];
  } | null>(null);

  const eventTypes = [
    'Wedding Ceremony',
    'Reception',
    'Engagement',
    'Pre-Wedding Shoot',
    'Mehendi',
    'Sangam',
    'Other',
  ];

  const durations = [
    '2 hours',
    '4 hours',
    '6 hours',
    '8 hours',
    'Full Day',
    'Multiple Days',
  ];

  const budgetRanges = [
    'Under ₹25,000',
    '₹25,000 - ₹50,000',
    '₹50,000 - ₹1,00,000',
    '₹1,00,000 - ₹2,00,000',
    'Above ₹2,00,000',
  ];

  useEffect(() => {
    // Check availability when date/time changes
    if (formData.eventDate && formData.eventTime) {
      checkAvailability();
    }
  }, [formData.eventDate, formData.eventTime]);

  const checkAvailability = async () => {
    try {
      // Simulate availability check
      // In real app, this would call the availability service
      const isWeekend = formData.eventDate.getDay() === 0 || formData.eventDate.getDay() === 6;
      const isAvailable = Math.random() > 0.3; // 70% chance of availability
      
      setAvailability({
        isAvailable,
        message: isAvailable 
          ? 'Available for booking' 
          : isWeekend 
            ? 'This date is very popular. Limited availability.'
            : 'Not available - conflicting booking exists',
        conflicts: isAvailable ? [] : [
          {
            customerName: 'Another Customer',
            eventTime: formData.eventTime.toLocaleTimeString(),
            status: 'CONFIRMED'
          }
        ]
      });
    } catch (error) {
      console.error('Error checking availability:', error);
    }
  };

  const handleDateChange = (event: any, selectedDate?: Date) => {
    setShowDatePicker(false);
    if (selectedDate) {
      setFormData(prev => ({ ...prev, eventDate: selectedDate }));
    }
  };

  const handleTimeChange = (event: any, selectedTime?: Date) => {
    setShowTimePicker(false);
    if (selectedTime) {
      setFormData(prev => ({ ...prev, eventTime: selectedTime }));
    }
  };

  const handleSubmit = async () => {
    // Validate form
    if (!formData.guestCount || !formData.eventType || !formData.duration) {
      Alert.alert('Error', 'Please fill in all required fields');
      return;
    }

    if (availability && !availability.isAvailable) {
      Alert.alert('Error', 'Selected date/time is not available. Please choose a different slot.');
      return;
    }

    try {
      setLoading(true);

      // Create booking request data
      const bookingData = {
        userId: user?.id || '',
        vendorId: type === 'vendor' ? id : undefined,
        venueId: type === 'venue' ? id : undefined,
        eventDate: formData.eventDate.toISOString().split('T')[0],
        eventTime: formData.eventTime.toTimeString().split(' ')[0],
        guestCount: parseInt(formData.guestCount),
        eventType: formData.eventType,
        duration: formData.duration,
        specialRequests: formData.specialRequests,
        budget: formData.budget ? parseFloat(formData.budget) : undefined,
        contactPreference: formData.contactPreference,
        status: 'pending',
        createdAt: new Date().toISOString(),
      };

      // TODO: Implement actual booking creation via GraphQL
      // await graphqlService.createBooking(bookingData);
      console.log('Creating booking:', bookingData);

      // Simulate API call for now
      await new Promise(resolve => setTimeout(resolve, 2000));

      Alert.alert(
        'Booking Request Sent!',
        `Your booking request for ${name} has been sent. You will receive a confirmation shortly.`,
        [
          {
            text: 'OK',
            onPress: () => navigation.goBack(),
          },
        ]
      );
    } catch (error) {
      console.error('Error creating booking:', error);
      Alert.alert('Error', 'Failed to create booking. Please try again.');
      setError('Failed to create booking. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  if (loading && !error) {
    return <LoadingSpinner fullScreen text="Processing booking..." />;
  }

  return (
    <ScrollView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {error && (
        <ErrorMessage
          message={error}
          onRetry={() => setError(null)}
        />
      )}
      <View style={styles.content}>
        {/* Header */}
        <View style={styles.header}>
          <Text style={[styles.title, { color: theme.colors.text }]}>
            Book {type === 'vendor' ? 'Service' : 'Venue'}
          </Text>
          <Text style={[styles.subtitle, { color: theme.colors.textSecondary }]}>
            {name}
          </Text>
        </View>

        {/* Event Details */}
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
            Event Details
          </Text>

          {/* Event Date */}
          <View style={styles.inputGroup}>
            <Text style={[styles.label, { color: theme.colors.text }]}>Event Date *</Text>
            <TouchableOpacity
              style={[styles.dateButton, { backgroundColor: theme.colors.surface }]}
              onPress={() => setShowDatePicker(true)}
            >
              <Ionicons name="calendar-outline" size={20} color={theme.colors.textSecondary} />
              <Text style={[styles.dateText, { color: theme.colors.text }]}>
                {formData.eventDate.toLocaleDateString()}
              </Text>
            </TouchableOpacity>
          </View>

          {/* Event Time */}
          <View style={styles.inputGroup}>
            <Text style={[styles.label, { color: theme.colors.text }]}>Event Time *</Text>
            <TouchableOpacity
              style={[styles.dateButton, { backgroundColor: theme.colors.surface }]}
              onPress={() => setShowTimePicker(true)}
            >
              <Ionicons name="time-outline" size={20} color={theme.colors.textSecondary} />
              <Text style={[styles.dateText, { color: theme.colors.text }]}>
                {formData.eventTime.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
              </Text>
            </TouchableOpacity>
          </View>

          {/* Availability Status */}
          {availability && (
            <View style={[
              styles.availabilityContainer,
              { backgroundColor: availability.isAvailable ? theme.colors.success + '20' : theme.colors.error + '20' }
            ]}>
              <Ionicons
                name={availability.isAvailable ? 'checkmark-circle' : 'close-circle'}
                size={20}
                color={availability.isAvailable ? theme.colors.success : theme.colors.error}
              />
              <Text style={[
                styles.availabilityText,
                { color: availability.isAvailable ? theme.colors.success : theme.colors.error }
              ]}>
                {availability.message}
              </Text>
            </View>
          )}

          {/* Guest Count */}
          <View style={styles.inputGroup}>
            <Text style={[styles.label, { color: theme.colors.text }]}>Number of Guests *</Text>
            <TextInput
              style={[styles.input, { backgroundColor: theme.colors.surface, color: theme.colors.text }]}
              placeholder="e.g., 150"
              placeholderTextColor={theme.colors.textSecondary}
              value={formData.guestCount}
              onChangeText={(text) => setFormData(prev => ({ ...prev, guestCount: text }))}
              keyboardType="numeric"
            />
          </View>

          {/* Event Type */}
          <View style={styles.inputGroup}>
            <Text style={[styles.label, { color: theme.colors.text }]}>Event Type *</Text>
            <View style={styles.optionsContainer}>
              {eventTypes.map((eventType) => (
                <TouchableOpacity
                  key={eventType}
                  style={[
                    styles.optionButton,
                    {
                      backgroundColor: formData.eventType === eventType
                        ? theme.colors.primary
                        : theme.colors.surface
                    }
                  ]}
                  onPress={() => setFormData(prev => ({ ...prev, eventType }))}
                >
                  <Text style={[
                    styles.optionText,
                    {
                      color: formData.eventType === eventType
                        ? '#fff'
                        : theme.colors.text
                    }
                  ]}>
                    {eventType}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>

          {/* Duration */}
          <View style={styles.inputGroup}>
            <Text style={[styles.label, { color: theme.colors.text }]}>Duration *</Text>
            <View style={styles.optionsContainer}>
              {durations.map((duration) => (
                <TouchableOpacity
                  key={duration}
                  style={[
                    styles.optionButton,
                    {
                      backgroundColor: formData.duration === duration
                        ? theme.colors.primary
                        : theme.colors.surface
                    }
                  ]}
                  onPress={() => setFormData(prev => ({ ...prev, duration }))}
                >
                  <Text style={[
                    styles.optionText,
                    {
                      color: formData.duration === duration
                        ? '#fff'
                        : theme.colors.text
                    }
                  ]}>
                    {duration}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>

          {/* Budget */}
          <View style={styles.inputGroup}>
            <Text style={[styles.label, { color: theme.colors.text }]}>Budget Range</Text>
            <View style={styles.optionsContainer}>
              {budgetRanges.map((budget) => (
                <TouchableOpacity
                  key={budget}
                  style={[
                    styles.optionButton,
                    {
                      backgroundColor: formData.budget === budget
                        ? theme.colors.primary
                        : theme.colors.surface
                    }
                  ]}
                  onPress={() => setFormData(prev => ({ ...prev, budget }))}
                >
                  <Text style={[
                    styles.optionText,
                    {
                      color: formData.budget === budget
                        ? '#fff'
                        : theme.colors.text
                    }
                  ]}>
                    {budget}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>

          {/* Special Requests */}
          <View style={styles.inputGroup}>
            <Text style={[styles.label, { color: theme.colors.text }]}>Special Requests</Text>
            <TextInput
              style={[styles.textArea, { backgroundColor: theme.colors.surface, color: theme.colors.text }]}
              placeholder="Any special requirements or requests..."
              placeholderTextColor={theme.colors.textSecondary}
              value={formData.specialRequests}
              onChangeText={(text) => setFormData(prev => ({ ...prev, specialRequests: text }))}
              multiline
              numberOfLines={4}
            />
          </View>
        </View>

        {/* Submit Button */}
        <TouchableOpacity
          style={[
            styles.submitButton,
            {
              backgroundColor: loading || (availability && !availability.isAvailable)
                ? theme.colors.textSecondary
                : theme.colors.primary
            }
          ]}
          onPress={handleSubmit}
          disabled={loading || (availability && !availability.isAvailable)}
        >
          <Text style={styles.submitButtonText}>
            {loading ? 'Sending Request...' : 'Send Booking Request'}
          </Text>
        </TouchableOpacity>
      </View>

      {/* Date/Time Pickers */}
      {showDatePicker && (
        <DateTimePicker
          value={formData.eventDate}
          mode="date"
          display="default"
          onChange={handleDateChange}
          minimumDate={new Date()}
        />
      )}

      {showTimePicker && (
        <DateTimePicker
          value={formData.eventTime}
          mode="time"
          display="default"
          onChange={handleTimeChange}
        />
      )}
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    padding: 16,
  },
  header: {
    marginBottom: 24,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 16,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
  },
  inputGroup: {
    marginBottom: 16,
  },
  label: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 8,
  },
  input: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 8,
    fontSize: 16,
  },
  textArea: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 8,
    fontSize: 16,
    height: 100,
    textAlignVertical: 'top',
  },
  dateButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 8,
    gap: 12,
  },
  dateText: {
    fontSize: 16,
  },
  availabilityContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    gap: 8,
    marginBottom: 16,
  },
  availabilityText: {
    fontSize: 14,
    fontWeight: '500',
  },
  optionsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  optionButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
  },
  optionText: {
    fontSize: 14,
    fontWeight: '500',
  },
  submitButton: {
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: 'center',
    marginTop: 24,
  },
  submitButtonText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
  },
});
