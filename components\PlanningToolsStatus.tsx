'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { 
  CheckCircle, 
  Database, 
  Users, 
  Calculator, 
  CheckSquare,
  Cloud,
  HardDrive,
  ArrowRight,
  Loader2
} from 'lucide-react'
import { useAuth } from '@/contexts/AuthContext'
import simplePlanningService from '@/lib/services/simplePlanningService'
import ChecklistService from '@/lib/services/checklistService'

interface PlanningToolStatus {
  name: string
  icon: React.ReactNode
  status: 'database' | 'localStorage' | 'mock' | 'loading'
  itemCount: number
  lastUpdated?: string
  description: string
}

export default function PlanningToolsStatus() {
  const [mounted, setMounted] = useState(false)
  const { user } = useAuth()
  const [toolsStatus, setToolsStatus] = useState<PlanningToolStatus[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    setMounted(true)
  }, [])

  useEffect(() => {
    if (mounted) {
      checkToolsStatus()
    }
  }, [mounted, user])

  const checkToolsStatus = async () => {
    try {
      setLoading(true)
      
      const status: PlanningToolStatus[] = []

      if (user?.userId) {
        // Check database data for logged-in users
        
        // Budget data
        const budgetData = await simplePlanningService.getUserPlanningData(user.userId, 'BUDGET')
        status.push({
          name: 'Budget Planner',
          icon: <Calculator className="h-5 w-5" />,
          status: budgetData.length > 0 ? 'database' : 'localStorage',
          itemCount: budgetData.length,
          lastUpdated: budgetData[0]?.updatedAt,
          description: `${budgetData.length} budget(s) saved in your account`
        })

        // Guest List data
        const guestData = await simplePlanningService.getUserPlanningData(user.userId, 'GUEST_LIST')
        const totalGuests = guestData.reduce((sum, list) => sum + (list.data.totalGuests || 0), 0)
        status.push({
          name: 'Guest List Manager',
          icon: <Users className="h-5 w-5" />,
          status: guestData.length > 0 ? 'database' : 'localStorage',
          itemCount: totalGuests,
          lastUpdated: guestData[0]?.updatedAt,
          description: `${totalGuests} guest(s) across ${guestData.length} list(s)`
        })

        // Checklist data
        const checklistData = await ChecklistService.getChecklistData(user.userId)
        const totalItems = checklistData.reduce((sum, cat) => sum + (cat.items?.length || 0), 0)
        const completedItems = checklistData.reduce((sum, cat) => 
          sum + (cat.items?.filter(item => item.completed).length || 0), 0)
        status.push({
          name: 'Planning Checklist',
          icon: <CheckSquare className="h-5 w-5" />,
          status: 'database',
          itemCount: totalItems,
          lastUpdated: new Date().toISOString(),
          description: `${completedItems}/${totalItems} tasks completed`
        })

      } else {
        // Check localStorage for non-logged-in users
        
        // Budget data
        const budgetData = localStorage.getItem('wedding-budgets')
        const budgets = budgetData ? JSON.parse(budgetData) : []
        status.push({
          name: 'Budget Planner',
          icon: <Calculator className="h-5 w-5" />,
          status: budgets.length > 0 ? 'localStorage' : 'mock',
          itemCount: budgets.length,
          description: budgets.length > 0 ? `${budgets.length} budget(s) in local storage` : 'Using sample data'
        })

        // Guest List data
        const guestData = localStorage.getItem('wedding-guests')
        const guests = guestData ? JSON.parse(guestData) : []
        status.push({
          name: 'Guest List Manager',
          icon: <Users className="h-5 w-5" />,
          status: guests.length > 0 ? 'localStorage' : 'mock',
          itemCount: guests.length,
          description: guests.length > 0 ? `${guests.length} guest(s) in local storage` : 'Using sample data'
        })

        // Checklist data (always uses default data for non-logged-in users)
        status.push({
          name: 'Planning Checklist',
          icon: <CheckSquare className="h-5 w-5" />,
          status: 'mock',
          itemCount: 0,
          description: 'Using default checklist template'
        })
      }

      setToolsStatus(status)
    } catch (error) {
      console.error('Error checking tools status:', error)
    } finally {
      setLoading(false)
    }
  }

  const getStatusBadge = (status: PlanningToolStatus['status']) => {
    switch (status) {
      case 'database':
        return (
          <Badge className="bg-green-100 text-green-800 border-green-200">
            <Database className="h-3 w-3 mr-1" />
            Database
          </Badge>
        )
      case 'localStorage':
        return (
          <Badge className="bg-yellow-100 text-yellow-800 border-yellow-200">
            <HardDrive className="h-3 w-3 mr-1" />
            Local Storage
          </Badge>
        )
      case 'mock':
        return (
          <Badge className="bg-blue-100 text-blue-800 border-blue-200">
            <Cloud className="h-3 w-3 mr-1" />
            Sample Data
          </Badge>
        )
      case 'loading':
        return (
          <Badge className="bg-gray-100 text-gray-800 border-gray-200">
            <Loader2 className="h-3 w-3 mr-1 animate-spin" />
            Loading
          </Badge>
        )
    }
  }

  const getStatusColor = (status: PlanningToolStatus['status']) => {
    switch (status) {
      case 'database':
        return 'border-green-200 bg-green-50'
      case 'localStorage':
        return 'border-yellow-200 bg-yellow-50'
      case 'mock':
        return 'border-blue-200 bg-blue-50'
      default:
        return 'border-gray-200 bg-gray-50'
    }
  }

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Planning Tools Status</CardTitle>
          <CardDescription>Checking data storage status...</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-6 w-6 animate-spin mr-2" />
            <span>Loading status...</span>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <CheckCircle className="h-5 w-5 text-green-600" />
          Planning Tools Status
        </CardTitle>
        <CardDescription>
          Current data storage status for all planning tools
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {toolsStatus.map((tool, index) => (
          <div
            key={index}
            className={`p-4 rounded-lg border ${getStatusColor(tool.status)} transition-colors`}
          >
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center gap-3">
                {tool.icon}
                <h3 className="font-medium text-gray-900">{tool.name}</h3>
              </div>
              {getStatusBadge(tool.status)}
            </div>
            
            <p className="text-sm text-gray-600 mb-2">{tool.description}</p>
            
            {tool.itemCount > 0 && (
              <div className="flex items-center gap-4 text-xs text-gray-500">
                <span>{tool.itemCount} items</span>
                {tool.lastUpdated && (
                  <span>Updated: {new Date(tool.lastUpdated).toLocaleDateString()}</span>
                )}
              </div>
            )}
          </div>
        ))}

        {/* Action Buttons */}
        <div className="pt-4 border-t">
          {user ? (
            <div className="space-y-2">
              <p className="text-sm text-green-700 font-medium">
                ✅ You're logged in! Your data is automatically saved to your account.
              </p>
              <Button
                onClick={checkToolsStatus}
                variant="outline"
                size="sm"
                className="w-full"
              >
                Refresh Status
              </Button>
            </div>
          ) : (
            <div className="space-y-2">
              <p className="text-sm text-blue-700 font-medium">
                💡 Log in to save your planning data across devices!
              </p>
              <div className="flex items-center gap-2 text-xs text-gray-600">
                <HardDrive className="h-3 w-3" />
                <ArrowRight className="h-3 w-3" />
                <Database className="h-3 w-3" />
                <span>Local Storage → Database</span>
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
