"use client"

import { useEffect } from "react"
import { useRouter } from "next/navigation"
import GoogleSignupFlow from "@/components/auth/GoogleSignupFlow"
import GoogleAuthService from "@/lib/services/googleAuthService"

export default function GoogleSignupPage() {
  const router = useRouter()

  useEffect(() => {
    // Check if there's an active Google signup session
    const signupData = GoogleAuthService.getGoogleSignupStatus()
    
    if (!signupData) {
      // No signup session found, redirect to login
      router.push('/login')
      return
    }

    // If signup is already completed, redirect to dashboard
    if (signupData.signupStep === 'completed') {
      router.push('/dashboard')
      return
    }
  }, [router])

  const handleSignupComplete = () => {
    // This will be called when the signup flow is completed
    console.log('Google signup flow completed')
  }

  return (
    <GoogleSignupFlow onComplete={handleSignupComplete} />
  )
}
