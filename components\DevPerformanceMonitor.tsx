'use client'

import { useEffect, useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Activity, Clock, Zap, AlertTriangle } from 'lucide-react'

interface PerformanceMetrics {
  renderTime: number
  bundleSize: number
  memoryUsage: number
  hydrationTime: number
  pageLoadTime: number
}

export default function DevPerformanceMonitor() {
  const [metrics, setMetrics] = useState<PerformanceMetrics | null>(null)
  const [isVisible, setIsVisible] = useState(false)

  useEffect(() => {
    // Only show in development
    if (process.env.NODE_ENV !== 'development') return

    const measurePerformance = () => {
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
      const memory = (performance as any).memory

      const metrics: PerformanceMetrics = {
        renderTime: Math.round(performance.now()),
        bundleSize: 0, // Would need webpack stats for accurate measurement
        memoryUsage: memory ? Math.round(memory.usedJSHeapSize / 1024 / 1024) : 0,
        hydrationTime: navigation ? Math.round(navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart) : 0,
        pageLoadTime: navigation ? Math.round(navigation.loadEventEnd - navigation.navigationStart) : 0
      }

      setMetrics(metrics)
    }

    // Measure after initial render
    const timer = setTimeout(measurePerformance, 1000)

    return () => clearTimeout(timer)
  }, [])

  // Don't render in production
  if (process.env.NODE_ENV !== 'development' || !metrics) return null

  const getPerformanceStatus = (metric: keyof PerformanceMetrics, value: number) => {
    const thresholds = {
      renderTime: { good: 100, warning: 300 },
      memoryUsage: { good: 50, warning: 100 },
      hydrationTime: { good: 50, warning: 150 },
      pageLoadTime: { good: 1000, warning: 3000 }
    }

    const threshold = thresholds[metric as keyof typeof thresholds]
    if (!threshold) return 'good'

    if (value <= threshold.good) return 'good'
    if (value <= threshold.warning) return 'warning'
    return 'poor'
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'good': return 'bg-green-100 text-green-800'
      case 'warning': return 'bg-yellow-100 text-yellow-800'
      case 'poor': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'good': return <Zap className="h-4 w-4" />
      case 'warning': return <Clock className="h-4 w-4" />
      case 'poor': return <AlertTriangle className="h-4 w-4" />
      default: return <Activity className="h-4 w-4" />
    }
  }

  if (!isVisible) {
    return (
      <div className="fixed bottom-4 right-4 z-50">
        <Button
          onClick={() => setIsVisible(true)}
          size="sm"
          variant="outline"
          className="bg-white shadow-lg"
        >
          <Activity className="h-4 w-4 mr-2" />
          Performance
        </Button>
      </div>
    )
  }

  return (
    <div className="fixed bottom-4 right-4 z-50 w-80">
      <Card className="shadow-lg">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-sm font-medium flex items-center">
              <Activity className="h-4 w-4 mr-2" />
              Dev Performance
            </CardTitle>
            <Button
              onClick={() => setIsVisible(false)}
              size="sm"
              variant="ghost"
              className="h-6 w-6 p-0"
            >
              ×
            </Button>
          </div>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="grid grid-cols-2 gap-3 text-xs">
            <div>
              <div className="flex items-center justify-between">
                <span>Render Time</span>
                <Badge 
                  className={getStatusColor(getPerformanceStatus('renderTime', metrics.renderTime))}
                  variant="secondary"
                >
                  {getStatusIcon(getPerformanceStatus('renderTime', metrics.renderTime))}
                  {metrics.renderTime}ms
                </Badge>
              </div>
            </div>
            
            <div>
              <div className="flex items-center justify-between">
                <span>Memory</span>
                <Badge 
                  className={getStatusColor(getPerformanceStatus('memoryUsage', metrics.memoryUsage))}
                  variant="secondary"
                >
                  {getStatusIcon(getPerformanceStatus('memoryUsage', metrics.memoryUsage))}
                  {metrics.memoryUsage}MB
                </Badge>
              </div>
            </div>
            
            <div>
              <div className="flex items-center justify-between">
                <span>Hydration</span>
                <Badge 
                  className={getStatusColor(getPerformanceStatus('hydrationTime', metrics.hydrationTime))}
                  variant="secondary"
                >
                  {getStatusIcon(getPerformanceStatus('hydrationTime', metrics.hydrationTime))}
                  {metrics.hydrationTime}ms
                </Badge>
              </div>
            </div>
            
            <div>
              <div className="flex items-center justify-between">
                <span>Page Load</span>
                <Badge 
                  className={getStatusColor(getPerformanceStatus('pageLoadTime', metrics.pageLoadTime))}
                  variant="secondary"
                >
                  {getStatusIcon(getPerformanceStatus('pageLoadTime', metrics.pageLoadTime))}
                  {metrics.pageLoadTime}ms
                </Badge>
              </div>
            </div>
          </div>
          
          <div className="pt-2 border-t">
            <div className="flex justify-between text-xs text-gray-600">
              <span>Next.js Dev Mode</span>
              <span>Turbo: {process.env.NODE_ENV === 'development' ? '✓' : '✗'}</span>
            </div>
          </div>
          
          <div className="flex gap-2">
            <Button
              onClick={() => window.location.reload()}
              size="sm"
              variant="outline"
              className="flex-1 text-xs"
            >
              Reload
            </Button>
            <Button
              onClick={() => {
                if (typeof window !== 'undefined' && 'gc' in window) {
                  (window as any).gc()
                }
              }}
              size="sm"
              variant="outline"
              className="flex-1 text-xs"
            >
              GC
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
