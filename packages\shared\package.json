{"name": "@BookmyFestive/shared", "version": "1.0.0", "description": "Shared utilities and services for BookmyFestive", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "rm -rf dist", "lint": "eslint src --ext .ts,.tsx", "test": "jest"}, "dependencies": {"aws-amplify": "^6.0.0", "axios": "^1.6.0"}, "devDependencies": {"@types/node": "^20.0.0", "typescript": "^5.0.0", "eslint": "^8.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "jest": "^29.0.0", "@types/jest": "^29.0.0"}, "peerDependencies": {"react": "^18.0.0", "react-native": "^0.72.0"}, "files": ["dist", "src"]}