"use client"

import { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import { TopHeader } from "@/components/top-header"
import { Header } from "@/components/header"
import { Footer } from "@/components/footer"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { SearchDatePicker } from "@/components/ui/date-picker"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { 
  Calendar as CalendarIcon, 
  Clock, 
  Users, 
  MapPin, 
  Phone, 
  Mail, 
  Star,
  CheckCircle,
  ArrowLeft,
  CreditCard,
  Shield,
  AlertCircle
} from 'lucide-react'
import { format } from "date-fns"
import { cn } from "@/lib/utils"
import { useAuth } from '@/contexts/AuthContext'
import { generateClient } from '@aws-amplify/api'
import { getVendor, getVenue } from '@/src/graphql/queries'
import AvailabilityChecker from '@/components/booking/AvailabilityChecker'
import { useSafeTranslation } from '@/hooks/use-safe-translation'
import { showToast } from '@/lib/toast'
import toast from 'react-hot-toast'

const client = generateClient()

export default function BookingPage() {
  const params = useParams()
  const router = useRouter()
  const { isAuthenticated, user } = useAuth()
  const { t } = useSafeTranslation()

  const bookingType = params.type as 'vendor' | 'venue'
  const entityId = params.id as string
  
  const [entity, setEntity] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const [submitting, setSubmitting] = useState(false)
  const [selectedDate, setSelectedDate] = useState<Date>()
  const [selectedTime, setSelectedTime] = useState<string>('')
  const [isAvailable, setIsAvailable] = useState<boolean | null>(null)
  const [availabilityConflicts, setAvailabilityConflicts] = useState<any[]>([])
  const [showAvailabilityChecker, setShowAvailabilityChecker] = useState(false)
  
  // Form state
  const [formData, setFormData] = useState({
    eventDate: '',
    eventTime: '',
    guestCount: '',
    eventType: '',
    duration: '',
    specialRequests: '',
    budget: '',
    contactPreference: 'phone',
    agreeToTerms: false
  })

  // Load entity data
  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/login')
      return
    }
    
    loadEntityData()
  }, [entityId, bookingType, isAuthenticated])

  const loadEntityData = async () => {
    try {
      setLoading(true)
      let result
      
      if (bookingType === 'vendor') {
        result = await client.graphql({ query: getVendor, variables: { id: entityId } })
        setEntity(result.data.getVendor)
      } else {
        result = await client.graphql({ query: getVenue, variables: { id: entityId } })
        setEntity(result.data.getVenue)
      }
    } catch (error) {
      console.error('Error loading entity:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleDateSelect = (date: Date | undefined) => {
    setSelectedDate(date)
    if (date) {
      const formattedDate = format(date, 'yyyy-MM-dd')
      setFormData(prev => {
        const updatedFormData = { ...prev, eventDate: formattedDate }
        // Show availability checker when both date and time are selected
        if (updatedFormData.eventDate && updatedFormData.eventTime) {
          setShowAvailabilityChecker(true)
        }
        return updatedFormData
      })
    } else {
      setFormData(prev => ({ ...prev, eventDate: '' }))
      setShowAvailabilityChecker(false)
    }
  }

  const handleInputChange = (field: string, value: string | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }))

    // Show availability checker when date and time are selected
    if (field === 'eventDate' || field === 'eventTime') {
      const updatedFormData = { ...formData, [field]: value }
      if (updatedFormData.eventDate && updatedFormData.eventTime) {
        setShowAvailabilityChecker(true)
      }
    }
  }

  const handleAvailabilityChange = (available: boolean, conflicts?: any[]) => {
    setIsAvailable(available)
    setAvailabilityConflicts(conflicts || [])
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!formData.agreeToTerms) {
      showToast.warning('Please agree to the terms and conditions')
      return
    }

    // Check availability before submitting
    if (isAvailable === false) {
      showToast.error('This date and time is not available. Please select a different date or time.')
      return
    }

    setSubmitting(true)

    try {
      // Validate required fields
      if (!formData.eventDate) {
        showToast.error('Please select an event date.')
        return
      }

      if (!formData.eventTime) {
        showToast.error('Please select an event time.')
        return
      }

      if (!formData.guestCount || parseInt(formData.guestCount) <= 0) {
        showToast.error('Please enter a valid guest count.')
        return
      }

      // Import the booking service
      const { BookingService } = await import('@/lib/services/bookingService')

      // Prepare booking data
      const bookingData = {
        entityId,
        entityType: bookingType.toUpperCase() as 'VENDOR' | 'VENUE',
        entityName: entity.name,
        customerName: user?.signInDetails?.loginId || user?.username || 'Customer',
        customerPhone: user?.phone || '',
        // For vendors, use their userId; for venues, use the venue's userId or provided vendorId
        vendorId: bookingType === 'vendor' ? entity.userId : (entity.userId || entity.vendorId),
        eventDate: formData.eventDate,
        eventTime: formData.eventTime,
        guestCount: parseInt(formData.guestCount),
        eventType: formData.eventType,
        duration: formData.duration,
        specialRequests: formData.specialRequests,
        budget: formData.budget,
        contactPreference: formData.contactPreference.toUpperCase() as 'PHONE' | 'EMAIL' | 'WHATSAPP'
      }

      console.log('🔍 Debug - Booking form data:', {
        entityId,
        entityType: bookingType,
        entityUserId: entity.userId,
        finalVendorId: bookingData.vendorId,
        eventDate: bookingData.eventDate,
        eventTime: bookingData.eventTime,
        guestCount: bookingData.guestCount
      })

      // Create booking using client-side service
      const result = await BookingService.createBooking(bookingData)

      if (!result.success) {
        // Handle availability conflicts
        if (result.conflictingBookings && result.conflictingBookings.length > 0) {
          setAvailabilityConflicts(result.conflictingBookings)
          setIsAvailable(false)
          toast.error(`Booking conflict detected: ${result.message}`)
          return
        }
        toast.error(result.message || 'Failed to create booking')
        return
      }

      // Send notifications (optional)
      try {
        await BookingService.sendBookingNotifications(result.booking)
      } catch (notificationError) {
        console.warn('Failed to send notifications:', notificationError)
        // Don't fail the booking if notifications fail
      }

      // Show success message
      toast.success('Booking submitted successfully! Redirecting to confirmation page...')

      // Redirect to success page with booking ID
      router.push(`/booking/success?type=${bookingType}&id=${entityId}&bookingId=${result.booking.id}`)

    } catch (error) {
      console.error('Booking error:', error)
      toast.error(`Failed to submit booking: ${error.message}`)
    } finally {
      setSubmitting(false)
    }
  }

  if (!isAuthenticated) {
    return null // Will redirect to login
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-white">
        <TopHeader />
        <Header />
        <div className="flex items-center justify-center py-20">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
            <p>Loading booking details...</p>
          </div>
        </div>
        <Footer />
      </div>
    )
  }

  if (!entity) {
    return (
      <div className="min-h-screen bg-white">
        <TopHeader />
        <Header />
        <div className="text-center py-20">
          <p className="text-red-600 mb-4">{bookingType} not found</p>
          <Button onClick={() => router.back()}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Go Back
          </Button>
        </div>
        <Footer />
      </div>
    )
  }

  const timeSlots = [
    '09:00 AM', '10:00 AM', '11:00 AM', '12:00 PM',
    '01:00 PM', '02:00 PM', '03:00 PM', '04:00 PM',
    '05:00 PM', '06:00 PM', '07:00 PM', '08:00 PM'
  ]

  const eventTypes = bookingType === 'venue' 
    ? ['Wedding Ceremony', 'Reception', 'Engagement', 'Pre-Wedding Shoot', 'Other']
    : ['Photography', 'Videography', 'Catering', 'Decoration', 'Music', 'Other']

  return (
    <div className="min-h-screen bg-gray-50">
      <TopHeader />
      <Header />
      
      {/* Header Section */}
      <section className="bg-gradient-to-r from-primary to-pink-600 text-white py-12">
        <div className="container mx-auto px-4">
          <div className="flex items-center gap-4 mb-6">
            <Button 
              variant="ghost" 
              onClick={() => router.back()}
              className="text-white hover:bg-white/20"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back
            </Button>
          </div>
          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
            <div>
              <h1 className="text-3xl lg:text-4xl font-bold mb-4">
                Book {entity.name}
              </h1>
              <p className="text-xl text-white/90 mb-4">
                {bookingType === 'vendor' ? entity.category : entity.type}
              </p>
              <div className="flex items-center gap-4 text-white/80">
                <div className="flex items-center gap-1">
                  <MapPin className="h-4 w-4" />
                  <span>{entity.city}, {entity.state}</span>
                </div>
                {entity.rating && (
                  <div className="flex items-center gap-1">
                    <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                    <span>{entity.rating}</span>
                  </div>
                )}
              </div>
            </div>
            
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6">
              <h3 className="text-lg font-semibold mb-4">Quick Info</h3>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span>Price Range:</span>
                  <span className="font-semibold">{entity.priceRange || 'Contact for pricing'}</span>
                </div>
                {bookingType === 'venue' && entity.capacity && (
                  <div className="flex justify-between">
                    <span>Capacity:</span>
                    <span className="font-semibold">{entity.capacity} guests</span>
                  </div>
                )}
                <div className="flex justify-between">
                  <span>Contact:</span>
                  <span className="font-semibold">
                    {bookingType === 'vendor' ? entity.contact : entity.contactPhone}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Booking Form Section */}
      <section className="py-12">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <form onSubmit={handleSubmit} className="space-y-8">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                {/* Left Column - Event Details */}
                <div className="space-y-6">
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <CalendarIcon className="h-5 w-5" />
                        Event Details
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      {/* Event Date */}
                      <div>
                        <Label htmlFor="eventDate">Event Date *</Label>
                        <SearchDatePicker
                          date={selectedDate}
                          onSelect={handleDateSelect}
                          placeholder={t('hero.datePlaceholder', 'Select Date')}
                          disabled={(date) =>
                            date < new Date(new Date().setHours(0, 0, 0, 0))
                          }
                        />
                      </div>

                      {/* Event Time */}
                      <div>
                        <Label htmlFor="eventTime">Preferred Time *</Label>
                        <Select onValueChange={(value) => handleInputChange('eventTime', value)}>
                          <SelectTrigger>
                            <SelectValue placeholder="Select time" />
                          </SelectTrigger>
                          <SelectContent>
                            {timeSlots.map((time) => (
                              <SelectItem key={time} value={time}>
                                {time}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>

                      {/* Availability Checker */}
                      {showAvailabilityChecker && formData.eventDate && formData.eventTime && (
                        <div className="col-span-2">
                          <AvailabilityChecker
                            entityId={entityId}
                            entityType={bookingType.toUpperCase() as 'VENDOR' | 'VENUE'}
                            entityName={entity?.name || ''}
                            selectedDate={formData.eventDate}
                            selectedTime={formData.eventTime}
                            duration={formData.duration}
                            onAvailabilityChange={handleAvailabilityChange}
                          />
                        </div>
                      )}

                      {/* Guest Count */}
                      <div>
                        <Label htmlFor="guestCount">Number of Guests *</Label>
                        <Input
                          id="guestCount"
                          type="number"
                          placeholder="e.g., 150"
                          value={formData.guestCount}
                          onChange={(e) => handleInputChange('guestCount', e.target.value)}
                          required
                        />
                      </div>

                      {/* Event Type */}
                      <div>
                        <Label htmlFor="eventType">Event Type *</Label>
                        <Select onValueChange={(value) => handleInputChange('eventType', value)}>
                          <SelectTrigger>
                            <SelectValue placeholder="Select event type" />
                          </SelectTrigger>
                          <SelectContent>
                            {eventTypes.map((type) => (
                              <SelectItem key={type} value={type}>
                                {type}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>

                      {/* Duration */}
                      <div>
                        <Label htmlFor="duration">Event Duration</Label>
                        <Select onValueChange={(value) => handleInputChange('duration', value)}>
                          <SelectTrigger>
                            <SelectValue placeholder="Select duration" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="2-4 hours">2-4 hours</SelectItem>
                            <SelectItem value="4-6 hours">4-6 hours</SelectItem>
                            <SelectItem value="6-8 hours">6-8 hours</SelectItem>
                            <SelectItem value="full-day">Full day</SelectItem>
                            <SelectItem value="multiple-days">Multiple days</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </CardContent>
                  </Card>
                </div>

                {/* Right Column - Additional Info */}
                <div className="space-y-6">
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <Users className="h-5 w-5" />
                        Additional Information
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      {/* Budget */}
                      <div>
                        <Label htmlFor="budget">Budget Range</Label>
                        <Select onValueChange={(value) => handleInputChange('budget', value)}>
                          <SelectTrigger>
                            <SelectValue placeholder="Select budget range" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="under-50k">Under ₹50,000</SelectItem>
                            <SelectItem value="50k-1l">₹50,000 - ₹1,00,000</SelectItem>
                            <SelectItem value="1l-2l">₹1,00,000 - ₹2,00,000</SelectItem>
                            <SelectItem value="2l-5l">₹2,00,000 - ₹5,00,000</SelectItem>
                            <SelectItem value="above-5l">Above ₹5,00,000</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      {/* Contact Preference */}
                      <div>
                        <Label htmlFor="contactPreference">Preferred Contact Method *</Label>
                        <Select onValueChange={(value) => handleInputChange('contactPreference', value)}>
                          <SelectTrigger>
                            <SelectValue placeholder="How should we contact you?" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="phone">Phone Call</SelectItem>
                            <SelectItem value="email">Email</SelectItem>
                            <SelectItem value="whatsapp">WhatsApp</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      {/* Special Requests */}
                      <div>
                        <Label htmlFor="specialRequests">Special Requests or Requirements</Label>
                        <Textarea
                          id="specialRequests"
                          placeholder="Any specific requirements, dietary restrictions, accessibility needs, etc."
                          value={formData.specialRequests}
                          onChange={(e) => handleInputChange('specialRequests', e.target.value)}
                          rows={4}
                        />
                      </div>
                    </CardContent>
                  </Card>

                  {/* Terms and Conditions */}
                  <Card className="border-2 border-primary/20">
                    <CardHeader>
                      <CardTitle className="text-lg">Agreement Required</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="flex items-start space-x-6 p-4 bg-gray-50 rounded-lg">
                        <Checkbox
                          id="terms"
                          checked={formData.agreeToTerms}
                          onCheckedChange={(checked) => handleInputChange('agreeToTerms', checked as boolean)}
                          className="w-6 h-6 mt-2"
                        />
                        <Label htmlFor="terms" className="text-lg leading-relaxed font-medium cursor-pointer">
                          I agree to the{' '}
                          <a href="/terms" className="text-primary hover:underline font-semibold text-lg">
                            Terms and Conditions
                          </a>{' '}
                          and{' '}
                          <a href="/privacy" className="text-primary hover:underline font-semibold text-lg">
                            Privacy Policy
                          </a>
                        </Label>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Submit Button */}
                  <Card>
                    <CardContent className="pt-6">
                      <div className="space-y-4">

                        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                          <div className="flex items-start gap-2">
                            <AlertCircle className="h-5 w-5 text-blue-600 mt-0.5" />
                            <div className="text-sm text-blue-800">
                              <p className="font-semibold mb-1">Booking Process</p>
                              <p>
                                This is an inquiry request. The {bookingType} will contact you within 24 hours 
                                to confirm availability and discuss details. No payment is required at this stage.
                              </p>
                            </div>
                          </div>
                        </div>

                        <Button
                          type="submit"
                          className="w-full bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white font-bold py-3 shadow-lg hover:shadow-xl transition-all duration-200"
                          disabled={submitting || !formData.agreeToTerms}
                        >
                          {submitting ? (
                            <>
                              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                              Submitting Booking Request...
                            </>
                          ) : (
                            <>
                              <CheckCircle className="h-5 w-5 mr-2" />
                              Submit Booking Request
                            </>
                          )}
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </div>
            </form>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  )
}
