'use client'

import { useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'

export default function Error({
  error,
  reset,
}: {
  error: Error & { digest?: string }
  reset: () => void
}) {
  useEffect(() => {
    // Log the error to an error reporting service
    console.error('Test Emails Page Error:', error)
  }, [error])

  return (
    <div className="container mx-auto p-6 max-w-2xl">
      <Card>
        <CardHeader>
          <CardTitle className="text-red-600">Something went wrong!</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
            <p className="text-red-800 mb-2">
              An error occurred while loading the email testing dashboard.
            </p>
            <p className="text-sm text-red-600">
              This might be due to:
            </p>
            <ul className="text-sm text-red-600 mt-2 ml-4 list-disc">
              <li>Email service not being available</li>
              <li>Authentication issues</li>
              <li>Network connectivity problems</li>
              <li>Browser compatibility issues</li>
            </ul>
          </div>

          <div className="flex gap-3">
            <Button onClick={reset} variant="default">
              Try again
            </Button>
            <Button 
              onClick={() => window.location.href = '/'} 
              variant="outline"
            >
              Go to Home
            </Button>
          </div>

          <div className="text-sm text-gray-600">
            <p>If the problem persists, you can also:</p>
            <ul className="mt-2 ml-4 list-disc">
              <li>Refresh the page</li>
              <li>Clear your browser cache</li>
              <li>Check your internet connection</li>
              <li>Try using the command-line test scripts instead</li>
            </ul>
          </div>

          <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <h3 className="font-medium text-blue-800 mb-2">Alternative Testing Methods:</h3>
            <p className="text-sm text-blue-600 mb-2">
              Instead of the web interface, you can test email services using the command line:
            </p>
            <div className="bg-gray-100 p-3 rounded font-mono text-sm">
              <div>npm run test:email</div>
              <div>npm run test:booking</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
} 