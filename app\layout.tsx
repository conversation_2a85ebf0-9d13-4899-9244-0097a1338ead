import type React from "react"
import type { <PERSON>ada<PERSON> } from "next"
import { Inter } from "next/font/google"
import "./globals.css"
import '@/styles/nprogress.css'
import { ClientRoot } from "@/components/ClientRoot"
import '@/lib/performance-fixes' // Apply performance optimizations
import PageLoader from '@/components/PageLoader';
import StructuredDataScript from '@/components/seo/StructuredDataScript';
import PWAInstallPrompt from '@/components/PWAInstallPrompt';
// Force dynamic rendering to avoid useSearchParams issues
export const dynamic = 'force-dynamic'


const inter = Inter({ subsets: ["latin"] })

export const metadata: Metadata = {
  title: "BookmyFestive - Your Dream Celebration Starts Here",
  description:
    "Discover the best wedding vendors, venues, and inspiration for your perfect day. Plan Your Dream Celebration with BookmyFestive.",
  keywords: "wedding, vendors, venues, photography, catering, decoration, bridal wear, wedding planning, BookmyFestive",
  generator: 'v0.dev',
  metadataBase: new URL(process.env.NEXT_PUBLIC_SITE_URL || 'https://BookmyFestive.com'),
  alternates: {
    canonical: '/',
  },
  manifest: '/manifest.json',
  themeColor: '#610f13',
  viewport: 'width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no, viewport-fit=cover',
  appleWebApp: {
    capable: true,
    statusBarStyle: 'default',
    title: 'BookmyFestive',
  },
  formatDetection: {
    telephone: false,
  },
  other: {
    'mobile-web-app-capable': 'yes',
    'apple-mobile-web-app-capable': 'yes',
    'apple-mobile-web-app-status-bar-style': 'default',
    'apple-mobile-web-app-title': 'BookmyFestive',
    'application-name': 'BookmyFestive',
    'msapplication-TileColor': '#610f13',
    'msapplication-config': '/browserconfig.xml',
  },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <head>
        <link rel="icon" href="/BookmyFestive_favicon.webp" type="image/webp" />
        <link rel="apple-touch-icon" href="/BookmyFestive_favicon.webp" />
        <link rel="manifest" href="/manifest.json" />
        <meta name="theme-color" content="#610f13" />
        <meta name="msapplication-TileColor" content="#610f13" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        {/* Razorpay Script */}
        <script src="https://checkout.razorpay.com/v1/checkout.js" async></script>
        {/* Service Worker Registration */}
        <script
          dangerouslySetInnerHTML={{
            __html: `
              if ('serviceWorker' in navigator) {
                window.addEventListener('load', function() {
                  navigator.serviceWorker.register('/sw.js')
                    .then(function(registration) {
                      console.log('SW registered: ', registration);
                    })
                    .catch(function(registrationError) {
                      console.log('SW registration failed: ', registrationError);
                    });
                });
              }
            `,
          }}
        />
      </head>
      <body className={inter.className}>
        <StructuredDataScript type="organization" />
        <StructuredDataScript type="website" />
        <ClientRoot>
          <PageLoader />
          {children}
          <PWAInstallPrompt />
        </ClientRoot>
      </body>
    </html>
  )
}