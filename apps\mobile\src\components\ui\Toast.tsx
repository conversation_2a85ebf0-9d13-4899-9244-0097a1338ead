import React, { useRef, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Animated,
  Dimensions,
  TouchableOpacity,
  SafeAreaView,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import * as Haptics from 'expo-haptics';
import { useTheme } from '../../providers/ThemeProvider';

const { width: screenWidth } = Dimensions.get('window');

export type ToastType = 'success' | 'error' | 'warning' | 'info';

interface ToastProps {
  visible: boolean;
  type: ToastType;
  title?: string;
  message: string;
  duration?: number;
  onHide: () => void;
  position?: 'top' | 'bottom';
  hapticFeedback?: boolean;
  showCloseButton?: boolean;
  onPress?: () => void;
}

export default function Toast({
  visible,
  type,
  title,
  message,
  duration = 3000,
  onHide,
  position = 'top',
  hapticFeedback = true,
  showCloseButton = true,
  onPress,
}: ToastProps) {
  const { theme } = useTheme();
  
  // Animation values
  const translateY = useRef(new Animated.Value(position === 'top' ? -200 : 200)).current;
  const opacity = useRef(new Animated.Value(0)).current;
  const scale = useRef(new Animated.Value(0.8)).current;

  // Timer ref
  const hideTimer = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    if (visible) {
      showToast();
      
      if (hapticFeedback) {
        switch (type) {
          case 'success':
            Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
            break;
          case 'error':
            Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
            break;
          case 'warning':
            Haptics.notificationAsync(Haptics.NotificationFeedbackType.Warning);
            break;
          default:
            Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
        }
      }

      // Auto-hide timer
      if (duration > 0) {
        hideTimer.current = setTimeout(() => {
          hideToast();
        }, duration);
      }
    } else {
      hideToast();
    }

    return () => {
      if (hideTimer.current) {
        clearTimeout(hideTimer.current);
      }
    };
  }, [visible, type, duration]);

  const showToast = () => {
    Animated.parallel([
      Animated.spring(translateY, {
        toValue: 0,
        tension: 100,
        friction: 8,
        useNativeDriver: true,
      }),
      Animated.timing(opacity, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.spring(scale, {
        toValue: 1,
        tension: 100,
        friction: 8,
        useNativeDriver: true,
      }),
    ]).start();
  };

  const hideToast = () => {
    Animated.parallel([
      Animated.timing(translateY, {
        toValue: position === 'top' ? -200 : 200,
        duration: 250,
        useNativeDriver: true,
      }),
      Animated.timing(opacity, {
        toValue: 0,
        duration: 250,
        useNativeDriver: true,
      }),
      Animated.timing(scale, {
        toValue: 0.8,
        duration: 250,
        useNativeDriver: true,
      }),
    ]).start(() => {
      onHide();
    });
  };

  const getToastConfig = () => {
    switch (type) {
      case 'success':
        return {
          backgroundColor: '#4CAF50',
          icon: 'checkmark-circle',
          iconColor: '#FFFFFF',
        };
      case 'error':
        return {
          backgroundColor: '#F44336',
          icon: 'close-circle',
          iconColor: '#FFFFFF',
        };
      case 'warning':
        return {
          backgroundColor: '#FF9800',
          icon: 'warning',
          iconColor: '#FFFFFF',
        };
      case 'info':
      default:
        return {
          backgroundColor: theme.colors.primary,
          icon: 'information-circle',
          iconColor: '#FFFFFF',
        };
    }
  };

  const handlePress = () => {
    if (onPress) {
      if (hapticFeedback) {
        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
      }
      onPress();
    }
  };

  const handleClose = () => {
    if (hapticFeedback) {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }
    hideToast();
  };

  if (!visible) {
    return null;
  }

  const config = getToastConfig();
  const containerStyle = position === 'top' ? styles.topContainer : styles.bottomContainer;

  return (
    <SafeAreaView style={[styles.safeArea, containerStyle]} pointerEvents="box-none">
      <Animated.View
        style={[
          styles.container,
          {
            backgroundColor: config.backgroundColor,
            opacity,
            transform: [
              { translateY },
              { scale },
            ],
          },
        ]}
      >
        <TouchableOpacity
          style={styles.content}
          onPress={handlePress}
          activeOpacity={onPress ? 0.8 : 1}
          disabled={!onPress}
        >
          {/* Icon */}
          <View style={styles.iconContainer}>
            <Ionicons
              name={config.icon as any}
              size={24}
              color={config.iconColor}
            />
          </View>

          {/* Text Content */}
          <View style={styles.textContainer}>
            {title && (
              <Text style={[styles.title, { color: config.iconColor }]}>
                {title}
              </Text>
            )}
            <Text style={[styles.message, { color: config.iconColor }]}>
              {message}
            </Text>
          </View>

          {/* Close Button */}
          {showCloseButton && (
            <TouchableOpacity
              style={styles.closeButton}
              onPress={handleClose}
              activeOpacity={0.7}
            >
              <Ionicons
                name="close"
                size={20}
                color={config.iconColor}
              />
            </TouchableOpacity>
          )}
        </TouchableOpacity>
      </Animated.View>
    </SafeAreaView>
  );
}

// Toast Manager for global toast handling
class ToastManager {
  private static instance: ToastManager;
  private toastRef: React.RefObject<any> | null = null;

  static getInstance(): ToastManager {
    if (!ToastManager.instance) {
      ToastManager.instance = new ToastManager();
    }
    return ToastManager.instance;
  }

  setToastRef(ref: React.RefObject<any>) {
    this.toastRef = ref;
  }

  show(config: Omit<ToastProps, 'visible' | 'onHide'>) {
    if (this.toastRef?.current) {
      this.toastRef.current.show(config);
    }
  }

  hide() {
    if (this.toastRef?.current) {
      this.toastRef.current.hide();
    }
  }

  success(message: string, title?: string) {
    this.show({ type: 'success', message, title });
  }

  error(message: string, title?: string) {
    this.show({ type: 'error', message, title });
  }

  warning(message: string, title?: string) {
    this.show({ type: 'warning', message, title });
  }

  info(message: string, title?: string) {
    this.show({ type: 'info', message, title });
  }
}

export const toastManager = ToastManager.getInstance();

// Global Toast Component
export const GlobalToast = React.forwardRef<any, {}>((props, ref) => {
  const [toastConfig, setToastConfig] = React.useState<ToastProps | null>(null);

  React.useImperativeHandle(ref, () => ({
    show: (config: Omit<ToastProps, 'visible' | 'onHide'>) => {
      setToastConfig({
        ...config,
        visible: true,
        onHide: () => setToastConfig(null),
      });
    },
    hide: () => {
      setToastConfig(null);
    },
  }));

  if (!toastConfig) {
    return null;
  }

  return <Toast {...toastConfig} />;
});

const styles = StyleSheet.create({
  safeArea: {
    position: 'absolute',
    left: 0,
    right: 0,
    zIndex: 9999,
    paddingHorizontal: 16,
  },
  topContainer: {
    top: 0,
    paddingTop: 10,
  },
  bottomContainer: {
    bottom: 0,
    paddingBottom: 10,
  },
  container: {
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  content: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
    paddingHorizontal: 16,
  },
  iconContainer: {
    marginRight: 12,
  },
  textContainer: {
    flex: 1,
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 2,
  },
  message: {
    fontSize: 14,
    lineHeight: 20,
  },
  closeButton: {
    marginLeft: 12,
    padding: 4,
  },
});
