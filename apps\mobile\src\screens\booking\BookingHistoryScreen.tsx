import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  RefreshControl,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { useTheme } from '../../providers/ThemeProvider';
import { Card } from '../../components/ui/Card';
import { Badge } from '../../components/ui/Badge';
import { Button } from '../../components/ui/Button';

export default function BookingHistoryScreen() {
  const navigation = useNavigation();
  const { theme } = useTheme();
  const [refreshing, setRefreshing] = useState(false);
  const [bookings, setBookings] = useState([
    {
      id: '1',
      serviceName: 'Wedding Photography',
      vendorName: 'Capture Moments Studio',
      date: '2024-03-15',
      time: '10:00 AM',
      status: 'confirmed',
      amount: 15000,
      location: 'Chennai, Tamil Nadu',
    },
    {
      id: '2',
      serviceName: 'Bridal Makeup',
      vendorName: 'Glamour Beauty Salon',
      date: '2024-03-14',
      time: '8:00 AM',
      status: 'pending',
      amount: 8000,
      location: 'Chennai, Tamil Nadu',
    },
    {
      id: '3',
      serviceName: 'Wedding Decoration',
      vendorName: 'Elegant Decorators',
      date: '2024-03-15',
      time: '6:00 AM',
      status: 'completed',
      amount: 25000,
      location: 'Chennai, Tamil Nadu',
    },
  ]);

  const onRefresh = async () => {
    setRefreshing(true);
    // TODO: Fetch latest bookings
    setTimeout(() => setRefreshing(false), 1000);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'confirmed':
        return theme.colors.secondary;
      case 'pending':
        return theme.colors.accent;
      case 'completed':
        return theme.colors.primary;
      case 'cancelled':
        return theme.colors.destructive;
      default:
        return theme.colors.textSecondary;
    }
  };

  const getStatusText = (status: string) => {
    return status.charAt(0).toUpperCase() + status.slice(1);
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    scrollContainer: {
      padding: 16,
      gap: 16,
    },
    header: {
      marginBottom: 8,
    },
    title: {
      fontSize: 24,
      fontWeight: 'bold',
      color: theme.colors.text,
      marginBottom: 4,
    },
    subtitle: {
      fontSize: 16,
      color: theme.colors.textSecondary,
    },
    bookingCard: {
      padding: 16,
      marginBottom: 12,
    },
    bookingHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'flex-start',
      marginBottom: 12,
    },
    serviceName: {
      fontSize: 18,
      fontWeight: '600',
      color: theme.colors.text,
      flex: 1,
      marginRight: 8,
    },
    vendorName: {
      fontSize: 14,
      color: theme.colors.textSecondary,
      marginBottom: 8,
    },
    bookingDetails: {
      gap: 8,
    },
    detailRow: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 8,
    },
    detailIcon: {
      fontSize: 16,
      width: 20,
    },
    detailText: {
      fontSize: 14,
      color: theme.colors.textSecondary,
      flex: 1,
    },
    amountText: {
      fontSize: 16,
      fontWeight: 'bold',
      color: theme.colors.primary,
    },
    bookingActions: {
      flexDirection: 'row',
      gap: 8,
      marginTop: 12,
      paddingTop: 12,
      borderTopWidth: 1,
      borderTopColor: theme.colors.border,
    },
    actionButton: {
      flex: 1,
    },
    emptyContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      padding: 40,
    },
    emptyIcon: {
      fontSize: 64,
      color: theme.colors.textSecondary,
      marginBottom: 16,
    },
    emptyTitle: {
      fontSize: 20,
      fontWeight: 'bold',
      color: theme.colors.text,
      marginBottom: 8,
      textAlign: 'center',
    },
    emptyText: {
      fontSize: 16,
      color: theme.colors.textSecondary,
      textAlign: 'center',
      lineHeight: 24,
    },
  });

  if (bookings.length === 0) {
    return (
      <View style={styles.container}>
        <View style={styles.emptyContainer}>
          <Text style={styles.emptyIcon}>📅</Text>
          <Text style={styles.emptyTitle}>No Bookings Yet</Text>
          <Text style={styles.emptyText}>
            Start booking Festive Services to see them here
          </Text>
          <Button
            onPress={() => navigation.navigate('MainTabs' as never)}
            style={{ marginTop: 24 }}
          >
            Browse Services
          </Button>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <ScrollView
        contentContainerStyle={styles.scrollContainer}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        <View style={styles.header}>
          <Text style={styles.title}>My Bookings</Text>
          <Text style={styles.subtitle}>
            {bookings.length} booking{bookings.length !== 1 ? 's' : ''}
          </Text>
        </View>

        {bookings.map((booking) => (
          <Card key={booking.id} style={styles.bookingCard}>
            <View style={styles.bookingHeader}>
              <View style={{ flex: 1 }}>
                <Text style={styles.serviceName}>{booking.serviceName}</Text>
                <Text style={styles.vendorName}>{booking.vendorName}</Text>
              </View>
              <Badge
                variant="secondary"
                style={{ backgroundColor: getStatusColor(booking.status) + '20' }}
              >
                <Text style={{ color: getStatusColor(booking.status) }}>
                  {getStatusText(booking.status)}
                </Text>
              </Badge>
            </View>

            <View style={styles.bookingDetails}>
              <View style={styles.detailRow}>
                <Text style={styles.detailIcon}>📅</Text>
                <Text style={styles.detailText}>
                  {booking.date} at {booking.time}
                </Text>
              </View>
              <View style={styles.detailRow}>
                <Text style={styles.detailIcon}>📍</Text>
                <Text style={styles.detailText}>{booking.location}</Text>
              </View>
              <View style={styles.detailRow}>
                <Text style={styles.detailIcon}>💰</Text>
                <Text style={styles.amountText}>
                  ₹{booking.amount.toLocaleString()}
                </Text>
              </View>
            </View>

            <View style={styles.bookingActions}>
              <Button
                variant="outline"
                size="sm"
                style={styles.actionButton}
                onPress={() =>
                  navigation.navigate('BookingDetail' as never, {
                    bookingId: booking.id,
                  })
                }
              >
                View Details
              </Button>
              {booking.status === 'pending' && (
                <Button
                  variant="destructive"
                  size="sm"
                  style={styles.actionButton}
                >
                  Cancel
                </Button>
              )}
            </View>
          </Card>
        ))}
      </ScrollView>
    </View>
  );
}
