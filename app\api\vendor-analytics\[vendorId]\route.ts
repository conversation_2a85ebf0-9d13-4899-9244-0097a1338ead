import { NextRequest, NextResponse } from 'next/server'
import { VendorAnalyticsService } from '@/lib/services/vendorAnalyticsService'

export async function GET(
  request: NextRequest,
  { params }: { params: { vendorId: string } }
) {
  try {
    const { searchParams } = new URL(request.url)
    const dateRange = searchParams.get('range') || '30d'
    
    const data = await VendorAnalyticsService.getVendorAnalytics(
      params.vendorId,
      dateRange
    )
    
    return NextResponse.json(data)
  } catch (error) {
    console.error('Vendor Analytics Error:', error)
    return NextResponse.json({ error: 'Failed to fetch vendor analytics' }, { status: 500 })
  }
}