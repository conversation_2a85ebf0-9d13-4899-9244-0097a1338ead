import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { useTheme } from '../providers/ThemeProvider';
import { useAuth } from '../providers/AuthProvider';
import { useWeddingTranslations, useCommonTranslations } from '../hooks/useTranslation';
import type { AppStackParamList } from '../navigation/AppNavigator';
import type { StackNavigationProp } from '@react-navigation/stack';

type WeddingPlanningNavigationProp = StackNavigationProp<AppStackParamList, 'WeddingPlanning'>;

interface PlanningTool {
  id: string;
  title: string;
  description: string;
  icon: keyof typeof Ionicons.glyphMap;
  color: string;
  screen: string;
  progress?: number;
  isCompleted?: boolean;
}

export default function WeddingPlanningScreen() {
  const navigation = useNavigation<WeddingPlanningNavigationProp>();
  const { theme } = useTheme();
  const { userProfile } = useAuth();
  const weddingT = useWeddingTranslations();
  const commonT = useCommonTranslations();

  // Mock wedding data - in real app, this would come from user profile
  const [weddingData] = useState({
    weddingDate: new Date('2024-12-15'),
    coupleName: 'John & Jane',
    venue: 'Grand Palace Hotel',
    guestCount: 150,
    budget: 500000,
  });

  const planningTools: PlanningTool[] = [
    {
      id: 'budget',
      title: weddingT.budgetTracker,
      description: 'Track your wedding expenses and stay within budget',
      icon: 'wallet-outline',
      color: '#4CAF50',
      screen: 'BudgetTracker',
      progress: 65,
    },
    {
      id: 'guest-list',
      title: weddingT.guestListManager,
      description: 'Manage your guest list and track RSVPs',
      icon: 'people-outline',
      color: '#2196F3',
      screen: 'GuestListManager',
      progress: 40,
    },
    {
      id: 'timeline',
      title: 'Wedding Timeline',
      description: 'Plan your wedding day schedule and timeline',
      icon: 'time-outline',
      color: '#FF9800',
      screen: 'WeddingTimeline',
      progress: 30,
    },
    {
      id: 'checklist',
      title: 'Wedding Checklist',
      description: 'Complete tasks and stay organized',
      icon: 'checkmark-circle-outline',
      color: '#9C27B0',
      screen: 'WeddingChecklist',
      progress: 75,
    },
    {
      id: 'vendors',
      title: 'Vendor Manager',
      description: 'Track vendor bookings and payments',
      icon: 'business-outline',
      color: '#F44336',
      screen: 'VendorManager',
      progress: 55,
    },
    {
      id: 'seating',
      title: 'Seating Planner',
      description: 'Plan your reception seating arrangement',
      icon: 'grid-outline',
      color: '#607D8B',
      screen: 'SeatingPlanner',
      progress: 20,
    },
  ];

  const getDaysUntilWedding = () => {
    const today = new Date();
    const wedding = new Date(weddingData.weddingDate);
    const diffTime = wedding.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  const getOverallProgress = () => {
    const totalProgress = planningTools.reduce((sum, tool) => sum + (tool.progress || 0), 0);
    return Math.round(totalProgress / planningTools.length);
  };

  const handleToolPress = (tool: PlanningTool) => {
    // Navigate to specific screens for implemented tools
    switch (tool.id) {
      case 'budget':
        navigation.navigate('BudgetTracker');
        break;
      case 'guest-list':
        navigation.navigate('GuestListManager');
        break;
      default:
        // For tools not yet implemented, show an alert
        Alert.alert(
          tool.title,
          `Opening ${tool.title}. This feature will be available in the next update!`,
          [
            { text: 'OK' },
            { text: 'Learn More', onPress: () => console.log('Learn more about', tool.title) }
          ]
        );
        break;
    }
  };

  const daysUntilWedding = getDaysUntilWedding();
  const overallProgress = getOverallProgress();

  return (
    <ScrollView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {/* Wedding Overview Card */}
      <View style={[styles.overviewCard, { backgroundColor: theme.colors.surface }]}>
        <View style={styles.overviewHeader}>
          <View>
            <Text style={[styles.coupleNames, { color: theme.colors.text }]}>
              {weddingData.coupleName}
            </Text>
            <Text style={[styles.weddingDate, { color: theme.colors.textSecondary }]}>
              {weddingData.weddingDate.toLocaleDateString('en-US', {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric',
              })}
            </Text>
          </View>
          
          <View style={styles.countdownContainer}>
            <Text style={[styles.countdownNumber, { color: theme.colors.primary }]}>
              {daysUntilWedding}
            </Text>
            <Text style={[styles.countdownLabel, { color: theme.colors.textSecondary }]}>
              days to go
            </Text>
          </View>
        </View>

        <View style={styles.progressContainer}>
          <View style={styles.progressHeader}>
            <Text style={[styles.progressLabel, { color: theme.colors.text }]}>
              Overall Progress
            </Text>
            <Text style={[styles.progressPercentage, { color: theme.colors.primary }]}>
              {overallProgress}%
            </Text>
          </View>
          
          <View style={[styles.progressBar, { backgroundColor: theme.colors.border }]}>
            <View 
              style={[
                styles.progressFill, 
                { 
                  backgroundColor: theme.colors.primary,
                  width: `${overallProgress}%`
                }
              ]} 
            />
          </View>
        </View>

        <View style={styles.quickStats}>
          <View style={styles.statItem}>
            <Ionicons name="location-outline" size={16} color={theme.colors.textSecondary} />
            <Text style={[styles.statText, { color: theme.colors.textSecondary }]}>
              {weddingData.venue}
            </Text>
          </View>
          
          <View style={styles.statItem}>
            <Ionicons name="people-outline" size={16} color={theme.colors.textSecondary} />
            <Text style={[styles.statText, { color: theme.colors.textSecondary }]}>
              {weddingData.guestCount} guests
            </Text>
          </View>
          
          <View style={styles.statItem}>
            <Ionicons name="wallet-outline" size={16} color={theme.colors.textSecondary} />
            <Text style={[styles.statText, { color: theme.colors.textSecondary }]}>
              ₹{weddingData.budget.toLocaleString()} budget
            </Text>
          </View>
        </View>
      </View>

      {/* Planning Tools Grid */}
      <View style={styles.toolsSection}>
        <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
          Planning Tools
        </Text>
        
        <View style={styles.toolsGrid}>
          {planningTools.map((tool) => (
            <TouchableOpacity
              key={tool.id}
              style={[styles.toolCard, { backgroundColor: theme.colors.surface }]}
              onPress={() => handleToolPress(tool)}
              activeOpacity={0.7}
            >
              <View style={[styles.toolIcon, { backgroundColor: tool.color + '20' }]}>
                <Ionicons name={tool.icon} size={24} color={tool.color} />
              </View>
              
              <Text style={[styles.toolTitle, { color: theme.colors.text }]}>
                {tool.title}
              </Text>
              
              <Text style={[styles.toolDescription, { color: theme.colors.textSecondary }]}>
                {tool.description}
              </Text>
              
              {tool.progress !== undefined && (
                <View style={styles.toolProgress}>
                  <View style={[styles.toolProgressBar, { backgroundColor: theme.colors.border }]}>
                    <View 
                      style={[
                        styles.toolProgressFill,
                        { 
                          backgroundColor: tool.color,
                          width: `${tool.progress}%`
                        }
                      ]} 
                    />
                  </View>
                  <Text style={[styles.toolProgressText, { color: tool.color }]}>
                    {tool.progress}%
                  </Text>
                </View>
              )}
            </TouchableOpacity>
          ))}
        </View>
      </View>

      {/* Quick Actions */}
      <View style={styles.quickActionsSection}>
        <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
          Quick Actions
        </Text>
        
        <View style={styles.quickActionsGrid}>
          <TouchableOpacity 
            style={[styles.quickActionCard, { backgroundColor: theme.colors.primary }]}
            onPress={() => Alert.alert('Add Task', 'Add a new task to your wedding checklist')}
          >
            <Ionicons name="add-circle-outline" size={24} color="white" />
            <Text style={styles.quickActionText}>Add Task</Text>
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={[styles.quickActionCard, { backgroundColor: theme.colors.secondary }]}
            onPress={() => Alert.alert('Find Vendors', 'Browse and book wedding vendors')}
          >
            <Ionicons name="search-outline" size={24} color="white" />
            <Text style={styles.quickActionText}>Find Vendors</Text>
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={[styles.quickActionCard, { backgroundColor: theme.colors.success }]}
            onPress={() => Alert.alert('Share Progress', 'Share your wedding planning progress')}
          >
            <Ionicons name="share-outline" size={24} color="white" />
            <Text style={styles.quickActionText}>Share Progress</Text>
          </TouchableOpacity>
        </View>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  overviewCard: {
    margin: 16,
    padding: 20,
    borderRadius: 16,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  overviewHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 20,
  },
  coupleNames: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  weddingDate: {
    fontSize: 16,
    lineHeight: 22,
  },
  countdownContainer: {
    alignItems: 'center',
  },
  countdownNumber: {
    fontSize: 32,
    fontWeight: 'bold',
  },
  countdownLabel: {
    fontSize: 12,
    textAlign: 'center',
  },
  progressContainer: {
    marginBottom: 20,
  },
  progressHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  progressLabel: {
    fontSize: 16,
    fontWeight: '600',
  },
  progressPercentage: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  progressBar: {
    height: 8,
    borderRadius: 4,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    borderRadius: 4,
  },
  quickStats: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    flexWrap: 'wrap',
    gap: 8,
  },
  statItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
    flex: 1,
    minWidth: '30%',
  },
  statText: {
    fontSize: 12,
    flex: 1,
  },
  toolsSection: {
    paddingHorizontal: 16,
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  toolsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  toolCard: {
    width: '48%',
    padding: 16,
    borderRadius: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  toolIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 12,
  },
  toolTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  toolDescription: {
    fontSize: 12,
    lineHeight: 16,
    marginBottom: 12,
  },
  toolProgress: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  toolProgressBar: {
    flex: 1,
    height: 4,
    borderRadius: 2,
    overflow: 'hidden',
  },
  toolProgressFill: {
    height: '100%',
    borderRadius: 2,
  },
  toolProgressText: {
    fontSize: 12,
    fontWeight: '600',
    minWidth: 32,
  },
  quickActionsSection: {
    paddingHorizontal: 16,
    marginBottom: 24,
  },
  quickActionsGrid: {
    flexDirection: 'row',
    gap: 12,
  },
  quickActionCard: {
    flex: 1,
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
    gap: 8,
  },
  quickActionText: {
    color: 'white',
    fontSize: 12,
    fontWeight: '600',
    textAlign: 'center',
  },
});
