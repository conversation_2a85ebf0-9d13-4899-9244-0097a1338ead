"use client";
import { TopHeader } from "@/components/top-header";
import { Header } from "@/components/header";
import { Footer } from "@/components/footer";

export default function CommunityPage() {
  return (
    <div className="min-h-screen bg-white flex flex-col">
      <TopHeader />
      <Header />
      <section className="flex flex-1 items-center justify-center bg-gray-50" style={{ paddingTop: 80, paddingBottom: 40 }}>
        <div className="w-full max-w-3xl mx-auto bg-white rounded-xl shadow-lg overflow-hidden flex flex-col items-center p-8">
          <h1 className="text-3xl md:text-4xl font-bold mb-4 text-primary">Community</h1>
          <p className="text-lg text-gray-600 mb-8 text-center">Join the conversation, ask questions, and connect with other couples and wedding experts.</p>
          <div className="w-full flex flex-col items-center justify-center min-h-[200px] border-2 border-dashed border-gray-200 rounded-lg bg-gray-50 p-8">
            <span className="text-gray-400 text-lg">[Community forums and groups will appear here]</span>
          </div>
        </div>
      </section>
      <Footer />
    </div>
  );
} 