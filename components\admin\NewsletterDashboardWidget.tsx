'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Mail, 
  Users, 
  TrendingUp, 
  UserX,
  ArrowRight,
  Activity,
  Calendar,
  Target
} from 'lucide-react';
import { newsletterService, NewsletterStats } from '@/lib/services/newsletterService';
import Link from 'next/link';

export default function NewsletterDashboardWidget() {
  const [stats, setStats] = useState<NewsletterStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadStats();
  }, []);

  const loadStats = async () => {
    try {
      setLoading(true);
      setError(null);
      const statsData = await newsletterService.getStats();
      setStats(statsData);
    } catch (err: any) {
      setError(err.message || 'Failed to load newsletter stats');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Mail className="w-5 h-5" />
            Newsletter Overview
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-32">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Mail className="w-5 h-5" />
            Newsletter Overview
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center text-red-600 py-8">
            <p>Failed to load newsletter data</p>
            <Button variant="outline" size="sm" onClick={loadStats} className="mt-2">
              Retry
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  const activeRate = stats ? ((stats.activeSubscribers / stats.totalSubscribers) * 100).toFixed(1) : '0';
  const growthRate = stats && stats.recentSubscriptions > 0 ? '+' + stats.recentSubscriptions : '0';

  return (
    <Card className="hover:shadow-lg transition-shadow">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Mail className="w-5 h-5 text-blue-600" />
            Newsletter Overview
          </CardTitle>
          <Badge variant="secondary">Email Marketing</Badge>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Key Metrics */}
        <div className="grid grid-cols-2 gap-4">
          <div className="text-center p-3 bg-green-50 rounded-lg">
            <div className="flex items-center justify-center gap-1 text-green-600 mb-1">
              <Users className="w-4 h-4" />
              <span className="text-xs font-medium">Active</span>
            </div>
            <div className="text-2xl font-bold text-green-700">
              {stats?.activeSubscribers || 0}
            </div>
            <div className="text-xs text-green-600">
              {activeRate}% of total
            </div>
          </div>

          <div className="text-center p-3 bg-blue-50 rounded-lg">
            <div className="flex items-center justify-center gap-1 text-blue-600 mb-1">
              <TrendingUp className="w-4 h-4" />
              <span className="text-xs font-medium">Growth</span>
            </div>
            <div className="text-2xl font-bold text-blue-700">
              {growthRate}
            </div>
            <div className="text-xs text-blue-600">
              Last 30 days
            </div>
          </div>
        </div>

        {/* Additional Stats */}
        <div className="space-y-2">
          <div className="flex justify-between items-center text-sm">
            <span className="text-gray-600">Total Subscribers</span>
            <span className="font-medium">{stats?.totalSubscribers || 0}</span>
          </div>
          
          <div className="flex justify-between items-center text-sm">
            <span className="text-gray-600">Unsubscribed</span>
            <span className="font-medium text-red-600">{stats?.unsubscribedCount || 0}</span>
          </div>
        </div>

        {/* Top Sources */}
        {stats && stats.topSources.length > 0 && (
          <div>
            <h4 className="text-sm font-medium text-gray-700 mb-2">Top Sources</h4>
            <div className="space-y-1">
              {stats.topSources.slice(0, 3).map((source, index) => (
                <div key={source.source} className="flex justify-between items-center text-xs">
                  <span className="text-gray-600 capitalize">
                    {source.source.toLowerCase().replace('_', ' ')}
                  </span>
                  <Badge variant="outline" className="text-xs">
                    {source.count}
                  </Badge>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Action Buttons */}
        <div className="pt-2 border-t border-gray-100">
          <div className="grid grid-cols-2 gap-2">
            <Link href="/dashboard/admin-newsletter">
              <Button variant="outline" size="sm" className="w-full">
                <Activity className="w-3 h-3 mr-1" />
                Manage
              </Button>
            </Link>
            
            <Button variant="outline" size="sm" onClick={loadStats} className="w-full">
              <TrendingUp className="w-3 h-3 mr-1" />
              Refresh
            </Button>
          </div>
          
          <Link href="/dashboard/admin-newsletter">
            <Button className="w-full mt-2" size="sm">
              View Full Dashboard
              <ArrowRight className="w-3 h-3 ml-1" />
            </Button>
          </Link>
        </div>

        {/* Quick Insights */}
        {stats && (
          <div className="pt-2 border-t border-gray-100">
            <div className="text-xs text-gray-500 space-y-1">
              <div className="flex items-center gap-1">
                <Target className="w-3 h-3" />
                <span>
                  {stats.topInterests.length > 0 
                    ? `Top interest: ${stats.topInterests[0].interest.toLowerCase()}`
                    : 'No interest data yet'
                  }
                </span>
              </div>
              <div className="flex items-center gap-1">
                <Calendar className="w-3 h-3" />
                <span>
                  {stats.recentSubscriptions > 0 
                    ? `${stats.recentSubscriptions} new subscribers this month`
                    : 'No new subscribers this month'
                  }
                </span>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
