import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Animated,
  Modal,
  ScrollView,
  RefreshControl,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useOffline } from '../providers/OfflineProvider';
import { useTheme } from '../providers/ThemeProvider';

interface OfflineIndicatorProps {
  showDetails?: boolean;
  position?: 'top' | 'bottom';
  style?: any;
}

export default function OfflineIndicator({ 
  showDetails = false, 
  position = 'top',
  style 
}: OfflineIndicatorProps) {
  const { theme } = useTheme();
  const {
    isOffline,
    networkState,
    connectionQuality,
    isSyncing,
    lastSyncResult,
    pendingActionsCount,
    cacheStats,
    forceSync,
    cleanExpiredCache,
  } = useOffline();

  const [showModal, setShowModal] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [fadeAnim] = useState(new Animated.Value(1));

  const getStatusColor = () => {
    if (isOffline) return '#ff4444';
    
    switch (connectionQuality) {
      case 'excellent': return '#00cc44';
      case 'good': return '#88cc00';
      case 'fair': return '#ffaa00';
      case 'poor': return '#ff6600';
      default: return '#ff4444';
    }
  };

  const getStatusIcon = () => {
    if (isOffline) return 'cloud-offline-outline';
    if (isSyncing) return 'sync-outline';
    
    switch (connectionQuality) {
      case 'excellent': return 'wifi-outline';
      case 'good': return 'wifi-outline';
      case 'fair': return 'cellular-outline';
      case 'poor': return 'cellular-outline';
      default: return 'cloud-offline-outline';
    }
  };

  const getStatusText = () => {
    if (isOffline) return 'Offline';
    if (isSyncing) return 'Syncing...';
    
    switch (connectionQuality) {
      case 'excellent': return 'Excellent';
      case 'good': return 'Good';
      case 'fair': return 'Fair';
      case 'poor': return 'Poor';
      default: return 'Offline';
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    try {
      await forceSync();
      await cleanExpiredCache();
    } catch (error) {
      console.error('Error during refresh:', error);
    } finally {
      setRefreshing(false);
    }
  };

  const formatBytes = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDate = (timestamp: number): string => {
    return new Date(timestamp).toLocaleString();
  };

  if (!isOffline && !showDetails) {
    return null; // Don't show indicator when online and details not requested
  }

  const indicatorStyle = [
    styles.indicator,
    {
      backgroundColor: getStatusColor(),
      [position]: 0,
    },
    style,
  ];

  return (
    <>
      <TouchableOpacity
        style={indicatorStyle}
        onPress={() => setShowModal(true)}
        activeOpacity={0.8}
      >
        <Ionicons 
          name={getStatusIcon()} 
          size={16} 
          color="white" 
          style={styles.icon}
        />
        <Text style={styles.statusText}>
          {getStatusText()}
          {pendingActionsCount > 0 && ` (${pendingActionsCount})`}
        </Text>
      </TouchableOpacity>

      <Modal
        visible={showModal}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setShowModal(false)}
      >
        <View style={[styles.modalContainer, { backgroundColor: theme.colors.background }]}>
          <View style={[styles.modalHeader, { backgroundColor: theme.colors.surface }]}>
            <Text style={[styles.modalTitle, { color: theme.colors.text }]}>
              Network & Sync Status
            </Text>
            <TouchableOpacity
              onPress={() => setShowModal(false)}
              style={styles.closeButton}
            >
              <Ionicons name="close" size={24} color={theme.colors.text} />
            </TouchableOpacity>
          </View>

          <ScrollView
            style={styles.modalContent}
            refreshControl={
              <RefreshControl
                refreshing={refreshing}
                onRefresh={handleRefresh}
                tintColor={theme.colors.primary}
              />
            }
          >
            {/* Network Status */}
            <View style={[styles.section, { backgroundColor: theme.colors.surface }]}>
              <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
                Network Status
              </Text>
              
              <View style={styles.statusRow}>
                <Ionicons 
                  name={getStatusIcon()} 
                  size={20} 
                  color={getStatusColor()} 
                />
                <Text style={[styles.statusLabel, { color: theme.colors.text }]}>
                  Connection: {getStatusText()}
                </Text>
              </View>

              <View style={styles.statusRow}>
                <Ionicons name="speedometer-outline" size={20} color={theme.colors.text} />
                <Text style={[styles.statusLabel, { color: theme.colors.text }]}>
                  Signal Strength: {networkState.strength}%
                </Text>
              </View>

              <View style={styles.statusRow}>
                <Ionicons name="information-circle-outline" size={20} color={theme.colors.text} />
                <Text style={[styles.statusLabel, { color: theme.colors.text }]}>
                  Type: {networkState.type}
                </Text>
              </View>
            </View>

            {/* Sync Status */}
            <View style={[styles.section, { backgroundColor: theme.colors.surface }]}>
              <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
                Sync Status
              </Text>
              
              <View style={styles.statusRow}>
                <Ionicons 
                  name={isSyncing ? "sync-outline" : "checkmark-circle-outline"} 
                  size={20} 
                  color={isSyncing ? theme.colors.warning : theme.colors.success} 
                />
                <Text style={[styles.statusLabel, { color: theme.colors.text }]}>
                  Status: {isSyncing ? 'Syncing...' : 'Idle'}
                </Text>
              </View>

              <View style={styles.statusRow}>
                <Ionicons name="list-outline" size={20} color={theme.colors.text} />
                <Text style={[styles.statusLabel, { color: theme.colors.text }]}>
                  Pending Actions: {pendingActionsCount}
                </Text>
              </View>

              {lastSyncResult && (
                <View style={styles.statusRow}>
                  <Ionicons 
                    name={lastSyncResult.success ? "checkmark-circle-outline" : "alert-circle-outline"} 
                    size={20} 
                    color={lastSyncResult.success ? theme.colors.success : theme.colors.error} 
                  />
                  <Text style={[styles.statusLabel, { color: theme.colors.text }]}>
                    Last Sync: {lastSyncResult.success ? 'Success' : 'Failed'} 
                    ({lastSyncResult.syncedActions} synced, {lastSyncResult.failedActions} failed)
                  </Text>
                </View>
              )}
            </View>

            {/* Cache Statistics */}
            {cacheStats && (
              <View style={[styles.section, { backgroundColor: theme.colors.surface }]}>
                <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
                  Cache Statistics
                </Text>
                
                <View style={styles.statusRow}>
                  <Ionicons name="archive-outline" size={20} color={theme.colors.text} />
                  <Text style={[styles.statusLabel, { color: theme.colors.text }]}>
                    Items: {cacheStats.totalItems}
                  </Text>
                </View>

                <View style={styles.statusRow}>
                  <Ionicons name="folder-outline" size={20} color={theme.colors.text} />
                  <Text style={[styles.statusLabel, { color: theme.colors.text }]}>
                    Size: {formatBytes(cacheStats.totalSize)}
                  </Text>
                </View>

                <View style={styles.statusRow}>
                  <Ionicons name="analytics-outline" size={20} color={theme.colors.text} />
                  <Text style={[styles.statusLabel, { color: theme.colors.text }]}>
                    Hit Rate: {cacheStats.hitRate.toFixed(1)}%
                  </Text>
                </View>

                <View style={styles.statusRow}>
                  <Ionicons name="trash-outline" size={20} color={theme.colors.text} />
                  <Text style={[styles.statusLabel, { color: theme.colors.text }]}>
                    Evictions: {cacheStats.evictionCount}
                  </Text>
                </View>
              </View>
            )}

            {/* Actions */}
            <View style={[styles.section, { backgroundColor: theme.colors.surface }]}>
              <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
                Actions
              </Text>
              
              <TouchableOpacity
                style={[styles.actionButton, { backgroundColor: theme.colors.primary }]}
                onPress={handleRefresh}
                disabled={refreshing}
              >
                <Ionicons name="refresh-outline" size={20} color="white" />
                <Text style={styles.actionButtonText}>
                  {refreshing ? 'Refreshing...' : 'Force Sync'}
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.actionButton, { backgroundColor: theme.colors.warning }]}
                onPress={async () => {
                  try {
                    await cleanExpiredCache();
                  } catch (error) {
                    console.error('Error cleaning cache:', error);
                  }
                }}
              >
                <Ionicons name="trash-outline" size={20} color="white" />
                <Text style={styles.actionButtonText}>Clean Cache</Text>
              </TouchableOpacity>
            </View>
          </ScrollView>
        </View>
      </Modal>
    </>
  );
}

const styles = StyleSheet.create({
  indicator: {
    position: 'absolute',
    left: 0,
    right: 0,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
    paddingHorizontal: 16,
    zIndex: 1000,
  },
  icon: {
    marginRight: 8,
  },
  statusText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '600',
  },
  modalContainer: {
    flex: 1,
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingTop: 50,
    paddingBottom: 16,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  closeButton: {
    padding: 8,
  },
  modalContent: {
    flex: 1,
    padding: 16,
  },
  section: {
    marginBottom: 16,
    padding: 16,
    borderRadius: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
  },
  statusRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  statusLabel: {
    marginLeft: 12,
    fontSize: 14,
    flex: 1,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    marginBottom: 8,
  },
  actionButtonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 8,
  },
});
