import planningToolsService from '@/lib/services/planningToolsService';
import { toast } from 'sonner';

export interface MigrationResult {
  success: boolean;
  message: string;
  migratedItems: number;
  errors: string[];
}

export class PlanningDataMigration {
  /**
   * Migrate all planning tools data from localStorage to database
   */
  static async migrateAllData(userId: string): Promise<{
    budget: MigrationResult;
    guestList: MigrationResult;
    overall: MigrationResult;
  }> {
    console.log('Starting planning data migration for user:', userId);

    const results = {
      budget: await this.migrateBudgetData(userId),
      guestList: await this.migrateGuestListData(userId),
      overall: { success: true, message: '', migratedItems: 0, errors: [] }
    };

    // Calculate overall results
    const totalMigrated = results.budget.migratedItems + results.guestList.migratedItems;
    const totalErrors = results.budget.errors.length + results.guestList.errors.length;
    const overallSuccess = results.budget.success && results.guestList.success;

    results.overall = {
      success: overallSuccess,
      message: `Migration completed. ${totalMigrated} items migrated${totalErrors > 0 ? ` with ${totalErrors} errors` : ''}.`,
      migratedItems: totalMigrated,
      errors: [...results.budget.errors, ...results.guestList.errors]
    };

    console.log('Migration results:', results);
    return results;
  }

  /**
   * Migrate budget data from localStorage
   */
  static async migrateBudgetData(userId: string): Promise<MigrationResult> {
    try {
      const budgetData = localStorage.getItem('wedding-budgets');
      
      if (!budgetData) {
        return {
          success: true,
          message: 'No budget data found in localStorage',
          migratedItems: 0,
          errors: []
        };
      }

      const budgets = JSON.parse(budgetData);
      
      if (!Array.isArray(budgets) || budgets.length === 0) {
        return {
          success: true,
          message: 'No valid budget data to migrate',
          migratedItems: 0,
          errors: []
        };
      }

      console.log('Migrating budget data:', budgets);

      const migratedBudgets = await planningToolsService.migrateBudgetFromLocalStorage(userId, budgets);

      // Clear localStorage after successful migration
      localStorage.removeItem('wedding-budgets');

      return {
        success: true,
        message: `Successfully migrated ${migratedBudgets.length} budget(s)`,
        migratedItems: migratedBudgets.length,
        errors: []
      };

    } catch (error) {
      console.error('Error migrating budget data:', error);
      return {
        success: false,
        message: 'Failed to migrate budget data',
        migratedItems: 0,
        errors: [error instanceof Error ? error.message : 'Unknown error']
      };
    }
  }

  /**
   * Migrate guest list data from localStorage
   */
  static async migrateGuestListData(userId: string): Promise<MigrationResult> {
    try {
      const guestData = localStorage.getItem('wedding-guests');
      
      if (!guestData) {
        return {
          success: true,
          message: 'No guest list data found in localStorage',
          migratedItems: 0,
          errors: []
        };
      }

      const guests = JSON.parse(guestData);
      
      if (!Array.isArray(guests) || guests.length === 0) {
        return {
          success: true,
          message: 'No valid guest data to migrate',
          migratedItems: 0,
          errors: []
        };
      }

      console.log('Migrating guest list data:', guests);

      const migratedGuestList = await planningToolsService.migrateGuestListFromLocalStorage(userId, guests);

      // Clear localStorage after successful migration
      localStorage.removeItem('wedding-guests');

      return {
        success: true,
        message: `Successfully migrated guest list with ${guests.length} guest(s)`,
        migratedItems: guests.length,
        errors: []
      };

    } catch (error) {
      console.error('Error migrating guest list data:', error);
      return {
        success: false,
        message: 'Failed to migrate guest list data',
        migratedItems: 0,
        errors: [error instanceof Error ? error.message : 'Unknown error']
      };
    }
  }

  /**
   * Check if user has data in localStorage that needs migration
   */
  static hasDataToMigrate(): {
    hasBudgetData: boolean;
    hasGuestData: boolean;
    hasAnyData: boolean;
    budgetCount: number;
    guestCount: number;
  } {
    const budgetData = localStorage.getItem('wedding-budgets');
    const guestData = localStorage.getItem('wedding-guests');

    let budgetCount = 0;
    let guestCount = 0;

    try {
      if (budgetData) {
        const budgets = JSON.parse(budgetData);
        budgetCount = Array.isArray(budgets) ? budgets.length : 0;
      }
    } catch (error) {
      console.error('Error parsing budget data:', error);
    }

    try {
      if (guestData) {
        const guests = JSON.parse(guestData);
        guestCount = Array.isArray(guests) ? guests.length : 0;
      }
    } catch (error) {
      console.error('Error parsing guest data:', error);
    }

    return {
      hasBudgetData: budgetCount > 0,
      hasGuestData: guestCount > 0,
      hasAnyData: budgetCount > 0 || guestCount > 0,
      budgetCount,
      guestCount
    };
  }

  /**
   * Show migration prompt to user
   */
  static showMigrationPrompt(onMigrate: () => void, onSkip: () => void): void {
    const migrationData = this.hasDataToMigrate();

    if (!migrationData.hasAnyData) {
      return;
    }

    const message = `We found ${migrationData.budgetCount} budget(s) and ${migrationData.guestCount} guest(s) in your local storage. Would you like to save them to your account for better access across devices?`;

    // You can customize this to use your preferred modal/dialog component
    if (confirm(message)) {
      onMigrate();
    } else {
      onSkip();
    }
  }

  /**
   * Perform migration with user feedback
   */
  static async performMigrationWithFeedback(userId: string): Promise<void> {
    try {
      toast.loading('Migrating your planning data...', { id: 'migration' });

      const results = await this.migrateAllData(userId);

      if (results.overall.success) {
        toast.success(results.overall.message, { id: 'migration' });
      } else {
        toast.error(`Migration completed with errors: ${results.overall.errors.join(', ')}`, { id: 'migration' });
      }

      // Show detailed results if needed
      if (results.budget.migratedItems > 0) {
        toast.success(`✅ ${results.budget.message}`);
      }

      if (results.guestList.migratedItems > 0) {
        toast.success(`✅ ${results.guestList.message}`);
      }

    } catch (error) {
      console.error('Migration failed:', error);
      toast.error('Failed to migrate planning data. Please try again.', { id: 'migration' });
    }
  }

  /**
   * Clear all localStorage planning data (use with caution)
   */
  static clearLocalStorageData(): void {
    const keys = ['wedding-budgets', 'wedding-guests'];
    
    keys.forEach(key => {
      localStorage.removeItem(key);
    });

    console.log('Cleared all planning data from localStorage');
  }

  /**
   * Backup localStorage data before migration
   */
  static backupLocalStorageData(): string {
    const backup = {
      budgets: localStorage.getItem('wedding-budgets'),
      guests: localStorage.getItem('wedding-guests'),
      timestamp: new Date().toISOString()
    };

    const backupString = JSON.stringify(backup);
    
    // Save backup to a different localStorage key
    localStorage.setItem('planning-data-backup', backupString);
    
    return backupString;
  }

  /**
   * Restore from backup (if migration fails)
   */
  static restoreFromBackup(): boolean {
    try {
      const backupData = localStorage.getItem('planning-data-backup');
      
      if (!backupData) {
        return false;
      }

      const backup = JSON.parse(backupData);

      if (backup.budgets) {
        localStorage.setItem('wedding-budgets', backup.budgets);
      }

      if (backup.guests) {
        localStorage.setItem('wedding-guests', backup.guests);
      }

      console.log('Restored planning data from backup');
      return true;

    } catch (error) {
      console.error('Error restoring from backup:', error);
      return false;
    }
  }
}
