"use client"

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { Switch } from '@/components/ui/switch'
import { Label } from '@/components/ui/label'
import {
  User,
  Mail,
  Phone,
  MapPin,
  Calendar,
  Camera,
  Save,
  Edit,
  Shield,
  Bell,
  Globe,
  Building,
  Loader2,
  CheckCircle,
  AlertCircle,
  Eye,
  EyeOff
} from 'lucide-react'
import Image from 'next/image'
import { useAuth } from '@/contexts/AuthContext'
import { profileService, UserProfileResponse, UserProfileInput } from '@/lib/services/profileService'
import { showToast } from '@/lib/toast'

export function ProfileManagement() {
  const { user } = useAuth()
  const [profile, setProfile] = useState<UserProfileResponse | null>(null)
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [editing, setEditing] = useState(false)
  const [activeTab, setActiveTab] = useState('personal')
  // Helper for showing image size error
  const [imageError, setImageError] = useState<string | null>(null);

  const [formData, setFormData] = useState<UserProfileInput>({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    dateOfBirth: '',
    gender: undefined,
    address: '',
    city: '',
    state: '',
    pincode: '',
    country: 'India',
    profilePhoto: '',
    bio: '',
    website: '',
    socialMedia: {
      facebook: '',
      instagram: '',
      twitter: '',
      linkedin: '',
      youtube: ''
    },
    preferences: {
      language: 'en',
      currency: 'INR',
      timezone: 'Asia/Kolkata',
      notifications: {
        email: true,
        sms: true,
        push: true,
        marketing: false
      },
      privacy: {
        profileVisibility: 'PUBLIC',
        contactVisibility: 'PUBLIC',
        showOnlineStatus: true
      }
    },
    businessInfo: {
      businessName: '',
      businessType: '',
      businessAddress: '',
      businessPhone: '',
      businessEmail: '',
      businessWebsite: '',
      gstNumber: '',
      panNumber: '',
      businessLicense: ''
    },
    isVendor: false
  })

  // Load profile data
  useEffect(() => {
    loadProfile()
  }, [])

  const loadProfile = async () => {
    try {
      setLoading(true)
      const profileData = await profileService.getCurrentUserProfile()

      if (profileData) {
        setProfile(profileData)
        setFormData({
          firstName: profileData.firstName || '',
          lastName: profileData.lastName || '',
          email: profileData.email || '',
          phone: profileData.phone || '',
          dateOfBirth: profileData.dateOfBirth || '',
          gender: profileData.gender,
          address: profileData.address || '',
          city: profileData.city || '',
          state: profileData.state || '',
          pincode: profileData.pincode || '',
          country: profileData.country || 'India',
          profilePhoto: profileData.profilePhoto || '',
          bio: profileData.bio || '',
          website: profileData.website || '',
          socialMedia: profileData.socialMedia || {
            facebook: '',
            instagram: '',
            twitter: '',
            linkedin: '',
            youtube: ''
          },
          preferences: profileData.preferences || {
            language: 'en',
            currency: 'INR',
            timezone: 'Asia/Kolkata',
            notifications: {
              email: true,
              sms: true,
              push: true,
              marketing: false
            },
            privacy: {
              profileVisibility: 'PUBLIC',
              contactVisibility: 'PUBLIC',
              showOnlineStatus: true
            }
          },
          businessInfo: profileData.businessInfo || {
            businessName: '',
            businessType: '',
            businessAddress: '',
            businessPhone: '',
            businessEmail: '',
            businessWebsite: '',
            gstNumber: '',
            panNumber: '',
            businessLicense: ''
          },
          isVendor: profileData.isVendor || false
        })
      } else {
        // No profile exists, enable editing mode
        setEditing(true)
      }
    } catch (error) {
      console.error('Error loading profile:', error)
      showToast.error('Failed to load profile')
      setEditing(true) // Enable editing if profile doesn't exist
    } finally {
      setLoading(false)
    }
  }

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleNestedInputChange = (parent: string, field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [parent]: {
        ...prev[parent as keyof typeof prev] as any,
        [field]: value
      }
    }))
  }

  const handleSave = async () => {
    try {
      setSaving(true)
      setImageError(null)

      // Validate required fields
      if (!formData.firstName.trim() || !formData.lastName.trim() || !formData.email.trim()) {
        showToast.error('Please fill in all required fields')
        return
      }

      // Validate email format
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      if (!emailRegex.test(formData.email)) {
        showToast.error('Please enter a valid email address')
        return
      }

      // Validate date of birth format if provided
      if (formData.dateOfBirth && !/^\d{4}-\d{2}-\d{2}$/.test(formData.dateOfBirth)) {
        showToast.error('Please enter a valid date of birth')
        return
      }

      // Prepare clean form data (remove empty strings)
      const cleanFormData: UserProfileInput = {
        firstName: formData.firstName.trim() || '',
        lastName: formData.lastName.trim() || '',
        email: formData.email.trim() || '',
        phone: formData.phone?.trim() || undefined,
        dateOfBirth: formData.dateOfBirth || undefined,
        gender: formData.gender,
        address: formData.address?.trim() || undefined,
        city: formData.city?.trim() || undefined,
        state: formData.state?.trim() || undefined,
        pincode: formData.pincode?.trim() || undefined,
        country: formData.country?.trim() || 'India',
        profilePhoto: formData.profilePhoto || undefined,
        bio: formData.bio?.trim() || undefined,
        website: formData.website?.trim() || undefined,
        socialMedia: formData.socialMedia,
        preferences: formData.preferences,
        businessInfo: formData.businessInfo,
        isVendor: formData.isVendor
      }

      if (profile) {
        // Update existing profile
        const updatedProfile = await profileService.updateProfile({
          id: profile.id,
          ...cleanFormData
        })
        setProfile(updatedProfile)
        showToast.success('Profile updated successfully')
      } else {
        // Create new profile
        const newProfile = await profileService.createProfile(cleanFormData)
        setProfile(newProfile)
        showToast.success('Profile created successfully')
      }

      setEditing(false)
    } catch (error) {
      console.error('Error saving profile:', error)
      showToast.error('Failed to save profile')
    } finally {
      setSaving(false)
    }
  }

  const handleCancel = () => {
    if (profile) {
      // Reset form data to original profile data
      loadProfile()
    }
    setEditing(false)
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center p-4 sm:p-8">
        <Loader2 className="w-6 h-6 sm:w-8 sm:h-8 animate-spin text-primary" />
        <span className="ml-2 text-sm sm:text-base">Loading profile...</span>
      </div>
    )
  }

  return (
    <div className="max-w-4xl mx-auto px-3 sm:px-4 lg:px-6 py-4 sm:py-6 lg:py-8">
      {/* Profile Header */}
      <Card className="mb-6 sm:mb-8 shadow-lg rounded-xl sm:rounded-2xl">
        <CardContent className="flex flex-col items-center py-6 sm:py-8 px-4 sm:px-6">
          <div className="relative mb-4 sm:mb-6">
            <Image
              src={profile?.profilePhoto || '/placeholder.svg'}
              alt="Profile"
              width={80}
              height={80}
              className="w-20 h-20 sm:w-24 sm:h-24 lg:w-28 lg:h-28 rounded-full object-cover border-4 border-primary shadow-lg"
            />
            {profile?.isVerified && (
              <CheckCircle className="absolute -bottom-1 -right-1 sm:-bottom-2 sm:-right-2 w-5 h-5 sm:w-6 sm:h-6 lg:w-7 lg:h-7 text-green-600 bg-white rounded-full border-2 border-white" />
            )}
            {editing && (
              <div className="mt-3 flex flex-col items-center">
                <label htmlFor="profilePhotoUpload" className="cursor-pointer px-3 sm:px-4 py-2 bg-primary text-white rounded-full text-xs sm:text-sm font-medium shadow hover:bg-primary/90 transition mb-1 touch-manipulation">
                  Upload Photo
                </label>
                <input
                  id="profilePhotoUpload"
                  type="file"
                  accept="image/*"
                  className="hidden"
                  onChange={e => {
                    setImageError(null);
                    const file = e.target.files?.[0];
                    if (file) {
                      if (file.size > 1024 * 1024) { // 1MB limit
                        setImageError('Image is too large. Please select a file under 1MB.');
                        return;
                      }
                      const reader = new FileReader();
                      reader.onload = async (ev) => {
                        const photoUrl = ev.target?.result;
                        if (photoUrl && typeof photoUrl === 'string' && profile) {
                          try {
                            const updated = await profileService.updateProfilePhoto(profile.id, photoUrl);
                            setProfile(updated);
                            handleInputChange('profilePhoto', photoUrl);
                            showToast.success('Profile photo updated successfully');
                          } catch (err) {
                            showToast.error('Failed to update profile photo');
                          }
                        } else if (typeof photoUrl === 'string') {
                          handleInputChange('profilePhoto', photoUrl);
                        }
                      };
                      reader.readAsDataURL(file);
                    }
                  }}
                />
                <span className="text-xs text-gray-400 text-center">JPG, PNG, or GIF (max 1MB)</span>
                {imageError && <span className="text-xs text-red-500 mt-1 text-center">{imageError}</span>}
              </div>
            )}
          </div>
          <h2 className="text-xl sm:text-2xl lg:text-3xl font-bold text-primary mb-1 text-center">
            {profile?.firstName} {profile?.lastName}
          </h2>
          <p className="text-gray-600 mb-2 text-sm sm:text-base text-center">{profile?.email}</p>
          <div className="flex flex-wrap gap-2 mb-4 justify-center">
            {profile?.isVendor && (
              <Badge variant="secondary" className="text-xs sm:text-sm">Vendor</Badge>
            )}
            {profile?.isVerified && (
              <Badge className="bg-green-100 text-green-800 text-xs sm:text-sm">Verified</Badge>
            )}
          </div>
          <div className="w-full sm:w-auto">
            {editing ? (
              <div className="flex flex-col sm:flex-row gap-2 w-full sm:w-auto">
                <Button 
                  variant="outline" 
                  onClick={handleCancel} 
                  disabled={saving}
                  className="w-full sm:w-auto min-h-[44px] touch-manipulation"
                >
                  Cancel
                </Button>
                <Button 
                  onClick={handleSave} 
                  disabled={saving}
                  className="w-full sm:w-auto min-h-[44px] touch-manipulation"
                >
                  {saving ? (
                    <>
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                      Saving...
                    </>
                  ) : (
                    <>
                      <Save className="w-4 h-4 mr-2" />
                      Save Changes
                    </>
                  )}
                </Button>
              </div>
            ) : (
              <Button 
                onClick={() => setEditing(true)} 
                className="w-full sm:w-auto px-4 sm:px-6 py-2 rounded-full bg-primary text-white font-semibold shadow hover:bg-primary/90 transition-all duration-200 min-h-[44px] touch-manipulation"
              >
                Edit Profile
              </Button>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Tab Navigation */}
      <div className="mb-4 sm:mb-6 border-b overflow-x-auto">
        <nav className="flex gap-4 sm:gap-6 lg:gap-8 justify-start sm:justify-center min-w-max">
          {[
            { id: 'personal', label: 'Personal Info', icon: User },
            { id: 'contact', label: 'Contact', icon: MapPin },
            { id: 'preferences', label: 'Preferences', icon: Bell },
          ].map((tab) => {
            const Icon = tab.icon
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex items-center gap-1 sm:gap-2 py-2 px-1 border-b-2 font-medium text-sm sm:text-base transition-colors duration-200 touch-manipulation min-h-[44px] whitespace-nowrap ${
                  activeTab === tab.id
                    ? 'border-primary text-primary'
                    : 'border-transparent text-gray-500 hover:text-primary'
                }`}
              >
                <Icon className="w-4 h-4 sm:w-5 sm:h-5" />
                <span className="hidden sm:inline">{tab.label}</span>
                <span className="sm:hidden">{tab.label.split(' ')[0]}</span>
              </button>
            )
          })}
        </nav>
      </div>

      {/* Tab Content */}
      <Card className="shadow-lg rounded-xl sm:rounded-2xl">
        <CardContent className="py-4 sm:py-6 lg:py-8 px-4 sm:px-6 lg:px-10">
          {/* Personal Information Tab */}
          {activeTab === 'personal' && (
            <form className="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6">
              <div>
                <Label htmlFor="firstName" className="text-sm sm:text-base">First Name *</Label>
                <Input
                  id="firstName"
                  value={formData.firstName}
                  onChange={(e) => handleInputChange('firstName', e.target.value)}
                  disabled={!editing}
                  placeholder="Enter your first name"
                  className="mt-1 min-h-[44px] text-sm sm:text-base"
                />
              </div>
              <div>
                <Label htmlFor="lastName" className="text-sm sm:text-base">Last Name *</Label>
                <Input
                  id="lastName"
                  value={formData.lastName}
                  onChange={(e) => handleInputChange('lastName', e.target.value)}
                  disabled={!editing}
                  placeholder="Enter your last name"
                  className="mt-1 min-h-[44px] text-sm sm:text-base"
                />
              </div>
              <div className="sm:col-span-2">
                <Label htmlFor="bio" className="text-sm sm:text-base">Bio</Label>
                <Textarea
                  id="bio"
                  value={formData.bio}
                  onChange={(e) => handleInputChange('bio', e.target.value)}
                  disabled={!editing}
                  placeholder="Tell us about yourself..."
                  rows={3}
                  className="mt-1 text-sm sm:text-base"
                />
              </div>
              <div>
                <Label htmlFor="dateOfBirth" className="text-sm sm:text-base">Date of Birth</Label>
                <Input
                  id="dateOfBirth"
                  type="date"
                  value={formData.dateOfBirth}
                  onChange={(e) => handleInputChange('dateOfBirth', e.target.value)}
                  disabled={!editing}
                  className="mt-1 min-h-[44px] text-sm sm:text-base"
                />
              </div>
              <div>
                <Label htmlFor="gender" className="text-sm sm:text-base">Gender</Label>
                <Select
                  value={formData.gender}
                  onValueChange={(value) => handleInputChange('gender', value)}
                  disabled={!editing}
                >
                  <SelectTrigger className="mt-1 min-h-[44px] text-sm sm:text-base">
                    <SelectValue placeholder="Select gender" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="MALE">Male</SelectItem>
                    <SelectItem value="FEMALE">Female</SelectItem>
                    <SelectItem value="OTHER">Other</SelectItem>
                    <SelectItem value="PREFER_NOT_TO_SAY">Prefer not to say</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </form>
          )}

          {/* Contact Tab */}
          {activeTab === 'contact' && (
            <form className="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6">
              <div>
                <Label htmlFor="phone" className="text-sm sm:text-base">Phone Number</Label>
                <Input
                  id="phone"
                  type="tel"
                  value={formData.phone}
                  onChange={(e) => handleInputChange('phone', e.target.value)}
                  disabled={!editing}
                  placeholder="+91 8148376909"
                  className="mt-1 min-h-[44px] text-sm sm:text-base"
                />
              </div>
              <div>
                <Label htmlFor="email" className="text-sm sm:text-base">Email Address *</Label>
                <Input
                  id="email"
                  type="email"
                  value={formData.email}
                  onChange={(e) => handleInputChange('email', e.target.value)}
                  disabled={!editing}
                  placeholder="Enter your email address"
                  className="mt-1 min-h-[44px] text-sm sm:text-base"
                />
              </div>
              <div className="sm:col-span-2">
                <Label htmlFor="address" className="text-sm sm:text-base">Address</Label>
                <Textarea
                  id="address"
                  value={formData.address}
                  onChange={(e) => handleInputChange('address', e.target.value)}
                  disabled={!editing}
                  placeholder="Enter your full address"
                  rows={2}
                  className="mt-1 text-sm sm:text-base"
                />
              </div>
              <div>
                <Label htmlFor="city" className="text-sm sm:text-base">City</Label>
                <Input
                  id="city"
                  value={formData.city}
                  onChange={(e) => handleInputChange('city', e.target.value)}
                  disabled={!editing}
                  placeholder="Enter your city"
                  className="mt-1 min-h-[44px] text-sm sm:text-base"
                />
              </div>
              <div>
                <Label htmlFor="state" className="text-sm sm:text-base">State</Label>
                <Input
                  id="state"
                  value={formData.state}
                  onChange={(e) => handleInputChange('state', e.target.value)}
                  disabled={!editing}
                  placeholder="Enter your state"
                  className="mt-1 min-h-[44px] text-sm sm:text-base"
                />
              </div>
              <div>
                <Label htmlFor="pincode" className="text-sm sm:text-base">Pincode</Label>
                <Input
                  id="pincode"
                  value={formData.pincode}
                  onChange={(e) => handleInputChange('pincode', e.target.value)}
                  disabled={!editing}
                  placeholder="Enter your pincode"
                  className="mt-1 min-h-[44px] text-sm sm:text-base"
                />
              </div>
              <div>
                <Label htmlFor="country" className="text-sm sm:text-base">Country</Label>
                <Input
                  id="country"
                  value={formData.country}
                  onChange={(e) => handleInputChange('country', e.target.value)}
                  disabled={!editing}
                  placeholder="Enter your country"
                  className="mt-1 min-h-[44px] text-sm sm:text-base"
                />
              </div>
            </form>
          )}

          {/* Preferences Tab */}
          {activeTab === 'preferences' && (
            <form className="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6">
              <div>
                <Label htmlFor="language" className="text-sm sm:text-base">Language</Label>
                <Input
                  id="language"
                  value={formData.preferences?.language || ''}
                  onChange={(e) => handleNestedInputChange('preferences', 'language', e.target.value)}
                  disabled={!editing}
                  placeholder="en"
                  className="mt-1 min-h-[44px] text-sm sm:text-base"
                />
              </div>
              <div>
                <Label htmlFor="currency" className="text-sm sm:text-base">Currency</Label>
                <Input
                  id="currency"
                  value={formData.preferences?.currency || ''}
                  onChange={(e) => handleNestedInputChange('preferences', 'currency', e.target.value)}
                  disabled={!editing}
                  placeholder="INR"
                  className="mt-1 min-h-[44px] text-sm sm:text-base"
                />
              </div>
              <div>
                <Label htmlFor="timezone" className="text-sm sm:text-base">Timezone</Label>
                <Input
                  id="timezone"
                  value={formData.preferences?.timezone || ''}
                  onChange={(e) => handleNestedInputChange('preferences', 'timezone', e.target.value)}
                  disabled={!editing}
                  placeholder="Asia/Kolkata"
                  className="mt-1 min-h-[44px] text-sm sm:text-base"
                />
              </div>
              <div className="sm:col-span-2">
                <Label className="text-sm sm:text-base">Notifications</Label>
                <div className="grid grid-cols-2 sm:grid-cols-4 gap-3 sm:gap-4 mt-2">
                  <div className="flex items-center gap-2">
                    <Switch
                      id="notif-email"
                      checked={formData.preferences?.notifications?.email || false}
                      onCheckedChange={(checked) => handleNestedInputChange('preferences', 'notifications', { ...formData.preferences?.notifications, email: checked })}
                      disabled={!editing}
                    />
                    <Label htmlFor="notif-email" className="text-xs sm:text-sm">Email</Label>
                  </div>
                  <div className="flex items-center gap-2">
                    <Switch
                      id="notif-sms"
                      checked={formData.preferences?.notifications?.sms || false}
                      onCheckedChange={(checked) => handleNestedInputChange('preferences', 'notifications', { ...formData.preferences?.notifications, sms: checked })}
                      disabled={!editing}
                    />
                    <Label htmlFor="notif-sms" className="text-xs sm:text-sm">SMS</Label>
                  </div>
                  <div className="flex items-center gap-2">
                    <Switch
                      id="notif-push"
                      checked={formData.preferences?.notifications?.push || false}
                      onCheckedChange={(checked) => handleNestedInputChange('preferences', 'notifications', { ...formData.preferences?.notifications, push: checked })}
                      disabled={!editing}
                    />
                    <Label htmlFor="notif-push" className="text-xs sm:text-sm">Push</Label>
                  </div>
                  <div className="flex items-center gap-2">
                    <Switch
                      id="notif-marketing"
                      checked={formData.preferences?.notifications?.marketing || false}
                      onCheckedChange={(checked) => handleNestedInputChange('preferences', 'notifications', { ...formData.preferences?.notifications, marketing: checked })}
                      disabled={!editing}
                    />
                    <Label htmlFor="notif-marketing" className="text-xs sm:text-sm">Marketing</Label>
                  </div>
                </div>
              </div>
            </form>
          )}
        </CardContent>
      </Card>
    </div>
  )
}

export default ProfileManagement