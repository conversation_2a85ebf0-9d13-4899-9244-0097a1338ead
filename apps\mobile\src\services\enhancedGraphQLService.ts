import { generateClient } from 'aws-amplify/api';
import { networkService } from './networkService';
import { cacheService } from './cacheService';
import { offlineService } from './offlineService';

const client = generateClient();

export interface QueryOptions {
  useCache?: boolean;
  cacheTTL?: number;
  forceRefresh?: boolean;
  priority?: 'low' | 'normal' | 'high';
}

export interface MutationOptions {
  optimistic?: boolean;
  queueOffline?: boolean;
  priority?: 'low' | 'normal' | 'high';
  maxRetries?: number;
}

class EnhancedGraphQLService {
  // Enhanced query method with caching and offline support
  async query<T>(
    query: string,
    variables: any = {},
    options: QueryOptions = {}
  ): Promise<T | null> {
    const {
      useCache = true,
      cacheTTL = 30 * 60 * 1000, // 30 minutes default
      forceRefresh = false,
      priority = 'normal',
    } = options;

    const cacheKey = this.generateCacheKey(query, variables);

    try {
      // Check cache first if not forcing refresh and cache is enabled
      if (useCache && !forceRefresh) {
        const cachedData = await cacheService.get<T>(cacheKey);
        if (cachedData) {
          console.log('Returning cached data for:', cacheKey);
          return cachedData;
        }
      }

      // If offline, try to return cached data or offline data
      if (networkService.isOffline()) {
        console.log('Offline: attempting to return cached data');
        const cachedData = await cacheService.get<T>(cacheKey);
        if (cachedData) {
          return cachedData;
        }

        // Try to get from offline data store
        const offlineData = await offlineService.getOfflineData();
        const entityData = this.extractEntityFromOfflineData(query, variables, offlineData);
        if (entityData) {
          return entityData as T;
        }

        throw new Error('No cached data available while offline');
      }

      // Execute the query
      console.log('Executing GraphQL query:', query.substring(0, 100) + '...');
      const result = await client.graphql({
        query,
        variables,
      });

      const data = (result.data as any)?.[Object.keys(result.data as any)[0]];

      // Cache the result if caching is enabled
      if (useCache && data) {
        await cacheService.set(cacheKey, data, {
          ttl: cacheTTL,
          priority,
        });
      }

      return data;
    } catch (error) {
      console.error('GraphQL query error:', error);

      // If online query fails, try to return cached data as fallback
      if (useCache) {
        const cachedData = await cacheService.get<T>(cacheKey);
        if (cachedData) {
          console.log('Returning cached data as fallback');
          return cachedData;
        }
      }

      throw error;
    }
  }

  // Enhanced mutation method with offline queueing and optimistic updates
  async mutate<T>(
    mutation: string,
    variables: any = {},
    options: MutationOptions = {}
  ): Promise<T | null> {
    const {
      optimistic = false,
      queueOffline = true,
      priority = 'normal',
      maxRetries = 3,
    } = options;

    try {
      // If offline and queueing is enabled, queue the mutation
      if (networkService.isOffline() && queueOffline) {
        const entityInfo = this.extractEntityInfoFromMutation(mutation, variables);
        
        await offlineService.queueAction({
          type: entityInfo.type,
          entity: entityInfo.entity,
          data: variables,
          priority,
          maxRetries,
        });

        console.log('Queued offline mutation:', entityInfo);

        // Return optimistic result if enabled
        if (optimistic) {
          return this.generateOptimisticResult<T>(mutation, variables);
        }

        return null;
      }

      // Execute the mutation
      console.log('Executing GraphQL mutation:', mutation.substring(0, 100) + '...');
      const result = await client.graphql({
        query: mutation,
        variables,
      });

      const data = (result.data as any)?.[Object.keys(result.data as any)[0]];

      // Invalidate related cache entries
      await this.invalidateRelatedCache(mutation, variables);

      return data;
    } catch (error) {
      console.error('GraphQL mutation error:', error);

      // If mutation fails and we're online, optionally queue for retry
      if (!networkService.isOffline() && queueOffline) {
        const entityInfo = this.extractEntityInfoFromMutation(mutation, variables);
        
        await offlineService.queueAction({
          type: entityInfo.type,
          entity: entityInfo.entity,
          data: variables,
          priority,
          maxRetries,
        });

        console.log('Queued failed mutation for retry:', entityInfo);
      }

      throw error;
    }
  }

  // Vendor operations with enhanced offline support
  async getVendor(id: string, options: QueryOptions = {}) {
    const query = `
      query GetVendor($id: ID!) {
        getVendor(id: $id) {
          id
          userId
          name
          description
          contact
          email
          address
          city
          state
          pincode
          website
          category
          profilePhoto
          gallery
          services
          socialMedia
          experience
          events
          responseTime
          rating
          reviewCount
          verified
          featured
          availability
          priceRange
          specializations
          awards
          languages
          coverage
          equipment
          status
          createdAt
          updatedAt
        }
      }
    `;
    return this.query(query, { id }, options);
  }

  async listVendors(filter: any = {}, limit: number = 20, nextToken?: string, options: QueryOptions = {}) {
    const query = `
      query ListVendors($filter: ModelVendorFilterInput, $limit: Int, $nextToken: String) {
        listVendors(filter: $filter, limit: $limit, nextToken: $nextToken) {
          items {
            id
            name
            description
            contact
            email
            city
            state
            category
            profilePhoto
            rating
            reviewCount
            verified
            featured
            priceRange
            createdAt
          }
          nextToken
        }
      }
    `;
    return this.query(query, { filter, limit, nextToken }, options);
  }

  async createVendor(input: any, options: MutationOptions = {}) {
    const mutation = `
      mutation CreateVendor($input: CreateVendorInput!) {
        createVendor(input: $input) {
          id
          name
          description
          contact
          email
          city
          state
          category
          profilePhoto
          createdAt
        }
      }
    `;
    return this.mutate(mutation, { input }, options);
  }

  // Venue operations
  async getVenue(id: string, options: QueryOptions = {}) {
    const query = `
      query GetVenue($id: ID!) {
        getVenue(id: $id) {
          id
          name
          description
          contact
          email
          address
          city
          state
          pincode
          website
          venueType
          capacity
          pricing
          amenities
          gallery
          availability
          rating
          reviewCount
          verified
          featured
          createdAt
          updatedAt
        }
      }
    `;
    return this.query(query, { id }, options);
  }

  async listVenues(filter: any = {}, limit: number = 20, nextToken?: string, options: QueryOptions = {}) {
    const query = `
      query ListVenues($filter: ModelVenueFilterInput, $limit: Int, $nextToken: String) {
        listVenues(filter: $filter, limit: $limit, nextToken: $nextToken) {
          items {
            id
            name
            description
            contact
            email
            city
            state
            venueType
            capacity
            pricing
            gallery
            rating
            reviewCount
            verified
            featured
            createdAt
          }
          nextToken
        }
      }
    `;
    return this.query(query, { filter, limit, nextToken }, options);
  }

  // Shop operations
  async getShop(id: string, options: QueryOptions = {}) {
    const query = `
      query GetShop($id: ID!) {
        getShop(id: $id) {
          id
          name
          description
          contact
          email
          address
          city
          state
          category
          products
          gallery
          rating
          reviewCount
          verified
          featured
          createdAt
          updatedAt
        }
      }
    `;
    return this.query(query, { id }, options);
  }

  // Utility methods
  private generateCacheKey(query: string, variables: any): string {
    const queryName = this.extractQueryName(query);
    const variablesHash = JSON.stringify(variables);
    return `${queryName}_${Buffer.from(variablesHash).toString('base64').substring(0, 20)}`;
  }

  private extractQueryName(query: string): string {
    const match = query.match(/(?:query|mutation)\s+(\w+)/);
    return match ? match[1] : 'unknown';
  }

  private extractEntityInfoFromMutation(mutation: string, variables: any): { type: 'CREATE' | 'UPDATE' | 'DELETE'; entity: string } {
    const mutationName = this.extractQueryName(mutation).toLowerCase();
    
    let type: 'CREATE' | 'UPDATE' | 'DELETE' = 'CREATE';
    if (mutationName.includes('update')) type = 'UPDATE';
    else if (mutationName.includes('delete')) type = 'DELETE';
    
    let entity = 'unknown';
    if (mutationName.includes('vendor')) entity = 'vendor';
    else if (mutationName.includes('venue')) entity = 'venue';
    else if (mutationName.includes('shop')) entity = 'shop';
    else if (mutationName.includes('order')) entity = 'order';
    else if (mutationName.includes('review')) entity = 'review';
    
    return { type, entity };
  }

  private extractEntityFromOfflineData(query: string, variables: any, offlineData: any): any {
    const queryName = this.extractQueryName(query).toLowerCase();
    
    if (queryName.includes('vendor')) {
      if (variables.id) {
        return offlineData.vendors?.find((v: any) => v.id === variables.id);
      }
      return { items: offlineData.vendors || [], nextToken: null };
    }
    
    if (queryName.includes('venue')) {
      if (variables.id) {
        return offlineData.venues?.find((v: any) => v.id === variables.id);
      }
      return { items: offlineData.venues || [], nextToken: null };
    }
    
    if (queryName.includes('shop')) {
      if (variables.id) {
        return offlineData.shops?.find((s: any) => s.id === variables.id);
      }
      return { items: offlineData.shops || [], nextToken: null };
    }
    
    return null;
  }

  private generateOptimisticResult<T>(mutation: string, variables: any): T | null {
    // Generate optimistic results for mutations
    const mutationName = this.extractQueryName(mutation).toLowerCase();
    
    if (mutationName.includes('create')) {
      return {
        id: `temp_${Date.now()}`,
        ...variables.input,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      } as T;
    }
    
    return null;
  }

  private async invalidateRelatedCache(mutation: string, variables: any): Promise<void> {
    const entityInfo = this.extractEntityInfoFromMutation(mutation, variables);
    
    // Remove related cache entries
    const patterns = [
      `list${entityInfo.entity}s`,
      `get${entityInfo.entity}`,
    ];
    
    // This is a simplified cache invalidation
    // In a real implementation, you'd want more sophisticated cache invalidation
    console.log('Invalidating cache for patterns:', patterns);
  }
}

// Singleton instance
export const enhancedGraphQLService = new EnhancedGraphQLService();
export default EnhancedGraphQLService;
