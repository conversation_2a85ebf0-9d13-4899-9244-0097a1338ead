"use client"

import { Suspense } from 'react'
import dynamic from 'next/dynamic'

/**
 * Dynamic Page Wrapper to prevent static generation issues
 * This forces pages to be rendered dynamically, avoiding useSearchParams Suspense issues
 */

interface DynamicPageWrapperProps {
  children: React.ReactNode
  fallback?: React.ReactNode
}

function PageContent({ children }: { children: React.ReactNode }) {
  return <>{children}</>
}

// Force dynamic rendering
const DynamicContent = dynamic(() => Promise.resolve(PageContent), {
  ssr: false,
  loading: () => (
    <div className="min-h-screen flex items-center justify-center">
      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
    </div>
  )
})

export function DynamicPageWrapper({ children, fallback }: DynamicPageWrapperProps) {
  return (
    <Suspense fallback={fallback || (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    )}>
      <DynamicContent>{children}</DynamicContent>
    </Suspense>
  )
}

export default DynamicPageWrapper
