// Platform detection utilities for cross-platform compatibility

import { PlatformInfo } from '../types';

// Detect current platform
export function getPlatformInfo(): PlatformInfo {
  const isReactNative = typeof navigator !== 'undefined' && navigator.product === 'ReactNative';
  const isWeb = typeof window !== 'undefined' && !isReactNative;
  const isMobile = isReactNative;
  const hasSessionStorage = typeof sessionStorage !== 'undefined';
  const hasAsyncStorage = (() => {
    try {
      require('@react-native-async-storage/async-storage');
      return true;
    } catch {
      return false;
    }
  })();

  return {
    isWeb,
    isMobile,
    isReactNative,
    hasSessionStorage,
    hasAsyncStorage,
  };
}

// Platform-specific checks
export const Platform = {
  isWeb: typeof window !== 'undefined' && typeof navigator !== 'undefined' && navigator.product !== 'ReactNative',
  isMobile: typeof navigator !== 'undefined' && navigator.product === 'ReactNative',
  isReactNative: typeof navigator !== 'undefined' && navigator.product === 'ReactNative',
  isServer: typeof window === 'undefined',
  
  // Feature detection
  hasSessionStorage: (() => {
    try {
      return typeof sessionStorage !== 'undefined';
    } catch {
      return false;
    }
  })(),
  
  hasLocalStorage: (() => {
    try {
      return typeof localStorage !== 'undefined';
    } catch {
      return false;
    }
  })(),
  
  hasAsyncStorage: (() => {
    try {
      require('@react-native-async-storage/async-storage');
      return true;
    } catch {
      return false;
    }
  })(),
};

// Safe API wrappers that work across platforms
export const SafeAPI = {
  // Safe sessionStorage wrapper
  sessionStorage: {
    getItem: (key: string): string | null => {
      if (!Platform.hasSessionStorage) return null;
      try {
        return sessionStorage.getItem(key);
      } catch {
        return null;
      }
    },
    
    setItem: (key: string, value: string): boolean => {
      if (!Platform.hasSessionStorage) return false;
      try {
        sessionStorage.setItem(key, value);
        return true;
      } catch {
        return false;
      }
    },
    
    removeItem: (key: string): boolean => {
      if (!Platform.hasSessionStorage) return false;
      try {
        sessionStorage.removeItem(key);
        return true;
      } catch {
        return false;
      }
    },
    
    clear: (): boolean => {
      if (!Platform.hasSessionStorage) return false;
      try {
        sessionStorage.clear();
        return true;
      } catch {
        return false;
      }
    }
  },

  // Safe localStorage wrapper
  localStorage: {
    getItem: (key: string): string | null => {
      if (!Platform.hasLocalStorage) return null;
      try {
        return localStorage.getItem(key);
      } catch {
        return null;
      }
    },
    
    setItem: (key: string, value: string): boolean => {
      if (!Platform.hasLocalStorage) return false;
      try {
        localStorage.setItem(key, value);
        return true;
      } catch {
        return false;
      }
    },
    
    removeItem: (key: string): boolean => {
      if (!Platform.hasLocalStorage) return false;
      try {
        localStorage.removeItem(key);
        return true;
      } catch {
        return false;
      }
    },
    
    clear: (): boolean => {
      if (!Platform.hasLocalStorage) return false;
      try {
        localStorage.clear();
        return true;
      } catch {
        return false;
      }
    }
  },

  // Safe console wrapper (some environments might not have console)
  console: {
    log: (...args: any[]) => {
      if (typeof console !== 'undefined' && console.log) {
        console.log(...args);
      }
    },
    
    warn: (...args: any[]) => {
      if (typeof console !== 'undefined' && console.warn) {
        console.warn(...args);
      }
    },
    
    error: (...args: any[]) => {
      if (typeof console !== 'undefined' && console.error) {
        console.error(...args);
      }
    }
  }
};

// Platform-specific component helpers
export const PlatformComponent = {
  // Check if component should render on current platform
  shouldRender: (platforms: ('web' | 'mobile')[]): boolean => {
    if (platforms.includes('web') && Platform.isWeb) return true;
    if (platforms.includes('mobile') && Platform.isMobile) return true;
    return false;
  },
  
  // Get platform-specific styles
  getStyles: <T>(webStyles: T, mobileStyles: T): T => {
    return Platform.isWeb ? webStyles : mobileStyles;
  }
};

export default Platform;
