name: E2E Tests

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]
  schedule:
    # Run tests daily at 2 AM UTC
    - cron: '0 2 * * *'

jobs:
  test:
    timeout-minutes: 60
    runs-on: ubuntu-latest
    
    strategy:
      fail-fast: false
      matrix:
        project: [chromium, firefox, webkit]
    
    steps:
    - uses: actions/checkout@v4
    
    - uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci --legacy-peer-deps
    
    - name: Install Playwright Browsers
      run: npx playwright install --with-deps ${{ matrix.project }}
    
    - name: Run Playwright tests
      run: npx playwright test --project=${{ matrix.project }}
      env:
        CI: true
    
    - uses: actions/upload-artifact@v4
      if: always()
      with:
        name: playwright-report-${{ matrix.project }}
        path: playwright-report/
        retention-days: 30

  mobile-test:
    timeout-minutes: 60
    runs-on: ubuntu-latest
    
    strategy:
      fail-fast: false
      matrix:
        project: ['Mobile Chrome', 'Mobile Safari', 'Tablet Chrome']
    
    steps:
    - uses: actions/checkout@v4
    
    - uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci --legacy-peer-deps
    
    - name: Install Playwright Browsers
      run: npx playwright install --with-deps chromium webkit
    
    - name: Run Mobile Playwright tests
      run: npx playwright test --project="${{ matrix.project }}"
      env:
        CI: true
    
    - uses: actions/upload-artifact@v4
      if: always()
      with:
        name: playwright-report-mobile-${{ matrix.project }}
        path: playwright-report/
        retention-days: 30

  smoke-test:
    timeout-minutes: 30
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci --legacy-peer-deps
    
    - name: Install Playwright Browsers
      run: npx playwright install --with-deps chromium
    
    - name: Run Smoke tests
      run: npx playwright test --project=chromium --grep="@smoke"
      env:
        CI: true
    
    - uses: actions/upload-artifact@v4
      if: always()
      with:
        name: playwright-report-smoke
        path: playwright-report/
        retention-days: 30

  test-report:
    if: always()
    needs: [test, mobile-test, smoke-test]
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Download all artifacts
      uses: actions/download-artifact@v4
      with:
        path: artifacts
    
    - name: Merge test reports
      run: |
        mkdir -p merged-report
        find artifacts -name "*.json" -exec cp {} merged-report/ \;
    
    - name: Upload merged report
      uses: actions/upload-artifact@v4
      with:
        name: merged-playwright-report
        path: merged-report/
        retention-days: 30
