"use client"

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { Home, Search, ShoppingCart, User, Heart, ShoppingBag  } from 'lucide-react'
import { cn } from '@/lib/utils'
import { useCart } from '@/components/ClientRoot'
import { useAuth } from '@/contexts/AuthContext'
import { usePWADetection } from '@/hooks/use-pwa-detection'
import { ClientOnly } from '@/components/client-only'

const navItems = [
  {
    href: '/',
    icon: Home,
    label: 'Home',
    activePattern: /^\/$/
  },
  {
    href: '/vendors',
    icon: Search,
    label: 'Vendors',
    activePattern: /^\/vendors/
  },
  {
    href: '/cart',
    icon: ShoppingCart,
    label: 'Cart',
    activePattern: /^\/cart/,
    showBadge: true
  },
  {
    href: '/shop',
    icon: ShoppingBag,
    label: 'Shop',
    activePattern: /^\/shop/
  },
  {
    href: '/dashboard',
    icon: User,
    label: 'Account',
    activePattern: /^\/dashboard|^\/login|^\/signup/
  }
]

export default function MobileBottomNav() {
  const pathname = usePathname()
  const { getTotalItems } = useCart()
  const { userProfile, isAuthenticated } = useAuth()
  const { isStandalone } = usePWADetection()
  const [cartCount, setCartCount] = useState(0)
  const [isVisible, setIsVisible] = useState(true)
  const [lastScrollY, setLastScrollY] = useState(0)

  // Update cart count
  useEffect(() => {
    setCartCount(getTotalItems)
  }, [getTotalItems])

  // Hide/show nav on scroll
  useEffect(() => {
    const handleScroll = () => {
      const currentScrollY = window.scrollY
      
      if (currentScrollY > lastScrollY && currentScrollY > 100) {
        // Scrolling down
        setIsVisible(false)
      } else {
        // Scrolling up
        setIsVisible(true)
      }
      
      setLastScrollY(currentScrollY)
    }

    window.addEventListener('scroll', handleScroll, { passive: true })
    return () => window.removeEventListener('scroll', handleScroll)
  }, [lastScrollY])

  // Don't show on desktop
  const [isMobile, setIsMobile] = useState(false)
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768)
    }
    
    checkMobile()
    window.addEventListener('resize', checkMobile)
    return () => window.removeEventListener('resize', checkMobile)
  }, [])

  if (!isMobile) return null

  return (
    <nav
      className={cn(
        'fixed bottom-0 left-0 right-0 z-50 bg-white border-t border-gray-200 transition-transform duration-300',
        isVisible ? 'translate-y-0' : 'translate-y-full',
        // Add extra padding for PWA standalone mode
        isStandalone ? 'pb-safe' : ''
      )}
    >
      <div className="flex items-center justify-around py-2 px-4 safe-area-pb">
        {navItems.map((item) => {
          const Icon = item.icon
          const isActive = item.activePattern.test(pathname)
          
          return (
            <Link
              key={item.href}
              href={item.href}
              className={cn(
                'flex flex-col items-center justify-center min-w-0 flex-1 py-2 px-1 relative',
                'transition-colors duration-200',
                isActive
                  ? 'text-primary'
                  : 'text-gray-500 hover:text-gray-700'
              )}
            >
              <div className="relative">
                <Icon className="h-5 w-5" />
                <ClientOnly>
                  {item.showBadge && cartCount > 0 && (
                    <span className="absolute -top-2 -right-2 h-4 w-4 bg-red-500 text-white text-xs rounded-full flex items-center justify-center font-medium">
                      {cartCount > 9 ? '9+' : cartCount}
                    </span>
                  )}
                </ClientOnly>
              </div>
              <span className="text-xs mt-1 truncate max-w-full">
                <ClientOnly fallback={item.label}>
                  {item.label === 'Account' && isAuthenticated && userProfile ? (
                    userProfile.firstName || 'Account'
                  ) : (
                    item.label
                  )}
                </ClientOnly>
              </span>
              {isActive && (
                <div className="absolute top-0 left-1/2 transform -translate-x-1/2 w-1 h-1 bg-primary rounded-full" />
              )}
            </Link>
          )
        })}
      </div>
    </nav>
  )
}
