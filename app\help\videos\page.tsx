"use client"

import { <PERSON>Header } from "@/components/top-header"
import { <PERSON><PERSON> } from "@/components/header"
import { <PERSON><PERSON> } from "@/components/footer"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { SimpleSEO } from "@/components/seo/SimpleSEO"
import { 
  ArrowLeft,
  Play,
  Clock,
  Users,
  Search,
  MapPin,
  ShoppingBag,
  Calendar,
  Star,
  Settings,
  Video
} from "lucide-react"
import Link from "next/link"
import { useHelpTranslations } from '@/lib/universal-i18n'

export default function VideoTutorialsPage() {
  const helpT = useHelpTranslations()
  const videoCategories = [
    {
      id: 'getting-started',
      title: 'Getting Started',
      icon: Users,
      description: 'Learn the basics of using BookmyFestive',
      videos: [
        {
          title: 'Creating Your Account',
          duration: '3:45',
          description: 'Step-by-step guide to sign up and set up your profile',
          thumbnail: '/videos/thumbnails/signup.jpg',
          level: 'Beginner',
          views: '12.5K'
        },
        {
          title: 'Understanding User Types',
          duration: '2:30',
          description: 'Difference between Customer and Vendor accounts',
          thumbnail: '/videos/thumbnails/user-types.jpg',
          level: 'Beginner',
          views: '8.2K'
        },
        {
          title: 'Profile Setup Best Practices',
          duration: '5:20',
          description: 'How to create an attractive and complete profile',
          thumbnail: '/videos/thumbnails/profile-setup.jpg',
          level: 'Beginner',
          views: '15.3K'
        },
        {
          title: 'Getting Started Tutorial 1',
          duration: 'TBD',
          description: 'Essential basics for new users on BookmyFestive',
          thumbnail: '/videos/thumbnails/getting-started-1.jpg',
          level: 'Beginner',
          views: 'New',
          isExternal: true,
          externalUrl: 'https://youtu.be/NPdURMa2mOI',
          embedId: 'NPdURMa2mOI'
        },
        {
          title: 'Getting Started Tutorial 2',
          duration: 'TBD',
          description: 'Advanced features and tips for BookmyFestive',
          thumbnail: '/videos/thumbnails/getting-started-2.jpg',
          level: 'Beginner',
          views: 'New',
          isExternal: true,
          externalUrl: 'https://youtu.be/FY409xGdpZA',
          embedId: 'FY409xGdpZA'
        },
        {
          title: 'Getting Started Tutorial 3',
          duration: 'TBD',
          description: 'Wedding planning workflow and best practices',
          thumbnail: '/videos/thumbnails/getting-started-3.jpg',
          level: 'Beginner',
          views: 'New',
          isExternal: true,
          externalUrl: 'https://youtu.be/r1YbUbey6Kw',
          embedId: 'r1YbUbey6Kw'
        },
        {
          title: 'Getting Started Tutorial 4',
          duration: 'TBD',
          description: 'Complete guide to using all BookmyFestive features',
          thumbnail: '/videos/thumbnails/getting-started-4.jpg',
          level: 'Beginner',
          views: 'New',
          isExternal: true,
          externalUrl: 'https://youtu.be/oyEa0yNf6ww',
          embedId: 'oyEa0yNf6ww'
        }
      ]
    },
    {
      id: 'finding-vendors',
      title: 'Finding Vendors',
      icon: Search,
      description: 'Master the art of finding the perfect vendors',
      videos: [
        {
          title: 'Advanced Search Techniques',
          duration: '4:15',
          description: 'Use filters and search options effectively',
          thumbnail: '/videos/thumbnails/search.jpg',
          level: 'Intermediate',
          views: '9.7K'
        },
        {
          title: 'Reading Vendor Profiles',
          duration: '3:50',
          description: 'What to look for in vendor profiles and portfolios',
          thumbnail: '/videos/thumbnails/vendor-profiles.jpg',
          level: 'Beginner',
          views: '11.2K'
        },
        {
          title: 'Contacting Vendors Effectively',
          duration: '6:10',
          description: 'How to write inquiries that get responses',
          thumbnail: '/videos/thumbnails/contact-vendors.jpg',
          level: 'Intermediate',
          views: '7.8K'
        }
      ]
    },
    {
      id: 'booking-venues',
      title: 'Booking Venues',
      icon: MapPin,
      description: 'Everything about finding and booking venues',
      videos: [
        {
          title: 'Venue Search Tips',
          duration: '5:30',
          description: 'How to find venues that match your vision and budget',
          thumbnail: '/videos/thumbnails/venue-search.jpg',
          level: 'Beginner',
          views: '13.4K'
        },
        {
          title: 'Site Visit Checklist',
          duration: '7:20',
          description: 'What to check during venue visits',
          thumbnail: '/videos/thumbnails/site-visit.jpg',
          level: 'Intermediate',
          views: '6.9K'
        },
        {
          title: 'Booking Process Walkthrough',
          duration: '4:45',
          description: 'Step-by-step venue booking process',
          thumbnail: '/videos/thumbnails/booking-process.jpg',
          level: 'Intermediate',
          views: '8.5K'
        }
      ]
    },
    {
      id: 'wedding-planning',
      title: 'Wedding Planning Tools',
      icon: Calendar,
      description: 'Make the most of our planning features',
      videos: [
        {
          title: 'Wedding Checklist Mastery',
          duration: '6:00',
          description: 'How to use and customize your wedding checklist',
          thumbnail: '/videos/thumbnails/checklist.jpg',
          level: 'Beginner',
          views: '16.7K'
        },
        {
          title: 'Budget Planning Made Easy',
          duration: '8:15',
          description: 'Set up and track your wedding budget effectively',
          thumbnail: '/videos/thumbnails/budget.jpg',
          level: 'Intermediate',
          views: '14.2K'
        },
        {
          title: 'Guest List Management',
          duration: '5:40',
          description: 'Organize and manage your wedding guest list',
          thumbnail: '/videos/thumbnails/guest-list.jpg',
          level: 'Beginner',
          views: '10.1K'
        }
      ]
    },
    {
      id: 'vendor-features',
      title: 'For Vendors',
      icon: Star,
      description: 'Grow your wedding business on our platform',
      videos: [
        {
          title: 'Creating a Winning Profile',
          duration: '9:30',
          description: 'How to create a profile that attracts customers',
          thumbnail: '/videos/thumbnails/vendor-profile.jpg',
          level: 'Beginner',
          views: '5.8K'
        },
        {
          title: 'Managing Inquiries',
          duration: '6:45',
          description: 'Best practices for responding to customer inquiries',
          thumbnail: '/videos/thumbnails/manage-inquiries.jpg',
          level: 'Intermediate',
          views: '4.3K'
        },
        {
          title: 'Building Your Reputation',
          duration: '7:20',
          description: 'How to get more reviews and grow your business',
          thumbnail: '/videos/thumbnails/reputation.jpg',
          level: 'Advanced',
          views: '3.9K'
        }
      ]
    }
  ]

  const featuredVideos = [
    {
      title: 'Complete Wedding Planning Guide',
      duration: '15:30',
      description: 'Everything you need to know about planning your wedding on BookmyFestive',
      thumbnail: '/videos/thumbnails/complete-guide.jpg',
      level: 'All Levels',
      views: '25.8K',
      featured: true
    },
    {
      title: 'Vendor Success Stories',
      duration: '12:45',
      description: 'Real vendors share how they grew their business with us',
      thumbnail: '/videos/thumbnails/success-stories.jpg',
      level: 'All Levels',
      views: '18.3K',
      featured: true
    },
    {
      title: 'Wedding Planning Tips & Tricks',
      duration: '8:32',
      description: 'Essential tips for planning the perfect wedding celebration',
      thumbnail: '/videos/thumbnails/wedding-tips.jpg',
      level: 'All Levels',
      views: '22.1K',
      featured: true,
      isExternal: true,
      externalUrl: 'https://www.youtube.com/watch?v=42Jn6wdGL68'
    }
  ]

  const externalVideos = [
    {
      title: 'Wedding Planning Tips & Tricks',
      duration: '8:32',
      description: 'Essential tips for planning the perfect wedding celebration from our YouTube channel',
      thumbnail: '/videos/thumbnails/wedding-tips.jpg',
      level: 'All Levels',
      views: '22.1K',
      source: 'YouTube',
      externalUrl: 'https://www.youtube.com/watch?v=42Jn6wdGL68',
      embedId: '42Jn6wdGL68'
    },
    {
      title: 'Getting Started with BookmyFestive',
      duration: 'TBD',
      description: 'Essential basics for new users starting their wedding planning journey',
      thumbnail: '/videos/thumbnails/getting-started-1.jpg',
      level: 'Beginner',
      views: 'New',
      source: 'YouTube',
      externalUrl: 'https://youtu.be/NPdURMa2mOI',
      embedId: 'NPdURMa2mOI'
    },
    {
      title: 'Advanced BookmyFestive Features',
      duration: 'TBD',
      description: 'Discover advanced features and tips to make the most of BookmyFestive',
      thumbnail: '/videos/thumbnails/getting-started-2.jpg',
      level: 'Intermediate',
      views: 'New',
      source: 'YouTube',
      externalUrl: 'https://youtu.be/FY409xGdpZA',
      embedId: 'FY409xGdpZA'
    },
    {
      title: 'Wedding Planning Workflow Guide',
      duration: 'TBD',
      description: 'Complete workflow and best practices for planning your perfect wedding',
      thumbnail: '/videos/thumbnails/getting-started-3.jpg',
      level: 'Intermediate',
      views: 'New',
      source: 'YouTube',
      externalUrl: 'https://youtu.be/r1YbUbey6Kw',
      embedId: 'r1YbUbey6Kw'
    },
    {
      title: 'Complete BookmyFestive Tutorial',
      duration: 'TBD',
      description: 'Comprehensive guide covering all BookmyFestive features and capabilities',
      thumbnail: '/videos/thumbnails/getting-started-4.jpg',
      level: 'All Levels',
      views: 'New',
      source: 'YouTube',
      externalUrl: 'https://youtu.be/oyEa0yNf6ww',
      embedId: 'oyEa0yNf6ww'
    }
  ]

  return (
    <>
      <SimpleSEO
        title="Video Tutorials - BookmyFestive Help"
        description="Watch step-by-step video tutorials to learn how to use BookmyFestive. From account setup to wedding planning, our videos guide you through every feature."
        keywords="video tutorials, BookmyFestive videos, wedding planning tutorials, how to use BookmyFestive"
        url="/help/videos"
      />
      
      <div className="min-h-screen bg-background">
        <TopHeader />
        <Header />
        
        {/* Breadcrumb */}
        <div className="bg-muted/30 py-4">
          <div className="container mx-auto px-4">
            <div className="flex items-center gap-2 text-sm">
              <Link href="/help" className="text-muted-foreground hover:text-foreground">
                Help Center
              </Link>
              <span className="text-muted-foreground">/</span>
              <span className="text-foreground">Video Tutorials</span>
            </div>
          </div>
        </div>

        {/* Header */}
        <section className="py-12">
          <div className="container mx-auto px-4">
            <Link href="/help" className="inline-flex items-center gap-2 text-primary hover:underline mb-6">
              <ArrowLeft className="w-4 h-4" />
              Back to Help Center
            </Link>
            
            <div className="text-center mb-12">
              <Video className="w-16 h-16 mx-auto mb-6 text-primary" />
              <h1 className="text-4xl font-bold mb-4">{helpT.videos?.title || 'Video Tutorials'}</h1>
              <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
                {helpT.videos?.subtitle || 'Learn how to use BookmyFestive with our comprehensive video guides. From basic setup to advanced features, we\'ve got you covered.'}
              </p>
            </div>
          </div>
        </section>

        {/* Featured Videos */}
        <section className="py-8">
          <div className="container mx-auto px-4">
            <h2 className="text-2xl font-bold mb-6">Featured Videos</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
              {featuredVideos.map((video, index) => (
                <Card key={index} className="overflow-hidden hover:shadow-lg transition-shadow">
                  <div className="relative">
                    {video.isExternal ? (
                      <div className="aspect-video bg-muted flex items-center justify-center">
                        <div className="text-center">
                          <Play className="w-16 h-16 text-primary mx-auto mb-2" />
                          <p className="text-sm text-muted-foreground">External Video</p>
                        </div>
                      </div>
                    ) : (
                      <div className="aspect-video bg-muted flex items-center justify-center">
                        <Play className="w-16 h-16 text-primary" />
                      </div>
                    )}
                    <Badge className="absolute top-2 right-2 bg-red-600">
                      Featured
                    </Badge>
                    {video.isExternal && (
                      <Badge className="absolute top-2 left-2 bg-blue-600">
                        YouTube
                      </Badge>
                    )}
                    <div className="absolute bottom-2 right-2 bg-black/70 text-white px-2 py-1 rounded text-sm">
                      {video.duration}
                    </div>
                  </div>
                  <CardContent className="p-4">
                    <h3 className="font-semibold mb-2">{video.title}</h3>
                    <p className="text-sm text-muted-foreground mb-3">{video.description}</p>
                    <div className="flex items-center justify-between text-xs text-muted-foreground mb-3">
                      <span>{video.views} views</span>
                      <Badge variant="outline">{video.level}</Badge>
                    </div>
                    {video.isExternal && (
                      <Button 
                        variant="outline" 
                        size="sm" 
                        className="w-full"
                        onClick={() => window.open(video.externalUrl, '_blank')}
                      >
                        Watch on YouTube
                      </Button>
                    )}
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* External Video Content */}
        <section className="py-8">
          <div className="container mx-auto px-4">
            <h2 className="text-2xl font-bold mb-6">External Video Content</h2>
            <p className="text-muted-foreground mb-6 max-w-3xl">
              Discover additional wedding planning content from our YouTube channel and trusted partners.
            </p>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {externalVideos.map((video, index) => (
                <Card key={index} className="overflow-hidden">
                  <CardHeader className="pb-4">
                    <div className="flex items-center gap-2 mb-2">
                      <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
                        {video.source}
                      </Badge>
                      <Badge variant="outline">{video.level}</Badge>
                    </div>
                    <CardTitle className="text-xl">{video.title}</CardTitle>
                    <p className="text-muted-foreground">{video.description}</p>
                  </CardHeader>
                  
                  <CardContent className="space-y-4">
                    {/* YouTube Embed */}
                    <div className="aspect-video bg-muted rounded-lg overflow-hidden">
                      <iframe
                        width="100%"
                        height="100%"
                        src={`https://www.youtube.com/embed/${video.embedId}`}
                        title={video.title}
                        frameBorder="0"
                        allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                        allowFullScreen
                        className="w-full h-full"
                      />
                    </div>
                    
                    <div className="flex items-center justify-between text-sm text-muted-foreground">
                      <span>{video.views} views</span>
                      <span>{video.duration}</span>
                    </div>
                    
                    <div className="flex gap-3">
                      <Button 
                        className="flex-1"
                        onClick={() => window.open(video.externalUrl, '_blank')}
                      >
                        Watch on YouTube
                      </Button>
                      <Button 
                        variant="outline"
                        onClick={() => {
                          navigator.clipboard.writeText(video.externalUrl);
                          // You can add a toast notification here
                        }}
                      >
                        Copy Link
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Video Categories */}
        <section className="py-8">
          <div className="container mx-auto px-4">
            <h2 className="text-2xl font-bold mb-8">Browse by Category</h2>
            
            {videoCategories.map((category) => (
              <div key={category.id} className="mb-12">
                <div className="flex items-center gap-3 mb-6">
                  <category.icon className="w-8 h-8 text-primary" />
                  <div>
                    <h3 className="text-xl font-semibold">{category.title}</h3>
                    <p className="text-muted-foreground">{category.description}</p>
                  </div>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {category.videos.map((video, index) => (
                    <Card key={index} className="overflow-hidden hover:shadow-lg transition-shadow cursor-pointer">
                      <div className="relative">
                        {video.isExternal ? (
                          <div className="aspect-video bg-muted flex items-center justify-center">
                            <div className="text-center">
                              <Play className="w-12 h-12 text-primary mx-auto mb-2" />
                              <p className="text-xs text-muted-foreground">YouTube Video</p>
                            </div>
                          </div>
                        ) : (
                          <div className="aspect-video bg-muted flex items-center justify-center">
                            <Play className="w-12 h-12 text-primary" />
                          </div>
                        )}
                        {video.isExternal && (
                          <Badge className="absolute top-2 left-2 bg-blue-600">
                            YouTube
                          </Badge>
                        )}
                        <div className="absolute bottom-2 right-2 bg-black/70 text-white px-2 py-1 rounded text-sm">
                          {video.duration}
                        </div>
                      </div>
                      <CardContent className="p-4">
                        <h4 className="font-semibold mb-2">{video.title}</h4>
                        <p className="text-sm text-muted-foreground mb-3">{video.description}</p>
                        <div className="flex items-center justify-between text-xs text-muted-foreground mb-3">
                          <span>{video.views} views</span>
                          <Badge variant="outline">{video.level}</Badge>
                        </div>
                        {video.isExternal && (
                          <Button
                            variant="outline"
                            size="sm"
                            className="w-full"
                            onClick={() => window.open(video.externalUrl, '_blank')}
                          >
                            Watch on YouTube
                          </Button>
                        )}
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </section>

        {/* Coming Soon */}
        <section className="py-16 bg-muted/30">
          <div className="container mx-auto px-4 text-center">
            <h2 className="text-3xl font-bold mb-6">More Videos Coming Soon!</h2>
            <p className="text-xl text-muted-foreground mb-8 max-w-2xl mx-auto">
              We're constantly creating new video tutorials based on user feedback. 
              Have a specific topic you'd like us to cover?
            </p>
            <Button size="lg">
              Request a Tutorial
            </Button>
          </div>
        </section>

        <Footer />
      </div>
    </>
  )
}
