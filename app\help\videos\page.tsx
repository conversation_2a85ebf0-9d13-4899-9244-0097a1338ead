"use client"

import { <PERSON>Header } from "@/components/top-header"
import { <PERSON><PERSON> } from "@/components/header"
import { Foot<PERSON> } from "@/components/footer"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { SimpleSEO } from "@/components/seo/SimpleSEO"
import { 
  ArrowLeft,
  Play,
  Clock,
  Users,
  Search,
  MapPin,
  ShoppingBag,
  Calendar,
  Star,
  Settings,
  Video,
  Copy,
  ExternalLink,
  Eye,
  Share2,
  Heart
} from "lucide-react"
import Link from "next/link"
import { useHelpTranslations } from '@/lib/universal-i18n'
import { useState, useEffect } from 'react'

interface VideoData {
  title: string
  duration: string
  description: string
  level: string
  views: string
  featured: boolean
  isExternal: boolean
  externalUrl: string
  embedId: string
  source: string
  realViews?: string
  realDuration?: string
  realTitle?: string
  realDescription?: string
  thumbnail?: string
}

export default function VideoTutorialsPage() {
  const helpT = useHelpTranslations()
  const [copiedLink, setCopiedLink] = useState<string | null>(null)
  const [likedVideos, setLikedVideos] = useState<Set<number>>(new Set())
  const [videos, setVideos] = useState<VideoData[]>([])
  const [loading, setLoading] = useState(true)

  const initialVideos: VideoData[] = [
    {
      title: 'Wedding Planning Tips & Tricks',
      duration: '8:32',
      description: 'Essential tips for planning the perfect wedding celebration from our YouTube channel',
      level: 'All Levels',
      views: '22.1K',
      featured: true,
      isExternal: true,
      externalUrl: 'https://youtu.be/NPdURMa2mOI?si=65_VHurlWIgCWo0C',
      embedId: 'NPdURMa2mOI',
      source: 'YouTube'
    },
    {
      title: 'Getting Started with BookmyFestive',
      duration: 'TBD',
      description: 'Essential basics for new users starting their wedding planning journey',
      level: 'Beginner',
      views: 'New',
      featured: true,
      isExternal: true,
      externalUrl: 'https://youtu.be/FY409xGdpZA?si=fjnuXJU1PQG5L7XN',
      embedId: 'FY409xGdpZA',
      source: 'YouTube'
    },
    {
      title: 'Advanced BookmyFestive Features',
      duration: 'TBD',
      description: 'Discover advanced features and tips to make the most of BookmyFestive',
      level: 'Intermediate',
      views: 'New',
      featured: true,
      isExternal: true,
      externalUrl: 'https://youtu.be/r1YbUbey6Kw?si=9fUQSEjelUyyhN_d',
      embedId: 'r1YbUbey6Kw',
      source: 'YouTube'
    },
    {
      title: 'Vendor Registration Guide',
      duration: 'TBD',
      description: 'Complete guide to registering as a vendor on BookmyFestive',
      level: 'Beginner',
      views: 'New',
      featured: false,
      isExternal: true,
      externalUrl: 'https://youtu.be/NPdURMa2mOI?si=65_VHurlWIgCWo0C',
      embedId: 'NPdURMa2mOI',
      source: 'YouTube'
    },
    {
      title: 'Customer Login Tutorial Video',
      duration: 'TBD',
      description: 'Step-by-step tutorial for customer login and account setup',
      level: 'Beginner',
      views: 'New',
      featured: false,
      isExternal: true,
      externalUrl: 'https://youtu.be/FY409xGdpZA?si=fjnuXJU1PQG5L7XN',
      embedId: 'FY409xGdpZA',
      source: 'YouTube'
    },
    {
      title: 'Vendor Booking Tutorial',
      duration: 'TBD',
      description: 'Learn how to book vendors effectively on our platform',
      level: 'Intermediate',
      views: 'New',
      featured: false,
      isExternal: true,
      externalUrl: 'https://youtu.be/oyEa0yNf6ww?si=eed7XdrhbMePBkcZ',
      embedId: 'oyEa0yNf6ww',
      source: 'YouTube'
    },
    {
      title: 'Order a Product on BookmyFestive',
      duration: 'TBD',
      description: 'Complete tutorial on how to order products through our platform',
      level: 'Beginner',
      views: 'New',
      featured: false,
      isExternal: true,
      externalUrl: 'https://youtu.be/JWMF3Jic2Yw?si=VGaeIGNC99WtWqY7',
      embedId: 'JWMF3Jic2Yw',
      source: 'YouTube'
    },
    {
      title: 'Budget Calculator Tool Tutorial',
      duration: 'TBD',
      description: 'Master the budget calculator tool for wedding planning',
      level: 'Intermediate',
      views: 'New',
      featured: false,
      isExternal: true,
      externalUrl: 'https://youtu.be/HOGq6bKr4EU?si=vrUfCoSGAqHfUFpu',
      embedId: 'HOGq6bKr4EU',
      source: 'YouTube'
    },
    {
      title: 'Check List Tool Tutorial',
      duration: 'TBD',
      description: 'How to use the wedding checklist tool effectively',
      level: 'Beginner',
      views: 'New',
      featured: false,
      isExternal: true,
      externalUrl: 'https://youtu.be/2uXonWtCIoA?si=nwDbw4g3eEhHH1dL',
      embedId: '2uXonWtCIoA',
      source: 'YouTube'
    },
    {
      title: 'Guest List Tool Tutorial',
      duration: 'TBD',
      description: 'Complete guide to managing your wedding guest list',
      level: 'Beginner',
      views: 'New',
      featured: false,
      isExternal: true,
      externalUrl: 'https://youtu.be/DSdvP63fmJM?si=k80K03Fu0qGnYsKj',
      embedId: 'DSdvP63fmJM',
      source: 'YouTube'
    },
    {
      title: 'Blog Posting Tutorial',
      duration: 'TBD',
      description: 'Learn how to create and publish blog posts on BookmyFestive',
      level: 'Intermediate',
      views: 'New',
      featured: false,
      isExternal: true,
      externalUrl: 'https://youtu.be/cwGbDvF5dr8?si=RxXCyzVteLJnudLa',
      embedId: 'cwGbDvF5dr8',
      source: 'YouTube'
    }
  ]

  // Fetch real YouTube data
  useEffect(() => {
    const fetchYouTubeData = async () => {
      setLoading(true)
      const updatedVideos = [...initialVideos]
      
      try {
        // Fetch data for each video
        const promises = updatedVideos.map(async (video, index) => {
          try {
            const response = await fetch(`/api/youtube-stats/${video.embedId}`)
            if (response.ok) {
              const data = await response.json()
              updatedVideos[index] = {
                ...video,
                realViews: data.views,
                realDuration: data.duration,
                realTitle: data.title,
                realDescription: data.description,
                thumbnail: data.thumbnail
              }
            }
          } catch (error) {
            console.error(`Failed to fetch data for video ${video.embedId}:`, error)
          }
        })
        
        await Promise.all(promises)
        setVideos(updatedVideos)
      } catch (error) {
        console.error('Error fetching YouTube data:', error)
        setVideos(initialVideos)
      } finally {
        setLoading(false)
      }
    }

    fetchYouTubeData()
  }, [])

  const featuredVideos = videos.filter(video => video.featured)
  const additionalVideos = videos.filter(video => !video.featured)

  const handleCopyLink = async (url: string) => {
    try {
      await navigator.clipboard.writeText(url)
      setCopiedLink(url)
      setTimeout(() => setCopiedLink(null), 2000)
    } catch (err) {
      console.error('Failed to copy link:', err)
    }
  }

  const handleShare = async (video: VideoData) => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: video.realTitle || video.title,
          text: video.realDescription || video.description,
          url: video.externalUrl,
        })
      } catch (err) {
        console.error('Error sharing:', err)
      }
    } else {
      // Fallback to copy link
      handleCopyLink(video.externalUrl)
    }
  }

  const toggleLike = (index: number) => {
    const newLikedVideos = new Set(likedVideos)
    if (newLikedVideos.has(index)) {
      newLikedVideos.delete(index)
    } else {
      newLikedVideos.add(index)
    }
    setLikedVideos(newLikedVideos)
  }

  const getLevelColor = (level: string) => {
    switch (level) {
      case 'Beginner':
        return 'bg-amber-50 text-amber-700 border-amber-200'
      case 'Intermediate':
        return 'bg-blue-50 text-blue-700 border-blue-200'
      case 'Advanced':
        return 'bg-purple-50 text-purple-700 border-purple-200'
      case 'All Levels':
        return 'bg-gray-50 text-gray-700 border-gray-200'
      default:
        return 'bg-gray-50 text-gray-700 border-gray-200'
    }
  }

  // Function to truncate long titles
  const truncateTitle = (title: string, maxLength: number = 50) => {
    if (title.length <= maxLength) return title
    
    // Try to break at a word boundary
    const truncated = title.substring(0, maxLength)
    const lastSpace = truncated.lastIndexOf(' ')
    
    if (lastSpace > 0) {
      return truncated.substring(0, lastSpace) + '...'
    }
    
    return truncated + '...'
  }

  // Function to get clean, short title
  const getCleanTitle = (title: string) => {
    // Remove common prefixes and suffixes
    let cleanTitle = title
      .replace(/^Customer Login Demo Video - /i, '')
      .replace(/^Vendor Registration Guide - /i, '')
      .replace(/^BookmyFestive/i, '')
      .replace(/^Welcome to /i, '')
      .replace(/^In this video/i, '')
      .replace(/^In this demo/i, '')
      .replace(/^Whether you're planning/i, '')
      .replace(/^Visit us today/i, '')
      .replace(/^Plan your dream event/i, '')
      .replace(/^#BookmyFestive.*$/i, '') // Remove hashtags
      .replace(/^🎉.*$/i, '') // Remove emoji lines
      .replace(/^📲.*$/i, '') // Remove emoji lines
      .replace(/^✅.*$/i, '') // Remove emoji lines
      .replace(/^🔑.*$/i, '') // Remove emoji lines
      .trim()
    
    // If title is still too long, truncate it
    return truncateTitle(cleanTitle, 60)
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-muted/30 flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-primary border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading video data...</p>
        </div>
      </div>
    )
  }

  return (
    <>
      <SimpleSEO
        title="Video Tutorials - BookmyFestive Help"
        description="Watch step-by-step video tutorials to learn how to use BookmyFestive. From account setup to wedding planning, our videos guide you through every feature."
        keywords="video tutorials, BookmyFestive videos, wedding planning tutorials, how to use BookmyFestive"
        url="/help/videos"
      />
      
      <div className="min-h-screen bg-muted/30">
        <TopHeader />
        <Header />
        
        {/* Breadcrumb */}
        <div className="bg-background/50 py-3 sm:py-4 border-b border-border/20">
          <div className="container mx-auto px-4">
            <div className="flex items-center gap-2 text-xs sm:text-sm">
              <Link href="/help" className="text-muted-foreground hover:text-foreground transition-colors">
                Help Center
              </Link>
              <span className="text-muted-foreground">/</span>
              <span className="text-foreground font-medium">Video Tutorials</span>
            </div>
          </div>
        </div>

        {/* Header */}
        <section className="py-6 sm:py-8 md:py-12">
          <div className="container mx-auto px-4">
            <Link href="/help" className="inline-flex items-center gap-2 text-primary hover:text-primary/80 transition-colors mb-4 sm:mb-6 group text-sm sm:text-base">
              <ArrowLeft className="w-4 h-4 group-hover:-translate-x-1 transition-transform" />
              <span className="group-hover:underline">Back to Help Center</span>
            </Link>

            <div className="text-center mb-6 sm:mb-8">
              <div className="w-16 h-16 sm:w-20 sm:h-20 bg-gradient-to-br from-primary to-primary/80 rounded-full flex items-center justify-center mx-auto mb-4 sm:mb-6 md:mb-8 shadow-lg">
                <Video className="w-8 h-8 sm:w-10 sm:h-10 text-primary-foreground" />
              </div>
              <h1 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold mb-3 sm:mb-4 md:mb-6 text-foreground leading-tight px-2">
                Video Tutorials
              </h1>
              <p className="text-base sm:text-lg md:text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed px-2">
                Learn how to use BookmyFestive with our comprehensive video guides. From basic setup to advanced features, we've got you covered.
              </p>
            </div>
          </div>
        </section>

        {/* Featured Videos Section */}
        <section className="py-8 sm:py-12">
          <div className="container mx-auto px-4">
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 lg:gap-8">
              {featuredVideos.map((video, index) => (
                <Card key={index} className="overflow-hidden hover:shadow-xl transition-all duration-300 bg-card border-0 shadow-lg hover:-translate-y-1 group">
                  {/* Video Thumbnail with Play Button */}
                  <div className="relative aspect-video bg-gradient-to-br from-muted to-muted/80 overflow-hidden">
                    {/* YouTube Embed */}
                    <iframe
                      width="100%"
                      height="100%"
                      src={`https://www.youtube.com/embed/${video.embedId}`}
                      title={getCleanTitle(video.realTitle || video.title)}
                      style={{ border: 0 }}
                      allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                      allowFullScreen
                      className="w-full h-full"
                    />
                    
                    {/* Top Right Corner Icons */}
                    <div className="absolute top-2 sm:top-3 right-2 sm:right-3 flex items-center gap-1 sm:gap-2">
                      {/* Share Icon */}
                      <button
                        onClick={() => handleShare(video)}
                        className="w-6 h-6 sm:w-8 sm:h-8 bg-background/90 rounded-full flex items-center justify-center hover:bg-background transition-colors"
                      >
                        <Share2 className="w-3 h-3 sm:w-4 sm:h-4 text-muted-foreground" />
                      </button>
                    </div>
                  </div>

                  {/* Card Content */}
                  <CardContent className="p-3 sm:p-4 md:p-6">
                    {/* Tags */}
                    <div className="flex items-center gap-1 sm:gap-2 mb-3 sm:mb-4 flex-wrap">
                      <Badge className="bg-blue-100 text-blue-700 border-blue-200 text-xs font-semibold px-2 sm:px-3 py-1">
                        {video.source}
                      </Badge>
                      <Badge className={`${getLevelColor(video.level)} text-xs font-semibold px-2 sm:px-3 py-1`}>
                        {video.level}
                      </Badge>
                    </div>

                    {/* Title */}
                    <h3 className="font-bold text-base sm:text-lg md:text-xl text-card-foreground mb-2 sm:mb-3 leading-tight group-hover:text-primary transition-colors">
                      {getCleanTitle(video.realTitle || video.title)}
                    </h3>

                    {/* Description */}
                    <p className="text-muted-foreground text-xs sm:text-sm mb-3 sm:mb-4 md:mb-5 leading-relaxed line-clamp-2 sm:line-clamp-3">
                      {video.realDescription || video.description}
                    </p>

                    {/* Metadata */}
                    <div className="flex items-center justify-between text-xs sm:text-sm text-muted-foreground mb-3 sm:mb-4 md:mb-5">
                      <span className="flex items-center gap-1 sm:gap-1.5 font-medium">
                        <Eye className="w-3 h-3 sm:w-4 sm:h-4" />
                        <span className="truncate">{video.realViews || video.views} views</span>
                      </span>
                      <span className="flex items-center gap-1 sm:gap-1.5 font-medium">
                        <Clock className="w-3 h-3 sm:w-4 sm:h-4" />
                        <span className="truncate">{video.realDuration || video.duration}</span>
                      </span>
                    </div>

                    {/* Action Buttons */}
                    <div className="flex flex-col sm:flex-row gap-2 sm:gap-3">
                      <Button
                        className="flex-1 bg-primary hover:bg-primary/90 text-primary-foreground font-semibold py-2 sm:py-2.5 text-sm sm:text-base transition-all duration-200 hover:shadow-lg"
                        onClick={() => window.open(video.externalUrl, '_blank')}
                      >
                        <ExternalLink className="w-3 h-3 sm:w-4 sm:h-4 mr-1 sm:mr-2" />
                        Watch on YouTube
                      </Button>
                      <Button
                        variant="outline"
                        className="flex-1 border-border text-foreground hover:bg-muted/50 font-semibold py-2 sm:py-2.5 text-sm sm:text-base transition-all duration-200 hover:border-primary"
                        onClick={() => handleCopyLink(video.externalUrl)}
                      >
                        <Copy className="w-3 h-3 sm:w-4 sm:h-4 mr-1 sm:mr-2" />
                        {copiedLink === video.externalUrl ? 'Copied!' : 'Copy Link'}
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Additional Videos Section */}
        <section className="py-8 sm:py-12 md:py-16 bg-background/50">
          <div className="container mx-auto px-4">
            <div className="text-center mb-8 sm:mb-12">
              <h2 className="text-2xl sm:text-3xl font-bold text-foreground mb-3 sm:mb-4 px-2">More Tutorials</h2>
              <p className="text-base sm:text-lg text-muted-foreground max-w-2xl mx-auto px-2">
                Explore our comprehensive collection of video guides to master every aspect of BookmyFestive
              </p>
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
              {additionalVideos.map((video, index) => (
                <Card key={index} className="overflow-hidden hover:shadow-xl transition-all duration-300 bg-card border-0 shadow-lg hover:-translate-y-1 group">
                  {/* Video Thumbnail with Play Button */}
                  <div className="relative aspect-video bg-gradient-to-br from-muted to-muted/80 overflow-hidden">
                    {/* YouTube Embed */}
                    <iframe
                      width="100%"
                      height="100%"
                      src={`https://www.youtube.com/embed/${video.embedId}`}
                      title={getCleanTitle(video.realTitle || video.title)}
                      style={{ border: 0 }}
                      allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                      allowFullScreen
                      className="w-full h-full"
                    />
                    
                    {/* Top Right Corner Icons */}
                    <div className="absolute top-2 right-2 flex items-center gap-1">
                      {/* Share Icon */}
                      <button
                        onClick={() => handleShare(video)}
                        className="w-7 h-7 bg-background/90 rounded-full flex items-center justify-center hover:bg-background transition-colors"
                      >
                        <Share2 className="w-3.5 h-3.5 text-muted-foreground" />
                      </button>
                    </div>
                  </div>

                  {/* Card Content */}
                  <CardContent className="p-5">
                    {/* Tags */}
                    <div className="flex items-center gap-2 mb-3">
                      <Badge className="bg-blue-100 text-blue-700 border-blue-200 text-xs font-semibold px-2 py-1">
                        {video.source}
                      </Badge>
                      <Badge className={`${getLevelColor(video.level)} text-xs font-semibold px-2 py-1`}>
                        {video.level}
                      </Badge>
                    </div>

                    {/* Title */}
                    <h3 className="font-bold text-lg text-card-foreground mb-2 leading-tight group-hover:text-primary transition-colors line-clamp-2">
                      {getCleanTitle(video.realTitle || video.title)}
                    </h3>

                    {/* Description */}
                    <p className="text-muted-foreground text-sm mb-4 leading-relaxed line-clamp-3">
                      {video.realDescription || video.description}
                    </p>

                    {/* Metadata */}
                    <div className="flex items-center justify-between text-sm text-muted-foreground mb-4">
                      <span className="flex items-center gap-1.5 font-medium">
                        <Eye className="w-4 h-4" />
                        {video.realViews || video.views} views
                      </span>
                      <span className="flex items-center gap-1.5 font-medium">
                        <Clock className="w-4 h-4" />
                        {video.realDuration || video.duration}
                      </span>
                    </div>

                    {/* Action Buttons */}
                    <div className="flex gap-2">
                      <Button 
                        className="flex-1 bg-primary hover:bg-primary/90 text-primary-foreground font-semibold py-2 text-sm transition-all duration-200 hover:shadow-lg"
                        onClick={() => window.open(video.externalUrl, '_blank')}
                      >
                        <ExternalLink className="w-4 h-4 mr-1.5" />
                        Watch
                      </Button>
                      <Button 
                        variant="outline"
                        size="sm"
                        className="border-border text-foreground hover:bg-muted/50 font-semibold transition-all duration-200 hover:border-primary"
                        onClick={() => handleCopyLink(video.externalUrl)}
                      >
                        <Copy className="w-4 h-4" />
                        {copiedLink === video.externalUrl ? 'Copied!' : ''}
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Coming Soon Section */}
        <section className="py-12 sm:py-16 md:py-20 bg-background">
          <div className="container mx-auto px-4 text-center">
            <div className="w-12 h-12 sm:w-16 sm:h-16 bg-gradient-to-br from-primary to-primary/80 rounded-full flex items-center justify-center mx-auto mb-6 sm:mb-8 shadow-lg">
              <Video className="w-6 h-6 sm:w-8 sm:h-8 text-primary-foreground" />
            </div>
            <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold mb-4 sm:mb-6 text-foreground px-2">More Videos Coming Soon!</h2>
            <p className="text-base sm:text-lg md:text-xl text-muted-foreground mb-6 sm:mb-8 md:mb-10 max-w-3xl mx-auto leading-relaxed px-2">
              We're constantly creating new video tutorials based on user feedback.
              Have a specific topic you'd like us to cover?
            </p>
            <Button
              size="lg"
              className="bg-primary hover:bg-primary/90 text-primary-foreground font-semibold px-6 sm:px-8 py-2.5 sm:py-3 text-base sm:text-lg hover:shadow-xl transition-all duration-200 w-full sm:w-auto max-w-xs"
            >
              Request a Tutorial
            </Button>
          </div>
        </section>

        <Footer />
      </div>
    </>
  )
}
