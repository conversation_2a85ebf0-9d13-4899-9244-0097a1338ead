"use client"

import { TopHeader } from "@/components/top-header"
import { <PERSON><PERSON> } from "@/components/header"
import { Footer } from "@/components/footer"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { SimpleSEO } from "@/components/seo/SimpleSEO"
import { 
  ArrowLeft,
  Play,
  Clock,
  Users,
  Search,
  MapPin,
  ShoppingBag,
  Calendar,
  Star,
  Settings,
  Video
} from "lucide-react"
import Link from "next/link"
import { useHelpTranslations } from '@/lib/universal-i18n'

export default function VideoTutorialsPage() {
  const helpT = useHelpTranslations()
  const videoCategories: any[] = []

  const featuredVideos = [
    {
      title: 'Vendor Registration Guide',
      duration: 'TBD',
      description: 'Complete guide to registering as a vendor on BookmyFestive',
      level: 'Beginner',
      views: 'New',
      featured: true,
      isExternal: true,
      externalUrl: 'https://youtu.be/NPdURMa2mOI?si=65_VHurlWIgCWo0C',
      embedId: 'NPdURMa2mOI'
    },
    {
      title: 'Customer Login Tutorial Video',
      duration: 'TBD',
      description: 'Step-by-step tutorial for customer login and account setup',
      level: 'Beginner',
      views: 'New',
      featured: true,
      isExternal: true,
      externalUrl: 'https://youtu.be/FY409xGdpZA?si=fjnuXJU1PQG5L7XN',
      embedId: 'FY409xGdpZA'
    },
    {
      title: 'Over All View of BookmyFestive',
      duration: 'TBD',
      description: 'Comprehensive overview of all BookmyFestive features and capabilities',
      level: 'All Levels',
      views: 'New',
      featured: true,
      isExternal: true,
      externalUrl: 'https://youtu.be/r1YbUbey6Kw?si=9fUQSEjelUyyhN_d',
      embedId: 'r1YbUbey6Kw'
    },
    {
      title: 'Vendor Booking Tutorial',
      duration: 'TBD',
      description: 'Learn how to book vendors effectively on our platform',
      level: 'Intermediate',
      views: 'New',
      featured: true,
      isExternal: true,
      externalUrl: 'https://youtu.be/oyEa0yNf6ww?si=eed7XdrhbMePBkcZ',
      embedId: 'oyEa0yNf6ww'
    },
    {
      title: 'Order a Product on BookmyFestive',
      duration: 'TBD',
      description: 'Complete tutorial on how to order products through our platform',
      level: 'Beginner',
      views: 'New',
      featured: true,
      isExternal: true,
      externalUrl: 'https://youtu.be/JWMF3Jic2Yw?si=VGaeIGNC99WtWqY7',
      embedId: 'JWMF3Jic2Yw'
    },
    {
      title: 'Budget Calculator Tool Tutorial',
      duration: 'TBD',
      description: 'Master the budget calculator tool for wedding planning',
      level: 'Intermediate',
      views: 'New',
      featured: true,
      isExternal: true,
      externalUrl: 'https://youtu.be/HOGq6bKr4EU?si=vrUfCoSGAqHfUFpu',
      embedId: 'HOGq6bKr4EU'
    },
    {
      title: 'Check List Tool Tutorial',
      duration: 'TBD',
      description: 'How to use the wedding checklist tool effectively',
      level: 'Beginner',
      views: 'New',
      featured: true,
      isExternal: true,
      externalUrl: 'https://youtu.be/2uXonWtCIoA?si=nwDbw4g3eEhHH1dL',
      embedId: '2uXonWtCIoA'
    },
    {
      title: 'Guest List Tool Tutorial',
      duration: 'TBD',
      description: 'Complete guide to managing your wedding guest list',
      level: 'Beginner',
      views: 'New',
      featured: true,
      isExternal: true,
      externalUrl: 'https://youtu.be/DSdvP63fmJM?si=k80K03Fu0qGnYsKj',
      embedId: 'DSdvP63fmJM'
    },
    {
      title: 'Blog Posting Tutorial',
      duration: 'TBD',
      description: 'Learn how to create and publish blog posts on BookmyFestive',
      level: 'Intermediate',
      views: 'New',
      featured: true,
      isExternal: true,
      externalUrl: 'https://youtu.be/cwGbDvF5dr8?si=RxXCyzVteLJnudLa',
      embedId: 'cwGbDvF5dr8'
    }
  ]

  const externalVideos: any[] = []

  return (
    <>
      <SimpleSEO
        title="Video Tutorials - BookmyFestive Help"
        description="Watch step-by-step video tutorials to learn how to use BookmyFestive. From account setup to wedding planning, our videos guide you through every feature."
        keywords="video tutorials, BookmyFestive videos, wedding planning tutorials, how to use BookmyFestive"
        url="/help/videos"
      />
      
      <div className="min-h-screen bg-background">
        <TopHeader />
        <Header />
        
        {/* Breadcrumb */}
        <div className="bg-muted/30 py-4">
          <div className="container mx-auto px-4">
            <div className="flex items-center gap-2 text-sm">
              <Link href="/help" className="text-muted-foreground hover:text-foreground">
                Help Center
              </Link>
              <span className="text-muted-foreground">/</span>
              <span className="text-foreground">Video Tutorials</span>
            </div>
          </div>
        </div>

        {/* Header */}
        <section className="py-12">
          <div className="container mx-auto px-4">
            <Link href="/help" className="inline-flex items-center gap-2 text-primary hover:underline mb-6">
              <ArrowLeft className="w-4 h-4" />
              Back to Help Center
            </Link>
            
            <div className="text-center mb-12">
              <Video className="w-16 h-16 mx-auto mb-6 text-primary" />
              <h1 className="text-4xl font-bold mb-4">{helpT.videos?.title || 'Video Tutorials'}</h1>
              <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
                {helpT.videos?.subtitle || 'Learn how to use BookmyFestive with our comprehensive video guides. From basic setup to advanced features, we\'ve got you covered.'}
              </p>
            </div>
          </div>
        </section>

        {/* Featured Videos */}
        <section className="py-8">
          <div className="container mx-auto px-4">
            <h2 className="text-2xl font-bold mb-6">Featured Videos</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-x-6 gap-y-[100px] mb-12">
              {featuredVideos.map((video, index) => (
                <div key={index} className="group">
                  {/* YouTube Embed */}
                  <div className="aspect-video bg-gray-100 rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow">
                    <iframe
                      width="100%"
                      height="100%"
                      src={`https://www.youtube.com/embed/${video.embedId}`}
                      title={video.title}
                      style={{ border: 0 }}
                      allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                      allowFullScreen
                      className="w-full h-full"
                    />
                  </div>
                  {/* Simple Title */}
                  <div className="mt-3">
                    <h3 className="font-medium text-gray-900 group-hover:text-primary transition-colors">
                      {video.title}
                    </h3>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* External Video Content */}
        <section className="py-8">
          <div className="container mx-auto px-4">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {externalVideos.map((video, index) => (
                <Card key={index} className="overflow-hidden">
                  <CardHeader className="pb-4">
                    <div className="flex items-center gap-2 mb-2">
                      <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
                        {video.source}
                      </Badge>
                      <Badge variant="outline">{video.level}</Badge>
                    </div>
                    <CardTitle className="text-xl">{video.title}</CardTitle>
                    <p className="text-muted-foreground">{video.description}</p>
                  </CardHeader>
                  
                  <CardContent className="space-y-4">
                    {/* YouTube Embed */}
                    <div className="aspect-video bg-muted rounded-lg overflow-hidden">
                      <iframe
                        width="100%"
                        height="100%"
                        src={`https://www.youtube.com/embed/${video.embedId}`}
                        title={video.title}
                        style={{ border: 0 }}
                        allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                        allowFullScreen
                        className="w-full h-full"
                      />
                    </div>
                    
                    <div className="flex items-center justify-between text-sm text-muted-foreground">
                      <span>{video.views} views</span>
                      <span>{video.duration}</span>
                    </div>
                    
                    <div className="flex gap-3">
                      <Button 
                        className="flex-1"
                        onClick={() => window.open(video.externalUrl, '_blank')}
                      >
                        Watch on YouTube
                      </Button>
                      <Button 
                        variant="outline"
                        onClick={() => {
                          navigator.clipboard.writeText(video.externalUrl);
                          // You can add a toast notification here
                        }}
                      >
                        Copy Link
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Video Categories */}
        <section className="py-8">
          <div className="container mx-auto px-4">
          
            
            {videoCategories.map((category) => (
              <div key={category.id} className="mb-12">
                <div className="flex items-center gap-3 mb-6">
                  <category.icon className="w-8 h-8 text-primary" />
                  <div>
                    <h3 className="text-xl font-semibold">{category.title}</h3>
                    <p className="text-muted-foreground">{category.description}</p>
                  </div>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {category.videos.map((video, index) => (
                    <Card key={index} className="overflow-hidden hover:shadow-lg transition-shadow cursor-pointer">
                      <div className="relative">
                        {video.isExternal ? (
                          <div className="aspect-video bg-muted flex items-center justify-center">
                            <div className="text-center">
                              <Play className="w-12 h-12 text-primary mx-auto mb-2" />
                              <p className="text-xs text-muted-foreground">YouTube Video</p>
                            </div>
                          </div>
                        ) : (
                          <div className="aspect-video bg-muted flex items-center justify-center">
                            <Play className="w-12 h-12 text-primary" />
                          </div>
                        )}
                        {video.isExternal && (
                          <Badge className="absolute top-2 left-2 bg-blue-600">
                            YouTube
                          </Badge>
                        )}
                        <div className="absolute bottom-2 right-2 bg-black/70 text-white px-2 py-1 rounded text-sm">
                          {video.duration}
                        </div>
                      </div>
                      <CardContent className="p-4">
                        <h4 className="font-semibold mb-2">{video.title}</h4>
                        <p className="text-sm text-muted-foreground mb-3">{video.description}</p>
                        <div className="flex items-center justify-between text-xs text-muted-foreground mb-3">
                          <span>{video.views} views</span>
                          <Badge variant="outline">{video.level}</Badge>
                        </div>
                        {video.isExternal && (
                          <Button
                            variant="outline"
                            size="sm"
                            className="w-full"
                            onClick={() => window.open(video.externalUrl, '_blank')}
                          >
                            Watch on YouTube
                          </Button>
                        )}
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </section>

        {/* Coming Soon */}
        <section className="py-16 bg-muted/30">
          <div className="container mx-auto px-4 text-center">
            <h2 className="text-3xl font-bold mb-6">More Videos Coming Soon!</h2>
            <p className="text-xl text-muted-foreground mb-8 max-w-2xl mx-auto">
              We're constantly creating new video tutorials based on user feedback. 
              Have a specific topic you'd like us to cover?
            </p>
            <Button size="lg">
              Request a Tutorial
            </Button>
          </div>
        </section>

        <Footer />
      </div>
    </>
  )
}
