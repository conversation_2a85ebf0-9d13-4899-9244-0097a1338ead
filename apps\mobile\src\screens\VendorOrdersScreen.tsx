import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Alert,
  RefreshControl,
  TextInput,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { useTheme } from '../providers/ThemeProvider';
import { useAuth } from '../providers/AuthProvider';
import LoadingSpinner from '../components/LoadingSpinner';
import EmptyState from '../components/EmptyState';
import NotificationIntegrationService from '../services/notificationIntegration';

interface VendorOrder {
  id: string;
  customerName: string;
  customerEmail: string;
  customerPhone: string;
  items: {
    id: string;
    name: string;
    quantity: number;
    price: number;
  }[];
  total: number;
  status: 'pending' | 'confirmed' | 'processing' | 'shipped' | 'delivered' | 'cancelled';
  paymentStatus: 'pending' | 'paid' | 'failed' | 'refunded';
  paymentMethod: string;
  shippingAddress: {
    fullName: string;
    addressLine1: string;
    city: string;
    state: string;
    pincode: string;
  };
  createdAt: string;
  updatedAt: string;
  estimatedDelivery?: string;
  trackingNumber?: string;
}

export default function VendorOrdersScreen() {
  const navigation = useNavigation();
  const { theme } = useTheme();
  const { user } = useAuth();
  
  const [orders, setOrders] = useState<VendorOrder[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [filterStatus, setFilterStatus] = useState<'all' | 'pending' | 'confirmed' | 'processing' | 'shipped' | 'delivered'>('all');

  useEffect(() => {
    loadOrders();
  }, []);

  const loadOrders = async () => {
    try {
      setLoading(true);
      
      // TODO: Replace with actual GraphQL query
      // const vendorOrders = await graphqlService.getVendorOrders(user?.id);
      
      // Mock data for now
      const mockOrders: VendorOrder[] = [
        {
          id: 'ORD_001',
          customerName: 'Priya Sharma',
          customerEmail: '<EMAIL>',
          customerPhone: '+91 8148376909',
          items: [
            { id: 'ITEM_001', name: 'Elegant Wedding Lehenga', quantity: 1, price: 25000 },
          ],
          total: 25000,
          status: 'pending',
          paymentStatus: 'paid',
          paymentMethod: 'card',
          shippingAddress: {
            fullName: 'Priya Sharma',
            addressLine1: '123 MG Road',
            city: 'Bangalore',
            state: 'Karnataka',
            pincode: '560001',
          },
          createdAt: '2024-01-25T10:30:00Z',
          updatedAt: '2024-01-25T10:30:00Z',
          estimatedDelivery: '2024-02-01',
        },
        {
          id: 'ORD_002',
          customerName: 'Rahul Kumar',
          customerEmail: '<EMAIL>',
          customerPhone: '+91 9876543211',
          items: [
            { id: 'ITEM_002', name: 'Groom Sherwani Set', quantity: 1, price: 18000 },
          ],
          total: 18000,
          status: 'confirmed',
          paymentStatus: 'paid',
          paymentMethod: 'upi',
          shippingAddress: {
            fullName: 'Rahul Kumar',
            addressLine1: '456 Brigade Road',
            city: 'Bangalore',
            state: 'Karnataka',
            pincode: '560025',
          },
          createdAt: '2024-01-24T14:15:00Z',
          updatedAt: '2024-01-24T16:20:00Z',
          estimatedDelivery: '2024-01-30',
        },
        {
          id: 'ORD_003',
          customerName: 'Anita Patel',
          customerEmail: '<EMAIL>',
          customerPhone: '+91 **********',
          items: [
            { id: 'ITEM_003', name: 'Bridal Jewelry Set', quantity: 1, price: 15000 },
            { id: 'ITEM_004', name: 'Wedding Accessories', quantity: 2, price: 5000 },
          ],
          total: 25000,
          status: 'shipped',
          paymentStatus: 'paid',
          paymentMethod: 'netbanking',
          shippingAddress: {
            fullName: 'Anita Patel',
            addressLine1: '789 Commercial Street',
            city: 'Bangalore',
            state: 'Karnataka',
            pincode: '560001',
          },
          createdAt: '2024-01-23T09:45:00Z',
          updatedAt: '2024-01-25T11:30:00Z',
          estimatedDelivery: '2024-01-28',
          trackingNumber: 'TRK123456789',
        },
      ];
      
      setOrders(mockOrders);
    } catch (error) {
      console.error('Failed to load orders:', error);
      Alert.alert('Error', 'Failed to load orders');
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadOrders();
    setRefreshing(false);
  };

  const filteredOrders = orders.filter(order => {
    const matchesSearch = order.customerName.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         order.id.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesFilter = filterStatus === 'all' || order.status === filterStatus;
    return matchesSearch && matchesFilter;
  });

  const handleUpdateOrderStatus = async (orderId: string, newStatus: VendorOrder['status']) => {
    try {
      // TODO: Update order status via GraphQL
      // await graphqlService.updateOrderStatus(orderId, newStatus);

      // Find the order to get customer details
      const order = orders.find(o => o.id === orderId);

      setOrders(prev => prev.map(order =>
        order.id === orderId
          ? { ...order, status: newStatus, updatedAt: new Date().toISOString() }
          : order
      ));

      // Send notification to customer about status update
      if (order) {
        await NotificationIntegrationService.handleOrderStatusUpdate(
          orderId,
          newStatus,
          order.customerName
        );
      }

      Alert.alert('Success', `Order status updated to ${newStatus}. Customer has been notified.`);
    } catch (error) {
      console.error('Failed to update order status:', error);
      Alert.alert('Error', 'Failed to update order status');
    }
  };

  const getStatusColor = (status: VendorOrder['status']) => {
    switch (status) {
      case 'pending':
        return theme.colors.warning;
      case 'confirmed':
      case 'processing':
        return theme.colors.info;
      case 'shipped':
        return theme.colors.primary;
      case 'delivered':
        return theme.colors.success;
      case 'cancelled':
        return theme.colors.error;
      default:
        return theme.colors.textSecondary;
    }
  };

  const getPaymentStatusColor = (status: VendorOrder['paymentStatus']) => {
    switch (status) {
      case 'paid':
        return theme.colors.success;
      case 'pending':
        return theme.colors.warning;
      case 'failed':
      case 'refunded':
        return theme.colors.error;
      default:
        return theme.colors.textSecondary;
    }
  };

  const getNextStatus = (currentStatus: VendorOrder['status']): VendorOrder['status'] | null => {
    switch (currentStatus) {
      case 'pending':
        return 'confirmed';
      case 'confirmed':
        return 'processing';
      case 'processing':
        return 'shipped';
      case 'shipped':
        return 'delivered';
      default:
        return null;
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-IN', {
      day: 'numeric',
      month: 'short',
      year: 'numeric',
    });
  };

  const renderOrderItem = ({ item: order }: { item: VendorOrder }) => {
    const nextStatus = getNextStatus(order.status);
    
    return (
      <TouchableOpacity
        style={[styles.orderCard, { backgroundColor: theme.colors.surface }]}
        onPress={() => navigation.navigate('OrderDetail' as never, { orderId: order.id } as never)}
      >
        {/* Order Header */}
        <View style={styles.orderHeader}>
          <View style={styles.orderInfo}>
            <Text style={[styles.orderId, { color: theme.colors.text }]}>
              #{order.id}
            </Text>
            <Text style={[styles.customerName, { color: theme.colors.textSecondary }]}>
              {order.customerName}
            </Text>
            <Text style={[styles.orderDate, { color: theme.colors.textSecondary }]}>
              {formatDate(order.createdAt)}
            </Text>
          </View>
          
          <View style={styles.orderRight}>
            <Text style={[styles.orderAmount, { color: theme.colors.text }]}>
              ₹{order.total.toLocaleString()}
            </Text>
            
            <View style={styles.statusContainer}>
              <View style={[styles.statusBadge, { backgroundColor: getStatusColor(order.status) + '20' }]}>
                <Text style={[styles.statusText, { color: getStatusColor(order.status) }]}>
                  {order.status.toUpperCase()}
                </Text>
              </View>
              
              <View style={[styles.paymentBadge, { backgroundColor: getPaymentStatusColor(order.paymentStatus) + '20' }]}>
                <Text style={[styles.paymentText, { color: getPaymentStatusColor(order.paymentStatus) }]}>
                  {order.paymentStatus.toUpperCase()}
                </Text>
              </View>
            </View>
          </View>
        </View>

        {/* Order Items */}
        <View style={styles.itemsContainer}>
          <Text style={[styles.itemsTitle, { color: theme.colors.textSecondary }]}>
            Items ({order.items.length}):
          </Text>
          {order.items.slice(0, 2).map((item, index) => (
            <Text key={item.id} style={[styles.itemText, { color: theme.colors.text }]}>
              • {item.name} (Qty: {item.quantity})
            </Text>
          ))}
          {order.items.length > 2 && (
            <Text style={[styles.moreItems, { color: theme.colors.textSecondary }]}>
              +{order.items.length - 2} more items
            </Text>
          )}
        </View>

        {/* Action Buttons */}
        <View style={styles.actionButtons}>
          <TouchableOpacity
            style={[styles.contactButton, { backgroundColor: theme.colors.info + '20' }]}
            onPress={() => Alert.alert('Contact Customer', `Call ${order.customerPhone}?`, [
              { text: 'Cancel', style: 'cancel' },
              { text: 'Call', onPress: () => {/* TODO: Implement calling */ } },
            ])}
          >
            <Ionicons name="call-outline" size={16} color={theme.colors.info} />
            <Text style={[styles.buttonText, { color: theme.colors.info }]}>Contact</Text>
          </TouchableOpacity>
          
          {nextStatus && (
            <TouchableOpacity
              style={[styles.updateButton, { backgroundColor: theme.colors.primary }]}
              onPress={() => handleUpdateOrderStatus(order.id, nextStatus)}
            >
              <Text style={styles.updateButtonText}>
                Mark as {nextStatus.charAt(0).toUpperCase() + nextStatus.slice(1)}
              </Text>
            </TouchableOpacity>
          )}
        </View>
      </TouchableOpacity>
    );
  };

  if (loading) {
    return <LoadingSpinner fullScreen text="Loading orders..." />;
  }

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {/* Header with Search */}
      <View style={[styles.header, { backgroundColor: theme.colors.surface }]}>
        <View style={[styles.searchContainer, { backgroundColor: theme.colors.background }]}>
          <Ionicons name="search-outline" size={20} color={theme.colors.textSecondary} />
          <TextInput
            style={[styles.searchInput, { color: theme.colors.text }]}
            placeholder="Search orders..."
            placeholderTextColor={theme.colors.textSecondary}
            value={searchQuery}
            onChangeText={setSearchQuery}
          />
        </View>
      </View>

      {/* Filter Tabs */}
      <View style={[styles.filterContainer, { backgroundColor: theme.colors.surface }]}>
        {(['all', 'pending', 'confirmed', 'processing', 'shipped', 'delivered'] as const).map((status) => (
          <TouchableOpacity
            key={status}
            style={[
              styles.filterTab,
              filterStatus === status && { backgroundColor: theme.colors.primary + '20' }
            ]}
            onPress={() => setFilterStatus(status)}
          >
            <Text style={[
              styles.filterText,
              { color: filterStatus === status ? theme.colors.primary : theme.colors.textSecondary }
            ]}>
              {status.charAt(0).toUpperCase() + status.slice(1)}
            </Text>
          </TouchableOpacity>
        ))}
      </View>

      {/* Orders List */}
      {filteredOrders.length === 0 ? (
        <EmptyState
          icon="receipt-outline"
          title="No orders found"
          message={searchQuery ? "Try adjusting your search" : "You haven't received any orders yet"}
          fullScreen
        />
      ) : (
        <FlatList
          data={filteredOrders}
          renderItem={renderOrderItem}
          keyExtractor={(item) => item.id}
          contentContainerStyle={styles.ordersList}
          showsVerticalScrollIndicator={false}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onRefresh}
              colors={[theme.colors.primary]}
              tintColor={theme.colors.primary}
            />
          }
        />
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    padding: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    gap: 8,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
  },
  filterContainer: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    paddingVertical: 8,
    gap: 8,
  },
  filterTab: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  filterText: {
    fontSize: 12,
    fontWeight: '500',
  },
  ordersList: {
    padding: 16,
  },
  orderCard: {
    padding: 16,
    borderRadius: 12,
    marginBottom: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  orderHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  orderInfo: {
    flex: 1,
  },
  orderId: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 2,
  },
  customerName: {
    fontSize: 14,
    marginBottom: 2,
  },
  orderDate: {
    fontSize: 12,
  },
  orderRight: {
    alignItems: 'flex-end',
  },
  orderAmount: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  statusContainer: {
    gap: 4,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    alignItems: 'center',
  },
  statusText: {
    fontSize: 10,
    fontWeight: '600',
  },
  paymentBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    alignItems: 'center',
  },
  paymentText: {
    fontSize: 10,
    fontWeight: '600',
  },
  itemsContainer: {
    marginBottom: 12,
  },
  itemsTitle: {
    fontSize: 12,
    fontWeight: '600',
    marginBottom: 4,
  },
  itemText: {
    fontSize: 12,
    marginBottom: 2,
  },
  moreItems: {
    fontSize: 12,
    fontStyle: 'italic',
  },
  actionButtons: {
    flexDirection: 'row',
    gap: 8,
    marginTop: 8,
  },
  contactButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    gap: 4,
  },
  buttonText: {
    fontSize: 12,
    fontWeight: '600',
  },
  updateButton: {
    flex: 1,
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  updateButtonText: {
    color: 'white',
    fontSize: 12,
    fontWeight: '600',
  },
});
