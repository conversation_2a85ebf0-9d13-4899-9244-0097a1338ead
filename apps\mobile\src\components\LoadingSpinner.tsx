import React from 'react';
import EnhancedLoading from './ui/EnhancedLoading';

interface LoadingSpinnerProps {
  size?: 'small' | 'medium' | 'large';
  text?: string;
  fullScreen?: boolean;
  type?: 'spinner' | 'dots' | 'pulse' | 'wave' | 'skeleton' | 'progress';
  overlay?: boolean;
  progress?: number;
}

export default function LoadingSpinner({
  size = 'large',
  text = 'Loading...',
  fullScreen = false,
  type = 'spinner',
  overlay = false,
  progress = 0,
}: LoadingSpinnerProps) {
  // Convert legacy size prop
  const enhancedSize = size === 'large' ? 'large' : size === 'small' ? 'small' : 'medium';

  return (
    <EnhancedLoading
      type={type}
      size={enhancedSize}
      text={text}
      fullScreen={fullScreen}
      overlay={overlay}
      progress={progress}
    />
  );
}
