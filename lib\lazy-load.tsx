'use client';

import React, { Suspense } from 'react';
import { Loader2 } from 'lucide-react';

// Default loading component
export const DefaultLoading = () => (
  <div className="flex justify-center items-center p-4 min-h-[200px]">
    <Loader2 className="h-8 w-8 animate-spin text-primary" />
  </div>
);

// Function to create a lazy-loaded component with custom loading UI
export function lazyLoad<T extends React.ComponentType<any>>(
  importFunc: () => Promise<{ default: T }>,
  LoadingComponent: React.ComponentType = DefaultLoading,
  errorFallback?: React.ComponentType<{ error: Error }>
) {
  const LazyComponent = React.lazy(importFunc);

  const ErrorBoundary = ({ children }: { children: React.ReactNode }) => {
    const [hasError, setHasError] = React.useState<Error | null>(null);

    React.useEffect(() => {
      // Only add event listeners on the client side
      if (typeof window === 'undefined') return;

      const handleError = (error: ErrorEvent) => {
        console.error('Lazy loading error:', error);
        setHasError(error.error);
      };

      window.addEventListener('error', handleError);
      return () => window.removeEventListener('error', handleError);
    }, []);

    if (hasError && errorFallback) {
      const ErrorComponent = errorFallback;
      return <ErrorComponent error={hasError} />;
    }

    return <>{children}</>;
  };

  return (props: React.ComponentProps<T>) => (
    <ErrorBoundary>
      <Suspense fallback={<LoadingComponent />}>
        <LazyComponent {...props} />
      </Suspense>
    </ErrorBoundary>
  );
}
