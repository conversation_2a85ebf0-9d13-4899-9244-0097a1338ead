import { generateClient } from 'aws-amplify/api'
import { listVendors, listReviews, listBookings, listInquiries } from '@/src/graphql/queries'

const client = generateClient()

export class VendorAnalyticsService {
  static async getVendorAnalytics(userId: string, dateRange: string) {
    try {
      const endDate = new Date()
      const startDate = new Date()
      
      switch (dateRange) {
        case '7d':
          startDate.setDate(endDate.getDate() - 7)
          break
        case '30d':
          startDate.setDate(endDate.getDate() - 30)
          break
        case '90d':
          startDate.setDate(endDate.getDate() - 90)
          break
        case '1y':
          startDate.setFullYear(endDate.getFullYear() - 1)
          break
      }

      // Fetch vendor data
      const vendorResult = await client.graphql({
        query: listVendors,
        variables: {
          filter: { userId: { eq: userId } }
        }
      })

      const vendors = vendorResult.data.listVendors.items

      if (vendors.length === 0) {
        return this.getEmptyAnalytics()
      }

      const vendor = vendors[0]

      // Fetch reviews
      const reviewsResult = await client.graphql({
        query: listReviews,
        variables: {
          filter: {
            entityId: { eq: vendor.id },
            createdAt: { ge: startDate.toISOString() }
          }
        }
      })

      // Fetch bookings
      const bookingsResult = await client.graphql({
        query: listBookings,
        variables: {
          filter: {
            vendorId: { eq: vendor.id },
            createdAt: { ge: startDate.toISOString() }
          }
        }
      })

      // Fetch inquiries
      const inquiriesResult = await client.graphql({
        query: listInquiries,
        variables: {
          filter: {
            vendorUserId: { eq: userId },
            createdAt: { ge: startDate.toISOString() }
          }
        }
      })

      const reviews = reviewsResult.data.listReviews.items
      const bookings = bookingsResult.data.listBookings.items
      const inquiries = inquiriesResult.data.listInquiries.items

      // Calculate metrics
      const avgRating = reviews.length > 0 
        ? reviews.reduce((sum, review) => sum + (review.rating || 0), 0) / reviews.length 
        : 0

      const profileViews = this.calculateProfileViews(vendor, dateRange)
      const contactClicks = this.calculateContactClicks(vendor, dateRange)

      return {
        profileViews,
        profileViewsChange: this.calculateChange(profileViews, dateRange),
        contactClicks,
        contactClicksChange: this.calculateChange(contactClicks, dateRange),
        inquiries: inquiries.length,
        inquiriesChange: this.calculateChange(inquiries.length, dateRange),
        avgRating: Math.round(avgRating * 10) / 10,
        ratingChange: this.calculateRatingChange(avgRating, dateRange),
        totalReviews: reviews.length,
        totalBookings: bookings.length,
        viewsTrend: this.generateViewsTrend(dateRange),
        engagementData: [
          { metric: 'Views', value: profileViews },
          { metric: 'Clicks', value: contactClicks },
          { metric: 'Inquiries', value: inquiries.length },
          { metric: 'Bookings', value: bookings.length }
        ],
        recentActivity: this.getRecentActivity(reviews, bookings, inquiries),
        phoneClicks: Math.floor(contactClicks * 0.6),
        emailClicks: Math.floor(contactClicks * 0.3),
        whatsappClicks: Math.floor(contactClicks * 0.1),
        photoViews: profileViews * 3,
        photoClicks: Math.floor(profileViews * 0.15),
        galleryCTR: 15,
        responseRate: 92,
        viewToContactRate: ((contactClicks / profileViews) * 100).toFixed(1),
        contactToInquiryRate: ((inquiries.length / contactClicks) * 100).toFixed(1),
        inquiryToBookingRate: ((bookings.length / Math.max(inquiries.length, 1)) * 100).toFixed(1),
        categoryRank: 3,
        aboveCategoryAvg: 15,
        marketShare: 2.3
      }
    } catch (error) {
      console.error('Error fetching vendor analytics:', error)
      return this.getEmptyAnalytics()
    }
  }

  private static calculateProfileViews(vendor: any, dateRange: string): number {
    // Mock calculation based on vendor data
    const baseViews = 100
    const multiplier = dateRange === '7d' ? 0.2 : dateRange === '30d' ? 1 : dateRange === '90d' ? 3 : 12
    return Math.floor(baseViews * multiplier * (1 + Math.random() * 0.5))
  }

  private static calculateContactClicks(vendor: any, dateRange: string): number {
    // Mock calculation
    const baseClicks = 20
    const multiplier = dateRange === '7d' ? 0.2 : dateRange === '30d' ? 1 : dateRange === '90d' ? 3 : 12
    return Math.floor(baseClicks * multiplier * (1 + Math.random() * 0.3))
  }

  private static calculateChange(current: number, dateRange: string): number {
    // Mock change calculation
    return Math.round((Math.random() * 30 - 5) * 10) / 10
  }

  private static calculateRatingChange(current: number, dateRange: string): number {
    return Math.round((Math.random() * 0.4 - 0.1) * 10) / 10
  }

  private static generateViewsTrend(dateRange: string) {
    const days = dateRange === '7d' ? 7 : dateRange === '30d' ? 30 : 90
    const trend = []
    
    for (let i = days - 1; i >= 0; i--) {
      const date = new Date()
      date.setDate(date.getDate() - i)
      trend.push({
        date: date.toISOString().split('T')[0],
        views: Math.floor(Math.random() * 50) + 20
      })
    }
    
    return trend
  }

  private static getRecentActivity(reviews: any[], bookings: any[], inquiries: any[]) {
    const activities = []
    
    reviews.slice(0, 3).forEach(review => {
      activities.push({
        action: `New review received (${review.rating} stars)`,
        timestamp: new Date(review.createdAt).toLocaleDateString()
      })
    })
    
    bookings.slice(0, 2).forEach(booking => {
      activities.push({
        action: 'New booking received',
        timestamp: new Date(booking.createdAt).toLocaleDateString()
      })
    })
    
    inquiries.slice(0, 2).forEach(inquiry => {
      activities.push({
        action: 'New inquiry received',
        timestamp: new Date(inquiry.createdAt).toLocaleDateString()
      })
    })
    
    return activities.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
  }

  private static getEmptyAnalytics() {
    return {
      profileViews: 0,
      profileViewsChange: 0,
      contactClicks: 0,
      contactClicksChange: 0,
      inquiries: 0,
      inquiriesChange: 0,
      avgRating: 0,
      ratingChange: 0,
      totalReviews: 0,
      totalBookings: 0,
      viewsTrend: [],
      engagementData: [],
      recentActivity: [],
      phoneClicks: 0,
      emailClicks: 0,
      whatsappClicks: 0,
      photoViews: 0,
      photoClicks: 0,
      galleryCTR: 0,
      responseRate: 0,
      viewToContactRate: 0,
      contactToInquiryRate: 0,
      inquiryToBookingRate: 0,
      categoryRank: 'N/A',
      aboveCategoryAvg: 0,
      marketShare: 0
    }
  }
}