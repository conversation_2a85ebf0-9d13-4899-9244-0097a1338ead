'use client';

import React, { useState } from 'react';
import { TopHeader } from '@/components/top-header';
import { Header } from '@/components/header';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Database, 
  ExternalLink, 
  Copy,
  AlertTriangle,
  CheckCircle,
  Settings
} from 'lucide-react';

export default function ManualAdminSetupPage() {
  const [copiedStep, setCopiedStep] = useState<number | null>(null);

  const copyToClipboard = (text: string, stepNumber: number) => {
    navigator.clipboard.writeText(text);
    setCopiedStep(stepNumber);
    setTimeout(() => setCopiedStep(null), 2000);
  };

  const awsSteps = [
    {
      title: "Open AWS DynamoDB Console",
      description: "Navigate to the DynamoDB service in AWS Console",
      action: "Go to AWS Console → DynamoDB → Tables",
      link: "https://console.aws.amazon.com/dynamodb/home?region=ap-south-1#tables:"
    },
    {
      title: "Find UserProfile Table",
      description: "Look for the table that contains user profiles",
      action: "Search for table name containing 'UserProfile' or 'thirumanam'",
      note: "Table name will be something like: UserProfile-[random-string]-kalyanam"
    },
    {
      title: "Add Admin Fields to Table",
      description: "Manually add the admin fields to the table structure",
      fields: [
        { name: "isAdmin", type: "Boolean", description: "Basic admin flag" },
        { name: "isSuperAdmin", type: "Boolean", description: "Super admin flag" },
        { name: "role", type: "String", description: "User role (ADMIN, SUPER_ADMIN, etc.)" },
        { name: "permissions", type: "List", description: "Array of permission strings" }
      ]
    },
    {
      title: "Update Your User Record",
      description: "Find your user record and add admin values",
      action: "Search by your userId and edit the item",
      values: {
        isAdmin: "true",
        isSuperAdmin: "true", 
        role: "SUPER_ADMIN",
        permissions: '["reviews:read", "reviews:moderate", "users:read", "users:update"]'
      }
    }
  ];

  const alternativeApproaches = [
    {
      title: "Use AWS CLI",
      description: "Update the table using AWS CLI commands",
      command: `aws dynamodb update-item \\
  --table-name UserProfile-[TABLE-ID]-kalyanam \\
  --key '{"id": {"S": "your-user-id"}}' \\
  --update-expression "SET isAdmin = :admin, isSuperAdmin = :super, #role = :role" \\
  --expression-attribute-names '{"#role": "role"}' \\
  --expression-attribute-values '{
    ":admin": {"BOOL": true},
    ":super": {"BOOL": true},
    ":role": {"S": "SUPER_ADMIN"}
  }'`
    },
    {
      title: "Fix Amplify Deployment",
      description: "Try to resolve the deployment issues",
      steps: [
        "Run: amplify env checkout kalyanam",
        "Run: amplify pull",
        "Run: amplify push --force",
        "If that fails, try: amplify env add [new-env-name]"
      ]
    },
    {
      title: "Use Basic Profile Approach",
      description: "Create a basic profile and add admin logic in code",
      action: "Use the Simple Admin Setup page to create a basic profile, then handle admin logic in the frontend"
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      <TopHeader />
      <Header />
      
      <div className="container mx-auto px-4 py-8 max-w-4xl">
        <div className="text-center mb-8">
          <div className="flex items-center justify-center mb-4">
            <Database className="h-8 w-8 text-orange-500 mr-2" />
            <h1 className="text-3xl font-bold text-gray-900">Manual Admin Setup</h1>
          </div>
          <p className="text-gray-600">Since the Amplify deployment failed, here's how to manually add admin fields</p>
        </div>

        {/* Deployment Status */}
        <Card className="mb-6 border-orange-200 bg-orange-50">
          <CardContent className="p-4">
            <div className="flex items-start">
              <AlertTriangle className="h-5 w-5 text-orange-500 mr-2 mt-0.5" />
              <div className="text-orange-800">
                <p className="font-semibold">Deployment Issue Detected</p>
                <p className="text-sm">The Amplify push failed due to CloudFormation parameter issues. The admin fields are not yet available in the database.</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* AWS Console Method */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle>Method 1: AWS Console (Recommended)</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              {awsSteps.map((step, index) => (
                <div key={index} className="border rounded-lg p-4">
                  <div className="flex items-center justify-between mb-2">
                    <h3 className="font-semibold text-lg">Step {index + 1}: {step.title}</h3>
                    <Badge variant="outline">Required</Badge>
                  </div>
                  
                  <p className="text-gray-600 mb-3">{step.description}</p>
                  
                  {step.action && (
                    <div className="bg-blue-50 p-3 rounded-lg mb-3">
                      <p className="text-blue-800 font-medium">Action: {step.action}</p>
                    </div>
                  )}
                  
                  {step.link && (
                    <Button asChild variant="outline" size="sm" className="mb-3">
                      <a href={step.link} target="_blank" rel="noopener noreferrer">
                        Open AWS Console
                        <ExternalLink className="ml-2 h-4 w-4" />
                      </a>
                    </Button>
                  )}
                  
                  {step.fields && (
                    <div className="space-y-2">
                      <p className="font-medium">Fields to add:</p>
                      {step.fields.map((field, fieldIndex) => (
                        <div key={fieldIndex} className="bg-gray-50 p-2 rounded text-sm">
                          <span className="font-mono font-semibold">{field.name}</span>
                          <span className="text-gray-500 mx-2">({field.type})</span>
                          <span className="text-gray-600">- {field.description}</span>
                        </div>
                      ))}
                    </div>
                  )}
                  
                  {step.values && (
                    <div className="space-y-2">
                      <p className="font-medium">Values to set:</p>
                      <div className="bg-gray-50 p-3 rounded font-mono text-sm">
                        {Object.entries(step.values).map(([key, value]) => (
                          <div key={key} className="flex justify-between items-center">
                            <span>{key}:</span>
                            <span className="text-blue-600">{value}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                  
                  {step.note && (
                    <div className="bg-yellow-50 p-3 rounded-lg mt-3">
                      <p className="text-yellow-800 text-sm"><strong>Note:</strong> {step.note}</p>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Alternative Methods */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle>Alternative Methods</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {alternativeApproaches.map((approach, index) => (
                <div key={index} className="border rounded-lg p-4">
                  <h3 className="font-semibold mb-2">{approach.title}</h3>
                  <p className="text-gray-600 mb-3">{approach.description}</p>
                  
                  {approach.command && (
                    <div className="bg-gray-900 text-green-400 p-3 rounded-lg font-mono text-sm overflow-x-auto">
                      <div className="flex justify-between items-start mb-2">
                        <span className="text-gray-400">AWS CLI Command:</span>
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() => copyToClipboard(approach.command, index)}
                          className="text-green-400 hover:text-green-300"
                        >
                          {copiedStep === index ? <CheckCircle className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
                        </Button>
                      </div>
                      <pre className="whitespace-pre-wrap">{approach.command}</pre>
                    </div>
                  )}
                  
                  {approach.steps && (
                    <div className="space-y-1">
                      {approach.steps.map((step, stepIndex) => (
                        <div key={stepIndex} className="bg-gray-50 p-2 rounded text-sm font-mono">
                          {step}
                        </div>
                      ))}
                    </div>
                  )}
                  
                  {approach.action && (
                    <div className="bg-blue-50 p-3 rounded-lg">
                      <p className="text-blue-800">{approach.action}</p>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Quick Links */}
        <Card>
          <CardHeader>
            <CardTitle>Quick Links</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <Button asChild variant="outline">
                <a href="https://console.aws.amazon.com/dynamodb/home?region=ap-south-1" target="_blank">
                  AWS DynamoDB
                  <ExternalLink className="ml-2 h-4 w-4" />
                </a>
              </Button>
              <Button asChild variant="outline">
                <a href="/simple-admin-setup">Simple Setup</a>
              </Button>
              <Button asChild variant="outline">
                <a href="/admin">Admin Dashboard</a>
              </Button>
              <Button asChild variant="outline">
                <a href="/dashboard/admin-reviews">Review Management</a>
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Success Message */}
        <Card className="mt-6 border-green-200 bg-green-50">
          <CardContent className="p-4">
            <div className="text-green-800">
              <p className="font-semibold mb-2">✅ After Manual Setup</p>
              <p className="text-sm">Once you've manually added the admin fields to your user record in DynamoDB, you'll be able to access all admin features in the application.</p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
