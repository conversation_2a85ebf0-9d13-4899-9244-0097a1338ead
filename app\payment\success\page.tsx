"use client"
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { TopHeader } from "@/components/top-header";
import { Header } from "@/components/header";
import { Footer } from "@/components/footer";
import { CheckCircle, Package, Truck, Home, Download } from "lucide-react";
import Link from "next/link";
import { useEffect, useState } from "react";

export default function PaymentSuccessPage() {
  const [orderNumber, setOrderNumber] = useState('');

  useEffect(() => {
    // Generate a random order number
    const randomOrderNumber = 'TM' + Date.now().toString().slice(-8);
    setOrderNumber(randomOrderNumber);
  }, []);

  return (
    <div className="min-h-screen bg-gray-50">
      <TopHeader />
      <Header />
      
      <div className="container mx-auto px-4 py-16 max-w-2xl">
        <Card className="text-center">
          <CardContent className="p-8">
            {/* Success Icon */}
            <div className="mb-6">
              <CheckCircle className="h-16 w-16 text-green-500 mx-auto mb-4" />
              <h1 className="text-2xl md:text-3xl font-bold text-green-700 mb-2">
                Payment Successful!
              </h1>
              <p className="text-gray-600">
                Thank you for your order. Your payment has been processed successfully.
              </p>
            </div>

            {/* Order Details */}
            <div className="bg-gray-50 rounded-lg p-6 mb-6">
              <h2 className="font-semibold mb-4">Order Details</h2>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span>Order Number:</span>
                  <span className="font-mono font-medium">{orderNumber}</span>
                </div>
                <div className="flex justify-between">
                  <span>Order Date:</span>
                  <span>{new Date().toLocaleDateString()}</span>
                </div>
                <div className="flex justify-between">
                  <span>Payment Method:</span>
                  <span>Credit Card</span>
                </div>
                <div className="flex justify-between">
                  <span>Status:</span>
                  <span className="text-green-600 font-medium">Confirmed</span>
                </div>
              </div>
            </div>

            {/* Next Steps */}
            <div className="mb-6">
              <h3 className="font-semibold mb-4">What happens next?</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                <div className="flex flex-col items-center p-4 bg-blue-50 rounded-lg">
                  <Package className="h-8 w-8 text-blue-600 mb-2" />
                  <span className="font-medium">Order Processing</span>
                  <span className="text-gray-600 text-center">We'll prepare your items for shipping</span>
                </div>
                <div className="flex flex-col items-center p-4 bg-orange-50 rounded-lg">
                  <Truck className="h-8 w-8 text-orange-600 mb-2" />
                  <span className="font-medium">Shipping</span>
                  <span className="text-gray-600 text-center">Your order will be shipped within 2-3 days</span>
                </div>
                <div className="flex flex-col items-center p-4 bg-green-50 rounded-lg">
                  <Home className="h-8 w-8 text-green-600 mb-2" />
                  <span className="font-medium">Delivery</span>
                  <span className="text-gray-600 text-center">Delivered to your doorstep in 5-7 days</span>
                </div>
              </div>
            </div>

            {/* Email Confirmation */}
            <div className="bg-blue-50 rounded-lg p-4 mb-6">
              <p className="text-sm text-blue-700">
                📧 A confirmation email with order details and tracking information has been sent to your email address.
              </p>
            </div>

            {/* Action Buttons */}
            <div className="space-y-3">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                <Button variant="outline" className="flex items-center gap-2">
                  <Download className="h-4 w-4" />
                  Download Invoice
                </Button>
                <Button variant="outline">
                  Track Your Order
                </Button>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                <Link href="/shop" className="w-full">
                  <Button variant="outline" className="w-full">
                    Continue Shopping
                  </Button>
                </Link>
                <Link href="/" className="w-full">
                  <Button className="w-full bg-primary hover:bg-primary/90">
                    Back to Home
                  </Button>
                </Link>
              </div>
            </div>

            {/* Support */}
            <div className="mt-8 pt-6 border-t">
              <p className="text-sm text-gray-600">
                Need help? Contact our support team at{' '}
                <a href="mailto:<EMAIL>" className="text-primary hover:underline">
                  <EMAIL>
                </a>{' '}
                or call{' '}
                <a href="tel:+918148376909" className="text-primary hover:underline">
                  +91 8148376909
                </a>
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
      
      <Footer />
    </div>
  );
}
