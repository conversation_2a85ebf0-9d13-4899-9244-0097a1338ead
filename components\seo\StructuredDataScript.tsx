'use client';

import { businessInfo } from '@/lib/config/seo';

interface StructuredDataScriptProps {
  type?: 'website' | 'organization' | 'local-business' | 'article' | 'product';
  data?: any;
}

export default function StructuredDataScript({ type = 'website', data }: StructuredDataScriptProps) {
  const generateStructuredData = () => {
    const baseData = {
      '@context': 'https://schema.org',
    };

    switch (type) {
      case 'organization':
        return {
          ...baseData,
          '@type': 'Organization',
          name: businessInfo.name,
          description: businessInfo.description,
          url: businessInfo.url,
          logo: businessInfo.logo,
          contactPoint: {
            '@type': 'ContactPoint',
            telephone: businessInfo.contactPoint.telephone,
            contactType: businessInfo.contactPoint.contactType,
            availableLanguage: businessInfo.contactPoint.availableLanguage,
          },
          address: {
            '@type': 'PostalAddress',
            streetAddress: businessInfo.address.streetAddress,
            addressLocality: businessInfo.address.addressLocality,
            addressRegion: businessInfo.address.addressRegion,
            postalCode: businessInfo.address.postalCode,
            addressCountry: businessInfo.address.addressCountry,
          },
          sameAs: businessInfo.sameAs,
        };

      case 'local-business':
        return {
          ...baseData,
          '@type': 'LocalBusiness',
          name: businessInfo.name,
          description: businessInfo.description,
          url: businessInfo.url,
          logo: businessInfo.logo,
          telephone: businessInfo.contactPoint.telephone,
          address: {
            '@type': 'PostalAddress',
            streetAddress: businessInfo.address.streetAddress,
            addressLocality: businessInfo.address.addressLocality,
            addressRegion: businessInfo.address.addressRegion,
            postalCode: businessInfo.address.postalCode,
            addressCountry: businessInfo.address.addressCountry,
          },
          openingHours: 'Mo-Su 00:00-23:59',
          priceRange: '₹₹',
          servesCuisine: 'Festive Services',
          sameAs: businessInfo.sameAs,
        };

      case 'website':
        return {
          ...baseData,
          '@type': 'WebSite',
          name: businessInfo.name,
          description: businessInfo.description,
          url: businessInfo.url,
          potentialAction: {
            '@type': 'SearchAction',
            target: {
              '@type': 'EntryPoint',
              urlTemplate: `${businessInfo.url}/search?q={search_term_string}`,
            },
            'query-input': 'required name=search_term_string',
          },
        };

      default:
        return { ...baseData, ...data };
    }
  };

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{
        __html: JSON.stringify(generateStructuredData()),
      }}
    />
  );
}