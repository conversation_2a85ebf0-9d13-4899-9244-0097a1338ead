'use client';

import React, { Suspense, lazy } from 'react';

// Lazy load the FeaturedShopProducts component to reduce initial bundle size
const FeaturedShopProducts = lazy(() => import('@/components/FeaturedShopProducts'));

export default function LazyFeaturedShopProducts() {
  return (
    <Suspense fallback={
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    }>
      <FeaturedShopProducts />
    </Suspense>
  );
}
