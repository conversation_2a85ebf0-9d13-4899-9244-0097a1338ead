import React, { useRef, useState } from 'react';
import {
  View,
  Text,
  Animated,
  PanGestureHandler,
  State,
  StyleSheet,
  Dimensions,
  TouchableOpacity,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import * as Haptics from 'expo-haptics';
import { useTheme } from '../../providers/ThemeProvider';

const { width: screenWidth } = Dimensions.get('window');

interface SwipeAction {
  id: string;
  title: string;
  icon?: string;
  color: string;
  backgroundColor: string;
  onPress: () => void;
}

interface SwipeActionsProps {
  children: React.ReactNode;
  leftActions?: SwipeAction[];
  rightActions?: SwipeAction[];
  actionWidth?: number;
  threshold?: number;
  hapticFeedback?: boolean;
  style?: any;
  disabled?: boolean;
}

export default function SwipeActions({
  children,
  leftActions = [],
  rightActions = [],
  actionWidth = 80,
  threshold = 0.3,
  hapticFeedback = true,
  style,
  disabled = false,
}: SwipeActionsProps) {
  const { theme } = useTheme();
  const [isOpen, setIsOpen] = useState(false);
  const [openDirection, setOpenDirection] = useState<'left' | 'right' | null>(null);
  
  // Animation values
  const translateX = useRef(new Animated.Value(0)).current;
  const leftActionOpacity = useRef(new Animated.Value(0)).current;
  const rightActionOpacity = useRef(new Animated.Value(0)).current;
  const leftActionScale = useRef(new Animated.Value(0.8)).current;
  const rightActionScale = useRef(new Animated.Value(0.8)).current;

  const maxLeftSwipe = leftActions.length * actionWidth;
  const maxRightSwipe = rightActions.length * actionWidth;

  const onGestureEvent = Animated.event(
    [{ nativeEvent: { translationX: translateX } }],
    {
      useNativeDriver: false,
      listener: (event: any) => {
        if (disabled) return;

        const { translationX: tx } = event.nativeEvent;
        
        // Update action visibility based on swipe direction
        if (tx > 0 && leftActions.length > 0) {
          // Swiping right (showing left actions)
          const progress = Math.min(tx / maxLeftSwipe, 1);
          
          Animated.timing(leftActionOpacity, {
            toValue: progress,
            duration: 0,
            useNativeDriver: true,
          }).start();

          Animated.timing(leftActionScale, {
            toValue: 0.8 + (0.2 * progress),
            duration: 0,
            useNativeDriver: true,
          }).start();

          // Hide right actions
          Animated.timing(rightActionOpacity, {
            toValue: 0,
            duration: 0,
            useNativeDriver: true,
          }).start();

          // Haptic feedback at threshold
          if (progress >= threshold && hapticFeedback) {
            Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
          }
        } else if (tx < 0 && rightActions.length > 0) {
          // Swiping left (showing right actions)
          const progress = Math.min(Math.abs(tx) / maxRightSwipe, 1);
          
          Animated.timing(rightActionOpacity, {
            toValue: progress,
            duration: 0,
            useNativeDriver: true,
          }).start();

          Animated.timing(rightActionScale, {
            toValue: 0.8 + (0.2 * progress),
            duration: 0,
            useNativeDriver: true,
          }).start();

          // Hide left actions
          Animated.timing(leftActionOpacity, {
            toValue: 0,
            duration: 0,
            useNativeDriver: true,
          }).start();

          // Haptic feedback at threshold
          if (progress >= threshold && hapticFeedback) {
            Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
          }
        }
      },
    }
  );

  const onHandlerStateChange = (event: any) => {
    if (disabled) return;

    const { state, translationX: tx } = event.nativeEvent;

    if (state === State.END) {
      const leftThreshold = maxLeftSwipe * threshold;
      const rightThreshold = maxRightSwipe * threshold;

      if (tx > leftThreshold && leftActions.length > 0) {
        // Open left actions
        openActions('left');
      } else if (tx < -rightThreshold && rightActions.length > 0) {
        // Open right actions
        openActions('right');
      } else {
        // Close actions
        closeActions();
      }
    }
  };

  const openActions = (direction: 'left' | 'right') => {
    setIsOpen(true);
    setOpenDirection(direction);

    const targetValue = direction === 'left' ? maxLeftSwipe : -maxRightSwipe;

    Animated.spring(translateX, {
      toValue: targetValue,
      useNativeDriver: false,
    }).start();

    if (hapticFeedback) {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    }
  };

  const closeActions = () => {
    setIsOpen(false);
    setOpenDirection(null);

    Animated.parallel([
      Animated.spring(translateX, {
        toValue: 0,
        useNativeDriver: false,
      }),
      Animated.timing(leftActionOpacity, {
        toValue: 0,
        duration: 200,
        useNativeDriver: true,
      }),
      Animated.timing(rightActionOpacity, {
        toValue: 0,
        duration: 200,
        useNativeDriver: true,
      }),
    ]).start();
  };

  const handleActionPress = (action: SwipeAction) => {
    if (hapticFeedback) {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Heavy);
    }

    action.onPress();
    closeActions();
  };

  const renderActions = (actions: SwipeAction[], side: 'left' | 'right') => {
    const opacity = side === 'left' ? leftActionOpacity : rightActionOpacity;
    const scale = side === 'left' ? leftActionScale : rightActionScale;

    return (
      <View style={[styles.actionsContainer, { [side]: 0 }]}>
        {actions.map((action, index) => (
          <Animated.View
            key={action.id}
            style={[
              styles.actionButton,
              {
                width: actionWidth,
                backgroundColor: action.backgroundColor,
                opacity,
                transform: [{ scale }],
              },
            ]}
          >
            <TouchableOpacity
              style={styles.actionTouchable}
              onPress={() => handleActionPress(action)}
              activeOpacity={0.8}
            >
              {action.icon && (
                <Ionicons
                  name={action.icon as any}
                  size={24}
                  color={action.color}
                  style={styles.actionIcon}
                />
              )}
              <Text style={[styles.actionText, { color: action.color }]}>
                {action.title}
              </Text>
            </TouchableOpacity>
          </Animated.View>
        ))}
      </View>
    );
  };

  return (
    <View style={[styles.container, style]}>
      {/* Left Actions */}
      {leftActions.length > 0 && renderActions(leftActions, 'left')}

      {/* Right Actions */}
      {rightActions.length > 0 && renderActions(rightActions, 'right')}

      {/* Main Content */}
      <PanGestureHandler
        onGestureEvent={onGestureEvent}
        onHandlerStateChange={onHandlerStateChange}
        enabled={!disabled}
        activeOffsetX={[-10, 10]}
        failOffsetY={[-20, 20]}
      >
        <Animated.View
          style={[
            styles.content,
            {
              transform: [{ translateX }],
            },
          ]}
        >
          {children}
        </Animated.View>
      </PanGestureHandler>

      {/* Overlay to close actions when tapping outside */}
      {isOpen && (
        <TouchableOpacity
          style={styles.overlay}
          onPress={closeActions}
          activeOpacity={1}
        />
      )}
    </View>
  );
}

// Preset action configurations
export const createDeleteAction = (onDelete: () => void): SwipeAction => ({
  id: 'delete',
  title: 'Delete',
  icon: 'trash',
  color: '#FFFFFF',
  backgroundColor: '#FF3B30',
  onPress: onDelete,
});

export const createEditAction = (onEdit: () => void): SwipeAction => ({
  id: 'edit',
  title: 'Edit',
  icon: 'pencil',
  color: '#FFFFFF',
  backgroundColor: '#007AFF',
  onPress: onEdit,
});

export const createArchiveAction = (onArchive: () => void): SwipeAction => ({
  id: 'archive',
  title: 'Archive',
  icon: 'archive',
  color: '#FFFFFF',
  backgroundColor: '#FF9500',
  onPress: onArchive,
});

export const createShareAction = (onShare: () => void): SwipeAction => ({
  id: 'share',
  title: 'Share',
  icon: 'share',
  color: '#FFFFFF',
  backgroundColor: '#34C759',
  onPress: onShare,
});

export const createFavoriteAction = (onFavorite: () => void, isFavorited: boolean = false): SwipeAction => ({
  id: 'favorite',
  title: isFavorited ? 'Unfavorite' : 'Favorite',
  icon: isFavorited ? 'heart' : 'heart-outline',
  color: '#FFFFFF',
  backgroundColor: '#FF2D92',
  onPress: onFavorite,
});

const styles = StyleSheet.create({
  container: {
    position: 'relative',
    overflow: 'hidden',
  },
  content: {
    backgroundColor: 'transparent',
  },
  actionsContainer: {
    position: 'absolute',
    top: 0,
    bottom: 0,
    flexDirection: 'row',
    alignItems: 'center',
    zIndex: 1,
  },
  actionButton: {
    justifyContent: 'center',
    alignItems: 'center',
    height: '100%',
  },
  actionTouchable: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    width: '100%',
    paddingHorizontal: 8,
  },
  actionIcon: {
    marginBottom: 4,
  },
  actionText: {
    fontSize: 12,
    fontWeight: '600',
    textAlign: 'center',
  },
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 2,
  },
});
