"use client"

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { Share2, Copy, Check, Facebook, MessageCircle } from 'lucide-react'
import { showToast } from '@/lib/toast'

interface ShareButtonProps {
  title: string
  description?: string
  url?: string
  image?: string
  variant?: "default" | "outline" | "ghost"
  size?: "default" | "sm" | "lg" | "icon"
  className?: string
}

export default function ShareButton({ 
  title, 
  description, 
  url, 
  image, 
  variant = "outline", 
  size = "icon", 
  className = "" 
}: ShareButtonProps) {
  const [copied, setCopied] = useState(false)
  const [open, setOpen] = useState(false)

  const shareUrl = url || (typeof window !== 'undefined' ? window.location.href : '')
  const shareTitle = encodeURIComponent(title)
  const shareText = encodeURIComponent(description || `Check out ${title} on BookmyFestive`)
  const encodedUrl = encodeURIComponent(shareUrl)

  const handleCopyLink = async () => {
    try {
      await navigator.clipboard.writeText(shareUrl)
      setCopied(true)
      showToast.success('Link copied to clipboard!')
      setTimeout(() => setCopied(false), 2000)
      setOpen(false)
    } catch (error) {
      showToast.error('Failed to copy link')
    }
  }

  const handleSocialShare = (platform: string) => {
    let shareLink = ''
    
    switch (platform) {
      case 'facebook':
        shareLink = `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}`
        break
      case 'twitter':
        shareLink = `https://twitter.com/intent/tweet?text=${shareText}&url=${encodedUrl}`
        break
      case 'whatsapp':
        shareLink = `https://wa.me/?text=${shareText}%20${encodedUrl}`
        break
      case 'telegram':
        shareLink = `https://t.me/share/url?url=${encodedUrl}&text=${shareText}`
        break
      case 'linkedin':
        shareLink = `https://www.linkedin.com/sharing/share-offsite/?url=${encodedUrl}`
        break
    }
    
    if (shareLink) {
      window.open(shareLink, '_blank', 'width=600,height=400')
      setOpen(false)
    }
  }

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button 
          size={size} 
          variant={variant} 
          className={`rounded-full hover:bg-primary/10 hover:text-primary hover:border-primary transition-all duration-200 ${className}`}
        >
          <Share2 className="h-4 w-4" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-56 sm:w-64 p-3" align="end" side="bottom">
        <div className="space-y-2">
          <p className="text-xs sm:text-sm font-medium text-gray-900 mb-3">Share this</p>
          
          {/* Social Media Options */}
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
            <Button
              variant="outline"
              size="sm"
              className="justify-start text-xs sm:text-sm h-8 sm:h-9"
              onClick={() => handleSocialShare('facebook')}
            >
              <Facebook className="h-3 w-3 sm:h-4 sm:w-4 mr-2 text-blue-600" />
              Facebook
            </Button>
            
            <Button
              variant="outline"
              size="sm"
              className="justify-start text-xs sm:text-sm h-8 sm:h-9"
              onClick={() => handleSocialShare('twitter')}
            >
              <span className="text-sm mr-2">🐦</span>
              Twitter
            </Button>
            
            <Button
              variant="outline"
              size="sm"
              className="justify-start text-xs sm:text-sm h-8 sm:h-9"
              onClick={() => handleSocialShare('whatsapp')}
            >
              <MessageCircle className="h-3 w-3 sm:h-4 sm:w-4 mr-2 text-green-600" />
              WhatsApp
            </Button>
            
            <Button
              variant="outline"
              size="sm"
              className="justify-start text-xs sm:text-sm h-8 sm:h-9"
              onClick={() => handleSocialShare('telegram')}
            >
              <span className="text-sm mr-2">📱</span>
              Telegram
            </Button>
          </div>
          
          {/* Copy Link */}
          <div className="pt-2 border-t">
            <Button
              variant="outline"
              size="sm"
              className="w-full justify-start text-xs sm:text-sm h-8 sm:h-9"
              onClick={handleCopyLink}
            >
              {copied ? (
                <Check className="h-3 w-3 sm:h-4 sm:w-4 mr-2 text-green-600" />
              ) : (
                <Copy className="h-3 w-3 sm:h-4 sm:w-4 mr-2" />
              )}
              {copied ? 'Copied!' : 'Copy Link'}
            </Button>
          </div>
        </div>
      </PopoverContent>
    </Popover>
  )
}