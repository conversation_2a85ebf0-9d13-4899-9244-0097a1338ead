"use client"
import { <PERSON><PERSON>eader } from "@/components/top-header"
import { <PERSON><PERSON> } from "@/components/header"
import { <PERSON><PERSON> } from "@/components/footer"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Checkbox } from "@/components/ui/checkbox"
import { Separator } from "@/components/ui/separator"
import { Eye, EyeOff, Mail, Lock, AlertCircle, Shield, Crown, Settings, Users, BarChart } from "lucide-react"
import Link from "next/link"
import { useState } from "react"
import { useAuth } from "@/contexts/AuthContext"
import { useRouter } from "next/navigation"
import AuthRoutingService from "@/lib/services/authRouting"
import toast from 'react-hot-toast'

export default function AdminLoginPage() {
  const [showPassword, setShowPassword] = useState(false)
  const [isPhone, setIsPhone] = useState(false)
  const [isForgotPassword, setIsForgotPassword] = useState(false)
  const [isResetPassword, setIsResetPassword] = useState(false)
  const [formData, setFormData] = useState({
    emailOrPhone: '',
    password: '',
    resetCode: '',
    newPassword: '',
    rememberMe: false
  })
  const [error, setError] = useState('')
  const [loading, setLoading] = useState(false)

  const { signInWithPassword, requestPasswordReset, confirmPasswordReset, userType, userProfile } = useAuth()
  const router = useRouter()

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    })
  }

  const validateInput = (input: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    const phoneRegex = /^(\+91|91)?[6-9]\d{9}$/ // Indian phone number format

    if (emailRegex.test(input)) {
      setIsPhone(false)
      return true
    } else if (phoneRegex.test(input.replace(/\s|-|\(|\)/g, ''))) {
      setIsPhone(true)
      return true
    }
    return false
  }

  const handleSignIn = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError('')

    if (!validateInput(formData.emailOrPhone)) {
      setError('Please enter a valid email address or phone number')
      setLoading(false)
      return
    }

    try {
      await signInWithPassword(formData.emailOrPhone, formData.password)
      toast.success('Welcome back! Redirecting to admin dashboard...')

      // Wait a moment for user profile to be fetched, then route appropriately
      setTimeout(() => {
        const dashboardRoute = AuthRoutingService.getDashboardRoute(userType, userProfile)
        router.push(dashboardRoute)
      }, 1000)
    } catch (error: any) {
      // Handle unconfirmed admin account
      if (error.name === 'UserNotConfirmedException') {
        toast.error('Admin account not confirmed. Please contact system administrator.')
        setError('Admin account not confirmed. Please contact system administrator.')
        setLoading(false)
        return
      }

      toast.error(error.message || 'Failed to sign in')
      setError(error.message || 'Failed to sign in')
    } finally {
      setLoading(false)
    }
  }

  const handleForgotPassword = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError('')

    if (!validateInput(formData.emailOrPhone)) {
      setError('Please enter a valid email address or phone number')
      setLoading(false)
      return
    }

    try {
      await requestPasswordReset(formData.emailOrPhone)
      toast.success('Reset code sent! Please check your email/phone.')
      setIsResetPassword(true)
    } catch (error: any) {
      toast.error(error.message || 'Failed to send reset code')
      setError(error.message || 'Failed to send reset code')
    } finally {
      setLoading(false)
    }
  }

  const handleResetPassword = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError('')

    if (formData.newPassword.length < 8) {
      setError('Password must be at least 8 characters long')
      setLoading(false)
      return
    }

    try {
      await confirmPasswordReset(formData.emailOrPhone, formData.resetCode, formData.newPassword)
      toast.success('Password reset successfully! Please sign in.')
      setIsForgotPassword(false)
      setIsResetPassword(false)
      setFormData({ ...formData, password: formData.newPassword, resetCode: '', newPassword: '' })
    } catch (error: any) {
      toast.error(error.message || 'Failed to reset password')
      setError(error.message || 'Failed to reset password')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-white flex flex-col">
      <TopHeader />
      <Header />
      <section className="flex flex-1 items-center justify-center bg-gradient-to-br from-orange-50 to-purple-50" style={{paddingTop: 40, paddingBottom: 40}}>
        <div className="w-full max-w-6xl mx-auto bg-white rounded-xl shadow-lg overflow-hidden flex flex-col lg:flex-row">
          {/* Left: Admin Info & Features */}
          <div className="lg:w-1/2 w-full bg-gradient-to-br from-orange-600 to-purple-600 p-8 lg:p-12 text-white">
            <div className="h-full flex flex-col justify-center">
              <div className="mb-8">
                <div className="flex items-center gap-3 mb-4">
                  <Shield className="w-8 h-8" />
                  <h1 className="text-3xl lg:text-4xl font-bold">
                    Admin Portal
                  </h1>
                </div>
                <p className="text-lg opacity-90 mb-6">
                  Secure access to BookmyFestive administration panel. Manage users, content, and platform operations.
                </p>
              </div>
              
              <div className="space-y-4 mb-8">
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center">
                    <Users className="w-4 h-4" />
                  </div>
                  <span>User Management & Analytics</span>
                </div>
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center">
                    <Settings className="w-4 h-4" />
                  </div>
                  <span>Platform Configuration</span>
                </div>
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center">
                    <BarChart className="w-4 h-4" />
                  </div>
                  <span>Reports & Insights</span>
                </div>
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center">
                    <Crown className="w-4 h-4" />
                  </div>
                  <span>Administrative Controls</span>
                </div>
              </div>

              <div className="text-center">
                <div className="text-2xl font-bold">Secure Access</div>
                <div className="text-sm opacity-80">Administrator Authentication Required</div>
              </div>
            </div>
          </div>

          {/* Right: Login Form */}
          <div className="lg:w-1/2 w-full flex flex-col justify-center p-8 lg:p-12">
            <div className="mb-6 text-center">
              <h2 className="text-2xl lg:text-3xl font-bold text-gray-900 mb-2">
                {isForgotPassword ? (isResetPassword ? 'Reset Password' : 'Forgot Password') : 'Administrator Sign In'}
              </h2>
              <p className="text-gray-600">
                {isForgotPassword ? (isResetPassword ? 'Enter the code and your new password' : 'Enter your email or phone to reset password') : 'Access the admin dashboard with your credentials'}
              </p>
            </div>

            {error && (
              <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg flex items-center gap-2 text-red-700">
                <AlertCircle className="h-4 w-4" />
                <span className="text-sm">{error}</span>
              </div>
            )}

            {/* Form content */}
            {isForgotPassword ? (
              isResetPassword ? (
                <form onSubmit={handleResetPassword} className="space-y-4">
                  <div className="relative">
                    <Input
                      name="resetCode"
                      type="text"
                      placeholder="Enter reset code"
                      className="pl-12"
                      value={formData.resetCode}
                      onChange={handleInputChange}
                      required
                    />
                    <span className="absolute left-4 top-1/2 -translate-y-1/2 text-orange-600">
                      <AlertCircle className="h-5 w-5" />
                    </span>
                  </div>
                  <div className="relative">
                    <Input
                      name="newPassword"
                      type={showPassword ? "text" : "password"}
                      placeholder="New Password*"
                      className="pl-12 pr-12"
                      value={formData.newPassword}
                      onChange={handleInputChange}
                      required
                    />
                    <span className="absolute left-4 top-1/2 -translate-y-1/2 text-orange-600">
                      <Lock className="h-5 w-5" />
                    </span>
                    <button
                      type="button"
                      className="absolute right-4 top-1/2 -translate-y-1/2 text-gray-500 hover:text-gray-700"
                      onClick={() => setShowPassword(!showPassword)}
                    >
                      {showPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
                    </button>
                  </div>
                  <Button type="submit" className="w-full bg-orange-600 hover:bg-orange-700" disabled={loading}>
                    {loading ? 'Resetting...' : 'Reset Password'}
                  </Button>
                  <Button
                    type="button"
                    variant="outline"
                    className="w-full"
                    onClick={() => {
                      setIsForgotPassword(false)
                      setIsResetPassword(false)
                    }}
                  >
                    Back to Sign In
                  </Button>
                </form>
              ) : (
                <form onSubmit={handleForgotPassword} className="space-y-4">
                  <div className="relative">
                    <Input
                      name="emailOrPhone"
                      type="text"
                      placeholder="Enter email or phone number"
                      className="pl-12"
                      value={formData.emailOrPhone}
                      onChange={handleInputChange}
                      required
                    />
                    <span className="absolute left-4 top-1/2 -translate-y-1/2 text-orange-600">
                      <Mail className="h-5 w-5" />
                    </span>
                  </div>
                  <Button type="submit" className="w-full bg-orange-600 hover:bg-orange-700" disabled={loading}>
                    {loading ? 'Sending...' : 'Send Reset Code'}
                  </Button>
                  <Button
                    type="button"
                    variant="outline"
                    className="w-full"
                    onClick={() => setIsForgotPassword(false)}
                  >
                    Back to Sign In
                  </Button>
                </form>
              )
            ) : (
              <form onSubmit={handleSignIn} className="space-y-4">
                <div className="relative">
                  <Input
                    name="emailOrPhone"
                    type="text"
                    placeholder="Admin Email or Phone Number*"
                    className="pl-12"
                    value={formData.emailOrPhone}
                    onChange={handleInputChange}
                    required
                  />
                  <span className="absolute left-4 top-1/2 -translate-y-1/2 text-orange-600">
                    <Mail className="h-5 w-5" />
                  </span>
                </div>
                <div className="relative">
                  <Input
                    name="password"
                    type={showPassword ? "text" : "password"}
                    placeholder="Password*"
                    className="pl-12 pr-12"
                    value={formData.password}
                    onChange={handleInputChange}
                    required
                  />
                  <span className="absolute left-4 top-1/2 -translate-y-1/2 text-orange-600">
                    <Lock className="h-5 w-5" />
                  </span>
                  <button
                    type="button"
                    className="absolute right-4 top-1/2 -translate-y-1/2 text-gray-500 hover:text-gray-700"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
                  </button>
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Checkbox 
                      id="remember" 
                      checked={formData.rememberMe}
                      onCheckedChange={(checked) => setFormData({ ...formData, rememberMe: checked as boolean })}
                    />
                    <label htmlFor="remember" className="text-sm text-gray-600">
                      Remember me
                    </label>
                  </div>
                  <button
                    type="button"
                    className="text-sm text-orange-600 hover:underline"
                    onClick={() => setIsForgotPassword(true)}
                  >
                    Forgot password?
                  </button>
                </div>

                <Button type="submit" className="w-full bg-orange-600 hover:bg-orange-700" disabled={loading}>
                  {loading ? 'Signing In...' : 'Sign In to Admin Panel'}
                </Button>
              </form>
            )}

            <div className="mt-6">
              <Separator className="my-4" />
              <div className="text-center space-y-2">
                <div className="text-sm text-gray-600">
                  <span>Need a different account type?</span>
                </div>
                <div className="flex justify-center gap-4">
                  <Link href="/login" className="text-blue-600 hover:underline text-sm font-medium">
                    Customer Login
                  </Link>
                  <Link href="/vendor-login" className="text-primary hover:underline text-sm font-medium">
                    Business Login
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
      <Footer />
    </div>
  )
}
