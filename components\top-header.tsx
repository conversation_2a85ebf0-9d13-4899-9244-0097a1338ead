"use client"

import { useState, useEffect } from 'react'
import { ChevronDown, MapPin, Globe, Edit, Download, Phone } from 'lucide-react'
import Link from 'next/link'
import { Button } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { useSafeTranslation } from '@/hooks/use-safe-translation'
import { useStateContext, INDIAN_STATES } from '@/contexts/StateContext'
import LanguageSelector from '@/components/LanguageSelector'
import { useTranslation } from '@/hooks/useTranslation'



export function TopHeader() {
  const { t } = useTranslation();
  const { selectedState, setSelectedState } = useStateContext()
  const [selectedLanguage, setSelectedLanguage] = useState('English')

  // Load saved preferences from localStorage
  useEffect(() => {
    const savedLanguage = localStorage.getItem('selectedLanguage')
    if (savedLanguage) {
      setSelectedLanguage(savedLanguage)
    }
  }, [])

  const handleStateChange = (state: typeof INDIAN_STATES[0]) => {
    setSelectedState(state)

    // Auto-select regional language when state changes
    setSelectedLanguage(state.language)
    localStorage.setItem('selectedLanguage', state.language)

    // Update i18n language if available
    if (state.languageCode && i18n.language !== state.languageCode) {
      i18n.changeLanguage(state.languageCode)
    }
  }

  const handleLanguageChange = (language: string) => {
    setSelectedLanguage(language)
    localStorage.setItem('selectedLanguage', language)

    // Update i18n language
    if (language === 'English') {
      i18n.changeLanguage('en')
    } else {
      const languageCode = selectedState.languageCode
      if (languageCode) {
        i18n.changeLanguage(languageCode)
      }
    }
  }

  const getLanguageOptions = () => {
    return [
      'English',
      selectedState.language
    ].filter((lang, index, arr) => arr.indexOf(lang) === index) // Remove duplicates
  }

  return (
    <div className="bg-primary text-white py-2 px-4 fixed top-0 left-0 right-0 z-[60] shadow-sm hidden md:block">
      <div className="container mx-auto flex items-center justify-between">
        {/* Left side - Platform tagline */}
        <div className="text-xs sm:text-sm font-medium truncate">
          {t('topHeader.tagline', "India's Favourite Festive Planning Platform")}
        </div>

        {/* Right side - Dropdowns */}
        <div className="flex items-center gap-2 sm:gap-4">
         
          {/* Language Selector */}
          <LanguageSelector />
          {/* Contact, Write Review & Download App links */}
          <div className="hidden lg:flex items-center gap-4">
            <Link href="/contact" className="flex items-center text-white hover:text-accent transition-colors duration-200 px-3 py-1 text-sm">
              <Phone className="w-4 h-4 mr-2" />
              {t('topHeader.contact', 'Contact Us')}
            </Link>
            <Link href="/reviews" className="flex items-center text-white hover:text-accent transition-colors duration-200 px-3 py-1 text-sm">
              <Edit className="w-4 h-4 mr-2" />
              {t('topHeader.writeReview', 'Write A Review')}
            </Link>
            <Link href="/help" className="flex items-center text-white hover:text-accent transition-colors duration-200 px-3 py-1 text-sm">
              <Globe className="w-4 h-4 mr-2" />
              {t('topHeader.help', 'Help')}
            </Link>
            <Link href="/download-app" className="flex items-center text-white hover:text-accent transition-colors duration-200 px-3 py-1 text-sm">
              <Download className="w-4 h-4 mr-2" />
              {t('topHeader.downloadApp', 'Download App')}
            </Link>
          </div>

          {/* Mobile Contact Link */}
          <div className="lg:hidden">
            <Link href="/contact" className="flex items-center text-white hover:text-accent transition-colors duration-200 p-2">
              <Phone className="w-3 h-3" />
            </Link>
          </div>
        </div>
      </div>
    </div>
  )
}
