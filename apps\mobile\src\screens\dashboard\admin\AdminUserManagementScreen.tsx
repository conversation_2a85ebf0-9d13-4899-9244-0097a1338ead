import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Alert,
  Image,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../../../providers/ThemeProvider';
import { useAuth } from '../../../providers/AuthProvider';
import { Header } from '../../../components/Header';
import { Card, Input, Button, Badge } from '../../../components/ui';
import type { StackNavigationProp } from '@react-navigation/stack';
import type { AppStackParamList } from '../../../navigation/AppNavigator';

type NavigationProp = StackNavigationProp<AppStackParamList>;

interface User {
  id: string;
  name: string;
  email: string;
  phone: string;
  role: 'customer' | 'vendor' | 'admin' | 'super_admin';
  status: 'active' | 'inactive' | 'suspended' | 'pending';
  avatar?: string;
  createdAt: string;
  lastLogin?: string;
  isVerified: boolean;
  businessInfo?: {
    businessName: string;
    businessType: string;
  };
  stats: {
    totalOrders: number;
    totalSpent: number;
    totalReviews: number;
  };
}

export default function AdminUserManagementScreen() {
  const { theme } = useTheme();
  const navigation = useNavigation<NavigationProp>();
  const { user } = useAuth();
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedRole, setSelectedRole] = useState('All');
  const [selectedStatus, setSelectedStatus] = useState('All');

  const roles = ['All', 'customer', 'vendor', 'admin'];
  const statuses = ['All', 'active', 'inactive', 'suspended', 'pending'];

  useEffect(() => {
    loadUsers();
  }, []);

  const loadUsers = async () => {
    try {
      setLoading(true);
      // TODO: Replace with actual API call
      const mockUsers: User[] = [
        {
          id: '1',
          name: 'Priya Sharma',
          email: '<EMAIL>',
          phone: '+91 8148376909',
          role: 'customer',
          status: 'active',
          avatar: '/placeholder-image.jpg
          createdAt: '2024-01-15T10:00:00Z',
          lastLogin: '2024-01-25T14:30:00Z',
          isVerified: true,
          stats: {
            totalOrders: 5,
            totalSpent: 125000,
            totalReviews: 3,
          },
        },
        {
          id: '2',
          name: 'Elegant Decorations',
          email: '<EMAIL>',
          phone: '+91 9876543211',
          role: 'vendor',
          status: 'active',
          avatar: '/placeholder-image.jpg
          createdAt: '2024-01-10T09:00:00Z',
          lastLogin: '2024-01-25T16:45:00Z',
          isVerified: true,
          businessInfo: {
            businessName: 'Elegant Decorations',
            businessType: 'Decorator',
          },
          stats: {
            totalOrders: 45,
            totalSpent: 0,
            totalReviews: 89,
          },
        },
        {
          id: '3',
          name: 'Rahul Kumar',
          email: '<EMAIL>',
          phone: '+91 9876543212',
          role: 'customer',
          status: 'pending',
          createdAt: '2024-01-24T11:00:00Z',
          isVerified: false,
          stats: {
            totalOrders: 0,
            totalSpent: 0,
            totalReviews: 0,
          },
        },
        {
          id: '4',
          name: 'Admin User',
          email: '<EMAIL>',
          phone: '+91 9876543213',
          role: 'admin',
          status: 'active',
          avatar: '/placeholder-image.jpg
          createdAt: '2024-01-01T00:00:00Z',
          lastLogin: '2024-01-25T18:00:00Z',
          isVerified: true,
          stats: {
            totalOrders: 0,
            totalSpent: 0,
            totalReviews: 0,
          },
        },
      ];
      
      setUsers(mockUsers);
    } catch (error) {
      console.error('Error loading users:', error);
      Alert.alert('Error', 'Failed to load users');
    } finally {
      setLoading(false);
    }
  };

  const filteredUsers = users.filter(user => {
    const matchesSearch = user.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         user.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         user.phone.includes(searchQuery);
    const matchesRole = selectedRole === 'All' || user.role === selectedRole;
    const matchesStatus = selectedStatus === 'All' || user.status === selectedStatus;
    return matchesSearch && matchesRole && matchesStatus;
  });

  const handleUserPress = (user: User) => {
    // TODO: Navigate to user detail screen
    Alert.alert('User Details', `View details for ${user.name}`);
  };

  const handleChangeUserStatus = (userId: string, newStatus: User['status']) => {
    Alert.alert(
      'Change Status',
      `Are you sure you want to change this user's status to ${newStatus}?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Confirm',
          onPress: () => {
            setUsers(prevUsers =>
              prevUsers.map(user =>
                user.id === userId ? { ...user, status: newStatus } : user
              )
            );
          },
        },
      ]
    );
  };

  const handleDeleteUser = (userId: string) => {
    Alert.alert(
      'Delete User',
      'Are you sure you want to delete this user? This action cannot be undone.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: () => {
            setUsers(prevUsers => prevUsers.filter(user => user.id !== userId));
          },
        },
      ]
    );
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-IN', {
      day: 'numeric',
      month: 'short',
      year: 'numeric',
    });
  };

  const formatCurrency = (amount: number) => {
    return `₹${amount.toLocaleString('en-IN')}`;
  };

  const getStatusColor = (status: User['status']) => {
    switch (status) {
      case 'active': return theme.colors.success;
      case 'inactive': return theme.colors.textSecondary;
      case 'suspended': return theme.colors.destructive;
      case 'pending': return theme.colors.warning;
      default: return theme.colors.textSecondary;
    }
  };

  const getRoleColor = (role: User['role']) => {
    switch (role) {
      case 'admin': return theme.colors.destructive;
      case 'vendor': return theme.colors.primary;
      case 'customer': return theme.colors.info;
      default: return theme.colors.textSecondary;
    }
  };

  const renderFilterRow = () => (
    <View style={styles.filterContainer}>
      <View style={styles.filterSection}>
        <Text style={[styles.filterLabel, { color: theme.colors.text }]}>Role:</Text>
        <FlatList
          data={roles}
          horizontal
          showsHorizontalScrollIndicator={false}
          keyExtractor={(item) => item}
          renderItem={({ item }) => (
            <TouchableOpacity
              style={[
                styles.filterButton,
                selectedRole === item && styles.filterButtonActive,
                { borderColor: theme.colors.border }
              ]}
              onPress={() => setSelectedRole(item)}
            >
              <Text
                style={[
                  styles.filterButtonText,
                  { color: selectedRole === item ? theme.colors.primary : theme.colors.textSecondary }
                ]}
              >
                {item}
              </Text>
            </TouchableOpacity>
          )}
        />
      </View>
      
      <View style={styles.filterSection}>
        <Text style={[styles.filterLabel, { color: theme.colors.text }]}>Status:</Text>
        <FlatList
          data={statuses}
          horizontal
          showsHorizontalScrollIndicator={false}
          keyExtractor={(item) => item}
          renderItem={({ item }) => (
            <TouchableOpacity
              style={[
                styles.filterButton,
                selectedStatus === item && styles.filterButtonActive,
                { borderColor: theme.colors.border }
              ]}
              onPress={() => setSelectedStatus(item)}
            >
              <Text
                style={[
                  styles.filterButtonText,
                  { color: selectedStatus === item ? theme.colors.primary : theme.colors.textSecondary }
                ]}
              >
                {item}
              </Text>
            </TouchableOpacity>
          )}
        />
      </View>
    </View>
  );

  const renderUser = ({ item }: { item: User }) => (
    <Card style={styles.userCard}>
      <TouchableOpacity onPress={() => handleUserPress(item)}>
        <View style={styles.userHeader}>
          <View style={styles.userAvatarContainer}>
            {item.avatar ? (
              <Image source={{ uri: item.avatar }} style={styles.userAvatar} />
            ) : (
              <View style={[styles.userAvatarPlaceholder, { backgroundColor: theme.colors.border }]}>
                <Ionicons name="person" size={24} color={theme.colors.textSecondary} />
              </View>
            )}
            {item.isVerified && (
              <View style={styles.verifiedBadge}>
                <Ionicons name="checkmark-circle" size={16} color={theme.colors.success} />
              </View>
            )}
          </View>
          
          <View style={styles.userInfo}>
            <View style={styles.userTitleRow}>
              <Text style={[styles.userName, { color: theme.colors.text }]} numberOfLines={1}>
                {item.name}
              </Text>
              <View style={styles.badgeContainer}>
                <Badge 
                  variant="outline"
                  style={[styles.roleBadge, { borderColor: getRoleColor(item.role) }]}
                >
                  <Text style={[styles.roleBadgeText, { color: getRoleColor(item.role) }]}>
                    {item.role}
                  </Text>
                </Badge>
                <Badge 
                  style={[styles.statusBadge, { backgroundColor: getStatusColor(item.status) }]}
                >
                  <Text style={styles.statusBadgeText}>{item.status}</Text>
                </Badge>
              </View>
            </View>
            
            <Text style={[styles.userEmail, { color: theme.colors.textSecondary }]} numberOfLines={1}>
              {item.email}
            </Text>
            
            {item.businessInfo && (
              <Text style={[styles.businessInfo, { color: theme.colors.primary }]} numberOfLines={1}>
                {item.businessInfo.businessName} • {item.businessInfo.businessType}
              </Text>
            )}
            
            <View style={styles.userMeta}>
              <Text style={[styles.metaText, { color: theme.colors.textSecondary }]}>
                Joined: {formatDate(item.createdAt)}
              </Text>
              {item.lastLogin && (
                <Text style={[styles.metaText, { color: theme.colors.textSecondary }]}>
                  Last login: {formatDate(item.lastLogin)}
                </Text>
              )}
            </View>
            
            <View style={styles.userStats}>
              <View style={styles.statItem}>
                <Text style={[styles.statValue, { color: theme.colors.text }]}>
                  {item.stats.totalOrders}
                </Text>
                <Text style={[styles.statLabel, { color: theme.colors.textSecondary }]}>
                  Orders
                </Text>
              </View>
              
              {item.role === 'customer' && (
                <View style={styles.statItem}>
                  <Text style={[styles.statValue, { color: theme.colors.text }]}>
                    {formatCurrency(item.stats.totalSpent)}
                  </Text>
                  <Text style={[styles.statLabel, { color: theme.colors.textSecondary }]}>
                    Spent
                  </Text>
                </View>
              )}
              
              <View style={styles.statItem}>
                <Text style={[styles.statValue, { color: theme.colors.text }]}>
                  {item.stats.totalReviews}
                </Text>
                <Text style={[styles.statLabel, { color: theme.colors.textSecondary }]}>
                  Reviews
                </Text>
              </View>
            </View>
          </View>
        </View>
      </TouchableOpacity>
      
      <View style={styles.userActions}>
        <TouchableOpacity
          style={[styles.actionButton, { backgroundColor: theme.colors.info }]}
          onPress={() => handleUserPress(item)}
        >
          <Ionicons name="eye" size={16} color="white" />
          <Text style={styles.actionButtonText}>View</Text>
        </TouchableOpacity>
        
        {item.status === 'active' ? (
          <TouchableOpacity
            style={[styles.actionButton, { backgroundColor: theme.colors.warning }]}
            onPress={() => handleChangeUserStatus(item.id, 'suspended')}
          >
            <Ionicons name="ban" size={16} color="white" />
            <Text style={styles.actionButtonText}>Suspend</Text>
          </TouchableOpacity>
        ) : (
          <TouchableOpacity
            style={[styles.actionButton, { backgroundColor: theme.colors.success }]}
            onPress={() => handleChangeUserStatus(item.id, 'active')}
          >
            <Ionicons name="checkmark" size={16} color="white" />
            <Text style={styles.actionButtonText}>Activate</Text>
          </TouchableOpacity>
        )}
        
        <TouchableOpacity
          style={[styles.actionButton, { backgroundColor: theme.colors.destructive }]}
          onPress={() => handleDeleteUser(item.id)}
        >
          <Ionicons name="trash" size={16} color="white" />
          <Text style={styles.actionButtonText}>Delete</Text>
        </TouchableOpacity>
      </View>
    </Card>
  );

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <Header title="User Management" showBack />
      
      {/* Search Bar */}
      <View style={styles.searchContainer}>
        <Input
          placeholder="Search users..."
          value={searchQuery}
          onChangeText={setSearchQuery}
          leftIcon={<Ionicons name="search" size={20} color={theme.colors.textSecondary} />}
        />
      </View>

      {/* Filters */}
      {renderFilterRow()}

      {/* Users List */}
      <FlatList
        data={filteredUsers}
        renderItem={renderUser}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.usersList}
        showsVerticalScrollIndicator={false}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  searchContainer: {
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  filterContainer: {
    paddingHorizontal: 16,
    marginBottom: 16,
    gap: 12,
  },
  filterSection: {
    gap: 8,
  },
  filterLabel: {
    fontSize: 14,
    fontWeight: '500',
  },
  filterButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    borderWidth: 1,
    marginRight: 8,
  },
  filterButtonActive: {
    backgroundColor: 'rgba(97, 15, 19, 0.1)',
  },
  filterButtonText: {
    fontSize: 12,
    fontWeight: '500',
  },
  usersList: {
    paddingHorizontal: 16,
    paddingBottom: 20,
  },
  userCard: {
    marginBottom: 16,
    padding: 16,
  },
  userHeader: {
    flexDirection: 'row',
    marginBottom: 12,
  },
  userAvatarContainer: {
    position: 'relative',
    marginRight: 12,
  },
  userAvatar: {
    width: 60,
    height: 60,
    borderRadius: 30,
  },
  userAvatarPlaceholder: {
    width: 60,
    height: 60,
    borderRadius: 30,
    justifyContent: 'center',
    alignItems: 'center',
  },
  verifiedBadge: {
    position: 'absolute',
    bottom: -2,
    right: -2,
    backgroundColor: 'white',
    borderRadius: 10,
  },
  userInfo: {
    flex: 1,
  },
  userTitleRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 4,
  },
  userName: {
    fontSize: 16,
    fontWeight: '600',
    flex: 1,
    marginRight: 8,
  },
  badgeContainer: {
    flexDirection: 'row',
    gap: 4,
  },
  roleBadge: {
    paddingHorizontal: 6,
    paddingVertical: 2,
  },
  roleBadgeText: {
    fontSize: 10,
    fontWeight: '500',
  },
  statusBadge: {
    paddingHorizontal: 6,
    paddingVertical: 2,
  },
  statusBadgeText: {
    color: 'white',
    fontSize: 10,
    fontWeight: '500',
  },
  userEmail: {
    fontSize: 14,
    marginBottom: 2,
  },
  businessInfo: {
    fontSize: 12,
    fontWeight: '500',
    marginBottom: 4,
  },
  userMeta: {
    marginBottom: 8,
    gap: 2,
  },
  metaText: {
    fontSize: 12,
  },
  userStats: {
    flexDirection: 'row',
    gap: 16,
  },
  statItem: {
    alignItems: 'center',
  },
  statValue: {
    fontSize: 14,
    fontWeight: 'bold',
  },
  statLabel: {
    fontSize: 10,
  },
  userActions: {
    flexDirection: 'row',
    gap: 8,
  },
  actionButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 6,
    gap: 4,
  },
  actionButtonText: {
    color: 'white',
    fontSize: 12,
    fontWeight: '500',
  },
});
