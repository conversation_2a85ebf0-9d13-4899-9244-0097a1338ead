"use client"

import { useRouter } from 'next/navigation'
import { useEffect, useCallback } from 'react'

/**
 * Navigation Performance Optimizer
 * 
 * Addresses slow first-click issues by implementing:
 * 1. Link prefetching
 * 2. Route preloading
 * 3. Component preloading
 * 4. Cache warming
 */

// Common routes that should be prefetched
const CRITICAL_ROUTES = [
  '/vendors',
  '/venues', 
  '/shop',
  '/planning',
  '/dashboard',
  '/login',
  '/download-app'
]

// Dashboard routes for authenticated users
const DASHBOARD_ROUTES = [
  '/dashboard/vendor',
  '/dashboard/venues',
  '/dashboard/shop',
  '/dashboard/admin-users',
  '/dashboard/admin-vendors'
]

/**
 * Prefetch critical routes on page load
 */
export function usePrefetchCriticalRoutes() {
  const router = useRouter()

  useEffect(() => {
    // Prefetch critical routes after a short delay
    const timer = setTimeout(() => {
      CRITICAL_ROUTES.forEach(route => {
        router.prefetch(route)
      })
    }, 1000) // Wait 1 second after page load

    return () => clearTimeout(timer)
  }, [router])
}

/**
 * Prefetch dashboard routes for authenticated users
 */
export function usePrefetchDashboardRoutes(isAuthenticated: boolean, userType: string | null) {
  const router = useRouter()

  useEffect(() => {
    if (!isAuthenticated || !userType) return

    const timer = setTimeout(() => {
      DASHBOARD_ROUTES.forEach(route => {
        router.prefetch(route)
      })
    }, 2000) // Wait 2 seconds for authenticated users

    return () => clearTimeout(timer)
  }, [router, isAuthenticated, userType])
}

/**
 * Optimized navigation hook with instant feedback
 */
export function useOptimizedNavigation() {
  const router = useRouter()

  const navigateWithPreload = useCallback(async (href: string) => {
    // Start prefetching immediately
    router.prefetch(href)
    
    // Small delay to allow prefetch to start
    await new Promise(resolve => setTimeout(resolve, 50))
    
    // Navigate
    router.push(href)
  }, [router])

  return { navigateWithPreload }
}

/**
 * Intersection Observer for link prefetching
 */
export function useLinkPrefetching() {
  const router = useRouter()

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const link = entry.target as HTMLAnchorElement
            const href = link.getAttribute('href')
            if (href && href.startsWith('/')) {
              router.prefetch(href)
            }
          }
        })
      },
      {
        rootMargin: '100px', // Start prefetching when link is 100px away from viewport
      }
    )

    // Observe all internal links
    const links = document.querySelectorAll('a[href^="/"]')
    links.forEach((link) => observer.observe(link))

    return () => {
      observer.disconnect()
    }
  }, [router])
}

/**
 * Preload components on hover
 */
export function useHoverPreload() {
  const router = useRouter()

  const handleMouseEnter = useCallback((href: string) => {
    router.prefetch(href)
  }, [router])

  return { handleMouseEnter }
}

/**
 * Cache warming for frequently accessed data
 */
export function useCacheWarming() {
  useEffect(() => {
    // Warm up localStorage cache
    const warmCache = () => {
      try {
        // Pre-load common data that might be needed
        const commonKeys = ['user-preferences', 'cart-data', 'recent-searches']
        commonKeys.forEach(key => {
          localStorage.getItem(key) // This warms the cache
        })
      } catch (error) {
        console.error('Cache warming failed:', error)
      }
    }

    // Warm cache after a delay
    const timer = setTimeout(warmCache, 500)
    return () => clearTimeout(timer)
  }, [])
}

// Note: OptimizedLink component moved to NavigationOptimizer.tsx to avoid JSX in .ts file

/**
 * Performance monitoring for navigation
 */
export function useNavigationPerformance() {
  const router = useRouter()

  useEffect(() => {
    const startTime = performance.now()

    const handleRouteChange = () => {
      const endTime = performance.now()
      const duration = endTime - startTime

      if (duration > 1000) {
        console.warn(`Slow navigation detected: ${duration.toFixed(2)}ms`)
      }
    }

    // Monitor route changes
    const originalPush = router.push
    router.push = (...args) => {
      const start = performance.now()
      const result = originalPush.apply(router, args)
      
      Promise.resolve(result).then(() => {
        const end = performance.now()
        if (end - start > 500) {
          console.warn(`Slow route change: ${(end - start).toFixed(2)}ms`)
        }
      })
      
      return result
    }

    return () => {
      // Restore original push method
      router.push = originalPush
    }
  }, [router])
}

export default {
  usePrefetchCriticalRoutes,
  usePrefetchDashboardRoutes,
  useOptimizedNavigation,
  useLinkPrefetching,
  useHoverPreload,
  useCacheWarming,
  OptimizedLink,
  useNavigationPerformance
}
