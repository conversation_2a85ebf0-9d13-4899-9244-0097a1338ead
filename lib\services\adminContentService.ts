import { generateClient } from 'aws-amplify/api';
import { getCurrentUser } from 'aws-amplify/auth';
import {
  createVendor,
  updateVendor,
  deleteVendor,
  createVenue,
  updateVenue,
  deleteVenue,
  createShop,
  updateShop,
  deleteShop
} from '@/src/graphql/mutations';
import {
  listVendors,
  getVendor,
  vendorsByUserId,
  listVenues,
  getVenue,
  venuesByUserId,
  listShops,
  getShop,
  shopsByUserId,
  getUserProfile,
  userProfilesByUserId
} from '@/src/graphql/queries';

// Create client with user pool authentication for authenticated operations
const authenticatedClient = generateClient({
  authMode: 'userPool'
});

// Create client with API key for public operations
const publicClient = generateClient({
  authMode: 'apiKey'
});

// Create admin client with API key for admin operations (bypasses user-level restrictions)
const adminClient = generateClient({
  authMode: 'apiKey'
});

export interface ServiceResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
}

export interface ContentFilters {
  status?: string;
  verified?: boolean;
  featured?: boolean;
  category?: string;
  city?: string;
  state?: string;
  searchTerm?: string;
  dateFrom?: string;
  dateTo?: string;
  userId?: string;
  inStock?: boolean;
}

export interface ContentStatistics {
  totalVendors: number;
  totalVenues: number;
  totalShops: number;
  verifiedVendors: number;
  featuredVendors: number;
  activeVenues: number;
  inStockProducts: number;
  recentContent: number;
}

export class AdminContentService {
  /**
   * Check if current user is admin
   */
  static async isCurrentUserAdmin(): Promise<boolean> {
    try {
      const user = await getCurrentUser();
      if (!user) return false;

      // Get user profile using the correct query (by userId index)
      const result = await publicClient.graphql({
        query: userProfilesByUserId,
        variables: {
          userId: user.userId,
          limit: 1
        }
      });

      const profiles = result.data?.userProfilesByUserId?.items || [];
      const userProfile = profiles.length > 0 ? profiles[0] : null;
      const isAdmin = userProfile?.isAdmin || userProfile?.isSuperAdmin || false;

      console.log('Admin check result:', {
        userId: user.userId,
        profileFound: !!userProfile,
        isAdmin: userProfile?.isAdmin,
        isSuperAdmin: userProfile?.isSuperAdmin,
        role: userProfile?.role,
        finalResult: isAdmin
      });

      return isAdmin;
    } catch (error) {
      console.error('Error checking admin status:', error);
      return false;
    }
  }

  /**
   * Get all vendors with admin privileges
   */
  static async getAllVendors(filters?: ContentFilters, limit: number = 10, nextToken?: string): Promise<ServiceResponse<{vendors: any[], nextToken?: string, totalCount?: number}>> {
    try {
      let filterConditions: any = {};

      if (filters) {
        if (filters.status) {
          filterConditions.status = { eq: filters.status };
        }
        if (filters.verified !== undefined) {
          filterConditions.verified = { eq: filters.verified };
        }
        if (filters.featured !== undefined) {
          filterConditions.featured = { eq: filters.featured };
        }
        if (filters.category) {
          filterConditions.category = { eq: filters.category };
        }
        if (filters.city) {
          filterConditions.city = { eq: filters.city };
        }
        if (filters.state) {
          filterConditions.state = { eq: filters.state };
        }
        if (filters.userId) {
          filterConditions.userId = { eq: filters.userId };
        }
        if (filters.dateFrom) {
          filterConditions.createdAt = { ge: filters.dateFrom };
        }
        if (filters.dateTo) {
          if (filterConditions.createdAt) {
            filterConditions.createdAt.le = filters.dateTo;
          } else {
            filterConditions.createdAt = { le: filters.dateTo };
          }
        }
      }

      const result = await publicClient.graphql({
        query: listVendors,
        variables: {
          filter: Object.keys(filterConditions).length > 0 ? filterConditions : undefined,
          limit,
          nextToken
        }
      });

      let vendors = result.data?.listVendors?.items || [];

      // Apply search term filter on client side if provided
      if (filters?.searchTerm) {
        const searchTerm = filters.searchTerm.toLowerCase();
        vendors = vendors.filter((vendor: any) => 
          vendor.name?.toLowerCase().includes(searchTerm) ||
          vendor.description?.toLowerCase().includes(searchTerm) ||
          vendor.email?.toLowerCase().includes(searchTerm) ||
          vendor.category?.toLowerCase().includes(searchTerm)
        );
      }

      // Get total count if this is the first page
      let totalCount;
      if (!nextToken) {
        const countResult = await this.getTotalCount('vendors');
        totalCount = countResult.success ? countResult.data : undefined;
      }

      return {
        success: true,
        data: {
          vendors,
          nextToken: result.data?.listVendors?.nextToken,
          totalCount
        }
      };
    } catch (error) {
      console.error('Error fetching all vendors:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to fetch vendors'
      };
    }
  }

  /**
   * Get all venues with admin privileges
   */
  static async getAllVenues(filters?: ContentFilters, limit: number = 10, nextToken?: string): Promise<ServiceResponse<{venues: any[], nextToken?: string, totalCount?: number}>> {
    try {
      let filterConditions: any = {};

      if (filters) {
        if (filters.status) {
          filterConditions.status = { eq: filters.status };
        }
        if (filters.featured !== undefined) {
          filterConditions.featured = { eq: filters.featured };
        }
        if (filters.category) {
          filterConditions.category = { eq: filters.category };
        }
        if (filters.city) {
          filterConditions.city = { eq: filters.city };
        }
        if (filters.state) {
          filterConditions.state = { eq: filters.state };
        }
        if (filters.userId) {
          filterConditions.userId = { eq: filters.userId };
        }
        if (filters.dateFrom) {
          filterConditions.createdAt = { ge: filters.dateFrom };
        }
        if (filters.dateTo) {
          if (filterConditions.createdAt) {
            filterConditions.createdAt.le = filters.dateTo;
          } else {
            filterConditions.createdAt = { le: filters.dateTo };
          }
        }
      }

      const result = await publicClient.graphql({
        query: listVenues,
        variables: {
          filter: Object.keys(filterConditions).length > 0 ? filterConditions : undefined,
          limit,
          nextToken
        }
      });

      let venues = result.data?.listVenues?.items || [];

      // Apply search term filter on client side if provided
      if (filters?.searchTerm) {
        const searchTerm = filters.searchTerm.toLowerCase();
        venues = venues.filter((venue: any) =>
          venue.name?.toLowerCase().includes(searchTerm) ||
          venue.description?.toLowerCase().includes(searchTerm) ||
          venue.location?.toLowerCase().includes(searchTerm) ||
          venue.category?.toLowerCase().includes(searchTerm)
        );
      }

      // Get total count if this is the first page
      let totalCount;
      if (!nextToken) {
        const countResult = await this.getTotalCount('venues');
        totalCount = countResult.success ? countResult.data : undefined;
      }

      return {
        success: true,
        data: {
          venues,
          nextToken: result.data?.listVenues?.nextToken,
          totalCount
        }
      };
    } catch (error) {
      console.error('Error fetching all venues:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to fetch venues'
      };
    }
  }

  /**
   * Get all shop products with admin privileges
   */
  static async getAllShops(filters?: ContentFilters, limit: number = 10, nextToken?: string): Promise<ServiceResponse<{shops: any[], nextToken?: string, totalCount?: number}>> {
    try {
      let filterConditions: any = {};

      if (filters) {
        if (filters.status) {
          filterConditions.status = { eq: filters.status };
        }
        if (filters.featured !== undefined) {
          filterConditions.featured = { eq: filters.featured };
        }
        if (filters.category) {
          filterConditions.category = { eq: filters.category };
        }
        if (filters.userId) {
          filterConditions.userId = { eq: filters.userId };
        }
        if (filters.dateFrom) {
          filterConditions.createdAt = { ge: filters.dateFrom };
        }
        if (filters.dateTo) {
          if (filterConditions.createdAt) {
            filterConditions.createdAt.le = filters.dateTo;
          } else {
            filterConditions.createdAt = { le: filters.dateTo };
          }
        }
      }

      const result = await publicClient.graphql({
        query: listShops,
        variables: {
          filter: Object.keys(filterConditions).length > 0 ? filterConditions : undefined,
          limit,
          nextToken
        }
      });

      let shops = result.data?.listShops?.items || [];

      // Apply search term filter on client side if provided
      if (filters?.searchTerm) {
        const searchTerm = filters.searchTerm.toLowerCase();
        shops = shops.filter((shop: any) =>
          shop.name?.toLowerCase().includes(searchTerm) ||
          shop.description?.toLowerCase().includes(searchTerm) ||
          shop.brand?.toLowerCase().includes(searchTerm) ||
          shop.category?.toLowerCase().includes(searchTerm)
        );
      }

      // Get total count if this is the first page
      let totalCount;
      if (!nextToken) {
        const countResult = await this.getTotalCount('shops');
        totalCount = countResult.success ? countResult.data : undefined;
      }

      return {
        success: true,
        data: {
          shops,
          nextToken: result.data?.listShops?.nextToken,
          totalCount
        }
      };
    } catch (error) {
      console.error('Error fetching all shops:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to fetch shops'
      };
    }
  }

  /**
   * Update vendor (admin can update any vendor)
   */
  static async updateVendor(vendorId: string, updateData: any): Promise<ServiceResponse<any>> {
    try {
      const input = {
        id: vendorId,
        ...updateData
      };

      const result = await authenticatedClient.graphql({
        query: updateVendor,
        variables: { input }
      });

      return {
        success: true,
        data: result.data?.updateVendor
      };
    } catch (error) {
      console.error('Error updating vendor:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to update vendor'
      };
    }
  }

  /**
   * Delete vendor (admin only)
   */
  static async deleteVendor(vendorId: string): Promise<ServiceResponse<boolean>> {
    try {
      await authenticatedClient.graphql({
        query: deleteVendor,
        variables: { input: { id: vendorId } }
      });

      return {
        success: true,
        data: true
      };
    } catch (error) {
      console.error('Error deleting vendor:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to delete vendor'
      };
    }
  }

  /**
   * Update venue (admin can update any venue)
   */
  static async updateVenue(venueId: string, updateData: any): Promise<ServiceResponse<any>> {
    try {
      const input = {
        id: venueId,
        ...updateData
      };

      const result = await authenticatedClient.graphql({
        query: updateVenue,
        variables: { input }
      });

      return {
        success: true,
        data: result.data?.updateVenue
      };
    } catch (error) {
      console.error('Error updating venue:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to update venue'
      };
    }
  }

  /**
   * Delete venue (admin only)
   */
  static async deleteVenue(venueId: string): Promise<ServiceResponse<boolean>> {
    try {
      await authenticatedClient.graphql({
        query: deleteVenue,
        variables: { input: { id: venueId } }
      });

      return {
        success: true,
        data: true
      };
    } catch (error) {
      console.error('Error deleting venue:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to delete venue'
      };
    }
  }

  /**
   * Update shop product (admin can update any shop)
   */
  static async updateShop(shopId: string, updateData: any): Promise<ServiceResponse<any>> {
    try {
      // Check if current user is admin
      const isAdmin = await this.isCurrentUserAdmin();
      if (!isAdmin) {
        return {
          success: false,
          error: 'Unauthorized: Admin access required'
        };
      }

      const input = {
        id: shopId,
        ...updateData
      };

      const result = await adminClient.graphql({
        query: updateShop,
        variables: { input }
      });

      return {
        success: true,
        data: result.data?.updateShop
      };
    } catch (error) {
      console.error('Error updating shop:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to update shop'
      };
    }
  }

  /**
   * Delete shop product (admin only)
   */
  static async deleteShop(shopId: string): Promise<ServiceResponse<boolean>> {
    try {
      // Check if current user is admin
      const isAdmin = await this.isCurrentUserAdmin();
      if (!isAdmin) {
        return {
          success: false,
          error: 'Unauthorized: Admin access required'
        };
      }

      await adminClient.graphql({
        query: deleteShop,
        variables: { input: { id: shopId } }
      });

      return {
        success: true,
        data: true
      };
    } catch (error) {
      console.error('Error deleting shop:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to delete shop'
      };
    }
  }

  /**
   * Get total count for any entity type
   */
  static async getTotalCount(entityType: 'vendors' | 'venues' | 'shops' | 'users' | 'reviews'): Promise<ServiceResponse<number>> {
    try {
      const isAdmin = await this.isCurrentUserAdmin();
      if (!isAdmin) {
        return {
          success: false,
          error: 'Unauthorized: Admin access required'
        };
      }

      let query = '';
      switch (entityType) {
        case 'vendors':
          query = `
            query ListVendors($limit: Int) {
              listVendors(limit: $limit) {
                items { id }
              }
            }
          `;
          break;
        case 'venues':
          query = `
            query ListVenues($limit: Int) {
              listVenues(limit: $limit) {
                items { id }
              }
            }
          `;
          break;
        case 'shops':
          query = `
            query ListShops($limit: Int) {
              listShops(limit: $limit) {
                items { id }
              }
            }
          `;
          break;
        case 'users':
          query = `
            query ListUserProfiles($limit: Int) {
              listUserProfiles(limit: $limit) {
                items { id }
              }
            }
          `;
          break;
        case 'reviews':
          query = `
            query ListReviews($limit: Int) {
              listReviews(limit: $limit) {
                items { id }
              }
            }
          `;
          break;
        default:
          return {
            success: false,
            error: 'Invalid entity type'
          };
      }

      const result = await publicClient.graphql({
        query,
        variables: { limit: 1000 } // Get a large number to count
      });

      const items = result.data?.[`list${entityType.charAt(0).toUpperCase() + entityType.slice(1)}`]?.items || [];

      return {
        success: true,
        data: items.length
      };
    } catch (error) {
      console.error(`Error getting total count for ${entityType}:`, error);
      return {
        success: false,
        error: error instanceof Error ? error.message : `Failed to get total count for ${entityType}`
      };
    }
  }

  /**
   * Get all users with pagination (admin only)
   */
  static async getAllUsers(limit: number = 10, nextToken?: string): Promise<ServiceResponse<{users: any[], nextToken?: string, totalCount?: number}>> {
    try {
      // Check if current user is admin
      const isAdmin = await this.isCurrentUserAdmin();
      if (!isAdmin) {
        return {
          success: false,
          error: 'Unauthorized: Admin access required'
        };
      }

      const result = await publicClient.graphql({
        query: `
          query ListUserProfiles($limit: Int, $nextToken: String) {
            listUserProfiles(limit: $limit, nextToken: $nextToken) {
              items {
                id
                userId
                name
                email
                role
                isAdmin
                isSuperAdmin
                createdAt
                updatedAt
              }
              nextToken
            }
          }
        `,
        variables: { limit, nextToken }
      });

      // Get total count if this is the first page
      let totalCount;
      if (!nextToken) {
        const countResult = await this.getTotalCount('users');
        totalCount = countResult.success ? countResult.data : undefined;
      }

      return {
        success: true,
        data: {
          users: result.data?.listUserProfiles?.items || [],
          nextToken: result.data?.listUserProfiles?.nextToken,
          totalCount
        }
      };
    } catch (error) {
      console.error('Error fetching users:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to fetch users'
      };
    }
  }

  /**
   * Get all reviews with pagination (admin only)
   */
  static async getAllReviews(limit: number = 10, nextToken?: string): Promise<ServiceResponse<{reviews: any[], nextToken?: string}>> {
    try {
      // Check if current user is admin
      const isAdmin = await this.isCurrentUserAdmin();
      if (!isAdmin) {
        return {
          success: false,
          error: 'Unauthorized: Admin access required'
        };
      }

      const result = await publicClient.graphql({
        query: `
          query ListReviews($limit: Int, $nextToken: String) {
            listReviews(limit: $limit, nextToken: $nextToken) {
              items {
                id
                rating
                comment
                userName
                userEmail
                productId
                productType
                createdAt
                updatedAt
              }
              nextToken
            }
          }
        `,
        variables: { limit, nextToken }
      });

      return {
        success: true,
        data: {
          reviews: result.data?.listReviews?.items || [],
          nextToken: result.data?.listReviews?.nextToken
        }
      };
    } catch (error) {
      console.error('Error fetching reviews:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to fetch reviews'
      };
    }
  }

  /**
   * Get content statistics
   */
  static async getContentStatistics(): Promise<ServiceResponse<ContentStatistics>> {
    try {
      // Fetch all content in parallel
      const [vendorsResult, venuesResult, shopsResult] = await Promise.all([
        publicClient.graphql({ query: listVendors, variables: { limit: 1000 } }),
        publicClient.graphql({ query: listVenues, variables: { limit: 1000 } }),
        publicClient.graphql({ query: listShops, variables: { limit: 1000 } })
      ]);

      const vendors = vendorsResult.data?.listVendors?.items || [];
      const venues = venuesResult.data?.listVenues?.items || [];
      const shops = shopsResult.data?.listShops?.items || [];

      const now = new Date();
      const lastWeek = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);

      const stats: ContentStatistics = {
        totalVendors: vendors.length,
        totalVenues: venues.length,
        totalShops: shops.length,
        verifiedVendors: vendors.filter((v: any) => v.verified).length,
        featuredVendors: vendors.filter((v: any) => v.featured).length,
        activeVenues: venues.filter((v: any) => v.status === 'active').length,
        inStockProducts: shops.filter((s: any) => s.inStock).length,
        recentContent: [
          ...vendors.filter((v: any) => new Date(v.createdAt) > lastWeek),
          ...venues.filter((v: any) => new Date(v.createdAt) > lastWeek),
          ...shops.filter((s: any) => new Date(s.createdAt) > lastWeek)
        ].length
      };

      return {
        success: true,
        data: stats
      };
    } catch (error) {
      console.error('Error fetching content statistics:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to fetch statistics'
      };
    }
  }

  /**
   * Bulk update content status
   */
  static async bulkUpdateStatus(contentType: 'vendor' | 'venue' | 'shop', ids: string[], status: string): Promise<ServiceResponse<boolean>> {
    try {
      const updatePromises = ids.map(id => {
        const input = { id, status };

        switch (contentType) {
          case 'vendor':
            return authenticatedClient.graphql({ query: updateVendor, variables: { input } });
          case 'venue':
            return authenticatedClient.graphql({ query: updateVenue, variables: { input } });
          case 'shop':
            return authenticatedClient.graphql({ query: updateShop, variables: { input } });
          default:
            throw new Error('Invalid content type');
        }
      });

      await Promise.all(updatePromises);

      return {
        success: true,
        data: true
      };
    } catch (error) {
      console.error('Error bulk updating status:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to bulk update status'
      };
    }
  }

  /**
   * Bulk delete content
   */
  static async bulkDelete(contentType: 'vendor' | 'venue' | 'shop', ids: string[]): Promise<ServiceResponse<boolean>> {
    try {
      const deletePromises = ids.map(id => {
        const input = { id };

        switch (contentType) {
          case 'vendor':
            return authenticatedClient.graphql({ query: deleteVendor, variables: { input } });
          case 'venue':
            return authenticatedClient.graphql({ query: deleteVenue, variables: { input } });
          case 'shop':
            return authenticatedClient.graphql({ query: deleteShop, variables: { input } });
          default:
            throw new Error('Invalid content type');
        }
      });

      await Promise.all(deletePromises);

      return {
        success: true,
        data: true
      };
    } catch (error) {
      console.error('Error bulk deleting:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to bulk delete'
      };
    }
  }
}

export default AdminContentService;
