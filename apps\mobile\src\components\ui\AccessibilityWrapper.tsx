import React from 'react';
import {
  View,
  TouchableOpacity,
  AccessibilityInfo,
  AccessibilityRole,
  AccessibilityState,
  AccessibilityValue,
  ViewStyle,
} from 'react-native';
import * as Haptics from 'expo-haptics';

interface AccessibilityWrapperProps {
  children: React.ReactNode;
  accessible?: boolean;
  accessibilityLabel?: string;
  accessibilityHint?: string;
  accessibilityRole?: AccessibilityRole;
  accessibilityState?: AccessibilityState;
  accessibilityValue?: AccessibilityValue;
  accessibilityActions?: Array<{
    name: string;
    label?: string;
  }>;
  onAccessibilityAction?: (event: { nativeEvent: { actionName: string } }) => void;
  onPress?: () => void;
  onLongPress?: () => void;
  disabled?: boolean;
  style?: ViewStyle;
  hapticFeedback?: boolean;
  hapticType?: 'light' | 'medium' | 'heavy' | 'selection' | 'success' | 'warning' | 'error';
  testID?: string;
  focusable?: boolean;
  importantForAccessibility?: 'auto' | 'yes' | 'no' | 'no-hide-descendants';
}

export default function AccessibilityWrapper({
  children,
  accessible = true,
  accessibilityLabel,
  accessibilityHint,
  accessibilityRole,
  accessibilityState,
  accessibilityValue,
  accessibilityActions,
  onAccessibilityAction,
  onPress,
  onLongPress,
  disabled = false,
  style,
  hapticFeedback = true,
  hapticType = 'light',
  testID,
  focusable = true,
  importantForAccessibility = 'auto',
}: AccessibilityWrapperProps) {

  const getHapticFeedback = () => {
    switch (hapticType) {
      case 'light':
        return Haptics.ImpactFeedbackStyle.Light;
      case 'medium':
        return Haptics.ImpactFeedbackStyle.Medium;
      case 'heavy':
        return Haptics.ImpactFeedbackStyle.Heavy;
      case 'selection':
        return Haptics.SelectionAsync();
      case 'success':
        return Haptics.NotificationFeedbackType.Success;
      case 'warning':
        return Haptics.NotificationFeedbackType.Warning;
      case 'error':
        return Haptics.NotificationFeedbackType.Error;
      default:
        return Haptics.ImpactFeedbackStyle.Light;
    }
  };

  const handlePress = () => {
    if (disabled) return;

    if (hapticFeedback) {
      if (hapticType === 'selection') {
        Haptics.selectionAsync();
      } else if (['success', 'warning', 'error'].includes(hapticType)) {
        Haptics.notificationAsync(getHapticFeedback() as any);
      } else {
        Haptics.impactAsync(getHapticFeedback() as any);
      }
    }

    if (onPress) {
      onPress();
    }
  };

  const handleLongPress = () => {
    if (disabled) return;

    if (hapticFeedback) {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Heavy);
    }

    if (onLongPress) {
      onLongPress();
    }
  };

  const accessibilityProps = {
    accessible,
    accessibilityLabel,
    accessibilityHint,
    accessibilityRole,
    accessibilityState: {
      ...accessibilityState,
      disabled,
    },
    accessibilityValue,
    accessibilityActions,
    onAccessibilityAction,
    testID,
    focusable: focusable && !disabled,
    importantForAccessibility,
  };

  // If there's an onPress handler, use TouchableOpacity
  if (onPress || onLongPress) {
    return (
      <TouchableOpacity
        style={style}
        onPress={handlePress}
        onLongPress={handleLongPress}
        disabled={disabled}
        activeOpacity={0.7}
        {...accessibilityProps}
      >
        {children}
      </TouchableOpacity>
    );
  }

  // Otherwise, use a regular View
  return (
    <View
      style={style}
      {...accessibilityProps}
    >
      {children}
    </View>
  );
}

// Preset accessibility components for common use cases
export const AccessibleButton = (props: AccessibilityWrapperProps) => (
  <AccessibilityWrapper
    {...props}
    accessibilityRole="button"
    hapticType="medium"
  />
);

export const AccessibleLink = (props: AccessibilityWrapperProps) => (
  <AccessibilityWrapper
    {...props}
    accessibilityRole="link"
    hapticType="light"
  />
);

export const AccessibleImage = (props: AccessibilityWrapperProps) => (
  <AccessibilityWrapper
    {...props}
    accessibilityRole="image"
    hapticFeedback={false}
  />
);

export const AccessibleText = (props: AccessibilityWrapperProps) => (
  <AccessibilityWrapper
    {...props}
    accessibilityRole="text"
    hapticFeedback={false}
  />
);

export const AccessibleHeader = (props: AccessibilityWrapperProps) => (
  <AccessibilityWrapper
    {...props}
    accessibilityRole="header"
    hapticFeedback={false}
    importantForAccessibility="yes"
  />
);

export const AccessibleList = (props: AccessibilityWrapperProps) => (
  <AccessibilityWrapper
    {...props}
    accessibilityRole="list"
    hapticFeedback={false}
  />
);

export const AccessibleListItem = (props: AccessibilityWrapperProps) => (
  <AccessibilityWrapper
    {...props}
    accessibilityRole="button"
    hapticType="light"
  />
);

export const AccessibleSwitch = (props: AccessibilityWrapperProps & { value?: boolean }) => (
  <AccessibilityWrapper
    {...props}
    accessibilityRole="switch"
    accessibilityState={{
      ...props.accessibilityState,
      checked: props.value,
    }}
    hapticType="selection"
  />
);

export const AccessibleCheckbox = (props: AccessibilityWrapperProps & { checked?: boolean }) => (
  <AccessibilityWrapper
    {...props}
    accessibilityRole="checkbox"
    accessibilityState={{
      ...props.accessibilityState,
      checked: props.checked,
    }}
    hapticType="selection"
  />
);

export const AccessibleRadio = (props: AccessibilityWrapperProps & { selected?: boolean }) => (
  <AccessibilityWrapper
    {...props}
    accessibilityRole="radio"
    accessibilityState={{
      ...props.accessibilityState,
      selected: props.selected,
    }}
    hapticType="selection"
  />
);

// Utility functions for accessibility
export const announceForAccessibility = (message: string) => {
  AccessibilityInfo.announceForAccessibility(message);
};

export const setAccessibilityFocus = (reactTag: number) => {
  AccessibilityInfo.setAccessibilityFocus(reactTag);
};

export const isScreenReaderEnabled = async (): Promise<boolean> => {
  return await AccessibilityInfo.isScreenReaderEnabled();
};

export const isReduceMotionEnabled = async (): Promise<boolean> => {
  return await AccessibilityInfo.isReduceMotionEnabled();
};

export const isReduceTransparencyEnabled = async (): Promise<boolean> => {
  return await AccessibilityInfo.isReduceTransparencyEnabled();
};

// Accessibility guidelines helper
export const AccessibilityGuidelines = {
  // Minimum touch target size (44x44 points on iOS, 48x48 dp on Android)
  MIN_TOUCH_TARGET_SIZE: 44,
  
  // Color contrast ratios
  CONTRAST_RATIOS: {
    NORMAL_TEXT: 4.5, // AA standard
    LARGE_TEXT: 3, // AA standard for text 18pt+ or 14pt+ bold
    ENHANCED_NORMAL: 7, // AAA standard
    ENHANCED_LARGE: 4.5, // AAA standard for large text
  },
  
  // Font size recommendations
  FONT_SIZES: {
    MIN_READABLE: 12,
    RECOMMENDED_MIN: 14,
    BODY_TEXT: 16,
    LARGE_TEXT: 18,
  },
  
  // Animation duration recommendations
  ANIMATION_DURATIONS: {
    QUICK: 200,
    NORMAL: 300,
    SLOW: 500,
    REDUCED_MOTION: 0, // For users with reduce motion enabled
  },
};

// Helper to check if content meets accessibility guidelines
export const checkAccessibilityCompliance = (props: {
  touchTargetSize?: { width: number; height: number };
  fontSize?: number;
  animationDuration?: number;
}) => {
  const issues: string[] = [];
  
  if (props.touchTargetSize) {
    const { width, height } = props.touchTargetSize;
    if (width < AccessibilityGuidelines.MIN_TOUCH_TARGET_SIZE || 
        height < AccessibilityGuidelines.MIN_TOUCH_TARGET_SIZE) {
      issues.push(`Touch target size (${width}x${height}) is below recommended minimum (${AccessibilityGuidelines.MIN_TOUCH_TARGET_SIZE}x${AccessibilityGuidelines.MIN_TOUCH_TARGET_SIZE})`);
    }
  }
  
  if (props.fontSize && props.fontSize < AccessibilityGuidelines.FONT_SIZES.MIN_READABLE) {
    issues.push(`Font size (${props.fontSize}) is below minimum readable size (${AccessibilityGuidelines.FONT_SIZES.MIN_READABLE})`);
  }
  
  return {
    isCompliant: issues.length === 0,
    issues,
  };
};
