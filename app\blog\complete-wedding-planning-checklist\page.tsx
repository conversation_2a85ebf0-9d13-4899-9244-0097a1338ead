import React from 'react';
import { Metadata } from 'next';
import Link from 'next/link';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Calendar, Clock, User, Eye, CheckCircle, ArrowRight } from 'lucide-react';

export const metadata: Metadata = {
  title: 'Complete Wedding Planning Checklist: 12 Months to Your Big Day | BookmyFestive',
  description: 'Master wedding planning with our comprehensive 12-month checklist. From engagement to honeymoon, get organized with timelines, tasks, and expert tips for your perfect Indian wedding.',
  keywords: 'wedding planning checklist, wedding timeline, wedding planning guide, Indian wedding planning, wedding organization, wedding tasks',
  openGraph: {
    title: 'Complete Wedding Planning Checklist: 12 Months to Your Big Day',
    description: 'Master wedding planning with our comprehensive 12-month checklist. From engagement to honeymoon, get organized with timelines, tasks, and expert tips.',
    type: 'article',
    url: 'https://bookmyfestive.com/blog/complete-wedding-planning-checklist',
  },
};

const checklistData = [
  {
    month: '12 Months Before',
    title: 'Foundation & Planning',
    tasks: [
      'Announce engagement and set wedding date',
      'Create wedding budget and financial plan',
      'Choose wedding style and theme',
      'Research and book wedding venue',
      'Hire wedding planner (optional)',
      'Create guest list and estimate count',
      'Research vendors in your area',
      'Book major vendors (photographer, videographer)',
      'Apply for marriage license if required',
      'Start wedding dress shopping'
    ]
  },
  {
    month: '11 Months Before',
    title: 'Vendor Selection',
    tasks: [
      'Book caterer and finalize menu',
      'Book decorator and discuss themes',
      'Book makeup artist and hairstylist',
      'Book DJ or live band',
      'Book transportation services',
      'Research honeymoon destinations',
      'Start wedding website creation',
      'Plan engagement party (if desired)',
      'Book hotel blocks for out-of-town guests',
      'Research wedding insurance options'
    ]
  },
  {
    month: '10 Months Before',
    title: 'Attire & Accessories',
    tasks: [
      'Order wedding dress and schedule fittings',
      'Shop for groom\'s attire',
      'Select bridesmaid dresses',
      'Choose groomsmen attire',
      'Shop for wedding rings',
      'Select wedding shoes and accessories',
      'Book jewelry and accessories',
      'Plan wedding day hairstyle',
      'Schedule makeup trials',
      'Plan wedding day timeline'
    ]
  },
  {
    month: '9 Months Before',
    title: 'Ceremony & Reception',
    tasks: [
      'Book officiant or priest',
      'Plan ceremony details and rituals',
      'Choose wedding music and songs',
      'Plan reception program and activities',
      'Book wedding cake baker',
      'Plan wedding favors and gifts',
      'Create wedding day timeline',
      'Plan rehearsal dinner',
      'Book wedding day transportation',
      'Plan wedding day photography schedule'
    ]
  },
  {
    month: '8 Months Before',
    title: 'Details & Logistics',
    tasks: [
      'Send save-the-date cards',
      'Plan wedding day timeline',
      'Book wedding day accommodation',
      'Plan wedding day meals',
      'Create wedding day emergency kit',
      'Plan wedding day backup plans',
      'Book wedding day security (if needed)',
      'Plan wedding day parking',
      'Create wedding day contact list',
      'Plan wedding day weather backup'
    ]
  },
  {
    month: '7 Months Before',
    title: 'Beauty & Wellness',
    tasks: [
      'Start skincare routine',
      'Plan fitness and diet regimen',
      'Schedule hair and makeup trials',
      'Book spa treatments',
      'Plan wedding day beauty schedule',
      'Choose wedding day fragrance',
      'Plan wedding day touch-up kit',
      'Schedule dental cleaning',
      'Plan wedding day relaxation',
      'Book pre-wedding spa day'
    ]
  },
  {
    month: '6 Months Before',
    title: 'Legal & Documentation',
    tasks: [
      'Apply for marriage license',
      'Gather required documents',
      'Plan wedding ceremony script',
      'Choose wedding vows',
      'Plan wedding day legal requirements',
      'Book wedding day notary (if needed)',
      'Plan wedding day documentation',
      'Create wedding day checklist',
      'Plan wedding day timeline',
      'Book wedding day coordinator'
    ]
  },
  {
    month: '5 Months Before',
    title: 'Guest Experience',
    tasks: [
      'Design and order wedding invitations',
      'Plan guest accommodation',
      'Create wedding website',
      'Plan guest activities and entertainment',
      'Book guest transportation',
      'Plan guest welcome bags',
      'Create guest information cards',
      'Plan guest seating arrangements',
      'Book guest entertainment',
      'Plan guest photography'
    ]
  },
  {
    month: '4 Months Before',
    title: 'Final Preparations',
    tasks: [
      'Send wedding invitations',
      'Finalize vendor contracts',
      'Schedule final vendor meetings',
      'Plan wedding day timeline',
      'Create wedding day checklist',
      'Plan wedding day backup plans',
      'Book wedding day accommodation',
      'Plan wedding day meals',
      'Create wedding day emergency kit',
      'Plan wedding day weather backup'
    ]
  },
  {
    month: '3 Months Before',
    title: 'Last Minute Details',
    tasks: [
      'Schedule final dress fittings',
      'Finalize wedding day timeline',
      'Plan wedding day schedule',
      'Create wedding day checklist',
      'Plan wedding day backup plans',
      'Book wedding day accommodation',
      'Plan wedding day meals',
      'Create wedding day emergency kit',
      'Plan wedding day weather backup',
      'Schedule final vendor meetings'
    ]
  },
  {
    month: '2 Months Before',
    title: 'Final Countdown',
    tasks: [
      'Send wedding reminders',
      'Finalize guest count',
      'Plan wedding day timeline',
      'Create wedding day checklist',
      'Plan wedding day backup plans',
      'Book wedding day accommodation',
      'Plan wedding day meals',
      'Create wedding day emergency kit',
      'Plan wedding day weather backup',
      'Schedule final vendor meetings'
    ]
  },
  {
    month: '1 Month Before',
    title: 'Final Week',
    tasks: [
      'Final dress fitting',
      'Wedding rehearsal',
      'Final vendor meetings',
      'Pack for honeymoon',
      'Create wedding day timeline',
      'Finalize wedding day checklist',
      'Plan wedding day backup plans',
      'Prepare wedding day emergency kit',
      'Finalize wedding day schedule',
      'Relax and enjoy the moment!'
    ]
  }
];

const relatedPosts = [
  {
    title: 'How to Choose the Perfect Wedding Photographer: 10 Essential Tips',
    excerpt: 'Your wedding photos are forever. Learn how to select the right photographer who will capture your special moments perfectly.',
    slug: 'choose-perfect-wedding-photographer',
    category: 'Vendor Selection'
  },
  {
    title: 'Wedding Budget Planning: Complete Guide to Managing Your Expenses',
    excerpt: 'Plan your dream wedding without breaking the bank. Our comprehensive budget guide helps you allocate funds wisely.',
    slug: 'wedding-budget-planning-guide',
    category: 'Budget Management'
  },
  {
    title: 'Monsoon Wedding Ideas: Making Rain Your Wedding\'s Best Friend',
    excerpt: 'Don\'t let the monsoon dampen your spirits! Discover creative ideas for a beautiful rainy season wedding celebration.',
    slug: 'monsoon-wedding-ideas',
    category: 'Seasonal Tips'
  }
];

export default function WeddingPlanningChecklistPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <div className="bg-gradient-to-r from-red-800 to-red-600 text-white py-16">
        <div className="container mx-auto px-4 text-center">
          <Badge variant="secondary" className="mb-4">
            Planning Guide
          </Badge>
          <h1 className="text-4xl md:text-5xl font-bold mb-6">
            Complete Wedding Planning Checklist
          </h1>
          <p className="text-xl md:text-2xl mb-8 max-w-4xl mx-auto">
            12 Months to Your Big Day - Master the art of wedding planning with our comprehensive timeline, tasks, and expert tips
          </p>
          <div className="flex flex-wrap justify-center items-center gap-6 text-sm">
            <div className="flex items-center gap-2">
              <User className="h-5 w-5" />
              <span>BookmyFestive Team</span>
            </div>
            <div className="flex items-center gap-2">
              <Calendar className="h-5 w-5" />
              <span>August 15, 2024</span>
            </div>
            <div className="flex items-center gap-2">
              <Clock className="h-5 w-5" />
              <span>8 min read</span>
            </div>
            <div className="flex items-center gap-2">
              <Eye className="h-5 w-5" />
              <span>2.5k views</span>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-4 py-12">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Article Content */}
          <div className="lg:col-span-3">
            <div className="bg-white rounded-lg shadow-lg p-8 mb-8">
              <div className="prose prose-lg max-w-none">
                <h2 className="text-2xl font-bold text-gray-900 mb-6">
                  Your Complete 12-Month Wedding Planning Timeline
                </h2>
                
                <p className="text-gray-600 mb-6">
                  Planning a wedding can feel overwhelming, but with the right timeline and checklist, you can organize everything perfectly. This comprehensive guide breaks down your wedding planning into manageable monthly tasks, ensuring nothing is missed and your special day goes smoothly.
                </p>

                <div className="bg-blue-50 border-l-4 border-blue-400 p-4 mb-8">
                  <p className="text-blue-800 font-medium">
                    💡 <strong>Pro Tip:</strong> Start planning as early as possible, especially for popular venues and vendors. Many book up 12-18 months in advance!
                  </p>
                </div>

                {/* Monthly Checklists */}
                {checklistData.map((month, index) => (
                  <div key={index} className="mb-8">
                    <div className="bg-red-50 border-l-4 border-red-400 p-4 mb-4">
                      <h3 className="text-xl font-bold text-red-800 mb-2">
                        {month.month}
                      </h3>
                      <p className="text-red-700 font-medium">
                        {month.title}
                      </p>
                    </div>
                    
                    <div className="space-y-3">
                      {month.tasks.map((task, taskIndex) => (
                        <div key={taskIndex} className="flex items-start gap-3">
                          <CheckCircle className="h-5 w-5 text-green-500 mt-0.5 flex-shrink-0" />
                          <span className="text-gray-700">{task}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                ))}

                <h3 className="text-xl font-bold text-gray-900 mb-4 mt-8">
                  Essential Wedding Planning Tips
                </h3>
                
                <ul className="space-y-3 mb-6">
                  <li className="flex items-start gap-3">
                    <CheckCircle className="h-5 w-5 text-green-500 mt-0.5 flex-shrink-0" />
                    <span><strong>Start Early:</strong> Begin planning 12-18 months before your wedding date</span>
                  </li>
                  <li className="flex items-start gap-3">
                    <CheckCircle className="h-5 w-5 text-green-500 mt-0.5 flex-shrink-0" />
                    <span><strong>Set a Budget:</strong> Determine your total budget and stick to it</span>
                  </li>
                  <li className="flex items-start gap-3">
                    <CheckCircle className="h-5 w-5 text-green-500 mt-0.5 flex-shrink-0" />
                    <span><strong>Prioritize:</strong> Focus on what matters most to you and your partner</span>
                  </li>
                  <li className="flex items-start gap-3">
                    <CheckCircle className="h-5 w-5 text-green-500 mt-0.5 flex-shrink-0" />
                    <span><strong>Delegate:</strong> Don't try to do everything yourself</span>
                  </li>
                  <li className="flex items-start gap-3">
                    <CheckCircle className="h-5 w-5 text-green-500 mt-0.5 flex-shrink-0" />
                    <span><strong>Stay Organized:</strong> Use wedding planning apps or spreadsheets</span>
                  </li>
                </ul>

                <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-6">
                  <p className="text-yellow-800">
                    🎯 <strong>Remember:</strong> Every wedding is unique, so feel free to adjust this timeline based on your specific needs and circumstances.
                  </p>
                </div>

                <h3 className="text-xl font-bold text-gray-900 mb-4">
                  Need Help with Wedding Planning?
                </h3>
                
                <p className="text-gray-600 mb-6">
                  BookmyFestive connects you with verified wedding vendors across India. From photographers to venues, we help you find the perfect vendors for your special day.
                </p>

                <div className="flex flex-col sm:flex-row gap-4">
                  <Link href="/vendors">
                    <Button className="bg-red-600 hover:bg-red-700">
                      Find Wedding Vendors
                      <ArrowRight className="h-4 w-4 ml-2" />
                    </Button>
                  </Link>
                  <Link href="/contact">
                    <Button variant="outline">
                      Get Planning Help
                    </Button>
                  </Link>
                </div>
              </div>
            </div>
          </div>

          {/* Sidebar */}
          <div className="lg:col-span-1">
            {/* Table of Contents */}
            <Card className="mb-6">
              <CardHeader>
                <CardTitle className="text-lg">Table of Contents</CardTitle>
              </CardHeader>
              <CardContent>
                <nav className="space-y-2">
                  {checklistData.map((month, index) => (
                    <Link
                      key={index}
                      href={`#month-${index}`}
                      className="block text-sm text-gray-600 hover:text-red-600 transition-colors"
                    >
                      {month.month}
                    </Link>
                  ))}
                </nav>
              </CardContent>
            </Card>

            {/* Related Posts */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Related Articles</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {relatedPosts.map((post, index) => (
                    <div key={index} className="border-b border-gray-200 pb-4 last:border-b-0">
                      <Badge variant="outline" className="text-xs mb-2">
                        {post.category}
                      </Badge>
                      <h4 className="font-medium text-gray-900 mb-2">
                        <Link href={`/blog/${post.slug}`} className="hover:text-red-600 transition-colors">
                          {post.title}
                        </Link>
                      </h4>
                      <p className="text-sm text-gray-600">
                        {post.excerpt}
                      </p>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
} 