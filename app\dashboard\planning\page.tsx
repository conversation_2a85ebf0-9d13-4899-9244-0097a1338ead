'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Ta<PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { 
  Calculator, 
  Users, 
  Calendar, 
  CheckSquare, 
  Lightbulb,
  TrendingUp,
  AlertCircle,
  Plus,
  Eye,
  Edit,
  Trash2,
  Download,
  Upload,
  RefreshCw
} from 'lucide-react'
import { useAuth } from '@/contexts/AuthContext'
import { toast } from 'sonner'
import planningToolsService, { WeddingPlan, Budget, GuestList, Timeline, WeddingIdea, PlanningProgress } from '@/lib/services/planningToolsService'
import { PlanningDataMigration } from '@/lib/utils/planningDataMigration'
import Link from 'next/link'

export default function PlanningDashboard() {
  const { user } = useAuth()
  const [loading, setLoading] = useState(true)
  const [weddingPlans, setWeddingPlans] = useState<WeddingPlan[]>([])
  const [budgets, setBudgets] = useState<Budget[]>([])
  const [guestLists, setGuestLists] = useState<GuestList[]>([])
  const [timelines, setTimelines] = useState<Timeline[]>([])
  const [ideas, setIdeas] = useState<WeddingIdea[]>([])
  const [progress, setProgress] = useState<PlanningProgress[]>([])
  const [showMigrationPrompt, setShowMigrationPrompt] = useState(false)

  // Load all planning data
  const loadPlanningData = async () => {
    if (!user?.userId) return

    try {
      setLoading(true)

      const [
        weddingPlansData,
        budgetsData,
        guestListsData,
        timelinesData,
        ideasData,
        progressData
      ] = await Promise.all([
        planningToolsService.getUserWeddingPlans(user.userId),
        planningToolsService.getUserBudgets(user.userId),
        planningToolsService.getUserGuestLists(user.userId),
        planningToolsService.getUserTimelines(user.userId),
        planningToolsService.getUserWeddingIdeas(user.userId),
        planningToolsService.getUserPlanningProgress(user.userId)
      ])

      setWeddingPlans(weddingPlansData)
      setBudgets(budgetsData)
      setGuestLists(guestListsData)
      setTimelines(timelinesData)
      setIdeas(ideasData)
      setProgress(progressData)

    } catch (error) {
      console.error('Error loading planning data:', error)
      toast.error('Failed to load planning data')
    } finally {
      setLoading(false)
    }
  }

  // Check for migration on component mount
  useEffect(() => {
    if (user?.userId) {
      loadPlanningData()

      // Check if user has data to migrate
      const migrationData = PlanningDataMigration.hasDataToMigrate()
      if (migrationData.hasAnyData) {
        setShowMigrationPrompt(true)
      }
    }
  }, [user?.userId])

  // Handle data migration
  const handleMigration = async () => {
    if (!user?.userId) return

    setShowMigrationPrompt(false)
    await PlanningDataMigration.performMigrationWithFeedback(user.userId)
    
    // Reload data after migration
    await loadPlanningData()
  }

  // Calculate overall statistics
  const stats = {
    totalBudgets: budgets.length,
    totalBudgetAmount: budgets.reduce((sum, budget) => sum + (budget.totalBudget || 0), 0),
    totalGuests: guestLists.reduce((sum, list) => sum + (list.totalGuests || 0), 0),
    confirmedGuests: guestLists.reduce((sum, list) => sum + (list.confirmedGuests || 0), 0),
    totalTasks: timelines.reduce((sum, timeline) => sum + (timeline.tasks?.length || 0), 0),
    completedTasks: timelines.reduce((sum, timeline) => 
      sum + (timeline.tasks?.filter(task => task.isCompleted).length || 0), 0),
    totalIdeas: ideas.length,
    favoriteIdeas: ideas.filter(idea => idea.isFavorite).length
  }

  const overallProgress = stats.totalTasks > 0 ? (stats.completedTasks / stats.totalTasks) * 100 : 0

  if (!user) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Authentication Required</h2>
          <p className="text-gray-600">Please log in to access your planning tools.</p>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8 space-y-8">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Planning Dashboard</h1>
          <p className="text-gray-600 mt-1">Manage all your wedding planning tools in one place</p>
        </div>
        <div className="mt-4 sm:mt-0 flex items-center gap-3">
          <Button
            onClick={loadPlanningData}
            disabled={loading}
            variant="outline"
            size="sm"
            className="flex items-center gap-2"
          >
            <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>
      </div>

      {/* Migration Prompt */}
      {showMigrationPrompt && (
        <Card className="border-blue-200 bg-blue-50">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-blue-800">
              <Upload className="h-5 w-5" />
              Data Migration Available
            </CardTitle>
            <CardDescription className="text-blue-700">
              We found existing planning data in your browser. Would you like to save it to your account for better access across devices?
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex gap-3">
              <Button onClick={handleMigration} size="sm">
                Migrate Data
              </Button>
              <Button 
                onClick={() => setShowMigrationPrompt(false)} 
                variant="outline" 
                size="sm"
              >
                Skip
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Overview Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Budget</CardTitle>
            <Calculator className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">₹{stats.totalBudgetAmount.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              {stats.totalBudgets} budget{stats.totalBudgets !== 1 ? 's' : ''}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Guest List</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalGuests}</div>
            <p className="text-xs text-muted-foreground">
              {stats.confirmedGuests} confirmed
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Tasks Progress</CardTitle>
            <CheckSquare className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{Math.round(overallProgress)}%</div>
            <Progress value={overallProgress} className="mt-2" />
            <p className="text-xs text-muted-foreground mt-1">
              {stats.completedTasks} of {stats.totalTasks} tasks
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Ideas Saved</CardTitle>
            <Lightbulb className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalIdeas}</div>
            <p className="text-xs text-muted-foreground">
              {stats.favoriteIdeas} favorites
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Planning Tools Tabs */}
      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList className="grid w-full grid-cols-6">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="budgets">Budgets</TabsTrigger>
          <TabsTrigger value="guests">Guests</TabsTrigger>
          <TabsTrigger value="timeline">Timeline</TabsTrigger>
          <TabsTrigger value="checklist">Checklist</TabsTrigger>
          <TabsTrigger value="ideas">Ideas</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Quick Actions */}
            <Card>
              <CardHeader>
                <CardTitle>Quick Actions</CardTitle>
                <CardDescription>Jump to your planning tools</CardDescription>
              </CardHeader>
              <CardContent className="space-y-3">
                <Link href="/planning/budget">
                  <Button variant="outline" className="w-full justify-start">
                    <Calculator className="h-4 w-4 mr-2" />
                    Budget Calculator
                  </Button>
                </Link>
                <Link href="/planning/guest-list">
                  <Button variant="outline" className="w-full justify-start">
                    <Users className="h-4 w-4 mr-2" />
                    Guest List Manager
                  </Button>
                </Link>
                <Link href="/planning/timeline">
                  <Button variant="outline" className="w-full justify-start">
                    <Calendar className="h-4 w-4 mr-2" />
                    Wedding Timeline
                  </Button>
                </Link>
                <Link href="/planning/checklist">
                  <Button variant="outline" className="w-full justify-start">
                    <CheckSquare className="h-4 w-4 mr-2" />
                    Planning Checklist
                  </Button>
                </Link>
              </CardContent>
            </Card>

            {/* Recent Activity */}
            <Card>
              <CardHeader>
                <CardTitle>Recent Activity</CardTitle>
                <CardDescription>Your latest planning updates</CardDescription>
              </CardHeader>
              <CardContent>
                {loading ? (
                  <div className="text-center py-4">
                    <RefreshCw className="h-6 w-6 animate-spin mx-auto mb-2" />
                    <p className="text-sm text-gray-600">Loading activity...</p>
                  </div>
                ) : (
                  <div className="space-y-3">
                    {budgets.slice(0, 3).map(budget => (
                      <div key={budget.id} className="flex items-center gap-3">
                        <Calculator className="h-4 w-4 text-blue-600" />
                        <div className="flex-1">
                          <p className="text-sm font-medium">{budget.name}</p>
                          <p className="text-xs text-gray-600">
                            Budget: ₹{budget.totalBudget.toLocaleString()}
                          </p>
                        </div>
                        <Badge variant="outline">Budget</Badge>
                      </div>
                    ))}
                    {guestLists.slice(0, 2).map(list => (
                      <div key={list.id} className="flex items-center gap-3">
                        <Users className="h-4 w-4 text-green-600" />
                        <div className="flex-1">
                          <p className="text-sm font-medium">{list.name}</p>
                          <p className="text-xs text-gray-600">
                            {list.totalGuests} guests
                          </p>
                        </div>
                        <Badge variant="outline">Guests</Badge>
                      </div>
                    ))}
                    {(budgets.length === 0 && guestLists.length === 0) && (
                      <p className="text-sm text-gray-600 text-center py-4">
                        No recent activity. Start planning your wedding!
                      </p>
                    )}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="budgets">
          <Card>
            <CardHeader>
              <CardTitle>Budget Management</CardTitle>
              <CardDescription>Track your wedding expenses and budget allocation</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <Calculator className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium mb-2">Budget Tools</h3>
                <p className="text-gray-600 mb-4">
                  Create and manage your wedding budgets with detailed category tracking.
                </p>
                <Link href="/planning/budget">
                  <Button>
                    <Plus className="h-4 w-4 mr-2" />
                    Create Budget
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="guests">
          <Card>
            <CardHeader>
              <CardTitle>Guest Management</CardTitle>
              <CardDescription>Organize your wedding guest list and track RSVPs</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium mb-2">Guest List Tools</h3>
                <p className="text-gray-600 mb-4">
                  Manage your wedding guests, track RSVPs, and organize seating arrangements.
                </p>
                <Link href="/planning/guest-list">
                  <Button>
                    <Plus className="h-4 w-4 mr-2" />
                    Manage Guests
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="timeline">
          <Card>
            <CardHeader>
              <CardTitle>Wedding Timeline</CardTitle>
              <CardDescription>Plan your wedding timeline and track important milestones</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <Calendar className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium mb-2">Timeline Planning</h3>
                <p className="text-gray-600 mb-4">
                  Create detailed timelines for your wedding planning and day-of coordination.
                </p>
                <Link href="/planning/timeline">
                  <Button>
                    <Plus className="h-4 w-4 mr-2" />
                    View Timeline
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="checklist">
          <Card>
            <CardHeader>
              <CardTitle>Planning Checklist</CardTitle>
              <CardDescription>Stay organized with comprehensive wedding planning checklists</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <CheckSquare className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium mb-2">Planning Checklist</h3>
                <p className="text-gray-600 mb-4">
                  Follow our comprehensive checklist to ensure nothing is forgotten.
                </p>
                <Link href="/planning/checklist">
                  <Button>
                    <Plus className="h-4 w-4 mr-2" />
                    View Checklist
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="ideas">
          <Card>
            <CardHeader>
              <CardTitle>Wedding Ideas</CardTitle>
              <CardDescription>Save and organize your wedding inspiration and ideas</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <Lightbulb className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium mb-2">Wedding Ideas</h3>
                <p className="text-gray-600 mb-4">
                  Collect and organize inspiration for your perfect wedding day.
                </p>
                <Link href="/planning/ideas">
                  <Button>
                    <Plus className="h-4 w-4 mr-2" />
                    Browse Ideas
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
