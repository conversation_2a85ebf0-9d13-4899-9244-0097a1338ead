#!/usr/bin/env node

/**
 * GraphQL Schema Validation Script
 * Validates the consolidated schema before deployment
 */

const fs = require('fs');
const path = require('path');

function validateSchema() {
  console.log('🔍 Validating GraphQL Schema...\n');

  const schemaPath = path.join(__dirname, '../amplify/backend/api/BookmyFestive/schema.graphql');
  
  if (!fs.existsSync(schemaPath)) {
    console.error('❌ Schema file not found:', schemaPath);
    process.exit(1);
  }

  const schemaContent = fs.readFileSync(schemaPath, 'utf8');
  
  // Check for duplicate type definitions
  const duplicateChecks = [
    { name: 'SubscriptionStatus', pattern: /enum SubscriptionStatus/g },
    { name: 'EmailFrequency', pattern: /enum EmailFrequency/g },
    { name: 'PaymentStatus', pattern: /enum PaymentStatus/g },
    { name: 'Address', pattern: /type Address/g }
  ];

  let hasErrors = false;

  duplicateChecks.forEach(check => {
    const matches = schemaContent.match(check.pattern);
    if (matches && matches.length > 1) {
      console.error(`❌ Duplicate type found: ${check.name} (${matches.length} occurrences)`);
      hasErrors = true;
    } else {
      console.log(`✅ ${check.name}: No duplicates`);
    }
  });

  // Check for required sections
  const requiredSections = [
    { name: 'Email Management', pattern: /# Email Management Schema/ },
    { name: 'Invoice Management', pattern: /# Invoice Management Schema/ },
    { name: 'Pricing Management', pattern: /# Pricing and Subscription Management Schema/ },
    { name: 'Custom Mutations', pattern: /# Custom Mutations/ },
    { name: 'Custom Queries', pattern: /# Custom Queries/ }
  ];

  requiredSections.forEach(section => {
    if (schemaContent.match(section.pattern)) {
      console.log(`✅ ${section.name}: Found`);
    } else {
      console.error(`❌ ${section.name}: Missing`);
      hasErrors = true;
    }
  });

  // Check for email types
  const emailTypes = [
    'LOGIN_OTP',
    'WELCOME_SIGNUP', 
    'NEWSLETTER_SUBSCRIPTION',
    'BOOKING_CONFIRMATION',
    'PAYMENT_SUCCESS',
    'WEEKLY_NEWS',
    'OFFERS_MAIL',
    'FAVORITES_NOTIFICATION',
    'VENDOR_LAUNCH',
    'AVAILABILITY_CHECK'
  ];

  console.log('\n📧 Checking Email Types:');
  emailTypes.forEach(type => {
    if (schemaContent.includes(type)) {
      console.log(`✅ ${type}: Found`);
    } else {
      console.error(`❌ ${type}: Missing`);
      hasErrors = true;
    }
  });

  // Check for custom mutations
  const customMutations = [
    'sendEmail',
    'sendBulkEmail',
    'generateInvoice',
    'createSubscription',
    'cancelSubscription'
  ];

  console.log('\n🔧 Checking Custom Mutations:');
  customMutations.forEach(mutation => {
    if (schemaContent.includes(`${mutation}(`)) {
      console.log(`✅ ${mutation}: Found`);
    } else {
      console.error(`❌ ${mutation}: Missing`);
      hasErrors = true;
    }
  });

  // Check for Lambda function references
  const lambdaFunctions = [
    'sendEmail-${env}',
    'generateInvoice-${env}',
    'createSubscription-${env}'
  ];

  console.log('\n⚡ Checking Lambda Function References:');
  lambdaFunctions.forEach(func => {
    if (schemaContent.includes(func)) {
      console.log(`✅ ${func}: Found`);
    } else {
      console.error(`❌ ${func}: Missing`);
      hasErrors = true;
    }
  });

  // Summary
  console.log('\n📊 Schema Validation Summary');
  console.log('============================');
  
  if (hasErrors) {
    console.error('❌ Schema validation failed! Please fix the errors above.');
    process.exit(1);
  } else {
    console.log('✅ Schema validation passed!');
    console.log('🚀 Schema is ready for deployment with amplify push');
    
    // Additional info
    const lines = schemaContent.split('\n').length;
    const types = (schemaContent.match(/type \w+/g) || []).length;
    const enums = (schemaContent.match(/enum \w+/g) || []).length;
    const mutations = (schemaContent.match(/\w+\([^)]*\):[^@]*@function/g) || []).length;
    
    console.log(`\n📈 Schema Statistics:`);
    console.log(`   Lines: ${lines}`);
    console.log(`   Types: ${types}`);
    console.log(`   Enums: ${enums}`);
    console.log(`   Custom Mutations: ${mutations}`);
  }
}

// Run validation
validateSchema();
