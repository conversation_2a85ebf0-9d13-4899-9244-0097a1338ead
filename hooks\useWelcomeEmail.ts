import { useState } from 'react';
import { emailService, WelcomeEmailData } from '@/lib/services/emailService';

interface UseWelcomeEmailReturn {
  loading: boolean;
  error: string | null;
  success: boolean;
  sendNewsletterWelcome: (data: WelcomeEmailData) => Promise<void>;
  sendUserWelcome: (data: WelcomeEmailData) => Promise<void>;
  clearMessages: () => void;
}

export const useWelcomeEmail = (): UseWelcomeEmailReturn => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  const clearMessages = () => {
    setError(null);
    setSuccess(false);
  };

  const sendNewsletterWelcome = async (data: WelcomeEmailData) => {
    setLoading(true);
    setError(null);
    setSuccess(false);

    try {
      const result = await emailService.sendNewsletterWelcomeEmail(data);
      if (result) {
        setSuccess(true);
      } else {
        throw new Error('Failed to send newsletter welcome email');
      }
    } catch (err: any) {
      setError(err.message || 'Failed to send newsletter welcome email');
    } finally {
      setLoading(false);
    }
  };

  const sendUserWelcome = async (data: WelcomeEmailData) => {
    setLoading(true);
    setError(null);
    setSuccess(false);

    try {
      const result = await emailService.sendUserWelcomeEmail(data);
      if (result) {
        setSuccess(true);
      } else {
        throw new Error('Failed to send user welcome email');
      }
    } catch (err: any) {
      setError(err.message || 'Failed to send user welcome email');
    } finally {
      setLoading(false);
    }
  };

  return {
    loading,
    error,
    success,
    sendNewsletterWelcome,
    sendUserWelcome,
    clearMessages
  };
};

export default useWelcomeEmail;
