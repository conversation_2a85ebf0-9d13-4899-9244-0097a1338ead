"use client"

import { useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'
import '@/lib/i18n-enhanced'

export function useSafeTranslation() {
  const { t, i18n, ready } = useTranslation()
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
  }, [])

  const safeT = (key: string, fallback?: string, options?: any) => {
    if (!mounted || !ready) {
      return fallback || key.split('.').pop() || key
    }
    
    const translation = t(key, options)
    
    // If translation is the same as key, it means translation is missing
    if (translation === key && fallback) {
      return fallback
    }
    
    return translation
  }

  return {
    t: safeT,
    i18n,
    ready: mounted && ready,
    mounted
  }
}
