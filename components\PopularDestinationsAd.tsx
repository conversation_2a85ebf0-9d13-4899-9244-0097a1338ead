"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { MapPin, Star, TrendingUp, Heart, Gift, Sparkles, ArrowRight } from "lucide-react"
import { useSafeTranslation } from '@/hooks/use-safe-translation'
import { useStateContext } from '@/contexts/StateContext'
import { useState, useEffect, useRef } from 'react'
import { useRouter } from 'next/navigation'
import { trackDestinationClick, trackDestinationImpression, trackCTAClick } from '@/lib/analytics/adTracking'
import Image from "next/image"

interface PopularDestinationsAdProps {
  className?: string
  showPromotionalBadges?: boolean
  showSpecialOffers?: boolean
  onDestinationClick?: (cityName: string, cityData: any) => void
  enableRedirects?: boolean
  redirectToVendors?: boolean
}

export function PopularDestinationsAd({
  className = "",
  showPromotionalBadges = true,
  showSpecialOffers = true,
  onDestinationClick,
  enableRedirects = true,
  redirectToVendors = true
}: PopularDestinationsAdProps) {
  const { t } = useSafeTranslation()
  const { selectedState } = useStateContext()
  const router = useRouter()
  const [hoveredCity, setHoveredCity] = useState<number | null>(null)
  const sectionRef = useRef<HTMLElement>(null)
  const [hasTrackedImpression, setHasTrackedImpression] = useState(false)

  // Helper function to get background gradient for cities
  const getCityBackground = (index: number) => {
    const gradients = [
      'linear-gradient(135deg, rgba(97, 15, 19, 0.85), rgba(220, 38, 38, 0.7))', // Deep Red
      'linear-gradient(135deg, rgba(34, 197, 94, 0.85), rgba(22, 163, 74, 0.7))', // Emerald Green
      'linear-gradient(135deg, rgba(246, 194, 68, 0.85), rgba(251, 146, 60, 0.7))', // Golden Orange
      'linear-gradient(135deg, rgba(236, 72, 153, 0.85), rgba(219, 39, 119, 0.7))', // Pink
      'linear-gradient(135deg, rgba(99, 102, 241, 0.85), rgba(79, 70, 229, 0.7))', // Indigo
      'linear-gradient(135deg, rgba(168, 85, 247, 0.85), rgba(147, 51, 234, 0.7))', // Purple
    ];
    return gradients[index % gradients.length];
  };

  // Helper function to get city icon
  const getCityIcon = (index: number) => {
    const icons = [
      <path key="icon1" d="M12 2L2 7v10c0 5.55 3.84 9.74 9 11 5.16-1.26 9-5.45 9-11V7l-10-5z"/>,
      <path key="icon2" d="M14 6l-3.75 5 2.85 3.8-1.6 1.2C9.81 13.75 7 10 7 10l-6 8h22L14 6z"/>,
      <path key="icon3" d="M12 1L3 5v6c0 5.55 3.84 9.74 9 11 5.16-1.26 9-5.45 9-11V5l-9-4z M12 7L8 10v7h8v-7l-4-3z"/>,
      <path key="icon4" d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>,
      <path key="icon5" d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"/>,
      <path key="icon6" d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z M15 10a3 3 0 1 1-6 0 3 3 0 0 1 6 0z"/>,
    ];
    return icons[index % icons.length];
  };

  // Get promotional offers for cities
  const getPromotionalOffer = (index: number) => {
    const offers = [
      { text: "20% OFF", color: "bg-red-500", description: "Limited Time" },
      { text: "FEATURED", color: "bg-green-500", description: "Top Choice" },
      { text: "HOT DEAL", color: "bg-orange-500", description: "Best Value" },
      { text: "TRENDING", color: "bg-pink-500", description: "Most Booked" },
      { text: "POPULAR", color: "bg-blue-500", description: "Customer Favorite" },
      { text: "PREMIUM", color: "bg-purple-500", description: "Luxury Option" },
    ];
    return offers[index % offers.length];
  };

  // Get special promotional messages
  const getPromotionalMessage = (index: number) => {
    const messages = [
      "🎉 Free venue consultation included!",
      "✨ Complimentary decoration package",
      "🎁 Special wedding photography deal",
      "💎 Premium vendor partnerships",
      "🌟 Exclusive venue discounts",
      "🎊 Complete wedding planning support"
    ];
    return messages[index % messages.length];
  };

  // Intersection Observer for impression tracking
  useEffect(() => {
    if (!sectionRef.current || hasTrackedImpression) return

    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting && entry.intersectionRatio > 0.5) {
            // Track impression when 50% of the section is visible
            const visibleCities = selectedState.cities.map(city =>
              t(city.nameKey, city.nameKey.split('.').pop())
            )

            trackDestinationImpression({
              visibleCities,
              state: selectedState.name,
              adPosition: 'home_page_main',
              scrollDepth: Math.round((window.scrollY / document.body.scrollHeight) * 100)
            })

            setHasTrackedImpression(true)
            observer.disconnect()
          }
        })
      },
      { threshold: 0.5 }
    )

    observer.observe(sectionRef.current)
    return () => observer.disconnect()
  }, [selectedState, hasTrackedImpression, t])

  // Handle city click with enhanced analytics and navigation
  const handleCityClick = (city: any, index: number) => {
    const cityName = t(city.nameKey, city.nameKey.split('.').pop());
    const offer = getPromotionalOffer(index);

    // Enhanced analytics tracking
    trackDestinationClick({
      cityName,
      cityIndex: index,
      state: selectedState.name,
      adPosition: 'home_page_main',
      hasPromotionalBadge: showPromotionalBadges,
      offerType: offer.text
    });

    // Handle navigation if enabled
    if (enableRedirects) {
      const targetPage = redirectToVendors ? '/vendors' : '/venues';
      const searchParams = new URLSearchParams();

      // Add city filter
      searchParams.set('city', cityName);
      searchParams.set('state', selectedState.name);

      // Add additional context for better filtering
      searchParams.set('source', 'destinations_ad');
      searchParams.set('offer', offer.text);

      const redirectUrl = `${targetPage}?${searchParams.toString()}`;

      console.log('Redirecting to:', redirectUrl);
      router.push(redirectUrl);
    }

    // Call custom click handler if provided
    if (onDestinationClick) {
      onDestinationClick(cityName, city);
    }
  };

  // Handle CTA button clicks with navigation
  const handleCTAClick = (ctaType: 'explore_all' | 'get_quote') => {
    trackCTAClick({
      ctaType,
      state: selectedState.name,
      adPosition: 'home_page_main'
    });

    // Handle navigation based on CTA type
    if (enableRedirects) {
      if (ctaType === 'explore_all') {
        const targetPage = redirectToVendors ? '/vendors' : '/venues';
        const searchParams = new URLSearchParams();

        // Add state filter for exploring all cities in the state
        searchParams.set('state', selectedState.name);
        searchParams.set('source', 'destinations_ad_explore_all');

        const redirectUrl = `${targetPage}?${searchParams.toString()}`;
        console.log('Exploring all cities, redirecting to:', redirectUrl);
        router.push(redirectUrl);

      } else if (ctaType === 'get_quote') {
        // Redirect to contact page with pre-filled context
        const searchParams = new URLSearchParams();
        searchParams.set('inquiry', 'wedding_planning');
        searchParams.set('state', selectedState.name);
        searchParams.set('source', 'destinations_ad_quote');

        const redirectUrl = `/contact?${searchParams.toString()}`;
        console.log('Getting quote, redirecting to:', redirectUrl);
        router.push(redirectUrl);
      }
    }
  };

  return (
    <section
      ref={sectionRef}
      className={`py-12 md:py-20 bg-gradient-to-br from-primary/8 via-white to-accent/8 relative overflow-hidden ${className}`}>
      {/* Decorative Background Elements */}
      <div className="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 opacity-5">
        <div className="w-full h-full border-4 border-primary rounded-full animate-pulse"></div>
        <div className="absolute top-8 left-8 right-8 bottom-8 border-2 border-accent rounded-full"></div>
        <div className="absolute top-16 left-16 right-16 bottom-16 border-2 border-primary rounded-full"></div>
      </div>

      {/* Floating promotional elements */}
      <div className="absolute top-10 left-10 w-20 h-20 bg-gradient-to-br from-accent/20 to-yellow-500/20 rounded-full blur-xl animate-bounce"></div>
      <div className="absolute bottom-10 right-10 w-16 h-16 bg-gradient-to-br from-primary/20 to-pink-500/20 rounded-full blur-xl animate-pulse"></div>

      <div className="container mx-auto px-4 relative">
        {/* Promotional Banner */}
        <div className="mb-8 text-center">
          <div className="inline-flex items-center bg-primary text-primary-foreground px-6 py-3 rounded-full shadow-lg">
            <Sparkles className="w-5 h-5 mr-2 animate-spin" />
            <span className="font-bold text-sm">
              {t('destinations.ad.limitedOffer', 'LIMITED TIME: Up to 30% OFF Wedding Packages!')}
            </span>
            <Sparkles className="w-5 h-5 ml-2 animate-spin" />
          </div>
        </div>

        {/* Header Section with Enhanced Styling */}
        <div className="text-center mb-12 md:mb-16">
          {showPromotionalBadges && (
            <div className="flex justify-center mb-6">
              <Badge className="bg-primary text-primary-foreground px-6 py-2 text-sm font-bold rounded-full shadow-lg">
                <Sparkles className="w-4 h-4 mr-2" />
                {t('destinations.ad.badge', 'FEATURED DESTINATIONS')}
              </Badge>
            </div>
          )}
          
          <div className="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-br from-accent to-yellow-500 rounded-full mb-6 shadow-2xl">
            <MapPin className="w-10 h-10 text-white" />
          </div>
          
          <h2 className="text-3xl md:text-4xl font-bold mb-6 text-primary bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent">
            {t('destinations.title', 'Popular Festive Destinations')}
          </h2>
          
          <p className="text-gray-700 max-w-3xl mx-auto leading-relaxed text-lg">
            {t('destinations.subtitle', `Discover the most sought-after wedding destinations across ${selectedState.name}`).replace('{stateName}', selectedState.name)}
          </p>

          {showSpecialOffers && (
            <div className="mt-6 flex justify-center">
              <div className="bg-gradient-to-r from-yellow-400 to-amber-500 text-white px-8 py-3 rounded-full shadow-lg">
                <Gift className="w-5 h-5 inline mr-2" />
                <span className="font-semibold text-yellow-50">{t('destinations.ad.offer', 'Special Wedding Packages Available!')}</span>
              </div>
            </div>
          )}
        </div>

        {/* Cities Grid with Modern, Simple Cards */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
          {selectedState.cities.map((city, index) => {
            return (
              <div
                key={index}
                className="group rounded-3xl shadow-lg bg-white/80 hover:shadow-2xl transition-all duration-300 overflow-hidden flex flex-col items-center justify-center min-h-[333px] relative" // 370px * 0.9 = 333px
                style={{ minHeight: 333, width: '90%', margin: '0 auto' }} // Reduce card width by 10%
              >
                <div className="relative w-full" style={{ aspectRatio: '1 / 1' }}>
                  {/* City Images */}
                  {index === 0 && (
                    <Image src="/chennai_image.jpg" alt="Chennai" fill style={{ objectFit: 'cover' }} priority />
                  )}
                  {index === 1 && (
                    <Image src="/coimbatore_image.jpg" alt="Coimbatore" fill style={{ objectFit: 'cover' }} priority />
                  )}
                  {index === 2 && (
                    <Image src="/madurai_image.jpg" alt="Madurai" fill style={{ objectFit: 'cover' }} priority />
                  )}
                  {index === 3 && (
                    <Image src="/tirunelveli_image.jpg" alt="Tirunelveli" fill style={{ objectFit: 'cover' }} priority />
                  )}
                  {/* Subtle overlay for text readability */}
                  <div className="absolute inset-0 bg-gradient-to-t from-black/40 to-transparent" />
                </div>
                <div className="flex-1 flex flex-col items-center justify-center px-6 py-6 text-center">
                  <h3 className="text-xl font-bold text-primary mb-1">
                    {t(city.nameKey, city.nameKey.split('.')?.pop())}
                  </h3>
                  <div className="text-sm text-gray-700 font-semibold mb-1">
                    {t(city.venuesKey, 'Venues')}
                  </div>
                  <div className="text-xs text-gray-500 mb-4">
                    {t(city.taglineKey, 'Beautiful City')}
                  </div>
                </div>
              </div>
            );
          })}
        </div>

        {/* Enhanced Call-to-Action Section */}
        <div className="text-center">
          <div className="bg-gradient-to-r from-yellow-400 to-amber-500 p-8 rounded-3xl shadow-2xl">
            <h3 className="text-2xl font-bold text-white mb-4">
              {t('destinations.ad.cta.title', 'Ready to Plan Your Dream Celebration?')}
            </h3>
            <p className="text-white/90 mb-6 max-w-2xl mx-auto">
              {t('destinations.ad.cta.subtitle', 'Discover exclusive wedding packages and venues in your favorite destination. Limited time offers available!')}
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <Button
                size="lg"
                className="bg-primary text-white hover:bg-primary/90 px-8 py-4 rounded-full font-bold shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1"
                onClick={() => handleCTAClick('explore_all')}
              >
                <Sparkles className="w-5 h-5 mr-2" />
                {t('destinations.exploreAll', 'Explore All Cities')}
              </Button>
              <Button
                variant="outline"
                size="lg"
                className="border-2 border-primary text-primary hover:bg-white hover:text-primary px-8 py-4 rounded-full font-bold transition-all duration-300 bg-white"
                onClick={() => handleCTAClick('get_quote')}
              >
                {t('destinations.ad.getQuote', 'Get Free Quote')}
              </Button>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
