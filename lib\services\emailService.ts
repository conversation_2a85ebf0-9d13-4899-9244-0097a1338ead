/**
 * Comprehensive Email Service for BookmyFestive using AWS Amplify
 * Handles all email templates and notifications across the application
 */

import { generateClient } from '@aws-amplify/api';
import { createEmailLog } from '@/src/graphql/mutations';
import { SESClient, SendEmailCommand } from '@aws-sdk/client-ses';

const client = generateClient();

// Email provider configuration
interface EmailProviderConfig {
  type: 'aws-ses' | 'sendgrid' | 'smtp';
  apiKey?: string;
  fromEmail?: string;
  region?: string;
  smtpHost?: string;
  smtpPort?: number;
  smtpUser?: string;
  smtpPass?: string;
}

// Email sending result
interface EmailSendResult {
  success: boolean;
  messageId?: string;
  error?: string;
}

// Enhanced email data interfaces for all email types
export interface WelcomeEmailData {
  email: string;
  firstName?: string;
  lastName?: string;
  type: 'newsletter' | 'login' | 'signup';
  preferences?: {
    weddingTips?: boolean;
    vendorRecommendations?: boolean;
    specialOffers?: boolean;
    eventUpdates?: boolean;
    blogUpdates?: boolean;
    frequency?: string;
  };
  interests?: string[];
  isVendor?: boolean;
  businessName?: string;
}

export interface OTPEmailData {
  email: string;
  userName: string;
  otp: string;
  expiryMinutes?: number;
}

export interface BookingEmailData {
  email: string;
  userName: string;
  bookingId: string;
  entityName: string;
  entityType: 'vendor' | 'venue';
  eventDate: string;
  eventTime?: string;
  amount?: string;
  status: 'confirmed' | 'pending' | 'cancelled';
  trackingUrl?: string;
}

export interface PaymentEmailData {
  email: string;
  userName: string;
  orderId: string;
  amount: string;
  items: Array<{
    name: string;
    quantity: number;
    price: string;
  }>;
  invoiceUrl?: string;
  trackingUrl?: string;
}

export interface NewsletterEmailData {
  email: string;
  userName?: string;
  articles: Array<{
    title: string;
    excerpt: string;
    url: string;
    image?: string;
  }>;
  offers?: Array<{
    title: string;
    discount: string;
    url: string;
  }>;
}

export interface OffersEmailData {
  email: string;
  userName: string;
  offers: Array<{
    title: string;
    description: string;
    discount: string;
    validUntil: string;
    url: string;
    image?: string;
  }>;
}

export interface FavoritesEmailData {
  email: string;
  userName: string;
  updates: Array<{
    name: string;
    type: 'vendor' | 'venue' | 'product';
    updateType: 'price_drop' | 'new_photos' | 'availability' | 'offer';
    url: string;
    image?: string;
  }>;
}

export interface VendorLaunchEmailData {
  email: string;
  userName: string;
  newVendors: Array<{
    name: string;
    category: string;
    city: string;
    discount?: string;
    url: string;
    image?: string;
  }>;
}

export interface AvailabilityEmailData {
  email: string;
  userName: string;
  entityName: string;
  entityType: 'vendor' | 'venue';
  requestedDate: string;
  status: 'available' | 'unavailable' | 'partially_available';
  alternativeDates?: string[];
  url: string;
}

export interface EmailTemplate {
  subject: string;
  htmlContent: string;
  textContent: string;
}

export interface OrderEmailData {
  email: string;
  userName: string;
  orderNumber: string;
  orderDate: string;
  totalAmount: string;
  items: Array<{
    name: string;
    quantity: number;
    price: string;
  }>;
  paymentMethod: string;
  estimatedDelivery: string;
}

class EmailService {
  private emailConfig!: EmailProviderConfig;
  private isConfigured: boolean = false;
  private sesClient?: SESClient;

  constructor() {
    this.loadEmailConfiguration();
  }

  /**
   * Load email configuration from environment variables
   */
  private loadEmailConfiguration() {
    const config: EmailProviderConfig = {
      type: (process.env.NEXT_PUBLIC_EMAIL_PROVIDER as any) || 'aws-ses',
      apiKey: process.env.NEXT_PUBLIC_EMAIL_API_KEY,
      fromEmail: process.env.NEXT_PUBLIC_FROM_EMAIL || '<EMAIL>',
      region: process.env.NEXT_PUBLIC_AWS_REGION || 'ap-south-1',
      smtpHost: process.env.NEXT_PUBLIC_SMTP_HOST,
      smtpPort: parseInt(process.env.NEXT_PUBLIC_SMTP_PORT || '587'),
      smtpUser: process.env.NEXT_PUBLIC_SMTP_USER,
      smtpPass: process.env.NEXT_PUBLIC_SMTP_PASS
    };

    this.emailConfig = config;
    
    // Initialize AWS SES client if using AWS
    if (config.type === 'aws-ses') {
      const accessKeyId = process.env.AWS_ACCESS_KEY_ID || '********************';
      const secretAccessKey = process.env.AWS_SECRET_ACCESS_KEY || 'cWT8hxW/UQLVPoQfJvaSHAxs704/qKKbODTMACRu';
      
      if (accessKeyId && secretAccessKey) {
        this.sesClient = new SESClient({ 
          region: config.region,
          credentials: {
            accessKeyId,
            secretAccessKey
          }
        });
      } else {
        console.warn('⚠️ AWS credentials not found. Email service will log only.');
      }
    }
    
    this.isConfigured = this.validateConfiguration(config);
    
    if (this.isConfigured) {
      console.log('✅ Email service configured successfully');
    } else {
      console.warn('⚠️ Email service not fully configured. Emails will be logged only.');
    }
  }

  /**
   * Validate email configuration
   */
  private validateConfiguration(config: EmailProviderConfig): boolean {
    switch (config.type) {
      case 'aws-ses':
        return !!(config.fromEmail && config.region && '********************' && 'cWT8hxW/UQLVPoQfJvaSHAxs704/qKKbODTMACRu');
      case 'sendgrid':
        return !!(config.apiKey && config.fromEmail);
      case 'smtp':
        return !!(config.smtpHost && config.smtpUser && config.smtpPass && config.fromEmail);
      default:
        return false;
    }
  }

  /**
   * Send actual email using configured provider
   */
  private async sendActualEmail(
    to: string,
    subject: string,
    htmlContent: string,
    textContent: string
  ): Promise<EmailSendResult> {
    if (!this.isConfigured) {
      return {
        success: false,
        error: 'Email service not configured'
      };
    }

    try {
      switch (this.emailConfig.type) {
        case 'aws-ses':
          return await this.sendViaAWSSES(to, subject, htmlContent, textContent);
        case 'sendgrid':
          return await this.sendViaSendGrid(to, subject, htmlContent, textContent);
        case 'smtp':
          return await this.sendViaSMTP(to, subject, htmlContent, textContent);
        default:
          return {
            success: false,
            error: 'Unsupported email provider'
          };
      }
    } catch (error) {
      console.error('Error sending actual email:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Send email via AWS SES using AWS SDK
   */
  private async sendViaAWSSES(
    to: string,
    subject: string,
    htmlContent: string,
    textContent: string
  ): Promise<EmailSendResult> {
    try {
      if (!this.sesClient) {
        throw new Error('AWS SES client not initialized');
      }

      const command = new SendEmailCommand({
        Source: this.emailConfig.fromEmail,
        Destination: { ToAddresses: [to] },
        Message: {
          Subject: { Data: subject },
          Body: {
            Html: { Data: htmlContent },
            Text: { Data: textContent }
          }
        }
      });

      const result = await this.sesClient.send(command);
      
      console.log('✅ Email sent successfully via AWS SES');
      
      return {
        success: true,
        messageId: result.MessageId
      };
    } catch (error) {
      console.error('AWS SES error:', error);
      return {
        success: false,
        error: `AWS SES error: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  /**
   * Send email via SendGrid
   */
  private async sendViaSendGrid(
    to: string,
    subject: string,
    htmlContent: string,
    textContent: string
  ): Promise<EmailSendResult> {
    try {
      const apiKey = process.env.SENDGRID_API_KEY;
      if (!apiKey) {
        return {
          success: false,
          error: 'SendGrid API key not configured. Please set SENDGRID_API_KEY environment variable.'
        };
      }

      const fromEmail = this.emailConfig.fromEmail || '<EMAIL>';
      
      const response = await fetch('https://api.sendgrid.com/v3/mail/send', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          personalizations: [
            {
              to: [{ email: to }]
            }
          ],
          from: { email: fromEmail },
          subject: subject,
          content: [
            {
              type: 'text/html',
              value: htmlContent
            },
            {
              type: 'text/plain',
              value: textContent
            }
          ]
        })
      });

      if (!response.ok) {
        const errorData = await response.text();
        throw new Error(`SendGrid API error: ${response.status} - ${errorData}`);
      }

      const messageId = response.headers.get('x-message-id') || `sg_${Date.now()}`;
      
      return {
        success: true,
        messageId
      };

    } catch (error) {
      console.error('SendGrid error:', error);
      return {
        success: false,
        error: `SendGrid error: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  /**
   * Send email via SMTP
   */
  private async sendViaSMTP(
    to: string,
    subject: string,
    htmlContent: string,
    textContent: string
  ): Promise<EmailSendResult> {
    try {
      // For SMTP, you would typically use a library like nodemailer
      // This is a simplified implementation
      
      const smtpConfig = {
        host: this.emailConfig.smtpHost,
        port: this.emailConfig.smtpPort,
        user: this.emailConfig.smtpUser,
        pass: this.emailConfig.smtpPass
      };

      if (!smtpConfig.host || !smtpConfig.user || !smtpConfig.pass) {
        return {
          success: false,
          error: 'SMTP configuration incomplete. Please set SMTP_HOST, SMTP_USER, and SMTP_PASS environment variables.'
        };
      }

      // In a real implementation, you would use nodemailer
      // For now, we'll simulate success
      console.log('📧 SMTP Email (Simulated):', { to, subject, host: smtpConfig.host });
      
      return {
        success: true,
        messageId: `smtp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
      };

    } catch (error) {
      console.error('SMTP error:', error);
      return {
        success: false,
        error: `SMTP error: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  /**
   * 1. Send Login OTP Email
   */
  async sendLoginOTPEmail(data: OTPEmailData): Promise<boolean> {
    try {
      const template = this.generateLoginOTPTemplate(data);
      const emailLogId = `log_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      const messageId = `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

      // First, try to send the actual email
      let emailSent = false;
      let actualMessageId = messageId;
      
      if (this.isConfigured) {
        try {
          const emailResult = await this.sendActualEmail(
            data.email,
            template.subject,
            template.htmlContent,
            template.textContent
          );
          
          if (emailResult.success) {
            emailSent = true;
            actualMessageId = emailResult.messageId || messageId;
            console.log('✅ OTP email sent successfully via', this.emailConfig.type);
          } else {
            console.warn('⚠️ OTP email sending failed:', emailResult.error);
          }
        } catch (emailError) {
          console.warn('⚠️ OTP email sending error:', emailError);
        }
      } else {
        console.log('📝 Email service not configured - logging only');
      }

      // Always log to database
      const result = await client.graphql({
        query: createEmailLog,
        variables: {
          input: {
            id: emailLogId,
            messageId: actualMessageId,
            emailType: 'LOGIN_OTP',
            recipient: data.email,
            subject: template.subject,
            status: emailSent ? 'SENT' : 'FAILED',
            errorMessage: emailSent ? undefined : 'Email service not configured or failed',
            templateData: JSON.stringify({
              userName: data.userName,
              otp: data.otp,
              expiryMinutes: data.expiryMinutes || 10
            }),
            sentAt: new Date().toISOString(),
            userId: data.userName
          }
        },
        authMode: 'userPool'
      });

      console.log('📧 OTP Email Content:');
      console.log('To:', data.email);
      console.log('Subject:', template.subject);
      console.log('Status:', emailSent ? 'SENT' : 'LOGGED ONLY');
      console.log('✅ Email logged to database with ID:', emailLogId);
      
      return !!(result.data as any)?.createEmailLog;
    } catch (error) {
      console.error('Error sending login OTP email:', error);
      return false;
    }
  }

  /**
   * Send User Welcome Email
   */
  async sendUserWelcomeEmail(data: WelcomeEmailData): Promise<boolean> {
    try {
      // Validate required fields
      if (!data.email || data.email.trim() === '') {
        console.error('❌ Email recipient cannot be empty');
        return false;
      }

      const template = this.generateUserWelcomeTemplate(data);
      const emailLogId = `log_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      const messageId = `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

      // Map the type to the correct EmailType enum value
      let emailType: string;
      switch (data.type) {
        case 'signup':
          emailType = 'WELCOME_SIGNUP';
          break;
        case 'login':
          emailType = 'WELCOME_SIGNUP'; // Use same type for login welcome
          break;
        case 'newsletter':
          emailType = 'NEWSLETTER_SUBSCRIPTION';
          break;
        default:
          emailType = 'WELCOME_SIGNUP';
      }

      // First, try to send the actual email
      let emailSent = false;
      let actualMessageId = messageId;
      
      if (this.isConfigured) {
        try {
          const emailResult = await this.sendActualEmail(
            data.email.trim(),
            template.subject,
            template.htmlContent,
            template.textContent
          );
          
          if (emailResult.success) {
            emailSent = true;
            actualMessageId = emailResult.messageId || messageId;
            console.log('✅ Email sent successfully via', this.emailConfig.type);
          } else {
            console.warn('⚠️ Email sending failed:', emailResult.error);
          }
        } catch (emailError) {
          console.warn('⚠️ Email sending error:', emailError);
        }
      } else {
        console.log('📝 Email service not configured - logging only');
      }

      // Always log to database
      const result = await client.graphql({
        query: createEmailLog,
        variables: {
          input: {
            id: emailLogId,
            messageId: actualMessageId,
            emailType,
            recipient: data.email.trim(),
            subject: template.subject,
            status: emailSent ? 'SENT' : 'FAILED',
            errorMessage: emailSent ? undefined : 'Email service not configured or failed',
            templateData: JSON.stringify({
              firstName: data.firstName,
              lastName: data.lastName,
              isVendor: data.isVendor || false,
              type: data.type
            }),
            sentAt: new Date().toISOString(),
            userId: data.email
          }
        },
        authMode: 'userPool'
      });

      console.log('📧 Welcome Email Content:');
      console.log('To:', data.email);
      console.log('Subject:', template.subject);
      console.log('Status:', emailSent ? 'SENT' : 'LOGGED ONLY');
      console.log('✅ Email logged to database with ID:', emailLogId);
      
      return !!(result.data as any)?.createEmailLog;
    } catch (error) {
      console.error('Error sending user welcome email:', error);
      return false;
    }
  }

  /**
   * Send Welcome Signup Email (Alias)
   */
  async sendWelcomeSignupEmail(data: WelcomeEmailData): Promise<boolean> {
    return this.sendUserWelcomeEmail({ ...data, type: 'signup' });
  }

  /**
   * Send Newsletter Welcome Email
   */
  async sendNewsletterWelcomeEmail(data: WelcomeEmailData): Promise<boolean> {
    try {
      // Validate required fields
      if (!data.email || data.email.trim() === '') {
        console.error('❌ Email recipient cannot be empty');
        return false;
      }

      const template = this.generateNewsletterWelcomeTemplate(data);
      const emailLogId = `log_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      const messageId = `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

      // First, try to send the actual email
      let emailSent = false;
      let actualMessageId = messageId;
      
      if (this.isConfigured) {
        try {
          const emailResult = await this.sendActualEmail(
            data.email.trim(),
            template.subject,
            template.htmlContent,
            template.textContent
          );
          
          if (emailResult.success) {
            emailSent = true;
            actualMessageId = emailResult.messageId || messageId;
            console.log('✅ Newsletter email sent successfully via', this.emailConfig.type);
          } else {
            console.warn('⚠️ Newsletter email sending failed:', emailResult.error);
          }
        } catch (emailError) {
          console.warn('⚠️ Newsletter email sending error:', emailError);
        }
      } else {
        console.log('📝 Email service not configured - logging only');
      }

      const result = await client.graphql({
        query: createEmailLog,
        variables: {
          input: {
            id: emailLogId,
            messageId: actualMessageId,
            emailType: 'NEWSLETTER_SUBSCRIPTION',
            recipient: data.email.trim(),
            subject: template.subject,
            status: emailSent ? 'SENT' : 'FAILED',
            errorMessage: emailSent ? undefined : 'Email service not configured or failed',
            templateData: JSON.stringify({
              firstName: data.firstName,
              lastName: data.lastName,
              interests: data.interests || [],
              preferences: data.preferences || {}
            }),
            sentAt: new Date().toISOString(),
            userId: data.email
          }
        },
        authMode: 'userPool'
      });

      console.log('📧 Newsletter Welcome Email:');
      console.log('To:', data.email);
      console.log('Subject:', template.subject);
      console.log('Status:', emailSent ? 'SENT' : 'LOGGED ONLY');
      console.log('✅ Email logged to database with ID:', emailLogId);
      
      return !!(result.data as any)?.createEmailLog;
    } catch (error) {
      console.error('Error sending newsletter welcome email:', error);
      return false;
    }
  }

  /**
   * Send Booking Confirmation Email
   */
  async sendBookingConfirmationEmail(data: BookingEmailData): Promise<boolean> {
    try {
      // Validate required fields
      if (!data.email || data.email.trim() === '') {
        console.error('❌ Email recipient cannot be empty');
        return false;
      }

      if (!data.userName || data.userName.trim() === '') {
        console.error('❌ User name cannot be empty');
        return false;
      }

      const template = this.generateBookingConfirmationTemplate(data);
      const emailLogId = `log_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      const messageId = `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

      // First, try to send the actual email
      let emailSent = false;
      let actualMessageId = messageId;
      
      if (this.isConfigured) {
        try {
          const emailResult = await this.sendActualEmail(
            data.email.trim(),
            template.subject,
            template.htmlContent,
            template.textContent
          );
          
          if (emailResult.success) {
            emailSent = true;
            actualMessageId = emailResult.messageId || messageId;
            console.log('✅ Booking confirmation email sent successfully via', this.emailConfig.type);
          } else {
            console.warn('⚠️ Booking confirmation email sending failed:', emailResult.error);
          }
        } catch (emailError) {
          console.warn('⚠️ Booking confirmation email sending error:', emailError);
        }
      } else {
        console.log('📝 Email service not configured - logging only');
      }

      const result = await client.graphql({
        query: createEmailLog,
        variables: {
          input: {
            id: emailLogId,
            messageId: actualMessageId,
            emailType: 'BOOKING_CONFIRMATION',
            recipient: data.email.trim(),
            subject: template.subject,
            status: emailSent ? 'SENT' : 'FAILED',
            errorMessage: emailSent ? undefined : 'Email service not configured or failed',
            templateData: JSON.stringify({
              userName: data.userName,
              bookingId: data.bookingId,
              entityName: data.entityName,
              entityType: data.entityType,
              eventDate: data.eventDate,
              status: data.status
            }),
            sentAt: new Date().toISOString(),
            userId: data.userName,
            bookingId: data.bookingId
          }
        },
        authMode: 'userPool'
      });

      console.log('📧 Booking Confirmation Email:');
      console.log('To:', data.email);
      console.log('Subject:', template.subject);
      console.log('Status:', emailSent ? 'SENT' : 'LOGGED ONLY');
      console.log('✅ Email logged to database with ID:', emailLogId);
      
      return !!(result.data as any)?.createEmailLog;
    } catch (error) {
      console.error('Error sending booking confirmation email:', error);
      return false;
    }
  }

  /**
   * Send Payment Success Invoice Email
   */
  async sendPaymentSuccessEmail(data: PaymentEmailData): Promise<boolean> {
    try {
      const template = this.generatePaymentSuccessTemplate(data);
      const emailLogId = `log_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      const messageId = `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

      // First, try to send the actual email
      let emailSent = false;
      let actualMessageId = messageId;
      
      if (this.isConfigured) {
        try {
          const emailResult = await this.sendActualEmail(
            data.email,
            template.subject,
            template.htmlContent,
            template.textContent
          );
          
          if (emailResult.success) {
            emailSent = true;
            actualMessageId = emailResult.messageId || messageId;
            console.log('✅ Payment success email sent successfully via', this.emailConfig.type);
          } else {
            console.warn('⚠️ Payment success email sending failed:', emailResult.error);
          }
        } catch (emailError) {
          console.warn('⚠️ Payment success email sending error:', emailError);
        }
      } else {
        console.log('📝 Email service not configured - logging only');
      }

      // Always log to database
      const result = await client.graphql({
        query: createEmailLog,
        variables: {
          input: {
            id: emailLogId,
            messageId: actualMessageId,
            emailType: 'PAYMENT_SUCCESS',
            recipient: data.email,
            subject: template.subject,
            status: emailSent ? 'SENT' : 'FAILED',
            errorMessage: emailSent ? undefined : 'Email service not configured or failed',
            templateData: JSON.stringify({
              userName: data.userName,
              orderId: data.orderId,
              amount: data.amount,
              items: data.items,
              invoiceUrl: data.invoiceUrl,
              trackingUrl: data.trackingUrl
            }),
            sentAt: new Date().toISOString(),
            userId: data.userName,
            orderId: data.orderId
          }
        },
        authMode: 'userPool'
      });

      console.log('📧 Payment Success Email:');
      console.log('To:', data.email);
      console.log('Subject:', template.subject);
      console.log('Status:', emailSent ? 'SENT' : 'LOGGED ONLY');
      console.log('✅ Email logged to database with ID:', emailLogId);
      
      return !!(result.data as any)?.createEmailLog;
    } catch (error) {
      console.error('Error sending payment success email:', error);
      return false;
    }
  }

  /**
   * Send Weekly News Email
   */
  async sendWeeklyNewsEmail(data: NewsletterEmailData): Promise<boolean> {
    try {
      const template = this.generateWeeklyNewsTemplate(data);
      const emailLogId = `log_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      const messageId = `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

      // First, try to send the actual email
      let emailSent = false;
      let actualMessageId = messageId;
      
      if (this.isConfigured) {
        try {
          const emailResult = await this.sendActualEmail(
            data.email,
            template.subject,
            template.htmlContent,
            template.textContent
          );
          
          if (emailResult.success) {
            emailSent = true;
            actualMessageId = emailResult.messageId || messageId;
            console.log('✅ Weekly news email sent successfully via', this.emailConfig.type);
          } else {
            console.warn('⚠️ Weekly news email sending failed:', emailResult.error);
          }
        } catch (emailError) {
          console.warn('⚠️ Weekly news email sending error:', emailError);
        }
      } else {
        console.log('📝 Email service not configured - logging only');
      }

      // Always log to database
      const result = await client.graphql({
        query: createEmailLog,
        variables: {
          input: {
            id: emailLogId,
            messageId: actualMessageId,
            emailType: 'WEEKLY_NEWS',
            recipient: data.email,
            subject: template.subject,
            status: emailSent ? 'SENT' : 'FAILED',
            errorMessage: emailSent ? undefined : 'Email service not configured or failed',
            templateData: JSON.stringify({
              userName: data.userName || 'there',
              articles: data.articles,
              offers: data.offers || []
            }),
            sentAt: new Date().toISOString(),
            userId: data.userName
          }
        },
        authMode: 'userPool'
      });

      console.log('📧 Weekly News Email:');
      console.log('To:', data.email);
      console.log('Subject:', template.subject);
      console.log('Status:', emailSent ? 'SENT' : 'LOGGED ONLY');
      console.log('✅ Email logged to database with ID:', emailLogId);
      
      return !!(result.data as any)?.createEmailLog;
    } catch (error) {
      console.error('Error sending weekly news email:', error);
      return false;
    }
  }

  /**
   * Send Offers Email
   */
  async sendOffersEmail(data: OffersEmailData): Promise<boolean> {
    try {
      const template = this.generateOffersTemplate(data);
      const emailLogId = `log_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      const messageId = `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

      // First, try to send the actual email
      let emailSent = false;
      let actualMessageId = messageId;
      
      if (this.isConfigured) {
        try {
          const emailResult = await this.sendActualEmail(
            data.email,
            template.subject,
            template.htmlContent,
            template.textContent
          );
          
          if (emailResult.success) {
            emailSent = true;
            actualMessageId = emailResult.messageId || messageId;
            console.log('✅ Offers email sent successfully via', this.emailConfig.type);
          } else {
            console.warn('⚠️ Offers email sending failed:', emailResult.error);
          }
        } catch (emailError) {
          console.warn('⚠️ Offers email sending error:', emailError);
        }
      } else {
        console.log('📝 Email service not configured - logging only');
      }

      // Always log to database
      const result = await client.graphql({
        query: createEmailLog,
        variables: {
          input: {
            id: emailLogId,
            messageId: actualMessageId,
            emailType: 'OFFERS_MAIL',
            recipient: data.email,
            subject: template.subject,
            status: emailSent ? 'SENT' : 'FAILED',
            errorMessage: emailSent ? undefined : 'Email service not configured or failed',
            templateData: JSON.stringify({
              userName: data.userName,
              offers: data.offers
            }),
            sentAt: new Date().toISOString(),
            userId: data.userName
          }
        },
        authMode: 'userPool'
      });

      console.log('📧 Offers Email:');
      console.log('To:', data.email);
      console.log('Subject:', template.subject);
      console.log('Status:', emailSent ? 'SENT' : 'LOGGED ONLY');
      console.log('✅ Email logged to database with ID:', emailLogId);
      
      return !!(result.data as any)?.createEmailLog;
    } catch (error) {
      console.error('Error sending offers email:', error);
      return false;
    }
  }

  /**
   * Send Favorites Notification Email
   */
  async sendFavoritesNotificationEmail(data: FavoritesEmailData): Promise<boolean> {
    try {
      const template = this.generateFavoritesNotificationTemplate(data);
      const emailLogId = `log_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      const messageId = `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

      // First, try to send the actual email
      let emailSent = false;
      let actualMessageId = messageId;
      
      if (this.isConfigured) {
        try {
          const emailResult = await this.sendActualEmail(
            data.email,
            template.subject,
            template.htmlContent,
            template.textContent
          );
          
          if (emailResult.success) {
            emailSent = true;
            actualMessageId = emailResult.messageId || messageId;
            console.log('✅ Favorites notification email sent successfully via', this.emailConfig.type);
          } else {
            console.warn('⚠️ Favorites notification email sending failed:', emailResult.error);
          }
        } catch (emailError) {
          console.warn('⚠️ Favorites notification email sending error:', emailError);
        }
      } else {
        console.log('📝 Email service not configured - logging only');
      }

      // Always log to database
      const result = await client.graphql({
        query: createEmailLog,
        variables: {
          input: {
            id: emailLogId,
            messageId: actualMessageId,
            emailType: 'FAVORITES_NOTIFICATION',
            recipient: data.email,
            subject: template.subject,
            status: emailSent ? 'SENT' : 'FAILED',
            errorMessage: emailSent ? undefined : 'Email service not configured or failed',
            templateData: JSON.stringify({
              userName: data.userName,
              updates: data.updates
            }),
            sentAt: new Date().toISOString(),
            userId: data.userName
          }
        },
        authMode: 'userPool'
      });

      console.log('📧 Favorites Notification Email:');
      console.log('To:', data.email);
      console.log('Subject:', template.subject);
      console.log('Status:', emailSent ? 'SENT' : 'LOGGED ONLY');
      console.log('✅ Email logged to database with ID:', emailLogId);
      
      return !!(result.data as any)?.createEmailLog;
    } catch (error) {
      console.error('Error sending favorites notification email:', error);
      return false;
    }
  }

  /**
   * Send Vendor Launch Email
   */
  async sendVendorLaunchEmail(data: VendorLaunchEmailData): Promise<boolean> {
    try {
      const template = this.generateVendorLaunchTemplate(data);
      const emailLogId = `log_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      const messageId = `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

      // First, try to send the actual email
      let emailSent = false;
      let actualMessageId = messageId;
      
      if (this.isConfigured) {
        try {
          const emailResult = await this.sendActualEmail(
            data.email,
            template.subject,
            template.htmlContent,
            template.textContent
          );
          
          if (emailResult.success) {
            emailSent = true;
            actualMessageId = emailResult.messageId || messageId;
            console.log('✅ Vendor launch email sent successfully via', this.emailConfig.type);
          } else {
            console.warn('⚠️ Vendor launch email sending failed:', emailResult.error);
          }
        } catch (emailError) {
          console.warn('⚠️ Vendor launch email sending error:', emailError);
        }
      } else {
        console.log('📝 Email service not configured - logging only');
      }

      // Always log to database
      const result = await client.graphql({
        query: createEmailLog,
        variables: {
          input: {
            id: emailLogId,
            messageId: actualMessageId,
            emailType: 'VENDOR_LAUNCH',
            recipient: data.email,
            subject: template.subject,
            status: emailSent ? 'SENT' : 'FAILED',
            errorMessage: emailSent ? undefined : 'Email service not configured or failed',
            templateData: JSON.stringify({
              userName: data.userName,
              newVendors: data.newVendors
            }),
            sentAt: new Date().toISOString(),
            userId: data.userName
          }
        },
        authMode: 'userPool'
      });

      console.log('📧 Vendor Launch Email:');
      console.log('To:', data.email);
      console.log('Subject:', template.subject);
      console.log('Status:', emailSent ? 'SENT' : 'LOGGED ONLY');
      console.log('✅ Email logged to database with ID:', emailLogId);
      
      return !!(result.data as any)?.createEmailLog;
    } catch (error) {
      console.error('Error sending vendor launch email:', error);
      return false;
    }
  }

  /**
   * Send Availability Check Email
   */
  async sendAvailabilityCheckEmail(data: AvailabilityEmailData): Promise<boolean> {
    try {
      const template = this.generateAvailabilityCheckTemplate(data);
      const emailLogId = `log_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      const messageId = `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

      // First, try to send the actual email
      let emailSent = false;
      let actualMessageId = messageId;
      
      if (this.isConfigured) {
        try {
          const emailResult = await this.sendActualEmail(
            data.email,
            template.subject,
            template.htmlContent,
            template.textContent
          );
          
          if (emailResult.success) {
            emailSent = true;
            actualMessageId = emailResult.messageId || messageId;
            console.log('✅ Availability check email sent successfully via', this.emailConfig.type);
          } else {
            console.warn('⚠️ Availability check email sending failed:', emailResult.error);
          }
        } catch (emailError) {
          console.warn('⚠️ Availability check email sending error:', emailError);
        }
      } else {
        console.log('📝 Email service not configured - logging only');
      }

      // Always log to database
      const result = await client.graphql({
        query: createEmailLog,
        variables: {
          input: {
            id: emailLogId,
            messageId: actualMessageId,
            emailType: 'AVAILABILITY_CHECK',
            recipient: data.email,
            subject: template.subject,
            status: emailSent ? 'SENT' : 'FAILED',
            errorMessage: emailSent ? undefined : 'Email service not configured or failed',
            templateData: JSON.stringify({
              userName: data.userName,
              entityName: data.entityName,
              entityType: data.entityType,
              requestedDate: data.requestedDate,
              status: data.status,
              alternativeDates: data.alternativeDates || [],
              url: data.url
            }),
            sentAt: new Date().toISOString(),
            userId: data.userName
          }
        },
        authMode: 'userPool'
      });

      console.log('📧 Availability Check Email:');
      console.log('To:', data.email);
      console.log('Subject:', template.subject);
      console.log('Status:', emailSent ? 'SENT' : 'LOGGED ONLY');
      console.log('✅ Email logged to database with ID:', emailLogId);
      
      return !!(result.data as any)?.createEmailLog;
    } catch (error) {
      console.error('Error sending availability check email:', error);
      return false;
    }
  }

  /**
   * Send bulk emails to multiple recipients
   */
  async sendBulkEmails(emailType: string, recipients: string[], templateData: any): Promise<boolean> {
    try {
      // For bulk emails, we'll send them individually for now
      // In production, you might want to use SES bulk sending
      let successCount = 0;
      
      for (const recipient of recipients) {
        try {
          const result = await this.sendActualEmail(
            recipient,
            `Bulk ${emailType}`,
            JSON.stringify(templateData),
            JSON.stringify(templateData)
          );
          
          if (result.success) {
            successCount++;
          }
        } catch (error) {
          console.error(`Error sending bulk email to ${recipient}:`, error);
        }
      }
      
      console.log(`✅ Bulk email sent: ${successCount}/${recipients.length} successful`);
      return successCount > 0;
    } catch (error) {
      console.error('Error sending bulk emails:', error);
      return false;
    }
  }

  /**
   * Generic email sending method using our email service
   */
  private async sendEmail(email: string, template: EmailTemplate, type: string): Promise<boolean> {
    try {
      const result = await this.sendActualEmail(
        email,
        template.subject,
        template.htmlContent,
        template.textContent
      );
      
      const success = result.success;
      if (success) {
        console.log(`${type} email sent successfully to:`, email);
      }
      return success;
    } catch (error) {
      console.error(`Error sending ${type} email:`, error);
      return false;
    }
  }

  /**
   * Send email to multiple recipients using our email service
   */
  private async sendBulkEmail(emails: string[], template: EmailTemplate, type: string): Promise<boolean> {
    try {
      let successCount = 0;
      
      for (const email of emails) {
        try {
          const result = await this.sendActualEmail(
            email,
            template.subject,
            template.htmlContent,
            template.textContent
          );
          
          if (result.success) {
            successCount++;
          }
        } catch (error) {
          console.error(`Error sending bulk ${type} email to ${email}:`, error);
        }
      }
      
      const success = successCount > 0;
      if (success) {
        console.log(`${type} bulk email sent successfully to ${successCount}/${emails.length} recipients`);
      }
      return success;
    } catch (error) {
      console.error(`Error sending bulk ${type} email:`, error);
      return false;
    }
  }

  /**
   * Generate Login OTP Email Template
   */
  private generateLoginOTPTemplate(data: OTPEmailData): EmailTemplate {
    const subject = `Your BookmyFestive Login OTP - ${data.otp}`;

    const htmlContent = `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login OTP - BookmyFestive</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: linear-gradient(135deg, #a31515, #d32f2f); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
        .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }
        .otp-box { background: white; border: 2px solid #a31515; padding: 20px; text-align: center; margin: 20px 0; border-radius: 8px; }
        .otp-code { font-size: 32px; font-weight: bold; color: #a31515; letter-spacing: 5px; }
        .footer { text-align: center; margin-top: 20px; color: #666; font-size: 14px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔐 Login Verification</h1>
            <p>BookmyFestive</p>
        </div>
        <div class="content">
            <h2>Hello ${data.userName}!</h2>
            <p>You requested to log in to your BookmyFestive account. Please use the OTP below to complete your login:</p>

            <div class="otp-box">
                <div class="otp-code">${data.otp}</div>
                <p><strong>Valid for ${data.expiryMinutes || 10} minutes</strong></p>
            </div>

            <p><strong>Security Note:</strong> Never share this OTP with anyone. Our team will never ask for your OTP.</p>

            <p>If you didn't request this login, please ignore this email or contact our support team.</p>
        </div>
        <div class="footer">
            <p>© 2025 BookmyFestive. All rights reserved.</p>
            <p>Making your Celebration dreams come true! 💕</p>
        </div>
    </div>
</body>
</html>`;

    const textContent = `
Login OTP - BookmyFestive

Hello ${data.userName}!

Your OTP for BookmyFestive login is: ${data.otp}
Valid for ${data.expiryMinutes || 10} minutes.

Security Note: Never share this OTP with anyone.

If you didn't request this login, please ignore this email.

© 2025 BookmyFestive. All rights reserved.
`;

    return { subject, htmlContent, textContent };
  }

  /**
   * Generate Booking Confirmation Email Template
   */
  private generateBookingConfirmationTemplate(data: BookingEmailData): EmailTemplate {
    const subject = `Booking Confirmed: ${data.entityName} - ${data.eventDate}`;

    const htmlContent = `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Booking Confirmation - BookmyFestive</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: linear-gradient(135deg, #a31515, #d32f2f); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
        .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }
        .booking-details { background: white; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #a31515; }
        .booking-id { background: #f0f0f0; padding: 10px; border-radius: 5px; font-family: monospace; text-align: center; margin: 10px 0; }
        .status { display: inline-block; padding: 5px 10px; border-radius: 20px; font-size: 14px; font-weight: bold; }
        .status-confirmed { background: #e6f7e6; color: #2e7d32; }
        .status-pending { background: #fff8e1; color: #f57f17; }
        .status-cancelled { background: #ffebee; color: #c62828; }
        .button { display: inline-block; background: #a31515; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; margin: 20px 0; }
        .footer { text-align: center; margin-top: 20px; color: #666; font-size: 14px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>✅ Booking Confirmed!</h1>
            <p>Your ${data.entityType === 'vendor' ? 'Vendor' : 'Venue'} Booking Details</p>
        </div>
        <div class="content">
            <h2>Hello ${data.userName}!</h2>
            <p>Your booking with <strong>${data.entityName}</strong> has been confirmed. Here are your booking details:</p>

            <div class="booking-details">
                <h3>Booking Information</h3>
                <p><strong>Booking ID:</strong></p>
                <div class="booking-id">${data.bookingId}</div>

                <p><strong>${data.entityType === 'vendor' ? 'Vendor' : 'Venue'}:</strong> ${data.entityName}</p>
                <p><strong>Event Date:</strong> ${data.eventDate}</p>
                ${data.eventTime ? `<p><strong>Event Time:</strong> ${data.eventTime}</p>` : ''}
                ${data.amount ? `<p><strong>Amount:</strong> ${data.amount}</p>` : ''}
                <p><strong>Status:</strong> <span class="status status-${data.status}">${data.status.toUpperCase()}</span></p>
            </div>

            <p>You can track your booking and communicate with the ${data.entityType} through your BookmyFestive dashboard.</p>

            ${data.trackingUrl ? `
            <div style="text-align: center;">
                <a href="${data.trackingUrl}" class="button">Track Your Booking</a>
            </div>
            ` : ''}

            <p><strong>Next Steps:</strong></p>
            <ol>
                <li>Review your booking details</li>
                <li>Communicate any specific requirements to the ${data.entityType}</li>
                <li>Make any remaining payments as per the schedule</li>
            </ol>

            <p>If you have any questions or need to make changes to your booking, please contact us or the ${data.entityType} directly.</p>

            <p>Thank you for choosing BookmyFestive for your wedding planning needs!</p>
        </div>
        <div class="footer">
            <p>© 2025 BookmyFestive. All rights reserved.</p>
            <p>Making your Celebration dreams come true! 💕</p>
        </div>
    </div>
</body>
</html>`;

    const textContent = `
Booking Confirmation - BookmyFestive

Hello ${data.userName}!

Your booking with ${data.entityName} has been confirmed. Here are your booking details:

Booking Information:
Booking ID: ${data.bookingId}
${data.entityType === 'vendor' ? 'Vendor' : 'Venue'}: ${data.entityName}
Event Date: ${data.eventDate}
${data.eventTime ? `Event Time: ${data.eventTime}` : ''}
${data.amount ? `Amount: ${data.amount}` : ''}
Status: ${data.status.toUpperCase()}

You can track your booking at: ${data.trackingUrl || 'https://BookmyFestive.com/dashboard/bookings'}

Next Steps:
1. Review your booking details
2. Communicate any specific requirements to the ${data.entityType}
3. Make any remaining payments as per the schedule

Thank you for choosing BookmyFestive for your wedding planning needs!

© 2025 BookmyFestive. All rights reserved.
`;

    return { subject, htmlContent, textContent };
  }

  /**
   * Generate Payment Success Email Template
   */
  private generatePaymentSuccessTemplate(data: PaymentEmailData): EmailTemplate {
    const subject = `Payment Successful - Order #${data.orderId}`;

    const htmlContent = `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment Successful - BookmyFestive</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: linear-gradient(135deg, #a31515, #d32f2f); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
        .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }
        .payment-details { background: white; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #a31515; }
        .order-id { background: #f0f0f0; padding: 10px; border-radius: 5px; font-family: monospace; text-align: center; margin: 10px 0; }
        .items-table { width: 100%; border-collapse: collapse; margin: 15px 0; }
        .items-table th, .items-table td { padding: 10px; text-align: left; border-bottom: 1px solid #eee; }
        .items-table th { background: #f5f5f5; }
        .total-row { font-weight: bold; }
        .button { display: inline-block; background: #a31515; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; margin: 10px 5px; }
        .footer { text-align: center; margin-top: 20px; color: #666; font-size: 14px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>💰 Payment Successful!</h1>
            <p>Thank you for your purchase</p>
        </div>
        <div class="content">
            <h2>Hello ${data.userName}!</h2>
            <p>Your payment has been successfully processed. Here are your order details:</p>

            <div class="payment-details">
                <h3>Order Information</h3>
                <p><strong>Order ID:</strong></p>
                <div class="order-id">${data.orderId}</div>

                <p><strong>Amount Paid:</strong> ${data.amount}</p>

                <h4>Order Items:</h4>
                <table class="items-table">
                    <thead>
                        <tr>
                            <th>Item</th>
                            <th>Quantity</th>
                            <th>Price</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${data.items.map(item => `
                        <tr>
                            <td>${item.name}</td>
                            <td>${item.quantity}</td>
                            <td>${item.price}</td>
                        </tr>
                        `).join('')}
                        <tr class="total-row">
                            <td colspan="2">Total</td>
                            <td>${data.amount}</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div style="text-align: center;">
                ${data.invoiceUrl ? `<a href="${data.invoiceUrl}" class="button">View Invoice</a>` : ''}
                ${data.trackingUrl ? `<a href="${data.trackingUrl}" class="button">Track Order</a>` : ''}
            </div>

            <p>If you have any questions about your order, please contact our customer support team.</p>

            <p>Thank you for shopping with BookmyFestive!</p>
        </div>
        <div class="footer">
            <p>© 2025 BookmyFestive. All rights reserved.</p>
            <p>Making your Celebration dreams come true! 💕</p>
        </div>
    </div>
</body>
</html>`;

    const textContent = `
Payment Successful - BookmyFestive

Hello ${data.userName}!

Your payment has been successfully processed. Here are your order details:

Order Information:
Order ID: ${data.orderId}
Amount Paid: ${data.amount}

Order Items:
${data.items.map(item => `- ${item.name} (${item.quantity}) - ${item.price}`).join('\n')}
Total: ${data.amount}

${data.invoiceUrl ? `View Invoice: ${data.invoiceUrl}` : ''}
${data.trackingUrl ? `Track Order: ${data.trackingUrl}` : ''}

Thank you for shopping with BookmyFestive!

© 2025 BookmyFestive. All rights reserved.
`;

    return { subject, htmlContent, textContent };
  }

  /**
   * Generate Weekly News Email Template
   */
  private generateWeeklyNewsTemplate(data: NewsletterEmailData): EmailTemplate {
    const subject = `This Week's Wedding Updates from BookmyFestive`;

    const htmlContent = `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Weekly Wedding Updates - BookmyFestive</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: linear-gradient(135deg, #a31515, #d32f2f); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
        .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }
        .article { background: white; padding: 20px; margin-bottom: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.05); }
        .article-image { width: 100%; height: 150px; object-fit: cover; border-radius: 6px; margin-bottom: 15px; }
        .article-title { font-size: 18px; font-weight: bold; margin-bottom: 10px; color: #a31515; }
        .article-excerpt { font-size: 14px; color: #555; margin-bottom: 15px; }
        .read-more { display: inline-block; background: #a31515; color: white; padding: 8px 15px; text-decoration: none; border-radius: 4px; font-size: 14px; }
        .offers-section { background: #fff8e1; padding: 20px; border-radius: 8px; margin: 20px 0; }
        .offer-item { margin-bottom: 15px; padding-bottom: 15px; border-bottom: 1px solid #eee; }
        .offer-item:last-child { border-bottom: none; margin-bottom: 0; padding-bottom: 0; }
        .offer-title { font-weight: bold; color: #a31515; }
        .offer-discount { display: inline-block; background: #a31515; color: white; padding: 3px 8px; border-radius: 4px; font-size: 12px; margin: 5px 0; }
        .footer { text-align: center; margin-top: 20px; color: #666; font-size: 14px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📰 Weekly Wedding Updates</h1>
            <p>Stay Inspired with BookmyFestive</p>
        </div>
        <div class="content">
            <h2>Hello ${data.userName || 'there'}!</h2>
            <p>Here are this week's top wedding trends, tips, and inspiration just for you:</p>

            <div class="articles-section">
                ${data.articles.map(article => `
                <div class="article">
                    ${article.image ? `<img src="${article.image}" alt="${article.title}" class="article-image">` : ''}
                    <div class="article-title">${article.title}</div>
                    <div class="article-excerpt">${article.excerpt}</div>
                    <a href="${article.url}" class="read-more">Read More</a>
                </div>
                `).join('')}
            </div>

            ${data.offers && data.offers.length > 0 ? `
            <div class="offers-section">
                <h3>🎁 Special Offers This Week</h3>
                ${data.offers.map(offer => `
                <div class="offer-item">
                    <div class="offer-title">${offer.title}</div>
                    <div class="offer-discount">${offer.discount} OFF</div>
                    <div><a href="${offer.url}">View Offer</a></div>
                </div>
                `).join('')}
            </div>
            ` : ''}

            <p>We hope you find these updates helpful for your wedding planning journey!</p>
            <p>Happy Planning,<br>The BookmyFestive Team</p>
        </div>
        <div class="footer">
            <p>© 2025 BookmyFestive. All rights reserved.</p>
            <p>You're receiving this email because you subscribed to our newsletter.</p>
            <p><a href="https://BookmyFestive.com/preferences">Manage Preferences</a> | <a href="https://BookmyFestive.com/unsubscribe">Unsubscribe</a></p>
        </div>
    </div>
</body>
</html>`;

    const textContent = `
Weekly Wedding Updates from BookmyFestive

Hello ${data.userName || 'there'}!

Here are this week's top wedding trends, tips, and inspiration just for you:

${data.articles.map(article => `
${article.title}
${article.excerpt}
Read More: ${article.url}
`).join('\n')}

${data.offers && data.offers.length > 0 ? `
Special Offers This Week:
${data.offers.map(offer => `
${offer.title} - ${offer.discount} OFF
View Offer: ${offer.url}
`).join('\n')}
` : ''}

We hope you find these updates helpful for your wedding planning journey!

Happy Planning,
The BookmyFestive Team

© 2025 BookmyFestive. All rights reserved.
You're receiving this email because you subscribed to our newsletter.
Manage Preferences: https://BookmyFestive.com/preferences
Unsubscribe: https://BookmyFestive.com/unsubscribe
`;

    return { subject, htmlContent, textContent };
  }

  /**
   * Generate Offers Email Template
   */
  private generateOffersTemplate(data: OffersEmailData): EmailTemplate {
    const subject = `Exclusive Wedding Offers Just for You! 🎁`;

    const htmlContent = `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Exclusive Wedding Offers - BookmyFestive</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: linear-gradient(135deg, #a31515, #d32f2f); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
        .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }
        .offer-card { background: white; border-radius: 8px; overflow: hidden; margin-bottom: 25px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); }
        .offer-image { width: 100%; height: 180px; object-fit: cover; }
        .offer-content { padding: 20px; }
        .offer-title { font-size: 18px; font-weight: bold; color: #a31515; margin-bottom: 10px; }
        .offer-description { font-size: 14px; color: #555; margin-bottom: 15px; }
        .offer-meta { display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px; }
        .offer-discount { background: #a31515; color: white; padding: 5px 10px; border-radius: 20px; font-weight: bold; }
        .offer-validity { font-size: 13px; color: #777; }
        .offer-button { display: inline-block; background: #a31515; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px; font-weight: bold; }
        .countdown { background: #fff8e1; padding: 15px; border-radius: 8px; text-align: center; margin: 20px 0; }
        .footer { text-align: center; margin-top: 20px; color: #666; font-size: 14px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎁 Exclusive Wedding Offers</h1>
            <p>Special Deals Just for You</p>
        </div>
        <div class="content">
            <h2>Hello ${data.userName}!</h2>
            <p>We've curated some amazing offers from our top wedding vendors just for you:</p>

            <div class="countdown">
                <h3>⏰ Limited Time Offers</h3>
                <p>Don't miss out on these exclusive deals for BookmyFestive members!</p>
            </div>

            ${data.offers.map(offer => `
            <div class="offer-card">
                ${offer.image ? `<img src="${offer.image}" alt="${offer.title}" class="offer-image">` : ''}
                <div class="offer-content">
                    <div class="offer-title">${offer.title}</div>
                    <div class="offer-description">${offer.description}</div>
                    <div class="offer-meta">
                        <div class="offer-discount">${offer.discount}</div>
                        <div class="offer-validity">Valid until: ${new Date(offer.validUntil).toLocaleDateString()}</div>
                    </div>
                    <a href="${offer.url}" class="offer-button">Claim Offer</a>
                </div>
            </div>
            `).join('')}

            <p>These offers are exclusively available to our BookmyFestive members. Simply click on the offer you're interested in to claim it.</p>
            <p>Happy Wedding Planning!</p>
        </div>
        <div class="footer">
            <p>© 2025 BookmyFestive. All rights reserved.</p>
            <p>You're receiving this email because you opted in for special offers.</p>
            <p><a href="https://BookmyFestive.com/preferences">Manage Preferences</a> | <a href="https://BookmyFestive.com/unsubscribe">Unsubscribe</a></p>
        </div>
    </div>
</body>
</html>`;

    const textContent = `
Exclusive Wedding Offers Just for You! 🎁

Hello ${data.userName}!

We've curated some amazing offers from our top wedding vendors just for you:

LIMITED TIME OFFERS - Don't miss out on these exclusive deals for BookmyFestive members!

${data.offers.map(offer => `
${offer.title}
${offer.description}
Discount: ${offer.discount}
Valid until: ${new Date(offer.validUntil).toLocaleDateString()}
Claim Offer: ${offer.url}
`).join('\n')}

These offers are exclusively available to our BookmyFestive members. Simply click on the offer you're interested in to claim it.

Happy Wedding Planning!

© 2025 BookmyFestive. All rights reserved.
You're receiving this email because you opted in for special offers.
Manage Preferences: https://BookmyFestive.com/preferences
Unsubscribe: https://BookmyFestive.com/unsubscribe
`;

    return { subject, htmlContent, textContent };
  }

  /**
   * Generate Favorites Notification Email Template
   */
  private generateFavoritesNotificationTemplate(data: FavoritesEmailData): EmailTemplate {
    const subject = `Updates on Your Favorite Vendors & Venues`;

    const htmlContent = `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Updates on Your Favorites - BookmyFestive</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: linear-gradient(135deg, #a31515, #d32f2f); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
        .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }
        .update-card { display: flex; background: white; border-radius: 8px; overflow: hidden; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.05); }
        .update-image { width: 120px; height: 120px; object-fit: cover; }
        .update-content { padding: 15px; flex: 1; }
        .update-title { font-size: 16px; font-weight: bold; margin-bottom: 5px; }
        .update-type { display: inline-block; padding: 3px 8px; border-radius: 12px; font-size: 12px; margin-bottom: 10px; }
        .update-type-price { background: #e8f5e9; color: #2e7d32; }
        .update-type-photos { background: #e3f2fd; color: #1565c0; }
        .update-type-availability { background: #fff8e1; color: #f57f17; }
        .update-type-offer { background: #fce4ec; color: #c2185b; }
        .update-button { display: inline-block; background: #a31515; color: white; padding: 6px 12px; text-decoration: none; border-radius: 4px; font-size: 13px; }
        .footer { text-align: center; margin-top: 20px; color: #666; font-size: 14px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>❤️ Updates on Your Favorites</h1>
            <p>New Updates from Vendors & Venues You Love</p>
        </div>
        <div class="content">
            <h2>Hello ${data.userName}!</h2>
            <p>There are new updates from the vendors and venues you've favorited:</p>

            ${data.updates.map(update => `
            <div class="update-card">
                ${update.image ? `<img src="${update.image}" alt="${update.name}" class="update-image">` : ''}
                <div class="update-content">
                    <div class="update-title">${update.name}</div>
                    <div class="update-type update-type-${update.updateType.split('_')[0]}">
                        ${update.updateType === 'price_drop' ? '💰 Price Drop' :
                          update.updateType === 'new_photos' ? '📸 New Photos' :
                          update.updateType === 'availability' ? '📅 New Availability' :
                          update.updateType === 'offer' ? '🎁 Special Offer' : 'Update'}
                    </div>
                    <p>${
                        update.updateType === 'price_drop' ? 'Prices have been reduced! Check out the new rates.' :
                        update.updateType === 'new_photos' ? 'New photos have been added to the gallery.' :
                        update.updateType === 'availability' ? 'New dates are now available for booking.' :
                        update.updateType === 'offer' ? 'A special offer is now available for you.' :
                        'There are new updates available.'
                    }</p>
                    <a href="${update.url}" class="update-button">View Details</a>
                </div>
            </div>
            `).join('')}

            <p>Don't miss out on these updates! Click on each item to see the details.</p>
            <p>Happy Wedding Planning!</p>
        </div>
        <div class="footer">
            <p>© 2025 BookmyFestive. All rights reserved.</p>
            <p>You're receiving this email because you've favorited these items.</p>
            <p><a href="https://BookmyFestive.com/preferences">Manage Preferences</a> | <a href="https://BookmyFestive.com/unsubscribe">Unsubscribe</a></p>
        </div>
    </div>
</body>
</html>`;

    const textContent = `
Updates on Your Favorite Vendors & Venues

Hello ${data.userName}!

There are new updates from the vendors and venues you've favorited:

${data.updates.map(update => `
${update.name}
${update.updateType === 'price_drop' ? '💰 Price Drop' :
  update.updateType === 'new_photos' ? '📸 New Photos' :
  update.updateType === 'availability' ? '📅 New Availability' :
  update.updateType === 'offer' ? '🎁 Special Offer' : 'Update'}

${update.updateType === 'price_drop' ? 'Prices have been reduced! Check out the new rates.' :
  update.updateType === 'new_photos' ? 'New photos have been added to the gallery.' :
  update.updateType === 'availability' ? 'New dates are now available for booking.' :
  update.updateType === 'offer' ? 'A special offer is now available for you.' :
  'There are new updates available.'}

View Details: ${update.url}
`).join('\n')}

Don't miss out on these updates! Click on each item to see the details.

Happy Wedding Planning!

© 2025 BookmyFestive. All rights reserved.
You're receiving this email because you've favorited these items.
Manage Preferences: https://BookmyFestive.com/preferences
Unsubscribe: https://BookmyFestive.com/unsubscribe
`;

    return { subject, htmlContent, textContent };
  }

  /**
   * Generate newsletter welcome email template
   */
  private generateNewsletterWelcomeTemplate(data: WelcomeEmailData): EmailTemplate {
    const name = data.firstName ? `${data.firstName}${data.lastName ? ' ' + data.lastName : ''}` : 'there';
    const interestsList = data.interests?.map(interest =>
      interest.toLowerCase().replace('_', ' ')
    ).join(', ') || 'wedding planning';

    const subject = `Welcome to BookmyFestive Newsletter! 💕`;

    const htmlContent = `
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Welcome to BookmyFestive Newsletter</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: linear-gradient(135deg, #ec4899, #8b5cf6); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
        .content { background: white; padding: 30px; border: 1px solid #e5e7eb; }
        .footer { background: #f9fafb; padding: 20px; text-align: center; border-radius: 0 0 10px 10px; font-size: 14px; color: #6b7280; }
        .button { display: inline-block; background: #ec4899; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 10px 0; }
        .highlight { background: #fef3f2; padding: 15px; border-radius: 6px; margin: 15px 0; border-left: 4px solid #ec4899; }
        .interests { background: #f0f9ff; padding: 15px; border-radius: 6px; margin: 15px 0; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎉 Welcome to BookmyFestive!</h1>
            <p>Thank you for subscribing to our newsletter</p>
        </div>
        
        <div class="content">
            <h2>Hello ${name}! 👋</h2>
            
            <p>Welcome to the BookmyFestive family! We're thrilled to have you join our community of couples planning their perfect wedding.</p>
            
            <div class="highlight">
                <h3>🎊 What to Expect:</h3>
                <ul>
                    <li><strong>Wedding Tips & Advice:</strong> Expert guidance for planning Your Dream Celebration</li>
                    <li><strong>Vendor Recommendations:</strong> Discover top-rated wedding vendors in your city</li>
                    <li><strong>Exclusive Offers:</strong> Special discounts and deals just for our subscribers</li>
                    <li><strong>Latest Trends:</strong> Stay updated with the newest wedding trends and ideas</li>
                </ul>
            </div>

            ${data.interests && data.interests.length > 0 ? `
            <div class="interests">
                <h3>📝 Your Interests:</h3>
                <p>Based on your preferences, we'll send you personalized content about: <strong>${interestsList}</strong></p>
            </div>
            ` : ''}

            <p>You'll receive our newsletter <strong>${data.preferences?.frequency?.toLowerCase() || 'weekly'}</strong> with the latest wedding inspiration, vendor spotlights, and exclusive offers.</p>
            
            <div style="text-align: center; margin: 30px 0;">
                <a href="https://BookmyFestive.com" class="button">Explore BookmyFestive</a>
            </div>
            
            <p>If you have any questions or need help planning your wedding, feel free to reach out to us. We're here to make your wedding journey magical!</p>
            
            <p>Happy Wedding Planning! 💕</p>
            <p><strong>The BookmyFestive Team</strong></p>
        </div>
        
        <div class="footer">
            <p>You're receiving this email because you subscribed to our newsletter.</p>
            <p><a href="https://BookmyFestive.com/newsletter/preferences">Manage Preferences</a> | <a href="https://BookmyFestive.com/newsletter/unsubscribe">Unsubscribe</a></p>
            <p>© 2025 BookmyFestive. All rights reserved.</p>
        </div>
    </div>
</body>
</html>`;

    const textContent = `
Welcome to BookmyFestive Newsletter!

Hello ${name}!

Thank you for subscribing to our newsletter. We're excited to have you join our community of couples planning their perfect wedding.

What to Expect:
- Wedding Tips & Advice: Expert guidance for planning Your Dream Celebration
- Vendor Recommendations: Discover top-rated wedding vendors in your city  
- Exclusive Offers: Special discounts and deals just for our subscribers
- Latest Trends: Stay updated with the newest wedding trends and ideas

${data.interests && data.interests.length > 0 ? `Your Interests: ${interestsList}` : ''}

You'll receive our newsletter ${data.preferences?.frequency?.toLowerCase() || 'weekly'} with the latest wedding inspiration, vendor spotlights, and exclusive offers.

Visit us at: https://BookmyFestive.com

Happy Wedding Planning!
The BookmyFestive Team

---
Manage Preferences: https://BookmyFestive.com/newsletter/preferences
Unsubscribe: https://BookmyFestive.com/newsletter/unsubscribe
`;

    return { subject, htmlContent, textContent };
  }

  /**
   * Generate user welcome email template
   */
  private generateUserWelcomeTemplate(data: WelcomeEmailData): EmailTemplate {
    const name = data.firstName ? `${data.firstName}${data.lastName ? ' ' + data.lastName : ''}` : 'there';
    const isSignup = data.type === 'signup';
    const userType = data.isVendor ? 'vendor' : 'couple';
    
    const subject = isSignup 
      ? `Welcome to BookmyFestive! Your wedding journey starts here 🎊`
      : `Welcome back to BookmyFestive! 👋`;

    const htmlContent = `
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${isSignup ? 'Welcome to' : 'Welcome back to'} BookmyFestive</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: linear-gradient(135deg, #ec4899, #8b5cf6); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
        .content { background: white; padding: 30px; border: 1px solid #e5e7eb; }
        .footer { background: #f9fafb; padding: 20px; text-align: center; border-radius: 0 0 10px 10px; font-size: 14px; color: #6b7280; }
        .button { display: inline-block; background: #ec4899; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 10px 5px; }
        .feature-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin: 20px 0; }
        .feature { background: #f8fafc; padding: 15px; border-radius: 6px; text-align: center; }
        .highlight { background: #fef3f2; padding: 15px; border-radius: 6px; margin: 15px 0; border-left: 4px solid #ec4899; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>${isSignup ? '🎉 Welcome to BookmyFestive!' : '👋 Welcome Back!'}</h1>
            <p>${isSignup ? "Let's make your wedding dreams come true" : "We're glad to see you again"}</p>
        </div>
        
        <div class="content">
            <h2>Hello ${name}! ${isSignup ? '🎊' : '😊'}</h2>
            
            ${isSignup ? `
            <p>Congratulations on taking the first step towards planning your perfect wedding! ${data.isVendor ? 'As a wedding vendor,' : 'As a couple,'} you now have access to India's most comprehensive wedding planning platform.</p>
            ` : `
            <p>Welcome back to BookmyFestive! We're excited to continue helping you ${data.isVendor ? 'grow your wedding business' : 'plan your perfect wedding'}.</p>
            `}

            ${data.isVendor ? `
            <div class="highlight">
                <h3>🏢 For Wedding Vendors:</h3>
                <ul>
                    <li><strong>Showcase Your Services:</strong> Create stunning vendor profiles</li>
                    <li><strong>Connect with Couples:</strong> Get inquiries from engaged couples</li>
                    <li><strong>Manage Bookings:</strong> Streamline your business operations</li>
                    <li><strong>Build Reviews:</strong> Grow your reputation with client testimonials</li>
                </ul>
            </div>
            ` : `
            <div class="highlight">
                <h3>💕 For Couples:</h3>
                <ul>
                    <li><strong>Find Vendors:</strong> Discover top-rated wedding professionals</li>
                    <li><strong>Plan Your Wedding:</strong> Use our comprehensive planning tools</li>
                    <li><strong>Get Inspiration:</strong> Browse thousands of wedding ideas</li>
                    <li><strong>Manage Budget:</strong> Keep track of your wedding expenses</li>
                </ul>
            </div>
            `}

            <div class="feature-grid">
                <div class="feature">
                    <h4>🔍 Search & Discover</h4>
                    <p>Find the perfect vendors for your special day</p>
                </div>
                <div class="feature">
                    <h4>📱 Mobile Friendly</h4>
                    <p>Plan on-the-go with our responsive platform</p>
                </div>
                <div class="feature">
                    <h4>💬 Direct Communication</h4>
                    <p>Connect directly with vendors and couples</p>
                </div>
                <div class="feature">
                    <h4>⭐ Reviews & Ratings</h4>
                    <p>Make informed decisions with real reviews</p>
                </div>
            </div>
            
            <div style="text-align: center; margin: 30px 0;">
                ${data.isVendor ? `
                <a href="https://BookmyFestive.com/dashboard" class="button">Go to Dashboard</a>
                <a href="https://BookmyFestive.com/vendors" class="button">Browse Vendors</a>
                ` : `
                <a href="https://BookmyFestive.com/vendors" class="button">Find Vendors</a>
                <a href="https://BookmyFestive.com/venues" class="button">Explore Venues</a>
                `}
            </div>
            
            ${isSignup ? `
            <p><strong>Next Steps:</strong></p>
            <ol>
                <li>Complete your profile to get personalized recommendations</li>
                <li>${data.isVendor ? 'Add your services and portfolio' : 'Start exploring vendors in your city'}</li>
                <li>Connect with our community and start planning!</li>
            </ol>
            ` : ''}
            
            <p>If you have any questions or need assistance, our support team is here to help. Feel free to reach out anytime!</p>
            
            <p>Happy ${data.isVendor ? 'Business Building' : 'Wedding Planning'}! 💕</p>
            <p><strong>The BookmyFestive Team</strong></p>
        </div>
        
        <div class="footer">
            <p>Need help? Contact <NAME_EMAIL></p>
            <p>Follow us: <a href="#">Facebook</a> | <a href="#">Instagram</a> | <a href="#">Twitter</a></p>
            <p>© 2025 BookmyFestive. All rights reserved.</p>
        </div>
    </div>
</body>
</html>`;

    const textContent = `
${isSignup ? 'Welcome to BookmyFestive!' : 'Welcome back to BookmyFestive!'}

Hello ${name}!

${isSignup ? `
Congratulations on joining BookmyFestive! ${data.isVendor ? 'As a wedding vendor,' : 'As a couple,'} you now have access to India's most comprehensive wedding planning platform.
` : `
Welcome back! We're excited to continue helping you ${data.isVendor ? 'grow your wedding business' : 'plan your perfect wedding'}.
`}

${data.isVendor ? `
For Wedding Vendors:
- Showcase Your Services: Create stunning vendor profiles
- Connect with Couples: Get inquiries from engaged couples  
- Manage Bookings: Streamline your business operations
- Build Reviews: Grow your reputation with client testimonials
` : `
For Couples:
- Find Vendors: Discover top-rated wedding professionals
- Plan Your Wedding: Use our comprehensive planning tools
- Get Inspiration: Browse thousands of wedding ideas
- Manage Budget: Keep track of your wedding expenses
`}

Key Features:
- Search & Discover: Find the perfect vendors for your special day
- Mobile Friendly: Plan on-the-go with our responsive platform
- Direct Communication: Connect directly with vendors and couples
- Reviews & Ratings: Make informed decisions with real reviews

Visit us at: https://BookmyFestive.com

${isSignup ? `
Next Steps:
1. Complete your profile to get personalized recommendations
2. ${data.isVendor ? 'Add your services and portfolio' : 'Start exploring vendors in your city'}
3. Connect with our community and start planning!
` : ''}

Need help? Contact <NAME_EMAIL>

Happy ${data.isVendor ? 'Business Building' : 'Wedding Planning'}!
The BookmyFestive Team
`;

    return { subject, htmlContent, textContent };
  }

  /**
   * Generate Vendor Launch Email Template
   */
  private generateVendorLaunchTemplate(data: VendorLaunchEmailData): EmailTemplate {
    const subject = `New Wedding Vendors Just Launched! 🎉`;

    const htmlContent = `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>New Wedding Vendors - BookmyFestive</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: linear-gradient(135deg, #a31515, #d32f2f); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
        .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }
        .vendor-card { background: white; border-radius: 8px; overflow: hidden; margin-bottom: 20px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); }
        .vendor-image { width: 100%; height: 150px; object-fit: cover; }
        .vendor-content { padding: 20px; }
        .vendor-name { font-size: 18px; font-weight: bold; color: #a31515; margin-bottom: 5px; }
        .vendor-category { display: inline-block; padding: 3px 8px; border-radius: 12px; font-size: 12px; margin-bottom: 10px; background: #e8f5e9; color: #2e7d32; }
        .vendor-city { font-size: 14px; color: #666; margin-bottom: 10px; }
        .vendor-discount { background: #a31515; color: white; padding: 5px 10px; border-radius: 20px; font-weight: bold; margin-bottom: 15px; display: inline-block; }
        .vendor-button { display: inline-block; background: #a31515; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px; font-size: 14px; }
        .footer { text-align: center; margin-top: 20px; color: #666; font-size: 14px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎉 New Wedding Vendors Launched!</h1>
            <p>Discover Fresh Talent for Your Special Day</p>
        </div>
        <div class="content">
            <h2>Hello ${data.userName}!</h2>
            <p>We're excited to announce that new wedding vendors have just joined BookmyFestive. Check them out:</p>

            ${data.newVendors.map(vendor => `
            <div class="vendor-card">
                ${vendor.image ? `<img src="${vendor.image}" alt="${vendor.name}" class="vendor-image">` : ''}
                <div class="vendor-content">
                    <div class="vendor-name">${vendor.name}</div>
                    <div class="vendor-category">${vendor.category}</div>
                    <div class="vendor-city">📍 ${vendor.city}</div>
                    ${vendor.discount ? `<div class="vendor-discount">${vendor.discount} OFF</div>` : ''}
                    <a href="${vendor.url}" class="vendor-button">View Profile</a>
                </div>
            </div>
            `).join('')}

            <p>These vendors are eager to make your wedding dreams come true. Don't miss out on the opportunity to work with fresh talent!</p>
            <p>Happy Wedding Planning!</p>
        </div>
        <div class="footer">
            <p>© 2025 BookmyFestive. All rights reserved.</p>
            <p>You're receiving this email because you opted in for vendor updates.</p>
            <p><a href="https://BookmyFestive.com/preferences">Manage Preferences</a> | <a href="https://BookmyFestive.com/unsubscribe">Unsubscribe</a></p>
        </div>
    </div>
</body>
</html>`;

    const textContent = `
New Wedding Vendors Just Launched! 🎉

Hello ${data.userName}!

We're excited to announce that new wedding vendors have just joined BookmyFestive. Check them out:

${data.newVendors.map(vendor => `
${vendor.name}
Category: ${vendor.category}
Location: ${vendor.city}
${vendor.discount ? `Special Offer: ${vendor.discount} OFF` : ''}
View Profile: ${vendor.url}
`).join('\n')}

These vendors are eager to make your wedding dreams come true. Don't miss out on the opportunity to work with fresh talent!

Happy Wedding Planning!

© 2025 BookmyFestive. All rights reserved.
You're receiving this email because you opted in for vendor updates.
Manage Preferences: https://BookmyFestive.com/preferences
Unsubscribe: https://BookmyFestive.com/unsubscribe
`;

    return { subject, htmlContent, textContent };
  }

  /**
   * Generate Availability Check Email Template
   */
  private generateAvailabilityCheckTemplate(data: AvailabilityEmailData): EmailTemplate {
    const subject = `Availability Check: ${data.entityName}`;

    const htmlContent = `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Availability Check - BookmyFestive</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: linear-gradient(135deg, #a31515, #d32f2f); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
        .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }
        .status-box { background: white; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #a31515; }
        .status-available { border-left-color: #4caf50; }
        .status-unavailable { border-left-color: #f44336; }
        .status-partial { border-left-color: #ff9800; }
        .button { display: inline-block; background: #a31515; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; margin: 20px 0; }
        .footer { text-align: center; margin-top: 20px; color: #666; font-size: 14px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📅 Availability Check Result</h1>
            <p>${data.entityName}</p>
        </div>
        <div class="content">
            <h2>Hello ${data.userName}!</h2>
            <p>Here's the availability status for your requested date:</p>

            <div class="status-box status-${data.status}">
                <h3>Availability Status</h3>
                <p><strong>${data.entityType === 'vendor' ? 'Vendor' : 'Venue'}:</strong> ${data.entityName}</p>
                <p><strong>Requested Date:</strong> ${data.requestedDate}</p>
                <p><strong>Status:</strong> 
                    <span style="color: ${
                        data.status === 'available' ? '#4caf50' :
                        data.status === 'unavailable' ? '#f44336' : '#ff9800'
                    }">
                        ${data.status.toUpperCase()}
                    </span>
                </p>
                
                ${data.status === 'partially_available' && data.alternativeDates && data.alternativeDates.length > 0 ? `
                <p><strong>Alternative Dates Available:</strong></p>
                <ul>
                    ${data.alternativeDates.map(date => `<li>${date}</li>`).join('')}
                </ul>
                ` : ''}
            </div>

            <div style="text-align: center;">
                <a href="${data.url}" class="button">View ${data.entityType === 'vendor' ? 'Vendor' : 'Venue'} Profile</a>
            </div>

            <p>If you have any questions or need to make changes to your request, please contact us or the ${data.entityType} directly.</p>
            <p>Thank you for using BookmyFestive!</p>
        </div>
        <div class="footer">
            <p>© 2025 BookmyFestive. All rights reserved.</p>
            <p>Making your Celebration dreams come true! 💕</p>
        </div>
    </div>
</body>
</html>`;

    const textContent = `
Availability Check: ${data.entityName}

Hello ${data.userName}!

Here's the availability status for your requested date:

Availability Status:
${data.entityType === 'vendor' ? 'Vendor' : 'Venue'}: ${data.entityName}
Requested Date: ${data.requestedDate}
Status: ${data.status.toUpperCase()}

${data.status === 'partially_available' && data.alternativeDates && data.alternativeDates.length > 0 ? `
Alternative Dates Available:
${data.alternativeDates.map(date => `- ${date}`).join('\n')}
` : ''}

View ${data.entityType === 'vendor' ? 'Vendor' : 'Venue'} Profile: ${data.url}

If you have any questions or need to make changes to your request, please contact us or the ${data.entityType} directly.

Thank you for using BookmyFestive!

© 2025 BookmyFestive. All rights reserved.
Making your Celebration dreams come true! 💕
`;

    return { subject, htmlContent, textContent };
  }

  /**
   * Send Order Confirmation Email
   */
  async sendOrderConfirmationEmail(data: OrderEmailData): Promise<boolean> {
    try {
      console.log('📧 Sending order confirmation email to:', data.email);

      // Generate the email template first
      const template = this.generateOrderConfirmationTemplate(data);

      // First, try to send the actual email
      const emailResult = await this.sendActualEmail(
        data.email,
        template.subject,
        template.htmlContent,
        template.textContent
      );

      // Then log the email event
      try {
        const result = await client.graphql({
          query: createEmailLog,
          variables: {
            input: {
              recipient: data.email.trim(),
              type: 'ORDER_CONFIRMATION',
              subject: template.subject,
              message: `Order confirmation email sent to ${data.userName}`,
              status: emailResult.success ? 'SENT' : 'FAILED',
              messageId: emailResult.messageId || 'N/A',
              metadata: JSON.stringify({
                orderNumber: data.orderNumber,
                orderDate: data.orderDate,
                totalAmount: data.totalAmount,
                paymentMethod: data.paymentMethod,
                itemCount: data.items.length
              })
            }
          },
          authMode: 'userPool'
        });

        if ((result.data as any)?.createEmailLog) {
          console.log('✅ Order confirmation email logged successfully');
        }
      } catch (logError: any) {
        console.warn('⚠️ Failed to log order confirmation email:', logError.message);
      }

      if (emailResult.success) {
        console.log('✅ Order confirmation email sent successfully');
        return true;
      } else {
        console.warn('⚠️ Order confirmation email sending failed:', emailResult.error);
        return false;
      }
    } catch (error: any) {
      console.error('Error sending order confirmation email:', error);
      return false;
    }
  }

  /**
   * Generate Order Confirmation Email Template
   */
  private generateOrderConfirmationTemplate(data: OrderEmailData): EmailTemplate {
    const subject = `Order Confirmation - ${data.orderNumber}`;
    
    const htmlContent = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Order Confirmation - BookmyFestive</title>
        <style>
          body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
            line-height: 1.6; 
            color: #333; 
            margin: 0; 
            padding: 0; 
            background-color: #f8f9fa;
          }
          .container { 
            max-width: 600px; 
            margin: 0 auto; 
            background-color: #ffffff;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
          }
          .header { 
            background: linear-gradient(135deg, #a31515, #d32f2f); 
            color: white; 
            padding: 30px; 
            text-align: center; 
          }
          .header h1 { 
            margin: 0; 
            font-size: 28px; 
            font-weight: 600;
          }
          .header p { 
            margin: 10px 0 0 0; 
            font-size: 16px; 
            opacity: 0.9;
          }
          .content { 
            padding: 30px; 
          }
          .order-summary { 
            background: #f8f9fa; 
            padding: 20px; 
            border-radius: 8px; 
            margin: 20px 0; 
            border-left: 4px solid #a31515;
          }
          .order-number { 
            background: #e9ecef; 
            padding: 12px; 
            border-radius: 6px; 
            font-family: 'Courier New', monospace; 
            font-size: 16px; 
            font-weight: bold; 
            text-align: center; 
            margin: 15px 0; 
            color: #495057;
          }
          .items-table { 
            width: 100%; 
            border-collapse: collapse; 
            margin: 20px 0; 
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
          }
          .items-table th, .items-table td { 
            padding: 15px; 
            text-align: left; 
            border-bottom: 1px solid #e9ecef; 
          }
          .items-table th { 
            background: #f8f9fa; 
            font-weight: 600;
            color: #495057;
          }
          .items-table tr:last-child td { 
            border-bottom: none; 
          }
          .total-section { 
            background: #f8f9fa; 
            padding: 20px; 
            border-radius: 8px; 
            margin: 20px 0; 
            text-align: right;
          }
          .total-amount { 
            font-size: 24px; 
            font-weight: bold; 
            color: #a31515; 
            margin: 10px 0;
          }
          .delivery-info { 
            background: #e8f5e9; 
            padding: 20px; 
            border-radius: 8px; 
            margin: 20px 0; 
            border-left: 4px solid #4caf50;
          }
          .footer { 
            background: #f8f9fa; 
            padding: 25px; 
            text-align: center; 
            color: #6c757d; 
            font-size: 14px; 
            border-top: 1px solid #e9ecef;
          }
          .contact-info { 
            margin: 20px 0; 
            padding: 20px; 
            background: #fff3cd; 
            border-radius: 8px; 
            border-left: 4px solid #ffc107;
          }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>🎉 Order Confirmed!</h1>
            <p>Thank you for your order, ${data.userName}!</p>
          </div>
          
          <div class="content">
            <div class="order-summary">
              <h2 style="margin-top: 0; color: #a31515;">Order Summary</h2>
              <p><strong>Order Number:</strong></p>
              <div class="order-number">${data.orderNumber}</div>
              <p><strong>Order Date:</strong> ${data.orderDate}</p>
              <p><strong>Payment Method:</strong> ${data.paymentMethod}</p>
            </div>
            
            <div class="delivery-info">
              <h3 style="margin-top: 0; color: #2e7d32;">📦 Delivery Information</h3>
              <p><strong>Estimated Delivery:</strong> ${data.estimatedDelivery}</p>
              <p>We'll keep you updated on your order status via email and SMS.</p>
            </div>
            
            <h3 style="color: #495057;">🛍️ Items Ordered</h3>
            <table class="items-table">
              <thead>
                <tr>
                  <th>Item</th>
                  <th>Quantity</th>
                  <th>Price</th>
                </tr>
              </thead>
              <tbody>
                ${data.items.map(item => `
                  <tr>
                    <td><strong>${item.name}</strong></td>
                    <td>${item.quantity}</td>
                    <td>${item.price}</td>
                  </tr>
                `).join('')}
              </tbody>
            </table>
            
            <div class="total-section">
              <div class="total-amount">Total Amount: ${data.totalAmount}</div>
            </div>
            
            <div class="contact-info">
              <h4 style="margin-top: 0; color: #856404;">📞 Need Help?</h4>
              <p>If you have any questions about your order, please contact our support team:</p>
              <p><strong>Email:</strong> <EMAIL></p>
              <p><strong>Phone:</strong> +91 8148376909</p>
              <p><strong>Hours:</strong> Monday - Saturday, 9:00 AM - 7:00 PM IST</p>
            </div>
          </div>
          
          <div class="footer">
            <p><strong>Thank you for choosing BookmyFestive!</strong></p>
            <p>Making your Celebration dreams come true! 💕</p>
            <p>© 2025 BookmyFestive. All rights reserved.</p>
          </div>
        </div>
      </body>
      </html>
    `;
    
    const textContent = `
      🎉 Order Confirmation - ${data.orderNumber}
      
      Thank you for your order, ${data.userName}!
      
      📋 ORDER SUMMARY:
      Order Number: ${data.orderNumber}
      Order Date: ${data.orderDate}
      Payment Method: ${data.paymentMethod}
      Estimated Delivery: ${data.estimatedDelivery}
      
      🛍️ ITEMS ORDERED:
      ${data.items.map(item => `• ${item.name} (Qty: ${item.quantity}) - ${item.price}`).join('\n')}
      
      💰 TOTAL AMOUNT: ${data.totalAmount}
      
      📦 DELIVERY:
      We'll keep you updated on your order status via email and SMS.
      
      📞 NEED HELP?
      If you have any questions about your order, please contact our support team:
      Email: <EMAIL>
      Phone: +91 8148376909
      Hours: Monday - Saturday, 9:00 AM - 7:00 PM IST
      
      Thank you for choosing BookmyFestive!
      Making your Celebration dreams come true! 💕
      
      © 2025 BookmyFestive. All rights reserved.
    `;
    
    return { subject, htmlContent, textContent };
  }
}

export const emailService = new EmailService();
export default emailService;
