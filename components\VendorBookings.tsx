"use client"

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { 
  Calendar, 
  Clock, 
  Users, 
  Phone, 
  Mail,
  MessageCircle,
  Star,
  AlertCircle,
  CheckCircle,
  XCircle,
  Loader2,
  Edit,
  DollarSign,
  FileText
} from 'lucide-react'
import { useAuth } from '@/contexts/AuthContext'
import { format } from 'date-fns'
import { showToast } from '@/lib/toast'

// Import debug utility for development
if (process.env.NODE_ENV === 'development') {
  import('@/lib/debug/bookingDebug').then(({ BookingDebug }) => {
    if (typeof window !== 'undefined') {
      (window as any).BookingDebug = BookingDebug
    }
  })
}

interface Booking {
  id: string
  customerId: string
  customerName: string
  customerEmail: string
  customerPhone: string
  entityName: string
  entityType: string
  eventDate: string
  eventTime: string
  guestCount: number
  eventType: string
  status: string
  priority: string
  budget: string
  specialRequests: string
  vendorNotes: string
  estimatedCost: string
  notes: string
  createdAt: string
  updatedAt: string
}

export default function VendorBookings() {
  const { user } = useAuth()
  const [bookings, setBookings] = useState<Booking[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [selectedStatus, setSelectedStatus] = useState<string>('all')
  const [selectedBooking, setSelectedBooking] = useState<Booking | null>(null)
  const [updating, setUpdating] = useState(false)
  
  // Update form state
  const [updateForm, setUpdateForm] = useState({
    status: '',
    vendorNotes: '',
    estimatedCost: '',
    priority: ''
  })

  useEffect(() => {
    if (user) {
      loadBookings()
    }
  }, [user])

  const loadBookings = async () => {
    try {
      setLoading(true)
      setError(null)

      // Direct GraphQL query instead of using BookingService to avoid caching issues
      const { generateClient } = await import('@aws-amplify/api')
      const { listBookings } = await import('@/src/graphql/queries')

      const client = generateClient()
      const vendorId = user?.userId

      console.log('🔍 Debug - VendorBookings DIRECT QUERY user info:', {
        userId: user?.userId,
        username: user?.username,
        hasUserId: !!user?.userId
      })

      if (!vendorId) {
        throw new Error('User ID not available for vendor query')
      }

      // Direct GraphQL query with vendor filter
      const filter = {
        or: [
          { vendorId: { eq: vendorId } },
          { entityId: { eq: vendorId } }
        ],
        ...(selectedStatus !== 'all' && { status: { eq: selectedStatus.toUpperCase() } })
      }

      console.log('🔍 Debug - VendorBookings DIRECT FILTER:', filter)

      const result = await client.graphql({
        query: listBookings,
        variables: {
          filter: filter,
          limit: 50,
          sortDirection: 'DESC'
        },
        authMode: 'userPool'
      })

      console.log('🔍 Debug - VendorBookings DIRECT RESULT:', {
        itemCount: result.data.listBookings.items.length,
        items: result.data.listBookings.items.map(item => ({
          id: item.id,
          customerId: item.customerId,
          vendorId: item.vendorId,
          entityId: item.entityId,
          entityName: item.entityName
        }))
      })

      const bookingsResult = {
        success: true,
        bookings: result.data.listBookings.items
      }

      if (!bookingsResult.success) {
        throw new Error('Failed to load bookings')
      }

      setBookings(bookingsResult.bookings || [])
    } catch (err) {
      console.error('Error loading bookings:', err)
      setError(err.message)
    } finally {
      setLoading(false)
    }
  }

  const handleUpdateBooking = async () => {
    if (!selectedBooking) return

    try {
      setUpdating(true)

      // Import the booking service
      const { BookingService } = await import('@/lib/services/bookingService')

      const updateData = {
        bookingId: selectedBooking.id,
        status: updateForm.status,
        vendorNotes: updateForm.vendorNotes,
        estimatedCost: updateForm.estimatedCost,
        priority: updateForm.priority
      }

      const result = await BookingService.updateBooking(updateData)

      if (!result.success) {
        throw new Error(result.message || 'Failed to update booking')
      }

      // Update local state
      setBookings(prev => prev.map(booking =>
        booking.id === selectedBooking.id ? result.booking : booking
      ))

      setSelectedBooking(null)
      setUpdateForm({ status: '', vendorNotes: '', estimatedCost: '', priority: '' })

    } catch (err) {
      console.error('Error updating booking:', err)
      showToast.error(`Failed to update booking: ${err.message}`)
    } finally {
      setUpdating(false)
    }
  }

  const openUpdateDialog = (booking: Booking) => {
    setSelectedBooking(booking)
    setUpdateForm({
      status: booking.status,
      vendorNotes: booking.vendorNotes || '',
      estimatedCost: booking.estimatedCost || '',
      priority: booking.priority || 'MEDIUM'
    })
  }

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'pending': return 'bg-yellow-100 text-yellow-800'
      case 'confirmed': return 'bg-blue-100 text-blue-800'
      case 'in_progress': return 'bg-purple-100 text-purple-800'
      case 'completed': return 'bg-green-100 text-green-800'
      case 'cancelled': return 'bg-red-100 text-red-800'
      case 'rejected': return 'bg-gray-100 text-gray-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status.toLowerCase()) {
      case 'pending': return <Clock className="h-4 w-4" />
      case 'confirmed': return <CheckCircle className="h-4 w-4" />
      case 'in_progress': return <Loader2 className="h-4 w-4 animate-spin" />
      case 'completed': return <CheckCircle className="h-4 w-4" />
      case 'cancelled': return <XCircle className="h-4 w-4" />
      case 'rejected': return <XCircle className="h-4 w-4" />
      default: return <AlertCircle className="h-4 w-4" />
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority?.toLowerCase()) {
      case 'high': return 'bg-red-100 text-red-800'
      case 'medium': return 'bg-yellow-100 text-yellow-800'
      case 'low': return 'bg-green-100 text-green-800'
      case 'urgent': return 'bg-red-200 text-red-900'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const filteredBookings = bookings.filter(booking => 
    selectedStatus === 'all' || booking.status.toLowerCase() === selectedStatus.toLowerCase()
  )

  const bookingCounts = {
    all: bookings.length,
    pending: bookings.filter(b => b.status.toLowerCase() === 'pending').length,
    confirmed: bookings.filter(b => b.status.toLowerCase() === 'confirmed').length,
    completed: bookings.filter(b => b.status.toLowerCase() === 'completed').length,
    cancelled: bookings.filter(b => b.status.toLowerCase() === 'cancelled').length
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-primary" />
          <p>Loading bookings...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
        <p className="text-red-600 mb-4">{error}</p>
        <Button onClick={loadBookings}>Try Again</Button>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold">Booking Requests</h2>
        <Button onClick={loadBookings} variant="outline" size="sm">
          <Loader2 className="h-4 w-4 mr-2" />
          Refresh
        </Button>
      </div>

      {/* Status Tabs */}
      <Tabs value={selectedStatus} onValueChange={setSelectedStatus}>
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="all">All ({bookingCounts.all})</TabsTrigger>
          <TabsTrigger value="pending">Pending ({bookingCounts.pending})</TabsTrigger>
          <TabsTrigger value="confirmed">Confirmed ({bookingCounts.confirmed})</TabsTrigger>
          <TabsTrigger value="completed">Completed ({bookingCounts.completed})</TabsTrigger>
          <TabsTrigger value="cancelled">Cancelled ({bookingCounts.cancelled})</TabsTrigger>
        </TabsList>

        <TabsContent value={selectedStatus} className="mt-6">
          {filteredBookings.length === 0 ? (
            <Card>
              <CardContent className="py-12 text-center">
                <Calendar className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  No booking requests
                </h3>
                <p className="text-gray-600">
                  {selectedStatus === 'all' 
                    ? "You haven't received any booking requests yet." 
                    : `No ${selectedStatus} bookings found.`
                  }
                </p>
              </CardContent>
            </Card>
          ) : (
            <div className="grid gap-6">
              {filteredBookings.map((booking) => (
                <Card key={booking.id} className="hover:shadow-lg transition-shadow">
                  <CardHeader>
                    <div className="flex items-start justify-between">
                      <div>
                        <CardTitle className="text-xl">{booking.customerName}</CardTitle>
                        <p className="text-gray-600">{booking.eventType} Event</p>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge className={getPriorityColor(booking.priority)}>
                          {booking.priority}
                        </Badge>
                        <Badge className={getStatusColor(booking.status)}>
                          {getStatusIcon(booking.status)}
                          <span className="ml-1 capitalize">{booking.status.replace('_', ' ')}</span>
                        </Badge>
                      </div>
                    </div>
                  </CardHeader>
                  
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-4">
                      <div className="flex items-center gap-2">
                        <Calendar className="h-4 w-4 text-gray-500" />
                        <span className="text-sm">
                          {format(new Date(booking.eventDate), 'PPP')}
                        </span>
                      </div>
                      
                      <div className="flex items-center gap-2">
                        <Clock className="h-4 w-4 text-gray-500" />
                        <span className="text-sm">{booking.eventTime}</span>
                      </div>
                      
                      <div className="flex items-center gap-2">
                        <Users className="h-4 w-4 text-gray-500" />
                        <span className="text-sm">{booking.guestCount} guests</span>
                      </div>
                      
                      <div className="flex items-center gap-2">
                        <Mail className="h-4 w-4 text-gray-500" />
                        <span className="text-sm">{booking.customerEmail}</span>
                      </div>
                      
                      {booking.customerPhone && (
                        <div className="flex items-center gap-2">
                          <Phone className="h-4 w-4 text-gray-500" />
                          <span className="text-sm">{booking.customerPhone}</span>
                        </div>
                      )}
                      
                      {booking.budget && (
                        <div className="flex items-center gap-2">
                          <DollarSign className="h-4 w-4 text-gray-500" />
                          <span className="text-sm">Budget: {booking.budget}</span>
                        </div>
                      )}
                    </div>

                    {booking.specialRequests && (
                      <div className="mb-4">
                        <h4 className="text-sm font-medium text-gray-900 mb-1">Special Requests:</h4>
                        <p className="text-sm text-gray-600">{booking.specialRequests}</p>
                      </div>
                    )}

                    {booking.vendorNotes && (
                      <div className="mb-4 p-3 bg-blue-50 rounded-lg">
                        <h4 className="text-sm font-medium text-blue-900 mb-1">Your Notes:</h4>
                        <p className="text-sm text-blue-800">{booking.vendorNotes}</p>
                      </div>
                    )}

                    {booking.estimatedCost && (
                      <div className="mb-4 p-3 bg-green-50 rounded-lg">
                        <h4 className="text-sm font-medium text-green-900 mb-1">Estimated Cost:</h4>
                        <p className="text-sm text-green-800 font-semibold">{booking.estimatedCost}</p>
                      </div>
                    )}

                    <div className="flex items-center justify-between pt-4 border-t">
                      <div className="text-xs text-gray-500">
                        Received on {format(new Date(booking.createdAt), 'PPp')}
                      </div>
                      
                      <div className="flex items-center gap-2">
                        <Button 
                          variant="outline" 
                          size="sm"
                          onClick={() => window.open(`tel:${booking.customerPhone}`, '_self')}
                          disabled={!booking.customerPhone}
                        >
                          <Phone className="h-4 w-4 mr-1" />
                          Call
                        </Button>
                        
                        <Button 
                          variant="outline" 
                          size="sm"
                          onClick={() => window.open(`mailto:${booking.customerEmail}`, '_self')}
                        >
                          <Mail className="h-4 w-4 mr-1" />
                          Email
                        </Button>
                        
                        <Dialog>
                          <DialogTrigger asChild>
                            <Button 
                              size="sm"
                              onClick={() => openUpdateDialog(booking)}
                            >
                              <Edit className="h-4 w-4 mr-1" />
                              Update
                            </Button>
                          </DialogTrigger>
                          
                          <DialogContent className="max-w-md">
                            <DialogHeader>
                              <DialogTitle>Update Booking</DialogTitle>
                            </DialogHeader>
                            
                            <div className="space-y-4">
                              <div>
                                <Label htmlFor="status">Status</Label>
                                <Select 
                                  value={updateForm.status} 
                                  onValueChange={(value) => setUpdateForm(prev => ({ ...prev, status: value }))}
                                >
                                  <SelectTrigger>
                                    <SelectValue placeholder="Select status" />
                                  </SelectTrigger>
                                  <SelectContent>
                                    <SelectItem value="PENDING">Pending</SelectItem>
                                    <SelectItem value="CONFIRMED">Confirmed</SelectItem>
                                    <SelectItem value="IN_PROGRESS">In Progress</SelectItem>
                                    <SelectItem value="COMPLETED">Completed</SelectItem>
                                    <SelectItem value="CANCELLED">Cancelled</SelectItem>
                                    <SelectItem value="REJECTED">Rejected</SelectItem>
                                  </SelectContent>
                                </Select>
                              </div>
                              
                              <div>
                                <Label htmlFor="priority">Priority</Label>
                                <Select 
                                  value={updateForm.priority} 
                                  onValueChange={(value) => setUpdateForm(prev => ({ ...prev, priority: value }))}
                                >
                                  <SelectTrigger>
                                    <SelectValue placeholder="Select priority" />
                                  </SelectTrigger>
                                  <SelectContent>
                                    <SelectItem value="LOW">Low</SelectItem>
                                    <SelectItem value="MEDIUM">Medium</SelectItem>
                                    <SelectItem value="HIGH">High</SelectItem>
                                    <SelectItem value="URGENT">Urgent</SelectItem>
                                  </SelectContent>
                                </Select>
                              </div>
                              
                              <div>
                                <Label htmlFor="estimatedCost">Estimated Cost</Label>
                                <Input
                                  id="estimatedCost"
                                  placeholder="e.g., ₹50,000"
                                  value={updateForm.estimatedCost}
                                  onChange={(e) => setUpdateForm(prev => ({ ...prev, estimatedCost: e.target.value }))}
                                />
                              </div>
                              
                              <div>
                                <Label htmlFor="vendorNotes">Notes for Customer</Label>
                                <Textarea
                                  id="vendorNotes"
                                  placeholder="Add notes for the customer..."
                                  value={updateForm.vendorNotes}
                                  onChange={(e) => setUpdateForm(prev => ({ ...prev, vendorNotes: e.target.value }))}
                                  rows={3}
                                />
                              </div>
                              
                              <div className="flex gap-2 pt-4">
                                <Button 
                                  onClick={handleUpdateBooking}
                                  disabled={updating}
                                  className="flex-1"
                                >
                                  {updating ? (
                                    <>
                                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                                      Updating...
                                    </>
                                  ) : (
                                    'Update Booking'
                                  )}
                                </Button>
                              </div>
                            </div>
                          </DialogContent>
                        </Dialog>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  )
}
