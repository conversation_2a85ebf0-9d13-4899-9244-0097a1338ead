import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Import language resources
import en from './locales/en.json';
import hi from './locales/hi.json';
import ta from './locales/ta.json';
import te from './locales/te.json';
import kn from './locales/kn.json';
import ml from './locales/ml.json';
import gu from './locales/gu.json';
import mr from './locales/mr.json';
import bn from './locales/bn.json';

// Language detection and persistence
const LANGUAGE_DETECTOR = {
  type: 'languageDetector' as const,
  async: true,
  detect: async (callback: (lng: string) => void) => {
    try {
      // Get saved language from AsyncStorage
      const savedLanguage = await AsyncStorage.getItem('user-language');
      if (savedLanguage) {
        callback(savedLanguage);
        return;
      }
      
      // Default to English if no saved language
      callback('en');
    } catch (error) {
      console.error('Error detecting language:', error);
      callback('en');
    }
  },
  init: () => {},
  cacheUserLanguage: async (lng: string) => {
    try {
      await AsyncStorage.setItem('user-language', lng);
    } catch (error) {
      console.error('Error saving language:', error);
    }
  },
};

// Initialize i18next
i18n
  .use(LANGUAGE_DETECTOR)
  .use(initReactI18next)
  .init({
    compatibilityJSON: 'v3', // For React Native compatibility
    fallbackLng: 'en',
    debug: __DEV__, // Enable debug in development
    
    // Language resources
    resources: {
      en: { translation: en },
      hi: { translation: hi },
      ta: { translation: ta },
      te: { translation: te },
      kn: { translation: kn },
      ml: { translation: ml },
      gu: { translation: gu },
      mr: { translation: mr },
      bn: { translation: bn },
    },
    
    // Interpolation options
    interpolation: {
      escapeValue: false, // React already escapes values
      formatSeparator: ',',
      format: (value, format) => {
        if (format === 'currency') {
          return new Intl.NumberFormat('en-IN', {
            style: 'currency',
            currency: 'INR',
          }).format(value);
        }
        if (format === 'number') {
          return new Intl.NumberFormat('en-IN').format(value);
        }
        if (format === 'date') {
          return new Date(value).toLocaleDateString();
        }
        if (format === 'datetime') {
          return new Date(value).toLocaleString();
        }
        return value;
      },
    },
    
    // React options
    react: {
      useSuspense: false, // Disable suspense for React Native
    },
  });

export default i18n;

// Export language options for UI
export const SUPPORTED_LANGUAGES = [
  { code: 'en', name: 'English', nativeName: 'English' },
  { code: 'hi', name: 'Hindi', nativeName: 'हिन्दी' },
  { code: 'ta', name: 'Tamil', nativeName: 'தமிழ்' },
  { code: 'te', name: 'Telugu', nativeName: 'తెలుగు' },
  { code: 'kn', name: 'Kannada', nativeName: 'ಕನ್ನಡ' },
  { code: 'ml', name: 'Malayalam', nativeName: 'മലയാളം' },
  { code: 'gu', name: 'Gujarati', nativeName: 'ગુજરાતી' },
  { code: 'mr', name: 'Marathi', nativeName: 'मराठी' },
  { code: 'bn', name: 'Bengali', nativeName: 'বাংলা' },
];

// Helper functions
export const getCurrentLanguage = () => i18n.language;

export const changeLanguage = async (languageCode: string) => {
  try {
    await i18n.changeLanguage(languageCode);
    await AsyncStorage.setItem('user-language', languageCode);
    return true;
  } catch (error) {
    console.error('Error changing language:', error);
    return false;
  }
};

export const isRTL = (languageCode?: string) => {
  const lang = languageCode || i18n.language;
  // Add RTL languages if needed (Arabic, Urdu, etc.)
  const rtlLanguages = ['ar', 'ur', 'fa'];
  return rtlLanguages.includes(lang);
};

// Translation helper with type safety
export const t = (key: string, options?: any) => {
  return i18n.t(key, options);
};

// Namespace-specific translation helpers
export const tCommon = (key: string, options?: any) => {
  return i18n.t(`common.${key}`, options);
};

export const tAuth = (key: string, options?: any) => {
  return i18n.t(`auth.${key}`, options);
};

export const tNavigation = (key: string, options?: any) => {
  return i18n.t(`navigation.${key}`, options);
};

export const tWedding = (key: string, options?: any) => {
  return i18n.t(`wedding.${key}`, options);
};

export const tVendor = (key: string, options?: any) => {
  return i18n.t(`vendor.${key}`, options);
};

export const tShop = (key: string, options?: any) => {
  return i18n.t(`shop.${key}`, options);
};

export const tBooking = (key: string, options?: any) => {
  return i18n.t(`booking.${key}`, options);
};

export const tPayment = (key: string, options?: any) => {
  return i18n.t(`payment.${key}`, options);
};

export const tNotification = (key: string, options?: any) => {
  return i18n.t(`notification.${key}`, options);
};

export const tError = (key: string, options?: any) => {
  return i18n.t(`error.${key}`, options);
};

export const tSuccess = (key: string, options?: any) => {
  return i18n.t(`success.${key}`, options);
};

// Format helpers
export const formatCurrency = (amount: number, languageCode?: string) => {
  const lang = languageCode || i18n.language;
  return new Intl.NumberFormat(lang === 'en' ? 'en-IN' : lang, {
    style: 'currency',
    currency: 'INR',
  }).format(amount);
};

export const formatNumber = (number: number, languageCode?: string) => {
  const lang = languageCode || i18n.language;
  return new Intl.NumberFormat(lang === 'en' ? 'en-IN' : lang).format(number);
};

export const formatDate = (date: Date | string, languageCode?: string) => {
  const lang = languageCode || i18n.language;
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  return dateObj.toLocaleDateString(lang === 'en' ? 'en-IN' : lang);
};

export const formatDateTime = (date: Date | string, languageCode?: string) => {
  const lang = languageCode || i18n.language;
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  return dateObj.toLocaleString(lang === 'en' ? 'en-IN' : lang);
};
