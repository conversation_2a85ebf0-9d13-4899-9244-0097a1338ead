import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
} from 'react-native';
import { useTheme } from '../../providers/ThemeProvider';
import { Header } from '../../components/Header';
import { Card } from '../../components/ui';

export default function PrivacyScreen() {
  const { theme } = useTheme();

  const privacySections = [
    {
      title: 'Information We Collect',
      content: `We collect information you provide directly to us, such as when you create an account, make a purchase, or contact us for support. This may include your name, email address, phone number, and payment information.`
    },
    {
      title: 'How We Use Your Information',
      content: `We use the information we collect to provide, maintain, and improve our services, process transactions, send you technical notices and support messages, and communicate with you about products, services, and events.`
    },
    {
      title: 'Information Sharing',
      content: `We do not sell, trade, or otherwise transfer your personal information to third parties without your consent, except as described in this privacy policy or as required by law.`
    },
    {
      title: 'Data Security',
      content: `We implement appropriate security measures to protect your personal information against unauthorized access, alteration, disclosure, or destruction. However, no method of transmission over the internet is 100% secure.`
    },
    {
      title: 'Cookies and Tracking',
      content: `We use cookies and similar tracking technologies to collect and use personal information about you. You can control cookies through your browser settings.`
    },
    {
      title: 'Your Rights',
      content: `You have the right to access, update, or delete your personal information. You may also opt out of certain communications from us. Contact us to exercise these rights.`
    },
    {
      title: 'Changes to This Policy',
      content: `We may update this privacy policy from time to time. We will notify you of any changes by posting the new privacy policy on this page and updating the "Last Updated" date.`
    },
    {
      title: 'Contact Us',
      content: `If you have any questions about this privacy policy, please contact <NAME_EMAIL> or through our support channels.`
    }
  ];

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <Header title="Privacy Policy" showBack />
      
      <ScrollView 
        style={styles.content}
        showsVerticalScrollIndicator={false}
      >
        <Card style={styles.headerCard}>
          <Text style={[styles.title, { color: theme.colors.text }]}>
            Privacy Policy
          </Text>
          <Text style={[styles.lastUpdated, { color: theme.colors.textSecondary }]}>
            Last Updated: January 25, 2024
          </Text>
          <Text style={[styles.intro, { color: theme.colors.textSecondary }]}>
            At BookmyFestive, we are committed to protecting your privacy and ensuring the security of your personal information. This privacy policy explains how we collect, use, and safeguard your data when you use our wedding planning platform.
          </Text>
        </Card>

        {privacySections.map((section, index) => (
          <Card key={index} style={styles.sectionCard}>
            <Text style={[styles.sectionTitle, { color: theme.colors.primary }]}>
              {section.title}
            </Text>
            <Text style={[styles.sectionContent, { color: theme.colors.text }]}>
              {section.content}
            </Text>
          </Card>
        ))}

        <Card style={styles.footerCard}>
          <Text style={[styles.footerTitle, { color: theme.colors.primary }]}>
            Need Help?
          </Text>
          <Text style={[styles.footerContent, { color: theme.colors.textSecondary }]}>
            If you have any questions about our privacy practices or this policy, please don't hesitate to contact our support team. We're here to help ensure your privacy is protected.
          </Text>
        </Card>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    paddingHorizontal: 16,
  },
  headerCard: {
    marginVertical: 16,
    padding: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  lastUpdated: {
    fontSize: 14,
    marginBottom: 16,
    fontStyle: 'italic',
  },
  intro: {
    fontSize: 16,
    lineHeight: 24,
  },
  sectionCard: {
    marginBottom: 16,
    padding: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 12,
  },
  sectionContent: {
    fontSize: 15,
    lineHeight: 22,
  },
  footerCard: {
    marginBottom: 32,
    padding: 20,
  },
  footerTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 12,
  },
  footerContent: {
    fontSize: 15,
    lineHeight: 22,
  },
});
