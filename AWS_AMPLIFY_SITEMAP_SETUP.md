# AWS Amplify Sitemap Setup Guide

## 📍 Current Status
Your sitemap is already configured and will work automatically in AWS Amplify.

## 🚀 How Sitemap Works in AWS Amplify

### 1. **Automatic Generation**
- Next.js automatically generates sitemap.xml from `/app/sitemap.ts`
- Available at: `https://yourdomain.com/sitemap.xml`
- No additional configuration needed

### 2. **File Locations**
```
/app/sitemap.ts          ✅ Already exists
/app/robots.ts           ✅ Already exists
/amplify.yml             ✅ Created (build config)
```

### 3. **Amplify Build Process**
```yaml
# amplify.yml handles the build
frontend:
  phases:
    build:
      commands:
        - npm run build
  artifacts:
    baseDirectory: .next
    files:
      - '**/*'  # Includes sitemap.xml
```

## 🔧 Deployment Steps

### Step 1: Push to Repository
```bash
git add .
git commit -m "Add sitemap and SEO improvements"
git push origin main
```

### Step 2: Amplify Auto-Deploy
- Amplify will automatically build and deploy
- Sitemap will be available at: `https://yourdomain.com/sitemap.xml`

### Step 3: Verify Sitemap
After deployment, check:
- `https://yourdomain.com/sitemap.xml`
- `https://yourdomain.com/robots.txt`

## 📊 Submit to Search Engines

### Google Search Console
1. Go to Google Search Console
2. Add property: `yourdomain.com`
3. Verify ownership
4. Submit sitemap: `https://yourdomain.com/sitemap.xml`

### Bing Webmaster Tools
1. Go to Bing Webmaster Tools
2. Add site: `yourdomain.com`
3. Verify ownership
4. Submit sitemap: `https://yourdomain.com/sitemap.xml`

## 🎯 Current Sitemap Content

Your sitemap includes:
- Homepage (/)
- Vendors (/vendors)
- Venues (/venues)
- Shop (/shop)
- Blog (/blog)
- Photos (/photos)
- Planning tools (/planning)
- Static pages (contact, about, privacy, terms)

## 🔄 Dynamic Content (Future)

To add dynamic content to sitemap:

```typescript
// In /app/sitemap.ts
async function generateVendorsSitemap() {
  const vendors = await VendorService.getAllActiveVendors();
  
  return vendors.map((vendor) => ({
    url: `${SITE_URL}/vendors/${vendor.slug}`,
    lastModified: new Date(vendor.updatedAt),
    changeFrequency: 'weekly' as const,
    priority: 0.8,
  }));
}
```

## ✅ Verification Checklist

After deployment:
- [ ] Visit `https://yourdomain.com/sitemap.xml`
- [ ] Check sitemap loads properly
- [ ] Submit to Google Search Console
- [ ] Submit to Bing Webmaster Tools
- [ ] Monitor indexing status

## 🚨 Troubleshooting

### If sitemap.xml returns 404:
1. Check if build completed successfully
2. Verify `/app/sitemap.ts` exists
3. Check Amplify build logs
4. Ensure domain is properly configured

### If sitemap is empty:
1. Check for errors in sitemap.ts
2. Verify SITE_URL environment variable
3. Check build logs for warnings

## 📈 SEO Impact

Once live, your sitemap will:
- Help Google discover all pages
- Improve indexing speed
- Provide page priority information
- Include last modified dates
- Support better SEO rankings

## 🎯 Next Steps

1. **Deploy to Amplify** - Push code and let Amplify build
2. **Verify sitemap** - Check sitemap.xml loads
3. **Submit to search engines** - Add to Search Console
4. **Monitor indexing** - Track page indexing status
5. **Add dynamic content** - Include vendor/venue pages

Your sitemap setup is complete and ready for deployment!