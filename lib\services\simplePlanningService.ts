import { generateClient } from 'aws-amplify/api';
import {
  createWeddingPlan,
  updateWeddingPlan,
  deleteWeddingPlan,
  createBudget,
  updateBudget,
  deleteBudget,
  createPlanningToolsData,
  updatePlanningToolsData,
  deletePlanningToolsData
} from '@/src/graphql/mutations.js';

import {
  listWeddingPlans,
  getWeddingPlan,
  weddingPlansByUserId,
  listBudgets,
  budgetsByUserId,
  listPlanningToolsData,
  planningToolsDataByUserId
} from '@/src/graphql/queries.js';

const client = generateClient();

// Simplified Types
export interface WeddingPlan {
  id: string;
  userId: string;
  weddingDate?: string;
  venue?: string;
  budget?: number;
  guestCount?: number;
  theme?: string;
  status: 'PLANNING' | 'CONFIRMED' | 'COMPLETED' | 'CANCELLED';
  notes?: string;
  createdAt: string;
  updatedAt: string;
}

export interface Budget {
  id: string;
  userId: string;
  weddingPlanId?: string;
  name: string;
  totalBudget: number;
  spentAmount?: number;
  isTemplate?: boolean;
  templateType?: string;
  createdAt: string;
  updatedAt: string;
}

export interface PlanningToolsData {
  id: string;
  userId: string;
  toolType: 'BUDGET' | 'GUEST_LIST' | 'TIMELINE' | 'CHECKLIST' | 'IDEAS' | 'WEDDING_PLAN';
  name: string;
  data: any; // JSON data
  metadata?: any; // Additional metadata
  createdAt: string;
  updatedAt: string;
}

class SimplePlanningService {
  // Wedding Plan Methods
  async createWeddingPlan(userId: string, planData: Partial<WeddingPlan>): Promise<WeddingPlan> {
    try {
      const response = await client.graphql({
        query: createWeddingPlan,
        variables: {
          input: {
            userId,
            weddingDate: planData.weddingDate,
            venue: planData.venue,
            budget: planData.budget,
            guestCount: planData.guestCount,
            theme: planData.theme,
            status: planData.status || 'PLANNING',
            notes: planData.notes
          }
        }
      });

      return response.data.createWeddingPlan;
    } catch (error) {
      console.error('Error creating wedding plan:', error);
      throw error;
    }
  }

  async getUserWeddingPlans(userId: string): Promise<WeddingPlan[]> {
    try {
      const response = await client.graphql({
        query: weddingPlansByUserId,
        variables: {
          userId
        }
      });

      return response.data.weddingPlansByUserIdAndCreatedAt.items;
    } catch (error) {
      console.error('Error fetching wedding plans:', error);
      return [];
    }
  }

  // Budget Methods
  async createBudget(userId: string, budgetData: Partial<Budget>): Promise<Budget> {
    try {
      const response = await client.graphql({
        query: createBudget,
        variables: {
          input: {
            userId,
            weddingPlanId: budgetData.weddingPlanId,
            name: budgetData.name || 'My Wedding Budget',
            totalBudget: budgetData.totalBudget || 0,
            spentAmount: budgetData.spentAmount || 0,
            isTemplate: budgetData.isTemplate || false,
            templateType: budgetData.templateType
          }
        }
      });

      return response.data.createBudget;
    } catch (error) {
      console.error('Error creating budget:', error);
      throw error;
    }
  }

  async getUserBudgets(userId: string): Promise<Budget[]> {
    try {
      const response = await client.graphql({
        query: budgetsByUserId,
        variables: {
          userId
        }
      });

      return response.data.budgetsByUserIdAndCreatedAt.items;
    } catch (error) {
      console.error('Error fetching budgets:', error);
      return [];
    }
  }

  // Planning Tools Data Methods (Generic JSON storage)
  async savePlanningData(
    userId: string,
    toolType: PlanningToolsData['toolType'],
    name: string,
    data: any,
    metadata?: any
  ): Promise<PlanningToolsData> {
    try {
      const response = await client.graphql({
        query: createPlanningToolsData,
        variables: {
          input: {
            userId,
            toolType,
            name,
            data: JSON.stringify(data),
            metadata: metadata ? JSON.stringify(metadata) : null
          }
        }
      });

      const result = response.data.createPlanningToolsData;
      return {
        ...result,
        data: JSON.parse(result.data),
        metadata: result.metadata ? JSON.parse(result.metadata) : null
      };
    } catch (error) {
      console.error('Error saving planning data:', error);
      throw error;
    }
  }

  async updatePlanningData(
    id: string,
    updates: Partial<PlanningToolsData>
  ): Promise<PlanningToolsData> {
    try {
      const updateInput: any = { id };
      
      if (updates.name) updateInput.name = updates.name;
      if (updates.data) updateInput.data = JSON.stringify(updates.data);
      if (updates.metadata) updateInput.metadata = JSON.stringify(updates.metadata);

      const response = await client.graphql({
        query: updatePlanningToolsData,
        variables: {
          input: updateInput
        }
      });

      const result = response.data.updatePlanningToolsData;
      return {
        ...result,
        data: JSON.parse(result.data),
        metadata: result.metadata ? JSON.parse(result.metadata) : null
      };
    } catch (error) {
      console.error('Error updating planning data:', error);
      throw error;
    }
  }

  async getUserPlanningData(
    userId: string,
    toolType?: PlanningToolsData['toolType']
  ): Promise<PlanningToolsData[]> {
    try {
      const response = await client.graphql({
        query: planningToolsDataByUserId,
        variables: {
          userId
        }
      });

      let items = response.data.planningToolsDataByUserIdAndCreatedAt.items;

      // Filter by tool type if specified
      if (toolType) {
        items = items.filter((item: any) => item.toolType === toolType);
      }

      // Parse JSON data
      return items.map((item: any) => ({
        ...item,
        data: JSON.parse(item.data),
        metadata: item.metadata ? JSON.parse(item.metadata) : null
      }));
    } catch (error) {
      console.error('Error fetching planning data:', error);
      return [];
    }
  }

  async deletePlanningData(id: string): Promise<boolean> {
    try {
      await client.graphql({
        query: deletePlanningToolsData,
        variables: {
          input: { id }
        }
      });

      return true;
    } catch (error) {
      console.error('Error deleting planning data:', error);
      return false;
    }
  }

  // Migration Methods
  async migrateBudgetFromLocalStorage(userId: string, localStorageData: any[]): Promise<Budget[]> {
    try {
      const migratedBudgets: Budget[] = [];

      for (const localBudget of localStorageData) {
        // Create budget record
        const budget = await this.createBudget(userId, {
          name: localBudget.name || 'Migrated Budget',
          totalBudget: localBudget.total || 0,
          spentAmount: 0
        });

        // Save detailed budget data as JSON
        await this.savePlanningData(
          userId,
          'BUDGET',
          `${budget.name} - Details`,
          {
            budgetId: budget.id,
            categories: localBudget.categories || [],
            total: localBudget.total || 0,
            originalData: localBudget
          },
          {
            migratedFrom: 'localStorage',
            migratedAt: new Date().toISOString()
          }
        );

        migratedBudgets.push(budget);
      }

      return migratedBudgets;
    } catch (error) {
      console.error('Error migrating budget data:', error);
      throw error;
    }
  }

  async migrateGuestListFromLocalStorage(userId: string, localStorageData: any[]): Promise<PlanningToolsData> {
    try {
      const guestListData = await this.savePlanningData(
        userId,
        'GUEST_LIST',
        'Migrated Guest List',
        {
          guests: localStorageData,
          totalGuests: localStorageData.length,
          confirmedGuests: localStorageData.filter(g => g.rsvpStatus === 'confirmed').length
        },
        {
          migratedFrom: 'localStorage',
          migratedAt: new Date().toISOString()
        }
      );

      return guestListData;
    } catch (error) {
      console.error('Error migrating guest list data:', error);
      throw error;
    }
  }

  // Utility Methods
  async getBudgetWithDetails(userId: string, budgetId: string): Promise<{
    budget: Budget;
    details: PlanningToolsData | null;
  }> {
    try {
      const [budgets, planningData] = await Promise.all([
        this.getUserBudgets(userId),
        this.getUserPlanningData(userId, 'BUDGET')
      ]);

      const budget = budgets.find(b => b.id === budgetId);
      const details = planningData.find(d => d.data.budgetId === budgetId);

      return {
        budget: budget || null,
        details: details || null
      };
    } catch (error) {
      console.error('Error fetching budget with details:', error);
      return { budget: null, details: null };
    }
  }

  async getGuestListData(userId: string): Promise<PlanningToolsData[]> {
    return this.getUserPlanningData(userId, 'GUEST_LIST');
  }

  async getTimelineData(userId: string): Promise<PlanningToolsData[]> {
    return this.getUserPlanningData(userId, 'TIMELINE');
  }

  async getIdeasData(userId: string): Promise<PlanningToolsData[]> {
    return this.getUserPlanningData(userId, 'IDEAS');
  }
}

export default new SimplePlanningService();
