import { test, expect } from '@playwright/test';
import { AuthHelpers } from '../utils/auth-helpers';

test.describe('User Signup', () => {
  let authHelpers: AuthHelpers;

  test.beforeEach(async ({ page }) => {
    authHelpers = new AuthHelpers(page);
    await authHelpers.logoutIfLoggedIn();
  });

  test('should display signup form', async ({ page }) => {
    await authHelpers.navigateTo('/login');
    
    // Look for signup link or tab
    const signupSelectors = [
      'text=Sign Up',
      'text=Register',
      'text=Create Account',
      '[data-testid="signup-tab"]'
    ];
    
    let signupFound = false;
    for (const selector of signupSelectors) {
      if (await authHelpers.elementExists(selector)) {
        await authHelpers.clickElement(selector);
        signupFound = true;
        break;
      }
    }
    
    if (!signupFound) {
      await authHelpers.navigateTo('/signup');
    }
    
    // Check if signup form elements are present
    await expect(page.locator('input[name="firstName"], input[placeholder*="First"]')).toBeVisible();
    await expect(page.locator('input[name="lastName"], input[placeholder*="Last"]')).toBeVisible();
    await expect(page.locator('input[type="email"]')).toBeVisible();
    await expect(page.locator('input[type="password"]')).toBeVisible();
    await expect(page.locator('button[type="submit"], button:has-text("Sign Up")')).toBeVisible();
  });

  test('should show validation errors for empty required fields', async ({ page }) => {
    await authHelpers.navigateTo('/signup');
    
    // Try to submit empty form
    await authHelpers.clickElement('button[type="submit"], button:has-text("Sign Up")');
    
    // Check for validation messages
    const errorSelectors = [
      'text=First name is required',
      'text=Last name is required',
      'text=Email is required',
      'text=Password is required',
      '.error',
      '[role="alert"]',
      '.text-red-500'
    ];
    
    let errorFound = false;
    for (const selector of errorSelectors) {
      if (await authHelpers.elementExists(selector)) {
        errorFound = true;
        break;
      }
    }
    
    expect(errorFound).toBe(true);
  });

  test('should show error for invalid email format', async ({ page }) => {
    await authHelpers.navigateTo('/signup');
    
    const testData = authHelpers.generateTestData();
    
    await authHelpers.fillInput('input[name="firstName"], input[placeholder*="First"]', testData.firstName);
    await authHelpers.fillInput('input[name="lastName"], input[placeholder*="Last"]', testData.lastName);
    await authHelpers.fillInput('input[type="email"]', 'invalid-email');
    await authHelpers.fillInput('input[type="password"]', testData.password);
    
    await authHelpers.clickElement('button[type="submit"], button:has-text("Sign Up")');
    
    // Check for email validation error
    const emailErrorSelectors = [
      'text=Invalid email',
      'text=Please enter a valid email',
      'text=Email format is invalid'
    ];
    
    let emailErrorFound = false;
    for (const selector of emailErrorSelectors) {
      if (await authHelpers.elementExists(selector)) {
        emailErrorFound = true;
        break;
      }
    }
    
    expect(emailErrorFound).toBe(true);
  });

  test('should show error for weak password', async ({ page }) => {
    await authHelpers.navigateTo('/signup');
    
    const testData = authHelpers.generateTestData();
    
    await authHelpers.fillInput('input[name="firstName"], input[placeholder*="First"]', testData.firstName);
    await authHelpers.fillInput('input[name="lastName"], input[placeholder*="Last"]', testData.lastName);
    await authHelpers.fillInput('input[type="email"]', testData.email);
    await authHelpers.fillInput('input[type="password"]', '123'); // Weak password
    
    await authHelpers.clickElement('button[type="submit"], button:has-text("Sign Up")');
    
    // Check for password validation error
    const passwordErrorSelectors = [
      'text=Password too weak',
      'text=Password must be',
      'text=Password should contain'
    ];
    
    let passwordErrorFound = false;
    for (const selector of passwordErrorSelectors) {
      if (await authHelpers.elementExists(selector)) {
        passwordErrorFound = true;
        break;
      }
    }
    
    expect(passwordErrorFound).toBe(true);
  });

  test('should successfully register new user', async ({ page }) => {
    await authHelpers.navigateTo('/signup');
    
    const testData = authHelpers.generateTestData();
    
    await authHelpers.fillInput('input[name="firstName"], input[placeholder*="First"]', testData.firstName);
    await authHelpers.fillInput('input[name="lastName"], input[placeholder*="Last"]', testData.lastName);
    await authHelpers.fillInput('input[type="email"]', testData.email);
    await authHelpers.fillInput('input[type="password"]', testData.password);
    
    // Fill phone if field exists
    if (await authHelpers.elementExists('input[name="phone"], input[type="tel"]')) {
      await authHelpers.fillInput('input[name="phone"], input[type="tel"]', testData.phone);
    }
    
    await authHelpers.clickElement('button[type="submit"], button:has-text("Sign Up")');
    
    await authHelpers.waitForLoadingToComplete();
    await authHelpers.waitForNetworkIdle();
    
    // Check for success indicators
    const successIndicators = [
      'text=Account created',
      'text=Registration successful',
      'text=Welcome',
      'text=Verify your email',
      'text=OTP'
    ];
    
    let successFound = false;
    for (const indicator of successIndicators) {
      if (await authHelpers.elementExists(indicator)) {
        successFound = true;
        break;
      }
    }
    
    // If OTP verification is required
    if (await authHelpers.elementExists('input[name="otp"], input[placeholder*="OTP"]')) {
      await authHelpers.handleOTPVerification();
      successFound = true;
    }
    
    // Check if redirected to dashboard or login
    const currentUrl = page.url();
    if (currentUrl.includes('/dashboard') || currentUrl.includes('/login') || await authHelpers.isUserLoggedIn()) {
      successFound = true;
    }
    
    expect(successFound).toBe(true);
  });

  test('should show error for existing email', async ({ page }) => {
    await authHelpers.navigateTo('/signup');
    
    // Use a common email that might already exist
    const existingEmail = '<EMAIL>';
    const testData = authHelpers.generateTestData();
    
    await authHelpers.fillInput('input[name="firstName"], input[placeholder*="First"]', testData.firstName);
    await authHelpers.fillInput('input[name="lastName"], input[placeholder*="Last"]', testData.lastName);
    await authHelpers.fillInput('input[type="email"]', existingEmail);
    await authHelpers.fillInput('input[type="password"]', testData.password);
    
    await authHelpers.clickElement('button[type="submit"], button:has-text("Sign Up")');
    
    await authHelpers.waitForLoadingToComplete();
    
    // Check for duplicate email error
    const duplicateErrorSelectors = [
      'text=Email already exists',
      'text=User already exists',
      'text=Email is already registered'
    ];
    
    let duplicateErrorFound = false;
    for (const selector of duplicateErrorSelectors) {
      if (await authHelpers.elementExists(selector)) {
        duplicateErrorFound = true;
        break;
      }
    }
    
    // Note: This test might pass even if no error is shown,
    // as the test email might not actually exist in the database
    console.log('Duplicate email test completed - error found:', duplicateErrorFound);
  });

  test('should have Google signup option', async ({ page }) => {
    await authHelpers.navigateTo('/signup');
    
    // Check for Google signup button
    const googleSelectors = [
      'button:has-text("Google")',
      'button:has-text("Continue with Google")',
      'button:has-text("Sign up with Google")',
      '[data-testid="google-signup"]'
    ];
    
    let googleSignupFound = false;
    for (const selector of googleSelectors) {
      if (await authHelpers.elementExists(selector)) {
        await expect(page.locator(selector)).toBeVisible();
        googleSignupFound = true;
        break;
      }
    }
    
    expect(googleSignupFound).toBe(true);
  });

  test('should have login link for existing users', async ({ page }) => {
    await authHelpers.navigateTo('/signup');
    
    // Check for login link
    const loginSelectors = [
      'text=Already have an account',
      'text=Sign In',
      'text=Login',
      'a[href*="login"]',
      '[data-testid="login-link"]'
    ];
    
    let loginLinkFound = false;
    for (const selector of loginSelectors) {
      if (await authHelpers.elementExists(selector)) {
        await expect(page.locator(selector)).toBeVisible();
        loginLinkFound = true;
        break;
      }
    }
    
    expect(loginLinkFound).toBe(true);
  });

  test('should handle terms and conditions checkbox', async ({ page }) => {
    await authHelpers.navigateTo('/signup');
    
    // Check if terms checkbox exists
    const termsSelectors = [
      'input[type="checkbox"]',
      'input[name="terms"]',
      'input[name="agreeToTerms"]'
    ];
    
    for (const selector of termsSelectors) {
      if (await authHelpers.elementExists(selector)) {
        const checkbox = page.locator(selector);
        
        // Try to submit without checking terms
        const testData = authHelpers.generateTestData();
        await authHelpers.fillInput('input[name="firstName"], input[placeholder*="First"]', testData.firstName);
        await authHelpers.fillInput('input[name="lastName"], input[placeholder*="Last"]', testData.lastName);
        await authHelpers.fillInput('input[type="email"]', testData.email);
        await authHelpers.fillInput('input[type="password"]', testData.password);
        
        await authHelpers.clickElement('button[type="submit"], button:has-text("Sign Up")');
        
        // Should show terms error
        const termsError = await authHelpers.elementExists('text=terms') || 
                          await authHelpers.elementExists('text=agreement') ||
                          await authHelpers.elementExists('text=accept');
        
        if (termsError) {
          // Now check the terms checkbox and try again
          await checkbox.check();
          await authHelpers.clickElement('button[type="submit"], button:has-text("Sign Up")');
        }
        
        break;
      }
    }
  });
});
