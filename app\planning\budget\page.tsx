'use client'
import { TopHeader } from "@/components/top-header"
import { Head<PERSON> } from "@/components/header"
import { Footer } from "@/components/footer"
import { showToast, toastMessages } from '@/lib/toast'
import { useState, useEffect, useRef, Suspense, lazy } from "react"
import { Plus, Trash2, Calculator, TrendingUp, Pie<PERSON>hart, Save, Cloud } from "@/lib/icon-imports"
import { useAuth } from '@/contexts/AuthContext'
import simplePlanningService from '@/lib/services/simplePlanningService'
import { toast } from 'sonner'
import PlanningDataMigration from '@/components/PlanningDataMigration'

// Lazy load heavy components
const LazyBudgetChart = lazy(() => import('@/components/lazy/LazyBudgetChart'))
const LazyPDFExporter = lazy(() => import('@/components/lazy/LazyPDFExporter'))

interface BudgetCategory {
  id?: number;
  name: string;
  percent: number;
  actual?: number;
  notes?: string;
  priority?: string;
}

const DEFAULT_CATEGORIES: BudgetCategory[] = [
  { name: "Venue", percent: 30 },
  { name: "Catering", percent: 25 },
  { name: "Photography", percent: 10 },
  { name: "Decoration", percent: 10 },
  { name: "Dress", percent: 10 },
  { name: "Miscellaneous", percent: 15 },
]

// Chart colors that match the theme
const CHART_COLORS = [
  'hsl(var(--primary))', // maroon
  'hsl(var(--secondary))', // green
  'hsl(var(--accent))', // gold
  'hsl(var(--muted-foreground))', // brown
  'hsl(var(--chart-3))', // dark brown
  'hsl(var(--chart-4))', // light brown
]

const BUDGET_TEMPLATES = {
  small: { name: "Small Wedding (50-100 guests)", amount: 300000 },
  medium: { name: "Medium Wedding (100-300 guests)", amount: 800000 },
  large: { name: "Large Wedding (300+ guests)", amount: 1500000 },
  luxury: { name: "Luxury Wedding", amount: 3000000 }
}

// Mock budget data for non-logged-in users
const MOCK_BUDGETS = [
  {
    id: 1,
    name: "Sample Wedding Budget",
    total: 500000,
    categories: [
      { name: "Venue", amount: 150000, percentage: 30 },
      { name: "Catering", amount: 125000, percentage: 25 },
      { name: "Photography", amount: 50000, percentage: 10 },
      { name: "Decoration", amount: 75000, percentage: 15 },
      { name: "Outfits", amount: 50000, percentage: 10 },
      { name: "Jewelry", amount: 25000, percentage: 5 },
      { name: "Miscellaneous", amount: 25000, percentage: 5 }
    ],
    createdAt: new Date().toISOString()
  }
]

export default function BudgetCalculatorPage() {
  const { user } = useAuth()
  const [mounted, setMounted] = useState(false)
  const pageRef = useRef<HTMLDivElement>(null)
  const contentRef = useRef<HTMLDivElement>(null)
  const [saving, setSaving] = useState(false)
  const [loading, setLoading] = useState(false)

  // Safe number formatting to prevent hydration issues
  const formatCurrency = (amount: number) => {
    if (!mounted) return `₹${amount}`
    return `₹${amount.toLocaleString()}`
  }

  const formatDate = (dateString: string) => {
    if (!mounted) return dateString
    return new Date(dateString).toLocaleDateString()
  }
  const [total, setTotal] = useState(500000)
  const [categories, setCategories] = useState<BudgetCategory[]>(DEFAULT_CATEGORIES)
  const [newCat, setNewCat] = useState("")
  const [newPercent, setNewPercent] = useState("")
  const [activeView, setActiveView] = useState<'budget' | 'tracker' | 'summary'>('budget')
  const [savedBudgets, setSavedBudgets] = useState<any[]>([])
  const [budgetName, setBudgetName] = useState("")

  // Load budget data based on user authentication
  useEffect(() => {
    setMounted(true)
  }, [])

  useEffect(() => {
    if (mounted) {
      loadBudgetData()
    }
  }, [mounted, user])

  const loadBudgetData = async () => {
    try {
      setLoading(true)

      if (user?.userId) {
        // Load from database for logged-in users
        const budgetData = await simplePlanningService.getUserPlanningData(user.userId, 'BUDGET')

        if (budgetData.length > 0) {
          setSavedBudgets(budgetData.map(item => ({
            id: item.id,
            name: item.name,
            total: item.data.total || 0,
            categories: item.data.categories || DEFAULT_CATEGORIES,
            createdAt: item.createdAt
          })))
        } else {
          // Check for localStorage data to migrate
          const saved = localStorage.getItem('wedding-budgets')
          if (saved) {
            setSavedBudgets(JSON.parse(saved))
            // Migration will be handled by PlanningDataMigration component
          } else {
            setSavedBudgets([])
          }
        }
      } else {
        // Use mock data for non-logged-in users
        setSavedBudgets(MOCK_BUDGETS)
      }
    } catch (error) {
      console.error('Error loading budget data:', error)
      toast.error('Failed to load budget data')
      // Fallback to localStorage or mock data
      if (user?.userId) {
        const saved = localStorage.getItem('wedding-budgets')
        if (saved) {
          setSavedBudgets(JSON.parse(saved))
        }
      } else {
        setSavedBudgets(MOCK_BUDGETS)
      }
    } finally {
      setLoading(false)
    }
  }

  // Prevent hydration mismatch by not rendering until mounted
  if (!mounted) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-background to-muted/20">
        <TopHeader />
        <Header />
        <main className="py-8 px-4 sm:py-12">
          <div className="max-w-7xl mx-auto">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
              <p className="mt-4 text-gray-600">Loading...</p>
            </div>
          </div>
        </main>
        <Footer />
      </div>
    )
  }

  const handleCategoryChange = (idx: number, field: string, value: string | number) => {
    setCategories(cats => cats.map((cat, i) =>
      i === idx ? { ...cat, [field]: field === "percent" ? Number(value) : value } : cat
    ))
  }

  const handleRemove = (idx: number) => {
    setCategories(cats => cats.filter((_, i) => i !== idx))
  }

  const handleAdd = () => {
    if (!newCat.trim() || !newPercent || isNaN(Number(newPercent))) return
    const newId = Math.max(...categories.map(c => c.id || 0), 0) + 1
    setCategories([...categories, {
      id: newId,
      name: newCat.trim(),
      percent: Number(newPercent),
      actual: 0,
      notes: "",
      priority: "medium"
    }])
    setNewCat("")
    setNewPercent("")
  }

  const saveBudget = async () => {
    if (!budgetName.trim()) {
      toast.error('Please enter a budget name')
      return
    }

    setSaving(true)
    console.log('🔄 Starting budget save process...')
    console.log('User:', user)
    console.log('Budget name:', budgetName)
    console.log('Total:', total)
    console.log('Categories:', categories)

    try {
      if (user?.userId) {
        console.log('💾 Saving to database for user:', user.userId)

        // Save to database for logged-in users
        const budgetData = {
          name: budgetName,
          total,
          categories,
          createdAt: new Date().toISOString()
        }

        console.log('📤 Calling simplePlanningService.savePlanningData with:', {
          userId: user.userId,
          toolType: 'BUDGET',
          name: budgetName,
          data: budgetData
        })

        const result = await simplePlanningService.savePlanningData(
          user.userId,
          'BUDGET',
          budgetName,
          budgetData,
          {
            totalBudget: total,
            categoryCount: categories.length
          }
        )

        console.log('✅ Save result:', result)

        // Reload budget data
        console.log('🔄 Reloading budget data...')
        await loadBudgetData()

        toast.success('Budget saved to your account!')
        console.log('✅ Budget saved successfully!')
      } else {
        console.log('💾 User not logged in, saving to localStorage')

        // Save to localStorage for non-logged-in users (mock data scenario)
        const budget = {
          id: Date.now(),
          name: budgetName,
          total,
          categories,
          createdAt: new Date().toISOString()
        }
        const updated = [...savedBudgets, budget]
        setSavedBudgets(updated)
        localStorage.setItem('wedding-budgets', JSON.stringify(updated))
        showToast.success('Budget saved locally!')
      }

      setBudgetName("")
    } catch (error) {
      console.error('❌ Error saving budget:', error)
      console.error('Error details:', {
        message: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined,
        name: error instanceof Error ? error.name : 'Unknown'
      })

      toast.error(`Failed to save budget: ${error instanceof Error ? error.message : 'Unknown error'}`)

      // Fallback to localStorage
      console.log('🔄 Falling back to localStorage...')
      const budget = {
        id: Date.now(),
        name: budgetName,
        total,
        categories,
        createdAt: new Date().toISOString()
      }
      const updated = [...savedBudgets, budget]
      setSavedBudgets(updated)
      localStorage.setItem('wedding-budgets', JSON.stringify(updated))
      toast.success('Budget saved locally as fallback')
    } finally {
      setSaving(false)
    }
  }

  const loadTemplate = (templateKey: string) => {
    setTotal(BUDGET_TEMPLATES[templateKey as keyof typeof BUDGET_TEMPLATES].amount)
  }

  // PDF download functionality is now handled by the LazyPDFExporter component

  const totalPercent = categories.reduce((sum, c) => sum + Number(c.percent), 0)
  const totalActual = categories.reduce((sum, c) => sum + Number(c.actual), 0)
  const remainingBudget = total - totalActual

  // Prepare data for pie chart
  const chartData = categories.map((cat, index) => ({
    name: cat.name,
    value: cat.percent,
    amount: Math.round((cat.percent / 100) * total),
    color: CHART_COLORS[index % CHART_COLORS.length]
  }))

  // CustomTooltip is now part of the LazyBudgetChart component

  return (
    <div className="min-h-screen bg-gradient-to-br from-background to-muted/20">
      <TopHeader />
      <Header />
      
      <main className="py-4 px-3 sm:py-6 sm:px-4 lg:py-8 lg:pt-20">
        <div className="max-w-7xl mx-auto">
          {/* PDF Content - This will be captured */}
          <div ref={contentRef}>
            {/* Header Section */}
            <div className="text-center mb-6 sm:mb-8 lg:mb-12">
              <div className="inline-flex items-center justify-center w-12 h-12 sm:w-16 sm:h-16 bg-gradient-to-br from-primary to-pink-600 rounded-full mb-3 sm:mb-4 lg:mb-6 shadow-lg">
                <Calculator className="w-6 h-6 sm:w-8 sm:h-8 text-white" />
              </div>
              <h1 className="text-xl sm:text-2xl lg:text-3xl xl:text-4xl font-bold text-foreground mb-2 sm:mb-3 lg:mb-4">
                Wedding Budget Planner
              </h1>
              <p className="text-sm sm:text-base lg:text-lg text-muted-foreground max-w-2xl mx-auto mb-3 sm:mb-4 lg:mb-6 px-2 sm:px-4">
                Plan and track your wedding expenses with our simple budget calculator
              </p>
            </div>

            {/* Migration Component */}
            {user && <PlanningDataMigration />}

            {/* Top Action Bar - Save, Add Category, Summary, Download */}
            <div className="bg-card rounded-xl shadow-sm border p-3 sm:p-4 lg:p-6 mb-4 sm:mb-6 lg:mb-8" data-pdf-exclude>
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4 lg:gap-6">
                {/* Save Budget Section */}
                <div className="space-y-2 sm:space-y-3">
                  <h3 className="text-sm sm:text-base lg:text-lg font-semibold text-card-foreground flex items-center gap-2">
                    <Save className="w-4 h-4 sm:w-5 sm:h-5 text-primary" />
                    Save Budget
                  </h3>
                  <div className="space-y-2">
                    <input
                      type="text"
                      placeholder="Budget name"
                      className="w-full bg-background border border-border rounded-lg px-2 sm:px-3 py-2 text-xs sm:text-sm focus:outline-none focus:ring-2 focus:ring-primary/20"
                      value={budgetName}
                      onChange={e => setBudgetName(e.target.value)}
                    />
                    <button
                      className="w-full bg-primary text-primary-foreground rounded-lg px-2 sm:px-3 lg:px-4 py-2 text-xs sm:text-sm font-semibold hover:bg-primary/90 transition-colors flex items-center justify-center gap-2 disabled:opacity-50"
                      onClick={saveBudget}
                      disabled={saving || !budgetName.trim()}
                    >
                      {saving ? (
                        <>
                          <div className="animate-spin rounded-full h-3 w-3 sm:h-4 sm:w-4 border-b-2 border-white"></div>
                          <span className="hidden sm:inline">Saving...</span>
                          <span className="sm:hidden">Save</span>
                        </>
                      ) : (
                        <>
                          <Cloud className="w-3 h-3 sm:w-4 sm:h-4" />
                          <span className="hidden sm:inline">Save to Account</span>
                          <span className="sm:hidden">Save</span>
                        </>
                      )}
                    </button>
                  </div>
                </div>

                {/* Add New Category Section */}
                <div className="space-y-2 sm:space-y-3">
                  <h3 className="text-sm sm:text-base lg:text-lg font-semibold text-card-foreground flex items-center gap-2">
                    <Plus className="w-4 h-4 sm:w-5 sm:h-5 text-primary" />
                    Add Category
                  </h3>
                  <div className="space-y-2">
                    <input
                      type="text"
                      placeholder="Category name"
                      className="w-full bg-background border border-border rounded-lg px-2 sm:px-3 py-2 text-xs sm:text-sm focus:outline-none focus:ring-2 focus:ring-primary/20"
                      value={newCat}
                      onChange={e => setNewCat(e.target.value)}
                    />
                    <div className="flex gap-2">
                      <input
                        type="number"
                        placeholder="%"
                        min={0}
                        max={100}
                        className="w-12 sm:w-16 lg:w-20 text-center bg-background border border-border rounded-md px-1 sm:px-2 lg:px-3 py-2 text-xs sm:text-sm lg:text-base font-semibold"
                        value={newPercent}
                        onChange={e => setNewPercent(e.target.value)}
                      />
                      <button
                        className="flex-1 bg-secondary text-secondary-foreground rounded-lg px-2 sm:px-3 py-2 text-xs sm:text-sm font-semibold hover:bg-secondary/90 transition-colors"
                        onClick={handleAdd}
                        disabled={!newCat.trim() || !newPercent || isNaN(Number(newPercent))}
                      >
                        Add
                      </button>
                    </div>
                  </div>
                </div>

                {/* Budget Summary Section */}
                <div className="space-y-2 sm:space-y-3" data-pdf-exclude>
                  <h3 className="text-sm sm:text-base lg:text-lg font-semibold text-card-foreground flex items-center gap-2">
                    <Calculator className="w-4 h-4 sm:w-5 sm:h-5 text-primary" />
                    Summary
                  </h3>
                  <div className="space-y-2">
                    <div className="text-lg sm:text-xl lg:text-2xl font-bold text-primary">
                      ₹{Math.round(total).toLocaleString()}
                    </div>
                    <div className="text-xs sm:text-sm text-muted-foreground">
                      Total allocation: {totalPercent}%
                    </div>
                    {totalPercent !== 100 && (
                      <div className="text-xs text-destructive">
                        {totalPercent < 100 ? 'Under' : 'Over'} by {Math.abs(100 - totalPercent)}%
                      </div>
                    )}
                  </div>
                </div>

                {/* Download PDF Section */}
                <div className="space-y-2 sm:space-y-3">
                  <h3 className="text-sm sm:text-base lg:text-lg font-semibold text-card-foreground flex items-center gap-2">
                    <PieChart className="w-4 h-4 sm:w-5 sm:h-5 text-primary" />
                    Export
                  </h3>
                  <div className="space-y-2">
                    <Suspense fallback={
                      <div className="w-full bg-muted rounded-lg px-2 sm:px-4 py-2 text-xs sm:text-sm text-center">
                        Loading...
                      </div>
                    }>
                      <LazyPDFExporter
                        contentRef={contentRef}
                        filename="wedding-budget"
                        buttonText="Download PDF"
                        className="w-full bg-accent text-accent-foreground rounded-lg px-2 sm:px-3 lg:px-4 py-2 text-xs sm:text-sm font-semibold hover:bg-accent/90 transition-colors"
                      />
                    </Suspense>
                  </div>
                </div>
              </div>
            </div>

          {/* Main Content - Responsive Layout */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6 lg:gap-8">
            {/* Left Column - Calculator */}
            <div className="space-y-4 sm:space-y-6 lg:space-y-8">
              {/* Total Budget Card */}
              <div className="bg-card rounded-xl shadow-sm border p-3 sm:p-4 lg:p-6 xl:p-8">
                <div className="flex flex-col items-center gap-3 sm:gap-4 lg:gap-6">
                  <div>
                    <h2 className="text-lg sm:text-xl lg:text-2xl font-semibold text-card-foreground mb-2 text-center">
                      Total Budget
                    </h2>
                  </div>
                  <div className="flex items-center justify-center gap-1 sm:gap-2">
                    <span className="text-lg sm:text-xl lg:text-2xl font-bold text-primary">₹</span>
                    <input
                      type="number"
                      min={0}
                      className="text-xl sm:text-2xl lg:text-3xl font-bold text-card-foreground bg-transparent border-b-2 border-primary/30 focus:border-primary outline-none text-center min-w-[100px] sm:min-w-[130px] lg:min-w-[180px]"
                      value={total}
                      onChange={e => setTotal(Number(e.target.value))}
                    />
                  </div>
                </div>
              </div>

              {/* Categories Section */}
              <div className="bg-card rounded-xl shadow-sm border p-3 sm:p-4 lg:p-6 xl:p-8">
                <div className="flex items-center gap-2 sm:gap-3 mb-3 sm:mb-4 lg:mb-6">
                  <TrendingUp className="w-4 h-4 sm:w-5 sm:h-5 lg:w-6 lg:h-6 text-primary" />
                  <h2 className="text-lg sm:text-xl lg:text-2xl font-semibold text-card-foreground">
                    Budget Categories
                  </h2>
                </div>

                {/* Categories List */}
                <div className="space-y-2 sm:space-y-3 lg:space-y-4 mb-4 sm:mb-6 lg:mb-8">
                  {categories.map((cat, idx) => (
                    <div key={idx} className="flex flex-col gap-2 sm:gap-3 p-2 sm:p-3 lg:p-4 bg-muted/30 rounded-lg">
                      <div className="flex-1 w-full">
                        <input
                          className="w-full bg-transparent border-none text-sm sm:text-base lg:text-lg font-medium text-card-foreground focus:outline-none"
                          value={cat.name}
                          onChange={e => handleCategoryChange(idx, "name", e.target.value)}
                          placeholder="Category name"
                        />
                      </div>
                      <div className="flex items-center justify-between gap-2 sm:gap-3">
                        <div className="flex items-center gap-1 sm:gap-2">
                          <input
                            type="number"
                            min={0}
                            max={100}
                            className="w-12 sm:w-16 lg:w-20 text-center bg-background border border-border rounded-md px-1 sm:px-2 lg:px-3 py-1 sm:py-2 text-sm sm:text-base lg:text-lg font-semibold"
                            value={cat.percent}
                            onChange={e => handleCategoryChange(idx, "percent", e.target.value)}
                          />
                          <span className="text-muted-foreground font-medium text-xs sm:text-sm lg:text-base">%</span>
                        </div>
                        <div className="text-right min-w-[80px] sm:min-w-[100px] lg:min-w-[120px]">
                          <div className="text-sm sm:text-base lg:text-lg font-bold text-primary">
                            ₹{Math.round((cat.percent / 100) * total).toLocaleString()}
                          </div>
                        </div>
                        <button
                          className="p-1 sm:p-1.5 lg:p-2 text-destructive hover:bg-destructive/10 rounded-md transition-colors"
                          onClick={() => handleRemove(idx)}
                          title="Remove category"
                        >
                          <Trash2 className="w-3 h-3 sm:w-4 sm:h-4" />
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Right Column - Pie Chart */}
            <div className="space-y-4 sm:space-y-6 lg:space-y-8">
              {/* Chart Card */}
              <div className="bg-card rounded-xl shadow-sm border p-3 sm:p-4 lg:p-6 xl:p-8 h-fit">
                <div className="flex items-center gap-2 sm:gap-3 mb-3 sm:mb-4 lg:mb-6">
                  <PieChart className="w-4 h-4 sm:w-5 sm:h-5 lg:w-6 lg:h-6 text-primary" />
                  <h2 className="text-lg sm:text-xl lg:text-2xl font-semibold text-card-foreground">
                    Budget Distribution
                  </h2>
                </div>
                
                <Suspense fallback={
                  <div className="flex items-center justify-center h-32 sm:h-48 lg:h-64">
                    <div className="animate-spin rounded-full h-6 w-6 sm:h-8 sm:w-8 border-b-2 border-primary"></div>
                  </div>
                }>
                  <LazyBudgetChart chartData={chartData} />
                </Suspense>

                                                                   {/* Chart Legend Details */}
                  <div className="mt-20 sm:mt-20 lg:mt-20 space-y-2 sm:space-y-3">
                   <h3 className="font-semibold text-card-foreground text-sm sm:text-base lg:text-lg">Category Breakdown</h3>
                   <div className="grid grid-cols-1 gap-1 sm:gap-2">
                     {chartData.map((item, index) => (
                       <div key={index} className="flex flex-col sm:flex-row sm:items-center justify-between p-2 sm:p-3 bg-muted/30 rounded-md gap-1 sm:gap-2">
                         <div className="flex items-center gap-1 sm:gap-2 min-w-0 flex-1">
                           <div 
                             className="w-2 h-2 sm:w-3 sm:h-3 rounded-full flex-shrink-0" 
                             style={{ backgroundColor: item.color }}
                           />
                           <span className="text-xs sm:text-sm font-medium text-card-foreground truncate">
                             {item.name}
                           </span>
                         </div>
                         <div className="flex items-center gap-2 sm:gap-3 text-right">
                           <div className="text-xs sm:text-sm font-bold text-primary">
                             ₹{item.amount.toLocaleString()}
                           </div>
                           <div className="text-xs text-muted-foreground font-medium">
                             {item.value}%
                           </div>
                         </div>
                       </div>
                     ))}
                   </div>
                 </div>
              </div>
            </div>
          </div>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  )
} 