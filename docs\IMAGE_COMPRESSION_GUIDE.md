# Image Compression for DynamoDB Size Limits

This guide explains how the application handles DynamoDB item size limit errors and automatically compresses images to fit within database constraints.

## Problem

DynamoDB has a maximum item size limit of 400KB. When storing images as base64 strings in database records, this limit can be easily exceeded, resulting in errors like:

```
ExpressionAttributeValues contains invalid value: Item size has exceeded the maximum allowed size for key :images
```

## Solution

The application now includes automatic image compression that:

1. **Detects size limit errors** automatically
2. **Compresses images** to fit within DynamoDB limits
3. **Retries operations** with compressed images
4. **Provides user feedback** about compression

## Components

### 1. Image Compression Utilities (`lib/image-optimization.ts`)

#### Key Functions:

- `compressImageForDB(file, options)` - Compress a single image
- `compressImagesForDB(images, options)` - Compress multiple images
- `checkImagesSizeLimit(images)` - Check if images exceed limits

#### Configuration:

```typescript
export const DB_LIMITS = {
  MAX_ITEM_SIZE: 400 * 1024,        // 400KB (DynamoDB limit)
  MAX_ATTRIBUTE_SIZE: 350 * 1024,   // 350KB (safe limit for images)
  TARGET_IMAGE_SIZE: 50 * 1024,     // 50KB target per image
}
```

### 2. Enhanced ImageUpload Component (`components/ui/image-upload.tsx`)

#### New Props:

- `compressForDB?: boolean` - Enable automatic compression for database storage
- `showSizeWarning?: boolean` - Show warnings when size limits are exceeded

#### Usage:

```tsx
<ImageUpload 
  images={form.images} 
  onImagesChange={setImages}
  compressForDB={true}           // Enable DB compression
  showSizeWarning={true}         // Show size warnings
  maxImages={8}
  label="Upload Product Images"
  description="Images will be compressed for database storage"
/>
```

### 3. DynamoDB Error Handler (`lib/dynamodb-error-handler.ts`)

#### Service Wrappers:

- `handleShopOperation()` - For shop/product operations
- `handleVendorOperation()` - For vendor operations  
- `handleVenueOperation()` - For venue operations

#### Usage in Services:

```typescript
// Before (prone to size errors)
const result = await client.graphql({
  query: createShop,
  variables: { input }
});

// After (with automatic compression)
return handleShopOperation(async (data) => {
  const result = await client.graphql({
    query: createShop,
    variables: { input: data }
  });
  return result.data.createShop;
}, input);
```

## Implementation Details

### Compression Strategy

1. **Progressive Quality Reduction**: Starts with 50% quality and reduces by 10% until size target is met
2. **Dimension Scaling**: Resizes images to maximum 800x600 pixels while maintaining aspect ratio
3. **Format Optimization**: Converts to JPEG format for better compression
4. **Safety Limits**: Uses 80% of DynamoDB limit to provide buffer space

### Size Calculations

```typescript
// Calculate base64 size in KB
function getBase64SizeKB(base64String: string): number {
  const base64Data = base64String.split(',')[1] || base64String;
  const sizeBytes = (base64Data.length * 3) / 4;
  const padding = (base64Data.match(/=/g) || []).length;
  return (sizeBytes - padding) / 1024;
}
```

### Error Detection

```typescript
const isItemSizeError = errorMessage.includes('Item size has exceeded the maximum allowed size');
const isImageSizeError = isItemSizeError && errorMessage.includes(':images');
```

## Dashboard Integration

All dashboard pages now use compressed image uploads:

### Shop Management (`app/dashboard/shop/page.tsx`)
```tsx
<ImageUpload 
  compressForDB={true}
  showSizeWarning={true}
  maxImages={8}
  // ... other props
/>
```

### Vendor Management (`app/dashboard/vendors/page.tsx`)
```tsx
<ImageUpload 
  compressForDB={true}
  showSizeWarning={true}
  maxImages={12}
  // ... other props
/>
```

### Venue Management (`app/dashboard/venues/page.tsx`)
```tsx
<ImageUpload 
  compressForDB={true}
  showSizeWarning={true}
  maxImages={15}
  // ... other props
/>
```

## User Experience

### Visual Feedback

1. **Size Warnings**: Yellow warning box shows when images exceed limits
2. **Compression Notifications**: Toast messages inform users about automatic compression
3. **Progress Indicators**: Loading states during compression operations

### Automatic Handling

1. **Transparent Compression**: Users don't need to manually compress images
2. **Fallback Behavior**: If compression fails, clear error messages are shown
3. **Retry Logic**: Operations automatically retry with compressed images

## Performance Considerations

### Client-Side Processing

- Compression happens in the browser using HTML5 Canvas
- No server-side processing required
- Immediate feedback to users

### Memory Management

- Images are processed one at a time to avoid memory issues
- Temporary canvas elements are cleaned up after use
- Base64 strings are optimized for storage

## Troubleshooting

### Common Issues

1. **Canvas Not Available**: Ensure code runs in browser environment
2. **Large Image Sets**: Consider reducing number of images if compression isn't sufficient
3. **Quality Too Low**: Adjust compression settings if image quality is unacceptable

### Debug Information

Enable debug logging to see compression details:

```typescript
console.log(`Compressing images: ${sizeCheck.totalSizeKB.toFixed(1)}KB -> target: ${sizeCheck.maxAllowedKB.toFixed(1)}KB`);
```

## Configuration Options

### Per-Service Limits

```typescript
// Shop images: 300KB total, 30KB per image
const compressedImages = await compressImagesForDB(images, {
  maxTotalSizeKB: 300,
  maxSizePerImageKB: 30,
  quality: 40
});

// Vendor gallery: 300KB total, 25KB per image  
const compressedGallery = await compressImagesForDB(gallery, {
  maxTotalSizeKB: 300,
  maxSizePerImageKB: 25,
  quality: 35
});

// Venue images: 300KB total, 20KB per image
const compressedImages = await compressImagesForDB(images, {
  maxTotalSizeKB: 300,
  maxSizePerImageKB: 20,
  quality: 35
});
```

### Quality Settings

```typescript
export const ImageQuality = {
  HERO: 90,        // High quality for hero images
  PRODUCT: 80,     // Good quality for product images
  THUMBNAIL: 70,   // Optimized for thumbnails
  AVATAR: 75,      // Good for profile images
  COMPRESSED: 50   // Heavily compressed for database storage
}
```

## Future Enhancements

1. **Cloud Storage Integration**: Move to S3/CloudFront for better performance
2. **Progressive Loading**: Implement lazy loading for compressed images
3. **Batch Processing**: Optimize compression for large image sets
4. **Quality Presets**: Allow users to choose compression levels
