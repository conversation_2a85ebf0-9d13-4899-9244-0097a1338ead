'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Star, ArrowLeft, Send } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { showToast, toastMessages } from '@/lib/toast';
import EntityReviewService from '@/lib/services/entityReviewService';

interface PlatformReviewFormProps {
  onBack: () => void;
  onSubmitted: () => void;
  className?: string;
}

export default function PlatformReviewForm({ onBack, onSubmitted, className }: PlatformReviewFormProps) {
  const { user } = useAuth();
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    location: '',
    weddingDate: '',
    rating: 5,
    title: '',
    review: '',
    wouldRecommend: true,
    // Platform-specific ratings
    usabilityRating: 5,
    supportRating: 5,
    featuresRating: 5,
    overallExperience: 5
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : value
    }));
  };

  const handleRatingChange = (field: string, rating: number) => {
    setFormData(prev => ({ ...prev, [field]: rating }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!user) {
      showToast.error(toastMessages.auth.authRequired);
      return;
    }

    setLoading(true);

    try {
      const reviewData = {
        ...formData,
        category: 'PLATFORM',
        reviewTarget: 'ADMIN',
        entityType: null, // Platform reviews don't have specific entities
        entityId: null
      };

      const result = await EntityReviewService.createEntityReview(reviewData, user);

      if (result.success) {
        showToast.success('Platform review submitted successfully! It will be reviewed by our admin team.');
        onSubmitted();
      } else {
        showToast.error(`Error: ${result.error}`);
      }
    } catch (error) {
      console.error('Error submitting platform review:', error);
      showToast.error(toastMessages.review.submitError);
    } finally {
      setLoading(false);
    }
  };

  const renderStarRating = (field: string, value: number, label: string) => (
    <div className="space-y-2">
      <Label className="text-sm font-medium">{label}</Label>
      <div className="flex space-x-1">
        {[1, 2, 3, 4, 5].map((star) => (
          <button
            key={star}
            type="button"
            onClick={() => handleRatingChange(field, star)}
            className="focus:outline-none"
          >
            <Star
              className={`w-6 h-6 ${
                star <= value
                  ? 'fill-yellow-400 text-yellow-400'
                  : 'text-gray-300 hover:text-yellow-400'
              } transition-colors`}
            />
          </button>
        ))}
        <span className="ml-2 text-sm text-gray-600">({value}/5)</span>
      </div>
    </div>
  );

  return (
    <div className={`max-w-2xl mx-auto ${className}`}>

      <Card>
        <CardHeader>
          <CardTitle>Share Your BookmyFestive Experience</CardTitle>
          <p className="text-sm text-gray-600">
            Your feedback helps us improve our platform and services for all couples.
          </p>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Basic Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="name">Your Name *</Label>
                <Input
                  id="name"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  required
                  placeholder="Enter your full name"
                />
              </div>
              <div>
                <Label htmlFor="email">Email Address *</Label>
                <Input
                  id="email"
                  name="email"
                  type="email"
                  value={formData.email}
                  onChange={handleInputChange}
                  required
                  placeholder="Enter your email"
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="location">Location</Label>
                <Input
                  id="location"
                  name="location"
                  value={formData.location}
                  onChange={handleInputChange}
                  placeholder="City, State"
                />
              </div>
              <div>
                <Label htmlFor="weddingDate">Wedding Date</Label>
                <Input
                  id="weddingDate"
                  name="weddingDate"
                  type="date"
                  value={formData.weddingDate}
                  onChange={handleInputChange}
                />
              </div>
            </div>

            {/* Platform-Specific Ratings */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-gray-900">Rate Your Experience</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {renderStarRating('usabilityRating', formData.usabilityRating, 'Website Usability')}
                {renderStarRating('supportRating', formData.supportRating, 'Customer Support')}
                {renderStarRating('featuresRating', formData.featuresRating, 'Platform Features')}
                {renderStarRating('overallExperience', formData.overallExperience, 'Overall Experience')}
              </div>
            </div>

            {/* Overall Rating */}
            <div>
              {renderStarRating('rating', formData.rating, 'Overall Rating *')}
            </div>

            {/* Review Content */}
            <div>
              <Label htmlFor="title">Review Title *</Label>
              <Input
                id="title"
                name="title"
                value={formData.title}
                onChange={handleInputChange}
                required
                placeholder="Summarize your experience in a few words"
                maxLength={100}
              />
            </div>

            <div>
              <Label htmlFor="review">Your Review *</Label>
              <Textarea
                id="review"
                name="review"
                value={formData.review}
                onChange={handleInputChange}
                required
                placeholder="Share your detailed experience with BookmyFestive..."
                rows={6}
                maxLength={1000}
              />
              <p className="text-xs text-gray-500 mt-1">
                {formData.review.length}/1000 characters
              </p>
            </div>

            {/* Recommendation */}
            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="wouldRecommend"
                name="wouldRecommend"
                checked={formData.wouldRecommend}
                onChange={handleInputChange}
                className="rounded border-gray-300"
              />
              <Label htmlFor="wouldRecommend" className="text-sm">
                I would recommend BookmyFestive to other couples
              </Label>
            </div>

            {/* Submit Button */}
            <div className="flex justify-end space-x-4">
              <Button type="button" variant="outline" onClick={onBack}>
                Cancel
              </Button>
              <Button type="submit" disabled={loading} className="bg-blue-600 hover:bg-blue-700">
                {loading ? (
                  'Submitting...'
                ) : (
                  <>
                    Submit Review
                    <Send className="w-4 h-4 ml-2" />
                  </>
                )}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
