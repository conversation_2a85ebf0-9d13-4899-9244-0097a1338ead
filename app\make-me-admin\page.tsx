'use client';

import React, { useState, useEffect } from 'react';
import { TopHeader } from '@/components/top-header';
import { Header } from '@/components/header';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Shield, 
  Crown, 
  User,
  CheckCircle,
  XCircle,
  AlertCircle,
  Copy
} from 'lucide-react';
import AdminUserService from '@/lib/services/adminUserService';
import { useAuth } from '@/contexts/AuthContext';

export default function MakeMeAdminPage() {
  const { user, isAuthenticated } = useAuth();
  const [userProfile, setUserProfile] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [actionLoading, setActionLoading] = useState(false);
  const [message, setMessage] = useState<{ type: 'success' | 'error' | 'info'; text: string } | null>(null);

  useEffect(() => {
    if (isAuthenticated && user) {
      loadUserProfile();
    }
  }, [isAuthenticated, user]);

  const loadUserProfile = async () => {
    try {
      setLoading(true);
      const profile = await AdminUserService.getUserProfile(user.userId);
      setUserProfile(profile);
      
      if (profile?.isAdmin || profile?.isSuperAdmin) {
        setMessage({ 
          type: 'success', 
          text: `You are already an ${profile.isSuperAdmin ? 'Super Admin' : 'Admin'}!` 
        });
      }
    } catch (error) {
      console.error('Error loading user profile:', error);
      setMessage({ type: 'error', text: 'Failed to load user profile' });
    } finally {
      setLoading(false);
    }
  };

  const handleMakeMeAdmin = async (adminLevel: 'ADMIN' | 'SUPER_ADMIN') => {
    if (!user) return;

    try {
      setActionLoading(true);
      const result = await AdminUserService.makeUserAdmin(user.userId, adminLevel);

      if (result.success) {
        setMessage({ 
          type: 'success', 
          text: `Successfully made you a ${adminLevel === 'SUPER_ADMIN' ? 'Super Admin' : 'Admin'}!` 
        });
        loadUserProfile(); // Refresh profile
      } else {
        setMessage({ type: 'error', text: result.error || 'Failed to make you admin' });
      }
    } catch (error) {
      console.error('Error making user admin:', error);
      setMessage({ type: 'error', text: 'Failed to make you admin' });
    } finally {
      setActionLoading(false);
    }
  };

  const copyUserId = () => {
    if (user?.userId) {
      navigator.clipboard.writeText(user.userId);
      setMessage({ type: 'info', text: 'User ID copied to clipboard!' });
    }
  };

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <Card className="w-96">
          <CardContent className="p-6 text-center">
            <User className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h2 className="text-xl font-semibold mb-2">Please Log In</h2>
            <p className="text-gray-600">You need to be logged in to make yourself admin.</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <TopHeader />
      <Header />
      
      <div className="container mx-auto px-4 py-8 max-w-2xl">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Make Me Admin</h1>
          <p className="text-gray-600">Quick utility to grant yourself admin privileges</p>
        </div>

        {/* Message */}
        {message && (
          <div className={`mb-6 p-4 rounded-lg ${
            message.type === 'success' 
              ? 'bg-green-50 border border-green-200 text-green-800'
              : message.type === 'error'
              ? 'bg-red-50 border border-red-200 text-red-800'
              : 'bg-blue-50 border border-blue-200 text-blue-800'
          }`}>
            <div className="flex items-center">
              {message.type === 'success' ? (
                <CheckCircle className="h-5 w-5 mr-2" />
              ) : message.type === 'error' ? (
                <XCircle className="h-5 w-5 mr-2" />
              ) : (
                <AlertCircle className="h-5 w-5 mr-2" />
              )}
              {message.text}
            </div>
          </div>
        )}

        {/* User Info */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle>Current User Information</CardTitle>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="text-center py-4">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mx-auto"></div>
                <p className="mt-2 text-gray-600">Loading...</p>
              </div>
            ) : (
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="font-medium">User ID:</span>
                  <div className="flex items-center space-x-2">
                    <code className="bg-gray-100 px-2 py-1 rounded text-sm">{user?.userId}</code>
                    <Button size="sm" variant="outline" onClick={copyUserId}>
                      <Copy className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
                
                {userProfile && (
                  <>
                    <div className="flex items-center justify-between">
                      <span className="font-medium">Name:</span>
                      <span>{userProfile.firstName} {userProfile.lastName}</span>
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <span className="font-medium">Email:</span>
                      <span>{userProfile.email}</span>
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <span className="font-medium">Current Role:</span>
                      <Badge variant={userProfile.isAdmin ? "default" : "secondary"}>
                        {userProfile.role || 'CUSTOMER'}
                      </Badge>
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <span className="font-medium">Admin Status:</span>
                      <div className="flex items-center space-x-2">
                        {userProfile.isSuperAdmin ? (
                          <Badge className="bg-purple-100 text-purple-800">
                            <Crown className="h-3 w-3 mr-1" />
                            Super Admin
                          </Badge>
                        ) : userProfile.isAdmin ? (
                          <Badge className="bg-blue-100 text-blue-800">
                            <Shield className="h-3 w-3 mr-1" />
                            Admin
                          </Badge>
                        ) : (
                          <Badge variant="outline">Not Admin</Badge>
                        )}
                      </div>
                    </div>
                  </>
                )}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Admin Actions - Always Show if Logged In */}
        {isAuthenticated && user && (
          <Card className="mb-6">
            <CardHeader>
              <CardTitle>Grant Admin Privileges</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {userProfile && (userProfile.isAdmin || userProfile.isSuperAdmin) ? (
                  <div className="text-center py-4">
                    <div className={`p-3 rounded-full w-16 h-16 mx-auto mb-4 ${
                      userProfile.isSuperAdmin ? 'bg-purple-100' : 'bg-blue-100'
                    }`}>
                      {userProfile.isSuperAdmin ? (
                        <Crown className="h-10 w-10 text-purple-600" />
                      ) : (
                        <Shield className="h-10 w-10 text-blue-600" />
                      )}
                    </div>
                    <p className="text-green-600 font-medium">
                      ✅ You already have {userProfile.isSuperAdmin ? 'Super Admin' : 'Admin'} privileges!
                    </p>
                  </div>
                ) : (
                  <>
                    <p className="text-gray-600">
                      Choose the level of admin privileges you want to grant yourself:
                    </p>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="border rounded-lg p-4">
                        <div className="flex items-center mb-2">
                          <Shield className="h-5 w-5 text-blue-600 mr-2" />
                          <h3 className="font-semibold">Admin</h3>
                        </div>
                        <p className="text-sm text-gray-600 mb-3">
                          Can moderate reviews, manage users, and access admin dashboards.
                        </p>
                        <Button
                          onClick={() => handleMakeMeAdmin('ADMIN')}
                          disabled={actionLoading}
                          className="w-full bg-blue-600 hover:bg-blue-700"
                        >
                          {actionLoading ? 'Processing...' : 'Make Me Admin'}
                        </Button>
                      </div>

                      <div className="border rounded-lg p-4">
                        <div className="flex items-center mb-2">
                          <Crown className="h-5 w-5 text-purple-600 mr-2" />
                          <h3 className="font-semibold">Super Admin</h3>
                        </div>
                        <p className="text-sm text-gray-600 mb-3">
                          Full system access including user management and system settings.
                        </p>
                        <Button
                          onClick={() => handleMakeMeAdmin('SUPER_ADMIN')}
                          disabled={actionLoading}
                          className="w-full bg-purple-600 hover:bg-purple-700"
                        >
                          {actionLoading ? 'Processing...' : 'Make Me Super Admin'}
                        </Button>
                      </div>
                    </div>

                    {/* Debug Info */}
                    <div className="mt-4 p-3 bg-gray-50 rounded-lg text-sm">
                      <p><strong>Debug Info:</strong></p>
                      <p>• User ID: {user?.userId || 'Not found'}</p>
                      <p>• Profile Loaded: {userProfile ? 'Yes' : 'No'}</p>
                      <p>• Is Admin: {userProfile?.isAdmin ? 'Yes' : 'No'}</p>
                      <p>• Is Super Admin: {userProfile?.isSuperAdmin ? 'Yes' : 'No'}</p>
                    </div>
                  </>
                )}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Already Admin */}
        {userProfile && (userProfile.isAdmin || userProfile.isSuperAdmin) && (
          <Card className="mb-6">
            <CardContent className="p-6 text-center">
              <div className={`p-3 rounded-full w-16 h-16 mx-auto mb-4 ${
                userProfile.isSuperAdmin ? 'bg-purple-100' : 'bg-blue-100'
              }`}>
                {userProfile.isSuperAdmin ? (
                  <Crown className="h-10 w-10 text-purple-600" />
                ) : (
                  <Shield className="h-10 w-10 text-blue-600" />
                )}
              </div>
              <h2 className="text-xl font-semibold mb-2">
                You're already an {userProfile.isSuperAdmin ? 'Super Admin' : 'Admin'}!
              </h2>
              <p className="text-gray-600 mb-4">
                You have admin privileges and can access the admin dashboard.
              </p>
              <div className="flex justify-center space-x-4">
                <Button asChild>
                  <a href="/admin">Go to Admin Dashboard</a>
                </Button>
                <Button variant="outline" asChild>
                  <a href="/dashboard/admin-reviews">Manage Reviews</a>
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Emergency Admin Section */}
        <Card className="mb-6 border-orange-200">
          <CardHeader>
            <CardTitle className="text-orange-800">Emergency Admin Access</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <p className="text-orange-700">
                If the buttons above don't work, you can manually make yourself admin using these steps:
              </p>

              <div className="bg-orange-50 p-4 rounded-lg">
                <h4 className="font-semibold text-orange-800 mb-2">Manual Method:</h4>
                <ol className="list-decimal list-inside space-y-2 text-sm text-orange-700">
                  <li>Go to <a href="/admin/users" className="underline text-blue-600">/admin/users</a></li>
                  <li>Use the "Make User Admin" form</li>
                  <li>Enter your User ID: <code className="bg-white px-1 rounded">{user?.userId || 'Login first'}</code></li>
                  <li>Select admin level and click "Make Admin"</li>
                </ol>
              </div>

              <div className="flex space-x-2">
                <Button asChild variant="outline" className="border-orange-300 text-orange-700">
                  <a href="/admin/users">Go to User Management</a>
                </Button>
                <Button asChild variant="outline" className="border-blue-300 text-blue-700">
                  <a href="/login">Login First</a>
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Instructions */}
        <Card>
          <CardHeader>
            <CardTitle>Instructions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3 text-sm">
              <p><strong>1.</strong> Make sure you're logged in with the account you want to make admin</p>
              <p><strong>2.</strong> Click either "Make Me Admin" or "Make Me Super Admin" above</p>
              <p><strong>3.</strong> Once granted, you can access the admin dashboard at <code>/admin</code></p>
              <p><strong>4.</strong> Use the admin dashboard to manage other users and reviews</p>

              <div className="mt-4 p-3 bg-yellow-50 rounded-lg">
                <p className="text-yellow-800">
                  <strong>Note:</strong> This is a development utility. In production, admin privileges should be granted through proper administrative processes.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
