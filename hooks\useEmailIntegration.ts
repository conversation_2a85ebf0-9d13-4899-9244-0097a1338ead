/**
 * Email Integration Hook for BookmyFestive
 * Provides easy integration of email functionality across the application
 */

import { useCallback } from 'react';
import { emailService } from '@/lib/services/emailService';
import { useAuth } from '@/contexts/AuthContext';

export interface EmailIntegrationHook {
  // 1. Login OTP
  sendLoginOTP: (email: string, otp: string, userName?: string) => Promise<boolean>;
  
  // 2. Welcome Signup
  sendWelcomeEmail: (userData: {
    email: string;
    firstName?: string;
    lastName?: string;
    isVendor?: boolean;
    businessName?: string;
  }) => Promise<boolean>;
  
  // 3. Newsletter Subscription
  sendNewsletterWelcome: (userData: {
    email: string;
    firstName?: string;
    lastName?: string;
    interests?: string[];
    preferences?: any;
  }) => Promise<boolean>;
  
  // 4. Booking Confirmation
  sendBookingConfirmation: (bookingData: {
    email: string;
    userName: string;
    bookingId: string;
    entityName: string;
    entityType: 'vendor' | 'venue';
    eventDate: string;
    eventTime?: string;
    amount?: string;
    status: string;
  }) => Promise<boolean>;
  
  // 5. Payment Success
  sendPaymentSuccess: (paymentData: {
    email: string;
    userName: string;
    orderId: string;
    amount: string;
    items: Array<{
      name: string;
      quantity: number;
      price: string;
    }>;
    invoiceUrl?: string;
  }) => Promise<boolean>;
  
  // 6. Weekly News
  sendWeeklyNews: (newsData: {
    email: string;
    userName?: string;
    articles: Array<{
      title: string;
      excerpt: string;
      url: string;
      image?: string;
    }>;
    offers?: Array<{
      title: string;
      discount: string;
      url: string;
    }>;
  }) => Promise<boolean>;
  
  // 7. Offers Email
  sendOffers: (offersData: {
    email: string;
    userName: string;
    offers: Array<{
      title: string;
      description: string;
      discount: string;
      validUntil: string;
      url: string;
      image?: string;
    }>;
  }) => Promise<boolean>;
  
  // 8. Favorites Notification
  sendFavoritesNotification: (favoritesData: {
    email: string;
    userName: string;
    updates: Array<{
      name: string;
      type: 'vendor' | 'venue' | 'product';
      updateType: 'price_drop' | 'new_photos' | 'availability' | 'offer';
      url: string;
      image?: string;
    }>;
  }) => Promise<boolean>;
  
  // 9. Vendor Launch
  sendVendorLaunch: (launchData: {
    email: string;
    userName: string;
    newVendors: Array<{
      name: string;
      category: string;
      city: string;
      discount?: string;
      url: string;
      image?: string;
    }>;
  }) => Promise<boolean>;
  
  // 10. Availability Check
  sendAvailabilityCheck: (availabilityData: {
    email: string;
    userName: string;
    entityName: string;
    entityType: 'vendor' | 'venue';
    requestedDate: string;
    status: 'available' | 'unavailable' | 'partially_available';
    alternativeDates?: string[];
    url: string;
  }) => Promise<boolean>;
}

export const useEmailIntegration = (): EmailIntegrationHook => {
  const { user, userProfile } = useAuth();

  const sendLoginOTP = useCallback(async (email: string, otp: string, userName?: string): Promise<boolean> => {
    try {
      return await emailService.sendLoginOTPEmail({
        email,
        userName: userName || email.split('@')[0],
        otp,
        expiryMinutes: 10
      });
    } catch (error) {
      console.error('Error sending login OTP:', error);
      return false;
    }
  }, []);

  const sendWelcomeEmail = useCallback(async (userData: {
    email: string;
    firstName?: string;
    lastName?: string;
    isVendor?: boolean;
    businessName?: string;
  }): Promise<boolean> => {
    try {
      return await emailService.sendWelcomeSignupEmail({
        email: userData.email,
        firstName: userData.firstName,
        lastName: userData.lastName,
        type: 'signup',
        isVendor: userData.isVendor,
        businessName: userData.businessName
      });
    } catch (error) {
      console.error('Error sending welcome email:', error);
      return false;
    }
  }, []);

  const sendNewsletterWelcome = useCallback(async (userData: {
    email: string;
    firstName?: string;
    lastName?: string;
    interests?: string[];
    preferences?: any;
  }): Promise<boolean> => {
    try {
      return await emailService.sendNewsletterWelcomeEmail({
        email: userData.email,
        firstName: userData.firstName,
        lastName: userData.lastName,
        type: 'newsletter',
        interests: userData.interests,
        preferences: userData.preferences
      });
    } catch (error) {
      console.error('Error sending newsletter welcome:', error);
      return false;
    }
  }, []);

  const sendBookingConfirmation = useCallback(async (bookingData: {
    email: string;
    userName: string;
    bookingId: string;
    entityName: string;
    entityType: 'vendor' | 'venue';
    eventDate: string;
    eventTime?: string;
    amount?: string;
    status: string;
  }): Promise<boolean> => {
    try {
      return await emailService.sendBookingConfirmationEmail({
        email: bookingData.email,
        userName: bookingData.userName,
        bookingId: bookingData.bookingId,
        entityName: bookingData.entityName,
        entityType: bookingData.entityType,
        eventDate: bookingData.eventDate,
        eventTime: bookingData.eventTime,
        amount: bookingData.amount,
        status: bookingData.status as any
      });
    } catch (error) {
      console.error('Error sending booking confirmation:', error);
      return false;
    }
  }, []);

  const sendPaymentSuccess = useCallback(async (paymentData: {
    email: string;
    userName: string;
    orderId: string;
    amount: string;
    items: Array<{
      name: string;
      quantity: number;
      price: string;
    }>;
    invoiceUrl?: string;
  }): Promise<boolean> => {
    try {
      return await emailService.sendPaymentSuccessEmail({
        email: paymentData.email,
        userName: paymentData.userName,
        orderId: paymentData.orderId,
        amount: paymentData.amount,
        items: paymentData.items,
        invoiceUrl: paymentData.invoiceUrl
      });
    } catch (error) {
      console.error('Error sending payment success email:', error);
      return false;
    }
  }, []);

  const sendWeeklyNews = useCallback(async (newsData: {
    email: string;
    userName?: string;
    articles: Array<{
      title: string;
      excerpt: string;
      url: string;
      image?: string;
    }>;
    offers?: Array<{
      title: string;
      discount: string;
      url: string;
    }>;
  }): Promise<boolean> => {
    try {
      return await emailService.sendWeeklyNewsEmail({
        email: newsData.email,
        userName: newsData.userName,
        articles: newsData.articles,
        offers: newsData.offers
      });
    } catch (error) {
      console.error('Error sending weekly news:', error);
      return false;
    }
  }, []);

  const sendOffers = useCallback(async (offersData: {
    email: string;
    userName: string;
    offers: Array<{
      title: string;
      description: string;
      discount: string;
      validUntil: string;
      url: string;
      image?: string;
    }>;
  }): Promise<boolean> => {
    try {
      return await emailService.sendOffersEmail({
        email: offersData.email,
        userName: offersData.userName,
        offers: offersData.offers
      });
    } catch (error) {
      console.error('Error sending offers email:', error);
      return false;
    }
  }, []);

  const sendFavoritesNotification = useCallback(async (favoritesData: {
    email: string;
    userName: string;
    updates: Array<{
      name: string;
      type: 'vendor' | 'venue' | 'product';
      updateType: 'price_drop' | 'new_photos' | 'availability' | 'offer';
      url: string;
      image?: string;
    }>;
  }): Promise<boolean> => {
    try {
      return await emailService.sendFavoritesNotificationEmail({
        email: favoritesData.email,
        userName: favoritesData.userName,
        updates: favoritesData.updates
      });
    } catch (error) {
      console.error('Error sending favorites notification:', error);
      return false;
    }
  }, []);

  const sendVendorLaunch = useCallback(async (launchData: {
    email: string;
    userName: string;
    newVendors: Array<{
      name: string;
      category: string;
      city: string;
      discount?: string;
      url: string;
      image?: string;
    }>;
  }): Promise<boolean> => {
    try {
      return await emailService.sendVendorLaunchEmail({
        email: launchData.email,
        userName: launchData.userName,
        newVendors: launchData.newVendors
      });
    } catch (error) {
      console.error('Error sending vendor launch email:', error);
      return false;
    }
  }, []);

  const sendAvailabilityCheck = useCallback(async (availabilityData: {
    email: string;
    userName: string;
    entityName: string;
    entityType: 'vendor' | 'venue';
    requestedDate: string;
    status: 'available' | 'unavailable' | 'partially_available';
    alternativeDates?: string[];
    url: string;
  }): Promise<boolean> => {
    try {
      return await emailService.sendAvailabilityCheckEmail({
        email: availabilityData.email,
        userName: availabilityData.userName,
        entityName: availabilityData.entityName,
        entityType: availabilityData.entityType,
        requestedDate: availabilityData.requestedDate,
        status: availabilityData.status,
        alternativeDates: availabilityData.alternativeDates,
        url: availabilityData.url
      });
    } catch (error) {
      console.error('Error sending availability check email:', error);
      return false;
    }
  }, []);

  return {
    sendLoginOTP,
    sendWelcomeEmail,
    sendNewsletterWelcome,
    sendBookingConfirmation,
    sendPaymentSuccess,
    sendWeeklyNews,
    sendOffers,
    sendFavoritesNotification,
    sendVendorLaunch,
    sendAvailabilityCheck
  };
};
