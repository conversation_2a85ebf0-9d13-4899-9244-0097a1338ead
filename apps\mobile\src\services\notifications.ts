import * as Notifications from 'expo-notifications';
import * as Device from 'expo-device';
import Constants from 'expo-constants';
import { Platform } from 'react-native';

// Configure notification behavior
Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowAlert: true,
    shouldPlaySound: true,
    shouldSetBadge: false,
  }),
});

export async function setupNotifications() {
  let token;

  if (Platform.OS === 'android') {
    await Notifications.setNotificationChannelAsync('default', {
      name: 'default',
      importance: Notifications.AndroidImportance.MAX,
      vibrationPattern: [0, 250, 250, 250],
      lightColor: '#FF231F7C',
    });
  }

  if (Device.isDevice) {
    const { status: existingStatus } = await Notifications.getPermissionsAsync();
    let finalStatus = existingStatus;
    
    if (existingStatus !== 'granted') {
      const { status } = await Notifications.requestPermissionsAsync();
      finalStatus = status;
    }
    
    if (finalStatus !== 'granted') {
      console.log('Failed to get push token for push notification!');
      return;
    }
    
    try {
      const projectId = Constants.expoConfig?.extra?.eas?.projectId ?? Constants.easConfig?.projectId;
      if (!projectId) {
        throw new Error('Project ID not found');
      }
      
      token = (await Notifications.getExpoPushTokenAsync({
        projectId,
      })).data;
      
      console.log('Push notification token:', token);
    } catch (e) {
      console.error('Error getting push token:', e);
    }
  } else {
    console.log('Must use physical device for Push Notifications');
  }

  return token;
}

export async function sendLocalNotification(title: string, body: string, data?: any) {
  await Notifications.scheduleNotificationAsync({
    content: {
      title,
      body,
      data,
    },
    trigger: null, // Send immediately
  });
}

export async function scheduleNotification(
  title: string,
  body: string,
  trigger: Notifications.NotificationTriggerInput,
  data?: any
) {
  await Notifications.scheduleNotificationAsync({
    content: {
      title,
      body,
      data,
    },
    trigger,
  });
}

export async function cancelAllNotifications() {
  await Notifications.cancelAllScheduledNotificationsAsync();
}

export async function cancelNotification(identifier: string) {
  await Notifications.cancelScheduledNotificationAsync(identifier);
}

// Wedding-specific notification helpers
export async function scheduleWeddingReminder(
  weddingDate: Date,
  reminderType: 'week' | 'day' | 'hour'
) {
  const now = new Date();
  const wedding = new Date(weddingDate);
  
  let triggerDate: Date;
  let title: string;
  let body: string;
  
  switch (reminderType) {
    case 'week':
      triggerDate = new Date(wedding.getTime() - 7 * 24 * 60 * 60 * 1000);
      title = 'Wedding Week!';
      body = 'Your special day is just one week away! 💒';
      break;
    case 'day':
      triggerDate = new Date(wedding.getTime() - 24 * 60 * 60 * 1000);
      title = 'Tomorrow is the Big Day!';
      body = 'Your wedding is tomorrow! Final preparations time! 🎉';
      break;
    case 'hour':
      triggerDate = new Date(wedding.getTime() - 60 * 60 * 1000);
      title = 'Wedding Hour!';
      body = 'Your wedding ceremony starts in 1 hour! 💍';
      break;
  }
  
  if (triggerDate > now) {
    await scheduleNotification(
      title,
      body,
      { date: triggerDate },
      { type: 'wedding_reminder', reminderType }
    );
  }
}

export async function notifyVendorResponse(vendorName: string, responseType: 'accepted' | 'declined') {
  const title = responseType === 'accepted' ? 'Booking Confirmed!' : 'Booking Update';
  const body = responseType === 'accepted' 
    ? `${vendorName} has confirmed your booking! 🎉`
    : `${vendorName} has responded to your inquiry.`;
    
  await sendLocalNotification(title, body, {
    type: 'vendor_response',
    vendorName,
    responseType,
  });
}

export async function notifyNewMessage(senderName: string, message: string) {
  await sendLocalNotification(
    `Message from ${senderName}`,
    message,
    {
      type: 'new_message',
      senderName,
    }
  );
}

export async function notifyPaymentReminder(vendorName: string, amount: number, dueDate: Date) {
  const title = 'Payment Reminder';
  const body = `Payment of ₹${amount.toLocaleString()} to ${vendorName} is due soon.`;

  // Schedule reminder 3 days before due date
  const reminderDate = new Date(dueDate.getTime() - 3 * 24 * 60 * 60 * 1000);

  if (reminderDate > new Date()) {
    await scheduleNotification(
      title,
      body,
      { date: reminderDate },
      {
        type: 'payment_reminder',
        vendorName,
        amount,
        dueDate: dueDate.toISOString(),
      }
    );
  }
}

// Enhanced notification functions for e-commerce and booking

export async function notifyOrderStatusUpdate(
  orderId: string,
  status: 'confirmed' | 'processing' | 'shipped' | 'delivered' | 'cancelled',
  customerName?: string
) {
  const statusMessages = {
    confirmed: {
      title: 'Order Confirmed! 🎉',
      body: `Your order #${orderId} has been confirmed and is being prepared.`,
    },
    processing: {
      title: 'Order Processing 📦',
      body: `Your order #${orderId} is now being processed.`,
    },
    shipped: {
      title: 'Order Shipped! 🚚',
      body: `Your order #${orderId} has been shipped and is on its way.`,
    },
    delivered: {
      title: 'Order Delivered! ✅',
      body: `Your order #${orderId} has been delivered successfully.`,
    },
    cancelled: {
      title: 'Order Cancelled ❌',
      body: `Your order #${orderId} has been cancelled.`,
    },
  };

  const message = statusMessages[status];
  await sendLocalNotification(message.title, message.body, {
    type: 'order_update',
    orderId,
    status,
    customerName,
  });
}

export async function notifyBookingConfirmation(
  bookingId: string,
  vendorName: string,
  serviceDate: Date,
  serviceName: string
) {
  const title = 'Booking Confirmed! 🎊';
  const body = `Your booking with ${vendorName} for ${serviceName} on ${serviceDate.toLocaleDateString()} has been confirmed.`;

  await sendLocalNotification(title, body, {
    type: 'booking_confirmation',
    bookingId,
    vendorName,
    serviceDate: serviceDate.toISOString(),
    serviceName,
  });
}

export async function notifyPaymentSuccess(orderId: string, amount: number, paymentMethod: string) {
  const title = 'Payment Successful! 💳';
  const body = `Payment of ₹${amount.toLocaleString()} for order #${orderId} was successful via ${paymentMethod}.`;

  await sendLocalNotification(title, body, {
    type: 'payment_success',
    orderId,
    amount,
    paymentMethod,
  });
}

export async function notifyPaymentFailed(orderId: string, amount: number, reason?: string) {
  const title = 'Payment Failed ❌';
  const body = `Payment of ₹${amount.toLocaleString()} for order #${orderId} failed. ${reason || 'Please try again.'}`;

  await sendLocalNotification(title, body, {
    type: 'payment_failed',
    orderId,
    amount,
    reason,
  });
}

export async function notifyVendorNewOrder(
  vendorId: string,
  orderId: string,
  customerName: string,
  amount: number
) {
  const title = 'New Order Received! 🛍️';
  const body = `New order #${orderId} from ${customerName} for ₹${amount.toLocaleString()}.`;

  await sendLocalNotification(title, body, {
    type: 'vendor_new_order',
    vendorId,
    orderId,
    customerName,
    amount,
  });
}

export async function notifyPromotionalOffer(
  title: string,
  message: string,
  offerCode?: string,
  validUntil?: Date
) {
  await sendLocalNotification(title, message, {
    type: 'promotion',
    offerCode,
    validUntil: validUntil?.toISOString(),
  });
}

export async function notifySystemMaintenance(
  maintenanceDate: Date,
  duration: string,
  affectedServices: string[]
) {
  const title = 'Scheduled Maintenance 🔧';
  const body = `System maintenance scheduled for ${maintenanceDate.toLocaleDateString()} (${duration}). Some services may be temporarily unavailable.`;

  // Schedule notification 24 hours before maintenance
  const notificationDate = new Date(maintenanceDate.getTime() - 24 * 60 * 60 * 1000);

  if (notificationDate > new Date()) {
    await scheduleNotification(
      title,
      body,
      { date: notificationDate },
      {
        type: 'system_maintenance',
        maintenanceDate: maintenanceDate.toISOString(),
        duration,
        affectedServices,
      }
    );
  }
}

// Batch notification functions
export async function scheduleWeddingPlanningReminders(weddingDate: Date, coupleName: string) {
  const reminders = [
    { days: 365, title: '1 Year to Go! 💍', message: 'Start planning Your Dream Celebration!' },
    { days: 180, title: '6 Months to Go! 📅', message: 'Time to finalize major vendors and venues.' },
    { days: 90, title: '3 Months to Go! 🎊', message: 'Send invitations and finalize guest list.' },
    { days: 30, title: '1 Month to Go! 🌟', message: 'Final preparations and confirmations.' },
    { days: 7, title: 'Wedding Week! 💒', message: 'Your special week has arrived!' },
    { days: 1, title: 'Tomorrow is the Big Day! 🎉', message: 'Final preparations time!' },
  ];

  for (const reminder of reminders) {
    const reminderDate = new Date(weddingDate.getTime() - reminder.days * 24 * 60 * 60 * 1000);

    if (reminderDate > new Date()) {
      await scheduleNotification(
        reminder.title,
        `${coupleName}, ${reminder.message}`,
        { date: reminderDate },
        {
          type: 'wedding_planning_reminder',
          weddingDate: weddingDate.toISOString(),
          daysUntilWedding: reminder.days,
          coupleName,
        }
      );
    }
  }
}

export async function cancelWeddingReminders(weddingDate: Date) {
  // Cancel all scheduled wedding reminders
  const scheduledNotifications = await Notifications.getAllScheduledNotificationsAsync();

  for (const notification of scheduledNotifications) {
    if (notification.content.data?.type === 'wedding_planning_reminder' &&
        notification.content.data?.weddingDate === weddingDate.toISOString()) {
      await Notifications.cancelScheduledNotificationAsync(notification.identifier);
    }
  }
}
