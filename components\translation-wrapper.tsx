"use client"

import { useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'
import '@/lib/i18n'

interface TranslationWrapperProps {
  translationKey: string
  fallback?: string
  className?: string
  style?: React.CSSProperties
  as?: keyof JSX.IntrinsicElements
  [key: string]: any
}

export function TranslationWrapper({
  translationKey,
  fallback = '',
  className = '',
  style,
  as: Component = 'span',
  ...props
}: TranslationWrapperProps) {
  const { t, ready } = useTranslation()
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
  }, [])

  // Show fallback during SSR and initial client render
  if (!mounted || !ready) {
    return (
      <Component className={className} style={style} {...props}>
        {fallback}
      </Component>
    )
  }

  return (
    <Component className={className} style={style} {...props}>
      {t(translationKey)}
    </Component>
  )
}

// Convenience components for common use cases
export function TranslatedText({ translationKey, fallback = '', className = '' }: {
  translationKey: string
  fallback?: string
  className?: string
}) {
  return (
    <TranslationWrapper 
      translationKey={translationKey} 
      fallback={fallback}
      className={className}
      as="span"
    />
  )
}

export function TranslatedHeading({ translationKey, fallback = '', className = '', level = 1 }: {
  translationKey: string
  fallback?: string
  className?: string
  level?: 1 | 2 | 3 | 4 | 5 | 6
}) {
  const HeadingComponent = `h${level}` as keyof JSX.IntrinsicElements
  
  return (
    <TranslationWrapper 
      translationKey={translationKey} 
      fallback={fallback}
      className={className}
      as={HeadingComponent}
    />
  )
}
