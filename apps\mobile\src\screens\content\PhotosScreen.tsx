import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Image,
  TouchableOpacity,
  FlatList,
  Dimensions,
  Modal
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../../providers/ThemeProvider';
import { Header } from '../../components/Header';
import { Card, Input, Button } from '../../components/ui';

const { width: screenWidth } = Dimensions.get('window');
const imageWidth = (screenWidth - 48) / 2; // 2 columns with padding

interface Photo {
  id: string;
  url: string;
  title: string;
  category: string;
  photographer: string;
  likes: number;
  isLiked: boolean;
  tags: string[];
}

interface PhotoCategory {
  id: string;
  name: string;
  count: number;
}

export default function PhotosScreen() {
  const { theme } = useTheme();
  const [photos, setPhotos] = useState<Photo[]>([]);
  const [categories, setCategories] = useState<PhotoCategory[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [loading, setLoading] = useState(true);
  const [selectedPhoto, setSelectedPhoto] = useState<Photo | null>(null);
  const [modalVisible, setModalVisible] = useState(false);

  useEffect(() => {
    loadPhotos();
    loadCategories();
  }, []);

  const loadPhotos = async () => {
    try {
      setLoading(true);
      // TODO: Replace with actual API call
      const mockPhotos: Photo[] = [
        {
          id: '1',
          url: '/placeholder-image.jpg
          title: 'Traditional South Indian Wedding',
          category: 'ceremony',
          photographer: 'Rajesh Kumar',
          likes: 245,
          isLiked: false,
          tags: ['traditional', 'south indian', 'ceremony']
        },
        {
          id: '2',
          url: '/placeholder-image.jpg
          title: 'Bridal Mehendi Design',
          category: 'mehendi',
          photographer: 'Priya Sharma',
          likes: 189,
          isLiked: true,
          tags: ['mehendi', 'bridal', 'henna']
        },
        {
          id: '3',
          url: '/placeholder-image.jpg
          title: 'Wedding Mandap Decoration',
          category: 'decoration',
          photographer: 'Amit Patel',
          likes: 312,
          isLiked: false,
          tags: ['mandap', 'decoration', 'flowers']
        },
        {
          id: '4',
          url: '/placeholder-image.jpg
          title: 'Couple Portrait Session',
          category: 'portraits',
          photographer: 'Neha Singh',
          likes: 156,
          isLiked: false,
          tags: ['couple', 'portrait', 'romantic']
        },
        {
          id: '5',
          url: '/placeholder-image.jpg
          title: 'Wedding Reception Setup',
          category: 'reception',
          photographer: 'Vikram Reddy',
          likes: 278,
          isLiked: true,
          tags: ['reception', 'setup', 'elegant']
        },
        {
          id: '6',
          url: '/placeholder-image.jpg
          title: 'Bridal Jewelry Collection',
          category: 'jewelry',
          photographer: 'Kavya Nair',
          likes: 203,
          isLiked: false,
          tags: ['jewelry', 'bridal', 'gold']
        }
      ];

      setPhotos(mockPhotos);
    } catch (error) {
      console.error('Error loading photos:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadCategories = async () => {
    try {
      // TODO: Replace with actual API call
      const mockCategories: PhotoCategory[] = [
        { id: 'all', name: 'All Photos', count: 156 },
        { id: 'ceremony', name: 'Ceremony', count: 45 },
        { id: 'mehendi', name: 'Mehendi', count: 32 },
        { id: 'decoration', name: 'Decoration', count: 28 },
        { id: 'portraits', name: 'Portraits', count: 23 },
        { id: 'reception', name: 'Reception', count: 18 },
        { id: 'jewelry', name: 'Jewelry', count: 10 }
      ];

      setCategories(mockCategories);
    } catch (error) {
      console.error('Error loading categories:', error);
    }
  };

  const filteredPhotos = photos.filter(photo => {
    const matchesCategory = selectedCategory === 'all' || photo.category === selectedCategory;
    const matchesSearch = photo.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         photo.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));
    return matchesCategory && matchesSearch;
  });

  const handlePhotoPress = (photo: Photo) => {
    setSelectedPhoto(photo);
    setModalVisible(true);
  };

  const handleLike = (photoId: string) => {
    setPhotos(prevPhotos =>
      prevPhotos.map(photo =>
        photo.id === photoId
          ? { ...photo, isLiked: !photo.isLiked, likes: photo.isLiked ? photo.likes - 1 : photo.likes + 1 }
          : photo
      )
    );
  };

  const renderPhoto = ({ item }: { item: Photo }) => (
    <TouchableOpacity
      style={styles.photoContainer}
      onPress={() => handlePhotoPress(item)}
    >
      <Image source={{ uri: item.url }} style={styles.photoImage} />
      <View style={styles.photoOverlay}>
        <TouchableOpacity
          style={styles.likeButton}
          onPress={() => handleLike(item.id)}
        >
          <Ionicons
            name={item.isLiked ? "heart" : "heart-outline"}
            size={20}
            color={item.isLiked ? theme.colors.destructive : "white"}
          />
        </TouchableOpacity>
      </View>
      <View style={styles.photoInfo}>
        <Text style={[styles.photoTitle, { color: theme.colors.text }]} numberOfLines={2}>
          {item.title}
        </Text>
        <View style={styles.photoMeta}>
          <Text style={[styles.photographer, { color: theme.colors.textSecondary }]}>
            {item.photographer}
          </Text>
          <View style={styles.likesContainer}>
            <Ionicons name="heart" size={12} color={theme.colors.destructive} />
            <Text style={[styles.likesCount, { color: theme.colors.textSecondary }]}>
              {item.likes}
            </Text>
          </View>
        </View>
      </View>
    </TouchableOpacity>
  );

  const renderCategory = ({ item }: { item: PhotoCategory }) => (
    <TouchableOpacity
      style={[
        styles.categoryButton,
        {
          backgroundColor: selectedCategory === item.id ? theme.colors.primary : theme.colors.surface,
          borderColor: theme.colors.border
        }
      ]}
      onPress={() => setSelectedCategory(item.id)}
    >
      <Text style={[
        styles.categoryText,
        {
          color: selectedCategory === item.id ? theme.colors.primaryForeground : theme.colors.text
        }
      ]}>
        {item.name}
      </Text>
      <Text style={[
        styles.categoryCount,
        {
          color: selectedCategory === item.id ? theme.colors.primaryForeground : theme.colors.textSecondary
        }
      ]}>
        {item.count}
      </Text>
    </TouchableOpacity>
  );

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <Header title="Wedding Photos" showBack showSearch={false} />

      {/* Search Bar */}
      <View style={styles.searchContainer}>
        <Input
          placeholder="Search photos..."
          value={searchQuery}
          onChangeText={setSearchQuery}
          leftIcon={<Ionicons name="search" size={20} color={theme.colors.textSecondary} />}
        />
      </View>

      {/* Categories */}
      <View style={styles.categoriesContainer}>
        <FlatList
          data={categories}
          renderItem={renderCategory}
          keyExtractor={(item) => item.id}
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.categoriesList}
        />
      </View>

      {/* Photos Grid */}
      <FlatList
        data={filteredPhotos}
        renderItem={renderPhoto}
        keyExtractor={(item) => item.id}
        numColumns={2}
        contentContainerStyle={styles.photosList}
        showsVerticalScrollIndicator={false}
      />

      {/* Photo Detail Modal */}
      <Modal
        visible={modalVisible}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setModalVisible(false)}
      >
        <View style={styles.modalContainer}>
          <TouchableOpacity
            style={styles.modalBackdrop}
            onPress={() => setModalVisible(false)}
          >
            <View style={styles.modalContent}>
              {selectedPhoto && (
                <>
                  <Image source={{ uri: selectedPhoto.url }} style={styles.modalImage} />
                  <View style={styles.modalInfo}>
                    <Text style={[styles.modalTitle, { color: theme.colors.text }]}>
                      {selectedPhoto.title}
                    </Text>
                    <Text style={[styles.modalPhotographer, { color: theme.colors.textSecondary }]}>
                      By {selectedPhoto.photographer}
                    </Text>
                    <View style={styles.modalTags}>
                      {selectedPhoto.tags.map((tag, index) => (
                        <View
                          key={index}
                          style={[styles.tag, { backgroundColor: theme.colors.surface, borderColor: theme.colors.border }]}
                        >
                          <Text style={[styles.tagText, { color: theme.colors.textSecondary }]}>
                            #{tag}
                          </Text>
                        </View>
                      ))}
                    </View>
                  </View>
                </>
              )}
            </View>
          </TouchableOpacity>
        </View>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  searchContainer: {
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  categoriesContainer: {
    paddingVertical: 8,
  },
  categoriesList: {
    paddingHorizontal: 16,
  },
  categoryButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginRight: 12,
    borderRadius: 20,
    borderWidth: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  categoryText: {
    fontSize: 14,
    fontWeight: '500',
    marginRight: 4,
  },
  categoryCount: {
    fontSize: 12,
  },
  photosList: {
    paddingHorizontal: 16,
    paddingBottom: 20,
  },
  photoContainer: {
    width: imageWidth,
    marginRight: 16,
    marginBottom: 16,
  },
  photoImage: {
    width: imageWidth,
    height: imageWidth * 1.2,
    borderRadius: 12,
    resizeMode: 'cover',
  },
  photoOverlay: {
    position: 'absolute',
    top: 8,
    right: 8,
  },
  likeButton: {
    backgroundColor: 'rgba(0,0,0,0.5)',
    borderRadius: 20,
    padding: 8,
  },
  photoInfo: {
    paddingTop: 8,
  },
  photoTitle: {
    fontSize: 14,
    fontWeight: '600',
    lineHeight: 18,
    marginBottom: 4,
  },
  photoMeta: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  photographer: {
    fontSize: 12,
    flex: 1,
  },
  likesContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  likesCount: {
    fontSize: 12,
    marginLeft: 4,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.9)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalBackdrop: {
    flex: 1,
    width: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    width: screenWidth - 40,
    backgroundColor: 'white',
    borderRadius: 16,
    overflow: 'hidden',
  },
  modalImage: {
    width: '100%',
    height: 300,
    resizeMode: 'cover',
  },
  modalInfo: {
    padding: 20,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  modalPhotographer: {
    fontSize: 14,
    marginBottom: 12,
  },
  modalTags: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  tag: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    borderWidth: 1,
    marginRight: 8,
    marginBottom: 8,
  },
  tagText: {
    fontSize: 12,
  },
});
