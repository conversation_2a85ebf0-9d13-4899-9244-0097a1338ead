import { MMKV } from 'react-native-mmkv';
import AsyncStorage from '@react-native-async-storage/async-storage';

export interface CacheOptions {
  ttl?: number; // Time to live in milliseconds
  priority?: 'low' | 'normal' | 'high';
  compress?: boolean;
  encrypt?: boolean;
}

export interface CacheItem<T = any> {
  data: T;
  timestamp: number;
  ttl?: number;
  priority: 'low' | 'normal' | 'high';
  size: number;
  accessCount: number;
  lastAccessed: number;
}

export interface CacheStats {
  totalItems: number;
  totalSize: number;
  hitRate: number;
  missRate: number;
  evictionCount: number;
}

class CacheService {
  private mmkv: MMKV;
  private fallbackStorage: typeof AsyncStorage;
  private stats: CacheStats;
  private maxSize: number = 50 * 1024 * 1024; // 50MB default
  private maxItems: number = 1000;
  private hitCount: number = 0;
  private missCount: number = 0;
  private evictionCount: number = 0;

  constructor() {
    try {
      this.mmkv = new MMKV({
        id: 'thirumanam-cache',
        encryptionKey: 'thirumanam-cache-key-2024',
      });
    } catch (error) {
      console.warn('MMKV initialization failed, falling back to AsyncStorage:', error);
      this.mmkv = null as any;
    }
    
    this.fallbackStorage = AsyncStorage;
    this.stats = {
      totalItems: 0,
      totalSize: 0,
      hitRate: 0,
      missRate: 0,
      evictionCount: 0,
    };
    
    this.initializeStats();
  }

  private async initializeStats() {
    try {
      const statsData = await this.getFromStorage('__cache_stats__');
      if (statsData) {
        this.stats = { ...this.stats, ...JSON.parse(statsData) };
      }
    } catch (error) {
      console.error('Error initializing cache stats:', error);
    }
  }

  private async getFromStorage(key: string): Promise<string | null> {
    try {
      if (this.mmkv) {
        return this.mmkv.getString(key) || null;
      }
      return await this.fallbackStorage.getItem(key);
    } catch (error) {
      console.error('Error getting from storage:', error);
      return null;
    }
  }

  private async setToStorage(key: string, value: string): Promise<void> {
    try {
      if (this.mmkv) {
        this.mmkv.set(key, value);
      } else {
        await this.fallbackStorage.setItem(key, value);
      }
    } catch (error) {
      console.error('Error setting to storage:', error);
      throw error;
    }
  }

  private async removeFromStorage(key: string): Promise<void> {
    try {
      if (this.mmkv) {
        this.mmkv.delete(key);
      } else {
        await this.fallbackStorage.removeItem(key);
      }
    } catch (error) {
      console.error('Error removing from storage:', error);
    }
  }

  private async getAllKeys(): Promise<string[]> {
    try {
      if (this.mmkv) {
        return this.mmkv.getAllKeys();
      }
      return await this.fallbackStorage.getAllKeys();
    } catch (error) {
      console.error('Error getting all keys:', error);
      return [];
    }
  }

  private calculateSize(data: any): number {
    try {
      return JSON.stringify(data).length * 2; // Rough estimate (UTF-16)
    } catch {
      return 0;
    }
  }

  private generateCacheKey(key: string): string {
    return `cache_${key}`;
  }

  private isExpired(item: CacheItem): boolean {
    if (!item.ttl) return false;
    return Date.now() - item.timestamp > item.ttl;
  }

  private async updateStats() {
    this.stats.hitRate = this.hitCount / (this.hitCount + this.missCount) * 100;
    this.stats.missRate = this.missCount / (this.hitCount + this.missCount) * 100;
    this.stats.evictionCount = this.evictionCount;
    
    try {
      await this.setToStorage('__cache_stats__', JSON.stringify(this.stats));
    } catch (error) {
      console.error('Error updating cache stats:', error);
    }
  }

  public async set<T>(key: string, data: T, options: CacheOptions = {}): Promise<void> {
    try {
      const cacheKey = this.generateCacheKey(key);
      const size = this.calculateSize(data);
      
      const cacheItem: CacheItem<T> = {
        data,
        timestamp: Date.now(),
        ttl: options.ttl,
        priority: options.priority || 'normal',
        size,
        accessCount: 0,
        lastAccessed: Date.now(),
      };

      // Check if we need to evict items
      await this.evictIfNeeded(size);

      await this.setToStorage(cacheKey, JSON.stringify(cacheItem));
      
      this.stats.totalItems++;
      this.stats.totalSize += size;
      await this.updateStats();
    } catch (error) {
      console.error('Error setting cache item:', error);
      throw error;
    }
  }

  public async get<T>(key: string): Promise<T | null> {
    try {
      const cacheKey = this.generateCacheKey(key);
      const itemData = await this.getFromStorage(cacheKey);
      
      if (!itemData) {
        this.missCount++;
        await this.updateStats();
        return null;
      }

      const cacheItem: CacheItem<T> = JSON.parse(itemData);
      
      // Check if expired
      if (this.isExpired(cacheItem)) {
        await this.remove(key);
        this.missCount++;
        await this.updateStats();
        return null;
      }

      // Update access statistics
      cacheItem.accessCount++;
      cacheItem.lastAccessed = Date.now();
      await this.setToStorage(cacheKey, JSON.stringify(cacheItem));
      
      this.hitCount++;
      await this.updateStats();
      
      return cacheItem.data;
    } catch (error) {
      console.error('Error getting cache item:', error);
      this.missCount++;
      await this.updateStats();
      return null;
    }
  }

  public async remove(key: string): Promise<void> {
    try {
      const cacheKey = this.generateCacheKey(key);
      const itemData = await this.getFromStorage(cacheKey);
      
      if (itemData) {
        const cacheItem: CacheItem = JSON.parse(itemData);
        this.stats.totalItems--;
        this.stats.totalSize -= cacheItem.size;
      }
      
      await this.removeFromStorage(cacheKey);
      await this.updateStats();
    } catch (error) {
      console.error('Error removing cache item:', error);
    }
  }

  public async has(key: string): Promise<boolean> {
    try {
      const cacheKey = this.generateCacheKey(key);
      const itemData = await this.getFromStorage(cacheKey);
      
      if (!itemData) return false;
      
      const cacheItem: CacheItem = JSON.parse(itemData);
      return !this.isExpired(cacheItem);
    } catch (error) {
      console.error('Error checking cache item:', error);
      return false;
    }
  }

  public async clear(): Promise<void> {
    try {
      const keys = await this.getAllKeys();
      const cacheKeys = keys.filter(key => key.startsWith('cache_'));
      
      for (const key of cacheKeys) {
        await this.removeFromStorage(key);
      }
      
      this.stats.totalItems = 0;
      this.stats.totalSize = 0;
      await this.updateStats();
    } catch (error) {
      console.error('Error clearing cache:', error);
    }
  }

  private async evictIfNeeded(newItemSize: number): Promise<void> {
    try {
      // Check if we exceed limits
      if (this.stats.totalSize + newItemSize <= this.maxSize && 
          this.stats.totalItems < this.maxItems) {
        return;
      }

      const keys = await this.getAllKeys();
      const cacheKeys = keys.filter(key => key.startsWith('cache_'));
      
      // Get all cache items with metadata
      const items: Array<{ key: string; item: CacheItem; cacheKey: string }> = [];
      
      for (const cacheKey of cacheKeys) {
        const itemData = await this.getFromStorage(cacheKey);
        if (itemData) {
          try {
            const item: CacheItem = JSON.parse(itemData);
            items.push({
              key: cacheKey.replace('cache_', ''),
              item,
              cacheKey,
            });
          } catch (error) {
            // Remove corrupted items
            await this.removeFromStorage(cacheKey);
          }
        }
      }

      // Sort by priority and access patterns (LRU with priority)
      items.sort((a, b) => {
        // First by priority (low priority gets evicted first)
        const priorityOrder = { low: 0, normal: 1, high: 2 };
        const priorityDiff = priorityOrder[a.item.priority] - priorityOrder[b.item.priority];
        if (priorityDiff !== 0) return priorityDiff;
        
        // Then by last accessed time (least recently used first)
        return a.item.lastAccessed - b.item.lastAccessed;
      });

      // Evict items until we have enough space
      let freedSpace = 0;
      let freedItems = 0;
      
      for (const { cacheKey, item } of items) {
        if (this.stats.totalSize - freedSpace + newItemSize <= this.maxSize && 
            this.stats.totalItems - freedItems < this.maxItems) {
          break;
        }
        
        await this.removeFromStorage(cacheKey);
        freedSpace += item.size;
        freedItems++;
        this.evictionCount++;
      }
      
      this.stats.totalSize -= freedSpace;
      this.stats.totalItems -= freedItems;
    } catch (error) {
      console.error('Error during cache eviction:', error);
    }
  }

  public async getStats(): Promise<CacheStats> {
    await this.updateStats();
    return { ...this.stats };
  }

  public async cleanExpired(): Promise<number> {
    try {
      const keys = await this.getAllKeys();
      const cacheKeys = keys.filter(key => key.startsWith('cache_'));
      let cleanedCount = 0;
      
      for (const cacheKey of cacheKeys) {
        const itemData = await this.getFromStorage(cacheKey);
        if (itemData) {
          try {
            const cacheItem: CacheItem = JSON.parse(itemData);
            if (this.isExpired(cacheItem)) {
              await this.removeFromStorage(cacheKey);
              this.stats.totalItems--;
              this.stats.totalSize -= cacheItem.size;
              cleanedCount++;
            }
          } catch (error) {
            // Remove corrupted items
            await this.removeFromStorage(cacheKey);
            cleanedCount++;
          }
        }
      }
      
      await this.updateStats();
      return cleanedCount;
    } catch (error) {
      console.error('Error cleaning expired cache items:', error);
      return 0;
    }
  }

  // Configuration methods
  public setMaxSize(size: number): void {
    this.maxSize = size;
  }

  public setMaxItems(count: number): void {
    this.maxItems = count;
  }
}

// Singleton instance
export const cacheService = new CacheService();
export default CacheService;
