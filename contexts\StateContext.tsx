"use client"

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react'

// Indian states with their major cities (using translation keys)
export const INDIAN_STATES = [
  {
    name: 'Tamil Nadu',
    code: 'TN',
    language: 'Tamil',
    languageCode: 'ta',
    cities: [
      { nameKey: 'destinations.cities.chennai.name', venuesKey: 'destinations.cities.chennai.venues', taglineKey: 'destinations.cities.chennai.tagline' },
      { nameKey: 'destinations.cities.coimbatore.name', venuesKey: 'destinations.cities.coimbatore.venues', taglineKey: 'destinations.cities.coimbatore.tagline' },
      { nameKey: 'destinations.cities.madurai.name', venuesKey: 'destinations.cities.madurai.venues', taglineKey: 'destinations.cities.madurai.tagline' },
      { nameKey: 'destinations.cities.tirunelveli.name', venuesKey: 'destinations.cities.tirunelveli.venues', taglineKey: 'destinations.cities.tirunelveli.tagline' }
    ]
  },
  {
    name: 'Karnataka',
    code: 'KA',
    language: 'Kannada',
    languageCode: 'kn',
    cities: [
      { nameKey: 'destinations.cities.bangalore.name', venuesKey: 'destinations.cities.bangalore.venues', taglineKey: 'destinations.cities.bangalore.tagline' },
      { nameKey: 'destinations.cities.mysore.name', venuesKey: 'destinations.cities.mysore.venues', taglineKey: 'destinations.cities.mysore.tagline' },
      { nameKey: 'destinations.cities.mangalore.name', venuesKey: 'destinations.cities.mangalore.venues', taglineKey: 'destinations.cities.mangalore.tagline' },
      { nameKey: 'destinations.cities.hubli.name', venuesKey: 'destinations.cities.hubli.venues', taglineKey: 'destinations.cities.hubli.tagline' }
    ]
  },
  {
    name: 'Kerala',
    code: 'KL',
    language: 'Malayalam',
    languageCode: 'ml',
    cities: [
      { nameKey: 'destinations.cities.kochi.name', venuesKey: 'destinations.cities.kochi.venues', taglineKey: 'destinations.cities.kochi.tagline' },
      { nameKey: 'destinations.cities.thiruvananthapuram.name', venuesKey: 'destinations.cities.thiruvananthapuram.venues', taglineKey: 'destinations.cities.thiruvananthapuram.tagline' },
      { nameKey: 'destinations.cities.kozhikode.name', venuesKey: 'destinations.cities.kozhikode.venues', taglineKey: 'destinations.cities.kozhikode.tagline' },
      { nameKey: 'destinations.cities.thrissur.name', venuesKey: 'destinations.cities.thrissur.venues', taglineKey: 'destinations.cities.thrissur.tagline' }
    ]
  },
  {
    name: 'Andhra Pradesh',
    code: 'AP',
    language: 'Telugu',
    languageCode: 'te',
    cities: [
      { nameKey: 'destinations.cities.visakhapatnam.name', venuesKey: 'destinations.cities.visakhapatnam.venues', taglineKey: 'destinations.cities.visakhapatnam.tagline' },
      { nameKey: 'destinations.cities.vijayawada.name', venuesKey: 'destinations.cities.vijayawada.venues', taglineKey: 'destinations.cities.vijayawada.tagline' },
      { nameKey: 'destinations.cities.guntur.name', venuesKey: 'destinations.cities.guntur.venues', taglineKey: 'destinations.cities.guntur.tagline' },
      { nameKey: 'destinations.cities.tirupati.name', venuesKey: 'destinations.cities.tirupati.venues', taglineKey: 'destinations.cities.tirupati.tagline' }
    ]
  },
  {
    name: 'Telangana',
    code: 'TS',
    language: 'Telugu',
    languageCode: 'te',
    cities: [
      { nameKey: 'destinations.cities.hyderabad.name', venuesKey: 'destinations.cities.hyderabad.venues', taglineKey: 'destinations.cities.hyderabad.tagline' },
      { nameKey: 'destinations.cities.warangal.name', venuesKey: 'destinations.cities.warangal.venues', taglineKey: 'destinations.cities.warangal.tagline' },
      { nameKey: 'destinations.cities.nizamabad.name', venuesKey: 'destinations.cities.nizamabad.venues', taglineKey: 'destinations.cities.nizamabad.tagline' },
      { nameKey: 'destinations.cities.karimnagar.name', venuesKey: 'destinations.cities.karimnagar.venues', taglineKey: 'destinations.cities.karimnagar.tagline' }
    ]
  },
  {
    name: 'Maharashtra',
    code: 'MH',
    language: 'Marathi',
    languageCode: 'mr',
    cities: [
      { nameKey: 'destinations.cities.mumbai.name', venuesKey: 'destinations.cities.mumbai.venues', taglineKey: 'destinations.cities.mumbai.tagline' },
      { nameKey: 'destinations.cities.pune.name', venuesKey: 'destinations.cities.pune.venues', taglineKey: 'destinations.cities.pune.tagline' },
      { nameKey: 'destinations.cities.nagpur.name', venuesKey: 'destinations.cities.nagpur.venues', taglineKey: 'destinations.cities.nagpur.tagline' },
      { nameKey: 'destinations.cities.nashik.name', venuesKey: 'destinations.cities.nashik.venues', taglineKey: 'destinations.cities.nashik.tagline' }
    ]
  },
  {
    name: 'Gujarat',
    code: 'GJ',
    language: 'Gujarati',
    languageCode: 'gu',
    cities: [
      { nameKey: 'destinations.cities.ahmedabad.name', venuesKey: 'destinations.cities.ahmedabad.venues', taglineKey: 'destinations.cities.ahmedabad.tagline' },
      { nameKey: 'destinations.cities.surat.name', venuesKey: 'destinations.cities.surat.venues', taglineKey: 'destinations.cities.surat.tagline' },
      { nameKey: 'destinations.cities.vadodara.name', venuesKey: 'destinations.cities.vadodara.venues', taglineKey: 'destinations.cities.vadodara.tagline' },
      { nameKey: 'destinations.cities.rajkot.name', venuesKey: 'destinations.cities.rajkot.venues', taglineKey: 'destinations.cities.rajkot.tagline' }
    ]
  },
  {
    name: 'Rajasthan',
    code: 'RJ',
    language: 'Hindi',
    languageCode: 'hi',
    cities: [
      { nameKey: 'destinations.cities.jaipur.name', venuesKey: 'destinations.cities.jaipur.venues', taglineKey: 'destinations.cities.jaipur.tagline' },
      { nameKey: 'destinations.cities.udaipur.name', venuesKey: 'destinations.cities.udaipur.venues', taglineKey: 'destinations.cities.udaipur.tagline' },
      { nameKey: 'destinations.cities.jodhpur.name', venuesKey: 'destinations.cities.jodhpur.venues', taglineKey: 'destinations.cities.jodhpur.tagline' },
      { nameKey: 'destinations.cities.jaisalmer.name', venuesKey: 'destinations.cities.jaisalmer.venues', taglineKey: 'destinations.cities.jaisalmer.tagline' }
    ]
  },
  {
    name: 'Punjab',
    code: 'PB',
    language: 'Punjabi',
    languageCode: 'pa',
    cities: [
      { nameKey: 'destinations.cities.chandigarh.name', venuesKey: 'destinations.cities.chandigarh.venues', taglineKey: 'destinations.cities.chandigarh.tagline' },
      { nameKey: 'destinations.cities.ludhiana.name', venuesKey: 'destinations.cities.ludhiana.venues', taglineKey: 'destinations.cities.ludhiana.tagline' },
      { nameKey: 'destinations.cities.amritsar.name', venuesKey: 'destinations.cities.amritsar.venues', taglineKey: 'destinations.cities.amritsar.tagline' },
      { nameKey: 'destinations.cities.jalandhar.name', venuesKey: 'destinations.cities.jalandhar.venues', taglineKey: 'destinations.cities.jalandhar.tagline' }
    ]
  },
  {
    name: 'West Bengal',
    code: 'WB',
    language: 'Bengali',
    languageCode: 'bn',
    cities: [
      { nameKey: 'destinations.cities.kolkata.name', venuesKey: 'destinations.cities.kolkata.venues', taglineKey: 'destinations.cities.kolkata.tagline' },
      { nameKey: 'destinations.cities.siliguri.name', venuesKey: 'destinations.cities.siliguri.venues', taglineKey: 'destinations.cities.siliguri.tagline' },
      { nameKey: 'destinations.cities.durgapur.name', venuesKey: 'destinations.cities.durgapur.venues', taglineKey: 'destinations.cities.durgapur.tagline' },
      { nameKey: 'destinations.cities.asansol.name', venuesKey: 'destinations.cities.asansol.venues', taglineKey: 'destinations.cities.asansol.tagline' }
    ]
  }
]

interface StateContextType {
  selectedState: typeof INDIAN_STATES[0]
  setSelectedState: (state: typeof INDIAN_STATES[0]) => void
  getCitiesForState: (stateCode: string) => typeof INDIAN_STATES[0]['cities']
}

const StateContext = createContext<StateContextType | undefined>(undefined)

export function StateProvider({ children }: { children: ReactNode }) {
  const [selectedState, setSelectedStateInternal] = useState(INDIAN_STATES[0]) // Default to Tamil Nadu
  const [isClient, setIsClient] = useState(false)

  // Load saved state from localStorage
  useEffect(() => {
    setIsClient(true)
    if (typeof window !== 'undefined') {
      const savedState = localStorage.getItem('selectedState')
      if (savedState) {
        const state = INDIAN_STATES.find(s => s.code === savedState)
        if (state) setSelectedStateInternal(state)
      }
    }
  }, [])

  const setSelectedState = (state: typeof INDIAN_STATES[0]) => {
    setSelectedStateInternal(state)
    if (isClient && typeof window !== 'undefined') {
      localStorage.setItem('selectedState', state.code)
    }
  }

  const getCitiesForState = (stateCode: string) => {
    const state = INDIAN_STATES.find(s => s.code === stateCode)
    return state?.cities || INDIAN_STATES[0].cities
  }

  return (
    <StateContext.Provider value={{
      selectedState,
      setSelectedState,
      getCitiesForState
    }}>
      {children}
    </StateContext.Provider>
  )
}

export function useStateContext() {
  const context = useContext(StateContext)
  if (context === undefined) {
    throw new Error('useStateContext must be used within a StateProvider')
  }
  return context
}
