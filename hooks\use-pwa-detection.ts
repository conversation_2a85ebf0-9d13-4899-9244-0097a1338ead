'use client'

import { useState, useEffect } from 'react'

interface PWADetection {
  isInstalled: boolean
  isStandalone: boolean
  canInstall: boolean
  installPrompt: any
  platform: 'ios' | 'android' | 'desktop' | 'unknown'
}

export function usePWADetection(): PWADetection {
  const [isInstalled, setIsInstalled] = useState(false)
  const [isStandalone, setIsStandalone] = useState(false)
  const [canInstall, setCanInstall] = useState(false)
  const [installPrompt, setInstallPrompt] = useState<any>(null)
  const [platform, setPlatform] = useState<'ios' | 'android' | 'desktop' | 'unknown'>('unknown')

  useEffect(() => {
    // Check if running in standalone mode (PWA installed)
    const checkStandalone = () => {
      const isStandaloneMode = 
        window.matchMedia('(display-mode: standalone)').matches ||
        (window.navigator as any).standalone === true ||
        document.referrer.includes('android-app://') ||
        window.location.search.includes('utm_source=homescreen')
      
      setIsStandalone(isStandaloneMode)
      setIsInstalled(isStandaloneMode)
    }

    // Detect platform
    const detectPlatform = () => {
      const userAgent = navigator.userAgent.toLowerCase()
      if (/iphone|ipad|ipod/.test(userAgent)) {
        setPlatform('ios')
      } else if (/android/.test(userAgent)) {
        setPlatform('android')
      } else if (/windows|mac|linux/.test(userAgent)) {
        setPlatform('desktop')
      } else {
        setPlatform('unknown')
      }
    }

    // Listen for beforeinstallprompt event
    const handleBeforeInstallPrompt = (e: Event) => {
      e.preventDefault()
      setInstallPrompt(e)
      setCanInstall(true)
    }

    // Listen for appinstalled event
    const handleAppInstalled = () => {
      setIsInstalled(true)
      setIsStandalone(true)
      setCanInstall(false)
      setInstallPrompt(null)
    }

    // Check display mode changes
    const handleDisplayModeChange = (e: MediaQueryListEvent) => {
      setIsStandalone(e.matches)
      setIsInstalled(e.matches)
    }

    checkStandalone()
    detectPlatform()

    // Add event listeners
    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt)
    window.addEventListener('appinstalled', handleAppInstalled)
    
    const mediaQuery = window.matchMedia('(display-mode: standalone)')
    mediaQuery.addEventListener('change', handleDisplayModeChange)

    // Cleanup
    return () => {
      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt)
      window.removeEventListener('appinstalled', handleAppInstalled)
      mediaQuery.removeEventListener('change', handleDisplayModeChange)
    }
  }, [])

  return {
    isInstalled,
    isStandalone,
    canInstall,
    installPrompt,
    platform
  }
}

// Helper function to trigger PWA install
export const triggerPWAInstall = async (installPrompt: any) => {
  if (!installPrompt) return false

  try {
    const result = await installPrompt.prompt()
    const outcome = await result.userChoice
    return outcome === 'accepted'
  } catch (error) {
    console.error('Error installing PWA:', error)
    return false
  }
}
