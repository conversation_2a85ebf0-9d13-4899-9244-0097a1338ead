import React, { useRef, useEffect } from 'react';
import {
  View,
  Animated,
  Dimensions,
  StyleSheet,
} from 'react-native';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

interface PageTransitionProps {
  children: React.ReactNode;
  type?: 'fade' | 'slide' | 'scale' | 'flip' | 'zoom' | 'blur';
  direction?: 'left' | 'right' | 'up' | 'down';
  duration?: number;
  delay?: number;
  style?: any;
}

export default function PageTransition({
  children,
  type = 'fade',
  direction = 'right',
  duration = 300,
  delay = 0,
  style,
}: PageTransitionProps) {
  // Animation values
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(getInitialSlideValue())).current;
  const scaleAnim = useRef(new Animated.Value(0.8)).current;
  const flipAnim = useRef(new Animated.Value(0)).current;
  const zoomAnim = useRef(new Animated.Value(0)).current;

  function getInitialSlideValue() {
    switch (direction) {
      case 'left':
        return -screenWidth;
      case 'right':
        return screenWidth;
      case 'up':
        return -screenHeight;
      case 'down':
        return screenHeight;
      default:
        return screenWidth;
    }
  }

  useEffect(() => {
    const startAnimation = () => {
      switch (type) {
        case 'fade':
          Animated.timing(fadeAnim, {
            toValue: 1,
            duration,
            useNativeDriver: true,
          }).start();
          break;

        case 'slide':
          Animated.parallel([
            Animated.timing(fadeAnim, {
              toValue: 1,
              duration,
              useNativeDriver: true,
            }),
            Animated.timing(slideAnim, {
              toValue: 0,
              duration,
              useNativeDriver: true,
            }),
          ]).start();
          break;

        case 'scale':
          Animated.parallel([
            Animated.timing(fadeAnim, {
              toValue: 1,
              duration,
              useNativeDriver: true,
            }),
            Animated.spring(scaleAnim, {
              toValue: 1,
              tension: 100,
              friction: 8,
              useNativeDriver: true,
            }),
          ]).start();
          break;

        case 'flip':
          Animated.sequence([
            Animated.timing(flipAnim, {
              toValue: 0.5,
              duration: duration / 2,
              useNativeDriver: true,
            }),
            Animated.timing(fadeAnim, {
              toValue: 1,
              duration: 0,
              useNativeDriver: true,
            }),
            Animated.timing(flipAnim, {
              toValue: 1,
              duration: duration / 2,
              useNativeDriver: true,
            }),
          ]).start();
          break;

        case 'zoom':
          Animated.parallel([
            Animated.timing(fadeAnim, {
              toValue: 1,
              duration,
              useNativeDriver: true,
            }),
            Animated.spring(zoomAnim, {
              toValue: 1,
              tension: 50,
              friction: 7,
              useNativeDriver: true,
            }),
          ]).start();
          break;

        case 'blur':
          // For blur effect, we'll use a simple fade with scale
          Animated.parallel([
            Animated.timing(fadeAnim, {
              toValue: 1,
              duration,
              useNativeDriver: true,
            }),
            Animated.timing(scaleAnim, {
              toValue: 1,
              duration,
              useNativeDriver: true,
            }),
          ]).start();
          break;
      }
    };

    const timer = setTimeout(startAnimation, delay);
    return () => clearTimeout(timer);
  }, [type, direction, duration, delay]);

  const getAnimatedStyle = () => {
    switch (type) {
      case 'fade':
        return {
          opacity: fadeAnim,
        };

      case 'slide':
        const translateKey = direction === 'left' || direction === 'right' ? 'translateX' : 'translateY';
        return {
          opacity: fadeAnim,
          transform: [
            { [translateKey]: slideAnim },
          ],
        };

      case 'scale':
        return {
          opacity: fadeAnim,
          transform: [
            { scale: scaleAnim },
          ],
        };

      case 'flip':
        const rotateAxis = direction === 'left' || direction === 'right' ? 'rotateY' : 'rotateX';
        const rotate = flipAnim.interpolate({
          inputRange: [0, 0.5, 1],
          outputRange: ['0deg', '90deg', '0deg'],
        });

        return {
          opacity: fadeAnim,
          transform: [
            { [rotateAxis]: rotate },
          ],
        };

      case 'zoom':
        const zoomScale = zoomAnim.interpolate({
          inputRange: [0, 1],
          outputRange: [0.3, 1],
        });

        return {
          opacity: fadeAnim,
          transform: [
            { scale: zoomScale },
          ],
        };

      case 'blur':
        const blurScale = scaleAnim.interpolate({
          inputRange: [0, 1],
          outputRange: [1.1, 1],
        });

        return {
          opacity: fadeAnim,
          transform: [
            { scale: blurScale },
          ],
        };

      default:
        return {
          opacity: fadeAnim,
        };
    }
  };

  return (
    <Animated.View style={[styles.container, getAnimatedStyle(), style]}>
      {children}
    </Animated.View>
  );
}

// Preset transition components
export const FadeTransition = (props: Omit<PageTransitionProps, 'type'>) => (
  <PageTransition {...props} type="fade" />
);

export const SlideLeftTransition = (props: Omit<PageTransitionProps, 'type' | 'direction'>) => (
  <PageTransition {...props} type="slide" direction="left" />
);

export const SlideRightTransition = (props: Omit<PageTransitionProps, 'type' | 'direction'>) => (
  <PageTransition {...props} type="slide" direction="right" />
);

export const SlideUpTransition = (props: Omit<PageTransitionProps, 'type' | 'direction'>) => (
  <PageTransition {...props} type="slide" direction="up" />
);

export const SlideDownTransition = (props: Omit<PageTransitionProps, 'type' | 'direction'>) => (
  <PageTransition {...props} type="slide" direction="down" />
);

export const ScaleTransition = (props: Omit<PageTransitionProps, 'type'>) => (
  <PageTransition {...props} type="scale" />
);

export const FlipTransition = (props: Omit<PageTransitionProps, 'type'>) => (
  <PageTransition {...props} type="flip" />
);

export const ZoomTransition = (props: Omit<PageTransitionProps, 'type'>) => (
  <PageTransition {...props} type="zoom" />
);

export const BlurTransition = (props: Omit<PageTransitionProps, 'type'>) => (
  <PageTransition {...props} type="blur" />
);

// Staggered transition helper
export const createStaggeredTransitions = (
  items: any[],
  renderItem: (item: any, index: number) => React.ReactNode,
  transitionType: PageTransitionProps['type'] = 'fade',
  staggerDelay: number = 100
) => {
  return items.map((item, index) => (
    <PageTransition
      key={index}
      type={transitionType}
      delay={index * staggerDelay}
    >
      {renderItem(item, index)}
    </PageTransition>
  ));
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});
