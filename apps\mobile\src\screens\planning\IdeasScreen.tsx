import React, { useState } from 'react';
import { View, FlatList } from 'react-native';
import { Card, Button, Badge } from '../../components/ui';

const initialIdeas = [
  { id: '1', title: 'Floral Mandap', favorite: false },
  { id: '2', title: 'Destination Wedding', favorite: false },
  { id: '3', title: 'Eco-friendly Decor', favorite: false },
];

const IdeasScreen = () => {
  const [ideas, setIdeas] = useState(initialIdeas);

  const toggleFavorite = (id: string) => {
    setIdeas(ideas.map(idea => idea.id === id ? { ...idea, favorite: !idea.favorite } : idea));
  };

  return (
    <View style={{ flex: 1, padding: 16 }}>
      <FlatList
        data={ideas}
        keyExtractor={item => item.id}
        renderItem={({ item }) => (
          <Card style={{ marginVertical: 8 }}>
            <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between' }}>
              <Badge>{item.title}</Badge>
              <Button
                title={item.favorite ? 'Saved' : 'Save'}
                onPress={() => toggleFavorite(item.id)}
                variant={item.favorite ? 'success' : 'default'}
              />
            </View>
          </Card>
        )}
      />
    </View>
  );
};

export default IdeasScreen; 