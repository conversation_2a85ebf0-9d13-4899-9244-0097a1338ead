/**
 * Test Setup File
 * Configures the testing environment for all tests
 */

// Mock environment variables
process.env.NODE_ENV = 'test';

// Mock console methods to reduce noise in tests
global.console = {
  ...console,
  log: jest.fn(),
  debug: jest.fn(),
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
};

// Mock fetch if not available in test environment
if (!global.fetch) {
  global.fetch = jest.fn();
}

// Mock localStorage if not available in test environment
if (!global.localStorage) {
  global.localStorage = {
    getItem: jest.fn(),
    setItem: jest.fn(),
    removeItem: jest.fn(),
    clear: jest.fn(),
    length: 0,
    key: jest.fn(),
  };
}

// Mock sessionStorage if not available in test environment
if (!global.sessionStorage) {
  global.sessionStorage = {
    getItem: jest.fn(),
    setItem: jest.fn(),
    removeItem: jest.fn(),
    clear: jest.fn(),
    length: 0,
    key: jest.fn(),
  };
}

// Mock window if not available in test environment
if (typeof window === 'undefined') {
  global.window = {} as any;
}

// Mock document if not available in test environment
if (typeof document === 'undefined') {
  global.document = {} as any;
}

// Set test timeout
jest.setTimeout(10000); 