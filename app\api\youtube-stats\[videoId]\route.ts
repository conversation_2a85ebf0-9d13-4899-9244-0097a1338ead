import { NextRequest, NextResponse } from 'next/server'

const YOUTUBE_API_KEY = 'AIzaSyAOH3ojP1uVzx3M3LkOyVUtr97EWbhvS5M'

export async function GET(
  request: NextRequest,
  { params }: { params: { videoId: string } }
) {
  const videoId = params.videoId
  
  try {
    const response = await fetch(
      `https://www.googleapis.com/youtube/v3/videos?id=${videoId}&part=statistics,contentDetails,snippet&key=${YOUTUBE_API_KEY}`
    )
    
    if (!response.ok) {
      throw new Error(`YouTube API responded with status: ${response.status}`)
    }
    
    const data = await response.json()
    
    if (!data.items || data.items.length === 0) {
      return NextResponse.json({ error: 'Video not found' }, { status: 404 })
    }
    
    const video = data.items[0]
    
    // Format duration from ISO 8601 to readable format
    const formatDuration = (duration: string) => {
      const match = duration.match(/PT(\d+H)?(\d+M)?(\d+S)?/)
      if (!match) return 'TBD'
      
      const hours = (match[1] || '').replace('H', '')
      const minutes = (match[2] || '').replace('M', '')
      const seconds = (match[3] || '').replace('S', '')
      
      let result = ''
      if (hours) result += `${hours}:`
      if (minutes) result += `${minutes.padStart(2, '0')}:`
      if (seconds) result += seconds.padStart(2, '0')
      
      return result || 'TBD'
    }
    
    // Format view count
    const formatViews = (viewCount: string) => {
      const count = parseInt(viewCount)
      if (count >= 1000000) {
        return `${(count / 1000000).toFixed(1)}M`
      } else if (count >= 1000) {
        return `${(count / 1000).toFixed(1)}K`
      }
      return count.toString()
    }
    
    return NextResponse.json({
      views: formatViews(video.statistics.viewCount),
      duration: formatDuration(video.contentDetails.duration),
      title: video.snippet.title,
      description: video.snippet.description,
      publishedAt: video.snippet.publishedAt,
      channelTitle: video.snippet.channelTitle,
      thumbnail: video.snippet.thumbnails?.high?.url || video.snippet.thumbnails?.medium?.url
    })
  } catch (error) {
    console.error('Error fetching YouTube data:', error)
    return NextResponse.json({ 
      error: 'Failed to fetch video data',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
} 