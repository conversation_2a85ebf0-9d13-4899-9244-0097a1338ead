'use client';

import React from 'react';
import { AuthenticatedRoute } from '@/components/RouteProtection';
import VendorPricingDashboard from '@/components/dashboard/VendorPricingDashboard';
import { useAuth } from '@/contexts/AuthContext';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { CreditCard, AlertCircle } from 'lucide-react';
import { useRouter } from 'next/navigation';

export default function PricingDashboardPage() {
  const { userProfile } = useAuth();
  const router = useRouter();

  // Check if user is a vendor
  if (!userProfile?.isVendor) {
      return (
    <AuthenticatedRoute>
      <div className="container mx-auto px-3 sm:px-4 py-4 sm:py-8">
        <Card className="max-w-md mx-auto">
          <CardHeader className="text-center">
            <div className="mx-auto mb-4 w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center">
              <AlertCircle className="h-6 w-6 text-orange-600" />
            </div>
            <CardTitle className="text-lg sm:text-xl">Vendor Access Required</CardTitle>
            <CardDescription className="text-sm sm:text-base">
              This page is only available for vendor accounts. Please update your profile to become a vendor.
            </CardDescription>
          </CardHeader>
          <CardContent className="text-center">
            <div className="space-y-3">
              <Button 
                onClick={() => router.push('/vendor-signup')}
                className="w-full"
              >
                <CreditCard className="h-4 w-4 mr-2" />
                Become a Vendor
              </Button>
              <Button 
                variant="outline" 
                onClick={() => router.push('/dashboard')}
                className="w-full"
              >
                Back to Dashboard
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </AuthenticatedRoute>
  );
  }

  return (
    <AuthenticatedRoute>
      <div className="container mx-auto px-3 sm:px-4 py-4 sm:py-8">
        <div className="mb-6 sm:mb-8">
          <h1 className="text-2xl sm:text-3xl font-bold text-gray-900 mb-2">Subscription & Billing</h1>
          <p className="text-sm sm:text-base text-gray-600">
            Manage your vendor subscription, view billing history, and upgrade your plan
          </p>
        </div>
        
        <VendorPricingDashboard />
      </div>
    </AuthenticatedRoute>
  );
}
