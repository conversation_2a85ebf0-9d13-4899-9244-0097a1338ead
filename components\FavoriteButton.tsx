"use client"

import React, { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Heart, Loader2 } from 'lucide-react'
import { useAuth } from '@/contexts/AuthContext'
import { FavoritesService, FavoriteInput } from '@/lib/services/favoritesService'
import { showToast } from '@/lib/toast'
import { useRouter } from 'next/navigation'
import { ClientOnly } from '@/components/client-only'

interface FavoriteButtonProps {
  entityId: string
  entityType: 'VENDOR' | 'VENUE' | 'SHOP_ITEM'
  entityData: {
    name: string
    image?: string
    price?: string
    location?: string
    city?: string
    state?: string
    rating?: number
    reviewCount?: number
    description?: string
  }
  variant?: 'default' | 'outline' | 'ghost'
  size?: 'default' | 'sm' | 'lg' | 'icon'
  className?: string
  showText?: boolean
}

export function FavoriteButton({
  entityId,
  entityType,
  entityData,
  variant = 'outline',
  size = 'icon',
  className = '',
  showText = false
}: FavoriteButtonProps) {
  const { isAuthenticated, user } = useAuth()
  const router = useRouter()
  const [isFavorited, setIsFavorited] = useState(false)
  const [loading, setLoading] = useState(false)
  const [favoriteId, setFavoriteId] = useState<string | null>(null)

  // Check if item is favorited on component mount
  useEffect(() => {
    if (isAuthenticated && user) {
      checkFavoriteStatus()
    }
  }, [isAuthenticated, user, entityId])

  const checkFavoriteStatus = async () => {
    try {
      const favorite = await FavoritesService.isFavorited(entityId, entityType)
      if (favorite) {
        setIsFavorited(true)
        setFavoriteId(favorite.id)
      } else {
        setIsFavorited(false)
        setFavoriteId(null)
      }
    } catch (error) {
      console.error('Error checking favorite status:', error)
    }
  }

  const handleFavoriteClick = async (e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()

    if (!isAuthenticated) {
      showToast.info('Please login to add favorites')
      router.push(`/login?redirect=${encodeURIComponent(window.location.pathname)}`)
      return
    }

    setLoading(true)

    try {
      if (isFavorited && favoriteId) {
        // Remove from favorites
        const result = await FavoritesService.removeFromFavorites(favoriteId)
        if (result.success) {
          setIsFavorited(false)
          setFavoriteId(null)
          showToast.success('Removed from favorites')
        }
      } else {
        // Add to favorites
        const favoriteInput: FavoriteInput = {
          entityId,
          entityType,
          entityName: entityData.name,
          entityImage: entityData.image,
          entityPrice: entityData.price,
          entityLocation: entityData.location,
          entityCity: entityData.city,
          entityState: entityData.state,
          entityRating: entityData.rating,
          entityReviewCount: entityData.reviewCount,
          entityDescription: entityData.description
        }

        const result = await FavoritesService.addToFavorites(favoriteInput)
        if (result.success && result.favorite) {
          setIsFavorited(true)
          setFavoriteId(result.favorite.id)
          showToast.success('Added to favorites')
        }
      }
    } catch (error) {
      console.error('Favorite action error:', error)
      showToast.error(error.message || 'Failed to update favorites')
    } finally {
      setLoading(false)
    }
  }

  const buttonClasses = `
    ${className}
    ${isFavorited
      ? 'text-red-500 border-red-500 bg-red-50 hover:bg-red-100 shadow-sm'
      : 'text-gray-500 hover:text-red-500 hover:border-red-500 hover:bg-red-50'
    }
    transition-all duration-200
  `

  return (
    <ClientOnly fallback={
      <Button
        variant={variant}
        size={size}
        className={buttonClasses}
        disabled
        title="Add to favorites"
      >
        <Heart className="h-4 w-4" />
        {showText && <span className="ml-2">Add to Favorites</span>}
      </Button>
    }>
      <Button
        variant={variant}
        size={size}
        className={buttonClasses}
        onClick={handleFavoriteClick}
        disabled={loading}
        title={isFavorited ? 'Remove from favorites' : 'Add to favorites'}
      >
        {loading ? (
          <Loader2 className="h-4 w-4 animate-spin" />
        ) : (
          <Heart
            className={`h-4 w-4 ${isFavorited ? 'fill-current' : ''}`}
          />
        )}
        {showText && (
          <span className="ml-2">
            {isFavorited ? 'Favorited' : 'Add to Favorites'}
          </span>
        )}
      </Button>
    </ClientOnly>
  )
}

export default FavoriteButton
