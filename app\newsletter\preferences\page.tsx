'use client';

import React, { useState, useEffect } from 'react';
import { Mail, Settings, CheckCircle, AlertCircle, Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Header } from "@/components/header";
import { Footer } from "@/components/footer";
import { TopHeader } from "@/components/top-header";
import { useNewsletter } from '@/hooks/useNewsletter';
import { useAuth } from '@/contexts/AuthContext';

export default function NewsletterPreferencesPage() {
  const { user, userProfile } = useAuth();
  const { loading, error, success, subscription, updatePreferences, checkSubscription, clearMessages } = useNewsletter();
  
  const [email, setEmail] = useState('');
  const [preferences, setPreferences] = useState({
    weddingTips: true,
    vendorRecommendations: true,
    specialOffers: true,
    eventUpdates: false,
    blogUpdates: false,
    frequency: 'WEEKLY'
  });
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (userProfile?.email) {
      setEmail(userProfile.email);
      loadUserPreferences(userProfile.email);
    }
  }, [userProfile]);

  const loadUserPreferences = async (userEmail: string) => {
    try {
      setIsLoading(true);
      await checkSubscription(userEmail);
    } catch (err) {
      console.error('Error loading preferences:', err);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (subscription?.preferences) {
      setPreferences({
        weddingTips: subscription.preferences.weddingTips ?? true,
        vendorRecommendations: subscription.preferences.vendorRecommendations ?? true,
        specialOffers: subscription.preferences.specialOffers ?? true,
        eventUpdates: subscription.preferences.eventUpdates ?? false,
        blogUpdates: subscription.preferences.blogUpdates ?? false,
        frequency: subscription.preferences.frequency ?? 'WEEKLY'
      });
    }
  }, [subscription]);

  const handlePreferenceChange = (key: string, value: boolean | string) => {
    setPreferences(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    clearMessages();

    if (!email) {
      return;
    }

    await updatePreferences(email, preferences);
  };

  const handleEmailCheck = async (e: React.FormEvent) => {
    e.preventDefault();
    clearMessages();

    if (!email) {
      return;
    }

    await checkSubscription(email);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <TopHeader />
      <Header />
      
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-2xl mx-auto">
          <div className="text-center mb-8">
            <div className="flex items-center justify-center mb-4">
              <Settings className="w-8 h-8 text-primary mr-2" />
              <h1 className="text-3xl font-bold">Newsletter Preferences</h1>
            </div>
            <p className="text-gray-600">
              Manage your email preferences and subscription settings
            </p>
          </div>

          {error && (
            <Alert variant="destructive" className="mb-6">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}
          
          {success && (
            <Alert className="mb-6">
              <CheckCircle className="h-4 w-4" />
              <AlertDescription>
                Your newsletter preferences have been updated successfully!
              </AlertDescription>
            </Alert>
          )}

          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Mail className="w-5 h-5" />
                Email Address
              </CardTitle>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleEmailCheck} className="space-y-4">
                <div>
                  <Label htmlFor="email">Enter your email to manage preferences</Label>
                  <div className="flex gap-2 mt-2">
                    <Input
                      id="email"
                      type="email"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      placeholder="Enter your email address"
                      required
                      disabled={loading || isLoading}
                    />
                    <Button 
                      type="submit" 
                      disabled={loading || isLoading || !email}
                      variant="outline"
                    >
                      {(loading || isLoading) ? (
                        <Loader2 className="w-4 h-4 animate-spin" />
                      ) : (
                        'Check'
                      )}
                    </Button>
                  </div>
                </div>
              </form>

              {subscription && (
                <div className="mt-4 p-4 bg-green-50 rounded-lg">
                  <div className="flex items-center gap-2 text-green-700">
                    <CheckCircle className="w-4 h-4" />
                    <span className="font-medium">Subscription found!</span>
                  </div>
                  <p className="text-sm text-green-600 mt-1">
                    Status: {subscription.status.toLowerCase().replace('_', ' ')} • 
                    Subscribed: {new Date(subscription.subscribedAt).toLocaleDateString()}
                  </p>
                </div>
              )}
            </CardContent>
          </Card>

          {subscription && subscription.status === 'ACTIVE' && (
            <Card>
              <CardHeader>
                <CardTitle>Email Preferences</CardTitle>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleSubmit} className="space-y-6">
                  <div className="space-y-4">
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="weddingTips"
                        checked={preferences.weddingTips}
                        onCheckedChange={(checked) => 
                          handlePreferenceChange('weddingTips', checked as boolean)
                        }
                        disabled={loading}
                      />
                      <Label htmlFor="weddingTips" className="text-sm font-medium">
                        Wedding Tips & Planning Advice
                      </Label>
                    </div>
                    <p className="text-xs text-gray-500 ml-6">
                      Get expert tips and advice for planning your perfect wedding
                    </p>

                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="vendorRecommendations"
                        checked={preferences.vendorRecommendations}
                        onCheckedChange={(checked) => 
                          handlePreferenceChange('vendorRecommendations', checked as boolean)
                        }
                        disabled={loading}
                      />
                      <Label htmlFor="vendorRecommendations" className="text-sm font-medium">
                        Vendor Recommendations & Reviews
                      </Label>
                    </div>
                    <p className="text-xs text-gray-500 ml-6">
                      Discover top-rated vendors and read authentic reviews
                    </p>

                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="specialOffers"
                        checked={preferences.specialOffers}
                        onCheckedChange={(checked) => 
                          handlePreferenceChange('specialOffers', checked as boolean)
                        }
                        disabled={loading}
                      />
                      <Label htmlFor="specialOffers" className="text-sm font-medium">
                        Special Offers & Discounts
                      </Label>
                    </div>
                    <p className="text-xs text-gray-500 ml-6">
                      Be the first to know about exclusive deals and discounts
                    </p>

                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="eventUpdates"
                        checked={preferences.eventUpdates}
                        onCheckedChange={(checked) => 
                          handlePreferenceChange('eventUpdates', checked as boolean)
                        }
                        disabled={loading}
                      />
                      <Label htmlFor="eventUpdates" className="text-sm font-medium">
                        Wedding Events & Exhibitions
                      </Label>
                    </div>
                    <p className="text-xs text-gray-500 ml-6">
                      Stay updated about wedding fairs and exhibitions near you
                    </p>

                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="blogUpdates"
                        checked={preferences.blogUpdates}
                        onCheckedChange={(checked) => 
                          handlePreferenceChange('blogUpdates', checked as boolean)
                        }
                        disabled={loading}
                      />
                      <Label htmlFor="blogUpdates" className="text-sm font-medium">
                        Blog Updates & Articles
                      </Label>
                    </div>
                    <p className="text-xs text-gray-500 ml-6">
                      Get notified when we publish new wedding-related articles
                    </p>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="frequency">Email Frequency</Label>
                    <Select 
                      value={preferences.frequency} 
                      onValueChange={(value) => handlePreferenceChange('frequency', value)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select frequency" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="DAILY">Daily</SelectItem>
                        <SelectItem value="WEEKLY">Weekly</SelectItem>
                        <SelectItem value="BIWEEKLY">Bi-weekly</SelectItem>
                        <SelectItem value="MONTHLY">Monthly</SelectItem>
                        <SelectItem value="SPECIAL_ONLY">Special offers only</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <Button 
                    type="submit" 
                    className="w-full" 
                    disabled={loading}
                  >
                    {loading ? (
                      <>
                        <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                        Updating Preferences...
                      </>
                    ) : (
                      'Update Preferences'
                    )}
                  </Button>
                </form>
              </CardContent>
            </Card>
          )}

          {subscription && subscription.status === 'UNSUBSCRIBED' && (
            <Card>
              <CardContent className="pt-6">
                <div className="text-center">
                  <AlertCircle className="w-12 h-12 text-yellow-500 mx-auto mb-4" />
                  <h3 className="text-lg font-medium mb-2">You're currently unsubscribed</h3>
                  <p className="text-gray-600 mb-4">
                    You unsubscribed from our newsletter on {' '}
                    {subscription.unsubscribedAt 
                      ? new Date(subscription.unsubscribedAt).toLocaleDateString()
                      : 'an unknown date'
                    }
                  </p>
                  <p className="text-sm text-gray-500">
                    To resubscribe, please use the newsletter subscription form on our homepage.
                  </p>
                </div>
              </CardContent>
            </Card>
          )}

          {email && !subscription && !loading && !isLoading && (
            <Card>
              <CardContent className="pt-6">
                <div className="text-center">
                  <Mail className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium mb-2">Email not found</h3>
                  <p className="text-gray-600 mb-4">
                    We couldn't find a newsletter subscription for this email address.
                  </p>
                  <p className="text-sm text-gray-500">
                    Would you like to subscribe to our newsletter? Visit our homepage to get started.
                  </p>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>

      <Footer />
    </div>
  );
}
