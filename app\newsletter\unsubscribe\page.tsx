'use client';

import React, { useState, useEffect } from 'react';
import { Mail, CheckCircle, AlertCircle, Loader2, Heart } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Header } from "@/components/header";
import { Footer } from "@/components/footer";
import { TopHeader } from "@/components/top-header";
import { useNewsletter } from '@/hooks/useNewsletter';
import { useSearchParams } from 'next/navigation';
import Link from 'next/link';

export default function UnsubscribePage() {
  const searchParams = useSearchParams();
  const { loading, error, success, unsubscribe, clearMessages } = useNewsletter();
  
  const [email, setEmail] = useState('');
  const [isUnsubscribed, setIsUnsubscribed] = useState(false);

  useEffect(() => {
    // Check if email is provided in URL params
    const emailParam = searchParams.get('email');
    if (emailParam) {
      setEmail(decodeURIComponent(emailParam));
    }
  }, [searchParams]);

  useEffect(() => {
    if (success) {
      setIsUnsubscribed(true);
    }
  }, [success]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    clearMessages();

    if (!email) {
      return;
    }

    await unsubscribe(email);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <TopHeader />
      <Header />
      
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-md mx-auto">
          <div className="text-center mb-8">
            <div className="flex items-center justify-center mb-4">
              <Mail className="w-8 h-8 text-gray-400 mr-2" />
              <h1 className="text-3xl font-bold">Unsubscribe</h1>
            </div>
            <p className="text-gray-600">
              We're sorry to see you go. You can unsubscribe from our newsletter below.
            </p>
          </div>

          {error && (
            <Alert variant="destructive" className="mb-6">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {isUnsubscribed ? (
            <Card>
              <CardContent className="pt-6">
                <div className="text-center">
                  <CheckCircle className="w-16 h-16 text-green-500 mx-auto mb-4" />
                  <h2 className="text-2xl font-bold mb-2">Successfully Unsubscribed</h2>
                  <p className="text-gray-600 mb-6">
                    You have been successfully unsubscribed from our newsletter. 
                    You will no longer receive emails from us.
                  </p>
                  
                  <div className="space-y-4">
                    <div className="p-4 bg-blue-50 rounded-lg">
                      <h3 className="font-medium text-blue-900 mb-2">
                        We'd love to have you back!
                      </h3>
                      <p className="text-sm text-blue-700 mb-3">
                        If you change your mind, you can always resubscribe to our newsletter.
                      </p>
                      <Link href="/">
                        <Button variant="outline" size="sm">
                          Resubscribe
                        </Button>
                      </Link>
                    </div>

                    <div className="p-4 bg-gray-50 rounded-lg">
                      <h3 className="font-medium text-gray-900 mb-2">
                        Still planning your wedding?
                      </h3>
                      <p className="text-sm text-gray-600 mb-3">
                        You can still browse our vendors, venues, and Festive Services without subscribing.
                      </p>
                      <Link href="/vendors">
                        <Button variant="outline" size="sm">
                          Browse Vendors
                        </Button>
                      </Link>
                    </div>
                  </div>

                  <div className="mt-6 pt-6 border-t border-gray-200">
                    <p className="text-xs text-gray-500 flex items-center justify-center gap-1">
                      Thank you for being part of our community 
                      <Heart className="w-3 h-3 text-red-400" />
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          ) : (
            <Card>
              <CardHeader>
                <CardTitle>Unsubscribe from Newsletter</CardTitle>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleSubmit} className="space-y-4">
                  <div>
                    <Label htmlFor="email">Email Address</Label>
                    <Input
                      id="email"
                      type="email"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      placeholder="Enter your email address"
                      required
                      disabled={loading}
                    />
                    <p className="text-xs text-gray-500 mt-1">
                      Enter the email address you want to unsubscribe
                    </p>
                  </div>

                  <Button 
                    type="submit" 
                    className="w-full" 
                    disabled={loading || !email}
                    variant="destructive"
                  >
                    {loading ? (
                      <>
                        <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                        Unsubscribing...
                      </>
                    ) : (
                      'Unsubscribe'
                    )}
                  </Button>
                </form>

                <div className="mt-6 p-4 bg-yellow-50 rounded-lg">
                  <h3 className="font-medium text-yellow-900 mb-2">
                    Before you go...
                  </h3>
                  <p className="text-sm text-yellow-700 mb-3">
                    Instead of unsubscribing completely, you can update your email preferences 
                    to receive only the content you're interested in.
                  </p>
                  <Link href="/newsletter/preferences">
                    <Button variant="outline" size="sm">
                      Update Preferences
                    </Button>
                  </Link>
                </div>

                <div className="mt-4 text-center">
                  <p className="text-xs text-gray-500">
                    Having trouble? <Link href="/contact" className="text-primary hover:underline">Contact us</Link> for help.
                  </p>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>

      <Footer />
    </div>
  );
}
