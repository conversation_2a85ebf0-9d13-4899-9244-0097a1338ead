"use client"

import { <PERSON>Header } from "@/components/top-header"
import { <PERSON><PERSON> } from "@/components/header"
import { Foot<PERSON> } from "@/components/footer"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { SimpleSEO } from "@/components/seo/SimpleSEO"
import { 
  ArrowLeft,
  Star,
  MessageCircle,
  ThumbsUp,
  ThumbsDown,
  Flag,
  Edit,
  Trash2,
  CheckCircle,
  AlertCircle,
  Camera,
  Users,
  Shield,
  Award,
  Heart,
  Eye,
  Filter,
  Search
} from "lucide-react"
import Link from "next/link"
import { useHelpTranslations } from '@/lib/universal-i18n'

export default function ReviewsHelpPage() {
  const helpT = useHelpTranslations()
  const reviewTypes = [
    {
      icon: Users,
      title: "Vendor Reviews",
      description: "Rate and review wedding vendors",
      examples: [
        "Photographer service quality",
        "Caterer food and service",
        "Decorator creativity and execution",
        "Makeup artist skills and punctuality"
      ]
    },
    {
      icon: Star,
      title: "Venue Reviews",
      description: "Share your venue experience",
      examples: [
        "Venue facilities and ambiance",
        "Staff behavior and service",
        "Food quality and presentation",
        "Overall value for money"
      ]
    },
    {
      icon: MessageCircle,
      title: "Product Reviews",
      description: "Review purchased products",
      examples: [
        "Product quality and durability",
        "Delivery time and packaging",
        "Seller communication",
        "Value for money"
      ]
    },
    {
      icon: Award,
      title: "Service Reviews",
      description: "Rate overall service experience",
      examples: [
        "Booking process ease",
        "Customer support quality",
        "Problem resolution",
        "Overall satisfaction"
      ]
    }
  ]

  const writingTips = [
    {
      tip: "Be Honest and Fair",
      description: "Provide truthful feedback based on your actual experience",
      details: [
        "Share both positive and negative aspects",
        "Avoid exaggerated claims",
        "Focus on facts rather than emotions",
        "Be constructive in your criticism"
      ]
    },
    {
      tip: "Be Specific and Detailed",
      description: "Include specific details that help other users",
      details: [
        "Mention specific services used",
        "Include dates and locations",
        "Describe what worked well and what didn't",
        "Provide context for your experience"
      ]
    },
    {
      tip: "Use Appropriate Language",
      description: "Keep your review professional and respectful",
      details: [
        "Avoid offensive or inappropriate language",
        "Be respectful even when disappointed",
        "Focus on the service, not personal attacks",
        "Use clear and understandable language"
      ]
    },
    {
      tip: "Include Photos When Relevant",
      description: "Add photos to support your review",
      details: [
        "Show the actual service or product",
        "Include before and after photos if applicable",
        "Ensure photos are clear and relevant",
        "Respect privacy and permissions"
      ]
    }
  ]

  const ratingGuidelines = [
    {
      stars: 5,
      title: "Excellent",
      description: "Exceeded expectations in every way",
      criteria: [
        "Outstanding service quality",
        "Exceptional value for money",
        "Highly professional behavior",
        "Would definitely recommend"
      ]
    },
    {
      stars: 4,
      title: "Very Good",
      description: "Met expectations with minor issues",
      criteria: [
        "Good service quality",
        "Fair value for money",
        "Professional behavior",
        "Would likely recommend"
      ]
    },
    {
      stars: 3,
      title: "Average",
      description: "Acceptable service with some concerns",
      criteria: [
        "Average service quality",
        "Reasonable value for money",
        "Some issues but manageable",
        "Might recommend with reservations"
      ]
    },
    {
      stars: 2,
      title: "Below Average",
      description: "Did not meet expectations",
      criteria: [
        "Poor service quality",
        "Below average value",
        "Several issues encountered",
        "Would not recommend"
      ]
    },
    {
      stars: 1,
      title: "Poor",
      description: "Significantly below expectations",
      criteria: [
        "Very poor service quality",
        "Poor value for money",
        "Major issues and problems",
        "Strongly would not recommend"
      ]
    }
  ]

  const reviewGuidelines = [
    "Reviews must be based on actual experience",
    "One review per service/product per user",
    "No promotional or spam content",
    "No personal information sharing",
    "No offensive or inappropriate language",
    "No false or misleading information",
    "Photos must be relevant and appropriate",
    "Reviews should be helpful to other users"
  ]

  const managingReviews = [
    {
      action: "Edit Your Review",
      description: "Update your review within 30 days of posting",
      icon: Edit,
      details: [
        "Click on your review from your profile",
        "Select 'Edit Review' option",
        "Make necessary changes",
        "Save the updated review"
      ]
    },
    {
      action: "Delete Your Review",
      description: "Remove your review if needed",
      icon: Trash2,
      details: [
        "Go to your profile reviews section",
        "Find the review you want to delete",
        "Click 'Delete' and confirm",
        "Review will be permanently removed"
      ]
    },
    {
      action: "Report Inappropriate Reviews",
      description: "Flag reviews that violate guidelines",
      icon: Flag,
      details: [
        "Click 'Report' on the problematic review",
        "Select the reason for reporting",
        "Provide additional details if needed",
        "Our team will investigate and take action"
      ]
    },
    {
      action: "Respond to Reviews",
      description: "Vendors can respond to customer reviews",
      icon: MessageCircle,
      details: [
        "Login to your vendor dashboard",
        "Go to reviews section",
        "Click 'Respond' on any review",
        "Write a professional response"
      ]
    }
  ]

  const helpfulReviewExample = {
    title: "Great Photography Service",
    rating: 5,
    content: "We hired [Vendor Name] for our wedding photography in Chennai on [Date]. The photographer was very professional and captured beautiful moments throughout the ceremony. The pre-wedding shoot was also excellent. Photos were delivered within 2 weeks as promised. The quality exceeded our expectations and the pricing was reasonable. Highly recommend for wedding photography!",
    photos: "Included 3 sample photos",
    helpful: "23 people found this helpful"
  }

  return (
    <>
      <SimpleSEO
        title="Reviews & Ratings Guide - BookmyFestive Help"
        description="Learn how to write helpful reviews, understand rating guidelines, and manage your reviews on BookmyFestive. Help other couples make informed decisions."
        keywords="wedding reviews, vendor ratings, review guidelines, customer feedback"
        url="/help/reviews"
      />
      
      <div className="min-h-screen bg-background">
        <TopHeader />
        <Header />

        {/* Header */}
        <section className="py-12">
          <div className="container mx-auto px-4">
            <Link href="/help" className="inline-flex items-center gap-2 text-primary hover:underline mb-6">
              <ArrowLeft className="w-4 h-4" />
              Back to Help Center
            </Link>
            
            <div className="text-center mb-12">
              <Star className="w-16 h-16 mx-auto mb-6 text-primary" />
              <h1 className="text-4xl font-bold mb-4">{helpT.reviews?.title || 'Reviews & Ratings Guide'}</h1>
              <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
                {helpT.reviews?.subtitle || 'Learn how to write helpful reviews and understand our rating system. Your feedback helps other couples make informed decisions for their special day.'}
              </p>
            </div>
          </div>
        </section>

        {/* Review Types */}
        <section className="py-16">
          <div className="container mx-auto px-4">
            <h2 className="text-3xl font-bold text-center mb-12">Types of Reviews</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {reviewTypes.map((type, index) => (
                <Card key={index} className="hover:shadow-lg transition-shadow">
                  <CardHeader className="text-center">
                    <type.icon className="w-12 h-12 mx-auto mb-4 text-primary" />
                    <CardTitle className="text-lg">{type.title}</CardTitle>
                    <p className="text-sm text-muted-foreground">{type.description}</p>
                  </CardHeader>
                  <CardContent>
                    <ul className="space-y-2">
                      {type.examples.map((example, idx) => (
                        <li key={idx} className="flex items-start gap-2">
                          <CheckCircle className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                          <span className="text-sm">{example}</span>
                        </li>
                      ))}
                    </ul>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Rating Guidelines */}
        <section className="py-16 bg-muted/30">
          <div className="container mx-auto px-4">
            <h2 className="text-3xl font-bold text-center mb-12">Rating Guidelines</h2>
            <div className="max-w-4xl mx-auto space-y-6">
              {ratingGuidelines.map((rating, index) => (
                <Card key={index}>
                  <CardHeader>
                    <div className="flex items-center gap-4">
                      <div className="flex">
                        {[...Array(5)].map((_, i) => (
                          <Star 
                            key={i} 
                            className={`w-6 h-6 ${i < rating.stars ? 'text-yellow-400 fill-current' : 'text-gray-300'}`} 
                          />
                        ))}
                      </div>
                      <div>
                        <CardTitle className="text-xl">{rating.title}</CardTitle>
                        <p className="text-muted-foreground">{rating.description}</p>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {rating.criteria.map((criterion, idx) => (
                        <div key={idx} className="flex items-start gap-2">
                          <CheckCircle className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                          <span className="text-sm">{criterion}</span>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Writing Tips */}
        <section className="py-16">
          <div className="container mx-auto px-4">
            <h2 className="text-3xl font-bold text-center mb-12">How to Write Helpful Reviews</h2>
            <div className="max-w-4xl mx-auto space-y-8">
              {writingTips.map((tip, index) => (
                <Card key={index}>
                  <CardHeader>
                    <CardTitle className="text-xl text-primary">{tip.tip}</CardTitle>
                    <p className="text-muted-foreground">{tip.description}</p>
                  </CardHeader>
                  <CardContent>
                    <ul className="space-y-2">
                      {tip.details.map((detail, idx) => (
                        <li key={idx} className="flex items-start gap-2">
                          <CheckCircle className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                          <span className="text-sm">{detail}</span>
                        </li>
                      ))}
                    </ul>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Example Review */}
        <section className="py-16 bg-muted/30">
          <div className="container mx-auto px-4">
            <h2 className="text-3xl font-bold text-center mb-12">Example of a Helpful Review</h2>
            <div className="max-w-4xl mx-auto">
              <Card className="border-green-200">
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div>
                      <CardTitle className="text-xl">{helpfulReviewExample.title}</CardTitle>
                      <div className="flex items-center gap-2 mt-2">
                        <div className="flex">
                          {[...Array(5)].map((_, i) => (
                            <Star 
                              key={i} 
                              className={`w-5 h-5 ${i < helpfulReviewExample.rating ? 'text-yellow-400 fill-current' : 'text-gray-300'}`} 
                            />
                          ))}
                        </div>
                        <Badge variant="outline">Verified Purchase</Badge>
                      </div>
                    </div>
                    <CheckCircle className="w-8 h-8 text-green-500" />
                  </div>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground mb-4">{helpfulReviewExample.content}</p>
                  <div className="flex items-center gap-4 text-sm text-muted-foreground">
                    <span className="flex items-center gap-1">
                      <Camera className="w-4 h-4" />
                      {helpfulReviewExample.photos}
                    </span>
                    <span className="flex items-center gap-1">
                      <ThumbsUp className="w-4 h-4" />
                      {helpfulReviewExample.helpful}
                    </span>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </section>

        {/* Managing Reviews */}
        <section className="py-16">
          <div className="container mx-auto px-4">
            <h2 className="text-3xl font-bold text-center mb-12">Managing Your Reviews</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {managingReviews.map((action, index) => (
                <Card key={index}>
                  <CardHeader>
                    <div className="flex items-center gap-3">
                      <action.icon className="w-8 h-8 text-primary" />
                      <div>
                        <CardTitle className="text-lg">{action.action}</CardTitle>
                        <p className="text-sm text-muted-foreground">{action.description}</p>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <ol className="space-y-2">
                      {action.details.map((detail, idx) => (
                        <li key={idx} className="flex items-start gap-2">
                          <span className="text-primary font-bold text-sm">{idx + 1}.</span>
                          <span className="text-sm">{detail}</span>
                        </li>
                      ))}
                    </ol>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Review Guidelines */}
        <section className="py-16 bg-muted/30">
          <div className="container mx-auto px-4">
            <h2 className="text-3xl font-bold text-center mb-12">Review Guidelines</h2>
            <div className="max-w-4xl mx-auto">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-3">
                    <Shield className="w-6 h-6 text-primary" />
                    Community Guidelines
                  </CardTitle>
                  <p className="text-muted-foreground">Please follow these guidelines when writing reviews</p>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {reviewGuidelines.map((guideline, index) => (
                      <div key={index} className="flex items-start gap-2">
                        <CheckCircle className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                        <span className="text-sm">{guideline}</span>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </section>

        {/* Quick Actions */}
        <section className="py-16">
          <div className="container mx-auto px-4 text-center">
            <h2 className="text-3xl font-bold mb-8">Start Reviewing</h2>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/dashboard/reviews">
                <Button size="lg" className="px-8">
                  <Star className="w-5 h-5 mr-2" />
                  Write a Review
                </Button>
              </Link>
              <Link href="/vendors">
                <Button variant="outline" size="lg" className="px-8">
                  <Users className="w-5 h-5 mr-2" />
                  Find Vendors to Review
                </Button>
              </Link>
              <Link href="/dashboard/reviews">
                <Button variant="outline" size="lg" className="px-8">
                  <Eye className="w-5 h-5 mr-2" />
                  Manage My Reviews
                </Button>
              </Link>
            </div>
          </div>
        </section>

        <Footer />
      </div>
    </>
  )
}
