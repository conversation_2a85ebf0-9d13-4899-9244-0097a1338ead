import React from 'react';
import { View, Text, StyleSheet, ViewStyle, TextStyle } from 'react-native';
import { useTheme } from '../../providers/ThemeProvider';

export interface CardProps {
  children: React.ReactNode;
  style?: ViewStyle;
  variant?: 'default' | 'elevated' | 'outlined';
}

export interface CardHeaderProps {
  children: React.ReactNode;
  style?: ViewStyle;
}

export interface CardTitleProps {
  children: React.ReactNode;
  style?: TextStyle;
}

export interface CardDescriptionProps {
  children: React.ReactNode;
  style?: TextStyle;
}

export interface CardContentProps {
  children: React.ReactNode;
  style?: ViewStyle;
}

export interface CardFooterProps {
  children: React.ReactNode;
  style?: ViewStyle;
}

export function Card({ children, style, variant = 'default' }: CardProps) {
  const { theme } = useTheme();

  const getCardStyle = (): ViewStyle => {
    const baseStyle: ViewStyle = {
      backgroundColor: theme.colors.card,
      borderRadius: theme.borderRadius.lg, // 8px to match web app
      borderWidth: 1,
      borderColor: theme.colors.border,
    };

    const variantStyles: Record<string, ViewStyle> = {
      default: {
        ...theme.shadows.sm, // Subtle shadow like web app
      },
      elevated: {
        ...theme.shadows.lg, // Larger shadow for elevated variant
      },
      outlined: {
        backgroundColor: 'transparent',
        shadowOpacity: 0,
        elevation: 0,
      },
    };

    return {
      ...baseStyle,
      ...variantStyles[variant],
    };
  };

  return (
    <View style={[getCardStyle(), style]}>
      {children}
    </View>
  );
}

export function CardHeader({ children, style }: CardHeaderProps) {
  const headerStyle: ViewStyle = {
    paddingHorizontal: 24, // p-6 to match web app
    paddingTop: 24,
    paddingBottom: 6, // pb-1.5
    flexDirection: 'column',
    gap: 6, // space-y-1.5
  };

  return (
    <View style={[headerStyle, style]}>
      {children}
    </View>
  );
}

export function CardTitle({ children, style }: CardTitleProps) {
  const { theme } = useTheme();

  const titleStyle: TextStyle = {
    fontSize: theme.typography.h3.fontSize, // text-2xl
    fontWeight: '600', // font-semibold
    color: theme.colors.cardForeground,
    lineHeight: theme.typography.h3.lineHeight,
    letterSpacing: -0.025, // tracking-tight
  };

  return (
    <Text style={[titleStyle, style]}>
      {children}
    </Text>
  );
}

export function CardDescription({ children, style }: CardDescriptionProps) {
  const { theme } = useTheme();

  const descriptionStyle: TextStyle = {
    fontSize: theme.typography.bodySmall.fontSize, // text-sm
    color: theme.colors.mutedForeground,
    lineHeight: theme.typography.bodySmall.lineHeight,
  };

  return (
    <Text style={[descriptionStyle, style]}>
      {children}
    </Text>
  );
}

export function CardContent({ children, style }: CardContentProps) {
  const contentStyle: ViewStyle = {
    paddingHorizontal: 24, // px-6
    paddingBottom: 24, // pb-6
    paddingTop: 0, // pt-0
  };

  return (
    <View style={[contentStyle, style]}>
      {children}
    </View>
  );
}

export function CardFooter({ children, style }: CardFooterProps) {
  const footerStyle: ViewStyle = {
    paddingHorizontal: 24, // px-6
    paddingBottom: 24, // pb-6
    paddingTop: 0, // pt-0
    flexDirection: 'row',
    alignItems: 'center',
  };

  return (
    <View style={[footerStyle, style]}>
      {children}
    </View>
  );
}

const styles = StyleSheet.create({
  // Additional styles can be added here if needed
});
