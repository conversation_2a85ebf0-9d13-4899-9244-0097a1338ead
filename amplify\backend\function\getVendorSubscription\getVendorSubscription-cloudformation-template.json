{"AWSTemplateFormatVersion": "2010-09-09", "Description": "Lambda function for getting vendor subscription", "Parameters": {"CloudWatchRule": {"Type": "String", "Default": "NONE", "Description": " Schedule Expression"}, "deploymentBucketName": {"Type": "String"}, "env": {"Type": "String"}, "s3Key": {"Type": "String"}, "storageThirumanam360Name": {"Type": "String", "Default": "storageThirumanam360Name"}, "storageThirumanam360Arn": {"Type": "String", "Default": "storageThirumanam360Arn"}, "apiThirumanam360GraphQLAPIIdOutput": {"Type": "String", "Default": "apiThirumanam360GraphQLAPIIdOutput"}, "apiThirumanam360GraphQLAPIEndpointOutput": {"Type": "String", "Default": "apiThirumanam360GraphQLAPIEndpointOutput"}, "apiThirumanam360VendorSubscriptionTableName": {"Type": "String", "Default": "apiThirumanam360VendorSubscriptionTableName"}, "apiThirumanam360VendorSubscriptionTableArn": {"Type": "String", "Default": "apiThirumanam360VendorSubscriptionTableArn"}}, "Conditions": {"ShouldNotCreateEnvResources": {"Fn::Equals": [{"Ref": "env"}, "NONE"]}}, "Resources": {"LambdaFunction": {"Type": "AWS::Lambda::Function", "Metadata": {"aws:asset:path": "./src", "aws:asset:property": "Code"}, "Properties": {"Code": {"S3Bucket": {"Ref": "deploymentBucketName"}, "S3Key": {"Ref": "s3Key"}}, "Handler": "index.handler", "FunctionName": {"Fn::If": ["ShouldNotCreateEnvResources", "getVendorSubscription", {"Fn::Join": ["", ["getVendorSubscription", "-", {"Ref": "env"}]]}]}, "Environment": {"Variables": {"ENV": {"Ref": "env"}, "REGION": {"Ref": "AWS::Region"}, "STORAGE_THIRUMANAM360_NAME": {"Ref": "storageThirumanam360Name"}, "STORAGE_THIRUMANAM360_ARN": {"Ref": "storageThirumanam360Arn"}, "API_THIRUMANAM360_GRAPHQLAPIIDOUTPUT": {"Ref": "apiThirumanam360GraphQLAPIIdOutput"}, "API_THIRUMANAM360_GRAPHQLAPIENDPOINTOUTPUT": {"Ref": "apiThirumanam360GraphQLAPIEndpointOutput"}, "API_THIRUMANAM360_VENDORSUBSCRIPTIONTABLE_NAME": {"Ref": "apiThirumanam360VendorSubscriptionTableName"}, "API_THIRUMANAM360_VENDORSUBSCRIPTIONTABLE_ARN": {"Ref": "apiThirumanam360VendorSubscriptionTableArn"}}}, "Role": {"Fn::GetAtt": ["LambdaExecutionRole", "<PERSON><PERSON>"]}, "Runtime": "nodejs18.x", "Layers": [], "Timeout": 25}}, "LambdaExecutionRole": {"Type": "AWS::IAM::Role", "Properties": {"RoleName": {"Fn::If": ["ShouldNotCreateEnvResources", "thirumanam360LambdaRole4a123456", {"Fn::Join": ["", ["thirumanam360LambdaRole4a123456", "-", {"Ref": "env"}]]}]}, "AssumeRolePolicyDocument": {"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Principal": {"Service": ["lambda.amazonaws.com"]}, "Action": ["sts:<PERSON><PERSON>Role"]}]}}}, "lambdaexecutionpolicy": {"DependsOn": ["LambdaExecutionRole"], "Type": "AWS::IAM::Policy", "Properties": {"PolicyName": "lambda-execution-policy", "Roles": [{"Ref": "LambdaExecutionRole"}], "PolicyDocument": {"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Action": ["logs:CreateLogGroup", "logs:CreateLogStream", "logs:PutLogEvents"], "Resource": {"Fn::Sub": ["arn:aws:logs:${region}:${account}:log-group:/aws/lambda/${lambda}:log-stream:*", {"region": {"Ref": "AWS::Region"}, "account": {"Ref": "AWS::AccountId"}, "lambda": {"Ref": "LambdaFunction"}}]}}]}}}, "AmplifyResourcesPolicy": {"DependsOn": ["LambdaExecutionRole"], "Type": "AWS::IAM::Policy", "Properties": {"PolicyName": "amplify-lambda-execution-policy", "Roles": [{"Ref": "LambdaExecutionRole"}], "PolicyDocument": {"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Action": ["dynamodb:Query", "dynamodb:GetItem", "dynamodb:<PERSON><PERSON>"], "Resource": [{"Ref": "apiThirumanam360VendorSubscriptionTableArn"}, {"Fn::Join": ["/", [{"Ref": "apiThirumanam360VendorSubscriptionTableArn"}, "index/*"]]}]}]}}}}, "Outputs": {"Name": {"Value": {"Ref": "LambdaFunction"}}, "Arn": {"Value": {"Fn::GetAtt": ["LambdaFunction", "<PERSON><PERSON>"]}}, "Region": {"Value": {"Ref": "AWS::Region"}}, "LambdaExecutionRole": {"Value": {"Ref": "LambdaExecutionRole"}}}}