"use client"

import { useState, useEffect } from 'react'
import { useParams } from 'next/navigation'
import { TopHeader } from "@/components/top-header";
import { Header } from "@/components/header";
import { Footer } from "@/components/footer";
import { EntityReviews } from "@/components/entity-reviews";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Textarea } from "@/components/ui/textarea";
import { Input } from "@/components/ui/input";
import { Loader2 } from "lucide-react";
import { Star, MapPin, Heart, Share2, CheckCircle, Award, Users, Clock, Phone, Mail, Globe, Facebook, Instagram, Youtube, Camera, Calendar, MessageCircle } from "@/lib/icon-imports";
import { generateClient } from '@aws-amplify/api';
import { getVendor } from '@/src/graphql/queries';
import { showToast, toastMessages } from '@/lib/toast'
import '@/lib/amplify-singleton'; // Auto-configures Amplify
import QuickInquiryForm from '@/components/QuickInquiryForm';
import AuthenticatedContactVendor from '@/components/AuthenticatedContactVendor';
import FavoriteButton from '@/components/FavoriteButton';
import AvailabilityChecker from '@/components/booking/AvailabilityChecker';
import { ClientOnly } from '@/components/client-only';

const client = generateClient();

export default function VendorDetailsPage() {
  const params = useParams()
  const vendorId = params.id as string

  const [vendor, setVendor] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Availability checker state
  const [selectedDate, setSelectedDate] = useState('')
  const [selectedTime, setSelectedTime] = useState('')
  const [isAvailable, setIsAvailable] = useState<boolean | null>(null)
  const [shareClicked, setShareClicked] = useState(false)
  const [isShared, setIsShared] = useState(false)

  // Load vendor data on component mount
  useEffect(() => {
    if (vendorId) {
      loadVendor()
    }
  }, [vendorId])

  const loadVendor = async () => {
    try {
      setLoading(true)
      setError(null)
      console.log('Loading vendor with ID:', vendorId)

      // Add timeout to prevent infinite loading
      const timeoutPromise = new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Request timeout')), 10000)
      );

      // Try to load vendor data with timeout
      let vendorData;
      try {
        const result: any = await Promise.race([
          client.graphql({ query: getVendor, variables: { id: vendorId } }),
          timeoutPromise
        ]);
        vendorData = result.data.getVendor;
        console.log('Vendor data received:', vendorData)
      } catch (serviceError) {
        console.warn('GraphQL failed, using mock data:', serviceError)
        // Fallback to mock data if GraphQL fails
        vendorData = {
          id: vendorId,
          name: 'Sample Wedding Vendor',
          description: 'Professional Festive Services for your special day.',
          location: 'Sample Location',
          city: 'Chennai',
          state: 'Tamil Nadu',
          category: 'Photography',
          price: '₹25,000',
          rating: 4.5,
          reviewCount: 30,
          images: ['/placeholder-vendor.jpg'],
          services: ['Wedding Photography', 'Pre-wedding Shoot', 'Video Coverage'],
          contact: '+91 9876543210',
          email: '<EMAIL>',
          website: 'https://samplevendor.com',
          experience: '5+ years',
          availability: 'Available'
        }
      }

      setVendor(vendorData);
    } catch (err) {
      console.error('Error loading vendor:', err)
      setError(`Failed to load vendor details: ${err.message}`)
    } finally {
      setLoading(false)
    }
  }

  const handleAvailabilityChange = (available: boolean, conflicts?: any[]) => {
    setIsAvailable(available)
    console.log('Vendor availability changed:', { available, conflicts })
  }
  // Handle share
  const handleShare = async () => {
    setShareClicked(true)
    setIsShared(true)
    try {
      if (navigator.share) {
        await navigator.share({
          title: vendor?.name || 'Check out this Vendor',
          text: `Look at this amazing Vendor: ${vendor?.name}`,
          url: window.location.href,
        })
      } else {
        // Fallback: copy to clipboard
        await navigator.clipboard.writeText(window.location.href)
        showToast.success('Link copied to clipboard!')
      }
    } catch (error) {
      console.error('Error sharing:', error)
      showToast.error('Failed to share')
    } finally {
      setTimeout(() => setIsShared(false), 1000)
      setTimeout(() => setShareClicked(false), 250)
    }
  }
  // Animation classes for creative effect
  const shareAnimClass = shareClicked ? 'scale-125 -rotate-6 shadow-lg' : '';

  // Loading state
  if (loading) {
    return (
      <div className="min-h-screen bg-white">
        <TopHeader />
        <Header />
        <div className="flex justify-center items-center py-20">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <span className="ml-2 text-gray-600">Loading vendor details...</span>
        </div>
        <Footer />
      </div>
    )
  }

  // Error state
  if (error || !vendor) {
    return (
      <div className="min-h-screen bg-white">
        <TopHeader />
        <Header />
        <div className="text-center py-20">
          <p className="text-red-600 mb-4">{error || 'Vendor not found'}</p>
          <Button onClick={loadVendor} className="bg-primary hover:bg-primary/90">
            Try Again
          </Button>
        </div>
        <Footer />
      </div>
    )
  }
  return (
    <div className="min-h-screen bg-white">
      <TopHeader />
      <Header />

      {/* Hero Section with Image Gallery */}
      <section className="relative w-full">
        <div className="h-64 sm:h-80 md:h-96 w-full relative overflow-hidden">
          {vendor.profilePhoto ? (
            <img
              src={vendor.profilePhoto}
              alt={vendor.name}
              className="absolute inset-0 w-full h-full object-cover object-center"
            />
          ) : (
            <div className="absolute inset-0 flex items-center justify-center bg-gradient-to-br from-primary/20 to-primary/10">
              <Camera className="h-16 w-16 sm:h-20 sm:w-20 md:h-24 md:w-24 text-primary/40" />
            </div>
          )}
          <div className="absolute inset-0 bg-black/30"></div>
        </div>
      </section>

      {/* Vendor Info Section - Below the hero image */}
      <section className="bg-white border-b">
        <div className="container mx-auto px-4 py-4 sm:py-6">
          <div className="space-y-3">
            <div>
              <h1 className="text-2xl sm:text-3xl md:text-4xl font-bold text-gray-900">{vendor.name}</h1>
              <p className="text-lg sm:text-xl text-gray-600">{vendor.category}</p>
            </div>
            <div className="flex items-center text-gray-600">
              <MapPin className="h-4 w-4 sm:h-5 sm:w-5 mr-2 text-primary" />
              <span className="text-base sm:text-lg">{vendor.city}, {vendor.state}</span>
            </div>
            <div className="block">
              <div className="text-xl sm:text-2xl md:text-3xl font-bold text-primary mb-1">
                {vendor.priceRange || "Contact for pricing"}
              </div>
              <div className="flex items-center gap-2">
                <Star className="h-4 w-4 sm:h-5 sm:w-5 fill-yellow-400 text-yellow-400" />
                <span className="text-base sm:text-lg font-semibold text-gray-900">{vendor.rating || "N/A"}</span>
                <span className="text-gray-600 text-sm sm:text-base">({vendor.reviewCount || 0} reviews)</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Main Content */}
      <section className="py-4 sm:py-6 md:py-8">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 sm:gap-6 md:gap-8">
            {/* Main Content */}
            <div className="lg:col-span-2">
              {/* About Section - Moved to Top */}
              <Card className="mb-4 sm:mb-6">
                <CardContent className="p-4 sm:p-6">
                  <div className="flex flex-col gap-4 sm:gap-6">
                    <div className="flex-1">
                      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 sm:gap-0">
                        <h3 className="text-lg sm:text-xl font-semibold">About {vendor.name}</h3>

                        {/* Like, Share & Social Media Icons */}
                        <div className="flex gap-2 sm:gap-3 justify-center sm:justify-end">
                          {/* Like and Share Icons */}
                          <FavoriteButton
                            entityId={vendor.id}
                            entityType="VENDOR"
                            entityData={{
                              name: vendor.name,
                              image: vendor.images?.[0],
                              price: vendor.priceRange,
                              location: vendor.location,
                              city: vendor.city,
                              state: vendor.state,
                              rating: vendor.rating,
                              reviewCount: vendor.reviewCount,
                              description: vendor.description
                            }}
                            variant="outline"
                            size="icon"
                            className="rounded-full h-8 w-8 sm:h-10 sm:w-10"
                          />
                          <Button 
                            size="sm" 
                            variant="secondary" 
                            className={`bg-white/90 hover:bg-white transition-all duration-300 ${
                              isShared ? 'text-[#8B0000]' : 'text-[#CD5C5C]'
                            } ${shareAnimClass}`}
                            onClick={handleShare}
                          >
                            <Share2 className={`h-10 w-10 ${isShared ? 'fill-current' : ''} transition-transform duration-300`} />
                          </Button>
                          {/* Social Media Icons */}
                          {vendor.socialMedia && (
                            <>
                              {vendor.socialMedia.facebook && (
                                <Button
                                  size="icon"
                                  variant="outline"
                                  className="rounded-full h-8 w-8 sm:h-10 sm:w-10 hover:bg-blue-600 hover:text-white hover:border-blue-600 transition-all duration-200"
                                  onClick={() => window.open(vendor.socialMedia.facebook, '_blank')}
                                >
                                  <Facebook className="h-4 w-4" />
                                </Button>
                              )}
                              {vendor.socialMedia.instagram && (
                                <Button
                                  size="icon"
                                  variant="outline"
                                  className="rounded-full h-8 w-8 sm:h-10 sm:w-10 hover:bg-pink-600 hover:text-white hover:border-pink-600 transition-all duration-200"
                                  onClick={() => window.open(vendor.socialMedia.instagram, '_blank')}
                                >
                                  <Instagram className="h-4 w-4" />
                                </Button>
                              )}
                              {vendor.socialMedia.youtube && (
                                <Button
                                  size="icon"
                                  variant="outline"
                                  className="rounded-full h-8 w-8 sm:h-10 sm:w-10 hover:bg-red-600 hover:text-white hover:border-red-600 transition-all duration-200"
                                  onClick={() => window.open(vendor.socialMedia.youtube, '_blank')}
                                >
                                  <Youtube className="h-4 w-4" />
                                </Button>
                              )}
                            </>
                          )}
                        </div>
                      </div>
                      <p className="text-gray-700 leading-relaxed text-sm sm:text-base mt-4">
                        {vendor.description || "No description available."}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Tabs defaultValue="overview" className="w-full">
                <TabsList className="grid w-full grid-cols-2 sm:grid-cols-4 mb-4 sm:mb-8 h-auto">
                  <TabsTrigger value="overview" className="text-xs sm:text-sm py-2">Overview</TabsTrigger>
                  <TabsTrigger value="services" className="text-xs sm:text-sm py-2">Services</TabsTrigger>
                  <TabsTrigger value="gallery" className="text-xs sm:text-sm py-2">Gallery</TabsTrigger>
                  <TabsTrigger value="reviews" className="text-xs sm:text-sm py-2">Reviews</TabsTrigger>
                </TabsList>

                <TabsContent value="overview" className="space-y-6">
                  {/* Availability Checker */}
                  <Card>
                    <CardContent className="p-6">
                      <h3 className="text-xl font-semibold mb-4 flex items-center gap-2">
                        <Calendar className="h-5 w-5 text-primary" />
                        Check Availability
                      </h3>
                      <p className="text-gray-600 mb-6">
                        Select your preferred date and time to check if {vendor.name} is available for your event.
                      </p>

                      <ClientOnly fallback={
                        <div className="p-4 border border-gray-200 rounded-lg bg-gray-50">
                          <p className="text-gray-600">Loading availability checker...</p>
                        </div>
                      }>
                        <AvailabilityChecker
                          entityId={vendor.id}
                          entityType="VENDOR"
                          entityName={vendor.name}
                          selectedDate={selectedDate}
                          selectedTime={selectedTime}
                          duration="4 hours"
                          onAvailabilityChange={handleAvailabilityChange}
                        />
                      </ClientOnly>
                    </CardContent>
                  </Card>

                  {/* Quick Stats */}
                  <Card>
                    <CardContent className="p-4 sm:p-6">
                      <h3 className="text-lg sm:text-xl font-semibold mb-4">Quick Stats</h3>
                      <div className="grid grid-cols-2 sm:grid-cols-4 gap-3 sm:gap-6">
                        <div className="text-center p-3 sm:p-4 bg-yellow-50 rounded-xl">
                          <Star className="h-6 w-6 sm:h-8 sm:w-8 fill-yellow-400 text-yellow-400 mx-auto mb-2" />
                          <div className="font-bold text-lg sm:text-xl text-gray-900">{vendor.rating || 'N/A'}</div>
                          <p className="text-xs sm:text-sm text-gray-600">Rating</p>
                        </div>
                        <div className="text-center p-3 sm:p-4 bg-blue-50 rounded-xl">
                          <Award className="h-6 w-6 sm:h-8 sm:w-8 text-blue-600 mx-auto mb-2" />
                          <div className="font-bold text-lg sm:text-xl text-gray-900">{vendor.experience || 'N/A'}</div>
                          <p className="text-xs sm:text-sm text-gray-600">Experience</p>
                        </div>
                        <div className="text-center p-3 sm:p-4 bg-green-50 rounded-xl">
                          <Users className="h-6 w-6 sm:h-8 sm:w-8 text-green-600 mx-auto mb-2" />
                          <div className="font-bold text-lg sm:text-xl text-gray-900">{vendor.events || 'N/A'}</div>
                          <p className="text-xs sm:text-sm text-gray-600">Events</p>
                        </div>
                        <div className="text-center p-3 sm:p-4 bg-purple-50 rounded-xl">
                          <Clock className="h-6 w-6 sm:h-8 sm:w-8 text-purple-600 mx-auto mb-2" />
                          <div className="font-bold text-lg sm:text-xl text-gray-900">{vendor.responseTime || 'N/A'}</div>
                          <p className="text-xs sm:text-sm text-gray-600">Response</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Specializations */}
                  {vendor.specializations && vendor.specializations.length > 0 && (
                    <Card>
                      <CardContent className="p-4 sm:p-6">
                        <h3 className="text-lg sm:text-xl font-semibold mb-4">Specializations</h3>
                        <div className="flex flex-wrap gap-2">
                          {vendor.specializations.map((spec: string, idx: number) => (
                            <Badge key={idx} variant="secondary" className="px-2 sm:px-3 py-1 text-xs sm:text-sm">
                              {spec}
                            </Badge>
                          ))}
                        </div>
                      </CardContent>
                    </Card>
                  )}

                  {/* Languages */}
                  {vendor.languages && vendor.languages.length > 0 && (
                    <Card>
                      <CardContent className="p-4 sm:p-6">
                        <h3 className="text-lg sm:text-xl font-semibold mb-4">Languages Spoken</h3>
                        <div className="flex flex-wrap gap-2">
                          {vendor.languages.map((lang: string, idx: number) => (
                            <Badge key={idx} variant="outline" className="px-2 sm:px-3 py-1 text-xs sm:text-sm">
                              {lang}
                            </Badge>
                          ))}
                        </div>
                      </CardContent>
                    </Card>
                  )}
                </TabsContent>

                <TabsContent value="services" className="space-y-4 sm:space-y-6">
                  <Card>
                    <CardContent className="p-4 sm:p-6">
                      <h3 className="text-lg sm:text-xl font-semibold mb-4 sm:mb-6">Services & Pricing</h3>
                      {vendor.services && vendor.services.length > 0 ? (
                        <div className="space-y-3 sm:space-y-4">
                          {vendor.services.map((service: any, idx: number) => (
                            <div key={idx} className="border rounded-xl p-3 sm:p-4 hover:shadow-md transition-shadow">
                              <div className="flex flex-col sm:flex-row sm:justify-between sm:items-start gap-2 sm:gap-0">
                                <div className="flex-1">
                                  <h4 className="font-semibold text-base sm:text-lg text-gray-900">{service.name}</h4>
                                  {service.description && (
                                    <p className="text-gray-600 mt-1 text-sm sm:text-base">{service.description}</p>
                                  )}
                                </div>
                                <div className="text-left sm:text-right">
                                  <div className="font-bold text-lg sm:text-xl text-primary">{service.price}</div>
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      ) : (
                        <p className="text-gray-500 text-sm sm:text-base">No services listed. Contact vendor for details.</p>
                      )}
                    </CardContent>
                  </Card>
                </TabsContent>

                <TabsContent value="gallery" className="space-y-4 sm:space-y-6">
                  <Card>
                    <CardContent className="p-4 sm:p-6">
                      <h3 className="text-lg sm:text-xl font-semibold mb-4 sm:mb-6">Photo Gallery</h3>
                      {vendor.gallery && vendor.gallery.length > 0 ? (
                        <div className="grid grid-cols-2 sm:grid-cols-3 gap-3 sm:gap-4">
                          {vendor.gallery.map((url: string, idx: number) => (
                            <div key={idx} className="relative aspect-square rounded-xl overflow-hidden group cursor-pointer">
                              <img
                                src={url}
                                alt={`Gallery ${idx + 1}`}
                                className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-110"
                              />
                              <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors duration-300"></div>
                            </div>
                          ))}
                        </div>
                      ) : (
                        <div className="text-center py-8 sm:py-12">
                          <Camera className="h-12 w-12 sm:h-16 sm:w-16 text-gray-300 mx-auto mb-4" />
                          <p className="text-gray-500 text-sm sm:text-base">No photos available</p>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                </TabsContent>

                <TabsContent value="reviews" className="space-y-4 sm:space-y-6">
                  <EntityReviews
                    entityType="VENDOR"
                    entityId={vendor.id}
                    entityName={vendor.name}
                  />
                </TabsContent>
              </Tabs>
            </div>

            {/* Sidebar */}
            <div className="lg:col-span-1">
              <div className="sticky top-8 space-y-4 sm:space-y-6">
                {/* Authenticated Contact Vendor Component */}
                <AuthenticatedContactVendor
                  vendor={{
                    id: vendor.id,
                    userId: vendor.userId,
                    name: vendor.name,
                    category: vendor.category,
                    contact: vendor.contact,
                    email: vendor.email,
                    website: vendor.website,
                    rating: vendor.rating,
                    reviewCount: vendor.reviewCount,
                    priceRange: vendor.priceRange
                  }}
                  showInquiryForm={true}
                />

                {/* Location */}
                <Card>
                  <CardContent className="p-4 sm:p-6">
                    <h3 className="text-base sm:text-lg font-bold mb-3 sm:mb-4">Location</h3>
                    <div className="space-y-2 text-xs sm:text-sm">
                      <div className="flex items-start">
                        <MapPin className="h-4 w-4 text-gray-500 mr-2 mt-0.5 flex-shrink-0" />
                        <div className="min-w-0">
                          {vendor.location && <p className="break-words">{vendor.location}</p>}
                          {vendor.city && vendor.state && <p className="break-words">{vendor.city}, {vendor.state}</p>}
                          {vendor.pincode && <p className="break-words">{vendor.pincode}</p>}
                          {vendor.fullAddress && <p className="mt-1 break-words">{vendor.fullAddress}</p>}
                          {!vendor.location && !vendor.city && !vendor.state && (
                            <p className="text-gray-500 italic">Location information not available</p>
                          )}
                        </div>
                      </div>
                    </div>
                    <div className="mt-4 h-24 sm:h-32 bg-gray-200 rounded-lg flex items-center justify-center">
                      <span className="text-gray-500 text-xs sm:text-sm">Map View</span>
                    </div>
                  </CardContent>
                </Card>

              </div>
            </div>
          </div>
        </div>
      </section>
      <Footer />
    </div>
  );
} 