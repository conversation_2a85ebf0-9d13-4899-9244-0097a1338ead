"use client"

import { useState } from 'react'
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Switch } from "@/components/ui/switch"
import { Star, X, Loader2 } from "lucide-react"
// import { EntityReviewService } from '@/src/services/entityReviewService' // TODO: Create this service
import { useAuth } from '@/contexts/AuthContext'
import { showToast, toastMessages } from '@/lib/toast'
import EntityReviewService from '@/lib/services/entityReviewService'

interface EntityReviewFormProps {
  entityType: 'SHOP' | 'VENUE' | 'VENDOR'
  entityId: string
  entityName: string
  onClose: () => void
  onSubmitted: () => void
}

export function EntityReviewForm({ entityType, entityId, entityName, onClose, onSubmitted }: EntityReviewFormProps) {
  const { user } = useAuth()
  const [loading, setLoading] = useState(false)
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    location: '',
    weddingDate: '',
    rating: 5,
    serviceRating: 5,
    valueRating: 5,
    communicationRating: 5,
    professionalismRating: 5,
    title: '',
    review: '',
    wouldRecommend: true
  })

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({ ...prev, [name]: value }))
  }

  const handleRatingChange = (field: string, rating: number) => {
    setFormData(prev => ({ ...prev, [field]: rating }))
  }

  const handleSwitchChange = (field: string, checked: boolean) => {
    setFormData(prev => ({ ...prev, [field]: checked }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!user) {
      showToast.error(toastMessages.auth.authRequired)
      return
    }

    setLoading(true)

    try {
      // Determine the appropriate category based on entity type
      let category = 'PRODUCT'; // Default fallback
      if (entityType === 'SHOP') {
        category = 'SHOP';
      } else if (entityType === 'VENUE') {
        category = 'VENUE';
      } else if (entityType === 'VENDOR') {
        category = 'VENDOR';
      } else if (entityType === 'PLATFORM') {
        category = 'PLATFORM';
      }

      const reviewData = {
        ...formData,
        entityType,
        entityId,
        category
      }

      // Create the review using the real service
      const result = await EntityReviewService.createEntityReview(reviewData, user)

      if (result.success) {
        showToast.success(toastMessages.review.submitSuccess)
        onSubmitted()
      } else {
        showToast.error(`Error: ${result.error}`)
      }
    } catch (error) {
      console.error('Error submitting review:', error)
      showToast.error(toastMessages.review.submitError)
    } finally {
      setLoading(false)
    }
  }

  const renderStarRating = (field: string, value: number, label: string) => (
    <div className="space-y-2">
      <Label className="text-sm font-medium">{label}</Label>
      <div className="flex items-center space-x-1">
        {[1, 2, 3, 4, 5].map((star) => (
          <button
            key={star}
            type="button"
            onClick={() => handleRatingChange(field, star)}
            className="focus:outline-none"
          >
            <Star
              className={`h-6 w-6 ${
                star <= value ? "fill-yellow-400 text-yellow-400" : "text-gray-300"
              } hover:text-yellow-400 transition-colors`}
            />
          </button>
        ))}
        <span className="ml-2 text-sm text-gray-600">{value}/5</span>
      </div>
    </div>
  )

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <Card className="w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>Write a Review for {entityName}</CardTitle>
            <Button variant="ghost" size="sm" onClick={onClose}>
              <X className="h-4 w-4" />
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Basic Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="name">Your Name *</Label>
                <Input
                  id="name"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  placeholder="Enter your name"
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="email">Email Address *</Label>
                <Input
                  id="email"
                  name="email"
                  type="email"
                  value={formData.email}
                  onChange={handleInputChange}
                  placeholder="Enter your email"
                  required
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="location">Location</Label>
                <Input
                  id="location"
                  name="location"
                  value={formData.location}
                  onChange={handleInputChange}
                  placeholder="City, State"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="weddingDate">Wedding Date</Label>
                <Input
                  id="weddingDate"
                  name="weddingDate"
                  type="date"
                  value={formData.weddingDate}
                  onChange={handleInputChange}
                />
              </div>
            </div>

            {/* Overall Rating */}
            {renderStarRating('rating', formData.rating, 'Overall Rating *')}

            {/* Detailed Ratings */}
            <div className="space-y-4">
              <h4 className="font-semibold text-lg">Detailed Ratings</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {renderStarRating('serviceRating', formData.serviceRating, 'Service Quality')}
                {renderStarRating('valueRating', formData.valueRating, 'Value for Money')}
                {renderStarRating('communicationRating', formData.communicationRating, 'Communication')}
                {renderStarRating('professionalismRating', formData.professionalismRating, 'Professionalism')}
              </div>
            </div>

            {/* Review Content */}
            <div className="space-y-2">
              <Label htmlFor="title">Review Title *</Label>
              <Input
                id="title"
                name="title"
                value={formData.title}
                onChange={handleInputChange}
                placeholder="Give your review a title"
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="review">Your Review *</Label>
              <Textarea
                id="review"
                name="review"
                value={formData.review}
                onChange={handleInputChange}
                placeholder={`Share your experience with ${entityName}...`}
                rows={5}
                required
              />
            </div>

            {/* Recommendation */}
            <div className="flex items-center space-x-2">
              <Switch
                id="wouldRecommend"
                checked={formData.wouldRecommend}
                onCheckedChange={(checked) => handleSwitchChange('wouldRecommend', checked)}
              />
              <Label htmlFor="wouldRecommend">
                I would recommend {entityName} to others
              </Label>
            </div>

            {/* Submit Button */}
            <div className="flex justify-end space-x-4 pt-4">
              <Button type="button" variant="outline" onClick={onClose}>
                Cancel
              </Button>
              <Button type="submit" disabled={loading} className="bg-primary hover:bg-primary/90">
                {loading ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Submitting...
                  </>
                ) : (
                  'Submit Review'
                )}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  )
}
