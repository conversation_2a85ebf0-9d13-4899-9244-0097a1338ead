import { generateClient } from 'aws-amplify/api';
import {
  listReviews,
  reviewsByEntityId,
  getVendor,
  listVendors,
  getVenue,
  listVenues,
  listShops
} from '@/src/graphql/queries';
import { 
  updateReview,
  createReview
} from '@/src/graphql/mutations';

const client = generateClient();

export interface VendorReviewData {
  id: string;
  userId: string;
  name: string;
  email: string;
  location?: string;
  weddingDate?: string;
  category: string;
  rating: number;
  serviceRating?: number;
  valueRating?: number;
  communicationRating?: number;
  professionalismRating?: number;
  title: string;
  review: string;
  wouldRecommend: boolean;
  verified: boolean;
  status: string;
  entityType: string;
  entityId: string;
  entityName?: string;
  userEntityComposite?: string;
  images?: string[];
  helpfulCount?: number;
  purchaseVerified?: boolean;
  reviewHelpfulUsers?: string[];
  vendorResponse?: string;
  responseDate?: string;
  hasResponse?: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface VendorReviewStats {
  totalReviews: number;
  averageRating: number;
  ratingDistribution: { [key: number]: number };
  recentReviews: number;
  responseRate: number;
  recommendationRate: number;
}

export interface VendorReviewFilters {
  statusFilter?: string;
  entityTypeFilter?: string;
  sortBy?: string;
  limit?: number;
  nextToken?: string;
}

export class VendorReviewService {
  /**
   * Get all reviews for a vendor's entities (vendors, venues, products)
   */
  static async getVendorReviews(userId: string, filters: VendorReviewFilters = {}): Promise<{
    success: boolean;
    data?: VendorReviewData[];
    stats?: VendorReviewStats;
    nextToken?: string;
    error?: string;
  }> {
    try {
      // First, get all entities owned by this vendor
      const vendorEntities = await this.getVendorEntities(userId);
      
      if (!vendorEntities.success || !vendorEntities.data || vendorEntities.data.length === 0) {
        return {
          success: true,
          data: [],
          stats: this.calculateEmptyStats()
        };
      }

      // Get reviews for all vendor entities
      const allReviews: VendorReviewData[] = [];
      
      for (const entity of vendorEntities.data) {
        const entityReviews = await this.getReviewsForEntity(entity.id, entity.type, filters);
        if (entityReviews.success && entityReviews.data) {
          // Add entity name to each review
          const reviewsWithEntityName = entityReviews.data.map(review => ({
            ...review,
            entityName: entity.name,
            hasResponse: !!(review.vendorResponse && review.vendorResponse.trim())
          }));
          allReviews.push(...reviewsWithEntityName);
        }
      }

      // Apply filters
      let filteredReviews = this.applyFilters(allReviews, filters);

      // Apply sorting
      filteredReviews = this.applySorting(filteredReviews, filters.sortBy || 'newest');

      // Calculate statistics
      const stats = this.calculateStats(allReviews);

      return {
        success: true,
        data: filteredReviews,
        stats
      };

    } catch (error) {
      console.error('Error getting vendor reviews:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get vendor reviews'
      };
    }
  }

  /**
   * Get all entities (vendors, venues, shop products) owned by a user
   */
  private static async getVendorEntities(userId: string): Promise<{
    success: boolean;
    data?: Array<{ id: string; name: string; type: string }>;
    error?: string;
  }> {
    try {
      const entities: Array<{ id: string; name: string; type: string }> = [];

      // Get vendor entities
      const vendorsResult = await client.graphql({
        query: listVendors,
        variables: {
          filter: {
            userId: { eq: userId }
          }
        }
      });

      if (vendorsResult.data.listVendors.items) {
        vendorsResult.data.listVendors.items.forEach(vendor => {
          entities.push({
            id: vendor.id,
            name: vendor.name,
            type: 'VENDOR'
          });
        });
      }

      // Get venue entities
      const venuesResult = await client.graphql({
        query: listVenues,
        variables: {
          filter: {
            userId: { eq: userId }
          }
        }
      });

      if (venuesResult.data.listVenues.items) {
        venuesResult.data.listVenues.items.forEach(venue => {
          entities.push({
            id: venue.id,
            name: venue.name,
            type: 'VENUE'
          });
        });
      }

      // Get shop product entities
      const shopsResult = await client.graphql({
        query: listShops,
        variables: {
          filter: {
            userId: { eq: userId }
          }
        }
      });

      if (shopsResult.data.listShops.items) {
        shopsResult.data.listShops.items.forEach(product => {
          entities.push({
            id: product.id,
            name: product.name,
            type: 'SHOP'
          });
        });
      }

      return {
        success: true,
        data: entities
      };

    } catch (error) {
      console.error('Error getting vendor entities:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get vendor entities'
      };
    }
  }

  /**
   * Get reviews for a specific entity
   */
  private static async getReviewsForEntity(
    entityId: string,
    entityType: string,
    filters: VendorReviewFilters
  ): Promise<{
    success: boolean;
    data?: VendorReviewData[];
    error?: string;
  }> {
    try {
      // Use listReviews with filter as fallback since reviewsByEntityId might not be deployed
      const result = await client.graphql({
        query: listReviews,
        variables: {
          filter: {
            entityId: { eq: entityId },
            entityType: { eq: entityType },
            ...(filters.statusFilter && filters.statusFilter !== 'ALL' ? {
              status: { eq: filters.statusFilter }
            } : {})
          },
          limit: filters.limit || 100
        }
      });

      const reviews: VendorReviewData[] = result.data.listReviews.items.map(item => ({
        id: item.id,
        userId: item.userId,
        name: item.name,
        email: item.email,
        location: item.location || '',
        weddingDate: item.weddingDate || '',
        category: item.category,
        rating: item.rating,
        serviceRating: item.serviceRating,
        valueRating: item.valueRating,
        communicationRating: item.communicationRating,
        professionalismRating: item.professionalismRating,
        title: item.title,
        review: item.review,
        wouldRecommend: item.wouldRecommend,
        verified: item.verified || false,
        status: item.status,
        entityType: item.entityType,
        entityId: item.entityId,
        userEntityComposite: item.userEntityComposite,
        images: item.images || [],
        helpfulCount: item.helpfulCount || 0,
        purchaseVerified: item.purchaseVerified || false,
        reviewHelpfulUsers: item.reviewHelpfulUsers || [],
        vendorResponse: item.vendorResponse || '',
        responseDate: item.responseDate || '',
        createdAt: item.createdAt,
        updatedAt: item.updatedAt
      }));

      return {
        success: true,
        data: reviews
      };

    } catch (error) {
      console.error('Error getting reviews for entity:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get reviews for entity'
      };
    }
  }

  /**
   * Apply filters to reviews
   */
  private static applyFilters(reviews: VendorReviewData[], filters: VendorReviewFilters): VendorReviewData[] {
    let filtered = [...reviews];

    // Status filter
    if (filters.statusFilter && filters.statusFilter !== 'ALL') {
      filtered = filtered.filter(review => review.status === filters.statusFilter);
    }

    // Entity type filter
    if (filters.entityTypeFilter && filters.entityTypeFilter !== 'ALL') {
      filtered = filtered.filter(review => review.entityType === filters.entityTypeFilter);
    }

    return filtered;
  }

  /**
   * Apply sorting to reviews
   */
  private static applySorting(reviews: VendorReviewData[], sortBy: string): VendorReviewData[] {
    const sorted = [...reviews];

    switch (sortBy) {
      case 'newest':
        return sorted.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
      case 'oldest':
        return sorted.sort((a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime());
      case 'highest-rating':
        return sorted.sort((a, b) => b.rating - a.rating);
      case 'lowest-rating':
        return sorted.sort((a, b) => a.rating - b.rating);
      case 'most-helpful':
        return sorted.sort((a, b) => (b.helpfulCount || 0) - (a.helpfulCount || 0));
      default:
        return sorted;
    }
  }

  /**
   * Calculate review statistics
   */
  private static calculateStats(reviews: VendorReviewData[]): VendorReviewStats {
    if (reviews.length === 0) {
      return this.calculateEmptyStats();
    }

    const totalReviews = reviews.length;
    const averageRating = reviews.reduce((sum, review) => sum + review.rating, 0) / totalReviews;

    const ratingDistribution: { [key: number]: number } = {};
    for (let i = 1; i <= 5; i++) {
      ratingDistribution[i] = reviews.filter(review => review.rating === i).length;
    }

    // Recent reviews (last 30 days)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    const recentReviews = reviews.filter(review => 
      new Date(review.createdAt) >= thirtyDaysAgo
    ).length;

    // Response rate
    const reviewsWithResponses = reviews.filter(review => 
      review.vendorResponse && review.vendorResponse.trim()
    ).length;
    const responseRate = (reviewsWithResponses / totalReviews) * 100;

    // Recommendation rate
    const recommendingReviews = reviews.filter(review => review.wouldRecommend).length;
    const recommendationRate = (recommendingReviews / totalReviews) * 100;

    return {
      totalReviews,
      averageRating,
      ratingDistribution,
      recentReviews,
      responseRate,
      recommendationRate
    };
  }

  /**
   * Calculate empty stats
   */
  private static calculateEmptyStats(): VendorReviewStats {
    return {
      totalReviews: 0,
      averageRating: 0,
      ratingDistribution: { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 },
      recentReviews: 0,
      responseRate: 0,
      recommendationRate: 0
    };
  }

  /**
   * Add vendor response to a review
   */
  static async addVendorResponse(reviewId: string, response: string): Promise<{
    success: boolean;
    data?: VendorReviewData;
    error?: string;
  }> {
    try {
      const result = await client.graphql({
        query: updateReview,
        variables: {
          input: {
            id: reviewId,
            vendorResponse: response.trim(),
            responseDate: new Date().toISOString()
          }
        }
      });

      return {
        success: true,
        data: result.data.updateReview as VendorReviewData
      };

    } catch (error) {
      console.error('Error adding vendor response:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to add vendor response'
      };
    }
  }

  /**
   * Update review status (for moderation)
   */
  static async updateReviewStatus(reviewId: string, status: string): Promise<{
    success: boolean;
    data?: VendorReviewData;
    error?: string;
  }> {
    try {
      const result = await client.graphql({
        query: updateReview,
        variables: {
          input: {
            id: reviewId,
            status
          }
        }
      });

      return {
        success: true,
        data: result.data.updateReview as VendorReviewData
      };

    } catch (error) {
      console.error('Error updating review status:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to update review status'
      };
    }
  }

  /**
   * Update review content (for editing)
   */
  static async updateReview(reviewId: string, updateData: {
    title?: string;
    review?: string;
    rating?: number;
    serviceRating?: number;
    valueRating?: number;
    communicationRating?: number;
    professionalismRating?: number;
    wouldRecommend?: boolean;
  }): Promise<{
    success: boolean;
    data?: VendorReviewData;
    error?: string;
  }> {
    try {
      const result = await client.graphql({
        query: updateReview,
        variables: {
          input: {
            id: reviewId,
            ...updateData
          }
        }
      });

      return {
        success: true,
        data: result.data.updateReview as VendorReviewData
      };

    } catch (error) {
      console.error('Error updating review:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to update review'
      };
    }
  }
}

export default VendorReviewService;
