const AWS = require('aws-sdk');
const { v4: uuidv4 } = require('uuid');

// Initialize AWS services
const ses = new AWS.SES({ region: process.env.AWS_REGION });
const dynamodb = new AWS.DynamoDB.DocumentClient();

// Email templates
const EMAIL_TEMPLATES = {
  LOGIN_OTP: {
    subject: 'Your BookmyFestive Login OTP - {{otp}}',
    getHtml: (data) => `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login OTP - BookmyFestive</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: linear-gradient(135deg, #a31515, #d32f2f); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
        .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }
        .otp-box { background: white; border: 2px solid #a31515; padding: 20px; text-align: center; margin: 20px 0; border-radius: 8px; }
        .otp-code { font-size: 32px; font-weight: bold; color: #a31515; letter-spacing: 5px; }
        .footer { text-align: center; margin-top: 20px; color: #666; font-size: 14px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔐 Login Verification</h1>
            <p>BookmyFestive</p>
        </div>
        <div class="content">
            <h2>Hello ${data.userName}!</h2>
            <p>You requested to log in to your BookmyFestive account. Please use the OTP below to complete your login:</p>
            
            <div class="otp-box">
                <div class="otp-code">${data.otp}</div>
                <p><strong>Valid for ${data.expiryMinutes || 10} minutes</strong></p>
            </div>
            
            <p><strong>Security Note:</strong> Never share this OTP with anyone. Our team will never ask for your OTP.</p>
            
            <p>If you didn't request this login, please ignore this email or contact our support team.</p>
        </div>
        <div class="footer">
            <p>© 2025 BookmyFestive. All rights reserved.</p>
            <p>Making your wedding dreams come true! 💕</p>
        </div>
    </div>
</body>
</html>`,
    getText: (data) => `
Login OTP - BookmyFestive

Hello ${data.userName}!

Your OTP for BookmyFestive login is: ${data.otp}
Valid for ${data.expiryMinutes || 10} minutes.

Security Note: Never share this OTP with anyone.

If you didn't request this login, please ignore this email.

© 2025 BookmyFestive. All rights reserved.
`
  },

  WELCOME_SIGNUP: {
    subject: 'Welcome to BookmyFestive! 🎉',
    getHtml: (data) => `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Welcome to BookmyFestive</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: linear-gradient(135deg, #a31515, #d32f2f); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
        .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }
        .welcome-box { background: white; padding: 25px; margin: 20px 0; border-radius: 8px; border-left: 4px solid #a31515; }
        .cta-button { display: inline-block; background: #a31515; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; margin: 20px 0; }
        .features { display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin: 20px 0; }
        .feature { background: white; padding: 15px; border-radius: 8px; text-align: center; }
        .footer { text-align: center; margin-top: 20px; color: #666; font-size: 14px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎉 Welcome to BookmyFestive!</h1>
            <p>Your Wedding Planning Journey Starts Here</p>
        </div>
        <div class="content">
            <div class="welcome-box">
                <h2>Hello ${data.userName}! 👋</h2>
                <p>Congratulations on taking the first step towards planning your perfect wedding! We're thrilled to have you join the BookmyFestive family.</p>
            </div>
            
            <h3>🌟 What You Can Do Now:</h3>
            <div class="features">
                <div class="feature">
                    <h4>🏛️ Find Venues</h4>
                    <p>Discover beautiful wedding venues</p>
                </div>
                <div class="feature">
                    <h4>👥 Book Vendors</h4>
                    <p>Connect with trusted vendors</p>
                </div>
                <div class="feature">
                    <h4>🛍️ Shop Products</h4>
                    <p>Browse wedding essentials</p>
                </div>
                <div class="feature">
                    <h4>📋 Plan & Track</h4>
                    <p>Use our planning tools</p>
                </div>
            </div>
            
            <div style="text-align: center;">
                <a href="${data.dashboardUrl || 'https://thirumanam360.com'}" class="cta-button">Start Planning Your Wedding</a>
            </div>
            
            <p><strong>Need Help?</strong> Our support team is here to assist you every step of the way. Feel free to reach out!</p>
        </div>
        <div class="footer">
            <p>© 2025 BookmyFestive. All rights reserved.</p>
            <p>Making your wedding dreams come true! 💕</p>
        </div>
    </div>
</body>
</html>`,
    getText: (data) => `
Welcome to BookmyFestive! 🎉

Hello ${data.userName}!

Congratulations on taking the first step towards planning your perfect wedding! We're thrilled to have you join the BookmyFestive family.

What You Can Do Now:
🏛️ Find Venues - Discover beautiful wedding venues
👥 Book Vendors - Connect with trusted vendors
🛍️ Shop Products - Browse wedding essentials
📋 Plan & Track - Use our planning tools

Start Planning: ${data.dashboardUrl || 'https://thirumanam360.com'}

Need Help? Our support team is here to assist you every step of the way.

© 2025 BookmyFestive. All rights reserved.
Making your wedding dreams come true! 💕
`
  },

  NEWSLETTER_SUBSCRIPTION: {
    subject: 'Welcome to BookmyFestive Newsletter! 📧',
    getHtml: (data) => `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Newsletter Subscription - BookmyFestive</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: linear-gradient(135deg, #a31515, #d32f2f); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
        .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }
        .highlight { background: #fef3f2; padding: 15px; border-radius: 6px; margin: 15px 0; border-left: 4px solid #a31515; }
        .interests { background: #f0f9ff; padding: 15px; border-radius: 6px; margin: 15px 0; }
        .footer { text-align: center; margin-top: 20px; color: #666; font-size: 14px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎉 Welcome to BookmyFestive!</h1>
            <p>Thank you for subscribing to our newsletter</p>
        </div>
        <div class="content">
            <h2>Hello ${data.userName}! 👋</h2>
            
            <p>Welcome to the BookmyFestive family! We're thrilled to have you join our community of couples planning their perfect wedding.</p>
            
            <div class="highlight">
                <h3>🎊 What to Expect:</h3>
                <ul>
                    <li><strong>Wedding Tips & Advice:</strong> Expert guidance for planning Your Dream Celebration</li>
                    <li><strong>Vendor Recommendations:</strong> Discover top-rated wedding vendors in your city</li>
                    <li><strong>Exclusive Offers:</strong> Special discounts and deals just for our subscribers</li>
                    <li><strong>Latest Trends:</strong> Stay updated with the newest wedding trends and ideas</li>
                </ul>
            </div>

            ${data.interests && data.interests.length > 0 ? `
            <div class="interests">
                <h3>📝 Your Interests:</h3>
                <p>Based on your preferences, we'll send you personalized content about: <strong>${data.interestsList}</strong></p>
            </div>
            ` : ''}

            <p>You'll receive our newsletter with the latest wedding inspiration, vendor spotlights, and exclusive offers.</p>
            
            <p>If you have any questions or need help planning your wedding, feel free to reach out to us. We're here to make your wedding journey magical!</p>
            
            <p>Happy Wedding Planning! 💕</p>
            <p><strong>The BookmyFestive Team</strong></p>
        </div>
        <div class="footer">
            <p>You're receiving this email because you subscribed to our newsletter.</p>
            <p><a href="https://thirumanam360.com/preferences">Manage Preferences</a> | <a href="https://thirumanam360.com/unsubscribe">Unsubscribe</a></p>
            <p>© 2025 BookmyFestive. All rights reserved.</p>
        </div>
    </div>
</body>
</html>`,
    getText: (data) => `
Welcome to BookmyFestive Newsletter! 📧

Hello ${data.userName}!

Welcome to the BookmyFestive family! We're thrilled to have you join our community of couples planning their perfect wedding.

What to Expect:
- Wedding Tips & Advice: Expert guidance for planning Your Dream Celebration
- Vendor Recommendations: Discover top-rated wedding vendors in your city
- Exclusive Offers: Special discounts and deals just for our subscribers
- Latest Trends: Stay updated with the newest wedding trends and ideas

${data.interests && data.interests.length > 0 ? `Your Interests: ${data.interestsList}` : ''}

You'll receive our newsletter with the latest wedding inspiration, vendor spotlights, and exclusive offers.

Happy Wedding Planning! 💕
The BookmyFestive Team

Manage Preferences: https://thirumanam360.com/preferences
Unsubscribe: https://thirumanam360.com/unsubscribe
© 2025 BookmyFestive. All rights reserved.
`
  },

  BOOKING_CONFIRMATION: {
    subject: 'Booking Confirmed - {{entityName}} | BookmyFestive',
    getHtml: (data) => `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Booking Confirmation - BookmyFestive</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: linear-gradient(135deg, #a31515, #d32f2f); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
        .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }
        .booking-card { background: white; padding: 25px; margin: 20px 0; border-radius: 8px; border-left: 4px solid #a31515; }
        .booking-details { display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin: 20px 0; }
        .detail-item { background: #f8f9fa; padding: 15px; border-radius: 6px; }
        .detail-label { font-weight: bold; color: #a31515; font-size: 12px; text-transform: uppercase; }
        .detail-value { font-size: 14px; margin-top: 5px; }
        .status-badge { display: inline-block; padding: 5px 15px; border-radius: 20px; font-size: 12px; font-weight: bold; text-transform: uppercase; }
        .status-confirmed { background: #d4edda; color: #155724; }
        .cta-button { display: inline-block; background: #a31515; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; margin: 20px 0; }
        .footer { text-align: center; margin-top: 20px; color: #666; font-size: 14px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎉 Booking Confirmed!</h1>
            <p>Your ${data.entityType} booking is confirmed</p>
        </div>
        <div class="content">
            <div class="booking-card">
                <h2>Hello ${data.userName}!</h2>
                <p>Great news! Your booking with <strong>${data.entityName}</strong> has been confirmed.</p>

                <div class="status-badge status-confirmed">✓ ${data.status}</div>

                <div class="booking-details">
                    <div class="detail-item">
                        <div class="detail-label">Booking ID</div>
                        <div class="detail-value">${data.bookingId}</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">${data.entityType === 'venue' ? 'Venue' : 'Vendor'}</div>
                        <div class="detail-value">${data.entityName}</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">Event Date</div>
                        <div class="detail-value">${new Date(data.eventDate).toLocaleDateString()}</div>
                    </div>
                    ${data.eventTime ? `
                    <div class="detail-item">
                        <div class="detail-label">Event Time</div>
                        <div class="detail-value">${data.eventTime}</div>
                    </div>
                    ` : ''}
                    ${data.amount ? `
                    <div class="detail-item">
                        <div class="detail-label">Amount</div>
                        <div class="detail-value">${data.amount}</div>
                    </div>
                    ` : ''}
                </div>

                <div style="text-align: center;">
                    <a href="${data.trackingUrl || 'https://thirumanam360.com/dashboard/bookings'}" class="cta-button">Track Your Booking</a>
                </div>

                <h3>📋 What's Next?</h3>
                <ul>
                    <li>The ${data.entityType} will contact you within 24 hours to discuss details</li>
                    <li>You can track your booking status in your dashboard</li>
                    <li>Any changes or cancellations can be made through your account</li>
                    <li>We'll send you reminders as your event date approaches</li>
                </ul>

                <p><strong>Need Help?</strong> Our support team is here to assist you. Contact us anytime!</p>
            </div>
        </div>
        <div class="footer">
            <p>© 2025 BookmyFestive. All rights reserved.</p>
            <p>Making your wedding dreams come true! 💕</p>
        </div>
    </div>
</body>
</html>`,
    getText: (data) => `
Booking Confirmed - ${data.entityName} | BookmyFestive

Hello ${data.userName}!

Great news! Your booking with ${data.entityName} has been confirmed.

Booking Details:
- Booking ID: ${data.bookingId}
- ${data.entityType === 'venue' ? 'Venue' : 'Vendor'}: ${data.entityName}
- Event Date: ${new Date(data.eventDate).toLocaleDateString()}
${data.eventTime ? `- Event Time: ${data.eventTime}` : ''}
${data.amount ? `- Amount: ${data.amount}` : ''}
- Status: ${data.status}

What's Next?
- The ${data.entityType} will contact you within 24 hours to discuss details
- You can track your booking status in your dashboard
- Any changes or cancellations can be made through your account
- We'll send you reminders as your event date approaches

Track Your Booking: ${data.trackingUrl || 'https://thirumanam360.com/dashboard/bookings'}

Need Help? Our support team is here to assist you. Contact us anytime!

© 2025 BookmyFestive. All rights reserved.
Making your wedding dreams come true! 💕
`
  },

  PAYMENT_SUCCESS: {
    subject: 'Payment Successful - Order {{orderId}} | BookmyFestive',
    getHtml: (data) => `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment Successful - BookmyFestive</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: linear-gradient(135deg, #a31515, #d32f2f); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
        .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }
        .success-badge { background: #d4edda; color: #155724; padding: 15px; border-radius: 8px; text-align: center; margin: 20px 0; }
        .order-summary { background: white; padding: 25px; margin: 20px 0; border-radius: 8px; }
        .item-row { display: flex; justify-content: space-between; align-items: center; padding: 10px 0; border-bottom: 1px solid #eee; }
        .item-row:last-child { border-bottom: none; }
        .total-row { font-weight: bold; font-size: 16px; color: #a31515; }
        .cta-buttons { text-align: center; margin: 30px 0; }
        .cta-button { display: inline-block; background: #a31515; color: white; padding: 12px 25px; text-decoration: none; border-radius: 5px; margin: 0 10px; }
        .cta-button.secondary { background: #6c757d; }
        .footer { text-align: center; margin-top: 20px; color: #666; font-size: 14px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>💳 Payment Successful!</h1>
            <p>Thank you for your purchase</p>
        </div>
        <div class="content">
            <div class="success-badge">
                <h3>✅ Payment Confirmed</h3>
                <p>Your payment has been processed successfully</p>
            </div>

            <h2>Hello ${data.userName}!</h2>
            <p>Thank you for your order! Your payment has been successfully processed and your order is now being prepared.</p>

            <div class="order-summary">
                <h3>📦 Order Summary</h3>
                <p><strong>Order ID:</strong> ${data.orderId}</p>
                <p><strong>Total Amount:</strong> ${data.amount}</p>

                <h4>Items Ordered:</h4>
                ${data.items.map(item => `
                <div class="item-row">
                    <div>
                        <strong>${item.name}</strong><br>
                        <small>Quantity: ${item.quantity}</small>
                    </div>
                    <div>${item.price}</div>
                </div>
                `).join('')}

                <div class="item-row total-row">
                    <div>Total Amount</div>
                    <div>${data.amount}</div>
                </div>
            </div>

            <div class="cta-buttons">
                ${data.invoiceUrl ? `<a href="${data.invoiceUrl}" class="cta-button">Download Invoice</a>` : ''}
                <a href="${data.trackingUrl || 'https://thirumanam360.com/dashboard/orders'}" class="cta-button secondary">Track Order</a>
            </div>

            <h3>📋 What Happens Next?</h3>
            <ul>
                <li>You'll receive an order confirmation shortly</li>
                <li>We'll prepare your items and notify you when they're ready</li>
                <li>Track your order status in your dashboard</li>
                <li>Delivery updates will be sent to your email</li>
            </ul>

            <p><strong>Questions?</strong> Contact our support team anytime. We're here to help!</p>
        </div>
        <div class="footer">
            <p>© 2025 BookmyFestive. All rights reserved.</p>
            <p>Making your wedding dreams come true! 💕</p>
        </div>
    </div>
</body>
</html>`,
    getText: (data) => `
Payment Successful - Order ${data.orderId} | BookmyFestive

Hello ${data.userName}!

✅ PAYMENT CONFIRMED
Your payment has been processed successfully

Thank you for your order! Your payment has been successfully processed and your order is now being prepared.

Order Summary:
- Order ID: ${data.orderId}
- Total Amount: ${data.amount}

Items Ordered:
${data.items.map(item => `- ${item.name} (Qty: ${item.quantity}) - ${item.price}`).join('\n')}

Total: ${data.amount}

${data.invoiceUrl ? `Download Invoice: ${data.invoiceUrl}` : ''}
Track Order: ${data.trackingUrl || 'https://thirumanam360.com/dashboard/orders'}

What Happens Next?
- You'll receive an order confirmation shortly
- We'll prepare your items and notify you when they're ready
- Track your order status in your dashboard
- Delivery updates will be sent to your email

Questions? Contact our support team anytime. We're here to help!

© 2025 BookmyFestive. All rights reserved.
Making your wedding dreams come true! 💕
`
  },

  WEEKLY_NEWS: {
    subject: 'This Week\'s Wedding Updates from BookmyFestive',
    getHtml: (data) => `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Weekly Wedding Updates - BookmyFestive</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: linear-gradient(135deg, #a31515, #d32f2f); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
        .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }
        .article { background: white; padding: 20px; margin-bottom: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.05); }
        .article-image { width: 100%; height: 150px; object-fit: cover; border-radius: 6px; margin-bottom: 15px; }
        .article-title { font-size: 18px; font-weight: bold; margin-bottom: 10px; color: #a31515; }
        .article-excerpt { font-size: 14px; color: #555; margin-bottom: 15px; }
        .read-more { display: inline-block; background: #a31515; color: white; padding: 8px 15px; text-decoration: none; border-radius: 4px; font-size: 14px; }
        .offers-section { background: #fff8e1; padding: 20px; border-radius: 8px; margin: 20px 0; }
        .offer-item { margin-bottom: 15px; padding-bottom: 15px; border-bottom: 1px solid #eee; }
        .offer-item:last-child { border-bottom: none; margin-bottom: 0; padding-bottom: 0; }
        .offer-title { font-weight: bold; color: #a31515; }
        .offer-discount { display: inline-block; background: #a31515; color: white; padding: 3px 8px; border-radius: 4px; font-size: 12px; margin: 5px 0; }
        .footer { text-align: center; margin-top: 20px; color: #666; font-size: 14px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📰 Weekly Wedding Updates</h1>
            <p>Stay Inspired with BookmyFestive</p>
        </div>
        <div class="content">
            <h2>Hello ${data.userName || 'there'}!</h2>
            <p>Here are this week's top wedding trends, tips, and inspiration just for you:</p>

            <div class="articles-section">
                ${data.articles.map(article => `
                <div class="article">
                    ${article.image ? `<img src="${article.image}" alt="${article.title}" class="article-image">` : ''}
                    <div class="article-title">${article.title}</div>
                    <div class="article-excerpt">${article.excerpt}</div>
                    <a href="${article.url}" class="read-more">Read More</a>
                </div>
                `).join('')}
            </div>

            ${data.offers && data.offers.length > 0 ? `
            <div class="offers-section">
                <h3>🎁 Special Offers This Week</h3>
                ${data.offers.map(offer => `
                <div class="offer-item">
                    <div class="offer-title">${offer.title}</div>
                    <div class="offer-discount">${offer.discount} OFF</div>
                    <div><a href="${offer.url}">View Offer</a></div>
                </div>
                `).join('')}
            </div>
            ` : ''}

            <p>We hope you find these updates helpful for your wedding planning journey!</p>
            <p>Happy Planning,<br>The BookmyFestive Team</p>
        </div>
        <div class="footer">
            <p>© 2025 BookmyFestive. All rights reserved.</p>
            <p>You're receiving this email because you subscribed to our newsletter.</p>
            <p><a href="https://thirumanam360.com/preferences">Manage Preferences</a> | <a href="https://thirumanam360.com/unsubscribe">Unsubscribe</a></p>
        </div>
    </div>
</body>
</html>`,
    getText: (data) => `
Weekly Wedding Updates from BookmyFestive

Hello ${data.userName || 'there'}!

Here are this week's top wedding trends, tips, and inspiration just for you:

${data.articles.map(article => `
${article.title}
${article.excerpt}
Read More: ${article.url}
`).join('\n')}

${data.offers && data.offers.length > 0 ? `
Special Offers This Week:
${data.offers.map(offer => `
${offer.title} - ${offer.discount} OFF
View Offer: ${offer.url}
`).join('\n')}
` : ''}

We hope you find these updates helpful for your wedding planning journey!

Happy Planning,
The BookmyFestive Team

© 2025 BookmyFestive. All rights reserved.
You're receiving this email because you subscribed to our newsletter.
Manage Preferences: https://thirumanam360.com/preferences
Unsubscribe: https://thirumanam360.com/unsubscribe
`
  },

  OFFERS_MAIL: {
    subject: 'Exclusive Wedding Offers Just for You! 🎁',
    getHtml: (data) => `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Exclusive Wedding Offers - BookmyFestive</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: linear-gradient(135deg, #a31515, #d32f2f); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
        .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }
        .offer-card { background: white; border-radius: 8px; overflow: hidden; margin-bottom: 25px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); }
        .offer-image { width: 100%; height: 180px; object-fit: cover; }
        .offer-content { padding: 20px; }
        .offer-title { font-size: 18px; font-weight: bold; color: #a31515; margin-bottom: 10px; }
        .offer-description { font-size: 14px; color: #555; margin-bottom: 15px; }
        .offer-meta { display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px; }
        .offer-discount { background: #a31515; color: white; padding: 5px 10px; border-radius: 20px; font-weight: bold; }
        .offer-validity { font-size: 13px; color: #777; }
        .offer-button { display: inline-block; background: #a31515; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px; font-weight: bold; }
        .countdown { background: #fff8e1; padding: 15px; border-radius: 8px; text-align: center; margin: 20px 0; }
        .footer { text-align: center; margin-top: 20px; color: #666; font-size: 14px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎁 Exclusive Wedding Offers</h1>
            <p>Special Deals Just for You</p>
        </div>
        <div class="content">
            <h2>Hello ${data.userName}!</h2>
            <p>We've curated some amazing offers from our top wedding vendors just for you:</p>

            <div class="countdown">
                <h3>⏰ Limited Time Offers</h3>
                <p>Don't miss out on these exclusive deals for BookmyFestive members!</p>
            </div>

            ${data.offers.map(offer => `
            <div class="offer-card">
                ${offer.image ? `<img src="${offer.image}" alt="${offer.title}" class="offer-image">` : ''}
                <div class="offer-content">
                    <div class="offer-title">${offer.title}</div>
                    <div class="offer-description">${offer.description}</div>
                    <div class="offer-meta">
                        <div class="offer-discount">${offer.discount}</div>
                        <div class="offer-validity">Valid until: ${new Date(offer.validUntil).toLocaleDateString()}</div>
                    </div>
                    <a href="${offer.url}" class="offer-button">Claim Offer</a>
                </div>
            </div>
            `).join('')}

            <p>These offers are exclusively available to our BookmyFestive members. Simply click on the offer you're interested in to claim it.</p>
            <p>Happy Wedding Planning!</p>
        </div>
        <div class="footer">
            <p>© 2025 BookmyFestive. All rights reserved.</p>
            <p>You're receiving this email because you opted in for special offers.</p>
            <p><a href="https://thirumanam360.com/preferences">Manage Preferences</a> | <a href="https://thirumanam360.com/unsubscribe">Unsubscribe</a></p>
        </div>
    </div>
</body>
</html>`,
    getText: (data) => `
Exclusive Wedding Offers Just for You! 🎁

Hello ${data.userName}!

We've curated some amazing offers from our top wedding vendors just for you:

LIMITED TIME OFFERS - Don't miss out on these exclusive deals for BookmyFestive members!

${data.offers.map(offer => `
${offer.title}
${offer.description}
Discount: ${offer.discount}
Valid until: ${new Date(offer.validUntil).toLocaleDateString()}
Claim Offer: ${offer.url}
`).join('\n')}

These offers are exclusively available to our BookmyFestive members. Simply click on the offer you're interested in to claim it.

Happy Wedding Planning!

© 2025 BookmyFestive. All rights reserved.
You're receiving this email because you opted in for special offers.
Manage Preferences: https://thirumanam360.com/preferences
Unsubscribe: https://thirumanam360.com/unsubscribe
`
  },

  FAVORITES_NOTIFICATION: {
    subject: 'Updates on Your Favorite Vendors & Venues',
    getHtml: (data) => `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Updates on Your Favorites - BookmyFestive</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: linear-gradient(135deg, #a31515, #d32f2f); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
        .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }
        .update-card { background: white; border-radius: 8px; padding: 20px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.05); }
        .update-title { font-size: 16px; font-weight: bold; margin-bottom: 5px; }
        .update-type { display: inline-block; padding: 3px 8px; border-radius: 12px; font-size: 12px; margin-bottom: 10px; }
        .update-type-price { background: #e8f5e9; color: #2e7d32; }
        .update-type-photos { background: #e3f2fd; color: #1565c0; }
        .update-type-availability { background: #fff8e1; color: #f57f17; }
        .update-type-offer { background: #fce4ec; color: #c2185b; }
        .update-button { display: inline-block; background: #a31515; color: white; padding: 6px 12px; text-decoration: none; border-radius: 4px; font-size: 13px; }
        .footer { text-align: center; margin-top: 20px; color: #666; font-size: 14px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>❤️ Updates on Your Favorites</h1>
            <p>New Updates from Vendors & Venues You Love</p>
        </div>
        <div class="content">
            <h2>Hello ${data.userName}!</h2>
            <p>There are new updates from the vendors and venues you've favorited:</p>

            ${data.updates.map(update => `
            <div class="update-card">
                <div class="update-title">${update.name}</div>
                <div class="update-type update-type-${update.updateType.split('_')[0]}">
                    ${update.updateType === 'price_drop' ? '💰 Price Drop' :
                      update.updateType === 'new_photos' ? '📸 New Photos' :
                      update.updateType === 'availability' ? '📅 New Availability' :
                      update.updateType === 'offer' ? '🎁 Special Offer' : 'Update'}
                </div>
                <p>${
                    update.updateType === 'price_drop' ? 'Prices have been reduced! Check out the new rates.' :
                    update.updateType === 'new_photos' ? 'New photos have been added to the gallery.' :
                    update.updateType === 'availability' ? 'New dates are now available for booking.' :
                    update.updateType === 'offer' ? 'A special offer is now available for you.' :
                    'There are new updates available.'
                }</p>
                <a href="${update.url}" class="update-button">View Details</a>
            </div>
            `).join('')}

            <p>Don't miss out on these updates! Click on each item to see the details.</p>
            <p>Happy Wedding Planning!</p>
        </div>
        <div class="footer">
            <p>© 2025 BookmyFestive. All rights reserved.</p>
            <p><a href="https://thirumanam360.com/unsubscribe">Unsubscribe</a></p>
        </div>
    </div>
</body>
</html>`,
    getText: (data) => `
Updates on Your Favorite Vendors & Venues

Hello ${data.userName}!

There are new updates from the vendors and venues you've favorited:

${data.updates.map(update => `
${update.name}
${update.updateType === 'price_drop' ? '💰 Price Drop' :
  update.updateType === 'new_photos' ? '📸 New Photos' :
  update.updateType === 'availability' ? '📅 New Availability' :
  update.updateType === 'offer' ? '🎁 Special Offer' : 'Update'}

${update.updateType === 'price_drop' ? 'Prices have been reduced! Check out the new rates.' :
  update.updateType === 'new_photos' ? 'New photos have been added to the gallery.' :
  update.updateType === 'availability' ? 'New dates are now available for booking.' :
  update.updateType === 'offer' ? 'A special offer is now available for you.' :
  'There are new updates available.'}

View Details: ${update.url}
`).join('\n')}

Don't miss out on these updates! Click on each item to see the details.

Happy Wedding Planning!

© 2025 BookmyFestive. All rights reserved.
Unsubscribe: https://thirumanam360.com/unsubscribe
`
  },

  VENDOR_LAUNCH: {
    subject: 'New Wedding Vendors Just Launched! 🚀',
    getHtml: (data) => `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>New Vendor Launch - BookmyFestive</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: linear-gradient(135deg, #a31515, #d32f2f); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
        .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }
        .vendor-card { background: white; border-radius: 8px; padding: 20px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.05); }
        .vendor-name { font-size: 18px; font-weight: bold; color: #a31515; margin-bottom: 5px; }
        .vendor-category { background: #f8f9fa; color: #6c757d; padding: 3px 8px; border-radius: 12px; font-size: 12px; display: inline-block; margin-bottom: 10px; }
        .vendor-location { color: #666; font-size: 14px; margin-bottom: 10px; }
        .launch-discount { background: #d4edda; color: #155724; padding: 10px; border-radius: 6px; margin: 10px 0; text-align: center; font-weight: bold; }
        .vendor-button { display: inline-block; background: #a31515; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px; font-size: 14px; }
        .footer { text-align: center; margin-top: 20px; color: #666; font-size: 14px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 New Vendors Just Launched!</h1>
            <p>Discover Amazing New Wedding Professionals</p>
        </div>
        <div class="content">
            <h2>Hello ${data.userName}!</h2>
            <p>We're excited to introduce you to some fantastic new wedding vendors who just joined BookmyFestive:</p>

            ${data.newVendors.map(vendor => `
            <div class="vendor-card">
                <div class="vendor-name">${vendor.name}</div>
                <div class="vendor-category">${vendor.category}</div>
                <div class="vendor-location">📍 ${vendor.city}</div>

                ${vendor.discount ? `
                <div class="launch-discount">
                    🎉 Launch Special: ${vendor.discount} OFF for early bookings!
                </div>
                ` : ''}

                <a href="${vendor.url}" class="vendor-button">View Profile</a>
            </div>
            `).join('')}

            <p>These vendors are offering special launch discounts for early bookings. Don't miss out on these amazing deals!</p>
            <p>Happy Wedding Planning!</p>
        </div>
        <div class="footer">
            <p>© 2025 BookmyFestive. All rights reserved.</p>
            <p><a href="https://thirumanam360.com/unsubscribe">Unsubscribe</a></p>
        </div>
    </div>
</body>
</html>`,
    getText: (data) => `
New Wedding Vendors Just Launched! 🚀

Hello ${data.userName}!

We're excited to introduce you to some fantastic new wedding vendors who just joined BookmyFestive:

${data.newVendors.map(vendor => `
${vendor.name}
Category: ${vendor.category}
Location: ${vendor.city}
${vendor.discount ? `Launch Special: ${vendor.discount} OFF for early bookings!` : ''}
View Profile: ${vendor.url}
`).join('\n')}

These vendors are offering special launch discounts for early bookings. Don't miss out on these amazing deals!

Happy Wedding Planning!

© 2025 BookmyFestive. All rights reserved.
Unsubscribe: https://thirumanam360.com/unsubscribe
`
  },

  AVAILABILITY_CHECK: {
    subject: 'Availability Update - {{entityName}} | BookmyFestive',
    getHtml: (data) => `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Availability Update - BookmyFestive</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: linear-gradient(135deg, #a31515, #d32f2f); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
        .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }
        .availability-card { background: white; padding: 25px; margin: 20px 0; border-radius: 8px; border-left: 4px solid #a31515; }
        .status-available { border-left-color: #28a745; }
        .status-unavailable { border-left-color: #dc3545; }
        .status-partial { border-left-color: #ffc107; }
        .status-badge { display: inline-block; padding: 5px 15px; border-radius: 20px; font-size: 12px; font-weight: bold; text-transform: uppercase; }
        .status-available-badge { background: #d4edda; color: #155724; }
        .status-unavailable-badge { background: #f8d7da; color: #721c24; }
        .status-partial-badge { background: #fff3cd; color: #856404; }
        .alternative-dates { background: #f8f9fa; padding: 15px; border-radius: 6px; margin: 15px 0; }
        .date-item { background: white; padding: 10px; margin: 5px 0; border-radius: 4px; border-left: 3px solid #a31515; }
        .cta-button { display: inline-block; background: #a31515; color: white; padding: 12px 25px; text-decoration: none; border-radius: 5px; margin: 20px 0; }
        .footer { text-align: center; margin-top: 20px; color: #666; font-size: 14px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📅 Availability Update</h1>
            <p>Your requested date status</p>
        </div>
        <div class="content">
            <div class="availability-card status-${data.status.replace('_', '-')}">
                <h2>Hello ${data.userName}!</h2>
                <p>We have an update on your availability request for <strong>${data.entityName}</strong>.</p>

                <div class="status-badge status-${data.status.replace('_', '-')}-badge">
                    ${data.status === 'available' ? '✅ Available' :
                      data.status === 'unavailable' ? '❌ Not Available' :
                      '⚠️ Partially Available'}
                </div>

                <h3>📋 Request Details:</h3>
                <ul>
                    <li><strong>${data.entityType === 'venue' ? 'Venue' : 'Vendor'}:</strong> ${data.entityName}</li>
                    <li><strong>Requested Date:</strong> ${new Date(data.requestedDate).toLocaleDateString()}</li>
                    <li><strong>Status:</strong> ${data.status.replace('_', ' ').toUpperCase()}</li>
                </ul>

                ${data.status === 'available' ? `
                <div style="background: #d4edda; padding: 15px; border-radius: 6px; margin: 15px 0;">
                    <h4 style="color: #155724; margin: 0 0 10px 0;">🎉 Great News!</h4>
                    <p style="color: #155724; margin: 0;">Your requested date is available! Contact the ${data.entityType} to proceed with booking.</p>
                </div>
                ` : data.status === 'unavailable' ? `
                <div style="background: #f8d7da; padding: 15px; border-radius: 6px; margin: 15px 0;">
                    <h4 style="color: #721c24; margin: 0 0 10px 0;">😔 Unfortunately</h4>
                    <p style="color: #721c24; margin: 0;">Your requested date is not available. Please check the alternative dates below.</p>
                </div>
                ` : `
                <div style="background: #fff3cd; padding: 15px; border-radius: 6px; margin: 15px 0;">
                    <h4 style="color: #856404; margin: 0 0 10px 0;">⚠️ Partial Availability</h4>
                    <p style="color: #856404; margin: 0;">Your requested date has limited availability. Contact the ${data.entityType} for more details.</p>
                </div>
                `}

                ${data.alternativeDates && data.alternativeDates.length > 0 ? `
                <div class="alternative-dates">
                    <h4>📅 Alternative Available Dates:</h4>
                    ${data.alternativeDates.map(date => `
                    <div class="date-item">
                        ${new Date(date).toLocaleDateString()} - Available
                    </div>
                    `).join('')}
                </div>
                ` : ''}

                <div style="text-align: center;">
                    <a href="${data.url}" class="cta-button">View ${data.entityType === 'venue' ? 'Venue' : 'Vendor'} Details</a>
                </div>

                <p><strong>Next Steps:</strong> Contact the ${data.entityType} directly to discuss your requirements and finalize the booking.</p>
            </div>
        </div>
        <div class="footer">
            <p>© 2025 BookmyFestive. All rights reserved.</p>
            <p>Making your wedding dreams come true! 💕</p>
        </div>
    </div>
</body>
</html>`,
    getText: (data) => `
Availability Update - ${data.entityName} | BookmyFestive

Hello ${data.userName}!

We have an update on your availability request for ${data.entityName}.

Request Details:
- ${data.entityType === 'venue' ? 'Venue' : 'Vendor'}: ${data.entityName}
- Requested Date: ${new Date(data.requestedDate).toLocaleDateString()}
- Status: ${data.status.replace('_', ' ').toUpperCase()}

${data.status === 'available' ?
  '🎉 Great News! Your requested date is available! Contact the ' + data.entityType + ' to proceed with booking.' :
  data.status === 'unavailable' ?
  '😔 Unfortunately, your requested date is not available. Please check the alternative dates below.' :
  '⚠️ Your requested date has limited availability. Contact the ' + data.entityType + ' for more details.'
}

${data.alternativeDates && data.alternativeDates.length > 0 ? `
Alternative Available Dates:
${data.alternativeDates.map(date => `- ${new Date(date).toLocaleDateString()} - Available`).join('\n')}
` : ''}

View ${data.entityType === 'venue' ? 'Venue' : 'Vendor'} Details: ${data.url}

Next Steps: Contact the ${data.entityType} directly to discuss your requirements and finalize the booking.

© 2025 BookmyFestive. All rights reserved.
Making your wedding dreams come true! 💕
`
  }
};

exports.handler = async (event) => {
  console.log('Event:', JSON.stringify(event, null, 2));
  
  try {
    const { emailType, recipient, templateData, userId, vendorId, bookingId, orderId } = event.arguments.input;
    
    // Parse template data
    const data = JSON.parse(templateData);
    
    // Get email template
    const template = EMAIL_TEMPLATES[emailType];
    if (!template) {
      throw new Error(`Email template not found for type: ${emailType}`);
    }
    
    // Generate email content
    const subject = template.subject.replace(/\{\{(\w+)\}\}/g, (match, key) => data[key] || match);
    const htmlContent = template.getHtml(data);
    const textContent = template.getText(data);
    
    // Send email using AWS SES
    const messageId = await sendEmailWithSES(recipient, subject, htmlContent, textContent);
    
    // Log email in database
    const emailLogId = await logEmailToDatabase({
      messageId,
      emailType,
      recipient,
      subject,
      status: 'SENT',
      templateData,
      userId,
      vendorId,
      bookingId,
      orderId
    });
    
    return {
      success: true,
      messageId,
      emailLogId
    };
    
  } catch (error) {
    console.error('Error sending email:', error);
    
    // Log failed email
    await logEmailToDatabase({
      messageId: null,
      emailType: event.arguments.input.emailType,
      recipient: event.arguments.input.recipient,
      subject: 'Failed to send',
      status: 'FAILED',
      errorMessage: error.message,
      templateData: event.arguments.input.templateData,
      userId: event.arguments.input.userId,
      vendorId: event.arguments.input.vendorId,
      bookingId: event.arguments.input.bookingId,
      orderId: event.arguments.input.orderId
    });
    
    return {
      success: false,
      error: error.message
    };
  }
};

async function sendEmailWithSES(recipient, subject, htmlContent, textContent) {
  const params = {
    Source: `BookmyFestive <${process.env.FROM_EMAIL || '<EMAIL>'}>`,
    Destination: {
      ToAddresses: [recipient]
    },
    Message: {
      Subject: {
        Data: subject,
        Charset: 'UTF-8'
      },
      Body: {
        Html: {
          Data: htmlContent,
          Charset: 'UTF-8'
        },
        Text: {
          Data: textContent,
          Charset: 'UTF-8'
        }
      }
    }
  };
  
  const result = await ses.sendEmail(params).promise();
  return result.MessageId;
}

async function logEmailToDatabase(logData) {
  const emailLogId = uuidv4();
  
  const params = {
    TableName: process.env.EMAILLOG_TABLE,
    Item: {
      id: emailLogId,
      messageId: logData.messageId,
      emailType: logData.emailType,
      recipient: logData.recipient,
      subject: logData.subject,
      status: logData.status,
      errorMessage: logData.errorMessage,
      templateData: logData.templateData,
      sentAt: new Date().toISOString(),
      userId: logData.userId,
      vendorId: logData.vendorId,
      bookingId: logData.bookingId,
      orderId: logData.orderId,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
  };
  
  await dynamodb.put(params).promise();
  return emailLogId;
}
