import { generateClient } from 'aws-amplify/api';
import { createInquiry, updateInquiry, deleteInquiry } from '@/src/graphql/mutations';
import { listInquiries, getInquiry } from '@/src/graphql/queries';
import { onCreateInquiry, onUpdateInquiry, onDeleteInquiry } from '@/src/graphql/subscriptions';
import { vendorNotificationService, VendorNotificationData } from './vendorNotificationService';

const client = generateClient();
const authenticatedClient = generateClient();
const publicClient = generateClient();

// Local storage for inquiries when database is not available
const LOCAL_STORAGE_KEY = 'thirumanam_inquiries';

// Helper functions for local storage
const getStoredInquiries = (): InquiryResponse[] => {
  if (typeof window === 'undefined') return [];
  try {
    const stored = localStorage.getItem(LOCAL_STORAGE_KEY);
    return stored ? JSON.parse(stored) : [];
  } catch {
    return [];
  }
};

const storeInquiry = (inquiry: InquiryResponse): void => {
  if (typeof window === 'undefined') return;
  try {
    const existing = getStoredInquiries();
    const updated = [inquiry, ...existing];
    localStorage.setItem(LOCAL_STORAGE_KEY, JSON.stringify(updated));
  } catch (error) {
    console.warn('Failed to store inquiry locally:', error);
  }
};

export interface InquiryInput {
  vendorUserId: string;
  vendorId: string;
  vendorName: string;
  customerUserId?: string;
  customerName: string;
  customerEmail: string;
  customerPhone?: string;
  eventDate?: string;
  message: string;
  inquiryType: 'VENDOR_INQUIRY' | 'SERVICE_QUOTE' | 'AVAILABILITY_CHECK' | 'GENERAL_QUESTION' | 'BOOKING_REQUEST' | 'VENUE_INQUIRY';
  budget?: string;
  guestCount?: string;
  venue?: string;
  additionalServices?: string[];
  preferredContactTime?: string;
}

export interface InquiryResponse {
  id: string;
  vendorUserId: string;
  vendorId: string;
  vendorName: string;
  customerUserId?: string;
  customerName: string;
  customerEmail: string;
  customerPhone?: string;
  eventDate?: string;
  message: string;
  inquiryType: string;
  status: string;
  priority?: string;
  budget?: string;
  guestCount?: string;
  venue?: string;
  additionalServices?: string[];
  preferredContactTime?: string;
  responseMessage?: string;
  respondedAt?: string;
  assignedTo?: string;
  followUpDate?: string;
  notes?: string;
  createdAt: string;
  updatedAt: string;
}

export interface InquiryUpdateInput {
  id: string;
  status?: 'NEW' | 'CONTACTED' | 'QUOTED' | 'NEGOTIATING' | 'CONFIRMED' | 'COMPLETED' | 'CANCELLED' | 'EXPIRED';
  priority?: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT';
  responseMessage?: string;
  assignedTo?: string;
  followUpDate?: string;
  notes?: string;
}

export interface ListInquiriesOptions {
  vendorUserId?: string;
  vendorId?: string;
  customerUserId?: string;
  status?: string;
  priority?: string;
  limit?: number;
  nextToken?: string;
}

class InquiryService {
  /**
   * Create a new inquiry (using Inquiry model)
   */
  async createInquiry(input: InquiryInput): Promise<InquiryResponse> {
    console.log('Creating inquiry with input:', input);

    try {
      const inquiryData = {
        vendorUserId: input.vendorUserId,
        vendorId: input.vendorId,
        vendorName: input.vendorName,
        customerUserId: input.customerUserId,
        customerName: input.customerName,
        customerEmail: input.customerEmail,
        customerPhone: input.customerPhone,
        eventDate: input.eventDate,
        message: input.message,
        inquiryType: input.inquiryType,
        status: 'NEW',
        priority: 'MEDIUM',
        budget: input.budget,
        guestCount: input.guestCount,
        venue: input.venue,
        additionalServices: input.additionalServices,
        preferredContactTime: input.preferredContactTime
      };

      console.log('Creating inquiry with data:', inquiryData);

      const result = await client.graphql({
        query: createInquiry,
        variables: { input: inquiryData }
      });

      const inquiry = result.data.createInquiry;
      console.log('Inquiry created successfully:', inquiry);

      // Send vendor notification email asynchronously
      this.sendVendorNotification(inquiry, input).catch(error => {
        console.error('Failed to send vendor notification:', error);
        // Don't throw error here as inquiry was created successfully
      });

      return inquiry;
    } catch (error) {
      console.error('Error creating inquiry:', error);

      // Fallback: If authorization error, create a mock response
      if (error.message?.includes('Not Authorized') || error.message?.includes('Unauthorized')) {
        console.log('Authorization error - creating mock response until schema is deployed');

        const mockInquiry: InquiryResponse = {
          id: `mock-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
          vendorUserId: inquiryData.vendorUserId,
          vendorId: inquiryData.vendorId,
          vendorName: inquiryData.vendorName,
          customerUserId: inquiryData.customerUserId,
          customerName: inquiryData.customerName,
          customerEmail: inquiryData.customerEmail,
          customerPhone: inquiryData.customerPhone,
          eventDate: inquiryData.eventDate,
          message: inquiryData.message,
          inquiryType: inquiryData.inquiryType,
          status: 'NEW',
          priority: 'MEDIUM',
          budget: inquiryData.budget,
          guestCount: inquiryData.guestCount,
          venue: inquiryData.venue,
          additionalServices: inquiryData.additionalServices,
          preferredContactTime: inquiryData.preferredContactTime,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        };

        console.log('Mock inquiry created:', mockInquiry);

        // Store mock inquiry locally for persistence
        storeInquiry(mockInquiry);

        // Send vendor notification for mock inquiry too
        this.sendVendorNotification(mockInquiry, input).catch(error => {
          console.error('Failed to send vendor notification for mock inquiry:', error);
        });

        return mockInquiry;
      }

      throw new Error('Failed to create inquiry. Please try again.');
    }
  }

  /**
   * Get inquiry by ID (using Inquiry model)
   */
  async getInquiry(id: string): Promise<InquiryResponse> {
    console.log('Getting inquiry by ID:', id);

    try {
      const result = await client.graphql({
        query: getInquiry,
        variables: { id }
      });

      if (!result.data.getInquiry) {
        throw new Error('Inquiry not found');
      }

      const inquiry = result.data.getInquiry;
      console.log('Found inquiry:', inquiry);
      return inquiry;
    } catch (error) {
      console.error('Error fetching inquiry:', error);
      throw new Error('Failed to fetch inquiry');
    }
  }



  /**
   * List inquiries with optional filters
   */
  async listInquiries(options: ListInquiriesOptions = {}): Promise<{
    items: InquiryResponse[];
    nextToken?: string;
  }> {
    console.log('listInquiries called with options:', options);

    try {
      const { vendorUserId, vendorId, customerUserId, limit = 20, nextToken } = options;

      let filter: any = {};

      // Build filter based on options
      if (vendorUserId) {
        filter.vendorUserId = { eq: vendorUserId };
      }
      if (vendorId) {
        filter.vendorId = { eq: vendorId };
      }
      if (customerUserId) {
        filter.customerUserId = { eq: customerUserId };
      }

      const variables: any = { limit, nextToken };
      if (Object.keys(filter).length > 0) {
        variables.filter = filter;
      }

      console.log('Querying inquiries with variables:', variables);

      // Try authenticated client first (for logged-in users)
      let result;
      try {
        result = await authenticatedClient.graphql({
          query: listInquiries,
          variables
        });
      } catch (authError) {
        console.log('Authenticated listInquiries failed, trying with API key:', authError);
        // Fallback to public client with API key for admin operations
        result = await publicClient.graphql({
          query: listInquiries,
          variables
        });
      }

      const inquiries = result.data.listInquiries.items;
      console.log('Returning API inquiries:', inquiries);

      return {
        items: inquiries,
        nextToken: result.data.listInquiries.nextToken
      };
    } catch (error) {
      console.error('Error listing inquiries:', error);

      // Fallback: If authorization error, return sample data until schema is deployed
      if (error.message?.includes('Not Authorized') || error.message?.includes('Unauthorized')) {
        console.log('Authorization error - using sample data until schema is deployed');

        return this.getMockInquiries(options);
      }

      throw new Error('Failed to list inquiries. Please try again.');
    }
  }

  /**
   * Generate mock inquiries for testing when authorization is not available
   */
  private getMockInquiries(options: ListInquiriesOptions): { items: InquiryResponse[], nextToken?: string } {
    const { vendorUserId, status, priority } = options;

    // Get locally stored inquiries
    const storedInquiries = getStoredInquiries();

    const allMockInquiries: InquiryResponse[] = [
      {
        id: 'mock-inquiry-1',
        vendorUserId: vendorUserId || 'current-user',
        vendorId: 'vendor-1',
        vendorName: 'Sample Photography Studio',
        customerUserId: 'customer-1',
        customerName: 'There',
        customerEmail: '<EMAIL>',
        customerPhone: '+91 8148376909',
        eventDate: '2024-12-25',
        message: 'Hi, I am interested in your photography services for my wedding on December 25th. Could you please share your packages and pricing? We are looking for both candid and traditional photography.',
        inquiryType: 'VENDOR_INQUIRY',
        status: 'NEW',
        priority: 'MEDIUM',
        budget: '₹50,000 - ₹75,000',
        guestCount: '150',
        venue: 'Grand Palace Hotel',
        additionalServices: ['Pre-wedding shoot', 'Album design'],
        preferredContactTime: 'Evening',
        createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(), // 2 hours ago
        updatedAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString()
      },
      {
        id: 'mock-inquiry-2',
        vendorUserId: vendorUserId || 'current-user',
        vendorId: 'venue-1',
        vendorName: 'Grand Palace Hotel',
        customerUserId: 'customer-2',
        customerName: 'Priya Sharma',
        customerEmail: '<EMAIL>',
        customerPhone: '+91 9876543211',
        eventDate: '2024-11-15',
        message: 'We are looking for a venue for our wedding reception. Can you check availability for November 15th and share the pricing details? We need accommodation for 300 guests.',
        inquiryType: 'VENUE_INQUIRY',
        status: 'CONTACTED',
        priority: 'HIGH',
        budget: '₹2,00,000 - ₹3,00,000',
        guestCount: '300',
        venue: 'Requested Venue',
        additionalServices: ['Catering', 'Decoration', 'Music'],
        preferredContactTime: 'Morning',
        responseMessage: 'Thank you for your inquiry. We have availability for November 15th. Please call us to discuss the details.',
        respondedAt: new Date(Date.now() - 30 * 60 * 1000).toISOString(), // 30 minutes ago
        createdAt: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(), // 4 hours ago
        updatedAt: new Date(Date.now() - 30 * 60 * 1000).toISOString()
      },
      {
        id: 'mock-inquiry-3',
        vendorUserId: vendorUserId || 'current-user',
        vendorId: 'vendor-2',
        vendorName: 'Elegant Decorators',
        customerUserId: 'customer-3',
        customerName: 'Rahul Kumar',
        customerEmail: '<EMAIL>',
        eventDate: '2025-01-20',
        message: 'Need decoration services for engagement ceremony. Please provide quote for floral decorations and stage setup.',
        inquiryType: 'SERVICE_QUOTE',
        status: 'QUOTED',
        priority: 'MEDIUM',
        budget: '₹25,000 - ₹40,000',
        guestCount: '100',
        preferredContactTime: 'Afternoon',
        responseMessage: 'We have sent you a detailed quote via email. Please review and let us know if you have any questions.',
        respondedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(), // 2 days ago
        createdAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(), // 3 days ago
        updatedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString()
      },
      {
        id: 'mock-inquiry-4',
        vendorUserId: vendorUserId || 'current-user',
        vendorId: 'vendor-3',
        vendorName: 'Melody Music Band',
        customerUserId: 'customer-4',
        customerName: 'Anita Patel',
        customerEmail: '<EMAIL>',
        customerPhone: '+91 9876543213',
        eventDate: '2024-10-30',
        message: 'Looking for live music band for sangeet ceremony. Do you have availability for October 30th?',
        inquiryType: 'AVAILABILITY_CHECK',
        status: 'CONFIRMED',
        priority: 'HIGH',
        budget: '₹15,000 - ₹25,000',
        guestCount: '80',
        preferredContactTime: 'Evening',
        responseMessage: 'Yes, we are available for October 30th. Booking confirmed with advance payment.',
        respondedAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(), // 5 days ago
        createdAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 days ago
        updatedAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString()
      },
      {
        id: 'mock-inquiry-5',
        vendorUserId: vendorUserId || 'current-user',
        vendorId: 'vendor-4',
        vendorName: 'Gourmet Caterers',
        customerUserId: 'customer-5',
        customerName: 'Vikram Singh',
        customerEmail: '<EMAIL>',
        customerPhone: '+91 9876543214',
        eventDate: '2025-02-14',
        message: 'Need catering services for Valentine\'s Day wedding. Menu should include both vegetarian and non-vegetarian options.',
        inquiryType: 'BOOKING_REQUEST',
        status: 'NEGOTIATING',
        priority: 'MEDIUM',
        budget: '₹1,50,000 - ₹2,00,000',
        guestCount: '250',
        venue: 'Sunset Garden Resort',
        additionalServices: ['Live counters', 'Welcome drinks'],
        preferredContactTime: 'Morning',
        responseMessage: 'We have prepared a customized menu. Let\'s discuss the final pricing and arrangements.',
        respondedAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(), // 1 day ago
        createdAt: new Date(Date.now() - 6 * 24 * 60 * 60 * 1000).toISOString(), // 6 days ago
        updatedAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString()
      }
    ];

    // Combine stored inquiries with mock inquiries
    const combinedInquiries = [...storedInquiries, ...allMockInquiries];

    // Filter inquiries based on options
    let filteredInquiries = combinedInquiries;

    if (status && status !== 'ALL') {
      filteredInquiries = filteredInquiries.filter(inquiry => inquiry.status === status);
    }

    if (priority && priority !== 'ALL') {
      filteredInquiries = filteredInquiries.filter(inquiry => inquiry.priority === priority);
    }

    return {
      items: filteredInquiries.slice(0, options.limit || 20),
      nextToken: undefined
    };
  }

  /**
   * Update inquiry (using Inquiry model)
   */
  async updateInquiry(input: InquiryUpdateInput): Promise<InquiryResponse> {
    console.log('Updating inquiry with input:', input);

    try {
      // Start with basic fields that are usually allowed
      const updateData: any = {
        id: input.id
      };

      // Add fields conditionally to avoid authorization errors
      if (input.status !== undefined) {
        updateData.status = input.status;
      }
      if (input.priority !== undefined) {
        updateData.priority = input.priority;
      }

      // Add respondedAt timestamp if responseMessage is provided
      if (input.responseMessage && !input.responseMessage.includes('undefined')) {
        updateData.respondedAt = new Date().toISOString();
      }

      console.log('Updating inquiry with basic data:', updateData);

      // Try authenticated client first (for logged-in users)
      let result;
      try {
        result = await authenticatedClient.graphql({
          query: updateInquiry,
          variables: { input: updateData }
        });
      } catch (authError) {
        console.log('Authenticated update failed, trying with API key:', authError);
        // Fallback to public client with API key for dashboard operations
        result = await publicClient.graphql({
          query: updateInquiry,
          variables: { input: updateData }
        });
      }

      // If basic update succeeded, try to update admin-only fields separately
      if (input.assignedTo !== undefined || input.responseMessage !== undefined ||
          input.followUpDate !== undefined || input.notes !== undefined) {

        console.log('Attempting to update admin-only fields...');

        try {
          const adminUpdateData: any = {
            id: input.id
          };

          if (input.assignedTo !== undefined) {
            adminUpdateData.assignedTo = input.assignedTo;
          }
          if (input.responseMessage !== undefined) {
            adminUpdateData.responseMessage = input.responseMessage;
          }
          if (input.followUpDate !== undefined) {
            adminUpdateData.followUpDate = input.followUpDate;
          }
          if (input.notes !== undefined) {
            adminUpdateData.notes = input.notes;
          }

          // Try with authenticated client for admin fields
          const adminResult = await authenticatedClient.graphql({
            query: updateInquiry,
            variables: { input: adminUpdateData }
          });

          result = adminResult; // Use the admin result if successful
          console.log('Admin fields updated successfully');
        } catch (adminError) {
          console.warn('Failed to update admin-only fields:', adminError);
          // Continue with the basic update result
          console.log('Continuing with basic update only');
        }
      }

      const inquiry = result.data.updateInquiry;
      console.log('Inquiry updated successfully:', inquiry);
      return inquiry;
    } catch (error) {
      console.error('Error updating inquiry:', error);

      // Provide more specific error message
      if (error.message?.includes('Unauthorized')) {
        throw new Error('You do not have permission to update this inquiry. Please check your admin privileges.');
      }

      throw new Error('Failed to update inquiry');
    }
  }

  /**
   * Update inquiry with basic fields only (status, priority)
   */
  async updateInquiryBasic(input: { id: string; status?: string; priority?: string }): Promise<InquiryResponse> {
    console.log('Updating inquiry with basic fields only:', input);

    try {
      const updateData: any = {
        id: input.id
      };

      if (input.status !== undefined) {
        updateData.status = input.status;
      }
      if (input.priority !== undefined) {
        updateData.priority = input.priority;
      }

      console.log('Basic update data:', updateData);

      // Try authenticated client first (for logged-in users)
      let result;
      try {
        result = await authenticatedClient.graphql({
          query: updateInquiry,
          variables: { input: updateData }
        });
      } catch (authError) {
        console.log('Authenticated basic update failed, trying with API key:', authError);
        // Fallback to public client with API key
        result = await publicClient.graphql({
          query: updateInquiry,
          variables: { input: updateData }
        });
      }

      const inquiry = result.data.updateInquiry;
      console.log('Basic inquiry update successful:', inquiry);
      return inquiry;
    } catch (error) {
      console.error('Error updating inquiry (basic):', error);
      throw new Error('Failed to update inquiry');
    }
  }

  /**
   * Delete inquiry (using Inquiry model)
   */
  async deleteInquiry(id: string): Promise<{ success: boolean; error?: string }> {
    console.log('InquiryService.deleteInquiry called with ID:', id);

    try {
      console.log('Deleting inquiry with ID:', id);

      // Try authenticated client first (for logged-in users)
      let result;
      try {
        result = await authenticatedClient.graphql({
          query: deleteInquiry,
          variables: { input: { id } }
        });
      } catch (authError) {
        console.log('Authenticated delete failed, trying with API key:', authError);
        // Fallback to public client with API key for admin operations
        result = await publicClient.graphql({
          query: deleteInquiry,
          variables: { input: { id } }
        });
      }

      console.log('Inquiry deleted successfully:', id);
      return { success: true };
    } catch (error) {
      console.error('Error deleting inquiry:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to delete inquiry'
      };
    }
  }

  /**
   * Get inquiries by vendor user ID (for vendor dashboard)
   */
  async getVendorInquiries(vendorUserId: string, limit = 20): Promise<InquiryResponse[]> {
    try {
      const result = await this.listInquiries({ vendorUserId, limit });
      return result.items;
    } catch (error) {
      console.error('Error fetching vendor inquiries:', error);
      throw new Error('Failed to fetch vendor inquiries');
    }
  }

  /**
   * Get inquiry statistics for dashboard
   */
  async getInquiryStats(vendorUserId?: string): Promise<{
    total: number;
    new: number;
    contacted: number;
    quoted: number;
    confirmed: number;
    thisMonth: number;
  }> {
    try {
      const inquiries = await this.listInquiries({ vendorUserId, limit: 1000 });
      const items = inquiries.items;

      const now = new Date();
      const thisMonth = new Date(now.getFullYear(), now.getMonth(), 1);

      const stats = {
        total: items.length,
        new: items.filter(i => i.status === 'NEW').length,
        contacted: items.filter(i => i.status === 'CONTACTED').length,
        quoted: items.filter(i => i.status === 'QUOTED').length,
        confirmed: items.filter(i => i.status === 'CONFIRMED').length,
        thisMonth: items.filter(i => new Date(i.createdAt) >= thisMonth).length,
      };

      return stats;
    } catch (error) {
      console.error('Error fetching inquiry stats:', error);
      throw new Error('Failed to fetch inquiry statistics');
    }
  }

  /**
   * Send vendor notification email
   */
  private async sendVendorNotification(inquiry: InquiryResponse, originalInput: InquiryInput): Promise<void> {
    try {
      const notificationData: VendorNotificationData = {
        vendorUserId: inquiry.vendorUserId,
        vendorId: inquiry.vendorId,
        vendorName: inquiry.vendorName,
        customerName: inquiry.customerName,
        customerEmail: inquiry.customerEmail,
        customerPhone: inquiry.customerPhone,
        eventDate: inquiry.eventDate,
        message: inquiry.message,
        inquiryType: inquiry.inquiryType,
        budget: inquiry.budget,
        guestCount: inquiry.guestCount,
        venue: inquiry.venue,
        inquiryId: inquiry.id,
        createdAt: inquiry.createdAt || new Date().toISOString()
      };

      await vendorNotificationService.sendInquiryNotification(notificationData);
      console.log('Vendor notification sent successfully for inquiry:', inquiry.id);
    } catch (error) {
      console.error('Error sending vendor notification:', error);
      // Don't throw error as this is a background operation
    }
  }

  /**
   * Subscribe to inquiry changes
   * Note: Subscriptions may not work until the GraphQL schema is properly deployed
   */
  subscribeToInquiries(vendorId?: string) {
    try {
      return {
        onCreate: client.graphql({
          query: onCreateInquiry,
          variables: vendorId ? { vendorId } : undefined
        }),
        onUpdate: client.graphql({
          query: onUpdateInquiry,
          variables: vendorId ? { vendorId } : undefined
        }),
        onDelete: client.graphql({
          query: onDeleteInquiry,
          variables: vendorId ? { vendorId } : undefined
        })
      };
    } catch (error) {
      console.warn('Failed to set up GraphQL subscriptions:', error);
      // Return mock subscriptions that don't do anything
      return {
        onCreate: {
          subscribe: () => ({
            unsubscribe: () => {},
            next: () => {},
            error: () => {}
          })
        },
        onUpdate: {
          subscribe: () => ({
            unsubscribe: () => {},
            next: () => {},
            error: () => {}
          })
        },
        onDelete: {
          subscribe: () => ({
            unsubscribe: () => {},
            next: () => {},
            error: () => {}
          })
        }
      };
    }
  }
}

export const inquiryService = new InquiryService();
export default inquiryService;
