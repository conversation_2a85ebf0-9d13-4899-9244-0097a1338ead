import React, { useState, useEffect } from 'react';
import { View, Text, ScrollView, TouchableOpacity, Alert, Image } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/Card';
import { Button } from '../ui/Button';
import { Input } from '../ui/Input';
import { Badge } from '../ui/Badge';
import { useAuth } from '../../providers/AuthProvider';
import { useTheme } from '../../providers/ThemeProvider';

interface UserProfile {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  dateOfBirth?: string;
  gender?: string;
  city?: string;
  state?: string;
  country?: string;
  profileImage?: string;
  bio?: string;
  role: 'customer' | 'vendor' | 'admin' | 'super_admin';
  isEmailVerified: boolean;
  isPhoneVerified: boolean;
  preferences: {
    notifications: boolean;
    marketing: boolean;
    newsletter: boolean;
  };
  createdAt: string;
  updatedAt: string;
}

export function ProfileManagement() {
  const { user, userProfile, updateProfile } = useAuth();
  const { theme } = useTheme();
  
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [editing, setEditing] = useState(false);
  const [activeTab, setActiveTab] = useState('personal');
  
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    dateOfBirth: '',
    gender: '',
    city: '',
    state: '',
    country: 'India',
    bio: '',
    preferences: {
      notifications: true,
      marketing: false,
      newsletter: true
    }
  });

  useEffect(() => {
    loadProfile();
  }, []);

  const loadProfile = async () => {
    try {
      setLoading(true);
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Mock profile data
      const mockProfile: UserProfile = {
        id: userProfile?.id || 'user123',
        firstName: userProfile?.firstName || 'John',
        lastName: userProfile?.lastName || 'Doe',
        email: userProfile?.email || '<EMAIL>',
        phone: '+91 9876543210',
        dateOfBirth: '1990-05-15',
        gender: 'Male',
        city: 'Chennai',
        state: 'Tamil Nadu',
        country: 'India',
        bio: 'Planning my dream wedding with the help of BookmyFestive!',
        role: userProfile?.role || 'customer',
        isEmailVerified: true,
        isPhoneVerified: false,
        preferences: {
          notifications: true,
          marketing: false,
          newsletter: true
        },
        createdAt: '2024-01-15T10:30:00Z',
        updatedAt: '2024-01-20T14:20:00Z'
      };
      
      setProfile(mockProfile);
      setFormData({
        firstName: mockProfile.firstName,
        lastName: mockProfile.lastName,
        email: mockProfile.email,
        phone: mockProfile.phone || '',
        dateOfBirth: mockProfile.dateOfBirth || '',
        gender: mockProfile.gender || '',
        city: mockProfile.city || '',
        state: mockProfile.state || '',
        country: mockProfile.country || 'India',
        bio: mockProfile.bio || '',
        preferences: mockProfile.preferences
      });
      
    } catch (error) {
      console.error('Error loading profile:', error);
      Alert.alert('Error', 'Failed to load profile');
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async () => {
    try {
      setSaving(true);
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // Update profile
      if (profile) {
        const updatedProfile = {
          ...profile,
          ...formData,
          updatedAt: new Date().toISOString()
        };
        setProfile(updatedProfile);
      }
      
      setEditing(false);
      Alert.alert('Success', 'Profile updated successfully');
      
    } catch (error) {
      console.error('Error saving profile:', error);
      Alert.alert('Error', 'Failed to update profile');
    } finally {
      setSaving(false);
    }
  };

  const updateFormData = (field: string, value: any) => {
    if (field.includes('.')) {
      const [parent, child] = field.split('.');
      setFormData(prev => ({
        ...prev,
        [parent]: {
          ...prev[parent as keyof typeof prev],
          [child]: value
        }
      }));
    } else {
      setFormData(prev => ({ ...prev, [field]: value }));
    }
  };

  const getRoleBadgeColor = (role: string) => {
    switch (role) {
      case 'admin': return '#F59E0B';
      case 'super_admin': return '#8B5CF6';
      case 'vendor': return '#10B981';
      default: return '#3B82F6';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-IN', {
      day: 'numeric',
      month: 'long',
      year: 'numeric'
    });
  };

  const renderPersonalInfo = () => (
    <View style={{ gap: 16 }}>
      <View style={{ flexDirection: 'row', gap: 12 }}>
        <Input
          label="First Name"
          value={formData.firstName}
          onChangeText={(value) => updateFormData('firstName', value)}
          editable={editing}
          style={{ flex: 1 }}
        />
        <Input
          label="Last Name"
          value={formData.lastName}
          onChangeText={(value) => updateFormData('lastName', value)}
          editable={editing}
          style={{ flex: 1 }}
        />
      </View>

      <Input
        label="Email"
        value={formData.email}
        onChangeText={(value) => updateFormData('email', value)}
        keyboardType="email-address"
        editable={editing}
        rightIcon={profile?.isEmailVerified ? "checkmark-circle" : "alert-circle"}
      />

      <Input
        label="Phone"
        value={formData.phone}
        onChangeText={(value) => updateFormData('phone', value)}
        keyboardType="phone-pad"
        editable={editing}
        rightIcon={profile?.isPhoneVerified ? "checkmark-circle" : "alert-circle"}
      />

      <Input
        label="Date of Birth"
        value={formData.dateOfBirth}
        onChangeText={(value) => updateFormData('dateOfBirth', value)}
        editable={editing}
        placeholder="YYYY-MM-DD"
      />

      <View style={{ flexDirection: 'row', gap: 12 }}>
        <Input
          label="City"
          value={formData.city}
          onChangeText={(value) => updateFormData('city', value)}
          editable={editing}
          style={{ flex: 1 }}
        />
        <Input
          label="State"
          value={formData.state}
          onChangeText={(value) => updateFormData('state', value)}
          editable={editing}
          style={{ flex: 1 }}
        />
      </View>

      <Input
        label="Bio"
        value={formData.bio}
        onChangeText={(value) => updateFormData('bio', value)}
        multiline
        numberOfLines={3}
        editable={editing}
        placeholder="Tell us about yourself..."
      />
    </View>
  );

  const renderPreferences = () => (
    <View style={{ gap: 16 }}>
      <Text style={{
        fontSize: 18,
        fontWeight: '600',
        color: theme.colors.text,
        marginBottom: 8,
      }}>
        Notification Preferences
      </Text>

      {Object.entries(formData.preferences).map(([key, value]) => (
        <TouchableOpacity
          key={key}
          onPress={() => editing && updateFormData(`preferences.${key}`, !value)}
          style={{
            flexDirection: 'row',
            justifyContent: 'space-between',
            alignItems: 'center',
            paddingVertical: 12,
            paddingHorizontal: 16,
            backgroundColor: theme.colors.muted,
            borderRadius: 8,
            opacity: editing ? 1 : 0.7,
          }}
        >
          <View>
            <Text style={{
              fontSize: 16,
              fontWeight: '600',
              color: theme.colors.text,
              textTransform: 'capitalize',
            }}>
              {key}
            </Text>
            <Text style={{
              fontSize: 12,
              color: theme.colors.textSecondary,
            }}>
              {key === 'notifications' && 'Receive push notifications'}
              {key === 'marketing' && 'Receive promotional emails'}
              {key === 'newsletter' && 'Receive newsletter updates'}
            </Text>
          </View>
          <View style={{
            width: 50,
            height: 30,
            borderRadius: 15,
            backgroundColor: value ? theme.colors.primary : theme.colors.border,
            justifyContent: 'center',
            paddingHorizontal: 2,
          }}>
            <View style={{
              width: 26,
              height: 26,
              borderRadius: 13,
              backgroundColor: '#fff',
              alignSelf: value ? 'flex-end' : 'flex-start',
            }} />
          </View>
        </TouchableOpacity>
      ))}
    </View>
  );

  if (loading) {
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
        <Text style={{ color: theme.colors.textSecondary }}>Loading profile...</Text>
      </View>
    );
  }

  if (!profile) {
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
        <Text style={{ color: theme.colors.textSecondary }}>Profile not found</Text>
      </View>
    );
  }

  return (
    <View style={{ flex: 1 }}>
      {/* Header */}
      <View style={{
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        padding: 16,
      }}>
        <Text style={{
          fontSize: 24,
          fontWeight: '700',
          color: theme.colors.text,
        }}>
          Profile
        </Text>
        <Button
          title={editing ? (saving ? "Saving..." : "Save") : "Edit"}
          onPress={editing ? handleSave : () => setEditing(true)}
          disabled={saving}
          loading={saving}
          icon={editing ? "save" : "create"}
          size="sm"
        />
      </View>

      {/* Profile Header */}
      <Card style={{ margin: 16, marginBottom: 8 }}>
        <CardContent style={{ alignItems: 'center', padding: 24 }}>
          <View style={{
            width: 80,
            height: 80,
            borderRadius: 40,
            backgroundColor: theme.colors.primary,
            justifyContent: 'center',
            alignItems: 'center',
            marginBottom: 12,
          }}>
            <Text style={{
              fontSize: 32,
              fontWeight: '700',
              color: '#fff',
            }}>
              {profile.firstName.charAt(0)}{profile.lastName.charAt(0)}
            </Text>
          </View>
          
          <Text style={{
            fontSize: 20,
            fontWeight: '700',
            color: theme.colors.text,
          }}>
            {profile.firstName} {profile.lastName}
          </Text>
          
          <Text style={{
            fontSize: 14,
            color: theme.colors.textSecondary,
            marginBottom: 8,
          }}>
            {profile.email}
          </Text>
          
          <Badge 
            variant="default" 
            style={{ backgroundColor: getRoleBadgeColor(profile.role) }}
          >
            {profile.role.replace('_', ' ').toUpperCase()}
          </Badge>
        </CardContent>
      </Card>

      {/* Tabs */}
      <ScrollView 
        horizontal 
        showsHorizontalScrollIndicator={false}
        style={{ flexGrow: 0, marginBottom: 16 }}
        contentContainerStyle={{ paddingHorizontal: 16, gap: 8 }}
      >
        {[
          { key: 'personal', label: 'Personal Info', icon: 'person' },
          { key: 'preferences', label: 'Preferences', icon: 'settings' }
        ].map((tab) => (
          <TouchableOpacity
            key={tab.key}
            onPress={() => setActiveTab(tab.key)}
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              gap: 6,
              paddingHorizontal: 16,
              paddingVertical: 8,
              borderRadius: 20,
              backgroundColor: activeTab === tab.key 
                ? theme.colors.primary 
                : theme.colors.muted,
            }}
          >
            <Ionicons
              name={tab.icon as any}
              size={16}
              color={activeTab === tab.key ? '#fff' : theme.colors.textSecondary}
            />
            <Text style={{
              fontSize: 14,
              fontWeight: '600',
              color: activeTab === tab.key ? '#fff' : theme.colors.text,
            }}>
              {tab.label}
            </Text>
          </TouchableOpacity>
        ))}
      </ScrollView>

      {/* Content */}
      <ScrollView style={{ flex: 1 }} contentContainerStyle={{ padding: 16 }}>
        <Card>
          <CardContent style={{ padding: 16 }}>
            {activeTab === 'personal' ? renderPersonalInfo() : renderPreferences()}
          </CardContent>
        </Card>
      </ScrollView>
    </View>
  );
}
