import NetInfo, { NetInfoState, NetInfoStateType } from '@react-native-community/netinfo';

export interface NetworkState {
  isConnected: boolean;
  isInternetReachable: boolean;
  type: NetInfoStateType;
  isWifiEnabled: boolean;
  isCellularEnabled: boolean;
  strength: number; // 0-100
}

export interface NetworkListener {
  (state: NetworkState): void;
}

class NetworkService {
  private listeners: NetworkListener[] = [];
  private currentState: NetworkState = {
    isConnected: false,
    isInternetReachable: false,
    type: NetInfoStateType.unknown,
    isWifiEnabled: false,
    isCellularEnabled: false,
    strength: 0,
  };

  constructor() {
    this.initialize();
  }

  private initialize() {
    // Subscribe to network state changes
    NetInfo.addEventListener(this.handleNetworkChange);
    
    // Get initial network state
    this.refreshNetworkState();
  }

  private handleNetworkChange = (state: NetInfoState) => {
    const networkState: NetworkState = {
      isConnected: state.isConnected ?? false,
      isInternetReachable: state.isInternetReachable ?? false,
      type: state.type,
      isWifiEnabled: state.type === NetInfoStateType.wifi && (state.isConnected ?? false),
      isCellularEnabled: state.type === NetInfoStateType.cellular && (state.isConnected ?? false),
      strength: this.calculateSignalStrength(state),
    };

    this.currentState = networkState;
    this.notifyListeners(networkState);
  };

  private calculateSignalStrength(state: NetInfoState): number {
    if (!state.isConnected) return 0;
    
    if (state.type === NetInfoStateType.wifi && state.details) {
      // WiFi signal strength (typically -30 to -90 dBm)
      const strength = (state.details as any).strength;
      if (typeof strength === 'number') {
        return Math.max(0, Math.min(100, ((strength + 90) / 60) * 100));
      }
    }
    
    if (state.type === NetInfoStateType.cellular && state.details) {
      // Cellular signal strength
      const strength = (state.details as any).cellularGeneration;
      if (strength === '4g' || strength === '5g') return 80;
      if (strength === '3g') return 60;
      if (strength === '2g') return 40;
    }
    
    return state.isConnected ? 70 : 0; // Default strength for connected state
  }

  private notifyListeners(state: NetworkState) {
    this.listeners.forEach(listener => {
      try {
        listener(state);
      } catch (error) {
        console.error('Error in network listener:', error);
      }
    });
  }

  public async refreshNetworkState(): Promise<NetworkState> {
    try {
      const state = await NetInfo.fetch();
      this.handleNetworkChange(state);
      return this.currentState;
    } catch (error) {
      console.error('Error fetching network state:', error);
      return this.currentState;
    }
  }

  public getCurrentState(): NetworkState {
    return this.currentState;
  }

  public isOnline(): boolean {
    return this.currentState.isConnected && this.currentState.isInternetReachable;
  }

  public isOffline(): boolean {
    return !this.isOnline();
  }

  public getConnectionType(): NetInfoStateType {
    return this.currentState.type;
  }

  public getSignalStrength(): number {
    return this.currentState.strength;
  }

  public addListener(listener: NetworkListener): () => void {
    this.listeners.push(listener);
    
    // Return unsubscribe function
    return () => {
      const index = this.listeners.indexOf(listener);
      if (index > -1) {
        this.listeners.splice(index, 1);
      }
    };
  }

  public removeListener(listener: NetworkListener): void {
    const index = this.listeners.indexOf(listener);
    if (index > -1) {
      this.listeners.splice(index, 1);
    }
  }

  public removeAllListeners(): void {
    this.listeners = [];
  }

  // Utility methods for connection quality
  public getConnectionQuality(): 'excellent' | 'good' | 'fair' | 'poor' | 'offline' {
    if (!this.isOnline()) return 'offline';
    
    const strength = this.getSignalStrength();
    if (strength >= 80) return 'excellent';
    if (strength >= 60) return 'good';
    if (strength >= 40) return 'fair';
    return 'poor';
  }

  public shouldUseCache(): boolean {
    const quality = this.getConnectionQuality();
    return quality === 'poor' || quality === 'offline';
  }

  public shouldSyncData(): boolean {
    const quality = this.getConnectionQuality();
    return quality === 'excellent' || quality === 'good';
  }

  // Test connectivity to specific endpoints
  public async testConnectivity(url: string = 'https://www.google.com', timeout: number = 5000): Promise<boolean> {
    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), timeout);
      
      const response = await fetch(url, {
        method: 'HEAD',
        signal: controller.signal,
      });
      
      clearTimeout(timeoutId);
      return response.ok;
    } catch (error) {
      return false;
    }
  }
}

// Singleton instance
export const networkService = new NetworkService();
export default NetworkService;
