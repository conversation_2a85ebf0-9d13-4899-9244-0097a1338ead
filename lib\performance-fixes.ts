/**
 * Performance fixes for development environment
 * Apply these optimizations to improve local development speed
 */

// 1. Optimize React DevTools in development
if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
  // Disable React DevTools profiler in development for better performance
  if (window.__REACT_DEVTOOLS_GLOBAL_HOOK__) {
    window.__REACT_DEVTOOLS_GLOBAL_HOOK__.settings = {
      ...window.__REACT_DEVTOOLS_GLOBAL_HOOK__.settings,
      profilerEnabled: false,
    }
  }
}

// 2. Optimize console logging
const originalConsole = {
  log: console.log,
  info: console.info,
  warn: console.warn,
  debug: console.debug,
}

if (process.env.NODE_ENV === 'development') {
  // Throttle console logs to prevent performance issues
  let logCount = 0
  const MAX_LOGS_PER_SECOND = 50

  const throttledLog = (originalFn: Function) => {
    return (...args: any[]) => {
      logCount++
      if (logCount <= MAX_LOGS_PER_SECOND) {
        originalFn(...args)
      }
    }
  }

  // Reset log count every second
  setInterval(() => {
    logCount = 0
  }, 1000)

  console.log = throttledLog(originalConsole.log)
  console.info = throttledLog(originalConsole.info)
  console.warn = throttledLog(originalConsole.warn)
  console.debug = throttledLog(originalConsole.debug)
}

// 3. Optimize image loading
export const optimizeImages = () => {
  if (typeof window !== 'undefined') {
    // Lazy load images that are not in viewport
    const images = document.querySelectorAll('img[data-src]')
    
    const imageObserver = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const img = entry.target as HTMLImageElement
          img.src = img.dataset.src || ''
          img.removeAttribute('data-src')
          imageObserver.unobserve(img)
        }
      })
    })

    images.forEach(img => imageObserver.observe(img))
  }
}

// 4. Debounce function for performance
export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) => {
  let timeout: NodeJS.Timeout
  return (...args: Parameters<T>) => {
    clearTimeout(timeout)
    timeout = setTimeout(() => func(...args), wait)
  }
}

// 5. Throttle function for performance
export const throttle = <T extends (...args: any[]) => any>(
  func: T,
  limit: number
): ((...args: Parameters<T>) => void) => {
  let inThrottle: boolean
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args)
      inThrottle = true
      setTimeout(() => inThrottle = false, limit)
    }
  }
}

// 6. Memory cleanup utilities
export const cleanupMemory = () => {
  if (typeof window !== 'undefined') {
    // Force garbage collection if available
    if ('gc' in window) {
      (window as any).gc()
    }
    
    // Clear unused event listeners
    const events = ['scroll', 'resize', 'mousemove', 'touchmove']
    events.forEach(event => {
      const listeners = (window as any)._eventListeners?.[event] || []
      listeners.forEach((listener: any) => {
        if (listener.cleanup) {
          listener.cleanup()
        }
      })
    })
  }
}

// 7. Optimize bundle loading
export const preloadCriticalResources = () => {
  if (typeof window !== 'undefined') {
    // Preload critical CSS
    const criticalCSS = [
      '/globals.css',
    ]
    
    criticalCSS.forEach(href => {
      const link = document.createElement('link')
      link.rel = 'preload'
      link.as = 'style'
      link.href = href
      document.head.appendChild(link)
    })
    
    // Preload critical JavaScript
    const criticalJS = [
      '/_next/static/chunks/main.js',
      '/_next/static/chunks/webpack.js',
    ]
    
    criticalJS.forEach(href => {
      const link = document.createElement('link')
      link.rel = 'preload'
      link.as = 'script'
      link.href = href
      document.head.appendChild(link)
    })
  }
}

// 8. Development-specific optimizations
if (process.env.NODE_ENV === 'development') {
  // Disable React Strict Mode warnings for better performance
  const originalWarn = console.warn
  console.warn = (...args) => {
    if (args[0]?.includes?.('React.StrictMode')) {
      return
    }
    originalWarn(...args)
  }
  
  // Optimize hot reload
  if (typeof window !== 'undefined') {
    window.addEventListener('beforeunload', cleanupMemory)
  }
}

// 9. Auto-apply optimizations
if (typeof window !== 'undefined') {
  // Apply optimizations when DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      optimizeImages()
      preloadCriticalResources()
    })
  } else {
    optimizeImages()
    preloadCriticalResources()
  }
  
  // Cleanup on page unload
  window.addEventListener('beforeunload', cleanupMemory)
}

export default {
  optimizeImages,
  debounce,
  throttle,
  cleanupMemory,
  preloadCriticalResources,
}
