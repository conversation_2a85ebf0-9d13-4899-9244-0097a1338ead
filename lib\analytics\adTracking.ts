// Analytics tracking utilities for advertisement interactions

export interface AdClickEvent {
  adType: string
  adId: string
  cityName?: string
  cityIndex?: number
  state?: string
  timestamp: string
  userId?: string
  sessionId?: string
  deviceType?: string
  referrer?: string
}

export interface AdImpressionEvent {
  adType: string
  adId: string
  visibleCities: string[]
  state: string
  timestamp: string
  userId?: string
  sessionId?: string
  viewDuration?: number
}

export interface AdConversionEvent {
  adType: string
  adId: string
  cityName: string
  conversionType: 'venue_search' | 'vendor_search' | 'quote_request' | 'contact_form'
  timestamp: string
  userId?: string
  sessionId?: string
}

class AdTrackingService {
  private sessionId: string
  private userId?: string

  constructor() {
    this.sessionId = this.generateSessionId()
    this.initializeTracking()
  }

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  private initializeTracking() {
    if (typeof window !== 'undefined') {
      // Get user ID from auth context if available
      this.userId = localStorage.getItem('userId') || undefined
      
      // Track page visibility for impression duration
      document.addEventListener('visibilitychange', this.handleVisibilityChange.bind(this))
    }
  }

  private handleVisibilityChange() {
    // Handle page visibility changes for accurate impression tracking
    if (document.hidden) {
      this.flushPendingEvents()
    }
  }

  private getDeviceType(): string {
    if (typeof window === 'undefined') return 'unknown'
    
    const width = window.innerWidth
    if (width < 768) return 'mobile'
    if (width < 1024) return 'tablet'
    return 'desktop'
  }

  private sendToAnalytics(eventName: string, eventData: any) {
    // Google Analytics 4
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', eventName, {
        ...eventData,
        session_id: this.sessionId,
        user_id: this.userId,
        device_type: this.getDeviceType(),
        page_url: window.location.href,
        page_title: document.title
      })
    }

    // Console logging for development
    console.log(`[AdTracking] ${eventName}:`, eventData)

    // Send to custom analytics endpoint (if available)
    this.sendToCustomEndpoint(eventName, eventData)
  }

  private async sendToCustomEndpoint(eventName: string, eventData: any) {
    try {
      // Only send in production or if custom endpoint is configured
      const analyticsEndpoint = process.env.NEXT_PUBLIC_ANALYTICS_ENDPOINT
      if (!analyticsEndpoint) return

      await fetch(analyticsEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          event: eventName,
          data: {
            ...eventData,
            sessionId: this.sessionId,
            userId: this.userId,
            timestamp: new Date().toISOString(),
            userAgent: navigator.userAgent,
            referrer: document.referrer
          }
        })
      })
    } catch (error) {
      console.warn('Failed to send analytics to custom endpoint:', error)
    }
  }

  // Track when a destination ad is clicked
  trackDestinationClick(data: {
    cityName: string
    cityIndex: number
    state: string
    adPosition?: string
    hasPromotionalBadge?: boolean
    offerType?: string
  }) {
    const event: AdClickEvent = {
      adType: 'popular_destinations',
      adId: `destinations_${data.state.toLowerCase().replace(/\s+/g, '_')}`,
      cityName: data.cityName,
      cityIndex: data.cityIndex,
      state: data.state,
      timestamp: new Date().toISOString(),
      userId: this.userId,
      sessionId: this.sessionId,
      deviceType: this.getDeviceType(),
      referrer: typeof window !== 'undefined' ? document.referrer : undefined
    }

    this.sendToAnalytics('destination_ad_click', {
      ...event,
      ad_position: data.adPosition,
      has_promotional_badge: data.hasPromotionalBadge,
      offer_type: data.offerType,
      event_category: 'Advertisement',
      event_label: data.cityName,
      custom_parameter_1: 'popular_destinations_ad'
    })
  }

  // Track when the destinations ad section becomes visible
  trackDestinationImpression(data: {
    visibleCities: string[]
    state: string
    adPosition?: string
    scrollDepth?: number
  }) {
    const event: AdImpressionEvent = {
      adType: 'popular_destinations',
      adId: `destinations_${data.state.toLowerCase().replace(/\s+/g, '_')}`,
      visibleCities: data.visibleCities,
      state: data.state,
      timestamp: new Date().toISOString(),
      userId: this.userId,
      sessionId: this.sessionId
    }

    this.sendToAnalytics('destination_ad_impression', {
      ...event,
      ad_position: data.adPosition,
      scroll_depth: data.scrollDepth,
      visible_cities_count: data.visibleCities.length,
      event_category: 'Advertisement',
      event_label: 'destinations_section_view'
    })
  }

  // Track conversions from destination ads
  trackDestinationConversion(data: {
    cityName: string
    conversionType: AdConversionEvent['conversionType']
    state: string
    adPosition?: string
  }) {
    const event: AdConversionEvent = {
      adType: 'popular_destinations',
      adId: `destinations_${data.state.toLowerCase().replace(/\s+/g, '_')}`,
      cityName: data.cityName,
      conversionType: data.conversionType,
      timestamp: new Date().toISOString(),
      userId: this.userId,
      sessionId: this.sessionId
    }

    this.sendToAnalytics('destination_ad_conversion', {
      ...event,
      ad_position: data.adPosition,
      event_category: 'Advertisement',
      event_label: `${data.conversionType}_${data.cityName}`,
      value: this.getConversionValue(data.conversionType)
    })
  }

  // Track CTA button clicks
  trackCTAClick(data: {
    ctaType: 'explore_all' | 'get_quote'
    state: string
    adPosition?: string
  }) {
    this.sendToAnalytics('destination_ad_cta_click', {
      ad_type: 'popular_destinations',
      cta_type: data.ctaType,
      state: data.state,
      ad_position: data.adPosition,
      session_id: this.sessionId,
      user_id: this.userId,
      event_category: 'Advertisement',
      event_label: `cta_${data.ctaType}`
    })
  }

  private getConversionValue(conversionType: AdConversionEvent['conversionType']): number {
    // Assign values to different conversion types for ROI tracking
    const values = {
      'venue_search': 10,
      'vendor_search': 8,
      'quote_request': 25,
      'contact_form': 15
    }
    return values[conversionType] || 5
  }

  private flushPendingEvents() {
    // Flush any pending analytics events when page becomes hidden
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', 'session_end', {
        session_id: this.sessionId,
        user_id: this.userId
      })
    }
  }

  // Get session analytics summary
  getSessionSummary() {
    return {
      sessionId: this.sessionId,
      userId: this.userId,
      deviceType: this.getDeviceType(),
      startTime: this.sessionId.split('_')[1],
      currentUrl: typeof window !== 'undefined' ? window.location.href : undefined
    }
  }
}

// Export singleton instance
export const adTracker = new AdTrackingService()

// Export utility functions
export const trackDestinationClick = (data: Parameters<typeof adTracker.trackDestinationClick>[0]) => 
  adTracker.trackDestinationClick(data)

export const trackDestinationImpression = (data: Parameters<typeof adTracker.trackDestinationImpression>[0]) => 
  adTracker.trackDestinationImpression(data)

export const trackDestinationConversion = (data: Parameters<typeof adTracker.trackDestinationConversion>[0]) => 
  adTracker.trackDestinationConversion(data)

export const trackCTAClick = (data: Parameters<typeof adTracker.trackCTAClick>[0]) => 
  adTracker.trackCTAClick(data)
