#!/usr/bin/env node

/**
 * <PERSON>ript to automatically update service worker version
 * This ensures the service worker cache is invalidated on each build
 */

const fs = require('fs');
const path = require('path');

const SW_PATH = path.join(__dirname, '../public/sw.js');

function updateServiceWorkerVersion() {
  try {
    // Read the service worker file
    let swContent = fs.readFileSync(SW_PATH, 'utf8');
    
    // Generate new version with timestamp
    const timestamp = Date.now();
    const newVersion = `1.0.2-${timestamp}`;
    
    // Replace the VERSION constant (handles both static and dynamic versions)
    const versionRegex = /const VERSION = [^;]+;/;
    const newVersionLine = `const VERSION = '${newVersion}';`;
    
    if (versionRegex.test(swContent)) {
      swContent = swContent.replace(versionRegex, newVersionLine);
      
      // Write back to file
      fs.writeFileSync(SW_PATH, swContent, 'utf8');
      
      console.log(`✅ Service Worker version updated to: ${newVersion}`);
      console.log(`📁 Updated file: ${SW_PATH}`);
    } else {
      console.error('❌ Could not find VERSION constant in service worker file');
      process.exit(1);
    }
  } catch (error) {
    console.error('❌ Error updating service worker version:', error.message);
    process.exit(1);
  }
}

// Run the update
updateServiceWorkerVersion();
