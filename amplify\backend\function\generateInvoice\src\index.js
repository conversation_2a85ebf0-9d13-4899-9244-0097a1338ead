const AWS = require('aws-sdk');
const PDFDocument = require('pdfkit');
const { v4: uuidv4 } = require('uuid');

// Initialize AWS services
const dynamodb = new AWS.DynamoDB.DocumentClient();
const s3 = new AWS.S3();

exports.handler = async (event) => {
  console.log('Event:', JSON.stringify(event, null, 2));
  
  try {
    const input = event.arguments.input;
    
    // Generate invoice number
    const invoiceNumber = generateInvoiceNumber(input.type);
    
    // Create invoice record
    const invoiceId = uuidv4();
    const invoiceData = {
      id: invoiceId,
      invoiceNumber,
      invoiceDate: new Date().toISOString().split('T')[0],
      dueDate: input.dueDate || null,
      type: input.type,
      customerId: input.customerId,
      customerName: input.customerName,
      customerEmail: input.customerEmail,
      customerPhone: input.customerPhone || null,
      customerAddress: input.customerAddress || null,
      vendorId: input.vendorId || null,
      vendorName: input.vendorName || null,
      vendorEmail: input.vendorEmail || null,
      vendorBusinessName: input.vendorBusinessName || null,
      vendorAddress: input.vendorAddress || null,
      vendorGstNumber: input.vendorGstNumber || null,
      items: input.items,
      subtotal: input.subtotal,
      taxAmount: input.taxAmount,
      discountAmount: input.discountAmount || 0,
      totalAmount: input.totalAmount,
      paymentStatus: input.paymentStatus,
      paymentMethod: input.paymentMethod || null,
      paymentDate: input.paymentDate || null,
      transactionId: input.transactionId || null,
      notes: input.notes || null,
      terms: input.terms || null,
      eventDate: input.eventDate || null,
      eventTime: input.eventTime || null,
      eventLocation: input.eventLocation || null,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    
    // Generate PDF
    const pdfBuffer = await generateInvoicePDF(invoiceData);
    
    // Upload PDF to S3
    const pdfKey = `invoices/${invoiceId}/${invoiceNumber}.pdf`;
    await s3.putObject({
      Bucket: process.env.STORAGE_BUCKET,
      Key: pdfKey,
      Body: pdfBuffer,
      ContentType: 'application/pdf',
      ACL: 'private'
    }).promise();
    
    const pdfUrl = `https://${process.env.STORAGE_BUCKET}.s3.${process.env.AWS_REGION}.amazonaws.com/${pdfKey}`;
    
    // Update invoice with PDF URL
    invoiceData.pdfUrl = pdfUrl;
    
    // Save invoice to database
    await dynamodb.put({
      TableName: process.env.INVOICE_TABLE,
      Item: invoiceData
    }).promise();
    
    return {
      success: true,
      invoiceId,
      invoiceNumber,
      pdfUrl
    };
    
  } catch (error) {
    console.error('Error generating invoice:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

function generateInvoiceNumber(type) {
  const prefix = {
    'PRODUCT_ORDER': 'INV-PRD',
    'VENUE_BOOKING': 'INV-VEN',
    'VENDOR_BOOKING': 'INV-VND',
    'SUBSCRIPTION': 'INV-SUB'
  }[type] || 'INV';
  
  const timestamp = Date.now().toString().slice(-8);
  const random = Math.random().toString(36).substr(2, 4).toUpperCase();
  
  return `${prefix}-${timestamp}-${random}`;
}

async function generateInvoicePDF(invoiceData) {
  return new Promise((resolve, reject) => {
    try {
      const doc = new PDFDocument({ margin: 50 });
      const chunks = [];
      
      doc.on('data', chunk => chunks.push(chunk));
      doc.on('end', () => resolve(Buffer.concat(chunks)));
      doc.on('error', reject);
      
      // Header
      doc.fontSize(24)
         .fillColor('#a31515')
         .text('BookmyFestive', 50, 50);
      
      doc.fontSize(20)
         .text('INVOICE', 450, 50);
      
      // Company details
      doc.fontSize(10)
         .fillColor('#666666')
         .text('Making your wedding dreams come true!', 50, 80)
         .text('Email: <EMAIL>', 50, 95)
         .text('Phone: +91 9876543210', 50, 110);
      
      // Invoice details
      doc.fontSize(10)
         .fillColor('#000000')
         .text(`Invoice Number: ${invoiceData.invoiceNumber}`, 450, 80)
         .text(`Invoice Date: ${new Date(invoiceData.invoiceDate).toLocaleDateString()}`, 450, 95);
      
      if (invoiceData.dueDate) {
        doc.text(`Due Date: ${new Date(invoiceData.dueDate).toLocaleDateString()}`, 450, 110);
      }
      
      // Customer details
      let yPosition = 150;
      doc.fontSize(12)
         .fillColor('#000000')
         .text('Bill To:', 50, yPosition);
      
      yPosition += 20;
      doc.fontSize(10)
         .text(invoiceData.customerName, 50, yPosition)
         .text(invoiceData.customerEmail, 50, yPosition + 15);
      
      if (invoiceData.customerPhone) {
        doc.text(invoiceData.customerPhone, 50, yPosition + 30);
        yPosition += 15;
      }
      
      if (invoiceData.customerAddress) {
        const address = invoiceData.customerAddress;
        doc.text(address.street, 50, yPosition + 30)
           .text(`${address.city}, ${address.state} ${address.pincode}`, 50, yPosition + 45)
           .text(address.country, 50, yPosition + 60);
        yPosition += 45;
      }
      
      // Vendor details (if applicable)
      if (invoiceData.vendorName) {
        doc.fontSize(12)
           .text('From:', 350, 150);
        
        doc.fontSize(10)
           .text(invoiceData.vendorBusinessName || invoiceData.vendorName, 350, 170)
           .text(invoiceData.vendorEmail || '', 350, 185);
      }
      
      // Items table
      yPosition = Math.max(yPosition + 100, 250);
      
      // Table header
      doc.rect(50, yPosition, 500, 25)
         .fillAndStroke('#f0f0f0', '#cccccc');
      
      doc.fontSize(10)
         .fillColor('#000000')
         .text('Description', 60, yPosition + 8)
         .text('Qty', 300, yPosition + 8)
         .text('Unit Price', 350, yPosition + 8)
         .text('Total', 450, yPosition + 8);
      
      yPosition += 35;
      
      // Table rows
      invoiceData.items.forEach(item => {
        doc.text(item.name, 60, yPosition)
           .text(item.quantity.toString(), 300, yPosition)
           .text(`₹${item.unitPrice.toLocaleString()}`, 350, yPosition)
           .text(`₹${item.totalPrice.toLocaleString()}`, 450, yPosition);
        
        if (item.description) {
          yPosition += 15;
          doc.fontSize(8)
             .fillColor('#666666')
             .text(item.description, 60, yPosition);
          doc.fontSize(10)
             .fillColor('#000000');
        }
        
        yPosition += 25;
      });
      
      // Totals
      yPosition += 20;
      const totalsX = 350;
      
      doc.text(`Subtotal: ₹${invoiceData.subtotal.toLocaleString()}`, totalsX, yPosition);
      yPosition += 15;
      
      if (invoiceData.discountAmount > 0) {
        doc.text(`Discount: -₹${invoiceData.discountAmount.toLocaleString()}`, totalsX, yPosition);
        yPosition += 15;
      }
      
      if (invoiceData.taxAmount > 0) {
        doc.text(`Tax: ₹${invoiceData.taxAmount.toLocaleString()}`, totalsX, yPosition);
        yPosition += 15;
      }
      
      doc.fontSize(12)
         .text(`Total: ₹${invoiceData.totalAmount.toLocaleString()}`, totalsX, yPosition);
      
      // Payment status
      yPosition += 30;
      const statusColor = invoiceData.paymentStatus === 'PAID' ? '#00aa00' : '#aa6600';
      doc.fontSize(12)
         .fillColor(statusColor)
         .text(`Payment Status: ${invoiceData.paymentStatus}`, 50, yPosition);
      
      // Notes
      if (invoiceData.notes) {
        yPosition += 40;
        doc.fontSize(10)
           .fillColor('#000000')
           .text('Notes:', 50, yPosition);
        
        yPosition += 15;
        doc.text(invoiceData.notes, 50, yPosition, { width: 500 });
      }
      
      // Footer
      doc.fontSize(8)
         .fillColor('#666666')
         .text('Thank you for your business!', 50, doc.page.height - 50);
      
      doc.end();
      
    } catch (error) {
      reject(error);
    }
  });
}
