import React from 'react';
import { TouchableOpacity, Text, StyleSheet, ViewStyle, TextStyle, ActivityIndicator, View } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../../providers/ThemeProvider';

export interface ButtonProps {
  title?: string;
  onPress: () => void;
  variant?: 'default' | 'outline' | 'secondary' | 'ghost' | 'destructive' | 'link';
  size?: 'default' | 'sm' | 'lg' | 'icon';
  disabled?: boolean;
  loading?: boolean;
  style?: ViewStyle;
  textStyle?: TextStyle;
  children?: React.ReactNode;
  icon?: keyof typeof Ionicons.glyphMap;
  iconPosition?: 'left' | 'right';
  fullWidth?: boolean;
  asChild?: boolean;
}

export function Button({
  title,
  onPress,
  variant = 'default',
  size = 'default',
  disabled = false,
  loading = false,
  style,
  textStyle,
  children,
  icon,
  iconPosition = 'left',
  fullWidth = false,
  asChild = false,
}: ButtonProps) {
  const { theme } = useTheme();

  const getButtonStyle = (): ViewStyle => {
    const baseStyle: ViewStyle = {
      borderRadius: theme.borderRadius.lg, // 8px to match web app
      alignItems: 'center',
      justifyContent: 'center',
      flexDirection: 'row',
      gap: 8, // 2 * 4px spacing for gap-2
    };

    // Size styles - exact match to web app
    const sizeStyles: Record<string, ViewStyle> = {
      default: { height: 40, paddingHorizontal: 16, paddingVertical: 8 }, // h-10 px-4 py-2
      sm: { height: 36, paddingHorizontal: 12, paddingVertical: 6 }, // h-9 px-3
      lg: { height: 44, paddingHorizontal: 32, paddingVertical: 12 }, // h-11 px-8
      icon: { height: 40, width: 40, paddingHorizontal: 0, paddingVertical: 0 }, // h-10 w-10
    };

    // Variant styles - exact match to web app
    const variantStyles: Record<string, ViewStyle> = {
      default: {
        backgroundColor: theme.colors.primary,
        borderWidth: 0,
      },
      outline: {
        backgroundColor: theme.colors.background,
        borderWidth: 1,
        borderColor: theme.colors.input,
      },
      secondary: {
        backgroundColor: theme.colors.secondary,
        borderWidth: 0,
      },
      ghost: {
        backgroundColor: 'transparent',
        borderWidth: 0,
      },
      destructive: {
        backgroundColor: theme.colors.destructive,
        borderWidth: 0,
      },
      link: {
        backgroundColor: 'transparent',
        borderWidth: 0,
        paddingHorizontal: 0,
        paddingVertical: 0,
        height: 'auto',
      },
    };

    return {
      ...baseStyle,
      ...sizeStyles[size],
      ...variantStyles[variant],
      ...(fullWidth && { width: '100%' }),
      opacity: disabled || loading ? 0.6 : 1,
    };
  };

  const getTextStyle = (): TextStyle => {
    const baseStyle: TextStyle = {
      fontSize: 14,
      fontWeight: '500', // font-medium
      textAlign: 'center',
    };

    // Size styles - exact match to web app
    const sizeStyles: Record<string, TextStyle> = {
      default: { fontSize: theme.typography.button.fontSize }, // text-sm
      sm: { fontSize: theme.typography.caption.fontSize }, // text-xs
      lg: { fontSize: theme.typography.bodySmall.fontSize }, // text-sm
      icon: { fontSize: theme.typography.button.fontSize }, // text-sm
    };

    // Variant styles - exact match to web app
    const variantStyles: Record<string, TextStyle> = {
      default: { color: theme.colors.primaryForeground },
      outline: { color: theme.colors.foreground },
      secondary: { color: theme.colors.secondaryForeground },
      ghost: { color: theme.colors.foreground },
      destructive: { color: theme.colors.destructiveForeground },
      link: {
        color: theme.colors.primary,
        textDecorationLine: 'underline',
        textDecorationColor: theme.colors.primary,
      },
    };

    return {
      ...baseStyle,
      ...sizeStyles[size],
      ...variantStyles[variant],
    };
  };

  const renderContent = () => {
    if (children) {
      return children;
    }

    const iconColor = variant === 'outline' || variant === 'ghost' || variant === 'link'
      ? theme.colors.text
      : '#fff';

    return (
      <View style={{ flexDirection: 'row', alignItems: 'center', gap: 8 }}>
        {loading && (
          <ActivityIndicator
            size="small"
            color={iconColor}
          />
        )}

        {!loading && icon && iconPosition === 'left' && (
          <Ionicons name={icon} size={size === 'sm' ? 16 : size === 'lg' ? 20 : 18} color={iconColor} />
        )}

        {title && (
          <Text style={[getTextStyle(), textStyle]}>
            {title}
          </Text>
        )}

        {!loading && icon && iconPosition === 'right' && (
          <Ionicons name={icon} size={size === 'sm' ? 16 : size === 'lg' ? 20 : 18} color={iconColor} />
        )}
      </View>
    );
  };

  return (
    <TouchableOpacity
      style={[getButtonStyle(), style]}
      onPress={onPress}
      disabled={disabled || loading}
      activeOpacity={variant === 'ghost' || variant === 'link' ? 0.6 : 0.8}
    >
      {renderContent()}
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  // Additional styles can be added here if needed
});
