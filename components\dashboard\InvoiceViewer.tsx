'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  Download, 
  Eye, 
  Search, 
  Filter, 
  Calendar,
  CreditCard,
  FileText,
  Building,
  User,
  Package,
  MapPin,
  Phone,
  Mail,
  ExternalLink
} from 'lucide-react';
import { invoiceService, type InvoiceData } from '@/lib/services/invoiceService';
import { useAuth } from '@/contexts/AuthContext';
import { showToast } from '@/lib/toast'
interface InvoiceViewerProps {
  userType?: 'customer' | 'vendor' | 'admin';
  userId?: string;
}

export default function InvoiceViewer({ userType = 'customer', userId }: InvoiceViewerProps) {
  const [invoices, setInvoices] = useState<InvoiceData[]>([]);
  const [filteredInvoices, setFilteredInvoices] = useState<InvoiceData[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [typeFilter, setTypeFilter] = useState('all');
  const [selectedInvoice, setSelectedInvoice] = useState<InvoiceData | null>(null);
  const [showInvoiceModal, setShowInvoiceModal] = useState(false);
  const { user, userProfile } = useAuth();

  const currentUserId = userId || user?.userId;

  useEffect(() => {
    if (currentUserId) {
      loadInvoices();
    }
  }, [currentUserId, userType]);

  useEffect(() => {
    filterInvoices();
  }, [searchTerm, statusFilter, typeFilter, invoices]);

  const loadInvoices = async () => {
    if (!currentUserId) return;
    
    setLoading(true);
    try {
      const invoiceData = await invoiceService.getInvoicesForUser(currentUserId, userType);
      setInvoices(invoiceData);
    } catch (error) {
      console.error('Error loading invoices:', error);
    } finally {
      setLoading(false);
    }
  };

  const filterInvoices = () => {
    let filtered = invoices;

    if (searchTerm) {
      filtered = filtered.filter(invoice =>
        invoice.invoiceNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
        invoice.customerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (invoice.vendorName && invoice.vendorName.toLowerCase().includes(searchTerm.toLowerCase()))
      );
    }

    if (statusFilter !== 'all') {
      filtered = filtered.filter(invoice => invoice.paymentStatus.toLowerCase() === statusFilter);
    }

    if (typeFilter !== 'all') {
      filtered = filtered.filter(invoice => invoice.type.toLowerCase() === typeFilter);
    }

    setFilteredInvoices(filtered);
  };

  const downloadInvoice = async (invoiceId: string) => {
    try {
      const result = await invoiceService.downloadInvoicePDF(invoiceId);
      if (result.success && result.blob) {
        const url = window.URL.createObjectURL(result.blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `invoice_${invoiceId}.pdf`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
      } else {
        showToast.error('Failed to download invoice');
      }
    } catch (error) {
      console.error('Error downloading invoice:', error);
      showToast.error('An error occurred while downloading invoice');
    }
  };

  const viewInvoiceDetails = (invoice: InvoiceData) => {
    setSelectedInvoice(invoice);
    setShowInvoiceModal(true);
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      paid: { color: 'bg-green-100 text-green-800', label: 'Paid' },
      pending: { color: 'bg-yellow-100 text-yellow-800', label: 'Pending' },
      overdue: { color: 'bg-red-100 text-red-800', label: 'Overdue' },
      cancelled: { color: 'bg-gray-100 text-gray-800', label: 'Cancelled' }
    };

    const config = statusConfig[status.toLowerCase() as keyof typeof statusConfig] || statusConfig.pending;
    
    return (
      <Badge className={config.color}>
        {config.label}
      </Badge>
    );
  };

  const getTypeBadge = (type: string) => {
    const typeConfig = {
      product_order: { color: 'bg-blue-100 text-blue-800', label: 'Product Order', icon: Package },
      venue_booking: { color: 'bg-purple-100 text-purple-800', label: 'Venue Booking', icon: MapPin },
      vendor_booking: { color: 'bg-orange-100 text-orange-800', label: 'Vendor Booking', icon: Building },
      subscription: { color: 'bg-indigo-100 text-indigo-800', label: 'Subscription', icon: CreditCard }
    };

    const config = typeConfig[type?.toLowerCase() as keyof typeof typeConfig] || typeConfig.product_order;
    const IconComponent = config.icon;
    
    return (
      <Badge className={`${config.color} flex items-center gap-1`}>
        <IconComponent className="h-3 w-3" />
        {config.label}
      </Badge>
    );
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center py-12">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-4 sm:space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4">
        <div>
          <h2 className="text-xl sm:text-2xl font-bold text-gray-900">Invoices</h2>
          <p className="text-sm sm:text-base text-gray-600">View and manage your invoices</p>
        </div>
        <div className="text-sm text-gray-500">
          Total: {filteredInvoices.length} invoices
        </div>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader className="pb-4">
          <CardTitle className="flex items-center gap-2 text-lg">
            <Filter className="h-5 w-5" />
            Filters
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="Search invoices..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>

            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger>
                <SelectValue placeholder="Payment Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="paid">Paid</SelectItem>
                <SelectItem value="pending">Pending</SelectItem>
                <SelectItem value="overdue">Overdue</SelectItem>
                <SelectItem value="cancelled">Cancelled</SelectItem>
              </SelectContent>
            </Select>

            <Select value={typeFilter} onValueChange={setTypeFilter}>
              <SelectTrigger>
                <SelectValue placeholder="Invoice Type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Types</SelectItem>
                <SelectItem value="product_order">Product Orders</SelectItem>
                <SelectItem value="venue_booking">Venue Bookings</SelectItem>
                <SelectItem value="vendor_booking">Vendor Bookings</SelectItem>
                <SelectItem value="subscription">Subscriptions</SelectItem>
              </SelectContent>
            </Select>

            <Button
              variant="outline"
              onClick={() => {
                setSearchTerm('');
                setStatusFilter('all');
                setTypeFilter('all');
              }}
              className="w-full sm:w-auto"
            >
              Clear Filters
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Invoice List */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {filteredInvoices.length === 0 ? (
          <div className="col-span-full">
            <Card>
              <CardContent className="text-center py-8 sm:py-12">
                <FileText className="h-8 w-8 sm:h-12 sm:w-12 text-gray-400 mx-auto mb-3 sm:mb-4" />
                <h3 className="text-base sm:text-lg font-medium text-gray-900 mb-2">No invoices found</h3>
                <p className="text-sm sm:text-base text-gray-600">
                  {searchTerm || statusFilter !== 'all' || typeFilter !== 'all'
                    ? 'Try adjusting your filters to see more results.'
                    : 'You don\'t have any invoices yet.'}
                </p>
              </CardContent>
            </Card>
          </div>
        ) : (
          filteredInvoices.map((invoice) => (
            <Card key={invoice.id} className="hover:shadow-lg transition-shadow h-fit">
              <CardContent className="p-4 sm:p-6 flex flex-col h-full">
                {/* Invoice Header - Mobile Optimized */}
                <div className="flex flex-col sm:flex-row sm:justify-between sm:items-start gap-4 mb-4">
                  <div className="flex-1">
                    <div className="flex flex-wrap items-center gap-2 mb-2">
                      <h3 className="text-base sm:text-lg font-semibold">Invoice #{invoice.invoiceNumber}</h3>
                      <div className="flex flex-wrap gap-1">
                        {getStatusBadge(invoice.paymentStatus)}
                        {getTypeBadge(invoice.type)}
                      </div>
                    </div>
                    <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-4 text-xs sm:text-sm text-gray-600">
                      <span className="flex items-center gap-1">
                        <Calendar className="h-3 w-3 sm:h-4 sm:w-4" />
                        {new Date(invoice.invoiceDate).toLocaleDateString()}
                      </span>
                      {invoice.dueDate && (
                        <span className="flex items-center gap-1">
                          <Calendar className="h-3 w-3 sm:h-4 sm:w-4" />
                          Due: {new Date(invoice.dueDate).toLocaleDateString()}
                        </span>
                      )}
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-xl sm:text-2xl font-bold text-gray-900">
                      ₹{invoice.totalAmount.toLocaleString()}
                    </div>
                    {invoice.paymentMethod && (
                      <div className="text-xs sm:text-sm text-gray-600 flex items-center gap-1 justify-end">
                        <CreditCard className="h-3 w-3" />
                        {invoice.paymentMethod}
                      </div>
                    )}
                  </div>
                </div>

                {/* Customer/Vendor Info - Mobile Optimized */}
                <div className="grid grid-cols-1 gap-3 sm:gap-4 mb-4 p-3 sm:p-4 bg-gray-50 rounded-lg flex-grow">
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2 flex items-center gap-2 text-sm sm:text-base">
                      <User className="h-4 w-4" />
                      Customer
                    </h4>
                    <p className="text-xs sm:text-sm text-gray-700">{invoice.customerName}</p>
                    {invoice.customerEmail && (
                      <p className="text-xs sm:text-sm text-gray-600 flex items-center gap-1">
                        <Mail className="h-3 w-3" />
                        {invoice.customerEmail}
                      </p>
                    )}
                    {invoice.customerPhone && (
                      <p className="text-xs sm:text-sm text-gray-600 flex items-center gap-1">
                        <Phone className="h-3 w-3" />
                        {invoice.customerPhone}
                      </p>
                    )}
                  </div>

                  {invoice.vendorName && (
                    <div>
                      <h4 className="font-medium text-gray-900 mb-2 flex items-center gap-2 text-sm sm:text-base">
                        <Building className="h-4 w-4" />
                        Vendor
                      </h4>
                      <p className="text-xs sm:text-sm text-gray-700">{invoice.vendorName}</p>
                      {invoice.vendorEmail && (
                        <p className="text-xs sm:text-sm text-gray-600 flex items-center gap-1">
                          <Mail className="h-3 w-3" />
                          {invoice.vendorEmail}
                        </p>
                      )}
                    </div>
                  )}
                </div>

                {/* Actions - Mobile Optimized */}
                <div className="flex flex-col sm:flex-row gap-2 sm:gap-3 mt-auto">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => viewInvoiceDetails(invoice)}
                    className="flex items-center gap-2 justify-center"
                  >
                    <Eye className="h-4 w-4" />
                    <span className="hidden sm:inline">View Details</span>
                    <span className="sm:hidden">View</span>
                  </Button>

                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => downloadInvoice(invoice.id)}
                    className="flex items-center gap-2 justify-center"
                  >
                    <Download className="h-4 w-4" />
                    <span className="hidden sm:inline">Download PDF</span>
                    <span className="sm:hidden">Download</span>
                  </Button>

                  {invoice.pdfUrl && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => window.open(invoice.pdfUrl, '_blank')}
                      className="flex items-center gap-2 justify-center"
                    >
                      <ExternalLink className="h-4 w-4" />
                      <span className="hidden sm:inline">Open PDF</span>
                      <span className="sm:hidden">Open</span>
                    </Button>
                  )}
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>

      {/* Invoice Details Modal */}
      {showInvoiceModal && selectedInvoice && (
        <InvoiceDetailsModal
          invoice={selectedInvoice}
          onClose={() => {
            setShowInvoiceModal(false);
            setSelectedInvoice(null);
          }}
          onDownload={() => downloadInvoice(selectedInvoice.id)}
        />
      )}
    </div>
  );
}

// Invoice Details Modal Component
interface InvoiceDetailsModalProps {
  invoice: InvoiceData;
  onClose: () => void;
  onDownload: () => void;
}

function InvoiceDetailsModal({ invoice, onClose, onDownload }: InvoiceDetailsModalProps) {
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-2 sm:p-4">
      <div className="bg-white rounded-lg max-w-4xl w-full max-h-[95vh] sm:max-h-[90vh] overflow-y-auto">
        <div className="flex justify-between items-center p-4 sm:p-6 border-b">
          <h2 className="text-lg sm:text-2xl font-bold">Invoice #{invoice.invoiceNumber}</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors p-1"
          >
            ✕
          </button>
        </div>

        <div className="p-4 sm:p-6 space-y-4 sm:space-y-6">
          {/* Invoice Header */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6">
            <div>
              <h3 className="font-semibold text-gray-900 mb-2">Invoice Details</h3>
              <div className="space-y-1 text-sm">
                <p><span className="font-medium">Date:</span> {new Date(invoice.invoiceDate).toLocaleDateString()}</p>
                {invoice.dueDate && (
                  <p><span className="font-medium">Due Date:</span> {new Date(invoice.dueDate).toLocaleDateString()}</p>
                )}
                <p><span className="font-medium">Type:</span> {invoice.type.replace('_', ' ').toUpperCase()}</p>
                <p><span className="font-medium">Status:</span> {invoice.paymentStatus.toUpperCase()}</p>
              </div>
            </div>

            <div>
              <h3 className="font-semibold text-gray-900 mb-2">Payment Information</h3>
              <div className="space-y-1 text-sm">
                {invoice.paymentMethod && (
                  <p><span className="font-medium">Method:</span> {invoice.paymentMethod}</p>
                )}
                {invoice.transactionId && (
                  <p><span className="font-medium">Transaction ID:</span> {invoice.transactionId}</p>
                )}
                {invoice.paymentDate && (
                  <p><span className="font-medium">Payment Date:</span> {new Date(invoice.paymentDate).toLocaleDateString()}</p>
                )}
              </div>
            </div>
          </div>

          {/* Items - Mobile Optimized Table */}
          <div>
            <h3 className="font-semibold text-gray-900 mb-4">Items</h3>
            <div className="border rounded-lg overflow-hidden">
              <div className="overflow-x-auto">
                <table className="w-full min-w-full">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-3 sm:px-4 py-2 sm:py-3 text-left text-xs sm:text-sm font-medium text-gray-900">Item</th>
                      <th className="px-2 sm:px-4 py-2 sm:py-3 text-center text-xs sm:text-sm font-medium text-gray-900">Qty</th>
                      <th className="px-2 sm:px-4 py-2 sm:py-3 text-right text-xs sm:text-sm font-medium text-gray-900">Unit Price</th>
                      <th className="px-2 sm:px-4 py-2 sm:py-3 text-right text-xs sm:text-sm font-medium text-gray-900">Total</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-200">
                    {invoice.items.map((item, index) => (
                      <tr key={index}>
                        <td className="px-3 sm:px-4 py-2 sm:py-3">
                          <div>
                            <div className="font-medium text-gray-900 text-xs sm:text-sm">{item.name}</div>
                            {item.description && (
                              <div className="text-xs text-gray-600">{item.description}</div>
                            )}
                          </div>
                        </td>
                        <td className="px-2 sm:px-4 py-2 sm:py-3 text-center text-xs sm:text-sm">{item.quantity}</td>
                        <td className="px-2 sm:px-4 py-2 sm:py-3 text-right text-xs sm:text-sm">₹{item.unitPrice.toLocaleString()}</td>
                        <td className="px-2 sm:px-4 py-2 sm:py-3 text-right font-medium text-xs sm:text-sm">₹{item.totalPrice.toLocaleString()}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>

          {/* Totals */}
          <div className="border-t pt-4">
            <div className="flex justify-end">
              <div className="w-full sm:w-64 space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Subtotal:</span>
                  <span>₹{invoice.subtotal.toLocaleString()}</span>
                </div>
                {invoice.discountAmount > 0 && (
                  <div className="flex justify-between text-green-600 text-sm">
                    <span>Discount:</span>
                    <span>-₹{invoice.discountAmount.toLocaleString()}</span>
                  </div>
                )}
                <div className="flex justify-between text-sm">
                  <span>Tax:</span>
                  <span>₹{invoice.taxAmount.toLocaleString()}</span>
                </div>
                <div className="flex justify-between font-bold text-base sm:text-lg border-t pt-2">
                  <span>Total:</span>
                  <span>₹{invoice.totalAmount.toLocaleString()}</span>
                </div>
              </div>
            </div>
          </div>

          {/* Notes */}
          {invoice.notes && (
            <div>
              <h3 className="font-semibold text-gray-900 mb-2">Notes</h3>
              <p className="text-sm text-gray-700 bg-gray-50 p-3 rounded">{invoice.notes}</p>
            </div>
          )}

          {/* Actions - Mobile Optimized */}
          <div className="flex flex-col sm:flex-row gap-3 pt-4 border-t">
            <Button onClick={onDownload} className="flex items-center gap-2 justify-center">
              <Download className="h-4 w-4" />
              Download PDF
            </Button>
            <Button variant="outline" onClick={onClose} className="justify-center">
              Close
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
