'use client';

import React, { useState, useEffect, Suspense } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  Download, 
  Eye, 
  Search, 
  Filter, 
  Calendar,
  CreditCard,
  FileText,
  Building,
  User,
  Package,
  MapPin,
  Phone,
  Mail,
  ExternalLink
} from 'lucide-react';
import { invoiceService, type InvoiceData } from '@/lib/services/invoiceService';
import { useAuth } from '@/contexts/AuthContext';
import { showToast } from '@/lib/toast'

interface InvoiceViewerProps {
  userType?: 'customer' | 'vendor' | 'admin';
  userId?: string;
}

// Error boundary component
class InvoiceViewerErrorBoundary extends React.Component<
  { children: React.ReactNode },
  { hasError: boolean; error?: Error }
> {
  constructor(props: { children: React.ReactNode }) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('InvoiceViewer Error:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="text-center py-12">
          <FileText className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 mb-2">
            Something went wrong loading invoices
          </h3>
          <p className="text-gray-600 mb-4">
            There was an error loading the invoice viewer. Please try refreshing the page.
          </p>
          <Button 
            onClick={() => window.location.reload()} 
            variant="outline"
          >
            Refresh Page
          </Button>
        </div>
      );
    }

    return this.props.children;
  }
}

export default function InvoiceViewer({ userType = 'customer', userId }: InvoiceViewerProps) {
  const [invoices, setInvoices] = useState<InvoiceData[]>([]);
  const [filteredInvoices, setFilteredInvoices] = useState<InvoiceData[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [typeFilter, setTypeFilter] = useState('all');
  const [selectedInvoice, setSelectedInvoice] = useState<InvoiceData | null>(null);
  const [showInvoiceModal, setShowInvoiceModal] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [invoicesPerPage, setInvoicesPerPage] = useState(10);
  const [totalPages, setTotalPages] = useState(1);
  
  const { user, userProfile } = useAuth();

  const currentUserId = userId || user?.userId;

  useEffect(() => {
    if (currentUserId) {
      loadInvoices();
    }
  }, [currentUserId, userType]);

  useEffect(() => {
    filterInvoices();
    setCurrentPage(1); // Reset to first page when filters change
  }, [searchTerm, statusFilter, typeFilter, invoices]);

  // Calculate pagination
  useEffect(() => {
    const total = filteredInvoices.length;
    const pages = Math.ceil(total / invoicesPerPage);
    setTotalPages(pages);
    
    // Adjust current page if it exceeds total pages
    if (currentPage > pages && pages > 0) {
      setCurrentPage(pages);
    }
  }, [filteredInvoices, currentPage, invoicesPerPage]);

  // Get current invoices for display
  const getCurrentInvoices = () => {
    const startIndex = (currentPage - 1) * invoicesPerPage;
    const endIndex = startIndex + invoicesPerPage;
    return filteredInvoices.slice(startIndex, endIndex);
  };

  // Handle page change
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    // Scroll to top of invoice list
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const loadInvoices = async () => {
    if (!currentUserId) return;
    
    setLoading(true);
    setError(null);
    try {
      const invoiceData = await invoiceService.getInvoicesForUser(currentUserId, userType);
      setInvoices(invoiceData);
    } catch (error) {
      console.error('Error loading invoices:', error);
      setError('Failed to load invoices. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const filterInvoices = () => {
    let filtered = invoices;

    if (searchTerm) {
      filtered = filtered.filter(invoice =>
        invoice.invoiceNumber?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        invoice.customer?.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (invoice.vendor?.name && invoice.vendor.name.toLowerCase().includes(searchTerm.toLowerCase()))
      );
    }

    if (statusFilter !== 'all') {
      filtered = filtered.filter(invoice => invoice.paymentStatus?.toLowerCase() === statusFilter);
    }

    if (typeFilter !== 'all') {
      filtered = filtered.filter(invoice => invoice.type?.toLowerCase() === typeFilter);
    }

    setFilteredInvoices(filtered);
  };

  const downloadInvoice = async (invoiceNumber: string) => {
    try {
      const result = await invoiceService.downloadInvoicePDF(invoiceNumber);
      if (result.success && result.blob) {
        const url = window.URL.createObjectURL(result.blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `invoice_${invoiceNumber}.pdf`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
        showToast.success('Invoice downloaded successfully');
      } else {
        showToast.error('Failed to download invoice');
      }
    } catch (error) {
      console.error('Error downloading invoice:', error);
      showToast.error('An error occurred while downloading invoice');
    }
  };

  const viewInvoiceDetails = (invoice: InvoiceData) => {
    setSelectedInvoice(invoice);
    setShowInvoiceModal(true);
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      paid: 'bg-green-100 text-green-800',
      pending: 'bg-yellow-100 text-yellow-800',
      overdue: 'bg-red-100 text-red-800',
      cancelled: 'bg-gray-100 text-gray-800'
    };

    const config = statusConfig[status?.toLowerCase() as keyof typeof statusConfig] || statusConfig.pending;
    return config;
  };

  const getTypeBadge = (type: string) => {
    const typeConfig = {
      product_order: 'bg-blue-100 text-blue-800',
      venue_booking: 'bg-purple-100 text-purple-800',
      vendor_booking: 'bg-orange-100 text-orange-800',
      subscription: 'bg-indigo-100 text-indigo-800'
    };

    const config = typeConfig[type?.toLowerCase() as keyof typeof typeConfig] || typeConfig.product_order;
    return config;
  };

  const getTypeLabel = (type: string) => {
    const typeLabels = {
      product_order: 'Product Order',
      venue_booking: 'Venue Booking',
      vendor_booking: 'Vendor Booking',
      subscription: 'Subscription'
    };

    return typeLabels[type?.toLowerCase() as keyof typeof typeLabels] || 'Product Order';
  };

  // Render loading state
  if (loading) {
    return (
      <div className="text-center py-12">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
        <p className="text-gray-600">Loading invoices...</p>
      </div>
    );
  }

  // Render error state
  if (error) {
    return (
      <div className="text-center py-12">
        <FileText className="h-12 w-12 text-red-500 mx-auto mb-4" />
        <h3 className="text-lg font-semibold text-gray-900 mb-2">
          Error Loading Invoices
        </h3>
        <p className="text-gray-600 mb-4">{error}</p>
        <Button onClick={loadInvoices} variant="outline">
          Try Again
        </Button>
      </div>
    );
  }

  return (
    <InvoiceViewerErrorBoundary>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4">
          <div>
            <h1 className="text-2xl sm:text-3xl font-bold text-gray-900">Invoice Dashboard</h1>
            <p className="text-sm sm:text-base text-gray-600">
              Manage and track all your invoices in one place
            </p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" size="sm">
              <Download className="h-4 w-4 mr-2" />
              Export All
            </Button>
          </div>
        </div>

        {/* Filters */}
        <Card>
          <CardContent className="p-6">
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search invoices..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>

              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="Payment Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="paid">Paid</SelectItem>
                  <SelectItem value="pending">Pending</SelectItem>
                  <SelectItem value="overdue">Overdue</SelectItem>
                  <SelectItem value="cancelled">Cancelled</SelectItem>
                </SelectContent>
              </Select>

              <Select value={typeFilter} onValueChange={setTypeFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="Invoice Type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Types</SelectItem>
                  <SelectItem value="product_order">Product Orders</SelectItem>
                  <SelectItem value="venue_booking">Venue Bookings</SelectItem>
                  <SelectItem value="vendor_booking">Vendor Bookings</SelectItem>
                  <SelectItem value="subscription">Subscriptions</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        {/* Invoices List */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Recent Invoices
            </CardTitle>
            <CardDescription>
              {filteredInvoices.length} invoices found
            </CardDescription>
          </CardHeader>
          <CardContent>
            {filteredInvoices.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                No invoices found matching your criteria
              </div>
            ) : (
              <div className="space-y-4">
                {getCurrentInvoices().map((invoice, index) => (
                  <div key={index} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50">
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        <span className="font-semibold text-gray-900">
                          {invoice.invoiceNumber}
                        </span>
                        <Badge className={getStatusBadge(invoice.paymentStatus)}>
                          {invoice.paymentStatus}
                        </Badge>
                        <Badge className={getTypeBadge(invoice.type)}>
                          {getTypeLabel(invoice.type)}
                        </Badge>
                      </div>
                      <div className="text-sm text-gray-600">
                        <div className="flex items-center gap-4">
                          <span className="flex items-center gap-1">
                            <Calendar className="h-3 w-3" />
                            {invoice.invoiceDate ? new Date(invoice.invoiceDate).toLocaleDateString() : 'N/A'}
                          </span>
                          <span className="flex items-center gap-1">
                            <CreditCard className="h-3 w-3" />
                            ₹{invoice.totalAmount?.toLocaleString() || '0'}
                          </span>
                          {invoice.customer && (
                            <span className="flex items-center gap-1">
                              <User className="h-3 w-3" />
                              {invoice.customer.name}
                            </span>
                          )}
                        </div>
                      </div>
                    </div>
                    <div className="flex gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => viewInvoiceDetails(invoice)}
                      >
                        <Eye className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => downloadInvoice(invoice.invoiceNumber)}
                      >
                        <Download className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Pagination */}
        {totalPages > 1 && (
          <Card>
            <CardContent className="p-4">
              <div className="flex flex-col sm:flex-row justify-between items-center gap-4">
                <div className="text-sm text-gray-600">
                  Showing {((currentPage - 1) * invoicesPerPage) + 1} to {Math.min(currentPage * invoicesPerPage, filteredInvoices.length)} of {filteredInvoices.length} invoices
                </div>
                
                <div className="flex items-center gap-4">
                  {/* Page Size Selector */}
                  <div className="flex items-center gap-2">
                    <span className="text-sm text-gray-600">Show:</span>
                    <Select value={invoicesPerPage.toString()} onValueChange={(value) => {
                      setInvoicesPerPage(parseInt(value));
                      setCurrentPage(1); // Reset to first page when changing page size
                    }}>
                      <SelectTrigger className="w-20">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="5">5</SelectItem>
                        <SelectItem value="10">10</SelectItem>
                        <SelectItem value="20">20</SelectItem>
                        <SelectItem value="50">50</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div className="flex items-center gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handlePageChange(1)}
                      disabled={currentPage === 1}
                    >
                      First
                    </Button>
                    
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handlePageChange(currentPage - 1)}
                      disabled={currentPage === 1}
                    >
                      Previous
                    </Button>
                    
                    {/* Page Numbers */}
                    <div className="flex items-center gap-1">
                      {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                        let pageNum;
                        if (totalPages <= 5) {
                          pageNum = i + 1;
                        } else if (currentPage <= 3) {
                          pageNum = i + 1;
                        } else if (currentPage >= totalPages - 2) {
                          pageNum = totalPages - 4 + i;
                        } else {
                          pageNum = currentPage - 2 + i;
                        }
                        
                        return (
                          <Button
                            key={pageNum}
                            variant={currentPage === pageNum ? "default" : "outline"}
                            size="sm"
                            onClick={() => handlePageChange(pageNum)}
                            className="w-10 h-10"
                          >
                            {pageNum}
                          </Button>
                        );
                      })}
                    </div>
                    
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handlePageChange(currentPage + 1)}
                      disabled={currentPage === totalPages}
                    >
                      Next
                    </Button>
                    
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handlePageChange(totalPages)}
                      disabled={currentPage === totalPages}
                    >
                      Last
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Invoice Details Modal */}
        {showInvoiceModal && selectedInvoice && (
          <InvoiceDetailsModal
            invoice={selectedInvoice}
            onClose={() => {
              setShowInvoiceModal(false);
              setSelectedInvoice(null);
            }}
            onDownload={() => downloadInvoice(selectedInvoice.invoiceNumber)}
          />
        )}
      </div>
    </InvoiceViewerErrorBoundary>
  );
}

// Helper functions
function getStatusBadge(status: string) {
  const statusConfig = {
    paid: 'bg-green-100 text-green-800',
    pending: 'bg-yellow-100 text-yellow-800',
    overdue: 'bg-red-100 text-red-800',
    cancelled: 'bg-gray-100 text-gray-800'
  };

  const config = statusConfig[status?.toLowerCase() as keyof typeof statusConfig] || statusConfig.pending;
  return config;
}

function getTypeBadge(type: string) {
  const typeConfig = {
    product_order: 'bg-blue-100 text-blue-800',
    venue_booking: 'bg-purple-100 text-purple-800',
    vendor_booking: 'bg-orange-100 text-orange-800',
    subscription: 'bg-indigo-100 text-indigo-800'
  };

  const config = typeConfig[type?.toLowerCase() as keyof typeof typeConfig] || typeConfig.product_order;
  return config;
}

function getTypeLabel(type: string) {
  const typeLabels = {
    product_order: 'Product Order',
    venue_booking: 'Venue Booking',
    vendor_booking: 'Vendor Booking',
    subscription: 'Subscription'
  };

  return typeLabels[type?.toLowerCase() as keyof typeof typeLabels] || 'Unknown';
}

// Invoice Details Modal Component
function InvoiceDetailsModal({ 
  invoice, 
  onClose, 
  onDownload 
}: { 
  invoice: InvoiceData; 
  onClose: () => void; 
  onDownload: () => void; 
}) {
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-bold">Invoice Details</h2>
          <Button
            variant="outline"
            size="sm"
            onClick={onClose}
          >
            Close
          </Button>
        </div>
        
        <div className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <h3 className="font-semibold text-gray-700">Invoice Number</h3>
              <p className="text-gray-900">{invoice.invoiceNumber}</p>
            </div>
            <div>
              <h3 className="font-semibold text-gray-700">Date</h3>
              <p className="text-gray-900">{invoice.invoiceDate ? new Date(invoice.invoiceDate).toLocaleDateString() : 'N/A'}</p>
            </div>
            <div>
              <h3 className="font-semibold text-gray-700">Status</h3>
              <Badge className={getStatusBadge(invoice.paymentStatus)}>
                {invoice.paymentStatus}
              </Badge>
            </div>
            <div>
              <h3 className="font-semibold text-gray-700">Type</h3>
              <Badge className={getTypeBadge(invoice.type)}>
                {getTypeLabel(invoice.type)}
              </Badge>
            </div>
          </div>
          
          <div>
            <h3 className="font-semibold text-gray-700 mb-2">Customer Information</h3>
            <div className="bg-gray-50 p-3 rounded">
              <p><strong>Name:</strong> {invoice.customer.name}</p>
              <p><strong>Email:</strong> {invoice.customer.email}</p>
              {invoice.customer.phone && <p><strong>Phone:</strong> {invoice.customer.phone}</p>}
            </div>
          </div>
          
          {invoice.vendor && (
            <div>
              <h3 className="font-semibold text-gray-700 mb-2">Vendor Information</h3>
              <div className="bg-gray-50 p-3 rounded">
                <p><strong>Name:</strong> {invoice.vendor.name}</p>
                <p><strong>Email:</strong> {invoice.vendor.email}</p>
                {invoice.vendor.businessName && <p><strong>Business:</strong> {invoice.vendor.businessName}</p>}
              </div>
            </div>
          )}
          
          <div>
            <h3 className="font-semibold text-gray-700 mb-2">Items</h3>
            <div className="space-y-2">
              {invoice.items.map((item, index) => (
                <div key={index} className="flex justify-between items-center bg-gray-50 p-3 rounded">
                  <div>
                    <p className="font-medium">{item.name}</p>
                    <p className="text-sm text-gray-600">Qty: {item.quantity}</p>
                  </div>
                  <div className="text-right">
                    <p className="font-medium">₹{item.unitPrice.toLocaleString()}</p>
                    <p className="text-sm text-gray-600">Total: ₹{item.totalPrice.toLocaleString()}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
          
          <div className="border-t pt-4">
            <div className="flex justify-between items-center">
              <span className="font-semibold">Total Amount:</span>
              <span className="text-xl font-bold text-primary">₹{invoice.totalAmount.toLocaleString()}</span>
            </div>
          </div>
          
          <div className="flex gap-2 pt-4">
            <Button onClick={onDownload} className="flex-1">
              <Download className="h-4 w-4 mr-2" />
              Download PDF
            </Button>
            <Button variant="outline" onClick={onClose} className="flex-1">
              Close
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
