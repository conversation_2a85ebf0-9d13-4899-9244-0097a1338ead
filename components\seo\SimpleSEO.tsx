'use client';

import Head from 'next/head';
import { SITE_URL } from '@/lib/config/seo';

interface SimpleSEOProps {
  title?: string;
  description?: string;
  keywords?: string;
  image?: string;
  url?: string;
  type?: string;
}

export const SimpleSEO: React.FC<SimpleSEOProps> = ({
  title = "BookmyFestive - India's Leading Wedding Planning Platform | Find Wedding Vendors",
  description = "Plan your perfect Indian wedding with BookmyFestive. Connect with 1000+ verified vendors, get expert planning tips, and manage your entire wedding journey in one platform.",
  keywords = "wedding planning India, wedding vendors, wedding venues, wedding photography, Indian wedding, wedding planning platform, wedding services",
  image = "/hero_image_1.jpg",
  url = "/",
  type = "website"
}) => {
  const fullUrl = url.startsWith('http') ? url : `${SITE_URL}${url}`;
  const fullImageUrl = image.startsWith('http') ? image : `${SITE_URL}${image}`;

  return (
    <Head>
      <title>{title}</title>
      <meta name="description" content={description} />
      <meta name="keywords" content={keywords} />
      <meta name="robots" content="index,follow" />
      <meta name="googlebot" content="index,follow" />
      <link rel="canonical" href={fullUrl} />
      
      {/* Open Graph */}
      <meta property="og:title" content={title} />
      <meta property="og:description" content={description} />
      <meta property="og:image" content={fullImageUrl} />
      <meta property="og:url" content={fullUrl} />
      <meta property="og:type" content={type} />
      <meta property="og:site_name" content="BookmyFestive" />
      <meta property="og:locale" content="en_US" />
      
      {/* Twitter */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:title" content={title} />
      <meta name="twitter:description" content={description} />
      <meta name="twitter:image" content={fullImageUrl} />
      <meta name="twitter:site" content="@BookmyFestive" />
      <meta name="twitter:creator" content="@BookmyFestive" />
      
      {/* Additional Meta Tags */}
      <meta name="author" content="BookmyFestive" />
      <meta name="viewport" content="width=device-width, initial-scale=1" />
      <meta name="theme-color" content="#A31515" />
      <meta name="msapplication-TileColor" content="#A31515" />
      
      {/* Local Business Schema */}
      <meta name="geo.region" content="IN" />
      <meta name="geo.placename" content="Chennai, Tamil Nadu, India" />
      <meta name="geo.position" content="13.0827;80.2707" />
      <meta name="ICBM" content="13.0827, 80.2707" />
    </Head>
  );
};

// Enhanced structured data component
export const SimpleStructuredData = () => {
  const organizationSchema = {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "BookmyFestive",
    "url": SITE_URL,
    "logo": `${SITE_URL}/BookmyFestive_logo.webp`,
    "description": "India's premier wedding planning platform connecting couples with 1000+ verified vendors, venues, and services.",
    "foundingDate": "2024",
    "contactPoint": {
      "@type": "ContactPoint",
      "contactType": "customer service",
      "telephone": "+91-**********",
      "email": "<EMAIL>",
      "availableLanguage": ["English", "Tamil", "Hindi"]
    },
    "address": {
      "@type": "PostalAddress",
      "addressLocality": "Chennai",
      "addressRegion": "Tamil Nadu",
      "addressCountry": "IN"
    },
    "sameAs": [
      "https://www.facebook.com/BookmyFestive",
      "https://www.instagram.com/BookmyFestive",
      "https://www.linkedin.com/company/BookmyFestive",
      "https://www.pinterest.com/bookmyFestive",
      "https://www.youtube.com/BookmyFestive"
    ],
    "hasOfferCatalog": {
      "@type": "OfferCatalog",
      "name": "Wedding Services",
      "itemListElement": [
        {
          "@type": "Offer",
          "itemOffered": {
            "@type": "Service",
            "name": "Wedding Photography",
            "description": "Professional wedding photography services"
          }
        },
        {
          "@type": "Offer",
          "itemOffered": {
            "@type": "Service",
            "name": "Wedding Venues",
            "description": "Wedding venues and banquet halls"
          }
        },
        {
          "@type": "Offer",
          "itemOffered": {
            "@type": "Service",
            "name": "Wedding Catering",
            "description": "Wedding catering services"
          }
        }
      ]
    }
  };

  const websiteSchema = {
    "@context": "https://schema.org",
    "@type": "WebSite",
    "name": "BookmyFestive",
    "url": SITE_URL,
    "description": "India's leading wedding planning platform",
    "potentialAction": {
      "@type": "SearchAction",
      "target": `${SITE_URL}/search?q={search_term_string}`,
      "query-input": "required name=search_term_string"
    }
  };

  const localBusinessSchema = {
    "@context": "https://schema.org",
    "@type": "LocalBusiness",
    "name": "BookmyFestive",
    "description": "India's premier wedding planning platform",
    "url": SITE_URL,
    "telephone": "+91-**********",
    "email": "<EMAIL>",
    "address": {
      "@type": "PostalAddress",
      "addressLocality": "Chennai",
      "addressRegion": "Tamil Nadu",
      "addressCountry": "IN"
    },
    "geo": {
      "@type": "GeoCoordinates",
      "latitude": 13.0827,
      "longitude": 80.2707
    },
    "openingHours": "Mo-Su 09:00-20:00",
    "priceRange": "₹₹",
    "currenciesAccepted": "INR",
    "paymentAccepted": "Cash, Credit Card, Debit Card, UPI, Net Banking"
  };

  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(organizationSchema)
        }}
      />
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(websiteSchema)
        }}
      />
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(localBusinessSchema)
        }}
      />
    </>
  );
};

// Enhanced Google Analytics component
export const SimpleGoogleAnalytics = () => {
  const GA_MEASUREMENT_ID = process.env.NEXT_PUBLIC_GA_MEASUREMENT_ID;

  if (!GA_MEASUREMENT_ID) {
    return null;
  }

  return (
    <>
      <script
        async
        src={`https://www.googletagmanager.com/gtag/js?id=${GA_MEASUREMENT_ID}`}
      />
      <script
        dangerouslySetInnerHTML={{
          __html: `
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            gtag('js', new Date());
            gtag('config', '${GA_MEASUREMENT_ID}', {
              page_title: document.title,
              page_location: window.location.href,
              custom_map: {
                'custom_parameter_1': 'page_category',
                'custom_parameter_2': 'user_type'
              }
            });
            
            // Track page views
            gtag('event', 'page_view', {
              page_title: document.title,
              page_location: window.location.href,
              page_category: 'wedding_planning'
            });
          `,
        }}
      />
    </>
  );
};
