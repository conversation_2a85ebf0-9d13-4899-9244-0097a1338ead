import { generateClient } from 'aws-amplify/api';
import { getCurrentUser } from 'aws-amplify/auth';
import {
  createShop,
  updateShop,
  deleteShop
} from '@/src/graphql/mutations';
import {
  getShop,
  listShops,
  shopsByUserId
} from '@/src/graphql/queries';
import { handleShopOperation } from '@/lib/dynamodb-error-handler';

// Create client with user pool authentication for authenticated operations
const authenticatedClient = generateClient({
  authMode: 'userPool'
});

// Create client with API key for public operations
const publicClient = generateClient({
  authMode: 'apiKey'
});

export interface ShopSpecifications {
  fabric?: string;
  work?: string;
  occasion?: string;
  care?: string;
  delivery?: string;
  returnPolicy?: string;
}

export interface ShopResponse {
  id: string;
  userId: string;
  name: string;
  category: string;
  price: string;
  originalPrice?: string;
  discount?: number;
  stock: number;
  sku?: string;
  brand?: string;
  featured?: boolean;
  description?: string;
  features?: string[];
  sizes?: string[];
  colors?: string[];
  images?: string[];
  specifications?: ShopSpecifications;
  rating?: number;
  reviewCount?: number;
  inStock: boolean;
  status: string;
  createdAt: string;
  updatedAt: string;
}

export interface CreateShopInput {
  userId: string;
  name: string;
  category: string;
  price: string;
  originalPrice?: string;
  discount?: number;
  stock: number;
  sku?: string;
  brand?: string;
  featured?: boolean;
  description?: string;
  features?: string[];
  sizes?: string[];
  colors?: string[];
  images?: string[];
  specifications?: ShopSpecifications;
  rating?: number;
  reviewCount?: number;
  inStock: boolean;
  status: string;
}

export interface UpdateShopInput {
  id: string;
  userId?: string;
  name?: string;
  category?: string;
  price?: string;
  originalPrice?: string;
  discount?: number;
  stock?: number;
  sku?: string;
  brand?: string;
  featured?: boolean;
  description?: string;
  features?: string[];
  sizes?: string[];
  colors?: string[];
  images?: string[];
  specifications?: ShopSpecifications;
  rating?: number;
  reviewCount?: number;
  inStock?: boolean;
  status?: string;
}

export const shopService = {
  // Create a new shop product
  async createShop(input: CreateShopInput): Promise<ShopResponse> {
    return handleShopOperation(async (data) => {
      const result: any = await authenticatedClient.graphql({
        query: createShop,
        variables: { input: data }
      });
      return result.data.createShop;
    }, input);
  },

  // Get a single shop product by ID
  async getShop(id: string): Promise<ShopResponse> {
    try {
      const result: any = await publicClient.graphql({
        query: getShop,
        variables: { id }
      });
      return result.data.getShop;
    } catch (error) {
      console.error('Error fetching shop:', error);
      throw error;
    }
  },

  // Update an existing shop product
  async updateShop(input: UpdateShopInput): Promise<ShopResponse> {
    return handleShopOperation(async (data) => {
      const result: any = await authenticatedClient.graphql({
        query: updateShop,
        variables: { input: data }
      });
      return result.data.updateShop;
    }, input);
  },

  // Delete a shop product
  async deleteShop(id: string): Promise<ShopResponse> {
    try {
      const result: any = await authenticatedClient.graphql({
        query: deleteShop,
        variables: { input: { id } }
      });
      return result.data.deleteShop;
    } catch (error) {
      console.error('Error deleting shop:', error);
      throw error;
    }
  },

  // List all shop products with optional filtering
  async listShops(filter?: any, limit: number = 10, nextToken?: string): Promise<{
    items: ShopResponse[];
    nextToken?: string;
  }> {
    try {
      const result: any = await publicClient.graphql({
        query: listShops,
        variables: { filter, limit, nextToken }
      });
      return result.data.listShops;
    } catch (error) {
      console.error('Error listing shops:', error);
      throw error;
    }
  },

  // Get all shops with pagination (for backward compatibility)
  async getAllShops(limit: number = 10, nextToken?: string): Promise<{
    shops: ShopResponse[];
    nextToken?: string;
  }> {
    try {
      const result = await this.listShops(undefined, limit, nextToken);
      return {
        shops: result.items,
        nextToken: result.nextToken
      };
    } catch (error) {
      console.error('Error getting all shops:', error);
      throw error;
    }
  },

  // Get shop products by user ID
  async getShopsByUserId(
    userId: string,
    sortDirection?: 'ASC' | 'DESC',
    filter?: any,
    limit: number = 10,
    nextToken?: string
  ): Promise<{
    items: ShopResponse[];
    nextToken?: string;
  }> {
    try {
      const result: any = await authenticatedClient.graphql({
        query: shopsByUserId,
        variables: {
          userId,
          sortDirection,
          filter,
          limit,
          nextToken
        }
      });
      return result.data.shopsByUserId;
    } catch (error) {
      console.error('Error fetching shops by user ID:', error);
      throw error;
    }
  },

  // Search shops by category
  async searchShopsByCategory(category: string, limit?: number): Promise<ShopResponse[]> {
    try {
      const filter = {
        category: { eq: category }
      };
      const result = await this.listShops(filter, limit);
      return result.items;
    } catch (error) {
      console.error('Error searching shops by category:', error);
      throw error;
    }
  },

  // Search shops by name
  async searchShopsByName(name: string, limit?: number): Promise<ShopResponse[]> {
    try {
      const filter = {
        name: { contains: name }
      };
      const result = await this.listShops(filter, limit);
      return result.items;
    } catch (error) {
      console.error('Error searching shops by name:', error);
      throw error;
    }
  },

  // Get featured shops
  async getFeaturedShops(limit?: number): Promise<ShopResponse[]> {
    try {
      const filter = {
        featured: { eq: true }
      };
      const result = await this.listShops(filter, limit);
      return result.items;
    } catch (error) {
      console.error('Error fetching featured shops:', error);
      throw error;
    }
  },

  // Get shops by status
  async getShopsByStatus(status: string, limit?: number): Promise<ShopResponse[]> {
    try {
      const filter = {
        status: { eq: status }
      };
      const result = await this.listShops(filter, limit);
      return result.items;
    } catch (error) {
      console.error('Error fetching shops by status:', error);
      throw error;
    }
  }
};
