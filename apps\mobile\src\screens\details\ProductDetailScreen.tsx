import React, { useState, useEffect } from 'react';
import { View, Text, ScrollView, StyleSheet, TouchableOpacity, Image, Alert, FlatList } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { StackNavigationProp } from '@react-navigation/stack';
import { RouteProp } from '@react-navigation/native';
import { AppStackParamList } from '../../navigation/AppNavigator';
import { useTheme } from '../../providers/ThemeProvider';
import { useCart } from '../../providers/CartProvider';
import { Card, CardContent, Badge, Button } from '../../components/ui';

type ProductDetailScreenNavigationProp = StackNavigationProp<AppStackParamList, 'ProductDetail'>;
type ProductDetailScreenRouteProp = RouteProp<AppStackParamList, 'ProductDetail'>;

interface Props {
  navigation: ProductDetailScreenNavigationProp;
  route: ProductDetailScreenRouteProp;
}

interface Product {
  id: string;
  name: string;
  description: string;
  price: number;
  images: string[];
  category: string;
  vendorId: string;
  vendorName: string;
  inStock: boolean;
  rating: number;
  reviewCount: number;
  sizes?: string[];
  colors?: string[];
  material?: string;
  care?: string[];
  features?: string[];
}

export default function ProductDetailScreen({ navigation, route }: Props) {
  const { theme } = useTheme();
  const { addToCart } = useCart();
  const { productId } = route.params;
  const [product, setProduct] = useState<Product | null>(null);
  const [loading, setLoading] = useState(true);
  const [isFavorite, setIsFavorite] = useState(false);
  const [selectedSize, setSelectedSize] = useState<string>('');
  const [selectedColor, setSelectedColor] = useState<string>('');
  const [quantity, setQuantity] = useState(1);

  useEffect(() => {
    loadProductDetails();
  }, [productId]);

  const loadProductDetails = async () => {
    try {
      setLoading(true);
      // Mock data for now - replace with actual API call
      const mockProduct: Product = {
        id: productId,
        name: 'Designer Bridal Lehenga',
        description: 'Exquisite handcrafted bridal lehenga with intricate embroidery and premium fabrics. Perfect for your special day with traditional elegance and modern comfort.',
        price: 45000,
        images: [
          '/placeholder-image.jpg
          '/placeholder-image.jpg
          '/placeholder-image.jpg
        ],
        category: 'Bridal Wear',
        vendorId: 'vendor-1',
        vendorName: 'Royal Bridal Collection',
        inStock: true,
        rating: 4.7,
        reviewCount: 23,
        sizes: ['S', 'M', 'L', 'XL', 'XXL'],
        colors: ['Red', 'Maroon', 'Pink', 'Gold'],
        material: 'Silk with Zardozi work',
        care: [
          'Dry clean only',
          'Store in a cool, dry place',
          'Avoid direct sunlight',
          'Handle with care',
        ],
        features: [
          'Handcrafted embroidery',
          'Premium silk fabric',
          'Traditional design',
          'Comfortable fit',
          'Includes dupatta',
          'Blouse piece included',
        ],
      };
      
      setProduct(mockProduct);
      if (mockProduct.sizes && mockProduct.sizes.length > 0) {
        setSelectedSize(mockProduct.sizes[0]);
      }
      if (mockProduct.colors && mockProduct.colors.length > 0) {
        setSelectedColor(mockProduct.colors[0]);
      }
    } catch (error) {
      console.error('Error loading product details:', error);
      Alert.alert('Error', 'Failed to load product details');
    } finally {
      setLoading(false);
    }
  };

  const toggleFavorite = () => {
    setIsFavorite(!isFavorite);
    // TODO: Implement favorite functionality with backend
  };

  const handleAddToCart = async () => {
    if (!product) return;

    if (product.sizes && !selectedSize) {
      Alert.alert('Size Required', 'Please select a size');
      return;
    }

    if (product.colors && !selectedColor) {
      Alert.alert('Color Required', 'Please select a color');
      return;
    }

    try {
      await addToCart({
        id: product.id,
        name: product.name,
        price: product.price,
        quantity: quantity,
        image: product.images[0],
        category: product.category,
        vendorName: product.vendorName,
        selectedSize,
        selectedColor,
      });

      Alert.alert(
        'Added to Cart',
        `${product.name} has been added to your cart`,
        [
          { text: 'Continue Shopping', style: 'cancel' },
          { text: 'View Cart', onPress: () => navigation.navigate('Cart') },
        ]
      );
    } catch (error) {
      Alert.alert('Error', 'Failed to add item to cart');
    }
  };

  const handleBuyNow = () => {
    handleAddToCart();
    // Navigate to cart or checkout
    navigation.navigate('Cart');
  };

  if (loading) {
    return (
      <View style={[styles.container, styles.centered, { backgroundColor: theme.colors.background }]}>
        <Text style={[styles.loadingText, { color: theme.colors.text }]}>Loading...</Text>
      </View>
    );
  }

  if (!product) {
    return (
      <View style={[styles.container, styles.centered, { backgroundColor: theme.colors.background }]}>
        <Text style={[styles.errorText, { color: theme.colors.error }]}>Product not found</Text>
      </View>
    );
  }

  return (
    <ScrollView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {/* Product Images - Matching Web App */}
      <View style={styles.imageContainer}>
        <FlatList
          data={product.images}
          horizontal
          pagingEnabled
          showsHorizontalScrollIndicator={false}
          keyExtractor={(item, index) => index.toString()}
          renderItem={({ item }) => (
            <Image
              source={{ uri: item }}
              style={styles.productImage}
              resizeMode="cover"
            />
          )}
        />

        {/* Floating Action Buttons - Matching Web App */}
        <View style={styles.floatingActions}>
          <TouchableOpacity style={styles.actionButton} onPress={toggleFavorite}>
            <Ionicons
              name={isFavorite ? 'heart' : 'heart-outline'}
              size={24}
              color={isFavorite ? '#EF4444' : theme.colors.foreground}
            />
          </TouchableOpacity>
          <TouchableOpacity style={styles.actionButton} onPress={() => {/* Share functionality */}}>
            <Ionicons name="share-outline" size={24} color={theme.colors.foreground} />
          </TouchableOpacity>
        </View>

        {/* Image Indicators */}
        <View style={styles.imageIndicators}>
          {product.images.map((_, index) => (
            <View
              key={index}
              style={[
                styles.imageIndicator,
                { backgroundColor: theme.colors.primaryForeground }
              ]}
            />
          ))}
        </View>
      </View>

      {/* Product Info */}
      <View style={[styles.infoContainer, { backgroundColor: theme.colors.surface }]}>
        <Text style={[styles.productName, { color: theme.colors.text }]}>
          {product.name}
        </Text>

        <Text style={[styles.vendorName, { color: theme.colors.primary }]}>
          by {product.vendorName}
        </Text>

        <View style={styles.ratingContainer}>
          <Ionicons name="star" size={16} color="#F59E0B" />
          <Text style={[styles.rating, { color: theme.colors.text }]}>
            {product.rating} ({product.reviewCount} reviews)
          </Text>
        </View>

        <Text style={[styles.price, { color: theme.colors.primary }]}>
          ₹{product.price.toLocaleString()}
        </Text>

        <View style={styles.stockContainer}>
          <Ionicons 
            name={product.inStock ? 'checkmark-circle' : 'close-circle'} 
            size={16} 
            color={product.inStock ? theme.colors.success : theme.colors.error} 
          />
          <Text style={[
            styles.stockText, 
            { color: product.inStock ? theme.colors.success : theme.colors.error }
          ]}>
            {product.inStock ? 'In Stock' : 'Out of Stock'}
          </Text>
        </View>
      </View>

      {/* Size Selection */}
      {product.sizes && product.sizes.length > 0 && (
        <View style={[styles.section, { backgroundColor: theme.colors.surface }]}>
          <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>Size</Text>
          <View style={styles.optionsContainer}>
            {product.sizes.map((size) => (
              <TouchableOpacity
                key={size}
                style={[
                  styles.optionButton,
                  {
                    backgroundColor: selectedSize === size ? theme.colors.primary : theme.colors.background,
                    borderColor: theme.colors.border,
                  }
                ]}
                onPress={() => setSelectedSize(size)}
              >
                <Text style={[
                  styles.optionText,
                  { color: selectedSize === size ? 'white' : theme.colors.text }
                ]}>
                  {size}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>
      )}

      {/* Color Selection */}
      {product.colors && product.colors.length > 0 && (
        <View style={[styles.section, { backgroundColor: theme.colors.surface }]}>
          <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>Color</Text>
          <View style={styles.optionsContainer}>
            {product.colors.map((color) => (
              <TouchableOpacity
                key={color}
                style={[
                  styles.optionButton,
                  {
                    backgroundColor: selectedColor === color ? theme.colors.primary : theme.colors.background,
                    borderColor: theme.colors.border,
                  }
                ]}
                onPress={() => setSelectedColor(color)}
              >
                <Text style={[
                  styles.optionText,
                  { color: selectedColor === color ? 'white' : theme.colors.text }
                ]}>
                  {color}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>
      )}

      {/* Quantity */}
      <View style={[styles.section, { backgroundColor: theme.colors.surface }]}>
        <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>Quantity</Text>
        <View style={styles.quantityContainer}>
          <TouchableOpacity
            style={[styles.quantityButton, { borderColor: theme.colors.border }]}
            onPress={() => setQuantity(Math.max(1, quantity - 1))}
          >
            <Ionicons name="remove" size={20} color={theme.colors.text} />
          </TouchableOpacity>
          <Text style={[styles.quantityText, { color: theme.colors.text }]}>
            {quantity}
          </Text>
          <TouchableOpacity
            style={[styles.quantityButton, { borderColor: theme.colors.border }]}
            onPress={() => setQuantity(quantity + 1)}
          >
            <Ionicons name="add" size={20} color={theme.colors.text} />
          </TouchableOpacity>
        </View>
      </View>

      {/* Description */}
      <View style={[styles.section, { backgroundColor: theme.colors.surface }]}>
        <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>Description</Text>
        <Text style={[styles.description, { color: theme.colors.textSecondary }]}>
          {product.description}
        </Text>
      </View>

      {/* Features */}
      {product.features && product.features.length > 0 && (
        <View style={[styles.section, { backgroundColor: theme.colors.surface }]}>
          <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>Features</Text>
          {product.features.map((feature, index) => (
            <View key={index} style={styles.featureItem}>
              <Ionicons name="checkmark-circle" size={16} color={theme.colors.success} />
              <Text style={[styles.featureText, { color: theme.colors.text }]}>
                {feature}
              </Text>
            </View>
          ))}
        </View>
      )}

      {/* Material & Care */}
      <View style={[styles.section, { backgroundColor: theme.colors.surface }]}>
        <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>Material & Care</Text>
        
        {product.material && (
          <View style={styles.detailItem}>
            <Text style={[styles.detailLabel, { color: theme.colors.textSecondary }]}>
              Material:
            </Text>
            <Text style={[styles.detailValue, { color: theme.colors.text }]}>
              {product.material}
            </Text>
          </View>
        )}

        {product.care && product.care.length > 0 && (
          <View style={styles.detailItem}>
            <Text style={[styles.detailLabel, { color: theme.colors.textSecondary }]}>
              Care Instructions:
            </Text>
            <View style={styles.careList}>
              {product.care.map((instruction, index) => (
                <Text key={index} style={[styles.careText, { color: theme.colors.text }]}>
                  • {instruction}
                </Text>
              ))}
            </View>
          </View>
        )}
      </View>

      {/* Action Buttons */}
      <View style={styles.actionContainer}>
        <TouchableOpacity 
          style={[styles.actionButton, styles.buyButton, { backgroundColor: theme.colors.primary }]}
          onPress={handleBuyNow}
          disabled={!product.inStock}
        >
          <Text style={styles.actionButtonText}>Buy Now</Text>
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={[styles.actionButton, styles.cartButton, { borderColor: theme.colors.primary }]}
          onPress={handleAddToCart}
          disabled={!product.inStock}
        >
          <Ionicons name="bag-outline" size={20} color={theme.colors.primary} />
          <Text style={[styles.cartButtonText, { color: theme.colors.primary }]}>
            Add to Cart
          </Text>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  centered: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
  },
  errorText: {
    fontSize: 16,
  },
  imageContainer: {
    position: 'relative',
    height: 300,
  },
  productImage: {
    width: 400,
    height: 300,
    backgroundColor: '#E5E7EB',
  },
  favoriteButton: {
    position: 'absolute',
    top: 16,
    right: 16,
    width: 44,
    height: 44,
    borderRadius: 22,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  infoContainer: {
    padding: 16,
    margin: 16,
    borderRadius: 12,
  },
  productName: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  vendorName: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
    marginBottom: 12,
  },
  rating: {
    fontSize: 14,
    fontWeight: '500',
  },
  price: {
    fontSize: 28,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  stockContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  stockText: {
    fontSize: 14,
    fontWeight: '500',
  },
  section: {
    margin: 16,
    padding: 16,
    borderRadius: 12,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 12,
  },
  optionsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  optionButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
  },
  optionText: {
    fontSize: 14,
    fontWeight: '500',
  },
  quantityContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 16,
  },
  quantityButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    borderWidth: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  quantityText: {
    fontSize: 18,
    fontWeight: 'bold',
    minWidth: 30,
    textAlign: 'center',
  },
  description: {
    fontSize: 14,
    lineHeight: 20,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginBottom: 8,
  },
  featureText: {
    fontSize: 14,
    flex: 1,
  },
  detailItem: {
    marginBottom: 12,
  },
  detailLabel: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 4,
  },
  detailValue: {
    fontSize: 14,
  },
  careList: {
    marginTop: 4,
  },
  careText: {
    fontSize: 14,
    marginBottom: 2,
  },
  actionContainer: {
    flexDirection: 'row',
    padding: 16,
    paddingBottom: 32,
    gap: 12,
  },
  actionButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    borderRadius: 12,
    gap: 8,
  },
  buyButton: {
    // Primary button styles
  },
  cartButton: {
    borderWidth: 2,
  },
  actionButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  cartButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
});
