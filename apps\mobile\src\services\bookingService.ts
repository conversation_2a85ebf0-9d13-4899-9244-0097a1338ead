import { generateClient } from 'aws-amplify/api';
import { getCurrentUser } from 'aws-amplify/auth';
import { createBooking, updateBooking } from '../graphql/mutations';
import { listBookings, getBooking } from '../graphql/queries';
import { AvailabilityService } from './availabilityService';

const client = generateClient();

export interface BookingInput {
  customerId: string;
  customerName: string;
  customerEmail: string;
  customerPhone: string;
  entityId: string;
  entityType: 'VENDOR' | 'VENUE';
  entityName: string;
  vendorId?: string;
  eventDate: string;
  eventTime: string;
  guestCount: number;
  eventType: string;
  duration?: string;
  specialRequests?: string;
  budget?: number;
  contactPreference: string;
}

export interface BookingResult {
  success: boolean;
  booking?: any;
  message: string;
  bookingId?: string;
}

export class BookingService {
  /**
   * Create a new booking
   */
  static async createBooking(bookingData: BookingInput): Promise<BookingResult> {
    try {
      // Check if user is authenticated
      const user = await getCurrentUser().catch(() => null);
      
      if (!user) {
        return {
          success: false,
          message: 'Please log in to create a booking'
        };
      }

      // First check availability
      const availability = await AvailabilityService.checkAvailability({
        entityId: bookingData.entityId,
        entityType: bookingData.entityType,
        eventDate: bookingData.eventDate,
        eventTime: bookingData.eventTime,
        duration: bookingData.duration
      });

      if (!availability.isAvailable) {
        return {
          success: false,
          message: `Not available: ${availability.message}`
        };
      }

      // Prepare booking input
      const bookingInput = {
        customerId: bookingData.customerId,
        customerName: bookingData.customerName,
        customerEmail: bookingData.customerEmail,
        customerPhone: bookingData.customerPhone,
        entityId: bookingData.entityId,
        entityType: bookingData.entityType,
        entityName: bookingData.entityName,
        vendorId: bookingData.vendorId,
        eventDate: bookingData.eventDate,
        eventTime: bookingData.eventTime,
        guestCount: bookingData.guestCount,
        eventType: bookingData.eventType,
        duration: bookingData.duration || '4 hours',
        specialRequests: bookingData.specialRequests || '',
        budget: bookingData.budget,
        contactPreference: bookingData.contactPreference,
        status: 'PENDING',
        priority: 'MEDIUM',
        notes: '',
        vendorNotes: '',
        paymentStatus: 'PENDING',
        contractSigned: false,
        reminderSent: false,
        communicationLog: [],
        attachments: [],
        metadata: JSON.stringify({
          source: 'mobile_app',
          timestamp: new Date().toISOString()
        })
      };

      // Create booking in database
      const result = await client.graphql({
        query: createBooking,
        variables: { input: bookingInput },
        authMode: 'userPool'
      });

      return {
        success: true,
        booking: result.data.createBooking,
        bookingId: result.data.createBooking.id,
        message: 'Booking created successfully'
      };

    } catch (error: any) {
      console.error('Booking creation error:', error);
      
      // Handle authorization errors gracefully
      if (error.message?.includes('authorization') || error.message?.includes('Unauthorized')) {
        return {
          success: false,
          message: 'Authentication required. Please log in and try again.'
        };
      }
      
      return {
        success: false,
        message: `Failed to create booking: ${error.message}`
      };
    }
  }

  /**
   * Get user's bookings
   */
  static async getUserBookings(userId: string, limit: number = 20): Promise<{
    success: boolean;
    bookings: any[];
    message: string;
  }> {
    try {
      const user = await getCurrentUser().catch(() => null);
      
      if (!user) {
        return {
          success: false,
          bookings: [],
          message: 'Please log in to view bookings'
        };
      }

      const result = await client.graphql({
        query: listBookings,
        variables: {
          filter: {
            customerId: { eq: userId }
          },
          limit,
          sortDirection: 'DESC'
        },
        authMode: 'userPool'
      });

      return {
        success: true,
        bookings: result.data.listBookings.items || [],
        message: 'Bookings retrieved successfully'
      };

    } catch (error: any) {
      console.error('Get bookings error:', error);
      
      if (error.message?.includes('authorization') || error.message?.includes('Unauthorized')) {
        return {
          success: false,
          bookings: [],
          message: 'Authentication required to view bookings'
        };
      }
      
      return {
        success: false,
        bookings: [],
        message: `Failed to fetch bookings: ${error.message}`
      };
    }
  }

  /**
   * Update booking status
   */
  static async updateBookingStatus(
    bookingId: string, 
    status: string, 
    notes?: string
  ): Promise<BookingResult> {
    try {
      const user = await getCurrentUser().catch(() => null);
      
      if (!user) {
        return {
          success: false,
          message: 'Please log in to update booking'
        };
      }

      const updateInput = {
        id: bookingId,
        status,
        notes: notes || '',
        updatedAt: new Date().toISOString()
      };

      const result = await client.graphql({
        query: updateBooking,
        variables: { input: updateInput },
        authMode: 'userPool'
      });

      return {
        success: true,
        booking: result.data.updateBooking,
        message: `Booking ${status.toLowerCase()} successfully`
      };

    } catch (error: any) {
      console.error('Update booking error:', error);
      
      if (error.message?.includes('authorization') || error.message?.includes('Unauthorized')) {
        return {
          success: false,
          message: 'Authentication required to update booking'
        };
      }
      
      return {
        success: false,
        message: `Failed to update booking: ${error.message}`
      };
    }
  }

  /**
   * Cancel booking
   */
  static async cancelBooking(
    bookingId: string, 
    reason: string
  ): Promise<BookingResult> {
    try {
      const updateInput = {
        id: bookingId,
        status: 'CANCELLED',
        cancellationReason: reason,
        cancellationDate: new Date().toISOString()
      };

      const result = await client.graphql({
        query: updateBooking,
        variables: { input: updateInput },
        authMode: 'userPool'
      });

      return {
        success: true,
        booking: result.data.updateBooking,
        message: 'Booking cancelled successfully'
      };

    } catch (error: any) {
      console.error('Cancel booking error:', error);
      
      if (error.message?.includes('authorization') || error.message?.includes('Unauthorized')) {
        return {
          success: false,
          message: 'Authentication required to cancel booking'
        };
      }
      
      return {
        success: false,
        message: `Failed to cancel booking: ${error.message}`
      };
    }
  }

  /**
   * Get booking details
   */
  static async getBookingDetails(bookingId: string): Promise<{
    success: boolean;
    booking?: any;
    message: string;
  }> {
    try {
      const user = await getCurrentUser().catch(() => null);
      
      if (!user) {
        return {
          success: false,
          message: 'Please log in to view booking details'
        };
      }

      const result = await client.graphql({
        query: getBooking,
        variables: { id: bookingId },
        authMode: 'userPool'
      });

      return {
        success: true,
        booking: result.data.getBooking,
        message: 'Booking details retrieved successfully'
      };

    } catch (error: any) {
      console.error('Get booking details error:', error);
      
      if (error.message?.includes('authorization') || error.message?.includes('Unauthorized')) {
        return {
          success: false,
          message: 'Authentication required to view booking details'
        };
      }
      
      return {
        success: false,
        message: `Failed to fetch booking details: ${error.message}`
      };
    }
  }
}
