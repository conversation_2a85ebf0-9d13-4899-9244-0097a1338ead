import { Page, expect } from '@playwright/test';
import { TestHelpers } from './test-helpers';

export class BookingHelpers extends TestHelpers {
  constructor(page: Page) {
    super(page);
  }

  /**
   * Navigate to venue booking page
   */
  async navigateToVenueBooking(venueId?: string) {
    if (venueId) {
      await this.navigateTo(`/booking/venue/${venueId}`);
    } else {
      // Navigate to venues page and select first venue
      await this.navigateTo('/venues');
      await this.waitForElement('.venue-card, [data-testid="venue-card"]');
      
      const venueCards = this.page.locator('.venue-card, [data-testid="venue-card"]');
      const firstVenue = venueCards.first();
      
      // Look for book now button
      const bookButtons = firstVenue.locator('button:has-text("Book"), a:has-text("Book"), [data-testid="book-button"]');
      if (await bookButtons.count() > 0) {
        await bookButtons.first().click();
      } else {
        // Click on venue card to go to details, then book
        await firstVenue.click();
        await this.waitForElement('button:has-text("Book"), [data-testid="book-button"]');
        await this.clickElement('button:has-text("Book"), [data-testid="book-button"]');
      }
    }
    
    await this.waitForPageLoad();
  }

  /**
   * Navigate to vendor booking page
   */
  async navigateToVendorBooking(vendorId?: string) {
    if (vendorId) {
      await this.navigateTo(`/booking/vendor/${vendorId}`);
    } else {
      // Navigate to vendors page and select first vendor
      await this.navigateTo('/vendors');
      await this.waitForElement('.vendor-card, [data-testid="vendor-card"]');
      
      const vendorCards = this.page.locator('.vendor-card, [data-testid="vendor-card"]');
      const firstVendor = vendorCards.first();
      
      // Look for book now button
      const bookButtons = firstVendor.locator('button:has-text("Book"), a:has-text("Book"), [data-testid="book-button"]');
      if (await bookButtons.count() > 0) {
        await bookButtons.first().click();
      } else {
        // Click on vendor card to go to details, then book
        await firstVendor.click();
        await this.waitForElement('button:has-text("Book"), [data-testid="book-button"]');
        await this.clickElement('button:has-text("Book"), [data-testid="book-button"]');
      }
    }
    
    await this.waitForPageLoad();
  }

  /**
   * Fill booking form with test data
   */
  async fillBookingForm(bookingData: {
    eventDate: string;
    eventTime?: string;
    guestCount: number;
    eventType?: string;
    specialRequests?: string;
    contactName?: string;
    contactEmail?: string;
    contactPhone?: string;
  }) {
    // Fill event date
    const dateInput = this.page.locator('input[type="date"], input[name="eventDate"], [data-testid="event-date"]');
    if (await dateInput.isVisible()) {
      await dateInput.fill(bookingData.eventDate);
    }

    // Fill event time if available
    if (bookingData.eventTime) {
      const timeInput = this.page.locator('input[type="time"], select[name="eventTime"], [data-testid="event-time"]');
      if (await timeInput.isVisible()) {
        await timeInput.fill(bookingData.eventTime);
      }
    }

    // Fill guest count
    const guestInput = this.page.locator('input[name="guestCount"], input[placeholder*="guest"], [data-testid="guest-count"]');
    if (await guestInput.isVisible()) {
      await guestInput.fill(bookingData.guestCount.toString());
    }

    // Fill event type if available
    if (bookingData.eventType) {
      const eventTypeSelect = this.page.locator('select[name="eventType"], [data-testid="event-type"]');
      if (await eventTypeSelect.isVisible()) {
        await eventTypeSelect.selectOption(bookingData.eventType);
      }
    }

    // Fill special requests if available
    if (bookingData.specialRequests) {
      const requestsTextarea = this.page.locator('textarea[name="specialRequests"], textarea[placeholder*="special"], [data-testid="special-requests"]');
      if (await requestsTextarea.isVisible()) {
        await requestsTextarea.fill(bookingData.specialRequests);
      }
    }

    // Fill contact information if available
    if (bookingData.contactName) {
      const nameInput = this.page.locator('input[name="contactName"], input[placeholder*="name"], [data-testid="contact-name"]');
      if (await nameInput.isVisible()) {
        await nameInput.fill(bookingData.contactName);
      }
    }

    if (bookingData.contactEmail) {
      const emailInput = this.page.locator('input[name="contactEmail"], input[type="email"], [data-testid="contact-email"]');
      if (await emailInput.isVisible()) {
        await emailInput.fill(bookingData.contactEmail);
      }
    }

    if (bookingData.contactPhone) {
      const phoneInput = this.page.locator('input[name="contactPhone"], input[type="tel"], [data-testid="contact-phone"]');
      if (await phoneInput.isVisible()) {
        await phoneInput.fill(bookingData.contactPhone);
      }
    }
  }

  /**
   * Check availability for selected date
   */
  async checkAvailability() {
    const checkButtons = [
      'button:has-text("Check Availability")',
      'button:has-text("Check")',
      '[data-testid="check-availability"]'
    ];

    for (const selector of checkButtons) {
      if (await this.elementExists(selector)) {
        await this.clickElement(selector);
        await this.waitForLoadingToComplete();
        break;
      }
    }
  }

  /**
   * Submit booking form
   */
  async submitBooking() {
    const submitButtons = [
      'button[type="submit"]',
      'button:has-text("Book Now")',
      'button:has-text("Submit Booking")',
      'button:has-text("Confirm Booking")',
      '[data-testid="submit-booking"]'
    ];

    for (const selector of submitButtons) {
      if (await this.elementExists(selector)) {
        await this.clickElement(selector);
        await this.waitForLoadingToComplete();
        break;
      }
    }
  }

  /**
   * Verify booking confirmation
   */
  async verifyBookingConfirmation(): Promise<boolean> {
    const confirmationIndicators = [
      'text=Booking Confirmed',
      'text=Booking Successful',
      'text=Thank you for your booking',
      'text=Booking ID',
      '[data-testid="booking-confirmation"]'
    ];

    for (const indicator of confirmationIndicators) {
      if (await this.elementExists(indicator)) {
        return true;
      }
    }

    // Check if redirected to confirmation page
    const currentUrl = this.page.url();
    if (currentUrl.includes('/confirmation') || currentUrl.includes('/success')) {
      return true;
    }

    return false;
  }

  /**
   * Get booking ID from confirmation
   */
  async getBookingId(): Promise<string | null> {
    const bookingIdSelectors = [
      '[data-testid="booking-id"]',
      'text=Booking ID',
      'text=Reference'
    ];

    for (const selector of bookingIdSelectors) {
      if (await this.elementExists(selector)) {
        const element = this.page.locator(selector);
        const text = await element.textContent();
        
        // Extract ID from text like "Booking ID: 12345"
        const match = text?.match(/\d+/);
        if (match) {
          return match[0];
        }
      }
    }

    return null;
  }

  /**
   * Generate test booking data
   */
  generateBookingData() {
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    const eventDate = tomorrow.toISOString().split('T')[0]; // YYYY-MM-DD format

    const testData = this.generateTestData();

    return {
      eventDate,
      eventTime: '18:00',
      guestCount: Math.floor(Math.random() * 200) + 50, // 50-250 guests
      eventType: 'Wedding',
      specialRequests: 'Please arrange for vegetarian catering options',
      contactName: `${testData.firstName} ${testData.lastName}`,
      contactEmail: testData.email,
      contactPhone: testData.phone
    };
  }

  /**
   * Verify form validation errors
   */
  async verifyValidationErrors(expectedErrors: string[]): Promise<boolean> {
    for (const error of expectedErrors) {
      const errorFound = await this.elementExists(`text=${error}`) ||
                        await this.elementExists(`.error:has-text("${error}")`) ||
                        await this.elementExists(`[role="alert"]:has-text("${error}")`);
      
      if (!errorFound) {
        return false;
      }
    }
    return true;
  }

  /**
   * Check if date is available
   */
  async isDateAvailable(date: string): Promise<boolean> {
    // Fill the date
    const dateInput = this.page.locator('input[type="date"], input[name="eventDate"]');
    if (await dateInput.isVisible()) {
      await dateInput.fill(date);
    }

    // Check availability
    await this.checkAvailability();

    // Look for availability indicators
    const availableIndicators = [
      'text=Available',
      'text=Date is available',
      '.available',
      '[data-testid="date-available"]'
    ];

    const unavailableIndicators = [
      'text=Not Available',
      'text=Date is not available',
      'text=Booked',
      '.unavailable',
      '[data-testid="date-unavailable"]'
    ];

    // Check for unavailable first
    for (const indicator of unavailableIndicators) {
      if (await this.elementExists(indicator)) {
        return false;
      }
    }

    // Check for available
    for (const indicator of availableIndicators) {
      if (await this.elementExists(indicator)) {
        return true;
      }
    }

    // If no clear indicator, assume available
    return true;
  }

  /**
   * Cancel booking if possible
   */
  async cancelBooking() {
    const cancelButtons = [
      'button:has-text("Cancel")',
      'button:has-text("Cancel Booking")',
      '[data-testid="cancel-booking"]'
    ];

    for (const selector of cancelButtons) {
      if (await this.elementExists(selector)) {
        await this.clickElement(selector);
        
        // Handle confirmation dialog if present
        const confirmButtons = [
          'button:has-text("Yes")',
          'button:has-text("Confirm")',
          'button:has-text("OK")'
        ];

        for (const confirmSelector of confirmButtons) {
          if (await this.elementExists(confirmSelector)) {
            await this.clickElement(confirmSelector);
            break;
          }
        }

        await this.waitForLoadingToComplete();
        break;
      }
    }
  }
}
