/**
 * Email Service Test Suite
 * Tests all email functionality including authorization and error handling
 */

import { emailService } from '../lib/services/emailService';
import { generateClient } from '@aws-amplify/api';

// Mock AWS Amplify client
jest.mock('@aws-amplify/api', () => ({
  generateClient: jest.fn()
}));

// Mock GraphQL client
const mockGraphQL = jest.fn();
const mockClient = {
  graphql: mockGraphQL
};

describe('EmailService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (generateClient as jest.Mock).mockReturnValue(mockClient);
  });

  describe('sendLoginOTPEmail', () => {
    it('should send login OTP email successfully', async () => {
      const mockResponse = {
        data: {
          sendEmail: {
            success: true,
            messageId: 'msg_123',
            emailLogId: 'log_456'
          }
        }
      };

      mockGraphQL.mockResolvedValue(mockResponse);

      const result = await emailService.sendLoginOTPEmail({
        email: '<EMAIL>',
        userName: 'TestUser',
        otp: '123456',
        expiryMinutes: 10
      });

      expect(result).toBe(true);
      expect(mockGraphQL).toHaveBeenCalledWith({
        query: expect.stringContaining('SendEmail'),
        variables: {
          input: {
            emailType: 'LOGIN_OTP',
            recipient: '<EMAIL>',
            templateData: expect.stringContaining('123456'),
            userId: 'TestUser'
          }
        }
      });
    });

    it('should handle GraphQL errors gracefully', async () => {
      mockGraphQL.mockRejectedValue(new Error('GraphQL Error'));

      const result = await emailService.sendLoginOTPEmail({
        email: '<EMAIL>',
        userName: 'TestUser',
        otp: '123456'
      });

      expect(result).toBe(false);
    });
  });

  describe('sendUserWelcomeEmail', () => {
    it('should send user welcome email successfully', async () => {
      const mockResponse = {
        data: {
          createEmailLog: {
            id: 'log_123',
            messageId: 'msg_456',
            emailType: 'USER_WELCOME',
            recipient: '<EMAIL>',
            status: 'SENT'
          }
        }
      };

      mockGraphQL.mockResolvedValue(mockResponse);

      const result = await emailService.sendUserWelcomeEmail({
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        type: 'signup',
        isVendor: false
      });

      expect(result).toBe(true);
      expect(mockGraphQL).toHaveBeenCalledWith({
        query: expect.stringContaining('CreateEmailLog'),
        variables: {
          input: {
            id: expect.stringMatching(/log_\d+_.+/),
            messageId: expect.stringMatching(/msg_\d+_.+/),
            emailType: 'USER_WELCOME',
            recipient: '<EMAIL>',
            subject: expect.stringContaining('Welcome'),
            status: 'SENT',
            templateData: expect.stringContaining('John'),
            sentAt: expect.any(String),
            userId: '<EMAIL>'
          }
        }
      });
    });

    it('should handle missing user data gracefully', async () => {
      const mockResponse = {
        data: {
          createEmailLog: null
        }
      };

      mockGraphQL.mockResolvedValue(mockResponse);

      const result = await emailService.sendUserWelcomeEmail({
        email: '<EMAIL>',
        type: 'signup'
      });

      expect(result).toBe(false);
    });
  });

  describe('sendNewsletterWelcomeEmail', () => {
    it('should send newsletter welcome email successfully', async () => {
      const mockResponse = {
        data: {
          createEmailLog: {
            id: 'log_123',
            messageId: 'msg_456',
            emailType: 'NEWSLETTER_SUBSCRIPTION',
            recipient: '<EMAIL>',
            status: 'SENT'
          }
        }
      };

      mockGraphQL.mockResolvedValue(mockResponse);

      const result = await emailService.sendNewsletterWelcomeEmail({
        email: '<EMAIL>',
        firstName: 'Jane',
        lastName: 'Smith',
        type: 'newsletter',
        interests: ['photography', 'decorations'],
        preferences: {
          weddingTips: true,
          vendorRecommendations: true,
          frequency: 'weekly'
        }
      });

      expect(result).toBe(true);
      expect(mockGraphQL).toHaveBeenCalledWith({
        query: expect.stringContaining('CreateEmailLog'),
        variables: {
          input: {
            id: expect.stringMatching(/log_\d+_.+/),
            messageId: expect.stringMatching(/msg_\d+_.+/),
            emailType: 'NEWSLETTER_SUBSCRIPTION',
            recipient: '<EMAIL>',
            subject: expect.stringContaining('Welcome'),
            status: 'SENT',
            templateData: expect.stringContaining('Jane'),
            sentAt: expect.any(String),
            userId: '<EMAIL>'
          }
        }
      });
    });
  });

  describe('sendBookingConfirmationEmail', () => {
    it('should send booking confirmation email successfully', async () => {
      const mockResponse = {
        data: {
          createEmailLog: {
            id: 'log_123',
            messageId: 'msg_456',
            emailType: 'BOOKING_CONFIRMATION',
            recipient: '<EMAIL>',
            status: 'SENT'
          }
        }
      };

      mockGraphQL.mockResolvedValue(mockResponse);

      const result = await emailService.sendBookingConfirmationEmail({
        email: '<EMAIL>',
        userName: 'TestUser',
        bookingId: 'booking_123',
        entityName: 'Dream Venue',
        entityType: 'venue',
        eventDate: '2024-12-25',
        eventTime: '6:00 PM',
        amount: '₹50000',
        status: 'confirmed',
        trackingUrl: 'https://example.com/track'
      });

      expect(result).toBe(true);
      expect(mockGraphQL).toHaveBeenCalledWith({
        query: expect.stringContaining('CreateEmailLog'),
        variables: {
          input: {
            id: expect.stringMatching(/log_\d+_.+/),
            messageId: expect.stringMatching(/msg_\d+_.+/),
            emailType: 'BOOKING_CONFIRMATION',
            recipient: '<EMAIL>',
            subject: expect.stringContaining('Dream Venue'),
            status: 'SENT',
            templateData: expect.stringContaining('booking_123'),
            sentAt: expect.any(String),
            userId: 'TestUser',
            bookingId: 'booking_123'
          }
        },
        authMode: 'userPool'
      });
    });

    it('should handle missing optional fields', async () => {
      const mockResponse = {
        data: {
          createEmailLog: {
            id: 'log_123',
            messageId: 'msg_456',
            emailType: 'BOOKING_CONFIRMATION',
            recipient: '<EMAIL>',
            status: 'SENT'
          }
        }
      };

      mockGraphQL.mockResolvedValue(mockResponse);

      const result = await emailService.sendBookingConfirmationEmail({
        email: '<EMAIL>',
        userName: 'TestUser',
        bookingId: 'booking_123',
        entityName: 'Test Vendor',
        entityType: 'vendor',
        eventDate: '2024-12-25',
        status: 'pending'
      });

      expect(result).toBe(true);
    });
  });

  describe('sendPaymentSuccessEmail', () => {
    it('should send payment success email successfully', async () => {
      const mockResponse = {
        data: {
          sendEmail: {
            success: true,
            messageId: 'msg_123',
            emailLogId: 'log_456'
          }
        }
      };

      mockGraphQL.mockResolvedValue(mockResponse);

      const result = await emailService.sendPaymentSuccessEmail({
        email: '<EMAIL>',
        userName: 'TestUser',
        orderId: 'order_123',
        amount: '₹25000',
        items: [
          { name: 'Wedding Dress', quantity: 1, price: '₹25000' }
        ],
        invoiceUrl: 'https://example.com/invoice.pdf',
        trackingUrl: 'https://example.com/track'
      });

      expect(result).toBe(true);
      expect(mockGraphQL).toHaveBeenCalledWith({
        query: expect.stringContaining('SendEmail'),
        variables: {
          input: {
            emailType: 'PAYMENT_SUCCESS',
            recipient: '<EMAIL>',
            templateData: expect.stringContaining('order_123'),
            userId: 'TestUser',
            orderId: 'order_123'
          }
        },
        authMode: 'userPool'
      });
    });
  });

  describe('sendWeeklyNewsEmail', () => {
    it('should send weekly news email successfully', async () => {
      const mockResponse = {
        data: {
          sendEmail: {
            success: true,
            messageId: 'msg_123',
            emailLogId: 'log_456'
          }
        }
      };

      mockGraphQL.mockResolvedValue(mockResponse);

      const result = await emailService.sendWeeklyNewsEmail({
        email: '<EMAIL>',
        userName: 'TestUser',
        articles: [
          {
            title: 'Top Wedding Trends 2024',
            excerpt: 'Discover the latest wedding trends',
            url: 'https://example.com/trends'
          }
        ],
        offers: [
          {
            title: 'Special Discount',
            discount: '20% OFF',
            url: 'https://example.com/offer'
          }
        ]
      });

      expect(result).toBe(true);
      expect(mockGraphQL).toHaveBeenCalledWith({
        query: expect.stringContaining('SendEmail'),
        variables: {
          input: {
            emailType: 'WEEKLY_NEWS',
            recipient: '<EMAIL>',
            templateData: expect.stringContaining('Top Wedding Trends'),
            userId: 'TestUser'
          }
        },
        authMode: 'userPool'
      });
    });
  });

  describe('sendOffersEmail', () => {
    it('should send offers email successfully', async () => {
      const mockResponse = {
        data: {
          sendEmail: {
            success: true,
            messageId: 'msg_123',
            emailLogId: 'log_456'
          }
        }
      };

      mockGraphQL.mockResolvedValue(mockResponse);

      const result = await emailService.sendOffersEmail({
        email: '<EMAIL>',
        userName: 'TestUser',
        offers: [
          {
            title: 'Wedding Photography Package',
            description: 'Professional wedding photography',
            discount: '15% OFF',
            validUntil: '2024-12-31',
            url: 'https://example.com/photo-offer'
          }
        ]
      });

      expect(result).toBe(true);
      expect(mockGraphQL).toHaveBeenCalledWith({
        query: expect.stringContaining('SendEmail'),
        variables: {
          input: {
            emailType: 'OFFERS_MAIL',
            recipient: '<EMAIL>',
            templateData: expect.stringContaining('Wedding Photography'),
            userId: 'TestUser'
          }
        },
        authMode: 'userPool'
      });
    });
  });

  describe('sendFavoritesNotificationEmail', () => {
    it('should send favorites notification email successfully', async () => {
      const mockResponse = {
        data: {
          sendEmail: {
            success: true,
            messageId: 'msg_123',
            emailLogId: 'log_456'
          }
        }
      };

      mockGraphQL.mockResolvedValue(mockResponse);

      const result = await emailService.sendFavoritesNotificationEmail({
        email: '<EMAIL>',
        userName: 'TestUser',
        updates: [
          {
            name: 'Dream Venue',
            type: 'venue',
            updateType: 'price_drop',
            url: 'https://example.com/venue'
          }
        ]
      });

      expect(result).toBe(true);
      expect(mockGraphQL).toHaveBeenCalledWith({
        query: expect.stringContaining('SendEmail'),
        variables: {
          input: {
            emailType: 'FAVORITES_NOTIFICATION',
            recipient: '<EMAIL>',
            templateData: expect.stringContaining('Dream Venue'),
            userId: 'TestUser'
          }
        },
        authMode: 'userPool'
      });
    });
  });

  describe('sendVendorLaunchEmail', () => {
    it('should send vendor launch email successfully', async () => {
      const mockResponse = {
        data: {
          sendEmail: {
            success: true,
            messageId: 'msg_123',
            emailLogId: 'log_456'
          }
        }
      };

      mockGraphQL.mockResolvedValue(mockResponse);

      const result = await emailService.sendVendorLaunchEmail({
        email: '<EMAIL>',
        userName: 'TestUser',
        newVendors: [
          {
            name: 'New Photography Studio',
            category: 'Photography',
            city: 'Chennai',
            discount: '10% OFF',
            url: 'https://example.com/studio'
          }
        ]
      });

      expect(result).toBe(true);
      expect(mockGraphQL).toHaveBeenCalledWith({
        query: expect.stringContaining('SendEmail'),
        variables: {
          input: {
            emailType: 'VENDOR_LAUNCH',
            recipient: '<EMAIL>',
            templateData: expect.stringContaining('New Photography Studio'),
            userId: 'TestUser'
          }
        },
        authMode: 'userPool'
      });
    });
  });

  describe('sendAvailabilityCheckEmail', () => {
    it('should send availability check email successfully', async () => {
      const mockResponse = {
        data: {
          sendEmail: {
            success: true,
            messageId: 'msg_123',
            emailLogId: 'log_456'
          }
        }
      };

      mockGraphQL.mockResolvedValue(mockResponse);

      const result = await emailService.sendAvailabilityCheckEmail({
        email: '<EMAIL>',
        userName: 'TestUser',
        entityName: 'Dream Venue',
        entityType: 'venue',
        requestedDate: '2024-12-25',
        status: 'available',
        alternativeDates: ['2024-12-26', '2024-12-27'],
        url: 'https://example.com/venue'
      });

      expect(result).toBe(true);
      expect(mockGraphQL).toHaveBeenCalledWith({
        query: expect.stringContaining('SendEmail'),
        variables: {
          input: {
            emailType: 'AVAILABILITY_CHECK',
            recipient: '<EMAIL>',
            templateData: expect.stringContaining('Dream Venue'),
            userId: 'TestUser'
          }
        },
        authMode: 'userPool'
      });
    });
  });

  describe('sendBulkEmails', () => {
    it('should send bulk emails successfully', async () => {
      const mockResponse = {
        data: {
          sendBulkEmail: {
            success: true,
            sentCount: 2,
            failedCount: 0,
            errors: [],
            emailLogIds: ['log_1', 'log_2']
          }
        }
      };

      mockGraphQL.mockResolvedValue(mockResponse);

      const result = await emailService.sendBulkEmails(
        'WEEKLY_NEWS',
        ['<EMAIL>', '<EMAIL>'],
        { message: 'Test bulk email' }
      );

      expect(result).toBe(true);
      expect(mockGraphQL).toHaveBeenCalledWith({
        query: expect.stringContaining('SendBulkEmail'),
        variables: {
          input: {
            emailType: 'WEEKLY_NEWS',
            recipients: ['<EMAIL>', '<EMAIL>'],
            templateData: expect.stringContaining('Test bulk email')
          }
        },
        authMode: 'userPool'
      });
    });

    it('should handle bulk email failures', async () => {
      const mockResponse = {
        data: {
          sendBulkEmail: {
            success: false,
            sentCount: 0,
            failedCount: 2,
            errors: ['Invalid email 1', 'Invalid email 2'],
            emailLogIds: []
          }
        }
      };

      mockGraphQL.mockResolvedValue(mockResponse);

      const result = await emailService.sendBulkEmails(
        'WEEKLY_NEWS',
        ['invalid1@', 'invalid2@'],
        { message: 'Test bulk email' }
      );

      expect(result).toBe(false);
    });
  });

  describe('Error Handling', () => {
    it('should handle GraphQL network errors', async () => {
      mockGraphQL.mockRejectedValue(new Error('Network error'));

      const result = await emailService.sendLoginOTPEmail({
        email: '<EMAIL>',
        userName: 'TestUser',
        otp: '123456'
      });

      expect(result).toBe(false);
    });

    it('should handle malformed GraphQL responses', async () => {
      mockGraphQL.mockResolvedValue({
        data: null
      });

      const result = await emailService.sendLoginOTPEmail({
        email: '<EMAIL>',
        userName: 'TestUser',
        otp: '123456'
      });

      expect(result).toBe(false);
    });

    it('should handle missing success field in response', async () => {
      mockGraphQL.mockResolvedValue({
        data: {
          sendEmail: {
            messageId: 'msg_123'
            // Missing success field
          }
        }
      });

      const result = await emailService.sendLoginOTPEmail({
        email: '<EMAIL>',
        userName: 'TestUser',
        otp: '123456'
      });

      expect(result).toBe(false);
    });
  });

  describe('Template Generation', () => {
    it('should generate proper email templates', () => {
      // Test that the service can generate templates without errors
      const loginTemplate = (emailService as any).generateLoginOTPTemplate({
        email: '<EMAIL>',
        userName: 'TestUser',
        otp: '123456'
      });

      expect(loginTemplate.subject).toContain('123456');
      expect(loginTemplate.htmlContent).toContain('TestUser');
      expect(loginTemplate.textContent).toContain('123456');

      const bookingTemplate = (emailService as any).generateBookingConfirmationTemplate({
        email: '<EMAIL>',
        userName: 'TestUser',
        bookingId: 'booking_123',
        entityName: 'Test Venue',
        entityType: 'venue',
        eventDate: '2024-12-25',
        status: 'confirmed'
      });

      expect(bookingTemplate.subject).toContain('Test Venue');
      expect(bookingTemplate.htmlContent).toContain('booking_123');
      expect(bookingTemplate.textContent).toContain('Test Venue');
    });
  });

  describe('Authentication Mode', () => {
    it('should use userPool auth mode for all mutations', async () => {
      const mockResponse = {
        data: {
          createEmailLog: {
            id: 'log_123',
            messageId: 'msg_456',
            emailType: 'BOOKING_CONFIRMATION',
            recipient: '<EMAIL>',
            status: 'SENT'
          }
        }
      };

      mockGraphQL.mockResolvedValue(mockResponse);

      await emailService.sendBookingConfirmationEmail({
        email: '<EMAIL>',
        userName: 'TestUser',
        bookingId: 'booking_123',
        entityName: 'Test Venue',
        entityType: 'venue',
        eventDate: '2024-12-25',
        status: 'confirmed'
      });

      // Verify that authMode: 'userPool' is used
      expect(mockGraphQL).toHaveBeenCalledWith(
        expect.objectContaining({
          authMode: 'userPool'
        })
      );
    });
  });
}); 