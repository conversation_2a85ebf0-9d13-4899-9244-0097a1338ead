"use client"

import { useState } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import DataSeeder from '@/components/admin/DataSeeder'
import { 
  Database, 
  Settings, 
  Shield, 
  Users,
  AlertTriangle,
  Info
} from 'lucide-react'

export default function AdminToolsPage() {
  const { isAuthenticated, user, userType, isVendor, isAdmin, isSuperAdmin } = useAuth()

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardContent className="pt-6">
            <div className="text-center">
              <Shield className="w-12 h-12 text-red-500 mx-auto mb-4" />
              <h2 className="text-2xl font-bold text-gray-900 mb-2">Access Denied</h2>
              <p className="text-gray-600">You need to be logged in to access admin tools.</p>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="space-y-6 sm:space-y-8">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-2">
        <div>
          <h1 className="text-xl sm:text-2xl lg:text-3xl font-semibold text-gray-900 tracking-tight">
            {isVendor ? 'Business Tools' : 'Admin Tools'}
          </h1>
          <p className="text-gray-500 mt-1 text-xs sm:text-sm">
            {isVendor
              ? 'Business management tools and utilities for your Festive Services'
              : 'Administrative tools and utilities for managing the platform'
            }
          </p>
        </div>
        <div className="flex items-center gap-2 bg-gray-100 rounded-full px-3 sm:px-4 py-2 text-xs sm:text-sm text-gray-700">
          <Users className="w-3 h-3 sm:w-4 sm:h-4" />
          <span className="capitalize">{userType || 'User'}</span> · {user?.email || 'N/A'}
        </div>
      </div>

      {/* Warning Notice */}
      <div className="flex items-start gap-2 bg-orange-50 border border-orange-100 rounded-md px-3 sm:px-4 py-2 text-xs sm:text-sm text-orange-800">
        <AlertTriangle className="w-4 h-4 text-orange-500 mt-0.5 flex-shrink-0" />
        <span>
          {isVendor
            ? 'These tools help you manage your business data and test your services. Use the data seeder to populate sample content for testing.'
            : 'These tools can modify your database and application data. Use with caution and ensure you have proper backups.'
          }
        </span>
      </div>

      {/* Admin Tools Tabs */}
      <Tabs defaultValue="seeder" className="space-y-4 sm:space-y-6">
        <TabsList className="flex w-full bg-gray-50 rounded-full p-1 mb-4 gap-1 sm:gap-2">
          <TabsTrigger value="seeder" className="flex-1 rounded-full data-[state=active]:bg-white data-[state=active]:shadow-sm data-[state=active]:text-primary text-xs sm:text-sm">
            <Database className="w-3 h-3 sm:w-4 sm:h-4 mr-1 sm:mr-2" />
            <span className="hidden sm:inline">Data Seeder</span>
            <span className="sm:hidden">Seeder</span>
          </TabsTrigger>
          <TabsTrigger value="maintenance" className="flex-1 rounded-full data-[state=active]:bg-white data-[state=active]:shadow-sm data-[state=active]:text-primary text-xs sm:text-sm">
            <Settings className="w-3 h-3 sm:w-4 sm:h-4 mr-1 sm:mr-2" />
            <span className="hidden sm:inline">Maintenance</span>
            <span className="sm:hidden">Tools</span>
          </TabsTrigger>
          <TabsTrigger value="info" className="flex-1 rounded-full data-[state=active]:bg-white data-[state=active]:shadow-sm data-[state=active]:text-primary text-xs sm:text-sm">
            <Info className="w-3 h-3 sm:w-4 sm:h-4 mr-1 sm:mr-2" />
            <span className="hidden sm:inline">System Info</span>
            <span className="sm:hidden">Info</span>
          </TabsTrigger>
        </TabsList>

        {/* Data Seeder Tab */}
        <TabsContent value="seeder">
          <div className="bg-white rounded-xl shadow-sm p-4 sm:p-6">
            <div className="mb-4">
              <div className="flex items-center gap-2 text-base sm:text-lg font-semibold text-gray-900 mb-1">
                <Database className="w-4 h-4 sm:w-5 sm:h-5" />
                {isVendor ? 'Sample Data Generator' : 'Test Data Seeder'}
              </div>
              <p className="text-gray-500 text-xs sm:text-sm">
                {isVendor
                  ? 'Generate sample products, services, and content to showcase your business offerings and test your listings.'
                  : 'Populate your database with sample products, vendors, and venues for testing purposes.'
                }
              </p>
            </div>
            <DataSeeder />
          </div>
        </TabsContent>

        {/* Maintenance Tab */}
        <TabsContent value="maintenance">
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6">
            <div className="bg-white rounded-xl shadow-sm p-4 sm:p-6 flex flex-col items-center text-center">
              <Database className="w-6 h-6 sm:w-8 sm:h-8 text-blue-600 mb-2 sm:mb-3" />
              <h3 className="font-semibold text-blue-900 mb-2 text-sm sm:text-base">
                {isVendor ? 'Data Management' : 'Database Cleanup'}
              </h3>
              <p className="text-xs sm:text-sm text-blue-700 mb-3 sm:mb-4">
                {isVendor
                  ? 'Manage your business listings, products, and service data efficiently.'
                  : 'Clean up orphaned records and optimize database performance.'
                }
              </p>
              <Button variant="outline" className="w-full text-xs sm:text-sm" disabled>
                Coming Soon
              </Button>
            </div>
            <div className="bg-white rounded-xl shadow-sm p-4 sm:p-6 flex flex-col items-center text-center">
              <Shield className="w-6 h-6 sm:w-8 sm:h-8 text-green-600 mb-2 sm:mb-3" />
              <h3 className="font-semibold text-green-900 mb-2 text-sm sm:text-base">Cache Management</h3>
              <p className="text-xs sm:text-sm text-green-700 mb-3 sm:mb-4">
                Clear application cache and refresh static content.
              </p>
              <Button variant="outline" className="w-full text-xs sm:text-sm" disabled>
                Coming Soon
              </Button>
            </div>
            <div className="bg-white rounded-xl shadow-sm p-4 sm:p-6 flex flex-col items-center text-center">
              <Users className="w-6 h-6 sm:w-8 sm:h-8 text-purple-600 mb-2 sm:mb-3" />
              <h3 className="font-semibold text-purple-900 mb-2 text-sm sm:text-base">
                {isVendor ? 'Customer Management' : 'User Management'}
              </h3>
              <p className="text-xs sm:text-sm text-purple-700 mb-3 sm:mb-4">
                {isVendor
                  ? 'Manage customer inquiries, bookings, and relationship tools.'
                  : 'Bulk user operations and account management tools.'
                }
              </p>
              <Button variant="outline" className="w-full text-xs sm:text-sm" disabled>
                Coming Soon
              </Button>
            </div>
            <div className="bg-white rounded-xl shadow-sm p-4 sm:p-6 flex flex-col items-center text-center">
              <AlertTriangle className="w-6 h-6 sm:w-8 sm:h-8 text-orange-600 mb-2 sm:mb-3" />
              <h3 className="font-semibold text-orange-900 mb-2 text-sm sm:text-base">
                {isVendor ? 'Business Analytics' : 'System Health'}
              </h3>
              <p className="text-xs sm:text-sm text-orange-700 mb-3 sm:mb-4">
                {isVendor
                  ? 'Monitor your business performance and customer engagement metrics.'
                  : 'Monitor system health and performance metrics.'
                }
              </p>
              <Button variant="outline" className="w-full text-xs sm:text-sm" disabled>
                Coming Soon
              </Button>
            </div>
          </div>
        </TabsContent>

        {/* System Info Tab */}
        <TabsContent value="info">
          <div className="bg-white rounded-xl shadow-sm p-4 sm:p-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6">
              <div>
                <h3 className="font-semibold text-gray-900 mb-3 text-sm sm:text-base">Application Info</h3>
                <div className="space-y-2 text-xs sm:text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Application:</span>
                    <span className="font-medium">BookmyFestive</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Version:</span>
                    <span className="font-medium">1.0.0</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Environment:</span>
                    <span className="font-medium">{process.env.NODE_ENV || 'development'}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Build Date:</span>
                    <span className="font-medium">{new Date().toLocaleDateString('en-US', {
                      year: 'numeric',
                      month: '2-digit',
                      day: '2-digit'
                    })}</span>
                  </div>
                </div>
              </div>
              <div>
                <h3 className="font-semibold text-gray-900 mb-3 text-sm sm:text-base">Database Info</h3>
                <div className="space-y-2 text-xs sm:text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Provider:</span>
                    <span className="font-medium">AWS DynamoDB</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">API:</span>
                    <span className="font-medium">AWS AppSync GraphQL</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Auth:</span>
                    <span className="font-medium">AWS Cognito</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Storage:</span>
                    <span className="font-medium">AWS S3</span>
                  </div>
                </div>
              </div>
              <div>
                <h3 className="font-semibold text-gray-900 mb-3 text-sm sm:text-base">User Session</h3>
                <div className="space-y-2 text-xs sm:text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600">User ID:</span>
                    <span className="font-medium font-mono text-xs">{user?.userId?.slice(-8) || 'N/A'}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Email:</span>
                    <span className="font-medium">{user?.email || 'N/A'}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Login Time:</span>
                    <span className="font-medium">{new Date().toLocaleTimeString()}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Session:</span>
                    <span className="font-medium text-green-600">Active</span>
                  </div>
                </div>
              </div>
              <div>
                <h3 className="font-semibold text-gray-900 mb-3 text-sm sm:text-base">Quick Actions</h3>
                <div className="space-y-2">
                  <Button variant="outline" size="sm" className="w-full justify-start text-xs sm:text-sm">
                    <Database className="w-3 h-3 sm:w-4 sm:h-4 mr-2" />
                    Export Data
                  </Button>
                  <Button variant="outline" size="sm" className="w-full justify-start text-xs sm:text-sm">
                    <Settings className="w-3 h-3 sm:w-4 sm:h-4 mr-2" />
                    System Settings
                  </Button>
                  <Button variant="outline" size="sm" className="w-full justify-start text-xs sm:text-sm">
                    <Shield className="w-3 h-3 sm:w-4 sm:h-4 mr-2" />
                    Security Logs
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
