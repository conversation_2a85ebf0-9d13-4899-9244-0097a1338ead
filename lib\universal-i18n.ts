"use client"

import { useTranslation as useI18nTranslation } from 'react-i18next';
import { useEffect, useState } from 'react';
import '@/lib/i18n-enhanced';

// Universal translation hook that works across all pages
export function useUniversalTranslation(namespace = 'common') {
  const { t, i18n, ready } = useI18nTranslation(namespace);
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  const safeT = (key: string, fallback?: string, options?: any) => {
    if (!mounted || !ready) {
      return fallback || key.split('.').pop() || key;
    }
    
    const translation = t(key, options);
    
    // If translation is the same as key, it means translation is missing
    if (translation === key && fallback) {
      return fallback;
    }
    
    return translation;
  };

  return {
    t: safeT,
    i18n,
    ready: mounted && ready,
    currentLanguage: i18n.language || 'en',
  };
}

// Quick translation functions for common sections
export const useCartTranslations = () => {
  const { t } = useUniversalTranslation('cart');
  return {
    title: t('title', 'Shopping Cart'),
    items: t('items', 'items'),
    item: t('item', 'item'),
    total: t('total', 'Total'),
    subtotal: t('subtotal', 'Subtotal'),
    checkout: t('checkout', 'Proceed to Checkout'),
    continueShopping: t('continueShopping', 'Continue Shopping'),
    clearCart: t('clearCart', 'Clear Cart'),
    emptyCart: t('emptyCart', 'Your cart is empty'),
    emptyCartMessage: t('emptyCartMessage', 'Looks like you haven\'t added any items to your cart yet.'),
    startShopping: t('startShopping', 'Start Shopping'),
    addToCart: t('addToCart', 'Add to Cart'),
    removeItem: t('removeItem', 'Remove Item'),
    updateQuantity: t('updateQuantity', 'Update Quantity'),
    saveForLater: t('saveForLater', 'Save for Later'),
    moveToCart: t('moveToCart', 'Move to Cart'),
    savedForLater: t('savedForLater', 'Saved for Later'),
    guestCart: t('guestCart', 'Guest Cart'),
    guestCartNote: t('guestCartNote', 'Login to sync and checkout.'),
    loginToCheckout: t('loginToCheckout', 'Login to Checkout'),
    orderSummary: t('orderSummary', 'Order Summary'),
    promoCode: t('promoCode', 'Promo Code'),
    enterCode: t('enterCode', 'Enter code'),
    apply: t('apply', 'Apply'),
    shipping: t('shipping', 'Shipping'),
    discount: t('discount', 'Discount'),
    free: t('free', 'FREE'),
    secure: t('secure', 'Secure'),
    freeShip: t('freeShip', 'Free Ship'),
    giftWrap: t('giftWrap', 'Gift Wrap'),
    bestPrice: t('bestPrice', 'Best Price'),
    secureCheckout: t('secureCheckout', 'Secure Checkout'),
    freeDelivery: t('freeDelivery', 'Free Delivery'),
    noSavedItems: t('noSavedItems', 'No saved items'),
    savedItemsMessage: t('savedItemsMessage', 'Items you save for later will appear here'),
    size: t('size', 'Size'),
    color: t('color', 'Color'),
    variant: t('variant', 'Variant'),
    off: t('off', 'OFF'),
  };
};

export const useCommonTranslations = () => {
  const { t } = useUniversalTranslation();
  return {
    loading: t('common.loading', 'Loading...'),
    error: t('common.error', 'Something went wrong'),
    tryAgain: t('common.tryAgain', 'Try Again'),
    viewAll: t('common.viewAll', 'View All'),
    learnMore: t('common.learnMore', 'Learn More'),
    getStarted: t('common.getStarted', 'Get Started'),
    contactUs: t('common.contactUs', 'Contact Us'),
    save: t('common.save', 'Save'),
    cancel: t('common.cancel', 'Cancel'),
    delete: t('common.delete', 'Delete'),
    edit: t('common.edit', 'Edit'),
    update: t('common.update', 'Update'),
    add: t('common.add', 'Add'),
    remove: t('common.remove', 'Remove'),
    search: t('common.search', 'Search'),
    filter: t('common.filter', 'Filter'),
    sort: t('common.sort', 'Sort'),
    next: t('common.next', 'Next'),
    previous: t('common.previous', 'Previous'),
    close: t('common.close', 'Close'),
    open: t('common.open', 'Open'),
    yes: t('common.yes', 'Yes'),
    no: t('common.no', 'No'),
    ok: t('common.ok', 'OK'),
  };
};

export const useHelpTranslations = () => {
  const { t } = useUniversalTranslation('help');
  return {
    title: t('title', 'How can we help you?'),
    subtitle: t('subtitle', 'Find answers, get support, and learn how to make the most of BookmyFestive'),
    searchPlaceholder: t('searchPlaceholder', 'Search for help articles, guides, or features...'),
    quickHelp: {
      title: t('quickHelp.title', 'Quick Help'),
      videoTutorials: {
        title: t('quickHelp.videoTutorials.title', 'Video Tutorials'),
        description: t('quickHelp.videoTutorials.description', 'Watch step-by-step guides')
      },
      userManual: {
        title: t('quickHelp.userManual.title', 'User Manual'),
        description: t('quickHelp.userManual.description', 'Complete documentation')
      },
      liveChat: {
        title: t('quickHelp.liveChat.title', 'Live Chat'),
        description: t('quickHelp.liveChat.description', 'Get instant help')
      },
      contactSupport: {
        title: t('quickHelp.contactSupport.title', 'Contact Support'),
        description: t('quickHelp.contactSupport.description', 'Call or email us')
      },
      access: t('quickHelp.access', 'Access'),
      startChat: t('quickHelp.startChat', 'Start Chat')
    },
    categories: {
      title: t('categories.title', 'Browse by Category'),
      viewAllArticles: t('categories.viewAllArticles', 'View All Articles')
    },
    popularArticles: {
      title: t('popularArticles.title', 'Popular Articles')
    },
    stillNeedHelp: {
      title: t('stillNeedHelp.title', 'Still need help?'),
      subtitle: t('stillNeedHelp.subtitle', 'Can\'t find what you\'re looking for? Our support team is here to help you.'),
      contactSupport: t('stillNeedHelp.contactSupport', 'Contact Support'),
      callUs: t('stillNeedHelp.callUs', 'Call Us: +91 8148376909')
    },
    faq: {
      title: t('faq.title', 'Frequently Asked Questions'),
      subtitle: t('faq.subtitle', 'Find quick answers to the most common questions about using BookmyFestive.'),
      backToHelp: t('faq.backToHelp', 'Back to Help Center'),
      stillHaveQuestions: {
        title: t('faq.stillHaveQuestions.title', 'Still have questions?'),
        subtitle: t('faq.stillHaveQuestions.subtitle', 'Our support team is ready to help you with any questions not covered in our FAQ.')
      }
    }
  };
};