"use client"

import { <PERSON>Header } from "@/components/top-header"
import { <PERSON><PERSON> } from "@/components/header"
import { <PERSON><PERSON> } from "@/components/footer"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { SimpleSEO } from "@/components/seo/SimpleSEO"
import { 
  Smartphone, 
  Download, 
  Star, 
  Bell, 
  Heart, 
  Search, 
  Calendar,
  MapPin,
  Users,
  Camera,
  ShoppingBag,
  CheckCircle,
  Apple,
  PlayCircle
} from "lucide-react"
import Link from "next/link"
import { useState } from "react"

export default function DownloadAppPage() {
  const [email, setEmail] = useState('')
  const [isSubscribed, setIsSubscribed] = useState(false)

  const handleNotifyMe = (e: React.FormEvent) => {
    e.preventDefault()
    if (email) {
      setIsSubscribed(true)
      // Here you would typically send the email to your backend
      console.log('Notify email:', email)
    }
  }

  const features = [
    {
      icon: Search,
      title: "Smart Search",
      description: "Find vendors, venues, and services with AI-powered search"
    },
    {
      icon: Heart,
      title: "Save Favorites",
      description: "Create wishlists and save your favorite vendors and venues"
    },
    {
      icon: Calendar,
      title: "Wedding Planner",
      description: "Organize your wedding timeline with our built-in planner"
    },
    {
      icon: Bell,
      title: "Real-time Notifications",
      description: "Get instant updates on bookings, offers, and reminders"
    },
    {
      icon: Camera,
      title: "Photo Gallery",
      description: "Browse stunning wedding photos and get inspired"
    },
    {
      icon: ShoppingBag,
      title: "Shop on the Go",
      description: "Purchase wedding essentials directly from the app"
    }
  ]

  const benefits = [
    "Access to 10,000+ verified vendors across India",
    "Exclusive mobile-only deals and discounts",
    "Offline access to your saved vendors and venues",
    "One-tap calling and messaging to vendors",
    "Real-time availability checking",
    "Secure payment gateway integration"
  ]

  return (
    <>
      <SimpleSEO
        title="Download BookmyFestive Mobile App - Coming Soon"
        description="Get ready for the ultimate wedding planning experience on your mobile. Download the BookmyFestive app to Plan Your Dream Celebration anytime, anywhere."
        keywords="BookmyFestive app, wedding planning app, mobile app, download app, wedding app india, tamil wedding app"
        image="/hero_image_1.webp"
        url="/download-app"
      />
      <div className="min-h-screen bg-background">
        <TopHeader />
        <Header />

        {/* Hero Section */}
        <section className="bg-[#5d1417] text-primary-foreground py-12 sm:py-16 md:py-20">
          <div className="container mx-auto px-4 text-center">
            <div className="max-w-4xl mx-auto">
              <Smartphone className="w-16 h-16 sm:w-20 sm:h-20 mx-auto mb-4 sm:mb-6 text-primary-foreground/90" />
              <h1 className="text-2xl sm:text-3xl md:text-4xl lg:text-6xl font-bold mb-4 sm:mb-6 text-primary-foreground leading-tight">
                Coming Soon to Your Phone
              </h1>
              <p className="text-base sm:text-lg md:text-xl lg:text-2xl mb-6 sm:mb-8 text-primary-foreground/90 px-2">
                The ultimate wedding planning experience is coming to mobile.
                Plan Your Dream Celebration anytime, anywhere.
              </p>

              {/* Notify Me Form */}
              <div className="max-w-md mx-auto px-2">
                {!isSubscribed ? (
                  <form onSubmit={handleNotifyMe} className="flex flex-col sm:flex-row gap-3">
                    <input
                      type="email"
                      placeholder="Enter your email to get notified"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      className="flex-1 px-3 sm:px-4 py-2 sm:py-3 rounded-lg text-foreground placeholder-muted-foreground bg-background border border-border text-sm sm:text-base"
                      required
                    />
                    <Button type="submit" className="bg-background text-foreground hover:bg-muted px-4 sm:px-6 py-2 sm:py-3 text-sm sm:text-base">
                      <Bell className="w-4 h-4 mr-2" />
                      Notify Me
                    </Button>
                  </form>
                ) : (
                  <div className="bg-background/20 backdrop-blur-sm rounded-lg p-3 sm:p-4 border border-border/20">
                    <CheckCircle className="w-6 h-6 mx-auto mb-2 text-primary-foreground" />
                    <p className="text-base sm:text-lg font-semibold text-primary-foreground">Thank you!</p>
                    <p className="text-sm sm:text-base text-primary-foreground/90">We'll notify you when the app is ready to download.</p>
                  </div>
                )}
              </div>
            </div>
          </div>
        </section>

        {/* App Store Badges Preview */}
        <section className="py-12 sm:py-16 bg-muted/50">
          <div className="container mx-auto px-4 text-center">
            <h2 className="text-2xl sm:text-3xl font-bold mb-6 sm:mb-8 text-foreground">Available Soon On</h2>
            <div className="flex flex-col sm:flex-row justify-center items-center gap-4 sm:gap-6 mb-6 sm:mb-8">
              <div className="bg-foreground text-background rounded-lg px-4 sm:px-6 py-2 sm:py-3 flex items-center gap-2 sm:gap-3 opacity-50 w-full sm:w-auto max-w-xs">
                <Apple className="w-6 sm:w-8 h-6 sm:h-8" />
                <div className="text-left">
                  <div className="text-xs">Download on the</div>
                  <div className="text-sm sm:text-lg font-semibold">App Store</div>
                </div>
              </div>
              <div className="bg-foreground text-background rounded-lg px-4 sm:px-6 py-2 sm:py-3 flex items-center gap-2 sm:gap-3 opacity-50 w-full sm:w-auto max-w-xs">
                <PlayCircle className="w-6 sm:w-8 h-6 sm:h-8" />
                <div className="text-left">
                  <div className="text-xs">Get it on</div>
                  <div className="text-sm sm:text-lg font-semibold">Google Play</div>
                </div>
              </div>
            </div>
            <p className="text-sm sm:text-base text-muted-foreground">Coming to iOS and Android devices</p>
          </div>
        </section>

        {/* Features Section */}
        <section className="py-12 sm:py-16 md:py-20 bg-background">
          <div className="container mx-auto px-4">
            <div className="text-center mb-12 sm:mb-16">
              <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold mb-3 sm:mb-4 text-foreground px-2">
                Everything You Need for Your Wedding
              </h2>
              <p className="text-base sm:text-lg md:text-xl text-muted-foreground max-w-2xl mx-auto px-2">
                Our mobile app will bring all the power of BookmyFestive to your fingertips
              </p>
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-8">
              {features.map((feature, index) => (
                <Card key={index} className="text-center p-4 sm:p-6 hover:shadow-lg transition-shadow bg-card border-border">
                  <CardContent className="pt-4 sm:pt-6">
                    <feature.icon className="w-10 sm:w-12 h-10 sm:h-12 mx-auto mb-3 sm:mb-4 text-primary" />
                    <h3 className="text-lg sm:text-xl font-semibold mb-2 sm:mb-3 text-card-foreground">{feature.title}</h3>
                    <p className="text-sm sm:text-base text-muted-foreground">{feature.description}</p>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Benefits Section */}
        <section className="py-12 sm:py-16 md:py-20 bg-muted/30">
          <div className="container mx-auto px-4">
            <div className="max-w-4xl mx-auto">
              <div className="text-center mb-12 sm:mb-16">
                <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold mb-3 sm:mb-4 text-foreground px-2">
                  Why Choose Our Mobile App?
                </h2>
                <p className="text-base sm:text-lg md:text-xl text-muted-foreground px-2">
                  Get exclusive benefits and features designed for mobile users
                </p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 sm:gap-6">
                {benefits.map((benefit, index) => (
                  <div key={index} className="flex items-start gap-3 px-2">
                    <CheckCircle className="w-5 sm:w-6 h-5 sm:h-6 text-secondary mt-1 flex-shrink-0" />
                    <p className="text-sm sm:text-base md:text-lg text-foreground">{benefit}</p>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-12 sm:py-16 md:py-20 bg-[#5d1417] text-primary-foreground">
          <div className="container mx-auto px-4 text-center">
            <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold mb-4 sm:mb-6 text-primary-foreground px-2 leading-tight">
              Be the First to Experience Mobile Wedding Planning
            </h2>
            <p className="text-base sm:text-lg md:text-xl mb-6 sm:mb-8 max-w-2xl mx-auto text-primary-foreground/90 px-2">
              Join thousands of couples who are already planning their dream weddings with BookmyFestive
            </p>
            <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 justify-center px-2">
              <Link href="/vendors" className="w-full sm:w-auto">
                <Button className="bg-background text-foreground hover:bg-muted px-6 sm:px-8 py-2 sm:py-3 w-full sm:w-auto text-sm sm:text-base">
                  <Users className="w-4 sm:w-5 h-4 sm:h-5 mr-2" />
                  Browse Vendors Now
                </Button>
              </Link>
              <Link href="/venues" className="w-full sm:w-auto">
                <Button className="bg-background text-foreground hover:bg-muted px-6 sm:px-8 py-2 sm:py-3 w-full sm:w-auto text-sm sm:text-base">
                  <MapPin className="w-4 sm:w-5 h-4 sm:h-5 mr-2" />
                  Find Venues
                </Button>
              </Link>
            </div>
          </div>
        </section>

        <Footer />
      </div>
    </>
  )
}
