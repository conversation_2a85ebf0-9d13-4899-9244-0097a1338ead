"use client"

import { generateClient } from '@aws-amplify/api'
import { createBooking, updateBooking } from '@/src/graphql/mutations'
import { listBookings, getBooking, getUserProfile } from '@/src/graphql/queries'
import { getCurrentUser } from 'aws-amplify/auth'
import { AvailabilityService } from './availabilityService'
import { emailService } from './emailService'
import { invoiceService } from './invoiceService'

// Create clients for different auth modes
const userPoolClient = generateClient({ authMode: 'userPool' })
const apiKeyClient = generateClient({ authMode: 'apiKey' })
const defaultClient = generateClient()

// Helper function to get the appropriate client based on authentication
async function getAuthenticatedClient() {
  try {
    const user = await getCurrentUser()
    if (user) {
      return { client: userPoolClient, authMode: 'userPool', isAuthenticated: true }
    }
  } catch (error) {
    console.log('User not authenticated, using apiKey client')
  }
  return { client: apiKeyClient, authMode: 'apiKey', isAuthenticated: false }
}

// Helper function to get user profile and determine if user is a vendor
async function getUserProfileInfo(userId: string) {
  try {
    const { client } = await getAuthenticatedClient()
    const result = await client.graphql({
      query: getUserProfile,
      variables: { id: userId }
    })
    return result.data.getUserProfile
  } catch (error) {
    console.warn('Could not fetch user profile:', error)
    return null
  }
}

export interface BookingInput {
  entityId: string
  entityType: 'VENDOR' | 'VENUE'
  entityName: string
  customerName?: string
  customerPhone?: string
  vendorId?: string
  eventDate: string
  eventTime: string
  guestCount: number
  eventType: string
  duration?: string
  specialRequests?: string
  budget?: string
  contactPreference: 'PHONE' | 'EMAIL' | 'WHATSAPP'
}

export interface BookingUpdateInput {
  bookingId: string
  status?: string
  vendorNotes?: string
  estimatedCost?: string
  priority?: string
  notes?: string
}

export interface BookingFilters {
  customerId?: string
  vendorId?: string
  status?: string
  entityType?: string
  limit?: number
}

export class BookingService {
  /**
   * Check availability with proper error handling
   */
  static async checkAvailability(
    entityId: string,
    entityType: 'VENDOR' | 'VENUE',
    selectedDate: string,
    selectedTime: string,
    duration?: string
  ) {
    try {
      return await AvailabilityService.checkAvailability({
        entityId,
        entityType,
        eventDate: selectedDate,
        eventTime: selectedTime,
        duration
      })
    } catch (error: any) {
      console.error('BookingService.checkAvailability error:', error)

      // Return mock availability for any errors
      const mockAvailable = Math.random() > 0.3 // 70% chance of being available
      return {
        isAvailable: mockAvailable,
        conflictingBookings: [],
        message: mockAvailable
          ? `Available for booking on ${selectedDate} at ${selectedTime} (estimated)`
          : `May not be available at the selected time. Please try a different time.`,
        suggestedDates: mockAvailable ? [] : this.generateMockSuggestedDates(selectedDate)
      }
    }
  }

  /**
   * Generate mock suggested dates
   */
  private static generateMockSuggestedDates(requestedDate: string): string[] {
    const date = new Date(requestedDate)
    const suggestions: string[] = []

    for (let i = 1; i <= 5; i++) {
      const nextDate = new Date(date)
      nextDate.setDate(date.getDate() + i)
      suggestions.push(nextDate.toISOString().split('T')[0])
    }

    return suggestions
  }
  static version = '1.0.1' // Version for debugging

  /**
   * Create a new booking with availability checking
   */
  static async createBooking(bookingData: BookingInput) {
    try {
      // Get current user
      const user = await getCurrentUser()
      if (!user) {
        throw new Error('Authentication required')
      }

      // Check availability before creating booking
      const availabilityCheck = await AvailabilityService.checkAvailability({
        entityId: bookingData.entityId,
        entityType: bookingData.entityType,
        eventDate: bookingData.eventDate,
        eventTime: bookingData.eventTime,
        duration: bookingData.duration
      })

      if (!availabilityCheck.isAvailable) {
        return {
          success: false,
          message: availabilityCheck.message,
          conflictingBookings: availabilityCheck.conflictingBookings,
          suggestedDates: availabilityCheck.suggestedDates
        }
      }

      // Get user attributes
      const userAttributes = user.signInDetails?.loginId || user.username

      // Determine vendor ID based on entity type
      // For vendors, we'll store both the entityId and use userId as vendorId for easier querying
      let vendorId = null
      if (bookingData.entityType === 'VENDOR') {
        // Use the provided vendorId if available, otherwise use entityId
        vendorId = bookingData.vendorId || bookingData.entityId
      } else if (bookingData.entityType === 'VENUE') {
        vendorId = bookingData.vendorId || null
      }

      console.log('🔍 Debug - Booking creation:', {
        entityType: bookingData.entityType,
        entityId: bookingData.entityId,
        providedVendorId: bookingData.vendorId,
        finalVendorId: vendorId,
        customerId: user.userId
      })

      // Validate eventDate format (should be YYYY-MM-DD for AWSDate)
      if (!bookingData.eventDate || !/^\d{4}-\d{2}-\d{2}$/.test(bookingData.eventDate)) {
        throw new Error(`Invalid event date format. Expected YYYY-MM-DD, got: ${bookingData.eventDate}`)
      }

      // Create booking input
      const bookingInput = {
        customerId: user.userId,
        customerName: bookingData.customerName || userAttributes,
        customerEmail: userAttributes,
        customerPhone: bookingData.customerPhone || '',
        entityId: bookingData.entityId,
        entityType: bookingData.entityType,
        entityName: bookingData.entityName,
        vendorId: vendorId,
        eventDate: bookingData.eventDate,
        eventTime: bookingData.eventTime,
        guestCount: parseInt(bookingData.guestCount.toString()),
        eventType: bookingData.eventType,
        duration: bookingData.duration || '',
        specialRequests: bookingData.specialRequests || '',
        budget: bookingData.budget || '',
        contactPreference: bookingData.contactPreference,
        status: 'PENDING',
        priority: 'MEDIUM',
        notes: '',
        vendorNotes: '',
        paymentStatus: 'PENDING',
        contractSigned: false,
        reminderSent: false,
        communicationLog: [],
        attachments: [],
        metadata: JSON.stringify({
          source: 'web_booking_form',
          userAgent: navigator.userAgent,
          timestamp: new Date().toISOString()
        })
      }

      // Create booking in database
      const { client } = await getAuthenticatedClient()
      const result = await client.graphql({
        query: createBooking,
        variables: { input: bookingInput }
      })

      const booking = result.data.createBooking;

      // Generate invoice for the booking
      try {
        const invoiceResult = await invoiceService.generateBookingInvoice({
          bookingId: booking.id,
          bookingType: bookingData.entityType.toLowerCase() as 'venue' | 'vendor',
          customerId: user.userId,
          customerName: bookingData.customerName || user.attributes?.name || 'Customer',
          customerEmail: user.attributes?.email || '',
          vendorId: bookingData.entityId,
          vendorName: bookingData.entityName,
          vendorEmail: bookingData.vendorEmail || '',
          serviceName: bookingData.serviceName || bookingData.entityName,
          eventDate: bookingData.eventDate,
          eventTime: bookingData.eventTime,
          amount: bookingData.totalAmount || 0,
          paymentStatus: bookingData.paymentStatus || 'pending',
          paymentMethod: bookingData.paymentMethod,
          transactionId: bookingData.transactionId
        });

        if (invoiceResult.success) {
          console.log('✅ Invoice generated successfully for booking:', booking.id);
        } else {
          console.warn('⚠️ Invoice generation failed:', invoiceResult.error);
        }
      } catch (invoiceError) {
        console.error('Invoice generation error:', invoiceError);
        // Don't fail the booking if invoice generation fails
      }

      // Send booking confirmation email
      try {
        await emailService.sendBookingConfirmationEmail({
          email: user.attributes?.email || '',
          userName: bookingData.customerName || user.attributes?.name || 'Customer',
          bookingId: booking.id,
          entityName: bookingData.entityName,
          entityType: bookingData.entityType.toLowerCase() as 'vendor' | 'venue',
          eventDate: bookingData.eventDate,
          eventTime: bookingData.eventTime,
          amount: bookingData.totalAmount ? `₹${bookingData.totalAmount}` : undefined,
          status: 'confirmed'
        });
      } catch (emailError) {
        console.error('Error sending booking confirmation email:', emailError);
        // Don't fail booking if email fails
      }

      return {
        success: true,
        booking,
        message: 'Booking created successfully'
      }

    } catch (error) {
      console.error('Booking creation error:', error)
      throw new Error(`Failed to create booking: ${error.message}`)
    }
  }

  /**
   * Create booking with reservation system (for high-conflict scenarios)
   */
  static async createBookingWithReservation(bookingData: BookingInput) {
    try {
      // Step 1: Create temporary reservation
      const reservation = await AvailabilityService.createReservation({
        entityId: bookingData.entityId,
        entityType: bookingData.entityType,
        eventDate: bookingData.eventDate,
        eventTime: bookingData.eventTime,
        duration: bookingData.duration
      })

      if (!reservation.success) {
        return {
          success: false,
          message: reservation.message
        }
      }

      // Step 2: Confirm reservation with actual booking data
      const confirmedBooking = await AvailabilityService.confirmReservation(
        reservation.reservationId!,
        bookingData
      )

      return confirmedBooking
    } catch (error) {
      console.error('Reservation booking error:', error)
      throw new Error(`Failed to create booking with reservation: ${error.message}`)
    }
  }

  /**
   * Check availability for a specific date and time
   */
  static async checkAvailability(
    entityId: string,
    entityType: 'VENDOR' | 'VENUE',
    eventDate: string,
    eventTime?: string,
    duration?: string
  ) {
    return await AvailabilityService.checkAvailability({
      entityId,
      entityType,
      eventDate,
      eventTime,
      duration
    })
  }

  /**
   * Get bookings with filters
   */
  static async getBookings(filters: BookingFilters = {}) {
    try {
      console.log('🔍 Debug - BookingService version:', BookingService.version)

      const user = await getCurrentUser()
      if (!user) {
        throw new Error('Authentication required')
      }

      console.log('🔍 Debug - User info:', {
        userId: user.userId,
        username: user.username,
        signInDetails: user.signInDetails
      })

      // Build filter based on user role and parameters
      let filter: any = {}

      console.log('🔍 Debug - BookingService.getBookings called')
      console.log('🔍 Debug - Filters received:', filters)
      console.log('🔍 Debug - Filter checks:', {
        hasCustomerId: !!filters.customerId,
        customerIdValue: filters.customerId,
        customerIdTrimmed: filters.customerId?.trim(),
        hasVendorId: !!filters.vendorId,
        vendorIdValue: filters.vendorId,
        vendorIdTrimmed: filters.vendorId?.trim(),
        filterKeys: Object.keys(filters)
      })

      // Force log the exact condition checks
      console.log('🔍 Debug - Condition 1 (customerId):', filters.customerId && filters.customerId.trim())
      console.log('🔍 Debug - Condition 2 (vendorId):', filters.vendorId && filters.vendorId.trim())

      if (filters.customerId && filters.customerId.trim()) {
        filter.customerId = { eq: filters.customerId }
        console.log('🔍 Debug - USING customerId filter:', filters.customerId)
      } else if (filters.vendorId && filters.vendorId.trim()) {
        // For vendor queries, we need to check both vendorId and entityId
        // because vendors might be stored as either
        filter.or = [
          { vendorId: { eq: filters.vendorId } },
          { entityId: { eq: filters.vendorId } }
        ]
        console.log('🔍 Debug - USING vendorId (OR entityId) filter:', filters.vendorId)
      } else {
        // Default to user's own bookings as customer
        filter.customerId = { eq: user.userId }
        console.log('🔍 Debug - USING default customer filter (no specific filter provided):', user.userId)
        console.log('🔍 Debug - Why default? customerId check:', !filters.customerId || !filters.customerId.trim())
        console.log('🔍 Debug - Why default? vendorId check:', !filters.vendorId || !filters.vendorId.trim())
      }

      if (filters.status) {
        filter.status = { eq: filters.status.toUpperCase() }
      }

      if (filters.entityType) {
        filter.entityType = { eq: filters.entityType.toUpperCase() }
      }

      console.log('🔍 Debug - Final filter:', filter)

      const { client: authClient } = await getAuthenticatedClient()
      const result = await authClient.graphql({
        query: listBookings,
        variables: {
          filter: filter,
          limit: filters.limit || 20,
          sortDirection: 'DESC'
        }
      })

      console.log('🔍 Debug - Query result:', {
        itemCount: result.data.listBookings.items.length,
        items: result.data.listBookings.items.map(item => ({
          id: item.id,
          customerId: item.customerId,
          vendorId: item.vendorId,
          entityId: item.entityId,
          entityName: item.entityName
        }))
      })

      return {
        success: true,
        bookings: result.data.listBookings.items,
        nextToken: result.data.listBookings.nextToken
      }

    } catch (error) {
      console.error('Get bookings error:', error)
      throw new Error(`Failed to fetch bookings: ${error.message}`)
    }
  }

  /**
   * Update booking status and details
   */
  static async updateBooking(updateData: BookingUpdateInput) {
    try {
      const user = await getCurrentUser()
      if (!user) {
        throw new Error('Authentication required')
      }

      const { bookingId, status, vendorNotes, estimatedCost, notes, priority } = updateData

      if (!bookingId) {
        throw new Error('Booking ID is required')
      }

      // Get existing booking to verify permissions
      const existingBooking = await client.graphql({
        query: getBooking,
        variables: { id: bookingId },
        authMode: 'userPool'
      })

      const booking = existingBooking.data.getBooking
      if (!booking) {
        throw new Error('Booking not found')
      }

      // Check permissions
      const isCustomer = booking.customerId === user.userId
      const isVendor = booking.vendorId === user.userId
      
      if (!isCustomer && !isVendor) {
        throw new Error('Unauthorized to update this booking')
      }

      // Prepare update input
      const updateInput: any = {
        id: bookingId
      }

      if (status) {
        updateInput.status = status.toUpperCase()
      }

      if (vendorNotes && isVendor) {
        updateInput.vendorNotes = vendorNotes
      }

      if (estimatedCost && isVendor) {
        updateInput.estimatedCost = estimatedCost
      }

      if (notes) {
        updateInput.notes = notes
      }

      if (priority && isVendor) {
        updateInput.priority = priority.toUpperCase()
      }

      // Add communication log entry
      const communicationEntry = {
        timestamp: new Date().toISOString(),
        type: 'SYSTEM',
        from: user.userId,
        to: isCustomer ? booking.vendorId : booking.customerId,
        message: `Booking status updated to ${status || 'updated'}`,
        status: 'SENT'
      }

      updateInput.communicationLog = [
        ...(booking.communicationLog || []),
        communicationEntry
      ]

      // Update booking
      const result = await client.graphql({
        query: updateBooking,
        variables: { input: updateInput },
        authMode: 'userPool'
      })

      return {
        success: true,
        booking: result.data.updateBooking,
        message: 'Booking updated successfully'
      }

    } catch (error) {
      console.error('Booking update error:', error)
      throw new Error(`Failed to update booking: ${error.message}`)
    }
  }

  /**
   * Get a single booking by ID
   */
  static async getBookingById(bookingId: string) {
    try {
      const user = await getCurrentUser()
      if (!user) {
        throw new Error('Authentication required')
      }

      const result = await client.graphql({
        query: getBooking,
        variables: { id: bookingId },
        authMode: 'userPool'
      })

      const booking = result.data.getBooking
      if (!booking) {
        throw new Error('Booking not found')
      }

      // Check permissions
      const isCustomer = booking.customerId === user.userId
      const isVendor = booking.vendorId === user.userId
      
      if (!isCustomer && !isVendor) {
        throw new Error('Unauthorized to view this booking')
      }

      return {
        success: true,
        booking: booking
      }

    } catch (error) {
      console.error('Get booking error:', error)
      throw new Error(`Failed to fetch booking: ${error.message}`)
    }
  }

  /**
   * Check if a specific user can book an entity on a given date
   */
  static async canUserBook(
    entityId: string,
    entityType: 'VENDOR' | 'VENUE',
    eventDate: string,
    userId: string
  ) {
    try {
      // Get the appropriate client based on authentication status
      const { client, isAuthenticated } = await getAuthenticatedClient()

      if (!isAuthenticated) {
        return {
          canBook: false,
          reason: 'AUTHENTICATION_REQUIRED',
          message: 'Please log in to check booking availability'
        }
      }

      // Check if user already has a booking for this entity on this date
      const existingBookingsResult = await client.graphql({
        query: listBookings,
        variables: {
          filter: {
            and: [
              { customerId: { eq: userId } },
              { entityId: { eq: entityId } },
              { eventDate: { eq: eventDate } },
              {
                or: [
                  { status: { eq: 'PENDING' } },
                  { status: { eq: 'CONFIRMED' } },
                  { status: { eq: 'IN_PROGRESS' } }
                ]
              }
            ]
          }
        }
      })

      const existingBookings = existingBookingsResult.data.listBookings.items || []

      if (existingBookings.length > 0) {
        const booking = existingBookings[0]
        return {
          canBook: false,
          reason: booking.status === 'CONFIRMED' ? 'already_confirmed' : 'pending_confirmation',
          message: booking.status === 'CONFIRMED'
            ? 'You already have a confirmed booking for this date'
            : 'You have a pending booking for this date',
          bookingId: booking.id,
          status: booking.status,
          conflictingBookings: existingBookings
        }
      }

      // Check general availability
      const availabilityResult = await AvailabilityService.checkAvailability({
        entityId,
        entityType,
        eventDate
      })

      return {
        canBook: availabilityResult.isAvailable,
        reason: availabilityResult.isAvailable ? 'available' : 'date_unavailable',
        message: availabilityResult.message,
        conflictingBookings: availabilityResult.conflictingBookings
      }

    } catch (error) {
      console.error('Error checking user booking status:', error)
      return {
        canBook: true, // Allow booking on error to not block users
        reason: 'error',
        message: 'Unable to check booking status. You can still proceed with booking.',
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * Check general availability for non-authenticated users
   */
  static async checkGeneralAvailability(
    entityId: string,
    entityType: 'VENDOR' | 'VENUE',
    eventDate: string
  ) {
    try {
      const availabilityResult = await AvailabilityService.checkAvailability({
        entityId,
        entityType,
        eventDate
      })

      return {
        available: availabilityResult.isAvailable,
        message: availabilityResult.message,
        conflictingBookings: availabilityResult.conflictingBookings
      }

    } catch (error) {
      console.error('Error checking general availability:', error)
      return {
        available: true, // Allow booking on error to not block users
        message: 'Unable to check availability. You can still proceed with booking.',
        conflictingBookings: []
      }
    }
  }

  /**
   * Cancel a user's booking
   */
  static async cancelUserBooking(bookingId: string, reason?: string) {
    try {
      const user = await getCurrentUser()
      if (!user) {
        throw new Error('Authentication required')
      }

      // Get existing booking to verify permissions
      const existingBooking = await client.graphql({
        query: getBooking,
        variables: { id: bookingId },
        authMode: 'userPool'
      })

      const booking = existingBooking.data.getBooking
      if (!booking) {
        throw new Error('Booking not found')
      }

      // Check permissions - only customer or vendor can cancel
      const isCustomer = booking.customerId === user.userId
      const isVendor = booking.vendorId === user.userId

      if (!isCustomer && !isVendor) {
        throw new Error('Unauthorized to cancel this booking')
      }

      // Update booking status to cancelled
      const updateInput = {
        id: bookingId,
        status: 'CANCELLED',
        cancellationReason: reason || 'Cancelled by user',
        cancellationDate: new Date().toISOString().split('T')[0]
      }

      const result = await client.graphql({
        query: updateBooking,
        variables: { input: updateInput },
        authMode: 'userPool'
      })

      return {
        success: true,
        booking: result.data.updateBooking,
        message: 'Booking cancelled successfully'
      }

    } catch (error) {
      console.error('Cancel booking error:', error)
      return {
        success: false,
        message: `Failed to cancel booking: ${error.message}`
      }
    }
  }

  /**
   * Send booking notification emails
   */
  static async sendBookingNotifications(booking: any) {
    try {
      // This would typically call an email service
      // For now, we'll just log the notification
      console.log('Booking notification sent for:', booking.id)

      // You can implement actual email sending here
      // using services like AWS SES, SendGrid, etc.

      return {
        success: true,
        message: 'Notifications sent successfully'
      }
    } catch (error) {
      console.error('Notification error:', error)
      return {
        success: false,
        message: 'Failed to send notifications'
      }
    }
  }
}

export default BookingService
