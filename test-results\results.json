{"config": {"configFile": "/Users/<USER>/Documents/Thiru/Thir<PERSON><PERSON>/playwright.config.ts", "rootDir": "/Users/<USER>/Documents/Thiru/Thirumanam/tests/e2e", "forbidOnly": false, "fullyParallel": true, "globalSetup": null, "globalTeardown": null, "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {}, "preserveOutput": "always", "reporter": [["html", null], ["json", {"outputFile": "test-results/results.json"}], ["junit", {"outputFile": "test-results/results.xml"}]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "/Users/<USER>/Documents/Thiru/Thirumanam/test-results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "chromium", "name": "chromium", "testDir": "/Users/<USER>/Documents/Thiru/Thirumanam/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/Documents/Thiru/Thirumanam/test-results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "firefox", "name": "firefox", "testDir": "/Users/<USER>/Documents/Thiru/Thirumanam/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/Documents/Thiru/Thirumanam/test-results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "webkit", "name": "webkit", "testDir": "/Users/<USER>/Documents/Thiru/Thirumanam/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/Documents/Thiru/Thirumanam/test-results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "Mobile Chrome", "name": "Mobile Chrome", "testDir": "/Users/<USER>/Documents/Thiru/Thirumanam/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/Documents/Thiru/Thirumanam/test-results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "Mobile Safari", "name": "Mobile Safari", "testDir": "/Users/<USER>/Documents/Thiru/Thirumanam/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/Documents/Thiru/Thirumanam/test-results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "Tablet Chrome", "name": "Tablet Chrome", "testDir": "/Users/<USER>/Documents/Thiru/Thirumanam/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/Documents/Thiru/Thirumanam/test-results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "Desktop Large", "name": "Desktop Large", "testDir": "/Users/<USER>/Documents/Thiru/Thirumanam/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/Documents/Thiru/Thirumanam/test-results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "Desktop Small", "name": "Desktop Small", "testDir": "/Users/<USER>/Documents/Thiru/Thirumanam/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.54.2", "workers": 4, "webServer": {"command": "npm run dev", "url": "http://localhost:3000", "reuseExistingServer": true, "timeout": 120000}}, "suites": [{"title": "admin/admin-dashboard.spec.ts", "file": "admin/admin-dashboard.spec.ts", "column": 0, "line": 0, "specs": [], "suites": [{"title": "Admin Dashboard", "file": "admin/admin-dashboard.spec.ts", "line": 4, "column": 6, "specs": [{"title": "should display admin dashboard", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "356588c3a798a4aafe3b-c069b97c76eedf66142d", "file": "admin/admin-dashboard.spec.ts", "line": 18, "column": 7}, {"title": "should have user management section", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "356588c3a798a4aafe3b-ffa878877661bfce3151", "file": "admin/admin-dashboard.spec.ts", "line": 41, "column": 7}, {"title": "should have vendor management section", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "356588c3a798a4aafe3b-904aa9f2be93dc026c34", "file": "admin/admin-dashboard.spec.ts", "line": 64, "column": 7}, {"title": "should display analytics/statistics", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "356588c3a798a4aafe3b-9f079514d597cfffb9b9", "file": "admin/admin-dashboard.spec.ts", "line": 87, "column": 7}, {"title": "should display admin dashboard", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "356588c3a798a4aafe3b-ec754322d9ccd23a79c1", "file": "admin/admin-dashboard.spec.ts", "line": 18, "column": 7}, {"title": "should have user management section", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "356588c3a798a4aafe3b-97efd261874086641b2f", "file": "admin/admin-dashboard.spec.ts", "line": 41, "column": 7}, {"title": "should have vendor management section", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "356588c3a798a4aafe3b-670328044ce99f46693a", "file": "admin/admin-dashboard.spec.ts", "line": 64, "column": 7}, {"title": "should display analytics/statistics", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "356588c3a798a4aafe3b-62f7fe1faeb161f00b33", "file": "admin/admin-dashboard.spec.ts", "line": 87, "column": 7}, {"title": "should display admin dashboard", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "356588c3a798a4aafe3b-f4ce8c3731fa884c0c36", "file": "admin/admin-dashboard.spec.ts", "line": 18, "column": 7}, {"title": "should have user management section", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "356588c3a798a4aafe3b-1f57416c4aebfebfdb8f", "file": "admin/admin-dashboard.spec.ts", "line": 41, "column": 7}, {"title": "should have vendor management section", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "356588c3a798a4aafe3b-55024e2caa06e094b611", "file": "admin/admin-dashboard.spec.ts", "line": 64, "column": 7}, {"title": "should display analytics/statistics", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "356588c3a798a4aafe3b-17d33874b4e3064d5f05", "file": "admin/admin-dashboard.spec.ts", "line": 87, "column": 7}, {"title": "should display admin dashboard", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "356588c3a798a4aafe3b-1f9dfff594efdc7bf54a", "file": "admin/admin-dashboard.spec.ts", "line": 18, "column": 7}, {"title": "should have user management section", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "356588c3a798a4aafe3b-c9014586ffe7e7e9bc7b", "file": "admin/admin-dashboard.spec.ts", "line": 41, "column": 7}, {"title": "should have vendor management section", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "356588c3a798a4aafe3b-67e8ac6c958a611d7e25", "file": "admin/admin-dashboard.spec.ts", "line": 64, "column": 7}, {"title": "should display analytics/statistics", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "356588c3a798a4aafe3b-25fc0585ca95c33db8d0", "file": "admin/admin-dashboard.spec.ts", "line": 87, "column": 7}, {"title": "should display admin dashboard", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "356588c3a798a4aafe3b-ea671d168adbc30c6967", "file": "admin/admin-dashboard.spec.ts", "line": 18, "column": 7}, {"title": "should have user management section", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "356588c3a798a4aafe3b-7c459d5cbf70905743d1", "file": "admin/admin-dashboard.spec.ts", "line": 41, "column": 7}, {"title": "should have vendor management section", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "356588c3a798a4aafe3b-e31f80b087a2da79582c", "file": "admin/admin-dashboard.spec.ts", "line": 64, "column": 7}, {"title": "should display analytics/statistics", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "356588c3a798a4aafe3b-6a148b99028e078d6701", "file": "admin/admin-dashboard.spec.ts", "line": 87, "column": 7}, {"title": "should display admin dashboard", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Tablet Chrome", "projectName": "Tablet Chrome", "results": [], "status": "skipped"}], "id": "356588c3a798a4aafe3b-4f6e51b8f10cfebb977b", "file": "admin/admin-dashboard.spec.ts", "line": 18, "column": 7}, {"title": "should have user management section", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Tablet Chrome", "projectName": "Tablet Chrome", "results": [], "status": "skipped"}], "id": "356588c3a798a4aafe3b-398060e9f5296e32d344", "file": "admin/admin-dashboard.spec.ts", "line": 41, "column": 7}, {"title": "should have vendor management section", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Tablet Chrome", "projectName": "Tablet Chrome", "results": [], "status": "skipped"}], "id": "356588c3a798a4aafe3b-f197e1eef4bf7bba7f43", "file": "admin/admin-dashboard.spec.ts", "line": 64, "column": 7}, {"title": "should display analytics/statistics", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Tablet Chrome", "projectName": "Tablet Chrome", "results": [], "status": "skipped"}], "id": "356588c3a798a4aafe3b-14a29bae80cbcbf7dd5f", "file": "admin/admin-dashboard.spec.ts", "line": 87, "column": 7}, {"title": "should display admin dashboard", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Large", "projectName": "Desktop Large", "results": [], "status": "skipped"}], "id": "356588c3a798a4aafe3b-ae126c70cbfb90a0dcb9", "file": "admin/admin-dashboard.spec.ts", "line": 18, "column": 7}, {"title": "should have user management section", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Large", "projectName": "Desktop Large", "results": [], "status": "skipped"}], "id": "356588c3a798a4aafe3b-1e987dcc5251e8edb5cb", "file": "admin/admin-dashboard.spec.ts", "line": 41, "column": 7}, {"title": "should have vendor management section", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Large", "projectName": "Desktop Large", "results": [], "status": "skipped"}], "id": "356588c3a798a4aafe3b-8ef7272de64748416ccb", "file": "admin/admin-dashboard.spec.ts", "line": 64, "column": 7}, {"title": "should display analytics/statistics", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Large", "projectName": "Desktop Large", "results": [], "status": "skipped"}], "id": "356588c3a798a4aafe3b-521ac23e5b1d50492f01", "file": "admin/admin-dashboard.spec.ts", "line": 87, "column": 7}, {"title": "should display admin dashboard", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Small", "projectName": "Desktop Small", "results": [], "status": "skipped"}], "id": "356588c3a798a4aafe3b-f374c19dc99e39e959bd", "file": "admin/admin-dashboard.spec.ts", "line": 18, "column": 7}, {"title": "should have user management section", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Small", "projectName": "Desktop Small", "results": [], "status": "skipped"}], "id": "356588c3a798a4aafe3b-c193bd14b17053a57ce7", "file": "admin/admin-dashboard.spec.ts", "line": 41, "column": 7}, {"title": "should have vendor management section", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Small", "projectName": "Desktop Small", "results": [], "status": "skipped"}], "id": "356588c3a798a4aafe3b-8cd5ecc7cf9e6440f987", "file": "admin/admin-dashboard.spec.ts", "line": 64, "column": 7}, {"title": "should display analytics/statistics", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Small", "projectName": "Desktop Small", "results": [], "status": "skipped"}], "id": "356588c3a798a4aafe3b-8196e1e8c83c06226109", "file": "admin/admin-dashboard.spec.ts", "line": 87, "column": 7}]}, {"title": "Admin User Management", "file": "admin/admin-dashboard.spec.ts", "line": 113, "column": 6, "specs": [{"title": "should list all users", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "356588c3a798a4aafe3b-be45570af14051c3cc1b", "file": "admin/admin-dashboard.spec.ts", "line": 126, "column": 7}, {"title": "should allow user search", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "356588c3a798a4aafe3b-d868300ccfb77034ac59", "file": "admin/admin-dashboard.spec.ts", "line": 148, "column": 7}, {"title": "should list all users", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "356588c3a798a4aafe3b-897eda5f95731d1316f2", "file": "admin/admin-dashboard.spec.ts", "line": 126, "column": 7}, {"title": "should allow user search", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "356588c3a798a4aafe3b-eb671ca461e7016a14f2", "file": "admin/admin-dashboard.spec.ts", "line": 148, "column": 7}, {"title": "should list all users", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "356588c3a798a4aafe3b-3584ee8c91f3e2cd2473", "file": "admin/admin-dashboard.spec.ts", "line": 126, "column": 7}, {"title": "should allow user search", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "356588c3a798a4aafe3b-68a174e8bbf475ca06e0", "file": "admin/admin-dashboard.spec.ts", "line": 148, "column": 7}, {"title": "should list all users", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "356588c3a798a4aafe3b-2a22563f183e7f69ebc2", "file": "admin/admin-dashboard.spec.ts", "line": 126, "column": 7}, {"title": "should allow user search", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "356588c3a798a4aafe3b-099578226497f0d1b8b1", "file": "admin/admin-dashboard.spec.ts", "line": 148, "column": 7}, {"title": "should list all users", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "356588c3a798a4aafe3b-cd2dfe979b68ee1e7b70", "file": "admin/admin-dashboard.spec.ts", "line": 126, "column": 7}, {"title": "should allow user search", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "356588c3a798a4aafe3b-3b312f76e129ca5fc4e3", "file": "admin/admin-dashboard.spec.ts", "line": 148, "column": 7}, {"title": "should list all users", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Tablet Chrome", "projectName": "Tablet Chrome", "results": [], "status": "skipped"}], "id": "356588c3a798a4aafe3b-47963a66241bf1df9789", "file": "admin/admin-dashboard.spec.ts", "line": 126, "column": 7}, {"title": "should allow user search", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Tablet Chrome", "projectName": "Tablet Chrome", "results": [], "status": "skipped"}], "id": "356588c3a798a4aafe3b-3095fb95b7f7c777d1bf", "file": "admin/admin-dashboard.spec.ts", "line": 148, "column": 7}, {"title": "should list all users", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Large", "projectName": "Desktop Large", "results": [], "status": "skipped"}], "id": "356588c3a798a4aafe3b-76d9c3a0ebd03a66749a", "file": "admin/admin-dashboard.spec.ts", "line": 126, "column": 7}, {"title": "should allow user search", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Large", "projectName": "Desktop Large", "results": [], "status": "skipped"}], "id": "356588c3a798a4aafe3b-1b2d97f467e364ddc26f", "file": "admin/admin-dashboard.spec.ts", "line": 148, "column": 7}, {"title": "should list all users", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Small", "projectName": "Desktop Small", "results": [], "status": "skipped"}], "id": "356588c3a798a4aafe3b-5826f80274f8a8fcec19", "file": "admin/admin-dashboard.spec.ts", "line": 126, "column": 7}, {"title": "should allow user search", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Small", "projectName": "Desktop Small", "results": [], "status": "skipped"}], "id": "356588c3a798a4aafe3b-fa45e013d4bcc5d276bc", "file": "admin/admin-dashboard.spec.ts", "line": 148, "column": 7}]}]}, {"title": "auth/login.spec.ts", "file": "auth/login.spec.ts", "column": 0, "line": 0, "specs": [], "suites": [{"title": "User Login", "file": "auth/login.spec.ts", "line": 4, "column": 6, "specs": [{"title": "should display login form", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "231ca1f0f4db3e4958c3-d6ab8afbdeea2690cfdf", "file": "auth/login.spec.ts", "line": 13, "column": 7}, {"title": "should show validation errors for empty fields", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "231ca1f0f4db3e4958c3-dfa61120a080b56b529b", "file": "auth/login.spec.ts", "line": 22, "column": 7}, {"title": "should show error for invalid credentials", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "231ca1f0f4db3e4958c3-6dcdc32d55cf84e72bde", "file": "auth/login.spec.ts", "line": 48, "column": 7}, {"title": "should successfully login with valid credentials", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "231ca1f0f4db3e4958c3-d3600b0a88d060d384dc", "file": "auth/login.spec.ts", "line": 86, "column": 7}, {"title": "should redirect to previous page after login", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "231ca1f0f4db3e4958c3-785e2ba6200381d83eb0", "file": "auth/login.spec.ts", "line": 115, "column": 7}, {"title": "should show/hide password toggle", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "231ca1f0f4db3e4958c3-ce09f43bfbbf3761d857", "file": "auth/login.spec.ts", "line": 142, "column": 7}, {"title": "should have Google login option", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "231ca1f0f4db3e4958c3-08a2297801012ae1e943", "file": "auth/login.spec.ts", "line": 172, "column": 7}, {"title": "should have forgot password link", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "231ca1f0f4db3e4958c3-3dd4fc3b526d4910e1f8", "file": "auth/login.spec.ts", "line": 195, "column": 7}, {"title": "should have signup link", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "231ca1f0f4db3e4958c3-f2cbebd67d346c13c8dc", "file": "auth/login.spec.ts", "line": 218, "column": 7}, {"title": "should display login form", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "231ca1f0f4db3e4958c3-9ef5921e0ddfd20a1bc1", "file": "auth/login.spec.ts", "line": 13, "column": 7}, {"title": "should show validation errors for empty fields", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "231ca1f0f4db3e4958c3-4579b3435ed606d19bee", "file": "auth/login.spec.ts", "line": 22, "column": 7}, {"title": "should show error for invalid credentials", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "231ca1f0f4db3e4958c3-b4faabef89039c1343ea", "file": "auth/login.spec.ts", "line": 48, "column": 7}, {"title": "should successfully login with valid credentials", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "231ca1f0f4db3e4958c3-e1c8ad71d0df5d12d592", "file": "auth/login.spec.ts", "line": 86, "column": 7}, {"title": "should redirect to previous page after login", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "231ca1f0f4db3e4958c3-1deb751d51a5b11573f5", "file": "auth/login.spec.ts", "line": 115, "column": 7}, {"title": "should show/hide password toggle", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "231ca1f0f4db3e4958c3-6eecb051036e02476be1", "file": "auth/login.spec.ts", "line": 142, "column": 7}, {"title": "should have Google login option", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "231ca1f0f4db3e4958c3-486beb5ab3f8c7b11942", "file": "auth/login.spec.ts", "line": 172, "column": 7}, {"title": "should have forgot password link", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "231ca1f0f4db3e4958c3-d9cb256ed12595057452", "file": "auth/login.spec.ts", "line": 195, "column": 7}, {"title": "should have signup link", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "231ca1f0f4db3e4958c3-07722392f97910def0ae", "file": "auth/login.spec.ts", "line": 218, "column": 7}, {"title": "should display login form", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "231ca1f0f4db3e4958c3-0f71864887bd3c877c34", "file": "auth/login.spec.ts", "line": 13, "column": 7}, {"title": "should show validation errors for empty fields", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "231ca1f0f4db3e4958c3-272cb00a80e627e6acd5", "file": "auth/login.spec.ts", "line": 22, "column": 7}, {"title": "should show error for invalid credentials", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "231ca1f0f4db3e4958c3-80fd24f9f73581573666", "file": "auth/login.spec.ts", "line": 48, "column": 7}, {"title": "should successfully login with valid credentials", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "231ca1f0f4db3e4958c3-dcadbf07dcc6755c5f7e", "file": "auth/login.spec.ts", "line": 86, "column": 7}, {"title": "should redirect to previous page after login", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "231ca1f0f4db3e4958c3-2e066fac994cf16376fa", "file": "auth/login.spec.ts", "line": 115, "column": 7}, {"title": "should show/hide password toggle", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "231ca1f0f4db3e4958c3-e058fa35a50fa5442bc6", "file": "auth/login.spec.ts", "line": 142, "column": 7}, {"title": "should have Google login option", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "231ca1f0f4db3e4958c3-27a48200f2820ccf15d7", "file": "auth/login.spec.ts", "line": 172, "column": 7}, {"title": "should have forgot password link", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "231ca1f0f4db3e4958c3-090386295a5d7b6b3722", "file": "auth/login.spec.ts", "line": 195, "column": 7}, {"title": "should have signup link", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "231ca1f0f4db3e4958c3-a468c9b6a0641c61e7ce", "file": "auth/login.spec.ts", "line": 218, "column": 7}, {"title": "should display login form", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "231ca1f0f4db3e4958c3-bbf592c60b1e854f6949", "file": "auth/login.spec.ts", "line": 13, "column": 7}, {"title": "should show validation errors for empty fields", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "231ca1f0f4db3e4958c3-28475ac9b4f34cdc4572", "file": "auth/login.spec.ts", "line": 22, "column": 7}, {"title": "should show error for invalid credentials", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "231ca1f0f4db3e4958c3-59f2f34e802c79ae3af4", "file": "auth/login.spec.ts", "line": 48, "column": 7}, {"title": "should successfully login with valid credentials", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "231ca1f0f4db3e4958c3-baec73b70d30fa4c2e6a", "file": "auth/login.spec.ts", "line": 86, "column": 7}, {"title": "should redirect to previous page after login", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "231ca1f0f4db3e4958c3-42abe30c9aa5949ecac0", "file": "auth/login.spec.ts", "line": 115, "column": 7}, {"title": "should show/hide password toggle", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "231ca1f0f4db3e4958c3-4fe6e3ad0b974c2bc77c", "file": "auth/login.spec.ts", "line": 142, "column": 7}, {"title": "should have Google login option", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "231ca1f0f4db3e4958c3-0d32da98fda5506d8373", "file": "auth/login.spec.ts", "line": 172, "column": 7}, {"title": "should have forgot password link", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "231ca1f0f4db3e4958c3-0627a694b1fcf1010158", "file": "auth/login.spec.ts", "line": 195, "column": 7}, {"title": "should have signup link", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "231ca1f0f4db3e4958c3-14e685866f45c8a754af", "file": "auth/login.spec.ts", "line": 218, "column": 7}, {"title": "should display login form", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "231ca1f0f4db3e4958c3-0e15d1bfa1e9cb3bf970", "file": "auth/login.spec.ts", "line": 13, "column": 7}, {"title": "should show validation errors for empty fields", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "231ca1f0f4db3e4958c3-d970eb57eef6a49773fe", "file": "auth/login.spec.ts", "line": 22, "column": 7}, {"title": "should show error for invalid credentials", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "231ca1f0f4db3e4958c3-83656bcc7914665b26d5", "file": "auth/login.spec.ts", "line": 48, "column": 7}, {"title": "should successfully login with valid credentials", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "231ca1f0f4db3e4958c3-382407bb3019de34c1db", "file": "auth/login.spec.ts", "line": 86, "column": 7}, {"title": "should redirect to previous page after login", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "231ca1f0f4db3e4958c3-81bdcb70aa5368bdd732", "file": "auth/login.spec.ts", "line": 115, "column": 7}, {"title": "should show/hide password toggle", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "231ca1f0f4db3e4958c3-802f723f8a2740078436", "file": "auth/login.spec.ts", "line": 142, "column": 7}, {"title": "should have Google login option", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "231ca1f0f4db3e4958c3-92a6a994fc6242cf2e1d", "file": "auth/login.spec.ts", "line": 172, "column": 7}, {"title": "should have forgot password link", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "231ca1f0f4db3e4958c3-9de506ce17db3983d48e", "file": "auth/login.spec.ts", "line": 195, "column": 7}, {"title": "should have signup link", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "231ca1f0f4db3e4958c3-3ca10bb0cf10a3c53c99", "file": "auth/login.spec.ts", "line": 218, "column": 7}, {"title": "should display login form", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Tablet Chrome", "projectName": "Tablet Chrome", "results": [], "status": "skipped"}], "id": "231ca1f0f4db3e4958c3-ce004d6b3a6f513232b0", "file": "auth/login.spec.ts", "line": 13, "column": 7}, {"title": "should show validation errors for empty fields", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Tablet Chrome", "projectName": "Tablet Chrome", "results": [], "status": "skipped"}], "id": "231ca1f0f4db3e4958c3-f1ee49272860ccb3a407", "file": "auth/login.spec.ts", "line": 22, "column": 7}, {"title": "should show error for invalid credentials", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Tablet Chrome", "projectName": "Tablet Chrome", "results": [], "status": "skipped"}], "id": "231ca1f0f4db3e4958c3-bd247dbf17e8077ef391", "file": "auth/login.spec.ts", "line": 48, "column": 7}, {"title": "should successfully login with valid credentials", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Tablet Chrome", "projectName": "Tablet Chrome", "results": [], "status": "skipped"}], "id": "231ca1f0f4db3e4958c3-4ab161d1b11f64dd7e4d", "file": "auth/login.spec.ts", "line": 86, "column": 7}, {"title": "should redirect to previous page after login", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Tablet Chrome", "projectName": "Tablet Chrome", "results": [], "status": "skipped"}], "id": "231ca1f0f4db3e4958c3-64a25d19bdce0e8f4757", "file": "auth/login.spec.ts", "line": 115, "column": 7}, {"title": "should show/hide password toggle", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Tablet Chrome", "projectName": "Tablet Chrome", "results": [], "status": "skipped"}], "id": "231ca1f0f4db3e4958c3-bdf4e9361a2f5c8ac632", "file": "auth/login.spec.ts", "line": 142, "column": 7}, {"title": "should have Google login option", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Tablet Chrome", "projectName": "Tablet Chrome", "results": [], "status": "skipped"}], "id": "231ca1f0f4db3e4958c3-a9e4f0027359b033478d", "file": "auth/login.spec.ts", "line": 172, "column": 7}, {"title": "should have forgot password link", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Tablet Chrome", "projectName": "Tablet Chrome", "results": [], "status": "skipped"}], "id": "231ca1f0f4db3e4958c3-cf735177243daad12c6d", "file": "auth/login.spec.ts", "line": 195, "column": 7}, {"title": "should have signup link", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Tablet Chrome", "projectName": "Tablet Chrome", "results": [], "status": "skipped"}], "id": "231ca1f0f4db3e4958c3-13464fab103a0a5e5d2e", "file": "auth/login.spec.ts", "line": 218, "column": 7}, {"title": "should display login form", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Large", "projectName": "Desktop Large", "results": [], "status": "skipped"}], "id": "231ca1f0f4db3e4958c3-49d49440bde34a57eb6b", "file": "auth/login.spec.ts", "line": 13, "column": 7}, {"title": "should show validation errors for empty fields", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Large", "projectName": "Desktop Large", "results": [], "status": "skipped"}], "id": "231ca1f0f4db3e4958c3-fcadd4961feb7d781370", "file": "auth/login.spec.ts", "line": 22, "column": 7}, {"title": "should show error for invalid credentials", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Large", "projectName": "Desktop Large", "results": [], "status": "skipped"}], "id": "231ca1f0f4db3e4958c3-573d9496c6dc541fb089", "file": "auth/login.spec.ts", "line": 48, "column": 7}, {"title": "should successfully login with valid credentials", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Large", "projectName": "Desktop Large", "results": [], "status": "skipped"}], "id": "231ca1f0f4db3e4958c3-aa024383b91dc8b11c09", "file": "auth/login.spec.ts", "line": 86, "column": 7}, {"title": "should redirect to previous page after login", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Large", "projectName": "Desktop Large", "results": [], "status": "skipped"}], "id": "231ca1f0f4db3e4958c3-186991ad9fc764519d2e", "file": "auth/login.spec.ts", "line": 115, "column": 7}, {"title": "should show/hide password toggle", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Large", "projectName": "Desktop Large", "results": [], "status": "skipped"}], "id": "231ca1f0f4db3e4958c3-c05c8dee68fa3a302c4d", "file": "auth/login.spec.ts", "line": 142, "column": 7}, {"title": "should have Google login option", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Large", "projectName": "Desktop Large", "results": [], "status": "skipped"}], "id": "231ca1f0f4db3e4958c3-f3c7e149d6f174545f9d", "file": "auth/login.spec.ts", "line": 172, "column": 7}, {"title": "should have forgot password link", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Large", "projectName": "Desktop Large", "results": [], "status": "skipped"}], "id": "231ca1f0f4db3e4958c3-ba75456b4af694771abf", "file": "auth/login.spec.ts", "line": 195, "column": 7}, {"title": "should have signup link", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Large", "projectName": "Desktop Large", "results": [], "status": "skipped"}], "id": "231ca1f0f4db3e4958c3-23149dfdd0dfad3c3b9b", "file": "auth/login.spec.ts", "line": 218, "column": 7}, {"title": "should display login form", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Small", "projectName": "Desktop Small", "results": [], "status": "skipped"}], "id": "231ca1f0f4db3e4958c3-045ea51eee94fb3fb144", "file": "auth/login.spec.ts", "line": 13, "column": 7}, {"title": "should show validation errors for empty fields", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Small", "projectName": "Desktop Small", "results": [], "status": "skipped"}], "id": "231ca1f0f4db3e4958c3-48c66635d5c2309c07c1", "file": "auth/login.spec.ts", "line": 22, "column": 7}, {"title": "should show error for invalid credentials", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Small", "projectName": "Desktop Small", "results": [], "status": "skipped"}], "id": "231ca1f0f4db3e4958c3-820cdd004c6e04fc23ba", "file": "auth/login.spec.ts", "line": 48, "column": 7}, {"title": "should successfully login with valid credentials", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Small", "projectName": "Desktop Small", "results": [], "status": "skipped"}], "id": "231ca1f0f4db3e4958c3-8781a9221c5e518b2fa8", "file": "auth/login.spec.ts", "line": 86, "column": 7}, {"title": "should redirect to previous page after login", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Small", "projectName": "Desktop Small", "results": [], "status": "skipped"}], "id": "231ca1f0f4db3e4958c3-12ee483bda6ba5201d72", "file": "auth/login.spec.ts", "line": 115, "column": 7}, {"title": "should show/hide password toggle", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Small", "projectName": "Desktop Small", "results": [], "status": "skipped"}], "id": "231ca1f0f4db3e4958c3-91b37c9ddce9f8dc45f5", "file": "auth/login.spec.ts", "line": 142, "column": 7}, {"title": "should have Google login option", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Small", "projectName": "Desktop Small", "results": [], "status": "skipped"}], "id": "231ca1f0f4db3e4958c3-5eeaae27deb7e81d9093", "file": "auth/login.spec.ts", "line": 172, "column": 7}, {"title": "should have forgot password link", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Small", "projectName": "Desktop Small", "results": [], "status": "skipped"}], "id": "231ca1f0f4db3e4958c3-eb68b579d3d1df745019", "file": "auth/login.spec.ts", "line": 195, "column": 7}, {"title": "should have signup link", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Small", "projectName": "Desktop Small", "results": [], "status": "skipped"}], "id": "231ca1f0f4db3e4958c3-275ba406d1da9695e98d", "file": "auth/login.spec.ts", "line": 218, "column": 7}]}]}, {"title": "auth/signup.spec.ts", "file": "auth/signup.spec.ts", "column": 0, "line": 0, "specs": [], "suites": [{"title": "User Signup", "file": "auth/signup.spec.ts", "line": 4, "column": 6, "specs": [{"title": "should display signup form", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "c60d589c25b5f9baa21a-27fef78f008b6e989e01", "file": "auth/signup.spec.ts", "line": 12, "column": 7}, {"title": "should show validation errors for empty required fields", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "c60d589c25b5f9baa21a-4be9c6bb7f4c83476888", "file": "auth/signup.spec.ts", "line": 44, "column": 7}, {"title": "should show error for invalid email format", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "c60d589c25b5f9baa21a-5b73b17b87965f403b76", "file": "auth/signup.spec.ts", "line": 72, "column": 7}, {"title": "should show error for weak password", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "c60d589c25b5f9baa21a-0f760c5050666149a608", "file": "auth/signup.spec.ts", "line": 102, "column": 7}, {"title": "should successfully register new user", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "c60d589c25b5f9baa21a-c2f725900b8d4f461279", "file": "auth/signup.spec.ts", "line": 132, "column": 7}, {"title": "should show error for existing email", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "c60d589c25b5f9baa21a-84e38c877d9a1a07b5d6", "file": "auth/signup.spec.ts", "line": 184, "column": 7}, {"title": "should have Google signup option", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "c60d589c25b5f9baa21a-cca8cae04ee2ed5867e3", "file": "auth/signup.spec.ts", "line": 220, "column": 7}, {"title": "should have login link for existing users", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "c60d589c25b5f9baa21a-08611c138ad557197026", "file": "auth/signup.spec.ts", "line": 243, "column": 7}, {"title": "should handle terms and conditions checkbox", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "c60d589c25b5f9baa21a-f951b029c5baa0f5f108", "file": "auth/signup.spec.ts", "line": 267, "column": 7}, {"title": "should display signup form", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "c60d589c25b5f9baa21a-842f25ddcaab2039a124", "file": "auth/signup.spec.ts", "line": 12, "column": 7}, {"title": "should show validation errors for empty required fields", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "c60d589c25b5f9baa21a-9c099d308f9115aee45d", "file": "auth/signup.spec.ts", "line": 44, "column": 7}, {"title": "should show error for invalid email format", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "c60d589c25b5f9baa21a-d76ade169a5de2b93dc6", "file": "auth/signup.spec.ts", "line": 72, "column": 7}, {"title": "should show error for weak password", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "c60d589c25b5f9baa21a-f2c6b4980af58b41d0b2", "file": "auth/signup.spec.ts", "line": 102, "column": 7}, {"title": "should successfully register new user", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "c60d589c25b5f9baa21a-695e8fd50e40700310b5", "file": "auth/signup.spec.ts", "line": 132, "column": 7}, {"title": "should show error for existing email", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "c60d589c25b5f9baa21a-9ed46f5201fe3278ef8f", "file": "auth/signup.spec.ts", "line": 184, "column": 7}, {"title": "should have Google signup option", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "c60d589c25b5f9baa21a-d77b8f00ef47f68ea06e", "file": "auth/signup.spec.ts", "line": 220, "column": 7}, {"title": "should have login link for existing users", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "c60d589c25b5f9baa21a-72a5fb0e9e7df3de644e", "file": "auth/signup.spec.ts", "line": 243, "column": 7}, {"title": "should handle terms and conditions checkbox", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "c60d589c25b5f9baa21a-c6c32551b11ac85d4e07", "file": "auth/signup.spec.ts", "line": 267, "column": 7}, {"title": "should display signup form", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "c60d589c25b5f9baa21a-1518508fc94b7a2ef961", "file": "auth/signup.spec.ts", "line": 12, "column": 7}, {"title": "should show validation errors for empty required fields", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "c60d589c25b5f9baa21a-5b4bb589b3744fb4f0b4", "file": "auth/signup.spec.ts", "line": 44, "column": 7}, {"title": "should show error for invalid email format", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "c60d589c25b5f9baa21a-9532f8992ee90f358edb", "file": "auth/signup.spec.ts", "line": 72, "column": 7}, {"title": "should show error for weak password", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "c60d589c25b5f9baa21a-b465f0d83af918cb1dde", "file": "auth/signup.spec.ts", "line": 102, "column": 7}, {"title": "should successfully register new user", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "c60d589c25b5f9baa21a-50803850a72b2b4fbaf3", "file": "auth/signup.spec.ts", "line": 132, "column": 7}, {"title": "should show error for existing email", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "c60d589c25b5f9baa21a-9a65448007e847cd54dd", "file": "auth/signup.spec.ts", "line": 184, "column": 7}, {"title": "should have Google signup option", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "c60d589c25b5f9baa21a-fe98b17025cd0ff4255c", "file": "auth/signup.spec.ts", "line": 220, "column": 7}, {"title": "should have login link for existing users", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "c60d589c25b5f9baa21a-1d3eefc69b418ab69ac1", "file": "auth/signup.spec.ts", "line": 243, "column": 7}, {"title": "should handle terms and conditions checkbox", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "c60d589c25b5f9baa21a-78357a01294a770b10ec", "file": "auth/signup.spec.ts", "line": 267, "column": 7}, {"title": "should display signup form", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "c60d589c25b5f9baa21a-f475fb19c4e5a957ade8", "file": "auth/signup.spec.ts", "line": 12, "column": 7}, {"title": "should show validation errors for empty required fields", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "c60d589c25b5f9baa21a-8915a9037315e6ae9c69", "file": "auth/signup.spec.ts", "line": 44, "column": 7}, {"title": "should show error for invalid email format", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "c60d589c25b5f9baa21a-25b8653d8d52952ee069", "file": "auth/signup.spec.ts", "line": 72, "column": 7}, {"title": "should show error for weak password", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "c60d589c25b5f9baa21a-c6ac3f85b3fa3db650c5", "file": "auth/signup.spec.ts", "line": 102, "column": 7}, {"title": "should successfully register new user", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "c60d589c25b5f9baa21a-f812ed54ec609e9d1665", "file": "auth/signup.spec.ts", "line": 132, "column": 7}, {"title": "should show error for existing email", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "c60d589c25b5f9baa21a-203373c565b5fca88886", "file": "auth/signup.spec.ts", "line": 184, "column": 7}, {"title": "should have Google signup option", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "c60d589c25b5f9baa21a-cb124ba6f19de801b287", "file": "auth/signup.spec.ts", "line": 220, "column": 7}, {"title": "should have login link for existing users", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "c60d589c25b5f9baa21a-fad477fa93dd591c68db", "file": "auth/signup.spec.ts", "line": 243, "column": 7}, {"title": "should handle terms and conditions checkbox", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "c60d589c25b5f9baa21a-544705d1adf33b2cdcf8", "file": "auth/signup.spec.ts", "line": 267, "column": 7}, {"title": "should display signup form", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "c60d589c25b5f9baa21a-3967154c3a8c5720020e", "file": "auth/signup.spec.ts", "line": 12, "column": 7}, {"title": "should show validation errors for empty required fields", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "c60d589c25b5f9baa21a-6c165379bb2df9603e0a", "file": "auth/signup.spec.ts", "line": 44, "column": 7}, {"title": "should show error for invalid email format", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "c60d589c25b5f9baa21a-e8de3d01dcc160c90fb5", "file": "auth/signup.spec.ts", "line": 72, "column": 7}, {"title": "should show error for weak password", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "c60d589c25b5f9baa21a-4b80df563c5d3fdb3d9d", "file": "auth/signup.spec.ts", "line": 102, "column": 7}, {"title": "should successfully register new user", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "c60d589c25b5f9baa21a-6c4c47e47b9cc24a5748", "file": "auth/signup.spec.ts", "line": 132, "column": 7}, {"title": "should show error for existing email", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "c60d589c25b5f9baa21a-86986f47a5b0401377f3", "file": "auth/signup.spec.ts", "line": 184, "column": 7}, {"title": "should have Google signup option", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "c60d589c25b5f9baa21a-95d0bab6a2eec6096355", "file": "auth/signup.spec.ts", "line": 220, "column": 7}, {"title": "should have login link for existing users", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "c60d589c25b5f9baa21a-ed6335848b1ef8a64525", "file": "auth/signup.spec.ts", "line": 243, "column": 7}, {"title": "should handle terms and conditions checkbox", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "c60d589c25b5f9baa21a-8b1ed542d359e1cbd3b1", "file": "auth/signup.spec.ts", "line": 267, "column": 7}, {"title": "should display signup form", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Tablet Chrome", "projectName": "Tablet Chrome", "results": [], "status": "skipped"}], "id": "c60d589c25b5f9baa21a-9ca19a90b46c9bfada3f", "file": "auth/signup.spec.ts", "line": 12, "column": 7}, {"title": "should show validation errors for empty required fields", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Tablet Chrome", "projectName": "Tablet Chrome", "results": [], "status": "skipped"}], "id": "c60d589c25b5f9baa21a-95892a2972e8db6a7958", "file": "auth/signup.spec.ts", "line": 44, "column": 7}, {"title": "should show error for invalid email format", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Tablet Chrome", "projectName": "Tablet Chrome", "results": [], "status": "skipped"}], "id": "c60d589c25b5f9baa21a-da062292f9b13ed6f0d5", "file": "auth/signup.spec.ts", "line": 72, "column": 7}, {"title": "should show error for weak password", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Tablet Chrome", "projectName": "Tablet Chrome", "results": [], "status": "skipped"}], "id": "c60d589c25b5f9baa21a-5153a724245354008e4f", "file": "auth/signup.spec.ts", "line": 102, "column": 7}, {"title": "should successfully register new user", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Tablet Chrome", "projectName": "Tablet Chrome", "results": [], "status": "skipped"}], "id": "c60d589c25b5f9baa21a-73f7026c2b92215fbffa", "file": "auth/signup.spec.ts", "line": 132, "column": 7}, {"title": "should show error for existing email", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Tablet Chrome", "projectName": "Tablet Chrome", "results": [], "status": "skipped"}], "id": "c60d589c25b5f9baa21a-c064ccbdee19a2aed0e5", "file": "auth/signup.spec.ts", "line": 184, "column": 7}, {"title": "should have Google signup option", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Tablet Chrome", "projectName": "Tablet Chrome", "results": [], "status": "skipped"}], "id": "c60d589c25b5f9baa21a-38f04a0b817201405f0a", "file": "auth/signup.spec.ts", "line": 220, "column": 7}, {"title": "should have login link for existing users", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Tablet Chrome", "projectName": "Tablet Chrome", "results": [], "status": "skipped"}], "id": "c60d589c25b5f9baa21a-85d40194a01702f9c3f2", "file": "auth/signup.spec.ts", "line": 243, "column": 7}, {"title": "should handle terms and conditions checkbox", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Tablet Chrome", "projectName": "Tablet Chrome", "results": [], "status": "skipped"}], "id": "c60d589c25b5f9baa21a-fed823fe1b5cb679689d", "file": "auth/signup.spec.ts", "line": 267, "column": 7}, {"title": "should display signup form", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Large", "projectName": "Desktop Large", "results": [], "status": "skipped"}], "id": "c60d589c25b5f9baa21a-9753dd435b60e89f96b9", "file": "auth/signup.spec.ts", "line": 12, "column": 7}, {"title": "should show validation errors for empty required fields", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Large", "projectName": "Desktop Large", "results": [], "status": "skipped"}], "id": "c60d589c25b5f9baa21a-ce69b401ab66739458e8", "file": "auth/signup.spec.ts", "line": 44, "column": 7}, {"title": "should show error for invalid email format", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Large", "projectName": "Desktop Large", "results": [], "status": "skipped"}], "id": "c60d589c25b5f9baa21a-d40e93e0f13e1d870dd5", "file": "auth/signup.spec.ts", "line": 72, "column": 7}, {"title": "should show error for weak password", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Large", "projectName": "Desktop Large", "results": [], "status": "skipped"}], "id": "c60d589c25b5f9baa21a-39e5e657af0969e2c997", "file": "auth/signup.spec.ts", "line": 102, "column": 7}, {"title": "should successfully register new user", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Large", "projectName": "Desktop Large", "results": [], "status": "skipped"}], "id": "c60d589c25b5f9baa21a-85271f7cbc89a863dbb9", "file": "auth/signup.spec.ts", "line": 132, "column": 7}, {"title": "should show error for existing email", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Large", "projectName": "Desktop Large", "results": [], "status": "skipped"}], "id": "c60d589c25b5f9baa21a-859e5e96c7cf33beebff", "file": "auth/signup.spec.ts", "line": 184, "column": 7}, {"title": "should have Google signup option", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Large", "projectName": "Desktop Large", "results": [], "status": "skipped"}], "id": "c60d589c25b5f9baa21a-f845e058b51360e3ae26", "file": "auth/signup.spec.ts", "line": 220, "column": 7}, {"title": "should have login link for existing users", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Large", "projectName": "Desktop Large", "results": [], "status": "skipped"}], "id": "c60d589c25b5f9baa21a-22ebd0e795960642fd0f", "file": "auth/signup.spec.ts", "line": 243, "column": 7}, {"title": "should handle terms and conditions checkbox", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Large", "projectName": "Desktop Large", "results": [], "status": "skipped"}], "id": "c60d589c25b5f9baa21a-4f0d6edf03bccaa71921", "file": "auth/signup.spec.ts", "line": 267, "column": 7}, {"title": "should display signup form", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Small", "projectName": "Desktop Small", "results": [], "status": "skipped"}], "id": "c60d589c25b5f9baa21a-8ba5b4a9d85ca806fbb0", "file": "auth/signup.spec.ts", "line": 12, "column": 7}, {"title": "should show validation errors for empty required fields", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Small", "projectName": "Desktop Small", "results": [], "status": "skipped"}], "id": "c60d589c25b5f9baa21a-a6fbaa00839e9bf47724", "file": "auth/signup.spec.ts", "line": 44, "column": 7}, {"title": "should show error for invalid email format", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Small", "projectName": "Desktop Small", "results": [], "status": "skipped"}], "id": "c60d589c25b5f9baa21a-52c4c5d0d019cf4cc66e", "file": "auth/signup.spec.ts", "line": 72, "column": 7}, {"title": "should show error for weak password", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Small", "projectName": "Desktop Small", "results": [], "status": "skipped"}], "id": "c60d589c25b5f9baa21a-96aa767569a3ca4a859b", "file": "auth/signup.spec.ts", "line": 102, "column": 7}, {"title": "should successfully register new user", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Small", "projectName": "Desktop Small", "results": [], "status": "skipped"}], "id": "c60d589c25b5f9baa21a-0b3f3d370ad4dd9c0426", "file": "auth/signup.spec.ts", "line": 132, "column": 7}, {"title": "should show error for existing email", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Small", "projectName": "Desktop Small", "results": [], "status": "skipped"}], "id": "c60d589c25b5f9baa21a-beba854f7ad8ecb946b9", "file": "auth/signup.spec.ts", "line": 184, "column": 7}, {"title": "should have Google signup option", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Small", "projectName": "Desktop Small", "results": [], "status": "skipped"}], "id": "c60d589c25b5f9baa21a-5a3909532a4bb003e1db", "file": "auth/signup.spec.ts", "line": 220, "column": 7}, {"title": "should have login link for existing users", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Small", "projectName": "Desktop Small", "results": [], "status": "skipped"}], "id": "c60d589c25b5f9baa21a-845245ed89e7162a6660", "file": "auth/signup.spec.ts", "line": 243, "column": 7}, {"title": "should handle terms and conditions checkbox", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Small", "projectName": "Desktop Small", "results": [], "status": "skipped"}], "id": "c60d589c25b5f9baa21a-8fabff98c27c6fd56c31", "file": "auth/signup.spec.ts", "line": 267, "column": 7}]}]}, {"title": "booking/venue-booking.spec.ts", "file": "booking/venue-booking.spec.ts", "column": 0, "line": 0, "specs": [], "suites": [{"title": "Venue Booking", "file": "booking/venue-booking.spec.ts", "line": 5, "column": 6, "specs": [{"title": "should redirect to login when not authenticated", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "d79b60d0ea95eb62d217-b369ccbcb642455ba685", "file": "booking/venue-booking.spec.ts", "line": 17, "column": 7}, {"title": "should display venue booking form when authenticated", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "d79b60d0ea95eb62d217-6ed8be491c2d6daa5fac", "file": "booking/venue-booking.spec.ts", "line": 36, "column": 7}, {"title": "should show validation errors for empty required fields", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "d79b60d0ea95eb62d217-6a321c28ef2b3625ab24", "file": "booking/venue-booking.spec.ts", "line": 61, "column": 7}, {"title": "should show error for past date selection", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "d79b60d0ea95eb62d217-ac62eb6d0080ee5c518c", "file": "booking/venue-booking.spec.ts", "line": 93, "column": 7}, {"title": "should check availability for selected date", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "d79b60d0ea95eb62d217-05da44fbf29275611666", "file": "booking/venue-booking.spec.ts", "line": 131, "column": 7}, {"title": "should successfully submit booking with valid data", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "d79b60d0ea95eb62d217-09f1efadc0e130f68433", "file": "booking/venue-booking.spec.ts", "line": 169, "column": 7}, {"title": "should display venue information on booking page", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "d79b60d0ea95eb62d217-cbda379ca82cdba7bd3d", "file": "booking/venue-booking.spec.ts", "line": 214, "column": 7}, {"title": "should handle guest count validation", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "d79b60d0ea95eb62d217-0e23f406125b7bef9624", "file": "booking/venue-booking.spec.ts", "line": 242, "column": 7}, {"title": "should allow cancellation of booking process", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "d79b60d0ea95eb62d217-f2735e6cd7056c9b6696", "file": "booking/venue-booking.spec.ts", "line": 276, "column": 7}, {"title": "should show booking summary before confirmation", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "d79b60d0ea95eb62d217-56b6bd39365fe085ad98", "file": "booking/venue-booking.spec.ts", "line": 305, "column": 7}, {"title": "should redirect to login when not authenticated", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "d79b60d0ea95eb62d217-2643714e842d1e718d66", "file": "booking/venue-booking.spec.ts", "line": 17, "column": 7}, {"title": "should display venue booking form when authenticated", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "d79b60d0ea95eb62d217-02b49ae319d0a122154a", "file": "booking/venue-booking.spec.ts", "line": 36, "column": 7}, {"title": "should show validation errors for empty required fields", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "d79b60d0ea95eb62d217-d1a10c36cb806a8d530d", "file": "booking/venue-booking.spec.ts", "line": 61, "column": 7}, {"title": "should show error for past date selection", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "d79b60d0ea95eb62d217-092c3c687b8bb7dfe156", "file": "booking/venue-booking.spec.ts", "line": 93, "column": 7}, {"title": "should check availability for selected date", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "d79b60d0ea95eb62d217-6bdcb72c8c50b0ea68a0", "file": "booking/venue-booking.spec.ts", "line": 131, "column": 7}, {"title": "should successfully submit booking with valid data", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "d79b60d0ea95eb62d217-a66f216a76797a06a038", "file": "booking/venue-booking.spec.ts", "line": 169, "column": 7}, {"title": "should display venue information on booking page", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "d79b60d0ea95eb62d217-09c24ed4264579990ee9", "file": "booking/venue-booking.spec.ts", "line": 214, "column": 7}, {"title": "should handle guest count validation", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "d79b60d0ea95eb62d217-ab5fddb3847c06267214", "file": "booking/venue-booking.spec.ts", "line": 242, "column": 7}, {"title": "should allow cancellation of booking process", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "d79b60d0ea95eb62d217-83d21f8150f4fd9be7aa", "file": "booking/venue-booking.spec.ts", "line": 276, "column": 7}, {"title": "should show booking summary before confirmation", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "d79b60d0ea95eb62d217-27c915679c270cf7876f", "file": "booking/venue-booking.spec.ts", "line": 305, "column": 7}, {"title": "should redirect to login when not authenticated", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "d79b60d0ea95eb62d217-a412180af35cb00e3f43", "file": "booking/venue-booking.spec.ts", "line": 17, "column": 7}, {"title": "should display venue booking form when authenticated", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "d79b60d0ea95eb62d217-1af4ceae475814041fc4", "file": "booking/venue-booking.spec.ts", "line": 36, "column": 7}, {"title": "should show validation errors for empty required fields", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "d79b60d0ea95eb62d217-c667e1db31965585bae6", "file": "booking/venue-booking.spec.ts", "line": 61, "column": 7}, {"title": "should show error for past date selection", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "d79b60d0ea95eb62d217-2e5dbab81eab31211a60", "file": "booking/venue-booking.spec.ts", "line": 93, "column": 7}, {"title": "should check availability for selected date", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "d79b60d0ea95eb62d217-9c719137af61e8f7e3e4", "file": "booking/venue-booking.spec.ts", "line": 131, "column": 7}, {"title": "should successfully submit booking with valid data", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "d79b60d0ea95eb62d217-f137ef54a1eb84e88e02", "file": "booking/venue-booking.spec.ts", "line": 169, "column": 7}, {"title": "should display venue information on booking page", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "d79b60d0ea95eb62d217-8472021b326119ab73f3", "file": "booking/venue-booking.spec.ts", "line": 214, "column": 7}, {"title": "should handle guest count validation", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "d79b60d0ea95eb62d217-7632ce2716512d9d59b9", "file": "booking/venue-booking.spec.ts", "line": 242, "column": 7}, {"title": "should allow cancellation of booking process", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "d79b60d0ea95eb62d217-cea6e3862fe956b41963", "file": "booking/venue-booking.spec.ts", "line": 276, "column": 7}, {"title": "should show booking summary before confirmation", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "d79b60d0ea95eb62d217-5d67973d5a4c1d6180c9", "file": "booking/venue-booking.spec.ts", "line": 305, "column": 7}, {"title": "should redirect to login when not authenticated", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "d79b60d0ea95eb62d217-c2577fdfab125e9c4ecf", "file": "booking/venue-booking.spec.ts", "line": 17, "column": 7}, {"title": "should display venue booking form when authenticated", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "d79b60d0ea95eb62d217-fc02b2b87c6b63cc022b", "file": "booking/venue-booking.spec.ts", "line": 36, "column": 7}, {"title": "should show validation errors for empty required fields", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "d79b60d0ea95eb62d217-44a5b69b4f149aacec5f", "file": "booking/venue-booking.spec.ts", "line": 61, "column": 7}, {"title": "should show error for past date selection", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "d79b60d0ea95eb62d217-a9d4e0febded2c35fdb5", "file": "booking/venue-booking.spec.ts", "line": 93, "column": 7}, {"title": "should check availability for selected date", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "d79b60d0ea95eb62d217-7dcc7dbd549496912e75", "file": "booking/venue-booking.spec.ts", "line": 131, "column": 7}, {"title": "should successfully submit booking with valid data", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "d79b60d0ea95eb62d217-35aaaefe0d8fd3126cbc", "file": "booking/venue-booking.spec.ts", "line": 169, "column": 7}, {"title": "should display venue information on booking page", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "d79b60d0ea95eb62d217-c86098c4bbda50782e1f", "file": "booking/venue-booking.spec.ts", "line": 214, "column": 7}, {"title": "should handle guest count validation", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "d79b60d0ea95eb62d217-58eb5bf54938f5d01678", "file": "booking/venue-booking.spec.ts", "line": 242, "column": 7}, {"title": "should allow cancellation of booking process", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "d79b60d0ea95eb62d217-5d8ceba3d50547f7ae48", "file": "booking/venue-booking.spec.ts", "line": 276, "column": 7}, {"title": "should show booking summary before confirmation", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "d79b60d0ea95eb62d217-a0381f8d9cc9c7e3a9f4", "file": "booking/venue-booking.spec.ts", "line": 305, "column": 7}, {"title": "should redirect to login when not authenticated", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "d79b60d0ea95eb62d217-45ce5aa65b0cbf729dec", "file": "booking/venue-booking.spec.ts", "line": 17, "column": 7}, {"title": "should display venue booking form when authenticated", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "d79b60d0ea95eb62d217-00542ccfe7b962c0b134", "file": "booking/venue-booking.spec.ts", "line": 36, "column": 7}, {"title": "should show validation errors for empty required fields", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "d79b60d0ea95eb62d217-0486a468062b9167a83f", "file": "booking/venue-booking.spec.ts", "line": 61, "column": 7}, {"title": "should show error for past date selection", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "d79b60d0ea95eb62d217-bec53488b0857ef9024b", "file": "booking/venue-booking.spec.ts", "line": 93, "column": 7}, {"title": "should check availability for selected date", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "d79b60d0ea95eb62d217-dd108a6837e92c05eb22", "file": "booking/venue-booking.spec.ts", "line": 131, "column": 7}, {"title": "should successfully submit booking with valid data", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "d79b60d0ea95eb62d217-f7093b23c8f8e6bfc4b5", "file": "booking/venue-booking.spec.ts", "line": 169, "column": 7}, {"title": "should display venue information on booking page", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "d79b60d0ea95eb62d217-31f99b08e4437b2d6a8d", "file": "booking/venue-booking.spec.ts", "line": 214, "column": 7}, {"title": "should handle guest count validation", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "d79b60d0ea95eb62d217-85e776c64ce60782892a", "file": "booking/venue-booking.spec.ts", "line": 242, "column": 7}, {"title": "should allow cancellation of booking process", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "d79b60d0ea95eb62d217-171e828e5fa2858c89fd", "file": "booking/venue-booking.spec.ts", "line": 276, "column": 7}, {"title": "should show booking summary before confirmation", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "d79b60d0ea95eb62d217-b08748f611c85474f8ec", "file": "booking/venue-booking.spec.ts", "line": 305, "column": 7}, {"title": "should redirect to login when not authenticated", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Tablet Chrome", "projectName": "Tablet Chrome", "results": [], "status": "skipped"}], "id": "d79b60d0ea95eb62d217-d50ea20a2321526b0d7a", "file": "booking/venue-booking.spec.ts", "line": 17, "column": 7}, {"title": "should display venue booking form when authenticated", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Tablet Chrome", "projectName": "Tablet Chrome", "results": [], "status": "skipped"}], "id": "d79b60d0ea95eb62d217-be9d84e66670bf931fc9", "file": "booking/venue-booking.spec.ts", "line": 36, "column": 7}, {"title": "should show validation errors for empty required fields", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Tablet Chrome", "projectName": "Tablet Chrome", "results": [], "status": "skipped"}], "id": "d79b60d0ea95eb62d217-4bafd3f33314c219cadd", "file": "booking/venue-booking.spec.ts", "line": 61, "column": 7}, {"title": "should show error for past date selection", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Tablet Chrome", "projectName": "Tablet Chrome", "results": [], "status": "skipped"}], "id": "d79b60d0ea95eb62d217-1132acf10200ece94583", "file": "booking/venue-booking.spec.ts", "line": 93, "column": 7}, {"title": "should check availability for selected date", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Tablet Chrome", "projectName": "Tablet Chrome", "results": [], "status": "skipped"}], "id": "d79b60d0ea95eb62d217-ad8b33660cd1183872c3", "file": "booking/venue-booking.spec.ts", "line": 131, "column": 7}, {"title": "should successfully submit booking with valid data", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Tablet Chrome", "projectName": "Tablet Chrome", "results": [], "status": "skipped"}], "id": "d79b60d0ea95eb62d217-a8ba44f684214dc3cbf7", "file": "booking/venue-booking.spec.ts", "line": 169, "column": 7}, {"title": "should display venue information on booking page", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Tablet Chrome", "projectName": "Tablet Chrome", "results": [], "status": "skipped"}], "id": "d79b60d0ea95eb62d217-3caf66977db3d70e7246", "file": "booking/venue-booking.spec.ts", "line": 214, "column": 7}, {"title": "should handle guest count validation", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Tablet Chrome", "projectName": "Tablet Chrome", "results": [], "status": "skipped"}], "id": "d79b60d0ea95eb62d217-cff28e2dec6c54ecd47f", "file": "booking/venue-booking.spec.ts", "line": 242, "column": 7}, {"title": "should allow cancellation of booking process", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Tablet Chrome", "projectName": "Tablet Chrome", "results": [], "status": "skipped"}], "id": "d79b60d0ea95eb62d217-03d77e83ced6b16b43dd", "file": "booking/venue-booking.spec.ts", "line": 276, "column": 7}, {"title": "should show booking summary before confirmation", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Tablet Chrome", "projectName": "Tablet Chrome", "results": [], "status": "skipped"}], "id": "d79b60d0ea95eb62d217-81052bcf621674840899", "file": "booking/venue-booking.spec.ts", "line": 305, "column": 7}, {"title": "should redirect to login when not authenticated", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Large", "projectName": "Desktop Large", "results": [], "status": "skipped"}], "id": "d79b60d0ea95eb62d217-9e38ad5eed977621eb0f", "file": "booking/venue-booking.spec.ts", "line": 17, "column": 7}, {"title": "should display venue booking form when authenticated", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Large", "projectName": "Desktop Large", "results": [], "status": "skipped"}], "id": "d79b60d0ea95eb62d217-f38187618523cbf2d476", "file": "booking/venue-booking.spec.ts", "line": 36, "column": 7}, {"title": "should show validation errors for empty required fields", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Large", "projectName": "Desktop Large", "results": [], "status": "skipped"}], "id": "d79b60d0ea95eb62d217-f82c097122c4e7f25a3a", "file": "booking/venue-booking.spec.ts", "line": 61, "column": 7}, {"title": "should show error for past date selection", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Large", "projectName": "Desktop Large", "results": [], "status": "skipped"}], "id": "d79b60d0ea95eb62d217-65a1066abcd85756ec4f", "file": "booking/venue-booking.spec.ts", "line": 93, "column": 7}, {"title": "should check availability for selected date", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Large", "projectName": "Desktop Large", "results": [], "status": "skipped"}], "id": "d79b60d0ea95eb62d217-09d00562f394b8a8121f", "file": "booking/venue-booking.spec.ts", "line": 131, "column": 7}, {"title": "should successfully submit booking with valid data", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Large", "projectName": "Desktop Large", "results": [], "status": "skipped"}], "id": "d79b60d0ea95eb62d217-8cfe4fc73570001d7c8c", "file": "booking/venue-booking.spec.ts", "line": 169, "column": 7}, {"title": "should display venue information on booking page", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Large", "projectName": "Desktop Large", "results": [], "status": "skipped"}], "id": "d79b60d0ea95eb62d217-13b71b43d2073be5a1ef", "file": "booking/venue-booking.spec.ts", "line": 214, "column": 7}, {"title": "should handle guest count validation", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Large", "projectName": "Desktop Large", "results": [], "status": "skipped"}], "id": "d79b60d0ea95eb62d217-1de7d0b9e7b79c4e2441", "file": "booking/venue-booking.spec.ts", "line": 242, "column": 7}, {"title": "should allow cancellation of booking process", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Large", "projectName": "Desktop Large", "results": [], "status": "skipped"}], "id": "d79b60d0ea95eb62d217-750e938ed54d20ac00a3", "file": "booking/venue-booking.spec.ts", "line": 276, "column": 7}, {"title": "should show booking summary before confirmation", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Large", "projectName": "Desktop Large", "results": [], "status": "skipped"}], "id": "d79b60d0ea95eb62d217-73c68329cdff33568b25", "file": "booking/venue-booking.spec.ts", "line": 305, "column": 7}, {"title": "should redirect to login when not authenticated", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Small", "projectName": "Desktop Small", "results": [], "status": "skipped"}], "id": "d79b60d0ea95eb62d217-de26fe5f368469cff0c5", "file": "booking/venue-booking.spec.ts", "line": 17, "column": 7}, {"title": "should display venue booking form when authenticated", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Small", "projectName": "Desktop Small", "results": [], "status": "skipped"}], "id": "d79b60d0ea95eb62d217-494e735ceeaab1b2317b", "file": "booking/venue-booking.spec.ts", "line": 36, "column": 7}, {"title": "should show validation errors for empty required fields", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Small", "projectName": "Desktop Small", "results": [], "status": "skipped"}], "id": "d79b60d0ea95eb62d217-e2fcb22603b389e042dc", "file": "booking/venue-booking.spec.ts", "line": 61, "column": 7}, {"title": "should show error for past date selection", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Small", "projectName": "Desktop Small", "results": [], "status": "skipped"}], "id": "d79b60d0ea95eb62d217-866e700a95a277746376", "file": "booking/venue-booking.spec.ts", "line": 93, "column": 7}, {"title": "should check availability for selected date", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Small", "projectName": "Desktop Small", "results": [], "status": "skipped"}], "id": "d79b60d0ea95eb62d217-ecb1b3a0480e56052ad8", "file": "booking/venue-booking.spec.ts", "line": 131, "column": 7}, {"title": "should successfully submit booking with valid data", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Small", "projectName": "Desktop Small", "results": [], "status": "skipped"}], "id": "d79b60d0ea95eb62d217-5c9c844b507dad0c7cef", "file": "booking/venue-booking.spec.ts", "line": 169, "column": 7}, {"title": "should display venue information on booking page", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Small", "projectName": "Desktop Small", "results": [], "status": "skipped"}], "id": "d79b60d0ea95eb62d217-5e65b02f768c2838168a", "file": "booking/venue-booking.spec.ts", "line": 214, "column": 7}, {"title": "should handle guest count validation", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Small", "projectName": "Desktop Small", "results": [], "status": "skipped"}], "id": "d79b60d0ea95eb62d217-b35da2faa488116d32fe", "file": "booking/venue-booking.spec.ts", "line": 242, "column": 7}, {"title": "should allow cancellation of booking process", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Small", "projectName": "Desktop Small", "results": [], "status": "skipped"}], "id": "d79b60d0ea95eb62d217-505acb9f1ef06437209d", "file": "booking/venue-booking.spec.ts", "line": 276, "column": 7}, {"title": "should show booking summary before confirmation", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Small", "projectName": "Desktop Small", "results": [], "status": "skipped"}], "id": "d79b60d0ea95eb62d217-495a9c7ab9b8240c85a0", "file": "booking/venue-booking.spec.ts", "line": 305, "column": 7}]}]}, {"title": "ecommerce/shopping-cart.spec.ts", "file": "ecommerce/shopping-cart.spec.ts", "column": 0, "line": 0, "specs": [], "suites": [{"title": "Shopping Cart", "file": "ecommerce/shopping-cart.spec.ts", "line": 5, "column": 6, "specs": [{"title": "should display shop page with products", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "c0b562d05918566207ef-f075d1a1e07f5704343a", "file": "ecommerce/shopping-cart.spec.ts", "line": 22, "column": 7}, {"title": "should add product to cart", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "c0b562d05918566207ef-0801d790e50b643d0ff2", "file": "ecommerce/shopping-cart.spec.ts", "line": 47, "column": 7}, {"title": "should display cart items", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "c0b562d05918566207ef-aa70fafd57a6a2da5212", "file": "ecommerce/shopping-cart.spec.ts", "line": 61, "column": 7}, {"title": "should update product quantity in cart", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "c0b562d05918566207ef-89e96b2326baf4c28105", "file": "ecommerce/shopping-cart.spec.ts", "line": 91, "column": 7}, {"title": "should remove product from cart", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "c0b562d05918566207ef-578ea8bbe2e801e3fc76", "file": "ecommerce/shopping-cart.spec.ts", "line": 110, "column": 7}, {"title": "should calculate total price correctly", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "c0b562d05918566207ef-44cfde087bf07bd7a9b1", "file": "ecommerce/shopping-cart.spec.ts", "line": 127, "column": 7}, {"title": "should clear entire cart", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "c0b562d05918566207ef-6ae87c1f11f911812dc3", "file": "ecommerce/shopping-cart.spec.ts", "line": 162, "column": 7}, {"title": "should show empty cart message when no items", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "c0b562d05918566207ef-6339cfa44b1652f5d5cf", "file": "ecommerce/shopping-cart.spec.ts", "line": 176, "column": 7}, {"title": "should persist cart items across page navigation", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "c0b562d05918566207ef-13ba1a097b69fa4f90e0", "file": "ecommerce/shopping-cart.spec.ts", "line": 202, "column": 7}, {"title": "should show product details in cart", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "c0b562d05918566207ef-01c22b7bbc5640a57dd1", "file": "ecommerce/shopping-cart.spec.ts", "line": 219, "column": 7}, {"title": "should handle quantity limits", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "c0b562d05918566207ef-01cf924e35f76be8acae", "file": "ecommerce/shopping-cart.spec.ts", "line": 246, "column": 7}, {"title": "should show continue shopping option", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "c0b562d05918566207ef-626a5ea52c0e5c91801f", "file": "ecommerce/shopping-cart.spec.ts", "line": 283, "column": 7}, {"title": "should display shop page with products", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "c0b562d05918566207ef-1ac6050338ccf4cd1493", "file": "ecommerce/shopping-cart.spec.ts", "line": 22, "column": 7}, {"title": "should add product to cart", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "c0b562d05918566207ef-3acad74fbd061ba1dfa3", "file": "ecommerce/shopping-cart.spec.ts", "line": 47, "column": 7}, {"title": "should display cart items", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "c0b562d05918566207ef-e2ca8b35f85e982fa187", "file": "ecommerce/shopping-cart.spec.ts", "line": 61, "column": 7}, {"title": "should update product quantity in cart", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "c0b562d05918566207ef-86524abce052187a7f29", "file": "ecommerce/shopping-cart.spec.ts", "line": 91, "column": 7}, {"title": "should remove product from cart", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "c0b562d05918566207ef-37e516a9b83964b8eba3", "file": "ecommerce/shopping-cart.spec.ts", "line": 110, "column": 7}, {"title": "should calculate total price correctly", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "c0b562d05918566207ef-0e3b61e7b6f90316d124", "file": "ecommerce/shopping-cart.spec.ts", "line": 127, "column": 7}, {"title": "should clear entire cart", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "c0b562d05918566207ef-aaeef92f0a00d5145456", "file": "ecommerce/shopping-cart.spec.ts", "line": 162, "column": 7}, {"title": "should show empty cart message when no items", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "c0b562d05918566207ef-69d2ff0cca0f0bfc33b4", "file": "ecommerce/shopping-cart.spec.ts", "line": 176, "column": 7}, {"title": "should persist cart items across page navigation", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "c0b562d05918566207ef-8479cc019a5774d2be48", "file": "ecommerce/shopping-cart.spec.ts", "line": 202, "column": 7}, {"title": "should show product details in cart", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "c0b562d05918566207ef-88eb6480874719d5008f", "file": "ecommerce/shopping-cart.spec.ts", "line": 219, "column": 7}, {"title": "should handle quantity limits", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "c0b562d05918566207ef-3c1dcff98795bdf22bc2", "file": "ecommerce/shopping-cart.spec.ts", "line": 246, "column": 7}, {"title": "should show continue shopping option", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "c0b562d05918566207ef-e66e83c3c112d35ce816", "file": "ecommerce/shopping-cart.spec.ts", "line": 283, "column": 7}, {"title": "should display shop page with products", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "c0b562d05918566207ef-f0fd78f09b6d053241cc", "file": "ecommerce/shopping-cart.spec.ts", "line": 22, "column": 7}, {"title": "should add product to cart", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "c0b562d05918566207ef-5469f647a2a8f65882d0", "file": "ecommerce/shopping-cart.spec.ts", "line": 47, "column": 7}, {"title": "should display cart items", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "c0b562d05918566207ef-33c39b7136c13cec324e", "file": "ecommerce/shopping-cart.spec.ts", "line": 61, "column": 7}, {"title": "should update product quantity in cart", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "c0b562d05918566207ef-f48483c3f81d364b5550", "file": "ecommerce/shopping-cart.spec.ts", "line": 91, "column": 7}, {"title": "should remove product from cart", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "c0b562d05918566207ef-2bbc4e298a184d6a6ee8", "file": "ecommerce/shopping-cart.spec.ts", "line": 110, "column": 7}, {"title": "should calculate total price correctly", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "c0b562d05918566207ef-6675ff57fa037b293a0b", "file": "ecommerce/shopping-cart.spec.ts", "line": 127, "column": 7}, {"title": "should clear entire cart", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "c0b562d05918566207ef-2e39fa2607fe3ab8644d", "file": "ecommerce/shopping-cart.spec.ts", "line": 162, "column": 7}, {"title": "should show empty cart message when no items", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "c0b562d05918566207ef-02626b41b7aaa83de79e", "file": "ecommerce/shopping-cart.spec.ts", "line": 176, "column": 7}, {"title": "should persist cart items across page navigation", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "c0b562d05918566207ef-fda78dbdb105b4650f9b", "file": "ecommerce/shopping-cart.spec.ts", "line": 202, "column": 7}, {"title": "should show product details in cart", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "c0b562d05918566207ef-6f40929f48c4ea08ad7e", "file": "ecommerce/shopping-cart.spec.ts", "line": 219, "column": 7}, {"title": "should handle quantity limits", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "c0b562d05918566207ef-b45644e421613830d902", "file": "ecommerce/shopping-cart.spec.ts", "line": 246, "column": 7}, {"title": "should show continue shopping option", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "c0b562d05918566207ef-f0465e8b041d528ea727", "file": "ecommerce/shopping-cart.spec.ts", "line": 283, "column": 7}, {"title": "should display shop page with products", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "c0b562d05918566207ef-dc090273fc78987ae68a", "file": "ecommerce/shopping-cart.spec.ts", "line": 22, "column": 7}, {"title": "should add product to cart", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "c0b562d05918566207ef-9c1e2cfd198fba94c62d", "file": "ecommerce/shopping-cart.spec.ts", "line": 47, "column": 7}, {"title": "should display cart items", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "c0b562d05918566207ef-ccab246bb51d19d4228d", "file": "ecommerce/shopping-cart.spec.ts", "line": 61, "column": 7}, {"title": "should update product quantity in cart", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "c0b562d05918566207ef-aecb032278fa8faf88e7", "file": "ecommerce/shopping-cart.spec.ts", "line": 91, "column": 7}, {"title": "should remove product from cart", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "c0b562d05918566207ef-cd68540824bc654863e5", "file": "ecommerce/shopping-cart.spec.ts", "line": 110, "column": 7}, {"title": "should calculate total price correctly", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "c0b562d05918566207ef-2f4e19f498a2056f2e39", "file": "ecommerce/shopping-cart.spec.ts", "line": 127, "column": 7}, {"title": "should clear entire cart", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "c0b562d05918566207ef-5eeb20f893aa277afa12", "file": "ecommerce/shopping-cart.spec.ts", "line": 162, "column": 7}, {"title": "should show empty cart message when no items", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "c0b562d05918566207ef-a246931d224e1d9f79a3", "file": "ecommerce/shopping-cart.spec.ts", "line": 176, "column": 7}, {"title": "should persist cart items across page navigation", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "c0b562d05918566207ef-b026a0cf1bd755569942", "file": "ecommerce/shopping-cart.spec.ts", "line": 202, "column": 7}, {"title": "should show product details in cart", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "c0b562d05918566207ef-48392e83c76a60e7d84c", "file": "ecommerce/shopping-cart.spec.ts", "line": 219, "column": 7}, {"title": "should handle quantity limits", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "c0b562d05918566207ef-875dfad74b0a68c9fd1d", "file": "ecommerce/shopping-cart.spec.ts", "line": 246, "column": 7}, {"title": "should show continue shopping option", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "c0b562d05918566207ef-9fae5f9175c3164086cf", "file": "ecommerce/shopping-cart.spec.ts", "line": 283, "column": 7}, {"title": "should display shop page with products", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "c0b562d05918566207ef-589ef6d2f588a291a2d5", "file": "ecommerce/shopping-cart.spec.ts", "line": 22, "column": 7}, {"title": "should add product to cart", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "c0b562d05918566207ef-3c91ef0418ba8e8ad2f5", "file": "ecommerce/shopping-cart.spec.ts", "line": 47, "column": 7}, {"title": "should display cart items", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "c0b562d05918566207ef-1e5b2d60414056fc9d7e", "file": "ecommerce/shopping-cart.spec.ts", "line": 61, "column": 7}, {"title": "should update product quantity in cart", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "c0b562d05918566207ef-2fb8b923120afe95cb5d", "file": "ecommerce/shopping-cart.spec.ts", "line": 91, "column": 7}, {"title": "should remove product from cart", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "c0b562d05918566207ef-cc7758a5d8618468cec0", "file": "ecommerce/shopping-cart.spec.ts", "line": 110, "column": 7}, {"title": "should calculate total price correctly", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "c0b562d05918566207ef-9d91bfd8cfd0d9f2436c", "file": "ecommerce/shopping-cart.spec.ts", "line": 127, "column": 7}, {"title": "should clear entire cart", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "c0b562d05918566207ef-d46527eefac57b6ebbf0", "file": "ecommerce/shopping-cart.spec.ts", "line": 162, "column": 7}, {"title": "should show empty cart message when no items", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "c0b562d05918566207ef-0acc23c5a4ff1adc291d", "file": "ecommerce/shopping-cart.spec.ts", "line": 176, "column": 7}, {"title": "should persist cart items across page navigation", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "c0b562d05918566207ef-d2ca7b376fd3e3289c4a", "file": "ecommerce/shopping-cart.spec.ts", "line": 202, "column": 7}, {"title": "should show product details in cart", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "c0b562d05918566207ef-27bc1e408c5d86db1246", "file": "ecommerce/shopping-cart.spec.ts", "line": 219, "column": 7}, {"title": "should handle quantity limits", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "c0b562d05918566207ef-18c5b88aa46a27edaccf", "file": "ecommerce/shopping-cart.spec.ts", "line": 246, "column": 7}, {"title": "should show continue shopping option", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "c0b562d05918566207ef-4dd2ca5f1b19e77ff795", "file": "ecommerce/shopping-cart.spec.ts", "line": 283, "column": 7}, {"title": "should display shop page with products", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Tablet Chrome", "projectName": "Tablet Chrome", "results": [], "status": "skipped"}], "id": "c0b562d05918566207ef-7818470c2a79c9ecc6b1", "file": "ecommerce/shopping-cart.spec.ts", "line": 22, "column": 7}, {"title": "should add product to cart", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Tablet Chrome", "projectName": "Tablet Chrome", "results": [], "status": "skipped"}], "id": "c0b562d05918566207ef-1cea3538b4d831d12158", "file": "ecommerce/shopping-cart.spec.ts", "line": 47, "column": 7}, {"title": "should display cart items", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Tablet Chrome", "projectName": "Tablet Chrome", "results": [], "status": "skipped"}], "id": "c0b562d05918566207ef-6f64ded035f95fcad541", "file": "ecommerce/shopping-cart.spec.ts", "line": 61, "column": 7}, {"title": "should update product quantity in cart", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Tablet Chrome", "projectName": "Tablet Chrome", "results": [], "status": "skipped"}], "id": "c0b562d05918566207ef-b2e334643d24e1214942", "file": "ecommerce/shopping-cart.spec.ts", "line": 91, "column": 7}, {"title": "should remove product from cart", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Tablet Chrome", "projectName": "Tablet Chrome", "results": [], "status": "skipped"}], "id": "c0b562d05918566207ef-0ff5805a1521d6638cf9", "file": "ecommerce/shopping-cart.spec.ts", "line": 110, "column": 7}, {"title": "should calculate total price correctly", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Tablet Chrome", "projectName": "Tablet Chrome", "results": [], "status": "skipped"}], "id": "c0b562d05918566207ef-f97955f57901985389a7", "file": "ecommerce/shopping-cart.spec.ts", "line": 127, "column": 7}, {"title": "should clear entire cart", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Tablet Chrome", "projectName": "Tablet Chrome", "results": [], "status": "skipped"}], "id": "c0b562d05918566207ef-a8e3fcdf1b8cd4c102fb", "file": "ecommerce/shopping-cart.spec.ts", "line": 162, "column": 7}, {"title": "should show empty cart message when no items", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Tablet Chrome", "projectName": "Tablet Chrome", "results": [], "status": "skipped"}], "id": "c0b562d05918566207ef-8df5b4b47c0c7e35e657", "file": "ecommerce/shopping-cart.spec.ts", "line": 176, "column": 7}, {"title": "should persist cart items across page navigation", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Tablet Chrome", "projectName": "Tablet Chrome", "results": [], "status": "skipped"}], "id": "c0b562d05918566207ef-425c9ecb02c255dc8c61", "file": "ecommerce/shopping-cart.spec.ts", "line": 202, "column": 7}, {"title": "should show product details in cart", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Tablet Chrome", "projectName": "Tablet Chrome", "results": [], "status": "skipped"}], "id": "c0b562d05918566207ef-5dbf7e98735b01b8fed7", "file": "ecommerce/shopping-cart.spec.ts", "line": 219, "column": 7}, {"title": "should handle quantity limits", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Tablet Chrome", "projectName": "Tablet Chrome", "results": [], "status": "skipped"}], "id": "c0b562d05918566207ef-fe48f174ff2265f02bf8", "file": "ecommerce/shopping-cart.spec.ts", "line": 246, "column": 7}, {"title": "should show continue shopping option", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Tablet Chrome", "projectName": "Tablet Chrome", "results": [], "status": "skipped"}], "id": "c0b562d05918566207ef-9dc6a03a523816ac9e52", "file": "ecommerce/shopping-cart.spec.ts", "line": 283, "column": 7}, {"title": "should display shop page with products", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Large", "projectName": "Desktop Large", "results": [], "status": "skipped"}], "id": "c0b562d05918566207ef-b5dfcce39b1ed74bea6f", "file": "ecommerce/shopping-cart.spec.ts", "line": 22, "column": 7}, {"title": "should add product to cart", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Large", "projectName": "Desktop Large", "results": [], "status": "skipped"}], "id": "c0b562d05918566207ef-484177b6dc534e853f85", "file": "ecommerce/shopping-cart.spec.ts", "line": 47, "column": 7}, {"title": "should display cart items", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Large", "projectName": "Desktop Large", "results": [], "status": "skipped"}], "id": "c0b562d05918566207ef-224a1345891046267823", "file": "ecommerce/shopping-cart.spec.ts", "line": 61, "column": 7}, {"title": "should update product quantity in cart", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Large", "projectName": "Desktop Large", "results": [], "status": "skipped"}], "id": "c0b562d05918566207ef-56b774f35c2bacd6e039", "file": "ecommerce/shopping-cart.spec.ts", "line": 91, "column": 7}, {"title": "should remove product from cart", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Large", "projectName": "Desktop Large", "results": [], "status": "skipped"}], "id": "c0b562d05918566207ef-34fa24b8d0178e175f4a", "file": "ecommerce/shopping-cart.spec.ts", "line": 110, "column": 7}, {"title": "should calculate total price correctly", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Large", "projectName": "Desktop Large", "results": [], "status": "skipped"}], "id": "c0b562d05918566207ef-586f9c177f323fc26012", "file": "ecommerce/shopping-cart.spec.ts", "line": 127, "column": 7}, {"title": "should clear entire cart", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Large", "projectName": "Desktop Large", "results": [], "status": "skipped"}], "id": "c0b562d05918566207ef-2091b8e689cc65ae2fff", "file": "ecommerce/shopping-cart.spec.ts", "line": 162, "column": 7}, {"title": "should show empty cart message when no items", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Large", "projectName": "Desktop Large", "results": [], "status": "skipped"}], "id": "c0b562d05918566207ef-62f6a42491f3a3d1afdc", "file": "ecommerce/shopping-cart.spec.ts", "line": 176, "column": 7}, {"title": "should persist cart items across page navigation", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Large", "projectName": "Desktop Large", "results": [], "status": "skipped"}], "id": "c0b562d05918566207ef-22aea73fbdbb4ec3cb27", "file": "ecommerce/shopping-cart.spec.ts", "line": 202, "column": 7}, {"title": "should show product details in cart", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Large", "projectName": "Desktop Large", "results": [], "status": "skipped"}], "id": "c0b562d05918566207ef-5db7895085481323f1a5", "file": "ecommerce/shopping-cart.spec.ts", "line": 219, "column": 7}, {"title": "should handle quantity limits", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Large", "projectName": "Desktop Large", "results": [], "status": "skipped"}], "id": "c0b562d05918566207ef-756802a2345cfe498af6", "file": "ecommerce/shopping-cart.spec.ts", "line": 246, "column": 7}, {"title": "should show continue shopping option", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Large", "projectName": "Desktop Large", "results": [], "status": "skipped"}], "id": "c0b562d05918566207ef-8d2cb944440775d4a758", "file": "ecommerce/shopping-cart.spec.ts", "line": 283, "column": 7}, {"title": "should display shop page with products", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Small", "projectName": "Desktop Small", "results": [], "status": "skipped"}], "id": "c0b562d05918566207ef-afea6e862286adfcb2be", "file": "ecommerce/shopping-cart.spec.ts", "line": 22, "column": 7}, {"title": "should add product to cart", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Small", "projectName": "Desktop Small", "results": [], "status": "skipped"}], "id": "c0b562d05918566207ef-14c4b4e26a1f032e5826", "file": "ecommerce/shopping-cart.spec.ts", "line": 47, "column": 7}, {"title": "should display cart items", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Small", "projectName": "Desktop Small", "results": [], "status": "skipped"}], "id": "c0b562d05918566207ef-01e256c7a0e66fbeb0e2", "file": "ecommerce/shopping-cart.spec.ts", "line": 61, "column": 7}, {"title": "should update product quantity in cart", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Small", "projectName": "Desktop Small", "results": [], "status": "skipped"}], "id": "c0b562d05918566207ef-55b6a16cc23133819759", "file": "ecommerce/shopping-cart.spec.ts", "line": 91, "column": 7}, {"title": "should remove product from cart", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Small", "projectName": "Desktop Small", "results": [], "status": "skipped"}], "id": "c0b562d05918566207ef-887ed91b839bbf66a426", "file": "ecommerce/shopping-cart.spec.ts", "line": 110, "column": 7}, {"title": "should calculate total price correctly", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Small", "projectName": "Desktop Small", "results": [], "status": "skipped"}], "id": "c0b562d05918566207ef-5ff89e8b6a49ff8a3b54", "file": "ecommerce/shopping-cart.spec.ts", "line": 127, "column": 7}, {"title": "should clear entire cart", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Small", "projectName": "Desktop Small", "results": [], "status": "skipped"}], "id": "c0b562d05918566207ef-a4718774cd611279b444", "file": "ecommerce/shopping-cart.spec.ts", "line": 162, "column": 7}, {"title": "should show empty cart message when no items", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Small", "projectName": "Desktop Small", "results": [], "status": "skipped"}], "id": "c0b562d05918566207ef-ac356fc0fcca7d8d8c3e", "file": "ecommerce/shopping-cart.spec.ts", "line": 176, "column": 7}, {"title": "should persist cart items across page navigation", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Small", "projectName": "Desktop Small", "results": [], "status": "skipped"}], "id": "c0b562d05918566207ef-fdc6ce7e88a0ed464c24", "file": "ecommerce/shopping-cart.spec.ts", "line": 202, "column": 7}, {"title": "should show product details in cart", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Small", "projectName": "Desktop Small", "results": [], "status": "skipped"}], "id": "c0b562d05918566207ef-06f209f6c8a5dc4287d5", "file": "ecommerce/shopping-cart.spec.ts", "line": 219, "column": 7}, {"title": "should handle quantity limits", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Small", "projectName": "Desktop Small", "results": [], "status": "skipped"}], "id": "c0b562d05918566207ef-df1b1b2aa6e529d977eb", "file": "ecommerce/shopping-cart.spec.ts", "line": 246, "column": 7}, {"title": "should show continue shopping option", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Small", "projectName": "Desktop Small", "results": [], "status": "skipped"}], "id": "c0b562d05918566207ef-561b87cf6ad5d313d392", "file": "ecommerce/shopping-cart.spec.ts", "line": 283, "column": 7}]}]}], "errors": [], "stats": {"startTime": "2025-08-01T13:32:21.760Z", "duration": 183.08799999999997, "expected": 0, "skipped": 368, "unexpected": 0, "flaky": 0}}