"use client"

import React, { Suspense } from 'react'
import { Loader2 } from 'lucide-react'

interface DashboardPageWrapperProps {
  children: React.ReactNode
  title?: string
  description?: string
  loading?: boolean
}

function DashboardPageSkeleton({ title }: { title?: string }) {
  return (
    <div className="space-y-6">
      {title && (
        <div className="space-y-2">
          <div className="h-8 bg-gray-200 rounded w-1/3 animate-pulse"></div>
          <div className="h-4 bg-gray-200 rounded w-1/2 animate-pulse"></div>
        </div>
      )}
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {[...Array(6)].map((_, i) => (
          <div key={i} className="bg-white rounded-lg border p-6 space-y-4">
            <div className="h-4 bg-gray-200 rounded w-3/4 animate-pulse"></div>
            <div className="h-4 bg-gray-200 rounded w-1/2 animate-pulse"></div>
            <div className="h-8 bg-gray-200 rounded animate-pulse"></div>
          </div>
        ))}
      </div>
    </div>
  )
}

function LoadingSpinner({ title }: { title?: string }) {
  return (
    <div className="flex flex-col items-center justify-center min-h-[400px] space-y-4">
      <Loader2 className="h-8 w-8 animate-spin text-primary" />
      <p className="text-gray-600">
        {title ? `Loading ${title.toLowerCase()}...` : 'Loading...'}
      </p>
    </div>
  )
}

export function DashboardPageWrapper({ 
  children, 
  title, 
  description, 
  loading = false 
}: DashboardPageWrapperProps) {
  if (loading) {
    return <LoadingSpinner title={title} />
  }

  return (
    <Suspense fallback={<DashboardPageSkeleton title={title} />}>
      <div className="space-y-6">
        {(title || description) && (
          <div className="space-y-2">
            {title && (
              <h1 className="text-2xl font-bold text-gray-900">{title}</h1>
            )}
            {description && (
              <p className="text-gray-600">{description}</p>
            )}
          </div>
        )}
        {children}
      </div>
    </Suspense>
  )
}

// Hook for optimized data loading
export function useDashboardData<T>(
  fetchFn: () => Promise<T>,
  dependencies: any[] = []
) {
  const [data, setData] = React.useState<T | null>(null)
  const [loading, setLoading] = React.useState(true)
  const [error, setError] = React.useState<string | null>(null)

  React.useEffect(() => {
    let isMounted = true
    
    const loadData = async () => {
      try {
        setLoading(true)
        setError(null)
        const result = await fetchFn()
        
        if (isMounted) {
          setData(result)
        }
      } catch (err) {
        if (isMounted) {
          setError(err instanceof Error ? err.message : 'An error occurred')
        }
      } finally {
        if (isMounted) {
          setLoading(false)
        }
      }
    }

    loadData()

    return () => {
      isMounted = false
    }
  }, dependencies)

  return { data, loading, error, refetch: () => fetchFn() }
}

export default DashboardPageWrapper
