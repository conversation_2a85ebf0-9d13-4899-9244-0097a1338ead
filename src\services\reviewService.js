import { generateClient } from 'aws-amplify/api';
import { createReview, updateReview, deleteReview } from '../graphql/mutations';
import { getReview, listReviews } from '../graphql/queries';

const client = generateClient();

/**
 * Service for managing reviews
 */
export class ReviewService {
  /**
   * Create a new review
   */
  static async createReview(reviewData) {
    try {
      console.log('Creating review with data:', reviewData);

      // Validate required fields
      const requiredFields = ['name', 'email', 'location', 'weddingDate', 'category', 'rating', 'title', 'review', 'userId'];
      const missingFields = [];

      for (const field of requiredFields) {
        if (!reviewData[field] || reviewData[field] === '' || reviewData[field] === null || reviewData[field] === undefined) {
          missingFields.push(field);
        }
      }

      if (missingFields.length > 0) {
        throw new Error(`Missing required fields: ${missingFields.join(', ')}`);
      }

      // Validate rating
      if (reviewData.rating < 1 || reviewData.rating > 5) {
        throw new Error('Rating must be between 1 and 5');
      }

      // Prepare review data for GraphQL
      const reviewInput = {
        userId: reviewData.userId,
        name: reviewData.name.trim(),
        email: reviewData.email.trim().toLowerCase(),
        location: reviewData.location.trim(),
        weddingDate: reviewData.weddingDate,
        category: reviewData.category,
        rating: parseInt(reviewData.rating),
        title: reviewData.title.trim(),
        review: reviewData.review.trim(),
        wouldRecommend: Boolean(reviewData.wouldRecommend),
        verified: false,
        status: 'PENDING', // Reviews need approval by default
        helpfulCount: 0,
        purchaseVerified: false,
        reviewHelpfulUsers: []
      };

      // Add optional fields if provided
      if (reviewData.vendorId) {
        reviewInput.vendorId = reviewData.vendorId;
      }
      if (reviewData.venueId) {
        reviewInput.venueId = reviewData.venueId;
      }
      if (reviewData.productId) {
        reviewInput.productId = reviewData.productId;
      }
      if (reviewData.serviceType) {
        reviewInput.serviceType = reviewData.serviceType;
      }
      // Determine reviewTarget based on category
      let reviewTarget = 'VENDOR'; // Default to VENDOR
      if (reviewInput.category === 'PLATFORM') {
        reviewTarget = 'ADMIN'; // Platform reviews go to admin dashboard
      } else {
        reviewTarget = 'VENDOR'; // Product/service reviews go to vendor dashboard
      }

      // Add reviewTarget and entity fields
      const finalReviewInput = {
        ...reviewInput,
        reviewTarget,
        entityType: reviewData.entityType || null,
        entityId: reviewData.entityId || null,
        userEntityComposite: reviewData.userEntityComposite || null,
        adminNotes: null,
        moderatedBy: null,
        moderatedAt: null
      };

      const result = await client.graphql({
        query: createReview,
        variables: {
          input: finalReviewInput
        }
      });

      console.log('Review created successfully:', result);

      return {
        success: true,
        data: result.data.createReview,
        message: 'Review submitted successfully! It will be published after approval.'
      };

    } catch (error) {
      console.error('Error creating review:', error);
      
      let errorMessage = 'Failed to submit review';
      
      if (error.message) {
        errorMessage = error.message;
      } else if (error.errors && error.errors.length > 0) {
        errorMessage = error.errors[0].message;
      }

      return {
        success: false,
        error: errorMessage
      };
    }
  }

  /**
   * Get a single review by ID
   */
  static async getReview(id) {
    try {
      const result = await client.graphql({
        query: getReview,
        variables: { id }
      });

      return {
        success: true,
        data: result.data.getReview
      };
    } catch (error) {
      console.error('Error getting review:', error);
      return {
        success: false,
        error: error.message || 'Failed to get review'
      };
    }
  }

  /**
   * List all reviews with pagination
   */
  static async listReviews(options = {}) {
    try {
      const { limit = 20, nextToken = null } = options;

      const result = await client.graphql({
        query: listReviews,
        variables: {
          limit,
          nextToken
        }
      });

      return {
        success: true,
        data: result.data.listReviews.items,
        nextToken: result.data.listReviews.nextToken
      };
    } catch (error) {
      console.error('Error listing reviews:', error);
      return {
        success: false,
        error: error.message || 'Failed to list reviews'
      };
    }
  }

  /**
   * List platform reviews (general reviews, not product/vendor specific)
   */
  static async listPlatformReviews(options = {}) {
    try {
      const { limit = 20, nextToken = null } = options;

      // Filter for platform reviews (no vendorId, venueId, or productId)
      const result = await client.graphql({
        query: listReviews,
        variables: {
          filter: {
            and: [
              { isPublished: { eq: true } },
              { isApproved: { eq: true } },
              { vendorId: { attributeExists: false } },
              { venueId: { attributeExists: false } },
              { productId: { attributeExists: false } }
            ]
          },
          limit,
          nextToken
        }
      });

      return {
        success: true,
        data: result.data.listReviews.items,
        nextToken: result.data.listReviews.nextToken
      };
    } catch (error) {
      console.error('Error listing platform reviews:', error);
      return {
        success: false,
        error: error.message || 'Failed to list platform reviews'
      };
    }
  }

  /**
   * Get reviews by category
   */
  static async getReviewsByCategory(category, options = {}) {
    try {
      const { limit = 20, nextToken = null } = options;

      const result = await client.graphql({
        query: listReviews,
        variables: {
          filter: {
            and: [
              { category: { eq: category } },
              { isPublished: { eq: true } },
              { isApproved: { eq: true } }
            ]
          },
          limit,
          nextToken
        }
      });

      return {
        success: true,
        data: result.data.listReviews.items,
        nextToken: result.data.listReviews.nextToken
      };
    } catch (error) {
      console.error('Error getting reviews by category:', error);
      return {
        success: false,
        error: error.message || 'Failed to get reviews by category'
      };
    }
  }

  /**
   * Get reviews by minimum rating
   */
  static async getReviewsByRating(minRating, options = {}) {
    try {
      const { limit = 20, nextToken = null } = options;

      const result = await client.graphql({
        query: listReviews,
        variables: {
          filter: {
            and: [
              { rating: { gte: minRating } },
              { isPublished: { eq: true } },
              { isApproved: { eq: true } }
            ]
          },
          limit,
          nextToken
        }
      });

      return {
        success: true,
        data: result.data.listReviews.items,
        nextToken: result.data.listReviews.nextToken
      };
    } catch (error) {
      console.error('Error getting reviews by rating:', error);
      return {
        success: false,
        error: error.message || 'Failed to get reviews by rating'
      };
    }
  }

  /**
   * Get review statistics
   */
  static async getReviewStats() {
    try {
      // Get all published reviews for statistics
      const result = await client.graphql({
        query: listReviews,
        variables: {
          filter: {
            and: [
              { isPublished: { eq: true } },
              { isApproved: { eq: true } }
            ]
          },
          limit: 1000 // Get a large number for stats calculation
        }
      });

      const reviews = result.data.listReviews.items;
      const totalReviews = reviews.length;

      if (totalReviews === 0) {
        return {
          success: true,
          data: {
            totalReviews: 0,
            averageRating: 0,
            satisfactionRate: 0,
            recommendationRate: 0,
            ratingDistribution: { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 }
          }
        };
      }

      // Calculate statistics
      const ratingSum = reviews.reduce((sum, review) => sum + review.rating, 0);
      const averageRating = ratingSum / totalReviews;

      const ratingDistribution = { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 };
      reviews.forEach(review => {
        ratingDistribution[review.rating]++;
      });

      const satisfactionRate = ((ratingDistribution[4] + ratingDistribution[5]) / totalReviews) * 100;

      // Calculate recommendation rate (if wouldRecommend field exists)
      const reviewsWithRecommendation = reviews.filter(r => r.wouldRecommend !== undefined && r.wouldRecommend !== null);
      const recommendationRate = reviewsWithRecommendation.length > 0 
        ? (reviewsWithRecommendation.filter(r => r.wouldRecommend).length / reviewsWithRecommendation.length) * 100
        : 0;

      return {
        success: true,
        data: {
          totalReviews,
          averageRating: Math.round(averageRating * 10) / 10,
          satisfactionRate: Math.round(satisfactionRate),
          recommendationRate: Math.round(recommendationRate),
          ratingDistribution
        }
      };

    } catch (error) {
      console.error('Error getting review stats:', error);
      return {
        success: false,
        error: error.message || 'Failed to get review statistics'
      };
    }
  }

  /**
   * Update a review (admin only)
   */
  static async updateReview(id, updateData) {
    try {
      const result = await client.graphql({
        query: updateReview,
        variables: {
          input: {
            id,
            ...updateData,
            updatedAt: new Date().toISOString()
          }
        }
      });

      return {
        success: true,
        data: result.data.updateReview
      };
    } catch (error) {
      console.error('Error updating review:', error);
      return {
        success: false,
        error: error.message || 'Failed to update review'
      };
    }
  }

  /**
   * Delete a review (admin only)
   */
  static async deleteReview(id) {
    try {
      const result = await client.graphql({
        query: deleteReview,
        variables: { input: { id } }
      });

      return {
        success: true,
        data: result.data.deleteReview
      };
    } catch (error) {
      console.error('Error deleting review:', error);
      return {
        success: false,
        error: error.message || 'Failed to delete review'
      };
    }
  }
}
