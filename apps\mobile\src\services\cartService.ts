import AsyncStorage from '@react-native-async-storage/async-storage';
import { CartItem, Cart } from '../shared/types';

class CartService {
  private storageKey = 'thirumanam-cart';

  async loadCart(): Promise<Cart> {
    try {
      const cartData = await AsyncStorage.getItem(this.storageKey);
      if (cartData) {
        const cart = JSON.parse(cartData);
        return {
          items: cart.items || [],
          total: cart.total || 0,
          itemCount: cart.itemCount || 0,
          lastUpdated: cart.lastUpdated || new Date().toISOString(),
        };
      }
    } catch (error) {
      console.error('Error loading cart:', error);
    }

    return {
      items: [],
      total: 0,
      itemCount: 0,
      lastUpdated: new Date().toISOString(),
    };
  }

  async saveCart(cart: Cart): Promise<void> {
    try {
      const cartData = {
        ...cart,
        lastUpdated: new Date().toISOString(),
      };
      await AsyncStorage.setItem(this.storageKey, JSON.stringify(cartData));
    } catch (error) {
      console.error('Error saving cart:', error);
    }
  }

  async addItem(item: CartItem): Promise<Cart> {
    const cart = await this.loadCart();
    const existingItemIndex = cart.items.findIndex(
      (cartItem) => cartItem.id === item.id
    );

    if (existingItemIndex >= 0) {
      cart.items[existingItemIndex].quantity += item.quantity;
    } else {
      cart.items.push(item);
    }

    cart.total = cart.items.reduce(
      (sum, cartItem) => sum + cartItem.price * cartItem.quantity,
      0
    );
    cart.itemCount = cart.items.reduce(
      (sum, cartItem) => sum + cartItem.quantity,
      0
    );

    await this.saveCart(cart);
    return cart;
  }

  async removeItem(itemId: string | number): Promise<Cart> {
    const cart = await this.loadCart();
    cart.items = cart.items.filter((item) => item.id !== itemId);

    cart.total = cart.items.reduce(
      (sum, item) => sum + item.price * item.quantity,
      0
    );
    cart.itemCount = cart.items.reduce(
      (sum, item) => sum + item.quantity,
      0
    );

    await this.saveCart(cart);
    return cart;
  }

  async updateQuantity(itemId: string | number, quantity: number): Promise<Cart> {
    const cart = await this.loadCart();
    const itemIndex = cart.items.findIndex((item) => item.id === itemId);

    if (itemIndex >= 0) {
      if (quantity <= 0) {
        cart.items.splice(itemIndex, 1);
      } else {
        cart.items[itemIndex].quantity = quantity;
      }

      cart.total = cart.items.reduce(
        (sum, item) => sum + item.price * item.quantity,
        0
      );
      cart.itemCount = cart.items.reduce(
        (sum, item) => sum + item.quantity,
        0
      );

      await this.saveCart(cart);
    }

    return cart;
  }

  async clearCart(): Promise<Cart> {
    const emptyCart: Cart = {
      items: [],
      total: 0,
      itemCount: 0,
      lastUpdated: new Date().toISOString(),
    };

    await this.saveCart(emptyCart);
    return emptyCart;
  }
}

export const cartService = new CartService();
export default CartService;
