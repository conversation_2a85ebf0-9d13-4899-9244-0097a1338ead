"use client"

import { TopHeader } from "@/components/top-header"
import { Head<PERSON> } from "@/components/header"
import { Footer } from "@/components/footer"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Search, HelpCircle, MessageCircle } from "lucide-react"

export default function QAPage() {
  const featuredQuestions = [
    {
      id: 1,
      title: "What's the average cost of a wedding in Chennai?",
      description: "I'm planning my wedding in Chennai and trying to set a realistic budget. What should I expect to spend on average?",
      author: "PriyaK",
      answers: 23,
    },
    {
      id: 2,
      title: "Best wedding venues in Bangalore under 5 lakhs?",
      description: "Looking for beautiful wedding venues in Bangalore that can accommodate 200-300 guests within a budget of 5 lakhs.",
      author: "<PERSON><PERSON><PERSON>",
      answers: 18,
    },
    {
      id: 3,
      title: "How to choose the right wedding photographer?",
      description: "What questions should I ask potential wedding photographers? What should I look for in their portfolio?",
      author: "MeeraS",
      answers: 31,
    },
    {
      id: 4,
      title: "Traditional vs modern wedding decorations - need advice",
      description: "Torn between traditional South Indian decorations and modern contemporary themes. How did you decide?",
      author: "Kavya<PERSON>",
      answers: 15,
    },
    {
      id: 5,
      title: "Wedding planning timeline - when to start what?",
      description: "Getting married in 8 months. What should I prioritize first? Looking for a detailed timeline.",
      author: "ArjunV",
      answers: 42,
    }
  ]

  const categories = [
    { name: "Budget" },
    { name: "Venues" },
    { name: "Photography" },
    { name: "Decorations" },
    { name: "Planning" },
    { name: "Catering" }
  ]

  return (
    <div className="min-h-screen bg-white">
      <TopHeader />
      <Header />

      <main className="py-4 px-3 sm:py-6 sm:px-4 lg:py-8 lg:pt-20">
        <div className="max-w-7xl mx-auto">
          {/* Header Section */}
          <div className="text-center mb-6 sm:mb-8 lg:mb-12">
            <div className="inline-flex items-center justify-center w-12 h-12 sm:w-16 sm:h-16 bg-gradient-to-br from-primary to-pink-600 rounded-full mb-3 sm:mb-4 lg:mb-6 shadow-lg">
              <MessageCircle className="w-6 h-6 sm:w-8 sm:h-8 text-white" />
            </div>
            <h1 className="text-xl sm:text-2xl lg:text-3xl xl:text-4xl font-bold text-foreground mb-2 sm:mb-3 lg:mb-4">
              Wedding Q&amp;A
            </h1>
            <p className="text-sm sm:text-base lg:text-lg text-muted-foreground max-w-2xl mx-auto mb-3 sm:mb-4 lg:mb-6 px-2 sm:px-4">
              Get expert answers to your wedding planning questions from experienced couples and professionals
            </p>
          </div>

          <div className="px-3 sm:px-4 lg:px-6">
            {/* Search and Ask */}
            <div className="flex flex-col md:flex-row gap-3 mb-8">
              <div className="flex-1 relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                <Input
                  placeholder="Search questions or ask something new..."
                  className="pl-10 h-10 bg-white border-gray-200"
                />
              </div>
              <Button className="h-10 px-5 flex items-center gap-2">
                <HelpCircle className="w-4 h-4" />
                Ask Question
              </Button>
            </div>

            {/* Categories - horizontal scroll */}
            <div className="mb-8 overflow-x-auto">
              <div className="flex gap-3">
                {categories.map((category) => (
                  <Badge key={category.name} className="bg-gray-200 text-gray-700 px-4 py-2 rounded-full cursor-pointer whitespace-nowrap hover:bg-gray-300 transition">
                    {category.name}
                  </Badge>
                ))}
              </div>
            </div>

            {/* Trending Questions */}
            <div>
              <h2 className="text-xl font-semibold mb-6">Trending Questions</h2>
              <div className="space-y-4">
                {featuredQuestions.map((question) => (
                  <div key={question.id} className="border border-gray-100 rounded-lg p-5 bg-white hover:shadow-sm transition">
                    <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-2 mb-1">
                      <h3 className="text-lg font-medium text-gray-900">{question.title}</h3>
                      <span className="text-xs text-gray-500">By {question.author}</span>
                    </div>
                    <p className="text-gray-700 text-sm mb-2 line-clamp-2">{question.description}</p>
                    <div className="flex items-center gap-4 text-xs text-gray-500">
                      <span>{question.answers} answers</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  )
}
