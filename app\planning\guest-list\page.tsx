'use client'
import { TopHeader } from "@/components/top-header"
import { Head<PERSON> } from "@/components/header"
import { Footer } from "@/components/footer"
import { useState, useEffect } from "react"
import { useAuth } from "@/contexts/AuthContext"
import simplePlanningService from "@/lib/services/simplePlanningService"
import { toast } from 'sonner'
import PlanningDataMigration from "@/components/PlanningDataMigration"

interface Guest {
  id: number
  name: string
  email: string
  phone: string
  category: 'family' | 'friends' | 'colleagues' | 'others'
  side: 'bride' | 'groom' | 'both'
  rsvpStatus: 'pending' | 'confirmed' | 'declined'
  plusOne: boolean
  dietaryRestrictions: string
  address: string
  notes: string
}

const GUEST_CATEGORIES = [
  { value: 'family', label: 'Family', color: 'bg-gray-100 text-red-800' },
  { value: 'friends', label: 'Friends', color: 'bg-yellow-100 text-yellow-800' },
  { value: 'colleagues', label: 'Colleagues', color: 'bg-red-50 text-red-700' },
  { value: 'others', label: 'Others', color: 'bg-yellow-50 text-yellow-700' }
]

const RSVP_STATUS = [
  { value: 'pending', label: 'Pending', color: 'bg-yellow-100 text-yellow-800' },
  { value: 'confirmed', label: 'Confirmed', color: 'bg-green-100 text-green-800' },
  { value: 'declined', label: 'Declined', color: 'bg-gray-100 text-gray-800' }
]

// Mock data for non-logged-in users
const MOCK_GUESTS: Guest[] = [
  {
    id: 1,
    name: "John Smith",
    email: "<EMAIL>",
    phone: "+91 8148376909",
    category: 'family',
    side: 'bride',
    rsvpStatus: 'confirmed',
    plusOne: true,
    dietaryRestrictions: "Vegetarian",
    address: "123 Main St, Mumbai",
    notes: "Uncle from bride's side"
  },
  {
    id: 2,
    name: "Sarah Johnson",
    email: "<EMAIL>",
    phone: "+91 9876543211",
    category: 'friends',
    side: 'groom',
    rsvpStatus: 'pending',
    plusOne: false,
    dietaryRestrictions: "",
    address: "456 Oak Ave, Delhi",
    notes: "College friend"
  },
  {
    id: 3,
    name: "Mike Wilson",
    email: "<EMAIL>",
    phone: "+91 9876543212",
    category: 'colleagues',
    side: 'both',
    rsvpStatus: 'confirmed',
    plusOne: true,
    dietaryRestrictions: "No seafood",
    address: "789 Pine St, Bangalore",
    notes: "Work colleague"
  }
]

export default function GuestListPage() {
  const { user } = useAuth()
  const [mounted, setMounted] = useState(false)
  const [guests, setGuests] = useState<Guest[]>([])
  const [showAddForm, setShowAddForm] = useState(false)
  const [editingGuest, setEditingGuest] = useState<Guest | null>(null)
  const [filterCategory, setFilterCategory] = useState<string>('all')
  const [filterSide, setFilterSide] = useState<string>('all')
  const [filterRSVP, setFilterRSVP] = useState<string>('all')
  const [searchTerm, setSearchTerm] = useState('')
  const [loading, setLoading] = useState(false)
  const [saving, setSaving] = useState(false)

  const [newGuest, setNewGuest] = useState<Partial<Guest>>({
    name: '',
    email: '',
    phone: '',
    category: 'family',
    side: 'bride',
    rsvpStatus: 'pending',
    plusOne: false,
    dietaryRestrictions: '',
    address: '',
    notes: ''
  })

  // Load guest data based on user authentication
  useEffect(() => {
    setMounted(true)
  }, [])

  useEffect(() => {
    if (mounted) {
      loadGuestData()
    }
  }, [mounted, user])

  const loadGuestData = async () => {
    try {
      setLoading(true)

      if (user?.userId) {
        // Load from database for logged-in users
        const guestListData = await simplePlanningService.getUserPlanningData(user.userId, 'GUEST_LIST')

        if (guestListData.length > 0) {
          // Use the most recent guest list
          const latestGuestList = guestListData[0]
          setGuests(latestGuestList.data.guests || [])
        } else {
          // Check for localStorage data to migrate
          const savedGuests = localStorage.getItem('wedding-guests')
          if (savedGuests) {
            const localGuests = JSON.parse(savedGuests)
            setGuests(localGuests)
            // Migration will be handled by PlanningDataMigration component
          } else {
            setGuests([])
          }
        }
      } else {
        // Use mock data for non-logged-in users
        setGuests(MOCK_GUESTS)
      }
    } catch (error) {
      console.error('Error loading guest data:', error)
      toast.error('Failed to load guest list')
      // Fallback to localStorage or mock data
      if (user?.userId) {
        const savedGuests = localStorage.getItem('wedding-guests')
        if (savedGuests) {
          setGuests(JSON.parse(savedGuests))
        }
      } else {
        setGuests(MOCK_GUESTS)
      }
    } finally {
      setLoading(false)
    }
  }

  // Prevent hydration mismatch by not rendering until mounted
  if (!mounted) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-background to-muted/20">
        <TopHeader />
        <Header />
        <main className="py-12 px-4">
          <div className="max-w-7xl mx-auto">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
              <p className="mt-4 text-gray-600">Loading...</p>
            </div>
          </div>
        </main>
        <Footer />
      </div>
    )
  }

  // Save guests to database or localStorage
  const saveGuests = async (updatedGuests: Guest[]) => {
    try {
      setSaving(true)
      setGuests(updatedGuests)

      if (user?.userId) {
        // Save to database for logged-in users
        const guestListData = {
          guests: updatedGuests,
          totalGuests: updatedGuests.length,
          confirmedGuests: updatedGuests.filter(g => g.rsvpStatus === 'confirmed').length,
          lastUpdated: new Date().toISOString()
        }

        // Check if guest list already exists
        const existingData = await simplePlanningService.getUserPlanningData(user.userId, 'GUEST_LIST')

        if (existingData.length > 0) {
          // Update existing guest list
          await simplePlanningService.updatePlanningData(existingData[0].id, {
            data: guestListData,
            metadata: {
              lastUpdated: new Date().toISOString(),
              totalGuests: updatedGuests.length
            }
          })
        } else {
          // Create new guest list
          await simplePlanningService.savePlanningData(
            user.userId,
            'GUEST_LIST',
            'My Wedding Guests',
            guestListData,
            {
              totalGuests: updatedGuests.length,
              createdAt: new Date().toISOString()
            }
          )
        }

        toast.success('Guest list saved to your account!')
      } else {
        // Save to localStorage for non-logged-in users (mock data scenario)
        localStorage.setItem('wedding-guests', JSON.stringify(updatedGuests))
        toast.success('Guest list saved locally!')
      }
    } catch (error) {
      console.error('Error saving guest list:', error)
      toast.error('Failed to save guest list. Please try again.')
      // Fallback to localStorage
      localStorage.setItem('wedding-guests', JSON.stringify(updatedGuests))
    } finally {
      setSaving(false)
    }
  }

  const addGuest = async () => {
    if (!newGuest.name?.trim()) return

    const guest: Guest = {
      id: Date.now(),
      name: newGuest.name.trim(),
      email: newGuest.email || '',
      phone: newGuest.phone || '',
      category: newGuest.category as Guest['category'] || 'family',
      side: newGuest.side as Guest['side'] || 'bride',
      rsvpStatus: newGuest.rsvpStatus as Guest['rsvpStatus'] || 'pending',
      plusOne: newGuest.plusOne || false,
      dietaryRestrictions: newGuest.dietaryRestrictions || '',
      address: newGuest.address || '',
      notes: newGuest.notes || ''
    }

    await saveGuests([...guests, guest])
    setNewGuest({
      name: '',
      email: '',
      phone: '',
      category: 'family',
      side: 'bride',
      rsvpStatus: 'pending',
      plusOne: false,
      dietaryRestrictions: '',
      address: '',
      notes: ''
    })
    setShowAddForm(false)
  }

  const updateGuest = async (updatedGuest: Guest) => {
    const updatedGuests = guests.map(guest =>
      guest.id === updatedGuest.id ? updatedGuest : guest
    )
    await saveGuests(updatedGuests)
    setEditingGuest(null)
  }

  const deleteGuest = async (id: number) => {
    if (confirm('Are you sure you want to delete this guest?')) {
      const updatedGuests = guests.filter(guest => guest.id !== id)
      await saveGuests(updatedGuests)
    }
  }

  const filteredGuests = guests.filter(guest => {
    const matchesSearch = guest.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         guest.email.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesCategory = filterCategory === 'all' || guest.category === filterCategory
    const matchesSide = filterSide === 'all' || guest.side === filterSide
    const matchesRSVP = filterRSVP === 'all' || guest.rsvpStatus === filterRSVP

    return matchesSearch && matchesCategory && matchesSide && matchesRSVP
  })

  const guestStats = {
    total: guests.length,
    confirmed: guests.filter(g => g.rsvpStatus === 'confirmed').length,
    pending: guests.filter(g => g.rsvpStatus === 'pending').length,
    declined: guests.filter(g => g.rsvpStatus === 'declined').length,
    withPlusOne: guests.filter(g => g.plusOne && g.rsvpStatus === 'confirmed').length,
    brideGuests: guests.filter(g => g.side === 'bride').length,
    groomGuests: guests.filter(g => g.side === 'groom').length,
    bothGuests: guests.filter(g => g.side === 'both').length
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-background to-muted/20">
      <TopHeader />
      <Header />
      
      <main className="py-4 px-3 sm:py-6 sm:px-4 lg:py-8 lg:pt-20">
        <div className="max-w-4xl mx-auto">
          {/* Header Section */}
          <div className="text-center mb-6 sm:mb-8 lg:mb-12">
            <div className="inline-flex items-center justify-center w-12 h-12 sm:w-16 sm:h-16 bg-gradient-to-br from-primary to-pink-600 rounded-full mb-3 sm:mb-4 lg:mb-6 shadow-lg">
              <svg className="w-6 h-6 sm:w-8 sm:h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
              </svg>
            </div>
            <h1 className="text-xl sm:text-2xl lg:text-3xl xl:text-4xl font-bold text-foreground mb-2 sm:mb-3 lg:mb-4">
              Wedding Guest List Manager
            </h1>
            <p className="text-sm sm:text-base lg:text-lg text-muted-foreground max-w-2xl mx-auto mb-4 sm:mb-6 lg:mb-8 px-2 sm:px-4">
              Organize and manage your wedding guests with ease. Track Status, manage guest categories, and keep everything organized for your special day.
            </p>
          </div>

          {/* Migration Component */}
          {user && <PlanningDataMigration />}

          {/* Progress Stats */}
          <div className="relative mb-8 sm:mb-12 lg:mb-16">
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 sm:gap-6">
              {/* Total Guests */}
              <div className="bg-white rounded-lg p-4 sm:p-6 lg:p-8 shadow-md border border-gray-200">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-xs sm:text-sm font-medium text-gray-600">Total Guests</p>
                    <p className="text-2xl sm:text-3xl lg:text-4xl font-bold text-primary">{guestStats.total}</p>
                  </div>
                  <div className="w-12 h-12 sm:w-14 sm:h-14 lg:w-16 lg:h-16 bg-gradient-to-br from-primary to-pink-600 rounded-lg flex items-center justify-center shadow-lg">
                    <svg className="w-6 h-6 sm:w-7 sm:h-7 lg:w-8 lg:h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                    </svg>
                  </div>
                </div>
              </div>
              {/* Pending */}
              <div className="bg-white rounded-lg p-4 sm:p-6 lg:p-8 shadow-md border border-gray-200">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-xs sm:text-sm font-medium text-gray-600 text-center">Pending</p>
                    <p className="text-2xl sm:text-3xl lg:text-4xl font-bold text-primary text-center">{guestStats.pending}</p>
                  </div>
                  <div className="w-12 h-12 sm:w-14 sm:h-14 lg:w-16 lg:h-16 bg-gradient-to-br from-yellow-500 to-yellow-600 rounded-lg flex items-center justify-center shadow-lg">
                    <svg className="w-6 h-6 sm:w-7 sm:h-7 lg:w-8 lg:h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                </div>
              </div>
              {/* Confirmed */}
              <div className="bg-white rounded-lg p-4 sm:p-6 lg:p-8 shadow-md border border-gray-200">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-xs sm:text-sm font-medium text-gray-600 text-right">Confirmed</p>
                    <p className="text-2xl sm:text-3xl lg:text-4xl font-bold text-primary text-right">{guestStats.confirmed}</p>
                  </div>
                  <div className="w-12 h-12 sm:w-14 sm:h-14 lg:w-16 lg:h-16 bg-gradient-to-br from-green-500 to-green-600 rounded-lg flex items-center justify-center shadow-lg">
                    <svg className="w-6 h-6 sm:w-7 sm:h-7 lg:w-8 lg:h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Add New Guest */}
          <div className="bg-card rounded-xl shadow-sm border p-4 sm:p-6 lg:p-8 mb-6 sm:mb-8">
            <h3 className="text-lg sm:text-xl font-semibold text-card-foreground mb-4 sm:mb-6">
              Add New Guest
            </h3>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4 mb-4 sm:mb-6">
              <input
                type="text"
                placeholder="Search guests..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="bg-background border border-border rounded-lg px-3 sm:px-4 py-2 sm:py-3 focus:outline-none focus:ring-2 focus:ring-primary/20 text-sm"
              />

              <select
                value={filterCategory}
                onChange={(e) => setFilterCategory(e.target.value)}
                className="bg-background border border-border rounded-lg px-3 sm:px-4 py-2 sm:py-3 focus:outline-none focus:ring-2 focus:ring-primary/20 text-sm"
              >
                <option value="all">All Categories</option>
                {GUEST_CATEGORIES.map(cat => (
                  <option key={cat.value} value={cat.value}>{cat.label}</option>
                ))}
              </select>

              <select
                value={filterSide}
                onChange={(e) => setFilterSide(e.target.value)}
                className="bg-background border border-border rounded-lg px-3 sm:px-4 py-2 sm:py-3 focus:outline-none focus:ring-2 focus:ring-primary/20 text-sm"
              >
                <option value="all">All Sides</option>
                <option value="bride">Bride's Side</option>
                <option value="groom">Groom's Side</option>
                <option value="both">Both Sides</option>
              </select>

              <select
                value={filterRSVP}
                onChange={(e) => setFilterRSVP(e.target.value)}
                className="bg-background border border-border rounded-lg px-3 sm:px-4 py-2 sm:py-3 focus:outline-none focus:ring-2 focus:ring-primary/20 text-sm"
              >
                <option value="all">Status</option>
                {RSVP_STATUS.map(status => (
                  <option key={status.value} value={status.value}>{status.label}</option>
                ))}
              </select>
            </div>

            <div className="flex justify-center">
              <button
                onClick={() => setShowAddForm(true)}
                className="bg-primary text-primary-foreground rounded-lg px-6 sm:px-8 py-3 sm:py-4 font-semibold hover:bg-primary/90 transition-colors flex items-center gap-2 sm:gap-3 text-base sm:text-lg"
              >
                <svg className="w-4 h-4 sm:w-5 sm:h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
                Add New Guest
              </button>
            </div>
          </div>

          {/* Guest List */}
          <div className="space-y-4 sm:space-y-6">
            <div className="bg-card rounded-xl shadow-sm border overflow-hidden">
              <div className="p-4 sm:p-6 border-b border-border">
                <h2 className="text-lg sm:text-xl font-bold text-card-foreground">
                  Guest List ({filteredGuests.length} {filteredGuests.length === 1 ? 'guest' : 'guests'})
                </h2>
              </div>

              {filteredGuests.length === 0 ? (
                <div className="text-center py-12">
                  <div className="text-muted-foreground mb-4">
                    <svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                    </svg>
                  </div>
                  <p className="text-muted-foreground text-lg mb-4">
                    {guests.length === 0 ? 'No guests added yet' : 'No guests match your filters'}
                  </p>
                  {guests.length === 0 && (
                    <button
                      onClick={() => setShowAddForm(true)}
                      className="bg-primary text-primary-foreground px-6 py-2 rounded-lg hover:bg-primary/90 transition-colors"
                    >
                      Add Your First Guest
                    </button>
                  )}
                </div>
              ) : (
                <div className="divide-y divide-border">
                  {filteredGuests.map((guest) => (
                    <div key={guest.id} className="p-4 sm:p-6 hover:bg-muted/30 transition-colors">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex flex-wrap items-center gap-2 sm:gap-3 mb-2">
                            <h3 className="font-semibold text-base sm:text-lg text-card-foreground">{guest.name}</h3>
                            <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                              GUEST_CATEGORIES.find(c => c.value === guest.category)?.color
                            }`}>
                              {GUEST_CATEGORIES.find(c => c.value === guest.category)?.label}
                            </span>
                            <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                              RSVP_STATUS.find(s => s.value === guest.rsvpStatus)?.color
                            }`}>
                              {RSVP_STATUS.find(s => s.value === guest.rsvpStatus)?.label}
                            </span>
                            {guest.plusOne && (
                              <span className="px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                +1
                              </span>
                            )}
                          </div>

                          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-2 sm:gap-4 text-xs sm:text-sm text-muted-foreground">
                            {guest.email && (
                              <div className="flex items-center gap-2">
                                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                                </svg>
                                <span>{guest.email}</span>
                              </div>
                            )}

                            {guest.phone && (
                              <div className="flex items-center gap-2">
                                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                                </svg>
                                <span>{guest.phone}</span>
                              </div>
                            )}

                            <div className="flex items-center gap-2">
                              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                              </svg>
                              <span className="capitalize">{guest.side}'s Side</span>
                            </div>
                          </div>

                          {guest.dietaryRestrictions && (
                            <div className="mt-2 text-sm text-muted-foreground">
                              <span className="font-medium">Dietary:</span> {guest.dietaryRestrictions}
                            </div>
                          )}

                          {guest.notes && (
                            <div className="mt-2 text-sm text-muted-foreground">
                              <span className="font-medium">Notes:</span> {guest.notes}
                            </div>
                          )}
                        </div>

                        <div className="flex gap-1 sm:gap-2 ml-2 sm:ml-4 flex-shrink-0">
                          <button
                            onClick={() => setEditingGuest(guest)}
                            className="text-yellow-600 hover:text-yellow-800 p-1 sm:p-2 hover:bg-yellow-50 rounded-lg transition-colors"
                            title="Edit guest"
                          >
                            <svg className="w-3 h-3 sm:w-4 sm:h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                            </svg>
                          </button>
                          <button
                            onClick={() => deleteGuest(guest.id)}
                            className="text-red-600 hover:text-red-800 p-1 sm:p-2 hover:bg-red-50 rounded-lg transition-colors"
                            title="Delete guest"
                          >
                            <svg className="w-3 h-3 sm:w-4 sm:h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                            </svg>
                          </button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>
      </main>

      {/* Add Guest Form */}
      {showAddForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-2xl shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-2xl font-bold text-gray-900">Add New Guest</h2>
                <button
                  onClick={() => setShowAddForm(false)}
                  className="text-gray-500 hover:text-gray-700"
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Name *</label>
                  <input
                    type="text"
                    value={newGuest.name || ''}
                    onChange={(e) => setNewGuest({...newGuest, name: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Email</label>
                  <input
                    type="email"
                    value={newGuest.email || ''}
                    onChange={(e) => setNewGuest({...newGuest, email: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Phone</label>
                  <input
                    type="tel"
                    value={newGuest.phone || ''}
                    onChange={(e) => setNewGuest({...newGuest, phone: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Category</label>
                  <select
                    value={newGuest.category || 'family'}
                    onChange={(e) => setNewGuest({...newGuest, category: e.target.value as Guest['category']})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                  >
                    {GUEST_CATEGORIES.map(cat => (
                      <option key={cat.value} value={cat.value}>{cat.label}</option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Side</label>
                  <select
                    value={newGuest.side || 'bride'}
                    onChange={(e) => setNewGuest({...newGuest, side: e.target.value as Guest['side']})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                  >
                    <option value="bride">Bride's Side</option>
                    <option value="groom">Groom's Side</option>
                    <option value="both">Both Sides</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">RSVP Status</label>
                  <select
                    value={newGuest.rsvpStatus || 'pending'}
                    onChange={(e) => setNewGuest({...newGuest, rsvpStatus: e.target.value as Guest['rsvpStatus']})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                  >
                    {RSVP_STATUS.map(status => (
                      <option key={status.value} value={status.value}>{status.label}</option>
                    ))}
                  </select>
                </div>

                <div className="md:col-span-2">
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={newGuest.plusOne || false}
                      onChange={(e) => setNewGuest({...newGuest, plusOne: e.target.checked})}
                      className="mr-2"
                    />
                    <span className="text-sm font-medium text-gray-700">Plus One</span>
                  </label>
                </div>

                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-1">Address</label>
                  <textarea
                    value={newGuest.address || ''}
                    onChange={(e) => setNewGuest({...newGuest, address: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                    rows={2}
                  />
                </div>

                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-1">Dietary Restrictions</label>
                  <input
                    type="text"
                    value={newGuest.dietaryRestrictions || ''}
                    onChange={(e) => setNewGuest({...newGuest, dietaryRestrictions: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                    placeholder="e.g., Vegetarian, Vegan, Allergies"
                  />
                </div>

                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-1">Notes</label>
                  <textarea
                    value={newGuest.notes || ''}
                    onChange={(e) => setNewGuest({...newGuest, notes: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                    rows={2}
                    placeholder="Additional notes about this guest"
                  />
                </div>
              </div>

              <div className="flex gap-4 mt-6">
                <button
                  onClick={addGuest}
                  disabled={!newGuest.name?.trim() || saving}
                  className="flex-1 bg-primary text-white py-2 rounded-lg hover:bg-primary-dark transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2"
                >
                  {saving ? (
                    <>
                      <svg className="animate-spin h-4 w-4" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Saving...
                    </>
                  ) : (
                    'Add Guest'
                  )}
                </button>
                <button
                  onClick={() => setShowAddForm(false)}
                  className="flex-1 bg-yellow-100 text-yellow-800 py-2 rounded-lg hover:bg-yellow-200 transition-colors"
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Edit Guest Modal */}
      {editingGuest && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-2xl shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-2xl font-bold text-gray-900">Edit Guest</h2>
                <button
                  onClick={() => setEditingGuest(null)}
                  className="text-gray-500 hover:text-gray-700"
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Name *</label>
                  <input
                    type="text"
                    value={editingGuest.name}
                    onChange={(e) => setEditingGuest({...editingGuest, name: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Email</label>
                  <input
                    type="email"
                    value={editingGuest.email}
                    onChange={(e) => setEditingGuest({...editingGuest, email: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Phone</label>
                  <input
                    type="tel"
                    value={editingGuest.phone}
                    onChange={(e) => setEditingGuest({...editingGuest, phone: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Category</label>
                  <select
                    value={editingGuest.category}
                    onChange={(e) => setEditingGuest({...editingGuest, category: e.target.value as Guest['category']})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                  >
                    {GUEST_CATEGORIES.map(cat => (
                      <option key={cat.value} value={cat.value}>{cat.label}</option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Side</label>
                  <select
                    value={editingGuest.side}
                    onChange={(e) => setEditingGuest({...editingGuest, side: e.target.value as Guest['side']})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                  >
                    <option value="bride">Bride's Side</option>
                    <option value="groom">Groom's Side</option>
                    <option value="both">Both Sides</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">RSVP Status</label>
                  <select
                    value={editingGuest.rsvpStatus}
                    onChange={(e) => setEditingGuest({...editingGuest, rsvpStatus: e.target.value as Guest['rsvpStatus']})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                  >
                    {RSVP_STATUS.map(status => (
                      <option key={status.value} value={status.value}>{status.label}</option>
                    ))}
                  </select>
                </div>

                <div className="md:col-span-2">
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={editingGuest.plusOne}
                      onChange={(e) => setEditingGuest({...editingGuest, plusOne: e.target.checked})}
                      className="mr-2"
                    />
                    <span className="text-sm font-medium text-gray-700">Plus One</span>
                  </label>
                </div>

                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-1">Address</label>
                  <textarea
                    value={editingGuest.address}
                    onChange={(e) => setEditingGuest({...editingGuest, address: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                    rows={2}
                  />
                </div>

                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-1">Dietary Restrictions</label>
                  <input
                    type="text"
                    value={editingGuest.dietaryRestrictions}
                    onChange={(e) => setEditingGuest({...editingGuest, dietaryRestrictions: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                  />
                </div>

                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-1">Notes</label>
                  <textarea
                    value={editingGuest.notes}
                    onChange={(e) => setEditingGuest({...editingGuest, notes: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                    rows={2}
                  />
                </div>
              </div>

              <div className="flex gap-4 mt-6">
                <button
                  onClick={() => updateGuest(editingGuest)}
                  className="flex-1 bg-primary text-white py-2 rounded-lg hover:bg-primary-dark transition-colors"
                >
                  Update Guest
                </button>
                <button
                  onClick={() => setEditingGuest(null)}
                  className="flex-1 bg-yellow-100 text-yellow-800 py-2 rounded-lg hover:bg-yellow-200 transition-colors"
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
      
      <Footer />
    </div>
  )
} 