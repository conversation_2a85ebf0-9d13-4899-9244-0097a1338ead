import { NextRequest, NextResponse } from 'next/server'
import { GoogleSearchConsoleService } from '@/lib/services/googleSearchConsoleService'

export async function GET(
  request: NextRequest,
  { params }: { params: { vendorId: string } }
) {
  try {
    const { searchParams } = new URL(request.url)
    const dateRange = searchParams.get('range') || '30d'
    const siteUrl = process.env.SITE_URL || 'https://BookmyFestive.com'
    
    const data = await GoogleSearchConsoleService.getSearchAnalytics(
      siteUrl,
      params.vendorId,
      dateRange
    )
    
    return NextResponse.json(data)
  } catch (error) {
    console.error('Search Console API Error:', error)
    return NextResponse.json({ error: 'Failed to fetch Search Console data' }, { status: 500 })
  }
}