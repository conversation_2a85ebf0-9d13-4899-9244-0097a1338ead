'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { 
  User, 
  CheckCircle, 
  AlertCircle, 
  Loader2,
  UserCheck,
  Database,
  RefreshCw
} from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { profileService } from '@/lib/services/profileService';
import { Header } from "@/components/header";
import { Footer } from "@/components/footer";
import { TopHeader } from "@/components/top-header";

export default function TestUserProfileCreationPage() {
  const { user, userProfile, isAuthenticated, refreshUserProfile } = useAuth();
  const [loading, setLoading] = useState(false);
  const [testResults, setTestResults] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  const runProfileTest = async () => {
    setLoading(true);
    setError(null);
    setTestResults(null);

    try {
      if (!user) {
        throw new Error('No authenticated user found');
      }

      const results = {
        cognitoUser: {
          userId: user.userId,
          username: user.username,
          email: user.signInDetails?.loginId || 'N/A',
          attributes: user.attributes || {}
        },
        profileExists: false,
        profileData: null,
        autoCreationWorked: false,
        timestamp: new Date().toISOString()
      };

      // Check if profile exists
      console.log('Checking for existing profile...');
      const existingProfile = await profileService.getProfile(user.userId);
      
      if (existingProfile) {
        results.profileExists = true;
        results.profileData = existingProfile;
        results.autoCreationWorked = true;
        console.log('Profile found:', existingProfile);
      } else {
        console.log('No profile found, testing auto-creation...');
        
        // Try to create profile manually to test the function
        try {
          const newProfile = await profileService.createProfileFromCognitoUser(user);
          results.profileExists = true;
          results.profileData = newProfile;
          results.autoCreationWorked = true;
          console.log('Profile created successfully:', newProfile);
          
          // Refresh the auth context
          await refreshUserProfile();
        } catch (createError) {
          console.error('Profile creation failed:', createError);
          results.autoCreationWorked = false;
          throw createError;
        }
      }

      setTestResults(results);
    } catch (err: any) {
      setError(err.message || 'Test failed');
      console.error('Profile test error:', err);
    } finally {
      setLoading(false);
    }
  };

  const refreshProfile = async () => {
    setLoading(true);
    try {
      await refreshUserProfile();
    } catch (err: any) {
      setError(err.message || 'Failed to refresh profile');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <TopHeader />
      <Header />
      
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold mb-4">UserProfile Auto-Creation Test</h1>
            <p className="text-gray-600">
              Test if UserProfile entries are automatically created when users sign up/login to Cognito
            </p>
          </div>

          {error && (
            <Alert variant="destructive" className="mb-6">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {/* Authentication Status */}
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="w-5 h-5" />
                Authentication Status
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span>Authenticated:</span>
                  <Badge variant={isAuthenticated ? "default" : "secondary"}>
                    {isAuthenticated ? "Yes" : "No"}
                  </Badge>
                </div>
                
                {user && (
                  <>
                    <div className="flex justify-between items-center">
                      <span>User ID:</span>
                      <code className="text-sm bg-gray-100 px-2 py-1 rounded">
                        {user.userId}
                      </code>
                    </div>
                    
                    <div className="flex justify-between items-center">
                      <span>Username:</span>
                      <code className="text-sm bg-gray-100 px-2 py-1 rounded">
                        {user.username}
                      </code>
                    </div>
                    
                    <div className="flex justify-between items-center">
                      <span>Email:</span>
                      <code className="text-sm bg-gray-100 px-2 py-1 rounded">
                        {user.signInDetails?.loginId || 'N/A'}
                      </code>
                    </div>
                  </>
                )}
                
                <div className="flex justify-between items-center">
                  <span>UserProfile Loaded:</span>
                  <Badge variant={userProfile ? "default" : "secondary"}>
                    {userProfile ? "Yes" : "No"}
                  </Badge>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Current Profile Status */}
          {userProfile && (
            <Card className="mb-6">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <UserCheck className="w-5 h-5" />
                  Current UserProfile
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex justify-between items-center">
                    <span>Profile ID:</span>
                    <code className="text-sm bg-gray-100 px-2 py-1 rounded">
                      {userProfile.id}
                    </code>
                  </div>
                  
                  <div className="flex justify-between items-center">
                    <span>Name:</span>
                    <span>{userProfile.firstName} {userProfile.lastName}</span>
                  </div>
                  
                  <div className="flex justify-between items-center">
                    <span>Email:</span>
                    <span>{userProfile.email}</span>
                  </div>
                  
                  <div className="flex justify-between items-center">
                    <span>Is Vendor:</span>
                    <Badge variant={userProfile.isVendor ? "default" : "secondary"}>
                      {userProfile.isVendor ? "Yes" : "No"}
                    </Badge>
                  </div>
                  
                  <div className="flex justify-between items-center">
                    <span>Registration Source:</span>
                    <Badge variant="outline">
                      {userProfile.registrationSource || 'N/A'}
                    </Badge>
                  </div>
                  
                  <div className="flex justify-between items-center">
                    <span>Created:</span>
                    <span className="text-sm">
                      {userProfile.createdAt ? new Date(userProfile.createdAt).toLocaleString() : 'N/A'}
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Test Controls */}
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Database className="w-5 h-5" />
                Test Controls
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex gap-4">
                <Button 
                  onClick={runProfileTest}
                  disabled={loading || !isAuthenticated}
                  className="flex items-center gap-2"
                >
                  {loading ? (
                    <Loader2 className="w-4 h-4 animate-spin" />
                  ) : (
                    <Database className="w-4 h-4" />
                  )}
                  Test Profile Creation
                </Button>
                
                <Button 
                  variant="outline"
                  onClick={refreshProfile}
                  disabled={loading || !isAuthenticated}
                  className="flex items-center gap-2"
                >
                  {loading ? (
                    <Loader2 className="w-4 h-4 animate-spin" />
                  ) : (
                    <RefreshCw className="w-4 h-4" />
                  )}
                  Refresh Profile
                </Button>
              </div>
              
              {!isAuthenticated && (
                <p className="text-sm text-gray-500 mt-2">
                  Please log in to test profile creation functionality.
                </p>
              )}
            </CardContent>
          </Card>

          {/* Test Results */}
          {testResults && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  {testResults.autoCreationWorked ? (
                    <CheckCircle className="w-5 h-5 text-green-600" />
                  ) : (
                    <AlertCircle className="w-5 h-5 text-red-600" />
                  )}
                  Test Results
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="p-4 bg-gray-50 rounded-lg">
                    <h4 className="font-medium mb-2">Cognito User Data:</h4>
                    <pre className="text-sm overflow-x-auto">
                      {JSON.stringify(testResults.cognitoUser, null, 2)}
                    </pre>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="text-center p-4 border rounded-lg">
                      <div className="text-2xl font-bold mb-2">
                        {testResults.profileExists ? (
                          <CheckCircle className="w-8 h-8 text-green-600 mx-auto" />
                        ) : (
                          <AlertCircle className="w-8 h-8 text-red-600 mx-auto" />
                        )}
                      </div>
                      <p className="text-sm font-medium">Profile Exists</p>
                      <p className="text-xs text-gray-500">
                        {testResults.profileExists ? "Found" : "Not Found"}
                      </p>
                    </div>
                    
                    <div className="text-center p-4 border rounded-lg">
                      <div className="text-2xl font-bold mb-2">
                        {testResults.autoCreationWorked ? (
                          <CheckCircle className="w-8 h-8 text-green-600 mx-auto" />
                        ) : (
                          <AlertCircle className="w-8 h-8 text-red-600 mx-auto" />
                        )}
                      </div>
                      <p className="text-sm font-medium">Auto Creation</p>
                      <p className="text-xs text-gray-500">
                        {testResults.autoCreationWorked ? "Working" : "Failed"}
                      </p>
                    </div>
                    
                    <div className="text-center p-4 border rounded-lg">
                      <div className="text-2xl font-bold mb-2">
                        <Database className="w-8 h-8 text-blue-600 mx-auto" />
                      </div>
                      <p className="text-sm font-medium">Test Time</p>
                      <p className="text-xs text-gray-500">
                        {new Date(testResults.timestamp).toLocaleTimeString()}
                      </p>
                    </div>
                  </div>
                  
                  {testResults.profileData && (
                    <div className="p-4 bg-green-50 rounded-lg">
                      <h4 className="font-medium mb-2 text-green-800">Profile Data:</h4>
                      <pre className="text-sm overflow-x-auto text-green-700">
                        {JSON.stringify(testResults.profileData, null, 2)}
                      </pre>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>

      <Footer />
    </div>
  );
}
