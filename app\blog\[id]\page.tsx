import { TopHeader } from "@/components/top-header"
import { <PERSON><PERSON> } from "@/components/header"
import { <PERSON><PERSON> } from "@/components/footer"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Separator } from "@/components/ui/separator"
import { Calendar, Clock, Heart, Share2, Bookmark, MessageCircle, Eye, ArrowLeft, ArrowRight } from "lucide-react"
import Image from "next/image"
import Link from "next/link"

export default function BlogPostDetailsPage() {
  const post = {
    id: 1,
    title: "10 Essential Wedding Planning Tips for Indian Weddings",
    excerpt:
      "Planning an Indian wedding can be overwhelming. Here are the top 10 tips to make your wedding planning journey smooth and memorable.",
    content: `
      <p>Planning an Indian wedding is both exciting and overwhelming. With multiple ceremonies, hundreds of guests, and countless details to manage, it's easy to feel lost in the process. But don't worry – with proper planning and organization, you can create the wedding of your dreams without losing your sanity.</p>

      <h2>1. Start Planning Early</h2>
      <p>Indian weddings require extensive planning. Start at least 12-18 months in advance to secure the best vendors, venues, and dates. Popular Festive seasons (October to March) book up quickly, so early planning is crucial.</p>

      <h2>2. Set a Realistic Budget</h2>
      <p>Determine your budget early and allocate funds to different aspects of the wedding. Typically, venue and catering take up 40-50% of the budget, followed by photography, decoration, and attire.</p>

      <h2>3. Create a Guest List</h2>
      <p>Indian weddings are known for their large guest lists. Create a comprehensive list early as it will determine your venue size, catering requirements, and overall budget.</p>

      <h2>4. Book Your Venue First</h2>
      <p>Once you have your guest count and budget, book your venue. Popular venues get booked months in advance, especially during wedding season.</p>

      <h2>5. Choose Your Wedding Dates Wisely</h2>
      <p>Consider auspicious dates according to your traditions, but also think about practical aspects like weather, guest availability, and vendor schedules.</p>

      <h2>6. Hire a Wedding Planner</h2>
      <p>A good wedding planner can save you time, stress, and often money. They have vendor connections and can negotiate better deals while ensuring everything runs smoothly.</p>

      <h2>7. Book Key Vendors Early</h2>
      <p>Photographers, decorators, and caterers are in high demand. Book them as soon as you finalize your dates and venue.</p>

      <h2>8. Plan Your Outfits in Advance</h2>
      <p>Wedding outfits, especially custom pieces, can take 2-3 months to complete. Start shopping early and have multiple fittings to ensure perfect fit.</p>

      <h2>9. Don't Forget the Legal Requirements</h2>
      <p>Ensure all legal documentation is in order. Register your marriage and keep all necessary documents ready.</p>

      <h2>10. Enjoy the Process</h2>
      <p>Remember, this is your special time. Don't get so caught up in planning that you forget to enjoy the journey. Take breaks, celebrate small milestones, and cherish the memories you're creating.</p>

      <p>Planning an Indian wedding is a marathon, not a sprint. With these tips and proper organization, you'll be well on your way to creating a beautiful, memorable celebration that you and your guests will cherish forever.</p>
    `,
    author: {
      name: "Priya Sharma",
      bio: "Wedding planning expert with 10+ years of experience in organizing Indian weddings",
      avatar: "/placeholder.svg?height=60&width=60",
      verified: true,
    },
    publishDate: "December 15, 2024",
    readTime: "5 min read",
    category: "Planning",
    tags: ["wedding planning", "indian weddings", "tips", "organization", "budget"],
    image: "/placeholder.svg?height=400&width=800",
    views: 2450,
    likes: 189,
    comment: 23,
    relatedPosts: [
      {
        id: 2,
        title: "Latest Bridal Lehenga Trends for 2024",
        excerpt:
          "Discover the hottest bridal lehenga trends that are taking the wedding fashion world by storm this year.",
        image: "/placeholder.svg?height=200&width=300",
        category: "Fashion",
        readTime: "4 min read",
      },
      {
        id: 3,
        title: "Budget-Friendly Wedding Decoration Ideas",
        excerpt:
          "Create stunning wedding decorations without breaking the bank. These creative ideas will transform your venue beautifully.",
        image: "/placeholder.svg?height=200&width=300",
        category: "Decor",
        readTime: "6 min read",
      },
      {
        id: 4,
        title: "Choosing the Perfect Wedding Photographer",
        excerpt:
          "Your wedding photos will be treasured forever. Learn how to select the right photographer to capture your special moments.",
        image: "/placeholder.svg?height=200&width=300",
        category: "Photography",
        readTime: "7 min read",
      },
    ],
    comments: [
      {
        id: 1,
        user: "Kavya Patel",
        avatar: "/placeholder.svg?height=40&width=40",
        comment:
          "This is so helpful! I'm planning my wedding next year and these tips are exactly what I needed. Thank you for sharing!",
        date: "2 days ago",
        likes: 12,
      },
      {
        id: 2,
        user: "Rohit Singh",
        avatar: "/placeholder.svg?height=40&width=40",
        comment:
          "Great article! The budget allocation tip is particularly useful. Can you share more details about vendor negotiations?",
        date: "3 days ago",
        likes: 8,
      },
      {
        id: 3,
        user: "Meera Gupta",
        avatar: "/placeholder.svg?height=40&width=40",
        comment:
          "I wish I had read this before planning my wedding! These are all spot-on tips that would have saved me so much stress.",
        date: "1 week ago",
        likes: 15,
      },
    ],
  }

  return (
    <div className="min-h-screen bg-white">
      <Header />

      <article className="py-8">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            {/* Back Button */}
            <div className="mb-6">
              <Link href="/blog">
                <Button variant="outline" size="sm">
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Back to Blog
                </Button>
              </Link>
            </div>

            {/* Article Header */}
            <header className="mb-8">
              <Badge className="mb-4">{post.category}</Badge>
              <h1 className="text-4xl font-bold mb-4">{post.title}</h1>
              <p className="text-xl text-gray-600 mb-6">{post.excerpt}</p>

              {/* Author and Meta Info */}
              <div className="flex items-center justify-between mb-6">
                <div className="flex items-center space-x-4">
                  <Avatar className="w-12 h-12">
                    <AvatarImage src={post.author.avatar || "/placeholder.svg"} />
                    <AvatarFallback>{post.author.name.charAt(0)}</AvatarFallback>
                  </Avatar>
                  <div>
                    <div className="flex items-center space-x-2">
                      <h3 className="font-semibold">{post.author.name}</h3>
                      {post.author.verified && <Badge className="bg-green-600 text-xs">Verified</Badge>}
                    </div>
                    <p className="text-sm text-gray-600">{post.author.bio}</p>
                  </div>
                </div>

                <div className="flex items-center space-x-4 text-sm text-gray-600">
                  <div className="flex items-center">
                    <Calendar className="h-4 w-4 mr-1" />
                    {post.publishDate}
                  </div>
                  <div className="flex items-center">
                    <Clock className="h-4 w-4 mr-1" />
                    {post.readTime}
                  </div>
                  <div className="flex items-center">
                    <Eye className="h-4 w-4 mr-1" />
                    {post.views}
                  </div>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex items-center space-x-4 mb-8">
                <Button variant="outline" size="sm">
                  <Heart className="h-4 w-4 mr-2" />
                  {post.likes}
                </Button>
                <Button variant="outline" size="sm">
                  <Share2 className="h-4 w-4 mr-2" />
                  Share
                </Button>
                <Button variant="outline" size="sm">
                  <Bookmark className="h-4 w-4 mr-2" />
                  Save
                </Button>
              </div>

              {/* Featured Image */}
              <div className="relative mb-8">
                <Image
                  src={post.image || "/placeholder.svg"}
                  alt={post.title}
                  width={800}
                  height={400}
                  className="w-full h-96 object-cover rounded-lg"
                />
              </div>
            </header>

            {/* Article Content */}
            <div className="prose prose-lg max-w-none mb-12">
              <div dangerouslySetInnerHTML={{ __html: post.content }} />
            </div>

            {/* Tags */}
            <div className="mb-8">
              <h3 className="font-semibold mb-3">Tags</h3>
              <div className="flex flex-wrap gap-2">
                {post.tags.map((tag, index) => (
                  <Badge key={index} variant="outline" className="text-xs">
                    #{tag}
                  </Badge>
                ))}
              </div>
            </div>

            <Separator className="my-8" />

            {/* Author Bio */}
            <div className="bg-gray-50 rounded-lg p-6 mb-8">
              <div className="flex items-start space-x-4">
                <Avatar className="w-16 h-16">
                  <AvatarImage src={post.author.avatar || "/placeholder.svg"} />
                  <AvatarFallback>{post.author.name.charAt(0)}</AvatarFallback>
                </Avatar>
                <div className="flex-1">
                  <div className="flex items-center space-x-2 mb-2">
                    <h3 className="text-xl font-semibold">{post.author.name}</h3>
                    {post.author.verified && <Badge className="bg-green-600 text-xs">Verified</Badge>}
                  </div>
                  <p className="text-gray-600 mb-4">{post.author.bio}</p>
                  <div className="flex space-x-3">
                    <Button variant="outline" size="sm">
                      Follow
                    </Button>
                    <Button variant="outline" size="sm">
                      View Profile
                    </Button>
                  </div>
                </div>
              </div>
            </div>

            {/* Comments Section */}
            <div className="mb-12">
              <h3 className="text-2xl font-bold mb-6 flex items-center">
                <MessageCircle className="h-6 w-6 mr-2" />
                Comments ({post.comments.length})
              </h3>

              <div className="space-y-6">
                {post.comments.map((comment) => (
                  <div key={comment.id} className="border-b pb-6 last:border-b-0">
                    <div className="flex items-start space-x-4">
                      <Avatar>
                        <AvatarImage src={comment.avatar || "/placeholder.svg"} />
                        <AvatarFallback>{comment.user.charAt(0)}</AvatarFallback>
                      </Avatar>
                      <div className="flex-1">
                        <div className="flex items-center justify-between mb-2">
                          <h4 className="font-semibold">{comment.user}</h4>
                          <span className="text-sm text-gray-500">{comment.date}</span>
                        </div>
                        <p className="text-gray-700 mb-3">{comment.comment}</p>
                        <div className="flex items-center space-x-4">
                          <Button variant="ghost" size="sm" className="text-gray-500 hover:text-primary">
                            <Heart className="h-4 w-4 mr-1" />
                            {comment.likes}
                          </Button>
                          <Button variant="ghost" size="sm" className="text-primary hover:text-primary/90">
                            Reply
                          </Button>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              {/* Add Comment */}
              <div className="mt-8 pt-6 border-t">
                <h4 className="font-semibold mb-4">Leave a Comment</h4>
                <div className="space-y-4">
                  <textarea
                    placeholder="Share your thoughts..."
                    className="w-full p-4 border rounded-lg resize-none focus:outline-none focus:ring-2 focus:ring-primary-500"
                    rows={4}
                  />
                  <div className="flex justify-end">
                    <Button className="bg-primary hover:bg-primary/90">Post Comment</Button>
                  </div>
                </div>
              </div>
            </div>

            {/* Related Posts */}
            <div>
              <h3 className="text-2xl font-bold mb-6">Related Articles</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {post.relatedPosts.map((relatedPost) => (
                  <Card key={relatedPost.id} className="overflow-hidden hover:shadow-lg transition-shadow">
                    <div className="relative">
                      <Image
                        src={relatedPost.image || "/placeholder.svg"}
                        alt={relatedPost.title}
                        width={300}
                        height={200}
                        className="w-full h-48 object-cover"
                      />
                    </div>
                    <CardContent className="p-4">
                      <Badge variant="secondary" className="mb-2 text-xs">
                        {relatedPost.category}
                      </Badge>
                      <h4 className="font-bold text-lg mb-2 line-clamp-2">{relatedPost.title}</h4>
                      <p className="text-gray-600 text-sm mb-3 line-clamp-2">{relatedPost.excerpt}</p>
                      <div className="flex items-center justify-between">
                        <span className="text-xs text-gray-500">{relatedPost.readTime}</span>
                        <Button variant="ghost" size="sm" className="text-primary hover:text-primary/90">
                          Read More <ArrowRight className="h-4 w-4 ml-1" />
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          </div>
        </div>
      </article>

      <Footer />
    </div>
  )
}
