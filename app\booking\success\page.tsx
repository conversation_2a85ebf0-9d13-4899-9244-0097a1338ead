"use client"

import { useEffect, useState } from 'react'
import { useSearchParams, useRouter } from 'next/navigation'
import { TopHeader } from "@/components/top-header"
import { Header } from "@/components/header"
import { Footer } from "@/components/footer"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import {
  CheckCircle,
  Calendar,
  Clock,
  Phone,
  Mail,
  MessageCircle,
  Home,
  ArrowRight,
  Star,
  Share2,
  FileText,
  Download
} from 'lucide-react'
import Link from 'next/link'
import { invoiceService } from "@/lib/services/invoiceService"
import { useAuth } from "@/contexts/AuthContext"
import { showToast } from "@/lib/toast"
import { emailService } from "@/lib/services/emailService";

export default function BookingSuccessPage() {
  const searchParams = useSearchParams()
  const router = useRouter()
  const { user, userProfile } = useAuth()

  const bookingType = searchParams.get('type') as 'vendor' | 'venue'
  const entityId = searchParams.get('id')
  const bookingId = searchParams.get('bookingId')

  const [showConfetti, setShowConfetti] = useState(true)

  useEffect(() => {
    // Hide confetti after 3 seconds
    const timer = setTimeout(() => {
      setShowConfetti(false)
    }, 3000)

    // Send booking confirmation email
    if (userProfile?.email && bookingId) {
      const sendConfirmationEmail = async () => {
        try {
          await emailService.sendBookingConfirmationEmail({
            email: userProfile.email,
            userName: userProfile?.firstName || userProfile.email,
            bookingId: bookingId,
            entityName: 'Your Selected Vendor', // You can make this dynamic
            entityType: bookingType,
            eventDate: new Date().toLocaleDateString(), // You can make this dynamic
            status: 'confirmed'
          });
          console.log('✅ Booking confirmation email sent successfully');
        } catch (emailError) {
          console.error('Error sending booking confirmation email:', emailError);
          // Don't fail the page if email fails
        }
      };
      
      sendConfirmationEmail();
    }

    if (searchParams) {
      // Generate invoice for the booking
      if (user?.userId && bookingId && entityId) {
        const generateBookingInvoice = async () => {
          try {
            if (bookingType === 'vendor') {
              // Generate vendor service invoice
              const invoiceResult = await invoiceService.generateVendorServiceInvoice({
                bookingId: bookingId,
                customerId: user.userId,
                customerName: userProfile?.firstName && userProfile?.lastName 
                  ? `${userProfile.firstName} ${userProfile.lastName}` 
                  : userProfile?.firstName || 'Customer',
                customerEmail: userProfile?.email || '',
                customerPhone: userProfile?.phone,
                customerAddress: userProfile?.address ? {
                  street: userProfile.address.street || '',
                  city: userProfile.address.city || '',
                  state: userProfile.address.state || '',
                  pincode: userProfile.address.pincode || '',
                  country: userProfile.address.country || 'India'
                } : undefined,
                vendorId: entityId,
                vendorName: 'Selected Vendor', // This should come from the booking data
                vendorEmail: '<EMAIL>', // This should come from the booking data
                vendorBusinessName: 'Vendor Business', // This should come from the booking data
                serviceName: 'Vendor Service',
                serviceDescription: 'Vendor service booking',
                eventDate: searchParams.get('eventDate') || new Date().toISOString().split('T')[0],
                eventTime: searchParams.get('eventTime') || '09:00 AM',
                eventLocation: searchParams.get('eventLocation') || 'Event Location',
                amount: parseFloat(searchParams.get('amount') || '0'),
                taxAmount: parseFloat(searchParams.get('taxAmount') || '0'),
                discountAmount: parseFloat(searchParams.get('discountAmount') || '0'),
                totalAmount: parseFloat(searchParams.get('totalAmount') || '0'),
                paymentStatus: 'pending',
                paymentMethod: searchParams.get('paymentMethod') || 'Online',
                transactionId: searchParams.get('transactionId') || `TXN_${Date.now()}`,
                terms: '50% advance payment required'
              });

              if (invoiceResult.success) {
                console.log('✅ Vendor service invoice generated successfully');
              } else {
                console.error('Failed to generate vendor service invoice:', invoiceResult.error);
              }
            } else if (bookingType === 'venue') {
              // Generate venue booking invoice
              const invoiceResult = await invoiceService.generateVenueBookingInvoice({
                bookingId: bookingId,
                customerId: user.userId,
                customerName: userProfile?.firstName && userProfile?.lastName 
                  ? `${userProfile.firstName} ${userProfile.lastName}` 
                  : userProfile?.firstName || 'Customer',
                customerEmail: userProfile?.email || '',
                customerPhone: userProfile?.phone,
                customerAddress: userProfile?.address ? {
                  street: userProfile.address.street || '',
                  city: userProfile.address.city || '',
                  state: userProfile.address.state || '',
                  pincode: userProfile.address.pincode || '',
                  country: userProfile.address.country || 'India'
                } : undefined,
                venueId: entityId,
                venueName: 'Selected Venue', // This should come from the booking data
                venueEmail: '<EMAIL>', // This should come from the booking data
                venueAddress: {
                  street: 'Venue Street', // This should come from the booking data
                  city: 'Venue City', // This should come from the booking data
                  state: 'Venue State', // This should come from the booking data
                  pincode: '600001', // This should come from the booking data
                  country: 'India'
                },
                packageName: 'Venue Package',
                packageDescription: 'Venue booking package',
                eventDate: searchParams.get('eventDate') || new Date().toISOString().split('T')[0],
                eventTime: searchParams.get('eventTime') || '06:00 PM',
                eventDuration: searchParams.get('eventDuration') || '8 hours',
                capacity: searchParams.get('capacity') || '100 guests',
                amount: parseFloat(searchParams.get('amount') || '0'),
                taxAmount: parseFloat(searchParams.get('taxAmount') || '0'),
                discountAmount: parseFloat(searchParams.get('discountAmount') || '0'),
                totalAmount: parseFloat(searchParams.get('totalAmount') || '0'),
                paymentStatus: 'pending',
                paymentMethod: searchParams.get('paymentMethod') || 'Online',
                transactionId: searchParams.get('transactionId') || `TXN_${Date.now()}`,
                terms: '30% advance payment required'
              });

              if (invoiceResult.success) {
                console.log('✅ Venue booking invoice generated successfully');
              } else {
                console.error('Failed to generate venue booking invoice:', invoiceResult.error);
              }
            }
          } catch (invoiceError) {
            console.error('Error generating booking invoice:', invoiceError);
            // Don't fail the page if invoice generation fails
          }
        };
        
        generateBookingInvoice();
      }
    }

    return () => clearTimeout(timer)
  }, [user, bookingId, userProfile, bookingType, entityId, searchParams])

  const handleDownloadInvoice = async () => {
    if (!bookingId || !user) {
      showToast.error('Booking information not available');
      return;
    }

    try {
      // Find invoice for this booking
      const invoices = await invoiceService.getInvoicesForUser(user.userId, 'customer');
      const bookingInvoice = invoices.find(invoice =>
        invoice.notes?.includes(bookingId) ||
        invoice.eventDate === searchParams?.get('eventDate')
      );

      if (bookingInvoice) {
        // Download the invoice - use invoiceNumber as identifier since InvoiceData doesn't have id
        const downloadResult = await invoiceService.downloadInvoicePDF(bookingInvoice.invoiceNumber);
        if (downloadResult.success) {
          showToast.success('Invoice downloaded successfully');
        } else {
          showToast.error('Failed to download invoice');
        }
      } else {
        showToast.error('Invoice not found for this booking');
      }
    } catch (error) {
      console.error('Error downloading invoice:', error);
      showToast.error('Error downloading invoice');
    }
  }

  const handleViewInvoice = async () => {
    if (!bookingId || !user) {
      showToast.error('Booking information not available');
      return;
    }

    try {
      // Find invoice for this booking
      const invoices = await invoiceService.getInvoicesForUser(user.userId, 'customer');
      const bookingInvoice = invoices.find(invoice =>
        invoice.notes?.includes(bookingId) ||
        invoice.eventDate === searchParams?.get('eventDate')
      );

      if (bookingInvoice) {
        // Navigate to invoice page
        router.push(`/dashboard/invoices?invoiceId=${bookingInvoice.id}`);
      } else {
        showToast.error('Invoice not found for this booking');
      }
    } catch (error) {
      console.error('Error viewing invoice:', error);
      showToast.error('Error viewing invoice');
    }
  }

  const nextSteps = [
    {
      icon: Phone,
      title: "Confirmation Call",
      description: `The ${bookingType} will contact you within 24 hours to confirm availability and discuss details.`,
      timeframe: "Within 24 hours"
    },
    {
      icon: Calendar,
      title: "Schedule Meeting",
      description: "Set up a detailed discussion about your requirements, timeline, and pricing.",
      timeframe: "2-3 days"
    },
    {
      icon: CheckCircle,
      title: "Finalize Booking",
      description: "Complete the booking with contract signing and advance payment.",
      timeframe: "Within a week"
    }
  ]

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 to-blue-50">
      <TopHeader />
      <Header />
      
      {/* Confetti Effect */}
      {showConfetti && (
        <div className="fixed inset-0 pointer-events-none z-50">
          <div className="absolute inset-0 overflow-hidden">
            {[...Array(50)].map((_, i) => (
              <div
                key={i}
                className="absolute animate-bounce"
                style={{
                  left: `${Math.random() * 100}%`,
                  top: `${Math.random() * 100}%`,
                  animationDelay: `${Math.random() * 2}s`,
                  animationDuration: `${2 + Math.random() * 2}s`
                }}
              >
                <div className="w-2 h-2 bg-primary rounded-full opacity-70"></div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Success Section */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            {/* Success Icon */}
            <div className="w-24 h-24 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-8">
              <CheckCircle className="w-12 h-12 text-green-600" />
            </div>

            {/* Success Message */}
            <h1 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-4">
              Booking Request Submitted!
            </h1>
            
            <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
              Thank you for choosing our platform! Your booking request has been successfully submitted. 
              The {bookingType} will contact you soon to confirm the details.
            </p>

            {/* Booking Reference */}
            <div className="bg-white rounded-lg shadow-lg p-6 mb-12 max-w-md mx-auto">
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Booking Reference</h3>
              <div className="text-2xl font-bold text-primary">
                #{Math.random().toString(36).substr(2, 9).toUpperCase()}
              </div>
              <p className="text-sm text-gray-500 mt-2">
                Save this reference number for future communication
              </p>
            </div>

            {/* Quick Actions */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4 mb-12">
              <Button
                variant="outline"
                className="flex items-center justify-center gap-2 py-3"
                onClick={() => router.push('/dashboard')}
              >
                <Calendar className="h-4 w-4" />
                View My Bookings
              </Button>

              {/* Invoice Actions */}
              {bookingId && user && (
                <>
                  <Button
                    variant="outline"
                    className="flex items-center justify-center gap-2 py-3"
                    onClick={handleViewInvoice}
                  >
                    <FileText className="h-4 w-4" />
                    View Invoice
                  </Button>

                  <Button
                    variant="outline"
                    className="flex items-center justify-center gap-2 py-3"
                    onClick={handleDownloadInvoice}
                  >
                    <Download className="h-4 w-4" />
                    Download Invoice
                  </Button>
                </>
              )}

              <Button
                variant="outline"
                className="flex items-center justify-center gap-2 py-3"
                onClick={() => router.push(`/${bookingType}s`)}
              >
                <Star className="h-4 w-4" />
                Browse More {bookingType}s
              </Button>

              <Button
                variant="outline"
                className="flex items-center justify-center gap-2 py-3"
                onClick={() => {
                  if (navigator.share) {
                    navigator.share({
                      title: 'Wedding Booking Success',
                      text: `I just booked a ${bookingType} for my wedding!`,
                      url: window.location.href
                    })
                  }
                }}
              >
                <Share2 className="h-4 w-4" />
                Share Good News
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Next Steps Section */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-3xl font-bold text-center text-gray-900 mb-12">
              What Happens Next?
            </h2>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {nextSteps.map((step, index) => (
                <Card key={index} className="relative">
                  <CardContent className="p-6 text-center">
                    {/* Step Number */}
                    <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                      <div className="w-8 h-8 bg-primary text-white rounded-full flex items-center justify-center font-bold text-sm">
                        {index + 1}
                      </div>
                    </div>
                    
                    {/* Icon */}
                    <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4 mt-4">
                      <step.icon className="w-8 h-8 text-primary" />
                    </div>
                    
                    {/* Content */}
                    <h3 className="text-xl font-semibold text-gray-900 mb-2">
                      {step.title}
                    </h3>
                    <p className="text-gray-600 mb-4">
                      {step.description}
                    </p>
                    <div className="text-sm font-medium text-primary">
                      {step.timeframe}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Contact Support Section */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-2xl mx-auto text-center">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">
              Need Help or Have Questions?
            </h2>
            <p className="text-gray-600 mb-8">
              Our support team is here to help you with any questions about your booking.
            </p>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Card>
                <CardContent className="p-6 text-center">
                  <MessageCircle className="w-8 h-8 text-primary mx-auto mb-3" />
                  <h3 className="font-semibold mb-2">Live Chat</h3>
                  <p className="text-sm text-gray-600 mb-4">
                    Get instant help from our support team
                  </p>
                  <Button variant="outline" className="w-full">
                    Start Chat
                  </Button>
                </CardContent>
              </Card>
              
              <Card>
                <CardContent className="p-6 text-center">
                  <Mail className="w-8 h-8 text-primary mx-auto mb-3" />
                  <h3 className="font-semibold mb-2">Email Support</h3>
                  <p className="text-sm text-gray-600 mb-4">
                    Send us your questions via email
                  </p>
                  <Button variant="outline" className="w-full">
                    <a href="mailto:<EMAIL>">
                      Send Email
                    </a>
                  </Button>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-gradient-to-r from-primary to-pink-600 text-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold mb-4">
            Continue Planning Your Perfect Wedding
          </h2>
          <p className="text-xl text-white/90 mb-8 max-w-2xl mx-auto">
            Explore more vendors, venues, and services to make your wedding day unforgettable.
          </p>
          
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/">
              <Button variant="secondary" className="bg-white text-primary hover:bg-white/90">
                <Home className="h-4 w-4 mr-2" />
                Back to Home
              </Button>
            </Link>
            
            <Link href="/planning/checklist">
              <Button variant="outline" className="border-white text-white hover:bg-white hover:text-primary">
                Wedding Checklist
                <ArrowRight className="h-4 w-4 ml-2" />
              </Button>
            </Link>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  )
}
