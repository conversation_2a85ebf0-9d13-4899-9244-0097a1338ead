/**
 * Lambda function to get subscription payment history
 * <PERSON>les fetching payment history for a subscription
 */

const AWS = require('aws-sdk');
const dynamodb = new AWS.DynamoDB.DocumentClient();

const TABLE_NAME = process.env.API_THIRUMANAM360_SUBSCRIPTIONPAYMENTTABLE_NAME;

exports.handler = async (event) => {
  console.log('Event:', JSON.stringify(event, null, 2));
  
  try {
    const { subscriptionId, limit = 50, nextToken } = event.arguments;
    
    if (!subscriptionId) {
      return {
        statusCode: 400,
        body: JSON.stringify({
          error: 'subscriptionId is required'
        })
      };
    }

    // Query for payments by subscriptionId
    const params = {
      TableName: TABLE_NAME,
      IndexName: 'bySubscriptionId',
      KeyConditionExpression: 'subscriptionId = :subscriptionId',
      ExpressionAttributeValues: {
        ':subscriptionId': subscriptionId
      },
      ScanIndexForward: false, // Get most recent first
      Limit: limit
    };

    if (nextToken) {
      params.ExclusiveStartKey = JSON.parse(Buffer.from(nextToken, 'base64').toString('utf8'));
    }

    console.log('DynamoDB Query params:', JSON.stringify(params, null, 2));
    
    const result = await dynamodb.query(params).promise();
    
    console.log('DynamoDB result:', JSON.stringify(result, null, 2));
    
    const response = {
      items: result.Items || [],
      nextToken: result.LastEvaluatedKey 
        ? Buffer.from(JSON.stringify(result.LastEvaluatedKey)).toString('base64')
        : null
    };
    
    return response;
    
  } catch (error) {
    console.error('Error fetching subscription payments:', error);
    
    return {
      statusCode: 500,
      body: JSON.stringify({
        error: 'Failed to fetch subscription payments',
        details: error.message
      })
    };
  }
};
