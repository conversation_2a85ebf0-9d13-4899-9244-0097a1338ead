import { generateClient } from 'aws-amplify/api';
import { createContact, updateContact, deleteContact } from '../graphql/mutations';
import { listContacts } from '../graphql/queries';

const client = generateClient();
const authenticatedClient = generateClient();
const publicClient = generateClient();

/**
 * Service for managing contact form submissions
 */
export class ContactService {
  /**
   * Submit a contact form
   */
  static async submitContact(contactData) {
    try {
      console.log('Submitting contact form with data:', contactData);

      // Validate required fields
      const requiredFields = ['name', 'email', 'subject', 'message'];
      const missingFields = [];

      for (const field of requiredFields) {
        if (!contactData[field] || contactData[field] === '' || contactData[field] === null || contactData[field] === undefined) {
          missingFields.push(field);
        }
      }

      if (missingFields.length > 0) {
        throw new Error(`Missing required fields: ${missingFields.join(', ')}`);
      }

      // Validate email format
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(contactData.email)) {
        throw new Error('Please enter a valid email address');
      }

      // Prepare contact data for GraphQL
      const contactInput = {
        name: contactData.name.trim(),
        email: contactData.email.trim().toLowerCase(),
        phone: contactData.phone ? contactData.phone.trim() : undefined,
        subject: contactData.subject.trim(),
        message: contactData.message.trim(),
        inquiryType: contactData.inquiryType || 'GENERAL',
        status: 'NEW',
        priority: contactData.priority || 'MEDIUM'
      };

      // Add optional fields if provided
      if (contactData.assignedTo) {
        contactInput.assignedTo = contactData.assignedTo;
      }

      const result = await client.graphql({
        query: createContact,
        variables: {
          input: contactInput
        }
      });

      console.log('Contact form submitted successfully:', result);

      return {
        success: true,
        data: result.data.createContact,
        message: 'Thank you for your message! We\'ll get back to you within 24 hours.'
      };

    } catch (error) {
      console.error('Error submitting contact form:', error);
      
      let errorMessage = 'Failed to submit contact form';
      
      if (error.message) {
        errorMessage = error.message;
      } else if (error.errors && error.errors.length > 0) {
        errorMessage = error.errors[0].message;
      }

      return {
        success: false,
        error: errorMessage
      };
    }
  }

  /**
   * List all contact submissions (admin only)
   */
  static async listContacts(options = {}) {
    try {
      const { limit = 50, nextToken = null } = options;

      // Try authenticated client first (for logged-in admins)
      let result;
      try {
        result = await authenticatedClient.graphql({
          query: listContacts,
          variables: {
            limit,
            nextToken
          }
        });
      } catch (authError) {
        console.log('Authenticated listContacts failed, trying with API key:', authError);
        // Fallback to public client with API key for dashboard operations
        result = await publicClient.graphql({
          query: listContacts,
          variables: {
            limit,
            nextToken
          }
        });
      }

      return {
        success: true,
        data: result.data.listContacts.items,
        nextToken: result.data.listContacts.nextToken
      };
    } catch (error) {
      console.error('Error listing contacts:', error);
      return {
        success: false,
        error: error.message || 'Failed to list contacts'
      };
    }
  }

  /**
   * Get unread contact count (admin only)
   */
  static async getUnreadCount() {
    try {
      // Try authenticated client first (for logged-in admins)
      let result;
      try {
        result = await authenticatedClient.graphql({
          query: listContacts,
          variables: {
            filter: {
              status: { eq: 'NEW' } // Use status instead of isRead
            },
            limit: 1000 // Get all unread to count them
          }
        });
      } catch (authError) {
        console.log('Authenticated getUnreadCount failed, trying with API key:', authError);
        // Fallback to public client with API key
        result = await publicClient.graphql({
          query: listContacts,
          variables: {
            filter: {
              status: { eq: 'NEW' } // Use status instead of isRead
            },
            limit: 1000 // Get all unread to count them
          }
        });
      }

      return {
        success: true,
        count: result.data.listContacts.items.length
      };
    } catch (error) {
      console.error('Error getting unread contact count:', error);
      return {
        success: false,
        error: error.message || 'Failed to get unread count'
      };
    }
  }

  /**
   * Mark contact as read (admin only)
   */
  static async markAsRead(contactId) {
    try {
      const result = await client.graphql({
        query: updateContact,
        variables: {
          input: {
            id: contactId,
            isRead: true,
            updatedAt: new Date().toISOString()
          }
        }
      });

      return {
        success: true,
        data: result.data.updateContact
      };
    } catch (error) {
      console.error('Error marking contact as read:', error);
      return {
        success: false,
        error: error.message || 'Failed to mark contact as read'
      };
    }
  }

  /**
   * Mark contact as resolved (admin only)
   */
  static async markAsResolved(contactId) {
    try {
      const result = await client.graphql({
        query: updateContact,
        variables: {
          input: {
            id: contactId,
            isResolved: true,
            isRead: true,
            updatedAt: new Date().toISOString()
          }
        }
      });

      return {
        success: true,
        data: result.data.updateContact
      };
    } catch (error) {
      console.error('Error marking contact as resolved:', error);
      return {
        success: false,
        error: error.message || 'Failed to mark contact as resolved'
      };
    }
  }

  /**
   * Delete contact (admin only)
   */
  static async deleteContact(contactId) {
    try {
      const result = await client.graphql({
        query: deleteContact,
        variables: {
          input: { id: contactId }
        }
      });

      return {
        success: true,
        data: result.data.deleteContact
      };
    } catch (error) {
      console.error('Error deleting contact:', error);
      return {
        success: false,
        error: error.message || 'Failed to delete contact'
      };
    }
  }

  /**
   * Send automated response email (if configured)
   */
  static async sendAutoResponse(contactData) {
    try {
      // This would integrate with your email service (SES, SendGrid, etc.)
      // For now, we'll just log it
      console.log('Auto-response would be sent to:', contactData.email);
      
      return {
        success: true,
        message: 'Auto-response sent successfully'
      };
    } catch (error) {
      console.error('Error sending auto-response:', error);
      return {
        success: false,
        error: error.message || 'Failed to send auto-response'
      };
    }
  }

  /**
   * Validate contact form data
   */
  static validateContactData(data) {
    const errors = [];

    // Required fields
    if (!data.name || data.name.trim().length < 2) {
      errors.push('Name must be at least 2 characters long');
    }

    if (!data.email || !data.email.includes('@')) {
      errors.push('Please enter a valid email address');
    }

    if (!data.subject || data.subject.trim().length < 5) {
      errors.push('Subject must be at least 5 characters long');
    }

    if (!data.message || data.message.trim().length < 10) {
      errors.push('Message must be at least 10 characters long');
    }

    // Optional phone validation
    if (data.phone && data.phone.trim().length > 0) {
      const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
      if (!phoneRegex.test(data.phone.replace(/[\s\-\(\)]/g, ''))) {
        errors.push('Please enter a valid phone number');
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Format contact data for display
   */
  static formatContactForDisplay(contact) {
    return {
      ...contact,
      formattedDate: new Date(contact.createdAt).toLocaleDateString(),
      formattedTime: new Date(contact.createdAt).toLocaleTimeString(),
      shortMessage: contact.message.length > 100
        ? contact.message.substring(0, 100) + '...'
        : contact.message
    };
  }

  /**
   * Get contact statistics (admin only)
   */
  static async getContactStats() {
    try {
      // Try authenticated client first (for logged-in admins)
      let result;
      try {
        result = await authenticatedClient.graphql({
          query: listContacts,
          variables: {
            limit: 1000 // Get all contacts to calculate stats
          }
        });
      } catch (authError) {
        console.log('Authenticated getContactStats failed, trying with API key:', authError);
        // Fallback to public client with API key
        result = await publicClient.graphql({
          query: listContacts,
          variables: {
            limit: 1000 // Get all contacts to calculate stats
          }
        });
      }

      const contacts = result.data.listContacts.items;

      const stats = {
        total: contacts.length,
        new: contacts.filter(c => c.status === 'NEW').length,
        inProgress: contacts.filter(c => c.status === 'IN_PROGRESS').length,
        resolved: contacts.filter(c => c.status === 'RESOLVED').length,
        closed: contacts.filter(c => c.status === 'CLOSED').length,
        highPriority: contacts.filter(c => c.priority === 'HIGH').length,
        mediumPriority: contacts.filter(c => c.priority === 'MEDIUM').length,
        lowPriority: contacts.filter(c => c.priority === 'LOW').length
      };

      return {
        success: true,
        data: stats
      };
    } catch (error) {
      console.error('Error getting contact stats:', error);
      return {
        success: false,
        error: error.message || 'Failed to get contact stats'
      };
    }
  }

  /**
   * Get contacts by status (admin only)
   */
  static async getContactsByStatus(status) {
    try {
      // Try authenticated client first (for logged-in admins)
      let result;
      try {
        result = await authenticatedClient.graphql({
          query: listContacts,
          variables: {
            filter: {
              status: { eq: status }
            },
            limit: 50
          }
        });
      } catch (authError) {
        console.log('Authenticated getContactsByStatus failed, trying with API key:', authError);
        // Fallback to public client with API key
        result = await publicClient.graphql({
          query: listContacts,
          variables: {
            filter: {
              status: { eq: status }
            },
            limit: 50
          }
        });
      }

      return {
        success: true,
        data: result.data.listContacts.items,
        nextToken: result.data.listContacts.nextToken
      };
    } catch (error) {
      console.error('Error getting contacts by status:', error);
      return {
        success: false,
        error: error.message || 'Failed to get contacts by status'
      };
    }
  }

  /**
   * Get contacts by inquiry type (admin only)
   */
  static async getContactsByInquiryType(inquiryType) {
    try {
      // Try authenticated client first (for logged-in admins)
      let result;
      try {
        result = await authenticatedClient.graphql({
          query: listContacts,
          variables: {
            filter: {
              inquiryType: { eq: inquiryType }
            },
            limit: 50
          }
        });
      } catch (authError) {
        console.log('Authenticated getContactsByInquiryType failed, trying with API key:', authError);
        // Fallback to public client with API key
        result = await publicClient.graphql({
          query: listContacts,
          variables: {
            filter: {
              inquiryType: { eq: inquiryType }
            },
            limit: 50
          }
        });
      }

      return {
        success: true,
        data: result.data.listContacts.items,
        nextToken: result.data.listContacts.nextToken
      };
    } catch (error) {
      console.error('Error getting contacts by inquiry type:', error);
      return {
        success: false,
        error: error.message || 'Failed to get contacts by inquiry type'
      };
    }
  }

  /**
   * Update contact (admin only)
   */
  static async updateContact(contactId, updateData) {
    try {
      // Try authenticated client first (for logged-in admins)
      let result;
      try {
        result = await authenticatedClient.graphql({
          query: updateContact,
          variables: {
            input: {
              id: contactId,
              ...updateData
            }
          }
        });
      } catch (authError) {
        console.log('Authenticated updateContact failed, trying with API key:', authError);
        // Fallback to public client with API key
        result = await publicClient.graphql({
          query: updateContact,
          variables: {
            input: {
              id: contactId,
              ...updateData
            }
          }
        });
      }

      return {
        success: true,
        data: result.data.updateContact
      };
    } catch (error) {
      console.error('Error updating contact:', error);
      return {
        success: false,
        error: error.message || 'Failed to update contact'
      };
    }
  }

  /**
   * Subscribe to contact creation events (admin only)
   */
  static subscribeToContactCreation(callback) {
    try {
      // For now, return a mock subscription
      // In a real implementation, this would use GraphQL subscriptions
      console.log('Contact creation subscription would be set up here');
      return {
        unsubscribe: () => console.log('Contact creation subscription unsubscribed')
      };
    } catch (error) {
      console.error('Error setting up contact creation subscription:', error);
      return null;
    }
  }

  /**
   * Subscribe to contact update events (admin only)
   */
  static subscribeToContactUpdates(callback) {
    try {
      // For now, return a mock subscription
      // In a real implementation, this would use GraphQL subscriptions
      console.log('Contact update subscription would be set up here');
      return {
        unsubscribe: () => console.log('Contact update subscription unsubscribed')
      };
    } catch (error) {
      console.error('Error setting up contact update subscription:', error);
      return null;
    }
  }

  /**
   * Subscribe to contact deletion events (admin only)
   */
  static subscribeToContactDeletion(callback) {
    try {
      // For now, return a mock subscription
      // In a real implementation, this would use GraphQL subscriptions
      console.log('Contact deletion subscription would be set up here');
      return {
        unsubscribe: () => console.log('Contact deletion subscription unsubscribed')
      };
    } catch (error) {
      console.error('Error setting up contact deletion subscription:', error);
      return null;
    }
  }
}
