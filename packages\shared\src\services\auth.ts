import { User, UserProfile, AuthState, LoginCredentials, SignupData } from '../types';

export class AuthService {
  private platform: 'web' | 'mobile';
  private storageKey = 'thirumanam-auth';

  constructor(platform: 'web' | 'mobile' = 'web') {
    this.platform = platform;
  }

  // Storage abstraction
  private async getStorage(): Promise<any> {
    if (this.platform === 'web') {
      return localStorage;
    } else {
      // For React Native, use AsyncStorage
      const AsyncStorage = await import('@react-native-async-storage/async-storage');
      return AsyncStorage.default;
    }
  }

  private async getItem(key: string): Promise<string | null> {
    const storage = await this.getStorage();
    
    if (this.platform === 'web') {
      return storage.getItem(key);
    } else {
      return await storage.getItem(key);
    }
  }

  private async setItem(key: string, value: string): Promise<void> {
    const storage = await this.getStorage();
    
    if (this.platform === 'web') {
      storage.setItem(key, value);
    } else {
      await storage.setItem(key, value);
    }
  }

  private async removeItem(key: string): Promise<void> {
    const storage = await this.getStorage();
    
    if (this.platform === 'web') {
      storage.removeItem(key);
    } else {
      await storage.removeItem(key);
    }
  }

  // Auth state management
  async getAuthState(): Promise<AuthState> {
    try {
      const authData = await this.getItem(this.storageKey);
      if (authData) {
        const parsed = JSON.parse(authData);
        return {
          isAuthenticated: parsed.isAuthenticated || false,
          isLoading: false,
          user: parsed.user || null,
          userProfile: parsed.userProfile || null,
          userType: parsed.userType || null,
          error: null,
        };
      }
    } catch (error) {
      console.error('Error loading auth state:', error);
    }

    return {
      isAuthenticated: false,
      isLoading: false,
      user: null,
      userProfile: null,
      userType: null,
      error: null,
    };
  }

  async saveAuthState(authState: Partial<AuthState>): Promise<void> {
    try {
      const currentState = await this.getAuthState();
      const newState = { ...currentState, ...authState };
      await this.setItem(this.storageKey, JSON.stringify(newState));
    } catch (error) {
      console.error('Error saving auth state:', error);
    }
  }

  async clearAuthState(): Promise<void> {
    try {
      await this.removeItem(this.storageKey);
    } catch (error) {
      console.error('Error clearing auth state:', error);
    }
  }

  // Token management
  async getToken(): Promise<string | null> {
    try {
      return await this.getItem('thirumanam-token');
    } catch (error) {
      console.error('Error getting token:', error);
      return null;
    }
  }

  async setToken(token: string): Promise<void> {
    try {
      await this.setItem('thirumanam-token', token);
    } catch (error) {
      console.error('Error setting token:', error);
    }
  }

  async removeToken(): Promise<void> {
    try {
      await this.removeItem('thirumanam-token');
    } catch (error) {
      console.error('Error removing token:', error);
    }
  }

  // Auth operations
  async login(credentials: LoginCredentials, apiService: any): Promise<AuthState> {
    try {
      const response = await apiService.login(credentials.email, credentials.password);
      
      if (response.success && response.data) {
        const { user, token, userProfile } = response.data;
        
        // Save token
        await this.setToken(token);
        
        // Save auth state
        const authState: AuthState = {
          isAuthenticated: true,
          isLoading: false,
          user,
          userProfile,
          userType: user.role || 'customer',
          error: null,
        };
        
        await this.saveAuthState(authState);
        return authState;
      } else {
        throw new Error(response.message || 'Login failed');
      }
    } catch (error) {
      const errorState: AuthState = {
        isAuthenticated: false,
        isLoading: false,
        user: null,
        userProfile: null,
        userType: null,
        error: error instanceof Error ? error.message : 'Login failed',
      };
      
      await this.saveAuthState(errorState);
      throw error;
    }
  }

  async signup(signupData: SignupData, apiService: any): Promise<AuthState> {
    try {
      const response = await apiService.signup(signupData);
      
      if (response.success && response.data) {
        const { user, token, userProfile } = response.data;
        
        // Save token
        await this.setToken(token);
        
        // Save auth state
        const authState: AuthState = {
          isAuthenticated: true,
          isLoading: false,
          user,
          userProfile,
          userType: signupData.userType || 'customer',
          error: null,
        };
        
        await this.saveAuthState(authState);
        return authState;
      } else {
        throw new Error(response.message || 'Signup failed');
      }
    } catch (error) {
      const errorState: AuthState = {
        isAuthenticated: false,
        isLoading: false,
        user: null,
        userProfile: null,
        userType: null,
        error: error instanceof Error ? error.message : 'Signup failed',
      };
      
      await this.saveAuthState(errorState);
      throw error;
    }
  }

  async logout(apiService?: any): Promise<void> {
    try {
      // Call logout API if available
      if (apiService) {
        await apiService.logout();
      }
    } catch (error) {
      console.error('Error calling logout API:', error);
    } finally {
      // Clear local auth state regardless of API call result
      await this.clearAuthState();
      await this.removeToken();
    }
  }

  async refreshToken(apiService: any): Promise<AuthState> {
    try {
      const response = await apiService.refreshToken();
      
      if (response.success && response.data) {
        const { user, token, userProfile } = response.data;
        
        // Save new token
        await this.setToken(token);
        
        // Update auth state
        const authState: AuthState = {
          isAuthenticated: true,
          isLoading: false,
          user,
          userProfile,
          userType: user.role || 'customer',
          error: null,
        };
        
        await this.saveAuthState(authState);
        return authState;
      } else {
        throw new Error('Token refresh failed');
      }
    } catch (error) {
      // Clear auth state on refresh failure
      await this.clearAuthState();
      await this.removeToken();
      
      const errorState: AuthState = {
        isAuthenticated: false,
        isLoading: false,
        user: null,
        userProfile: null,
        userType: null,
        error: 'Session expired',
      };
      
      throw error;
    }
  }

  async updateProfile(profileData: Partial<UserProfile>, apiService: any): Promise<UserProfile> {
    try {
      const response = await apiService.updateUserProfile(profileData);
      
      if (response.success && response.data) {
        const updatedProfile = response.data;
        
        // Update local auth state
        const currentState = await this.getAuthState();
        await this.saveAuthState({
          ...currentState,
          userProfile: updatedProfile,
        });
        
        return updatedProfile;
      } else {
        throw new Error(response.message || 'Profile update failed');
      }
    } catch (error) {
      console.error('Error updating profile:', error);
      throw error;
    }
  }

  // Utility methods
  async isAuthenticated(): Promise<boolean> {
    const authState = await this.getAuthState();
    const token = await this.getToken();
    return authState.isAuthenticated && !!token;
  }

  async getCurrentUser(): Promise<User | null> {
    const authState = await this.getAuthState();
    return authState.user;
  }

  async getUserProfile(): Promise<UserProfile | null> {
    const authState = await this.getAuthState();
    return authState.userProfile;
  }

  async getUserType(): Promise<string | null> {
    const authState = await this.getAuthState();
    return authState.userType;
  }

  // Platform-specific methods
  async initializeAuth(apiService: any): Promise<AuthState> {
    try {
      const token = await this.getToken();
      
      if (token) {
        // Try to refresh token to validate it
        return await this.refreshToken(apiService);
      } else {
        // No token, return unauthenticated state
        return {
          isAuthenticated: false,
          isLoading: false,
          user: null,
          userProfile: null,
          userType: null,
          error: null,
        };
      }
    } catch (error) {
      console.error('Error initializing auth:', error);
      return {
        isAuthenticated: false,
        isLoading: false,
        user: null,
        userProfile: null,
        userType: null,
        error: null,
      };
    }
  }
}

// Create singleton instances
export const webAuthService = new AuthService('web');
export const mobileAuthService = new AuthService('mobile');

export default AuthService;
