import React, { useState, useEffect } from 'react';
import { View, Text, TouchableOpacity, Alert } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '../ui/Card';
import { Button } from '../ui/Button';
import { Badge } from '../ui/Badge';
import { useTheme } from '../../providers/ThemeProvider';
import { AvailabilityService } from '../../services/availabilityService';

interface AvailabilityCheckerProps {
  entityId: string;
  entityType: 'VENDOR' | 'VENUE';
  entityName: string;
  selectedDate: string;
  selectedTime: string;
  duration?: string;
  onAvailabilityChange: (isAvailable: boolean, conflicts?: any[]) => void;
}

interface ConflictInfo {
  bookingId: string;
  customerName: string;
  eventDate: string;
  eventTime: string;
  status: string;
  duration?: string;
}

export function AvailabilityChecker({
  entityId,
  entityType,
  entityName,
  selectedDate,
  selectedTime,
  duration,
  onAvailabilityChange
}: AvailabilityCheckerProps) {
  const { theme } = useTheme();
  const [checking, setChecking] = useState(false);
  const [isAvailable, setIsAvailable] = useState<boolean | null>(null);
  const [conflicts, setConflicts] = useState<ConflictInfo[]>([]);
  const [suggestedDates, setSuggestedDates] = useState<string[]>([]);
  const [message, setMessage] = useState('');

  useEffect(() => {
    if (selectedDate && selectedTime) {
      checkAvailability();
    }
  }, [selectedDate, selectedTime, entityId]);

  const checkAvailability = async () => {
    if (!selectedDate || !selectedTime) return;

    setChecking(true);
    setIsAvailable(null);
    setConflicts([]);
    setSuggestedDates([]);
    setMessage('');

    try {
      // Use the real availability service
      const result = await AvailabilityService.checkAvailability({
        entityId,
        entityType,
        eventDate: selectedDate,
        eventTime: selectedTime,
        duration
      });

      setIsAvailable(result.isAvailable);
      setConflicts(result.conflictingBookings);
      setMessage(result.message);

      if (result.suggestedDates) {
        setSuggestedDates(result.suggestedDates);
      }

      onAvailabilityChange(result.isAvailable, result.conflictingBookings);

    } catch (error: any) {
      console.error('Availability check error:', error);
      setIsAvailable(false);
      setMessage('Unable to check availability. Please try again.');
      Alert.alert('Error', 'Failed to check availability. Please try again.');
    } finally {
      setChecking(false);
    }
  };

  const generateSuggestedDates = (originalDate: string): string[] => {
    const date = new Date(originalDate);
    const suggestions: string[] = [];
    
    // Generate 3 alternative dates
    for (let i = 1; i <= 3; i++) {
      const newDate = new Date(date);
      newDate.setDate(date.getDate() + i);
      suggestions.push(newDate.toISOString().split('T')[0]);
    }
    
    return suggestions;
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-IN', {
      weekday: 'long',
      day: 'numeric',
      month: 'long',
      year: 'numeric'
    });
  };

  const formatTime = (timeString: string) => {
    const [hours, minutes] = timeString.split(':');
    const hour = parseInt(hours);
    const ampm = hour >= 12 ? 'PM' : 'AM';
    const displayHour = hour % 12 || 12;
    return `${displayHour}:${minutes} ${ampm}`;
  };

  const getAvailabilityIcon = () => {
    if (checking) return 'time';
    if (isAvailable === null) return 'help-circle';
    return isAvailable ? 'checkmark-circle' : 'close-circle';
  };

  const getAvailabilityColor = () => {
    if (checking) return theme.colors.textSecondary;
    if (isAvailable === null) return theme.colors.textSecondary;
    return isAvailable ? '#10B981' : '#EF4444';
  };

  const handleSuggestedDateSelect = (date: string) => {
    Alert.alert(
      'Select Alternative Date',
      `Would you like to check availability for ${formatDate(date)}?`,
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Check', 
          onPress: () => {
            // This would typically update the parent component's selected date
            // For now, we'll just show a message
            Alert.alert('Info', 'This would update the selected date in the booking form');
          }
        }
      ]
    );
  };

  return (
    <Card>
      <CardHeader>
        <View style={{ flexDirection: 'row', alignItems: 'center', gap: 8 }}>
          <Ionicons 
            name={getAvailabilityIcon() as any} 
            size={20} 
            color={getAvailabilityColor()} 
          />
          <CardTitle>Availability Check</CardTitle>
        </View>
      </CardHeader>

      <CardContent>
        <View style={{ gap: 16 }}>
          {/* Selected Date & Time Display */}
          <View style={{
            backgroundColor: theme.colors.muted,
            padding: 12,
            borderRadius: 8,
          }}>
            <View style={{ flexDirection: 'row', alignItems: 'center', gap: 8, marginBottom: 4 }}>
              <Ionicons name="calendar" size={16} color={theme.colors.textSecondary} />
              <Text style={{
                fontSize: 14,
                fontWeight: '600',
                color: theme.colors.text,
              }}>
                {formatDate(selectedDate)}
              </Text>
            </View>
            <View style={{ flexDirection: 'row', alignItems: 'center', gap: 8 }}>
              <Ionicons name="time" size={16} color={theme.colors.textSecondary} />
              <Text style={{
                fontSize: 14,
                color: theme.colors.textSecondary,
              }}>
                {formatTime(selectedTime)}
                {duration && ` (${duration})`}
              </Text>
            </View>
          </View>

          {/* Status Message */}
          {message && (
            <View style={{
              backgroundColor: isAvailable ? '#F0FDF4' : '#FEF2F2',
              borderColor: isAvailable ? '#BBF7D0' : '#FECACA',
              borderWidth: 1,
              borderRadius: 8,
              padding: 12,
              flexDirection: 'row',
              alignItems: 'center',
              gap: 8,
            }}>
              <Ionicons 
                name={isAvailable ? "checkmark-circle" : "alert-circle"} 
                size={16} 
                color={isAvailable ? '#059669' : '#DC2626'} 
              />
              <Text style={{
                fontSize: 14,
                color: isAvailable ? '#059669' : '#DC2626',
                flex: 1,
              }}>
                {message}
              </Text>
            </View>
          )}

          {/* Conflicts Display */}
          {conflicts.length > 0 && (
            <View>
              <Text style={{
                fontSize: 16,
                fontWeight: '600',
                color: theme.colors.text,
                marginBottom: 8,
              }}>
                Existing Bookings
              </Text>
              {conflicts.map((conflict, index) => (
                <View
                  key={index}
                  style={{
                    backgroundColor: '#FEF2F2',
                    borderColor: '#FECACA',
                    borderWidth: 1,
                    borderRadius: 8,
                    padding: 12,
                    marginBottom: 8,
                  }}
                >
                  <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center' }}>
                    <Text style={{
                      fontSize: 14,
                      fontWeight: '600',
                      color: '#DC2626',
                    }}>
                      {conflict.customerName}
                    </Text>
                    <Badge variant="default" style={{ backgroundColor: '#DC2626' }}>
                      {conflict.status}
                    </Badge>
                  </View>
                  <Text style={{
                    fontSize: 12,
                    color: '#7F1D1D',
                    marginTop: 4,
                  }}>
                    {formatTime(conflict.eventTime)}
                    {conflict.duration && ` • ${conflict.duration}`}
                  </Text>
                </View>
              ))}
            </View>
          )}

          {/* Suggested Dates */}
          {suggestedDates.length > 0 && (
            <View>
              <Text style={{
                fontSize: 16,
                fontWeight: '600',
                color: theme.colors.text,
                marginBottom: 8,
              }}>
                Alternative Dates
              </Text>
              <View style={{ gap: 8 }}>
                {suggestedDates.map((date, index) => (
                  <TouchableOpacity
                    key={index}
                    onPress={() => handleSuggestedDateSelect(date)}
                    style={{
                      flexDirection: 'row',
                      alignItems: 'center',
                      justifyContent: 'space-between',
                      padding: 12,
                      backgroundColor: theme.colors.muted,
                      borderRadius: 8,
                      borderWidth: 1,
                      borderColor: theme.colors.border,
                    }}
                  >
                    <View style={{ flexDirection: 'row', alignItems: 'center', gap: 8 }}>
                      <Ionicons name="calendar" size={16} color={theme.colors.primary} />
                      <Text style={{
                        fontSize: 14,
                        color: theme.colors.text,
                      }}>
                        {formatDate(date)}
                      </Text>
                    </View>
                    <Ionicons name="chevron-forward" size={16} color={theme.colors.textSecondary} />
                  </TouchableOpacity>
                ))}
              </View>
            </View>
          )}

          {/* Refresh Button */}
          <Button
            title={checking ? "Checking..." : "Check Again"}
            onPress={checkAvailability}
            disabled={checking}
            loading={checking}
            variant="outline"
            icon="refresh"
            fullWidth
          />

          {/* Info */}
          <View style={{
            backgroundColor: '#F0F9FF',
            borderColor: '#BAE6FD',
            borderWidth: 1,
            borderRadius: 8,
            padding: 12,
            flexDirection: 'row',
            alignItems: 'flex-start',
            gap: 8,
          }}>
            <Ionicons name="information-circle" size={16} color="#0284C7" />
            <Text style={{
              fontSize: 12,
              color: '#0C4A6E',
              flex: 1,
            }}>
              Availability is checked in real-time. We recommend booking as soon as possible to secure your preferred date and time.
            </Text>
          </View>
        </View>
      </CardContent>
    </Card>
  );
}
