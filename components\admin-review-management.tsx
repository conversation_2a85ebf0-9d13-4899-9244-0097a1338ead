"use client"

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { 
  Star, 
  Check, 
  X, 
  Clock, 
  Trash2, 
  RefreshCw, 
  Filter,
  Eye,
  Edit,
  AlertTriangle,
  CheckCircle,
  XCircle
} from "lucide-react"
// import { EntityReviewService } from '@/src/services/entityReviewService' // TODO: Create this services
import { useAuth } from '@/contexts/AuthContext'
import { showToast, toastMessages } from '@/lib/toast'

interface AdminReview {
  id: string
  userId: string
  name: string
  email: string
  location: string
  weddingDate: string
  entityType: string
  entityId: string
  rating: number
  serviceRating?: number
  valueRating?: number
  communicationRating?: number
  professionalismRating?: number
  title: string
  review: string
  wouldRecommend: boolean
  status: string
  verified: boolean
  helpfulCount: number
  createdAt: string
  updatedAt: string
}

export function AdminReviewManagement() {
  const { user, isAuthenticated } = useAuth()
  const [reviews, setReviews] = useState<AdminReview[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedReviews, setSelectedReviews] = useState<string[]>([])
  const [statusFilter, setStatusFilter] = useState<string>('ALL')
  const [entityTypeFilter, setEntityTypeFilter] = useState<string>('ALL')
  const [updating, setUpdating] = useState<string[]>([])
  const [stats, setStats] = useState({
    total: 0,
    pending: 0,
    approved: 0,
    rejected: 0
  })

  useEffect(() => {
    if (isAuthenticated && user) {
      loadReviews()
    }
  }, [isAuthenticated, user, statusFilter, entityTypeFilter])

  const loadReviews = async () => {
    try {
      setLoading(true)
      const filterOptions = {
        limit: 100,
        statusFilter: statusFilter === 'ALL' ? null : statusFilter
      }
      
      const result = await EntityReviewService.getAllReviewsForAdmin(filterOptions)
      
      if (result.success) {
        let filteredReviews = result.data
        
        // Apply entity type filter
        if (entityTypeFilter !== 'ALL') {
          filteredReviews = filteredReviews.filter(review => review.entityType === entityTypeFilter)
        }
        
        setReviews(filteredReviews)
        calculateStats(filteredReviews)
      } else {
        console.error('Error loading reviews:', result.error)
      }
    } catch (error) {
      console.error('Error loading reviews:', error)
    } finally {
      setLoading(false)
    }
  }

  const calculateStats = (reviewsData: AdminReview[]) => {
    const total = reviewsData.length
    const pending = reviewsData.filter(r => r.status === 'PENDING').length
    const approved = reviewsData.filter(r => r.status === 'APPROVED').length
    const rejected = reviewsData.filter(r => r.status === 'REJECTED').length
    
    setStats({ total, pending, approved, rejected })
  }

  const handleStatusUpdate = async (reviewId: string, newStatus: string) => {
    try {
      setUpdating(prev => [...prev, reviewId])
      
      const result = await EntityReviewService.updateReviewStatus(reviewId, newStatus, user)
      
      if (result.success) {
        // Update local state
        setReviews(prev => prev.map(review => 
          review.id === reviewId 
            ? { ...review, status: newStatus, updatedAt: new Date().toISOString() }
            : review
        ))
        
        // Recalculate stats
        const updatedReviews = reviews.map(review => 
          review.id === reviewId ? { ...review, status: newStatus } : review
        )
        calculateStats(updatedReviews)
        
        showToast.success(`Review status updated to ${newStatus}`)
      } else {
        showToast.error(`Error: ${result.error}`)
      }
    } catch (error) {
      console.error('Error updating status:', error)
      showToast.error('Failed to update review status')
    } finally {
      setUpdating(prev => prev.filter(id => id !== reviewId))
    }
  }

  const handleBulkStatusUpdate = async (newStatus: string) => {
    if (selectedReviews.length === 0) {
      showToast.warning('Please select reviews to update')
      return
    }

    if (!confirm(`Update ${selectedReviews.length} reviews to ${newStatus}?`)) {
      return
    }

    try {
      setUpdating(prev => [...prev, ...selectedReviews])
      
      const updates = selectedReviews.map(reviewId => ({
        reviewId,
        newStatus
      }))
      
      const result = await EntityReviewService.bulkUpdateReviewStatus(updates, user)
      
      if (result.success) {
        // Update local state
        setReviews(prev => prev.map(review => 
          selectedReviews.includes(review.id)
            ? { ...review, status: newStatus, updatedAt: new Date().toISOString() }
            : review
        ))
        
        setSelectedReviews([])
        loadReviews() // Reload to get fresh stats
        showToast.success(result.message)
      } else {
        showToast.error(`Error: ${result.error}`)
      }
    } catch (error) {
      console.error('Error in bulk update:', error)
      showToast.error('Failed to bulk update reviews')
    } finally {
      setUpdating([])
    }
  }

  const handleDeleteReview = async (reviewId: string) => {
    if (!confirm('Are you sure you want to delete this review? This action cannot be undone.')) {
      return
    }

    try {
      setUpdating(prev => [...prev, reviewId])
      
      const result = await EntityReviewService.deleteReview(reviewId, user)
      
      if (result.success) {
        setReviews(prev => prev.filter(review => review.id !== reviewId))
        loadReviews() // Reload to get fresh stats
        showToast.success('Review deleted successfully')
      } else {
        showToast.error(`Error: ${result.error}`)
      }
    } catch (error) {
      console.error('Error deleting review:', error)
      showToast.error('Failed to delete review')
    } finally {
      setUpdating(prev => prev.filter(id => id !== reviewId))
    }
  }

  const toggleReviewSelection = (reviewId: string) => {
    setSelectedReviews(prev => 
      prev.includes(reviewId)
        ? prev.filter(id => id !== reviewId)
        : [...prev, reviewId]
    )
  }

  const selectAllReviews = () => {
    if (selectedReviews.length === reviews.length) {
      setSelectedReviews([])
    } else {
      setSelectedReviews(reviews.map(r => r.id))
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'APPROVED':
        return <CheckCircle className="h-4 w-4 text-green-600" />
      case 'REJECTED':
        return <XCircle className="h-4 w-4 text-red-600" />
      case 'PENDING':
        return <Clock className="h-4 w-4 text-yellow-600" />
      default:
        return <AlertTriangle className="h-4 w-4 text-gray-600" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'APPROVED':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'REJECTED':
        return 'bg-red-100 text-red-800 border-red-200'
      case 'PENDING':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const renderStars = (rating: number) => {
    return (
      <div className="flex items-center">
        {[...Array(5)].map((_, i) => (
          <Star
            key={i}
            className={`h-4 w-4 ${
              i < Math.floor(rating) ? "fill-yellow-400 text-yellow-400" : "text-gray-300"
            }`}
          />
        ))}
        <span className="ml-1 text-sm text-gray-600">{rating}/5</span>
      </div>
    )
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  if (!isAuthenticated) {
    return (
      <Card>
        <CardContent className="p-6 text-center">
          <AlertTriangle className="h-12 w-12 text-yellow-500 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 mb-2">Admin Access Required</h3>
          <p className="text-gray-600">Please log in with admin credentials to manage reviews.</p>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      {/* Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-blue-600">{stats.total}</div>
            <p className="text-sm text-gray-600">Total Reviews</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-yellow-600">{stats.pending}</div>
            <p className="text-sm text-gray-600">Pending</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-green-600">{stats.approved}</div>
            <p className="text-sm text-gray-600">Approved</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-red-600">{stats.rejected}</div>
            <p className="text-sm text-gray-600">Rejected</p>
          </CardContent>
        </Card>
      </div>

      {/* Filters and Actions */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>Review Management</span>
            <Button onClick={loadReviews} disabled={loading} size="sm">
              <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap items-center gap-4 mb-6">
            {/* Status Filter */}
            <div className="flex items-center space-x-2">
              <Filter className="h-4 w-4 text-gray-500" />
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="ALL">All Status</SelectItem>
                  <SelectItem value="PENDING">Pending</SelectItem>
                  <SelectItem value="APPROVED">Approved</SelectItem>
                  <SelectItem value="REJECTED">Rejected</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Entity Type Filter */}
            <Select value={entityTypeFilter} onValueChange={setEntityTypeFilter}>
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="ALL">All Types</SelectItem>
                <SelectItem value="SHOP">Shop</SelectItem>
                <SelectItem value="VENUE">Venue</SelectItem>
                <SelectItem value="VENDOR">Vendor</SelectItem>
              </SelectContent>
            </Select>

            {/* Bulk Actions */}
            {selectedReviews.length > 0 && (
              <div className="flex items-center space-x-2 ml-auto">
                <span className="text-sm text-gray-600">
                  {selectedReviews.length} selected
                </span>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => handleBulkStatusUpdate('APPROVED')}
                  className="text-green-600 border-green-600 hover:bg-green-50"
                >
                  <Check className="h-4 w-4 mr-1" />
                  Approve
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => handleBulkStatusUpdate('REJECTED')}
                  className="text-red-600 border-red-600 hover:bg-red-50"
                >
                  <X className="h-4 w-4 mr-1" />
                  Reject
                </Button>
              </div>
            )}
          </div>

          {/* Select All */}
          <div className="flex items-center space-x-2 mb-4">
            <Checkbox
              checked={selectedReviews.length === reviews.length && reviews.length > 0}
              onCheckedChange={selectAllReviews}
            />
            <label className="text-sm text-gray-600">
              Select All ({reviews.length} reviews)
            </label>
          </div>
        </CardContent>
      </Card>

      {/* Reviews List */}
      <Card>
        <CardHeader>
          <CardTitle>Reviews ({reviews.length})</CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="text-center py-8">
              <RefreshCw className="h-8 w-8 animate-spin text-gray-400 mx-auto mb-4" />
              <p className="text-gray-600">Loading reviews...</p>
            </div>
          ) : reviews.length === 0 ? (
            <div className="text-center py-8">
              <Eye className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-600 mb-2">No reviews found</h3>
              <p className="text-gray-500">No reviews match the current filters.</p>
            </div>
          ) : (
            <div className="space-y-4">
              {reviews.map((review) => (
                <div key={review.id} className="border rounded-lg p-6 hover:bg-gray-50">
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex items-start space-x-3">
                      <Checkbox
                        checked={selectedReviews.includes(review.id)}
                        onCheckedChange={() => toggleReviewSelection(review.id)}
                      />
                      <div className="flex-1">
                        <div className="flex items-center gap-3 mb-2">
                          <h3 className="font-semibold text-lg text-gray-900">{review.name}</h3>
                          <Badge className={getStatusColor(review.status)}>
                            {getStatusIcon(review.status)}
                            <span className="ml-1">{review.status}</span>
                          </Badge>
                          {review.entityType && (
                            <Badge variant="outline">
                              {review.entityType}
                            </Badge>
                          )}
                          {review.verified && (
                            <Badge className="bg-blue-100 text-blue-800">
                              Verified
                            </Badge>
                          )}
                        </div>
                        <p className="text-gray-600 text-sm mb-2">
                          {review.email} • {review.location} • Wedding: {formatDate(review.weddingDate)}
                        </p>
                        <div className="flex items-center gap-4 mb-2">
                          {renderStars(review.rating)}
                          {review.wouldRecommend && (
                            <Badge variant="outline" className="text-green-600 border-green-600 text-xs">
                              Recommends
                            </Badge>
                          )}
                        </div>
                      </div>
                    </div>
                    <div className="text-right text-sm text-gray-500">
                      <p>Created: {formatDate(review.createdAt)}</p>
                      <p>Updated: {formatDate(review.updatedAt)}</p>
                      <p>ID: {review.id.slice(-8)}</p>
                    </div>
                  </div>

                  <div className="mb-4">
                    <h4 className="font-semibold text-gray-900 mb-2">{review.title}</h4>
                    <p className="text-gray-700 mb-3">{review.review}</p>
                  </div>

                  {/* Detailed Ratings */}
                  {(review.serviceRating || review.valueRating || review.communicationRating || review.professionalismRating) && (
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-2 mb-4 p-3 bg-gray-50 rounded">
                      {review.serviceRating && (
                        <div className="text-center">
                          <div className="text-sm font-medium">{review.serviceRating}/5</div>
                          <p className="text-xs text-gray-600">Service</p>
                        </div>
                      )}
                      {review.valueRating && (
                        <div className="text-center">
                          <div className="text-sm font-medium">{review.valueRating}/5</div>
                          <p className="text-xs text-gray-600">Value</p>
                        </div>
                      )}
                      {review.communicationRating && (
                        <div className="text-center">
                          <div className="text-sm font-medium">{review.communicationRating}/5</div>
                          <p className="text-xs text-gray-600">Communication</p>
                        </div>
                      )}
                      {review.professionalismRating && (
                        <div className="text-center">
                          <div className="text-sm font-medium">{review.professionalismRating}/5</div>
                          <p className="text-xs text-gray-600">Professional</p>
                        </div>
                      )}
                    </div>
                  )}

                  {/* Entity Information */}
                  <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
                    <div className="flex items-center space-x-4">
                      <span>Entity: {review.entityType} #{review.entityId}</span>
                      <span>User: {review.userId}</span>
                      <span>{review.helpfulCount || 0} helpful votes</span>
                    </div>
                  </div>

                  {/* Action Buttons */}
                  <div className="flex items-center justify-between pt-4 border-t">
                    <div className="flex items-center space-x-2">
                      {review.status !== 'APPROVED' && (
                        <Button
                          size="sm"
                          onClick={() => handleStatusUpdate(review.id, 'APPROVED')}
                          disabled={updating.includes(review.id)}
                          className="bg-green-600 hover:bg-green-700 text-white"
                        >
                          {updating.includes(review.id) ? (
                            <RefreshCw className="h-4 w-4 animate-spin" />
                          ) : (
                            <Check className="h-4 w-4" />
                          )}
                          <span className="ml-1">Approve</span>
                        </Button>
                      )}
                      {review.status !== 'REJECTED' && (
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleStatusUpdate(review.id, 'REJECTED')}
                          disabled={updating.includes(review.id)}
                          className="text-red-600 border-red-600 hover:bg-red-50"
                        >
                          {updating.includes(review.id) ? (
                            <RefreshCw className="h-4 w-4 animate-spin" />
                          ) : (
                            <X className="h-4 w-4" />
                          )}
                          <span className="ml-1">Reject</span>
                        </Button>
                      )}
                      {review.status !== 'PENDING' && (
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleStatusUpdate(review.id, 'PENDING')}
                          disabled={updating.includes(review.id)}
                          className="text-yellow-600 border-yellow-600 hover:bg-yellow-50"
                        >
                          {updating.includes(review.id) ? (
                            <RefreshCw className="h-4 w-4 animate-spin" />
                          ) : (
                            <Clock className="h-4 w-4" />
                          )}
                          <span className="ml-1">Pending</span>
                        </Button>
                      )}
                    </div>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleDeleteReview(review.id)}
                      disabled={updating.includes(review.id)}
                      className="text-red-600 border-red-600 hover:bg-red-50"
                    >
                      {updating.includes(review.id) ? (
                        <RefreshCw className="h-4 w-4 animate-spin" />
                      ) : (
                        <Trash2 className="h-4 w-4" />
                      )}
                      <span className="ml-1">Delete</span>
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
