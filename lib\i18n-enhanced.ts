"use client"

import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import LanguageDetector from 'i18next-browser-languagedetector';

// Import all language resources
import enCommon from '../public/locales/en/common.json';
import enCart from '../public/locales/en/cart.json';
import enHelp from '../public/locales/en/help.json';
import taCommon from '../public/locales/ta/common.json';
import taCart from '../public/locales/ta/cart.json';
import taHelp from '../public/locales/ta/help.json';
import hiCommon from '../public/locales/hi/common.json';
import hiCart from '../public/locales/hi/cart.json';
import hiHelp from '../public/locales/hi/help.json';
import teCommon from '../public/locales/te/common.json';
import teCart from '../public/locales/te/cart.json';
import teHelp from '../public/locales/te/help.json';
import knCommon from '../public/locales/kn/common.json';
import knCart from '../public/locales/kn/cart.json';
import knHelp from '../public/locales/kn/help.json';
import mlCommon from '../public/locales/ml/common.json';
import mlCart from '../public/locales/ml/cart.json';
import mlHelp from '../public/locales/ml/help.json';
import guCommon from '../public/locales/gu/common.json';
import guCart from '../public/locales/gu/cart.json';
import guHelp from '../public/locales/gu/help.json';
import mrCommon from '../public/locales/mr/common.json';
import mrCart from '../public/locales/mr/cart.json';
import mrHelp from '../public/locales/mr/help.json';
import bnCommon from '../public/locales/bn/common.json';
import bnCart from '../public/locales/bn/cart.json';
import bnHelp from '../public/locales/bn/help.json';

// Language configuration
export const SUPPORTED_LANGUAGES = [
  { code: 'en', name: 'English', nativeName: 'English', flag: '🇺🇸' },
  { code: 'hi', name: 'Hindi', nativeName: 'हिन्दी', flag: '🇮🇳' },
  { code: 'ta', name: 'Tamil', nativeName: 'தமிழ்', flag: '🇮🇳' },
  { code: 'te', name: 'Telugu', nativeName: 'తెలుగు', flag: '🇮🇳' },
  { code: 'kn', name: 'Kannada', nativeName: 'ಕನ್ನಡ', flag: '🇮🇳' },
  { code: 'ml', name: 'Malayalam', nativeName: 'മലയാളം', flag: '🇮🇳' },
  { code: 'gu', name: 'Gujarati', nativeName: 'ગુજરાતી', flag: '🇮🇳' },
  { code: 'mr', name: 'Marathi', nativeName: 'मराठी', flag: '🇮🇳' },
  { code: 'bn', name: 'Bengali', nativeName: 'বাংলা', flag: '🇮🇳' },
];

// Resources configuration
const resources = {
  en: { common: enCommon, cart: enCart, help: enHelp },
  ta: { common: taCommon, cart: taCart, help: taHelp },
  hi: { common: hiCommon, cart: hiCart, help: hiHelp },
  te: { common: teCommon, cart: teCart, help: teHelp },
  kn: { common: knCommon, cart: knCart, help: knHelp },
  ml: { common: mlCommon, cart: mlCart, help: mlHelp },
  gu: { common: guCommon, cart: guCart, help: guHelp },
  mr: { common: mrCommon, cart: mrCart, help: mrHelp },
  bn: { common: bnCommon, cart: bnCart, help: bnHelp },
};

// Initialize i18n
if (!i18n.isInitialized) {
  i18n
    .use(LanguageDetector)
    .use(initReactI18next)
    .init({
    resources,
    fallbackLng: 'en',
    debug: process.env.NODE_ENV === 'development',
    
    // Namespace configuration
    ns: ['common', 'cart', 'help'],
    defaultNS: 'common',
    
    // Detection options
    detection: {
      order: ['localStorage', 'navigator', 'htmlTag'],
      lookupLocalStorage: 'selectedLanguage',
      caches: ['localStorage'],
    },
    
    // Interpolation options
    interpolation: {
      escapeValue: false,
      formatSeparator: ',',
      format: (value, format, lng) => {
        if (format === 'currency') {
          return new Intl.NumberFormat(lng === 'en' ? 'en-IN' : lng, {
            style: 'currency',
            currency: 'INR',
          }).format(value);
        }
        if (format === 'number') {
          return new Intl.NumberFormat(lng === 'en' ? 'en-IN' : lng).format(value);
        }
        if (format === 'date') {
          return new Date(value).toLocaleDateString(lng === 'en' ? 'en-IN' : lng);
        }
        if (format === 'datetime') {
          return new Date(value).toLocaleString(lng === 'en' ? 'en-IN' : lng);
        }
        return value;
      },
    },
    
    // React options
    react: {
      useSuspense: false,
    },
  });
}

// Helper functions
export const getCurrentLanguage = () => i18n.language;

export const changeLanguage = async (languageCode: string) => {
  try {
    await i18n.changeLanguage(languageCode);
    if (typeof window !== 'undefined') {
      localStorage.setItem('selectedLanguage', languageCode);
    }
    return true;
  } catch (error) {
    console.error('Error changing language:', error);
    return false;
  }
};

export const isRTL = (languageCode?: string) => {
  const lang = languageCode || i18n.language;
  const rtlLanguages = ['ar', 'ur', 'fa'];
  return rtlLanguages.includes(lang);
};

// Translation helper with type safety
export const t = (key: string, options?: any) => {
  return i18n.t(key, options);
};

// Namespace-specific translation helpers
export const tCommon = (key: string, options?: any) => {
  return i18n.t(`common.${key}`, options);
};

export const tNavigation = (key: string, options?: any) => {
  return i18n.t(`navigation.${key}`, options);
};

export const tHeader = (key: string, options?: any) => {
  return i18n.t(`header.${key}`, options);
};

export const tHero = (key: string, options?: any) => {
  return i18n.t(`hero.${key}`, options);
};

export const tServices = (key: string, options?: any) => {
  return i18n.t(`services.${key}`, options);
};

export const tVendors = (key: string, options?: any) => {
  return i18n.t(`featuredVendors.${key}`, options);
};

export const tVenues = (key: string, options?: any) => {
  return i18n.t(`venues.${key}`, options);
};

export const tShop = (key: string, options?: any) => {
  return i18n.t(`shop.${key}`, options);
};

export const tBudget = (key: string, options?: any) => {
  return i18n.t(`budget.${key}`, options);
};

export const tGuestList = (key: string, options?: any) => {
  return i18n.t(`guestList.${key}`, options);
};

export const tChecklist = (key: string, options?: any) => {
  return i18n.t(`checklist.${key}`, options);
};

export const tReviews = (key: string, options?: any) => {
  return i18n.t(`reviews.${key}`, options);
};

export const tContact = (key: string, options?: any) => {
  return i18n.t(`contact.${key}`, options);
};

export const tFooter = (key: string, options?: any) => {
  return i18n.t(`footer.${key}`, options);
};

// Format helpers
export const formatCurrency = (amount: number, languageCode?: string) => {
  const lang = languageCode || i18n.language;
  return new Intl.NumberFormat(lang === 'en' ? 'en-IN' : lang, {
    style: 'currency',
    currency: 'INR',
  }).format(amount);
};

export const formatNumber = (number: number, languageCode?: string) => {
  const lang = languageCode || i18n.language;
  return new Intl.NumberFormat(lang === 'en' ? 'en-IN' : lang).format(number);
};

export const formatDate = (date: Date | string, languageCode?: string) => {
  const lang = languageCode || i18n.language;
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  return dateObj.toLocaleDateString(lang === 'en' ? 'en-IN' : lang);
};

export const formatDateTime = (date: Date | string, languageCode?: string) => {
  const lang = languageCode || i18n.language;
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  return dateObj.toLocaleString(lang === 'en' ? 'en-IN' : lang);
};

export default i18n;