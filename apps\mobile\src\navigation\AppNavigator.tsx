import React from 'react';
import { createStackNavigator, TransitionPresets } from '@react-navigation/stack';
import { Easing } from 'react-native';
import MainNavigator from './MainNavigator';

// Import detail screens
import VendorDetailScreen from '../screens/details/VendorDetailScreen';
import VenueDetailScreen from '../screens/details/VenueDetailScreen';
import ProductDetailScreen from '../screens/details/ProductDetailScreen';

// Import core screens
import CartScreen from '../screens/CartScreen';
import VendorsScreen from '../screens/VendorsScreen';
import VenuesScreen from '../screens/VenuesScreen';
import SearchScreen from '../screens/SearchScreen';

// Import authentication screens
import LoginScreen from '../screens/auth/LoginScreen';
import RegisterScreen from '../screens/auth/SignupScreen'; // Note: SignupScreen not RegisterScreen
import VendorSignupScreen from '../screens/auth/VendorSignupScreen'; // TODO: Create this screen
import ForgotPasswordScreen from '../screens/auth/ForgotPasswordScreen';

// Import dashboard screens
import CustomerDashboardScreen from '../screens/dashboard/CustomerDashboardScreen';
import VendorDashboardScreen from '../screens/VendorDashboardScreen';
import VendorProductsScreen from '../screens/VendorProductsScreen';
import VendorOrdersScreen from '../screens/VendorOrdersScreen';
import AdminDashboardScreen from '../screens/AdminDashboardScreen';
import AdminAnalyticsScreen from '../screens/AdminAnalyticsScreen';
import VendorAnalyticsScreen from '../screens/VendorAnalyticsScreen';

// Import new dashboard screens
import VendorBusinessProfileScreen from '../screens/dashboard/vendor/VendorBusinessProfileScreen';
import VendorShopManagementScreen from '../screens/dashboard/vendor/VendorShopManagementScreen';
import VendorInquiryManagementScreen from '../screens/dashboard/vendor/VendorInquiryManagementScreen';
import AdminUserManagementScreen from '../screens/dashboard/admin/AdminUserManagementScreen';
import AdminOrderManagementScreen from '../screens/dashboard/admin/AdminOrderManagementScreen';
import AdminNewsletterManagementScreen from '../screens/dashboard/admin/AdminNewsletterManagementScreen';

// Import e-commerce screens
import CheckoutScreen from '../screens/CheckoutScreen';
import OrdersScreen from '../screens/OrdersScreen';
import OrderDetailScreen from '../screens/OrderDetailScreen';
import PaymentScreen from '../screens/ecommerce/PaymentScreen';
import OrderConfirmationScreen from '../screens/ecommerce/OrderConfirmationScreen';

// Import booking screens
import BookingScreen from '../screens/BookingScreen';
import BookingHistoryScreen from '../screens/booking/BookingHistoryScreen';
import BookingDetailScreen from '../screens/booking/BookingDetailScreen';

// Import content screens
import BlogScreen from '../screens/content/BlogScreen';
import BlogDetailScreen from '../screens/content/BlogDetailScreen';
import PhotosScreen from '../screens/content/PhotosScreen';
import RealWeddingsScreen from '../screens/content/RealWeddingsScreen';
import CommunityScreen from '../screens/content/CommunityScreen';

// Import planning screens
import WeddingPlanningScreen from '../screens/WeddingPlanningScreen';
import BudgetTrackerScreen from '../screens/BudgetTrackerScreen';
import GuestListManagerScreen from '../screens/GuestListManagerScreen';
import TimelineScreen from '../screens/planning/TimelineScreen';

// Import support screens
import HelpScreen from '../screens/support/HelpScreen';
import ContactScreen from '../screens/support/ContactScreen';
import PrivacyScreen from '../screens/support/PrivacyScreen';
import TermsScreen from '../screens/support/TermsScreen';
import NewsletterScreen from '../screens/support/NewsletterScreen';
import OffersScreen from '../screens/support/OffersScreen';

// Import review screens
import ReviewScreen from '../screens/ReviewScreen';
import ReviewsScreen from '../screens/reviews/ReviewsScreen';
import WriteReviewScreen from '../screens/reviews/WriteReviewScreen';
import ReviewDetailScreen from '../screens/reviews/ReviewDetailScreen';

// Import utility screens
import NotificationScreen from '../screens/NotificationScreen';
import SettingsScreen from '../screens/SettingsScreen';
import LoadingScreen from '../screens/LoadingScreen';
import ErrorScreen from '../screens/ErrorScreen';
import NotFoundScreen from '../screens/NotFoundScreen';

// Import components
import NotificationBell from '../components/NotificationBell';

export type AppStackParamList = {
  // Core navigation
  MainTabs: undefined;
  Loading: undefined;
  Error: { message?: string };
  NotFound: undefined;

  // Authentication
  Login: undefined;
  Register: undefined;
  VendorSignup: undefined;
  ForgotPassword: undefined;

  // Detail screens
  VendorDetail: { vendorId: string };
  VenueDetail: { venueId: string };
  ProductDetail: { productId: string };

  // Main content screens
  Cart: undefined;
  Vendors: { category?: string; city?: string };
  Venues: { city?: string; venueType?: string };
  Search: { query?: string; category?: string };

  // Booking system
  Booking: {
    type: 'vendor' | 'venue';
    id: string;
    name: string;
    vendorId?: string;
    venueId?: string;
    serviceType: string;
  };
  BookingHistory: undefined;
  BookingDetail: { bookingId: string };

  // E-commerce
  Checkout: undefined;
  Orders: undefined;
  OrderDetail: { orderId: string };
  Payment: { orderId: string; amount: number };
  OrderConfirmation: { orderId: string };

  // Dashboard screens
  CustomerDashboard: undefined;
  VendorDashboard: undefined;
  VendorProducts: undefined;
  VendorOrders: undefined;
  AdminDashboard: undefined;
  AdminAnalytics: undefined;
  VendorAnalytics: undefined;

  // Vendor Dashboard Screens
  VendorBusinessProfile: undefined;
  VendorShopManagement: undefined;
  VendorVenueManagement: undefined;
  VendorOrderManagement: undefined;
  VendorBookingManagement: undefined;
  VendorInquiryManagement: undefined;
  VendorReviewManagement: undefined;
  VendorBlogManagement: undefined;

  // Admin Dashboard Screens
  AdminUserManagement: undefined;
  AdminVendorManagement: undefined;
  AdminVenueManagement: undefined;
  AdminShopManagement: undefined;
  AdminOrderManagement: undefined;
  AdminReviewManagement: undefined;
  AdminNewsletterManagement: undefined;

  // Content screens
  Blog: undefined;
  BlogDetail: { blogId: string };
  Photos: undefined;
  RealWeddings: undefined;
  Community: undefined;

  // Planning tools
  WeddingPlanning: undefined;
  BudgetTracker: undefined;
  GuestListManager: undefined;
  Timeline: undefined;

  // Review system
  Review: {
    vendorId?: string;
    venueId?: string;
    shopId?: string;
    serviceName: string;
  };
  Reviews: { entityType: 'vendor' | 'venue' | 'shop'; entityId: string };
  WriteReview: { entityType: 'vendor' | 'venue' | 'shop'; entityId: string };
  ReviewDetail: { reviewId: string };

  // Support screens
  Help: undefined;
  Contact: undefined;
  Privacy: undefined;
  Terms: undefined;
  Newsletter: undefined;
  Offers: undefined;

  // Utility screens
  Notifications: undefined;
  Settings: undefined;
};

const Stack = createStackNavigator<AppStackParamList>();

// Custom transition configurations
const customTransition = {
  gestureEnabled: true,
  gestureDirection: 'horizontal' as const,
  transitionSpec: {
    open: {
      animation: 'timing' as const,
      config: {
        duration: 300,
        easing: Easing.out(Easing.poly(4)),
      },
    },
    close: {
      animation: 'timing' as const,
      config: {
        duration: 250,
        easing: Easing.in(Easing.poly(4)),
      },
    },
  },
  cardStyleInterpolator: ({ current, layouts }: any) => {
    return {
      cardStyle: {
        transform: [
          {
            translateX: current.progress.interpolate({
              inputRange: [0, 1],
              outputRange: [layouts.screen.width, 0],
            }),
          },
        ],
        opacity: current.progress.interpolate({
          inputRange: [0, 0.5, 1],
          outputRange: [0, 0.5, 1],
        }),
      },
    };
  },
};

const modalTransition = {
  gestureEnabled: true,
  gestureDirection: 'vertical' as const,
  transitionSpec: {
    open: {
      animation: 'spring' as const,
      config: {
        stiffness: 1000,
        damping: 500,
        mass: 3,
        overshootClamping: true,
        restDisplacementThreshold: 0.01,
        restSpeedThreshold: 0.01,
      },
    },
    close: {
      animation: 'timing' as const,
      config: {
        duration: 200,
        easing: Easing.in(Easing.ease),
      },
    },
  },
  cardStyleInterpolator: ({ current, layouts }: any) => {
    return {
      cardStyle: {
        transform: [
          {
            translateY: current.progress.interpolate({
              inputRange: [0, 1],
              outputRange: [layouts.screen.height, 0],
            }),
          },
        ],
      },
    };
  },
};

export default function AppNavigator() {
  return (
    <Stack.Navigator
      screenOptions={{
        headerStyle: {
          backgroundColor: '#610f13', // Maroon - matches web app
        },
        headerTintColor: '#fff',
        headerTitleStyle: {
          fontWeight: 'bold',
        },
        headerRight: () => <NotificationBell color="#fff" />,
        ...customTransition,
      }}
    >
      <Stack.Screen
        name="MainTabs"
        component={MainNavigator}
        options={{ headerShown: false }}
      />

      {/* Utility Screens */}
      <Stack.Screen
        name="Loading"
        component={LoadingScreen}
        options={{ headerShown: false }}
      />

      <Stack.Screen
        name="Error"
        component={ErrorScreen}
        options={{ title: 'Error' }}
      />

      <Stack.Screen
        name="NotFound"
        component={NotFoundScreen}
        options={{ title: 'Page Not Found' }}
      />

      {/* Authentication Screens */}
      <Stack.Screen
        name="Login"
        component={LoginScreen}
        options={{
          title: 'Login',
          presentation: 'modal',
          ...modalTransition,
        }}
      />

      <Stack.Screen
        name="Register"
        component={RegisterScreen}
        options={{
          title: 'Register',
          presentation: 'modal',
          ...modalTransition,
        }}
      />

      <Stack.Screen
        name="VendorSignup"
        component={VendorSignupScreen}
        options={{
          title: 'Vendor Signup',
          presentation: 'modal',
          ...modalTransition,
        }}
      />

      <Stack.Screen
        name="ForgotPassword"
        component={ForgotPasswordScreen}
        options={{
          title: 'Reset Password',
          presentation: 'modal',
          ...modalTransition,
        }}
      />

      {/* Detail Screens */}
      <Stack.Screen
        name="VendorDetail"
        component={VendorDetailScreen}
        options={{ title: 'Vendor Details' }}
      />

      <Stack.Screen
        name="VenueDetail"
        component={VenueDetailScreen}
        options={{ title: 'Venue Details' }}
      />

      <Stack.Screen
        name="ProductDetail"
        component={ProductDetailScreen}
        options={{ title: 'Product Details' }}
      />
      
      {/* Full Screen Modals */}
      <Stack.Screen
        name="Cart"
        component={CartScreen}
        options={{
          title: 'Shopping Cart',
          presentation: 'modal',
          ...modalTransition,
        }}
      />
      
      <Stack.Screen 
        name="Vendors" 
        component={VendorsScreen}
        options={{ title: 'Wedding Vendors' }}
      />
      
      <Stack.Screen
        name="Venues"
        component={VenuesScreen}
        options={{ title: 'Wedding Venues' }}
      />

      <Stack.Screen
        name="Search"
        component={SearchScreen}
        options={{ title: 'Search' }}
      />

      <Stack.Screen
        name="Booking"
        component={BookingScreen}
        options={{
          title: 'Book Service',
          presentation: 'modal',
          ...modalTransition,
        }}
      />

      <Stack.Screen
        name="Review"
        component={ReviewScreen}
        options={{
          title: 'Write Review',
          presentation: 'modal',
          ...modalTransition,
        }}
      />

      <Stack.Screen
        name="Checkout"
        component={CheckoutScreen}
        options={{
          title: 'Checkout',
          presentation: 'modal',
          ...modalTransition,
        }}
      />

      <Stack.Screen
        name="Orders"
        component={OrdersScreen}
        options={{
          title: 'My Orders',
        }}
      />

      <Stack.Screen
        name="OrderDetail"
        component={OrderDetailScreen}
        options={{
          title: 'Order Details',
        }}
      />

      <Stack.Screen
        name="VendorDashboard"
        component={VendorDashboardScreen}
        options={{
          title: 'Vendor Dashboard',
        }}
      />

      <Stack.Screen
        name="VendorProducts"
        component={VendorProductsScreen}
        options={{
          title: 'Manage Products',
        }}
      />

      <Stack.Screen
        name="VendorOrders"
        component={VendorOrdersScreen}
        options={{
          title: 'Manage Orders',
        }}
      />

      <Stack.Screen
        name="AdminDashboard"
        component={AdminDashboardScreen}
        options={{
          title: 'Admin Dashboard',
        }}
      />

      <Stack.Screen
        name="Notifications"
        component={NotificationScreen}
        options={{
          title: 'Notifications',
        }}
      />

      <Stack.Screen
        name="WeddingPlanning"
        component={WeddingPlanningScreen}
        options={{
          title: 'Wedding Planning',
        }}
      />

      <Stack.Screen
        name="BudgetTracker"
        component={BudgetTrackerScreen}
        options={{
          title: 'Budget Tracker',
        }}
      />

      <Stack.Screen
        name="GuestListManager"
        component={GuestListManagerScreen}
        options={{
          title: 'Guest List Manager',
        }}
      />

      <Stack.Screen
        name="Settings"
        component={SettingsScreen}
        options={{
          title: 'Settings',
        }}
      />

      <Stack.Screen
        name="AdminAnalytics"
        component={AdminAnalyticsScreen}
        options={{
          headerShown: false,
        }}
      />

      <Stack.Screen
        name="VendorAnalytics"
        component={VendorAnalyticsScreen}
        options={{
          headerShown: false,
        }}
      />

      {/* Dashboard Screens */}
      <Stack.Screen
        name="CustomerDashboard"
        component={CustomerDashboardScreen}
        options={{
          title: 'My Dashboard',
        }}
      />

      {/* Vendor Dashboard Screens */}
      <Stack.Screen
        name="VendorBusinessProfile"
        component={VendorBusinessProfileScreen}
        options={{
          title: 'Business Profile',
        }}
      />

      <Stack.Screen
        name="VendorShopManagement"
        component={VendorShopManagementScreen}
        options={{
          title: 'Shop Management',
        }}
      />

      <Stack.Screen
        name="VendorInquiryManagement"
        component={VendorInquiryManagementScreen}
        options={{
          title: 'Inquiry Management',
        }}
      />

      {/* Admin Dashboard Screens */}
      <Stack.Screen
        name="AdminUserManagement"
        component={AdminUserManagementScreen}
        options={{
          title: 'User Management',
        }}
      />

      <Stack.Screen
        name="AdminOrderManagement"
        component={AdminOrderManagementScreen}
        options={{
          title: 'Order Management',
        }}
      />

      <Stack.Screen
        name="AdminNewsletterManagement"
        component={AdminNewsletterManagementScreen}
        options={{
          title: 'Newsletter Management',
        }}
      />

      {/* E-commerce Screens */}
      <Stack.Screen
        name="Payment"
        component={PaymentScreen}
        options={{
          title: 'Payment',
          presentation: 'modal',
          ...modalTransition,
        }}
      />

      <Stack.Screen
        name="OrderConfirmation"
        component={OrderConfirmationScreen}
        options={{
          title: 'Order Confirmed',
          headerLeft: () => null, // Prevent going back
        }}
      />

      {/* Booking Screens */}
      <Stack.Screen
        name="BookingHistory"
        component={BookingHistoryScreen}
        options={{
          title: 'Booking History',
        }}
      />

      <Stack.Screen
        name="BookingDetail"
        component={BookingDetailScreen}
        options={{
          title: 'Booking Details',
        }}
      />

      {/* Content Screens */}
      <Stack.Screen
        name="Blog"
        component={BlogScreen}
        options={{
          title: 'Wedding Blog',
        }}
      />

      <Stack.Screen
        name="BlogDetail"
        component={BlogDetailScreen}
        options={{
          title: 'Blog Post',
        }}
      />

      <Stack.Screen
        name="Photos"
        component={PhotosScreen}
        options={{
          title: 'Wedding Photos',
        }}
      />

      <Stack.Screen
        name="RealWeddings"
        component={RealWeddingsScreen}
        options={{
          title: 'Real Weddings',
        }}
      />

      <Stack.Screen
        name="Community"
        component={CommunityScreen}
        options={{
          title: 'Wedding Community',
        }}
      />

      {/* Planning Tools */}
      <Stack.Screen
        name="Timeline"
        component={TimelineScreen}
        options={{
          title: 'Wedding Timeline',
        }}
      />

      {/* Review System */}
      <Stack.Screen
        name="Reviews"
        component={ReviewsScreen}
        options={{
          title: 'Reviews',
        }}
      />

      <Stack.Screen
        name="WriteReview"
        component={WriteReviewScreen}
        options={{
          title: 'Write Review',
          presentation: 'modal',
          ...modalTransition,
        }}
      />

      <Stack.Screen
        name="ReviewDetail"
        component={ReviewDetailScreen}
        options={{
          title: 'Review Details',
        }}
      />

      {/* Support Screens */}
      <Stack.Screen
        name="Help"
        component={HelpScreen}
        options={{
          title: 'Help & Support',
        }}
      />

      <Stack.Screen
        name="Contact"
        component={ContactScreen}
        options={{
          title: 'Contact Us',
        }}
      />

      <Stack.Screen
        name="Privacy"
        component={PrivacyScreen}
        options={{
          title: 'Privacy Policy',
        }}
      />

      <Stack.Screen
        name="Terms"
        component={TermsScreen}
        options={{
          title: 'Terms of Service',
        }}
      />

      <Stack.Screen
        name="Newsletter"
        component={NewsletterScreen}
        options={{
          title: 'Newsletter',
        }}
      />

      <Stack.Screen
        name="Offers"
        component={OffersScreen}
        options={{
          title: 'Special Offers',
        }}
      />
    </Stack.Navigator>
  );
}
