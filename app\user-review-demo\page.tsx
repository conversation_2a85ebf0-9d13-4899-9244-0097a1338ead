'use client'

import { LayoutWrapper } from "@/components/layout-wrapper"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { User, Star, ShoppingBag, MapPin, Users, Shield, Database, CheckCircle } from "lucide-react"
import { EntityReviews } from "@/components/entity-reviews"
import { UserReviewsDashboard } from "@/components/user-reviews-dashboard"
import { useAuth } from '@/contexts/AuthContext'

export default function UserReviewDemoPage() {
  const { isAuthenticated, user } = useAuth()

  // Mock entity data for demonstration
  const mockEntities = {
    shop: {
      id: "user-shop-demo-1",
      name: "Premium Bridal Boutique",
      type: "SHOP"
    },
    venue: {
      id: "user-venue-demo-1", 
      name: "Royal Garden Wedding Venue",
      type: "VENUE"
    },
    vendor: {
      id: "user-vendor-demo-1",
      name: "Elite Wedding Photography",
      type: "VENDOR"
    }
  }

  return (
    <LayoutWrapper>
      <div className="min-h-screen bg-gradient-to-br from-background to-muted/20 py-8">
        <div className="container mx-auto px-4">
          <div className="text-center mb-8">
            <h1 className="text-4xl font-bold text-gray-900 mb-4">
              👤 User-Specific Review System Demo
            </h1>
            <p className="text-xl text-gray-600 max-w-4xl mx-auto">
              Experience our enhanced review system with user authentication, duplicate prevention, 
              and personalized review management. Each user can only review each item once.
            </p>
          </div>

          {/* Authentication Status */}
          <Card className="mb-8">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="w-6 h-6" />
                Authentication Status
              </CardTitle>
            </CardHeader>
            <CardContent>
              {isAuthenticated ? (
                <div className="flex items-center justify-between p-4 bg-green-50 border border-green-200 rounded-lg">
                  <div className="flex items-center space-x-3">
                    <CheckCircle className="w-6 h-6 text-green-600" />
                    <div>
                      <p className="font-semibold text-green-900">Authenticated User</p>
                      <p className="text-sm text-green-700">
                        User ID: {user?.sub || user?.id || 'Unknown'}
                      </p>
                      <p className="text-sm text-green-700">
                        Email: {user?.email || 'Not available'}
                      </p>
                    </div>
                  </div>
                  <Badge className="bg-green-600">
                    Logged In
                  </Badge>
                </div>
              ) : (
                <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                  <div className="flex items-center space-x-3">
                    <Shield className="w-6 h-6 text-yellow-600" />
                    <div>
                      <p className="font-semibold text-yellow-900">Not Authenticated</p>
                      <p className="text-sm text-yellow-700">
                        Please log in to submit reviews and see user-specific features
                      </p>
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Enhanced Features */}
          <Card className="mb-8">
            <CardHeader>
              <CardTitle>🚀 Enhanced User-Specific Features</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h4 className="font-semibold text-gray-900 mb-3">🔐 User Authentication:</h4>
                  <ul className="space-y-2 text-sm text-gray-700">
                    <li>• <strong>Required Login:</strong> Users must authenticate to submit reviews</li>
                    <li>• <strong>User ID Tracking:</strong> Each review is linked to a specific user</li>
                    <li>• <strong>Duplicate Prevention:</strong> One review per user per item</li>
                    <li>• <strong>Review Ownership:</strong> Users can only edit their own reviews</li>
                    <li>• <strong>Helpful Voting:</strong> Prevents duplicate helpful votes per user</li>
                    <li>• <strong>User Dashboard:</strong> View and manage all your reviews</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-semibold text-gray-900 mb-3">📊 Enhanced Data Model:</h4>
                  <ul className="space-y-2 text-sm text-gray-700">
                    <li>• <strong>User-Entity Composite:</strong> Unique constraint per user-item pair</li>
                    <li>• <strong>Helpful Users Tracking:</strong> List of users who voted helpful</li>
                    <li>• <strong>Purchase Verification:</strong> Track verified purchases/bookings</li>
                    <li>• <strong>Review Status:</strong> Pending, approved, rejected workflow</li>
                    <li>• <strong>Rich Metadata:</strong> Location, wedding date, images</li>
                    <li>• <strong>Multi-dimensional Ratings:</strong> Service, value, communication</li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Main Demo Tabs */}
          <Tabs defaultValue="reviews" className="space-y-6">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="reviews" className="flex items-center gap-2">
                <Star className="w-4 h-4" />
                Entity Reviews
              </TabsTrigger>
              <TabsTrigger value="dashboard" className="flex items-center gap-2">
                <User className="w-4 h-4" />
                My Reviews
              </TabsTrigger>
              <TabsTrigger value="shop" className="flex items-center gap-2">
                <ShoppingBag className="w-4 h-4" />
                Shop Demo
              </TabsTrigger>
              <TabsTrigger value="venue" className="flex items-center gap-2">
                <MapPin className="w-4 h-4" />
                Venue Demo
              </TabsTrigger>
            </TabsList>

            <TabsContent value="reviews">
              <Card>
                <CardHeader>
                  <CardTitle>User-Specific Review Features</CardTitle>
                  <p className="text-gray-600">
                    Test the enhanced review system with user authentication and duplicate prevention.
                  </p>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    <Card className="border-blue-200">
                      <CardHeader>
                        <CardTitle className="flex items-center gap-2 text-blue-900">
                          <ShoppingBag className="w-5 h-5" />
                          Shop Reviews
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <EntityReviews 
                          entityType="SHOP"
                          entityId={mockEntities.shop.id}
                          entityName={mockEntities.shop.name}
                        />
                      </CardContent>
                    </Card>

                    <Card className="border-green-200">
                      <CardHeader>
                        <CardTitle className="flex items-center gap-2 text-green-900">
                          <MapPin className="w-5 h-5" />
                          Venue Reviews
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <EntityReviews 
                          entityType="VENUE"
                          entityId={mockEntities.venue.id}
                          entityName={mockEntities.venue.name}
                        />
                      </CardContent>
                    </Card>

                    <Card className="border-purple-200">
                      <CardHeader>
                        <CardTitle className="flex items-center gap-2 text-purple-900">
                          <Users className="w-5 h-5" />
                          Vendor Reviews
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <EntityReviews 
                          entityType="VENDOR"
                          entityId={mockEntities.vendor.id}
                          entityName={mockEntities.vendor.name}
                        />
                      </CardContent>
                    </Card>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="dashboard">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <User className="w-5 h-5" />
                    User Reviews Dashboard
                  </CardTitle>
                  <p className="text-gray-600">
                    View and manage all your reviews in one place.
                  </p>
                </CardHeader>
                <CardContent>
                  <UserReviewsDashboard />
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="shop">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <ShoppingBag className="w-5 h-5" />
                    {mockEntities.shop.name} - Shop Review Demo
                  </CardTitle>
                  <p className="text-gray-600">
                    Test shop-specific reviews with user authentication.
                  </p>
                </CardHeader>
                <CardContent>
                  <EntityReviews 
                    entityType="SHOP"
                    entityId={mockEntities.shop.id}
                    entityName={mockEntities.shop.name}
                  />
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="venue">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <MapPin className="w-5 h-5" />
                    {mockEntities.venue.name} - Venue Review Demo
                  </CardTitle>
                  <p className="text-gray-600">
                    Test venue-specific reviews with user authentication.
                  </p>
                </CardHeader>
                <CardContent>
                  <EntityReviews 
                    entityType="VENUE"
                    entityId={mockEntities.venue.id}
                    entityName={mockEntities.venue.name}
                  />
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>

          {/* Technical Implementation */}
          <Card className="mt-8">
            <CardHeader>
              <CardTitle>🔧 Technical Implementation Details</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h4 className="font-semibold text-gray-900 mb-3">🛠️ Key Fixes Applied:</h4>
                  <ul className="space-y-1 text-sm text-gray-700">
                    <li>• <strong>User ID Validation:</strong> Proper user authentication check</li>
                    <li>• <strong>Composite Key:</strong> userEntityComposite for uniqueness</li>
                    <li>• <strong>Duplicate Prevention:</strong> Check existing reviews before creation</li>
                    <li>• <strong>Helpful Vote Tracking:</strong> Prevent duplicate helpful votes</li>
                    <li>• <strong>User Context:</strong> Pass user object to all review operations</li>
                    <li>• <strong>Review Ownership:</strong> Show edit options for user's own reviews</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-semibold text-gray-900 mb-3">📁 Updated Files:</h4>
                  <ul className="space-y-1 text-sm text-gray-700 font-mono">
                    <li>• <code>amplify/backend/api/thirumanam/schema.graphql</code></li>
                    <li>• <code>src/services/entityReviewService.js</code></li>
                    <li>• <code>components/entity-reviews.tsx</code></li>
                    <li>• <code>components/entity-review-form.tsx</code></li>
                    <li>• <code>components/user-reviews-dashboard.tsx</code></li>
                    <li>• <code>app/user-review-demo/page.tsx</code></li>
                  </ul>
                </div>
              </div>

              <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                <h4 className="font-semibold text-blue-900 mb-2">🧪 How to Test:</h4>
                <ol className="list-decimal list-inside space-y-1 text-sm text-blue-800">
                  <li>Ensure you're logged in to see user-specific features</li>
                  <li>Try to submit a review for any entity (shop/venue/vendor)</li>
                  <li>Attempt to submit another review for the same entity (should be prevented)</li>
                  <li>Mark reviews as helpful (should prevent duplicate votes)</li>
                  <li>Check the "My Reviews" dashboard to see your submitted reviews</li>
                  <li>View review statistics and user-specific UI changes</li>
                </ol>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </LayoutWrapper>
  )
}
