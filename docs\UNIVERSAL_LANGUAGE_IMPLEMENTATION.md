# Universal Language Implementation Guide

## Overview
This guide explains how to implement language support across all pages in the BookmyFestive application.

## Quick Start

### 1. Import Translation Hooks
```tsx
import { useUniversalTranslation, useCartTranslations, useCommonTranslations } from '@/lib/universal-i18n';
```

### 2. Use in Components
```tsx
export default function MyPage() {
  const { t } = useUniversalTranslation();
  const cartT = useCartTranslations();
  const commonT = useCommonTranslations();

  return (
    <div>
      <h1>{t('hero.title', 'Default Title')}</h1>
      <button>{commonT.save}</button>
      <p>{cartT.emptyCart}</p>
    </div>
  );
}
```

## Available Translation Hooks

### 1. `useUniversalTranslation(namespace?)`
- **Purpose**: Access any translation key from any namespace
- **Usage**: `const { t, currentLanguage } = useUniversalTranslation('common');`
- **Parameters**: 
  - `namespace` (optional): 'common', 'cart', etc. Defaults to 'common'

### 2. `useCartTranslations()`
- **Purpose**: Pre-configured cart-specific translations
- **Usage**: `const cartT = useCartTranslations();`
- **Available Keys**: title, items, total, checkout, etc.

### 3. `useCommonTranslations()`
- **Purpose**: Pre-configured common UI translations
- **Usage**: `const commonT = useCommonTranslations();`
- **Available Keys**: loading, save, cancel, delete, etc.

## Translation Key Structure

### Common Namespace (`common`)
```json
{
  "navigation": { "vendors": "Vendors", "venues": "Venues" },
  "hero": { "title": "Plan Your Dream Celebration" },
  "common": { "loading": "Loading...", "save": "Save" }
}
```

### Cart Namespace (`cart`)
```json
{
  "title": "Shopping Cart",
  "items": "items",
  "checkout": "Proceed to Checkout"
}
```

## Adding New Translations

### 1. Add to English First
```bash
# Edit /public/locales/en/common.json or cart.json
{
  "newSection": {
    "newKey": "New English Text"
  }
}
```

### 2. Use Translation Script
```bash
# Run the translation update script
node scripts/update-all-translations.js
```

### 3. Manual Translation (Optional)
Edit individual language files in `/public/locales/{lang}/`

## Best Practices

### 1. Always Provide Fallbacks
```tsx
// Good
const text = t('hero.title', 'Default Title');

// Bad
const text = t('hero.title');
```

### 2. Use Namespace-Specific Hooks
```tsx
// Good - for cart pages
const cartT = useCartTranslations();

// Good - for common UI elements
const commonT = useCommonTranslations();

// Good - for specific namespaces
const { t } = useUniversalTranslation('cart');
```

### 3. Group Related Translations
```json
{
  "userProfile": {
    "title": "User Profile",
    "edit": "Edit Profile",
    "save": "Save Changes"
  }
}
```

## Supported Languages

- English (en) - Default
- Tamil (ta)
- Hindi (hi)
- Telugu (te)
- Kannada (kn)
- Malayalam (ml)
- Gujarati (gu)
- Marathi (mr)
- Bengali (bn)
- Odia (or)
- Assamese (as)
- Urdu (ur)
- Nepali (ne)
- Punjabi (pa)

## Example Implementation

### Simple Page Translation
```tsx
"use client"

import { useUniversalTranslation } from '@/lib/universal-i18n';

export default function AboutPage() {
  const { t } = useUniversalTranslation();

  return (
    <div>
      <h1>{t('about.title', 'About Us')}</h1>
      <p>{t('about.description', 'We help Plan Your Dream Celebration.')}</p>
    </div>
  );
}
```

### Complex Page with Multiple Namespaces
```tsx
"use client"

import { useUniversalTranslation, useCartTranslations, useCommonTranslations } from '@/lib/universal-i18n';

export default function ShopPage() {
  const { t } = useUniversalTranslation();
  const cartT = useCartTranslations();
  const commonT = useCommonTranslations();

  return (
    <div>
      <h1>{t('shop.title', 'Wedding Shop')}</h1>
      <button>{cartT.addToCart}</button>
      <button>{commonT.loading}</button>
    </div>
  );
}
```

## Language Switching

The language selector component automatically handles language switching and persistence. Users can change languages from the header, and their preference is saved in localStorage.

## Performance Notes

- Translations are loaded at build time for better performance
- The I18nProvider is wrapped at the root level for universal access
- Fallbacks ensure the app works even if translations are missing
- Namespace-specific hooks reduce bundle size by loading only needed translations

## Troubleshooting

### Translation Not Showing
1. Check if the key exists in the JSON file
2. Verify the namespace is correct
3. Ensure fallback text is provided
4. Check browser console for i18n errors

### Language Not Switching
1. Verify the language file exists
2. Check if the language is added to SUPPORTED_LANGUAGES
3. Clear localStorage and try again
4. Check browser console for loading errors

## Migration Guide

### Converting Existing Pages
1. Import translation hooks
2. Replace hardcoded text with translation calls
3. Add translation keys to JSON files
4. Test with different languages

### Example Migration
```tsx
// Before
<h1>Shopping Cart</h1>

// After
const cartT = useCartTranslations();
<h1>{cartT.title}</h1>
```

This implementation ensures consistent language support across all pages with minimal code changes and maximum flexibility.