"use client"

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Checkbox } from '@/components/ui/checkbox';
import { ImageUpload } from '@/components/ui/image-upload';
import { 
  ShoppingBag, 
  Search, 
  Plus, 
  Edit, 
  Trash2, 
  Filter, 
  Download, 
  Upload,
  Eye,
  CheckCircle,
  XCircle,
  Star,
  Package,
  DollarSign,
  Calendar,
  Users,
  TrendingUp,
  Briefcase
} from 'lucide-react';
import { AdminOnlyRoute } from '@/components/RouteProtection';
import AdminContentService, { ContentFilters } from '@/lib/services/adminContentService';
import toast from 'react-hot-toast';

export default function AdminShopsPage() {
  const [shops, setShops] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedShops, setSelectedShops] = useState<string[]>([]);
  const [filters, setFilters] = useState<ContentFilters>({});
  const [showFilters, setShowFilters] = useState(false);
  const [showShopDetails, setShowShopDetails] = useState(false);
  const [selectedShop, setSelectedShop] = useState<any>(null);
  const [shopStats, setShopStats] = useState<any>(null);
  const [nextToken, setNextToken] = useState<string | undefined>();
  const [hasMore, setHasMore] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(10);

  // Multi-step edit modal state
  const [editForm, setEditForm] = useState<any>(null);
  const [editStep, setEditStep] = useState(1);
  const totalEditSteps = 4;
  const [saving, setSaving] = useState(false);

  useEffect(() => {
    loadShops();
    loadShopStats();
  }, []);

  useEffect(() => {
    const delayedSearch = setTimeout(() => {
      loadShops();
    }, 500);

    return () => clearTimeout(delayedSearch);
  }, [searchTerm, filters]);

  const loadShops = async (loadMore: boolean = false, page: number = 1) => {
    try {
      setLoading(true);

      const searchFilters: ContentFilters = {
        ...filters,
        searchTerm: searchTerm || undefined
      };

      const result = await AdminContentService.getAllShops(
        searchFilters,
        itemsPerPage,
        loadMore ? nextToken : undefined
      );

      if (result.success && result.data) {
        if (loadMore) {
          setShops(prev => [...prev, ...result.data!.shops]);
        } else {
          setShops(result.data.shops);
          setCurrentPage(page);
        }
        setNextToken(result.data.nextToken);
        setHasMore(!!result.data.nextToken);
      } else {
        toast.error(result.error || 'Failed to load shops');
      }
    } catch (error) {
      console.error('Error loading shops:', error);
      toast.error('Failed to load shops');
    } finally {
      setLoading(false);
    }
  };

  const loadShopStats = async () => {
    try {
      const result = await AdminContentService.getContentStatistics();
      if (result.success) {
        setShopStats(result.data);
      }
    } catch (error) {
      console.error('Error loading shop stats:', error);
    }
  };

  const handleDeleteShop = async (shopId: string) => {
    if (!confirm('Are you sure you want to delete this product? This action cannot be undone.')) {
      return;
    }

    try {
      const result = await AdminContentService.deleteShop(shopId);
      if (result.success) {
        toast.success('Product deleted successfully');
        loadShops();
        loadShopStats();
      } else {
        toast.error(result.error || 'Failed to delete product');
      }
    } catch (error) {
      console.error('Error deleting product:', error);
      toast.error('Failed to delete product');
    }
  };

  const handleBulkAction = async (action: 'delete' | 'activate' | 'deactivate' | 'feature' | 'unfeature') => {
    if (selectedShops.length === 0) {
      toast.error('Please select products first');
      return;
    }

    if (action === 'delete' && !confirm(`Are you sure you want to delete ${selectedShops.length} products? This action cannot be undone.`)) {
      return;
    }

    try {
      if (action === 'delete') {
        const result = await AdminContentService.bulkDelete('shop', selectedShops);
        if (result.success) {
          toast.success(`${selectedShops.length} products deleted successfully`);
        } else {
          toast.error(result.error || 'Failed to delete products');
        }
      } else {
        const updateData: any = {};
        if (action === 'activate') updateData.status = 'active';
        if (action === 'deactivate') updateData.status = 'inactive';
        if (action === 'feature') updateData.featured = true;
        if (action === 'unfeature') updateData.featured = false;

        const updatePromises = selectedShops.map(shopId => 
          AdminContentService.updateShop(shopId, updateData)
        );
        await Promise.all(updatePromises);
        toast.success(`${selectedShops.length} products updated successfully`);
      }
      
      setSelectedShops([]);
      loadShops();
      loadShopStats();
    } catch (error) {
      console.error(`Error performing bulk ${action}:`, error);
      toast.error(`Failed to ${action} products`);
    }
  };

  const handleSelectAll = () => {
    if (selectedShops.length === shops.length) {
      setSelectedShops([]);
    } else {
      setSelectedShops(shops.map(shop => shop.id));
    }
  };

  const handleShopSelect = (shopId: string) => {
    setSelectedShops(prev => 
      prev.includes(shopId) 
        ? prev.filter(id => id !== shopId)
        : [...prev, shopId]
    );
  };

  const applyFilters = (newFilters: ContentFilters) => {
    setFilters(newFilters);
    setNextToken(undefined);
  };

  const clearFilters = () => {
    setFilters({});
    setSearchTerm('');
    setNextToken(undefined);
  };

  // Open modal and prefill form
  const openShopDetails = (shop: any) => {
    setSelectedShop(shop);
    setEditForm({
      ...shop,
      features: shop.features ? [...shop.features] : [],
      images: shop.images ? [...shop.images] : [],
      imageUrls: shop.imageUrls ? [...shop.imageUrls] : [],
    });
    setEditStep(1);
    setShowShopDetails(true);
  };

  // Handle form field changes
  const handleEditChange = (field: string, value: any) => {
    setEditForm((prev: any) => ({ ...prev, [field]: value }));
  };

  // Save handler (sanitize input)
  const handleSaveEdit = async () => {
    if (!editForm) return;
    setSaving(true);
    try {
      // Only send valid fields, remove undefined/null/empty, clean arrays
      const allowedFields = [
        'name', 'category', 'brand', 'sku', 'description', 'price', 'originalPrice', 'discount', 'stock', 'inStock', 'featured',
        'features', 'images', 'status', 'rating', 'reviewCount'
      ];

      // Specification fields that should be nested under specifications
      const specificationFields = ['fabric', 'work', 'occasion', 'care', 'delivery', 'returnPolicy'];

      const cleaned: any = {};

      // Process regular fields
      for (const key of allowedFields) {
        if (key in editForm && editForm[key] !== undefined && editForm[key] !== null && editForm[key] !== '') {
          if (key === 'features' && Array.isArray(editForm.features)) {
            cleaned.features = editForm.features.filter((f: string) => f && f.trim());
          } else if (key === 'images' && Array.isArray(editForm.images)) {
            cleaned.images = editForm.images.filter((img: string) => img && img.trim());
          } else {
            cleaned[key] = editForm[key];
          }
        }
      }

      // Process specification fields
      const specifications: any = {};
      let hasSpecifications = false;
      for (const key of specificationFields) {
        if (key in editForm && editForm[key] !== undefined && editForm[key] !== null && editForm[key] !== '') {
          specifications[key] = editForm[key];
          hasSpecifications = true;
        }
      }

      if (hasSpecifications) {
        cleaned.specifications = specifications;
      }

      // Handle imageUrls separately - convert to images if needed
      if (editForm.imageUrls && Array.isArray(editForm.imageUrls)) {
        const validImageUrls = editForm.imageUrls.filter((img: string) => img && img.trim());
        if (validImageUrls.length > 0) {
          cleaned.images = validImageUrls;
        }
      }

      cleaned.id = editForm.id;
      const result = await AdminContentService.updateShop(editForm.id, cleaned);
      if (result.success) {
        toast.success('Product updated successfully');
        setShowShopDetails(false);
        setEditForm(null);
        loadShops();
      } else {
        toast.error(result.error || 'Failed to update product');
      }
    } catch (error) {
      toast.error('Failed to update product');
    } finally {
      setSaving(false);
    }
  };

  const getStatusBadge = (shop: any) => {
    if (shop.status === 'active') {
      return <Badge className="bg-green-100 text-green-800"><CheckCircle className="w-3 h-3 mr-1" />Active</Badge>;
    }
    return <Badge variant="secondary"><XCircle className="w-3 h-3 mr-1" />Inactive</Badge>;
  };

  const getFeaturedBadge = (shop: any) => {
    if (shop.featured) {
      return <Badge className="bg-blue-100 text-blue-800"><Star className="w-3 h-3 mr-1" />Featured</Badge>;
    }
    return null;
  };

  const getStockBadge = (shop: any) => {
    if (shop.inStock && shop.stock > 0) {
      return <Badge className="bg-blue-100 text-blue-800"><Package className="w-3 h-3 mr-1" />In Stock ({shop.stock})</Badge>;
    }
    return <Badge variant="destructive"><XCircle className="w-3 h-3 mr-1" />Out of Stock</Badge>;
  };

  return (
    <AdminOnlyRoute>
      <div className="space-y-8">
        {/* Header */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 mb-1">Manage Shops</h1>
            <p className="text-gray-500 text-base">{shops.length} shop{shops.length !== 1 ? 's' : ''}</p>
          </div>
          <div className="flex gap-2">
            {selectedShops.length > 0 && (
              <Button variant="outline" onClick={() => handleBulkAction('delete')} className="text-red-600 hover:text-red-700 hover:bg-red-50">Delete Selected ({selectedShops.length})</Button>
            )}
            <Button className="bg-primary hover:bg-primary/90"><Plus className="w-4 h-4 mr-2" />Add Shop</Button>
          </div>
        </div>
        {/* Filters */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-4 mb-6">
          <div className="grid grid-cols-1 md:grid-cols-6 gap-4">
              <div className="relative">
              <Input type="text" placeholder="Search shops..." value={searchTerm} onChange={e => setSearchTerm(e.target.value)} className="w-full pl-10" />
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              </div>
            <Select value={filters.category || 'all'} onValueChange={value => applyFilters({ ...filters, category: value === 'all' ? undefined : value })}>
              <SelectTrigger><SelectValue placeholder="All Categories" /></SelectTrigger>
                      <SelectContent>
                <SelectItem value="all">All Categories</SelectItem>
                        <SelectItem value="Clothing">Clothing</SelectItem>
                        <SelectItem value="Jewelry">Jewelry</SelectItem>
                        <SelectItem value="Accessories">Accessories</SelectItem>
                        <SelectItem value="Footwear">Footwear</SelectItem>
                        <SelectItem value="Decor">Decor</SelectItem>
                      </SelectContent>
                    </Select>
            <Select value={filters.status || 'all'} onValueChange={value => applyFilters({ ...filters, status: value === 'all' ? undefined : value })}>
              <SelectTrigger><SelectValue placeholder="All Status" /></SelectTrigger>
                      <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                        <SelectItem value="active">Active</SelectItem>
                        <SelectItem value="inactive">Inactive</SelectItem>
                        <SelectItem value="pending">Pending</SelectItem>
                      </SelectContent>
                    </Select>
            <Select value={filters.inStock !== undefined ? String(filters.inStock) : 'all'} onValueChange={value => applyFilters({ ...filters, inStock: value === 'all' ? undefined : value === 'true' })}>
              <SelectTrigger><SelectValue placeholder="All Products" /></SelectTrigger>
                      <SelectContent>
                <SelectItem value="all">All Products</SelectItem>
                        <SelectItem value="true">In Stock</SelectItem>
                        <SelectItem value="false">Out of Stock</SelectItem>
                      </SelectContent>
                    </Select>
            <Select value={filters.featured !== undefined ? String(filters.featured) : 'all'} onValueChange={value => applyFilters({ ...filters, featured: value === 'all' ? undefined : value === 'true' })}>
              <SelectTrigger><SelectValue placeholder="All Shops" /></SelectTrigger>
                      <SelectContent>
                <SelectItem value="all">All Shops</SelectItem>
                        <SelectItem value="true">Featured Only</SelectItem>
                        <SelectItem value="false">Non-Featured</SelectItem>
                      </SelectContent>
                    </Select>
            {/* Add sortBy if needed */}
                  </div>
                </div>
        {/* Shop Cards Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {loading ? (
            <div className="col-span-full text-center py-12">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
              <p className="mt-2 text-gray-600">Loading shops...</p>
              </div>
            ) : shops.length === 0 ? (
            <div className="col-span-full text-center py-12">
              <Briefcase className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-600">No shops found</p>
              </div>
            ) : (
            shops.map((shop) => (
              <Card key={shop.id} className="overflow-hidden rounded-2xl shadow border border-gray-100 bg-white hover:shadow-md transition-shadow relative">
                <CardContent className="p-6 flex flex-col h-full">
                  <div className="flex items-start justify-between mb-2">
                        <div className="flex-1">
                      <h2 className="font-bold text-xl text-gray-900 mb-1 line-clamp-1">{shop.name}</h2>
                      <p className="text-primary font-medium text-xs uppercase tracking-wide mb-1">{shop.category}</p>
                    </div>
                    <div className="flex gap-2">
                      {shop.featured && <Badge className="bg-blue-100 text-blue-800"><Star className="w-3 h-3 mr-1" />Featured</Badge>}
                      <Badge variant={shop.status === 'active' ? 'default' : 'secondary'}>{shop.status || 'active'}</Badge>
                    </div>
                    </div>
                  <div className="text-gray-500 text-sm mb-2 flex items-center gap-2">
                    <span>₹{shop.price || 'N/A'}</span>
                    </div>
                  <div className="text-gray-500 text-sm mb-2 flex items-center gap-2">
                    <span>{shop.brand ? `Brand: ${shop.brand}` : ''}</span>
                      </div>
                  <div className="flex items-center gap-2 mt-1 mb-2">
                    {shop.inStock ? <Badge className="bg-green-100 text-green-800"><CheckCircle className="w-3 h-3 mr-1" />In Stock</Badge> : <Badge variant="secondary"><XCircle className="w-3 h-3 mr-1" />Out of Stock</Badge>}
                    </div>
                  <div className="flex gap-2 mt-auto">
                    <Button variant="outline" size="sm" onClick={() => openShopDetails(shop)} className="flex-1">Edit</Button>
                    <Button variant="outline" size="sm" onClick={() => handleDeleteShop(shop.id)} className="flex-1 text-red-600 hover:text-red-700">Delete</Button>
                    <Checkbox checked={selectedShops.includes(shop.id)} onCheckedChange={() => handleShopSelect(shop.id)} className="ml-2 mt-1" />
                  </div>
                </CardContent>
              </Card>
            ))
          )}
        </div>
                {/* Pagination Controls */}
        {shops.length > 0 && (
          <div className="flex justify-center items-center gap-4 pt-6">
            <Button
              variant="outline"
              onClick={() => {
                if (currentPage > 1) {
                  loadShops(false, currentPage - 1);
                }
              }}
              disabled={loading || currentPage === 1}
            >
              Previous
            </Button>
            <span className="text-sm text-gray-600">
              Page {currentPage} • {shops.length} items
            </span>
            <Button
              variant="outline"
              onClick={() => {
                if (hasMore) {
                  loadShops(false, currentPage + 1);
                }
              }}
              disabled={loading || !hasMore}
            >
              Next
            </Button>
          </div>
        )}
        {/* Shop Details Modal */}
        <Dialog open={showShopDetails} onOpenChange={setShowShopDetails}>
          <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto rounded-2xl">
            <DialogHeader>
              <DialogTitle>Edit Product</DialogTitle>
            </DialogHeader>
            {editForm && (
              <form className="space-y-6" onSubmit={e => { e.preventDefault(); handleSaveEdit(); }}>
                {/* Progress Bar */}
                <div className="mb-4">
                  <div className="w-full h-3 rounded-full bg-gray-200 mb-1">
                    <div className="h-3 rounded-full bg-[#800000] transition-all" style={{ width: `${(editStep / totalEditSteps) * 100}%` }} />
                  </div>
                  <div className="flex justify-between text-sm text-gray-500">
                    <span>Step {editStep} of {totalEditSteps}</span>
                    <span>{['Basic Info', 'Pricing & Inventory', 'Features & Images', 'Specs & Status'][editStep-1]}</span>
                  </div>
                </div>
                {/* Step 1: Basic Info */}
                {editStep === 1 && (
                  <div className="bg-gray-50 p-6 rounded-lg">
                    <h3 className="text-lg font-semibold mb-4">Basic Information</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Product Name *</label>
                        <Input value={editForm.name || ''} onChange={e => handleEditChange('name', e.target.value)} required />
                  </div>
                  <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Category *</label>
                        <Input value={editForm.category || ''} onChange={e => handleEditChange('category', e.target.value)} required />
                  </div>
                  <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Brand</label>
                        <Input value={editForm.brand || ''} onChange={e => handleEditChange('brand', e.target.value)} />
                  </div>
                  <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">SKU</label>
                        <Input value={editForm.sku || ''} onChange={e => handleEditChange('sku', e.target.value)} />
                      </div>
                      <div className="col-span-2">
                        <label className="block text-sm font-medium text-gray-700 mb-2">Description</label>
                        <textarea className="border rounded-lg px-3 py-2 w-full h-24 focus:ring-2 focus:ring-primary focus:border-transparent" value={editForm.description || ''} onChange={e => handleEditChange('description', e.target.value)} />
                  </div>
                    </div>
                  </div>
                )}
                {/* Step 2: Pricing & Inventory */}
                {editStep === 2 && (
                  <div className="bg-gray-50 p-6 rounded-lg">
                    <h3 className="text-lg font-semibold mb-4">Pricing & Inventory</h3>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Current Price *</label>
                        <Input value={editForm.price || ''} onChange={e => handleEditChange('price', e.target.value)} required />
                  </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Original Price</label>
                        <Input value={editForm.originalPrice || ''} onChange={e => handleEditChange('originalPrice', e.target.value)} />
                </div>
                  <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Discount %</label>
                        <Input value={editForm.discount || ''} onChange={e => handleEditChange('discount', e.target.value)} />
                  </div>
                  <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Stock Quantity *</label>
                        <Input value={editForm.stock || ''} onChange={e => handleEditChange('stock', e.target.value)} required />
                      </div>
                      <div className="flex items-center gap-4 mt-2">
                        <Checkbox checked={!!editForm.inStock} onCheckedChange={v => handleEditChange('inStock', v)} />
                        <span>In Stock</span>
                        <Checkbox checked={!!editForm.featured} onCheckedChange={v => handleEditChange('featured', v)} />
                        <span>Featured Product</span>
                        </div>
                    </div>
                  </div>
                )}
                {/* Step 3: Features & Images */}
                {editStep === 3 && (
                  <div className="bg-gray-50 p-6 rounded-lg">
                    <h3 className="text-lg font-semibold mb-4">Product Features & Images</h3>
                    <div className="mb-4">
                      <label className="block text-sm font-medium text-gray-700 mb-2">Features (one per line)</label>
                      <textarea className="border rounded-lg px-3 py-2 w-full h-24 focus:ring-2 focus:ring-primary focus:border-transparent" value={(editForm.features || []).join('\n')} onChange={e => handleEditChange('features', e.target.value.split('\n'))} placeholder="Enter each feature on a new line" />
                    </div>
                    <div className="mb-4">
                      <ImageUpload
                        images={editForm?.images || []}
                        onImagesChange={images => handleEditChange('images', images)}
                        maxImages={8}
                        label="Upload Product Images"
                        description="Upload product images (JPG, PNG, WebP) - Images will be compressed for database storage"
                        compressForDB={true}
                        showSizeWarning={true}
                      />
                    </div>
                    <div className="mb-4">
                      <label className="block text-sm font-medium text-gray-700 mb-2">Or Add Image URLs</label>
                      <div className="flex gap-2">
                        <Input className="flex-1" placeholder="https://example.com/image.jpg" />
                        <Button type="button" variant="outline" size="sm">Add URL</Button>
                        </div>
                    </div>
                  </div>
                )}
                {/* Step 4: Specs & Status */}
                {editStep === 4 && (
                  <div className="bg-gray-50 p-6 rounded-lg">
                    <h3 className="text-lg font-semibold mb-4">Specifications & Status</h3>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Fabric</label>
                        <Input value={editForm.fabric || ''} onChange={e => handleEditChange('fabric', e.target.value)} placeholder="e.g., Pure Silk" />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Work</label>
                        <Input value={editForm.work || ''} onChange={e => handleEditChange('work', e.target.value)} placeholder="e.g., Hand Embroidery" />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Occasion</label>
                        <Input value={editForm.occasion || ''} onChange={e => handleEditChange('occasion', e.target.value)} placeholder="e.g., Wedding, Reception" />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Care Instructions</label>
                        <Input value={editForm.care || ''} onChange={e => handleEditChange('care', e.target.value)} placeholder="e.g., Dry Clean Only" />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Delivery Time</label>
                        <Input value={editForm.delivery || ''} onChange={e => handleEditChange('delivery', e.target.value)} placeholder="e.g., 7-10 business days" />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Return Policy</label>
                        <Input value={editForm.returnPolicy || ''} onChange={e => handleEditChange('returnPolicy', e.target.value)} placeholder="e.g., 15 days return" />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Status</label>
                        <Input value={editForm.status || ''} onChange={e => handleEditChange('status', e.target.value)} placeholder="Active" />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Rating (0-5)</label>
                        <Input type="number" min={0} max={5} step={0.1} value={editForm.rating || 0} onChange={e => handleEditChange('rating', parseFloat(e.target.value))} />
                      </div>
                  <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Review Count</label>
                        <Input type="number" min={0} value={editForm.reviewCount || 0} onChange={e => handleEditChange('reviewCount', parseInt(e.target.value))} />
                        </div>
                    </div>
                  </div>
                )}
                {/* Navigation Buttons */}
                <div className="flex gap-3 justify-between pt-4 border-t mt-6">
                  <Button type="button" variant="outline" onClick={() => setShowShopDetails(false)} disabled={saving}>Cancel</Button>
                  <div className="flex gap-2 ml-auto">
                    {editStep > 1 && (
                      <Button type="button" variant="outline" onClick={() => setEditStep(editStep - 1)} disabled={saving}>Back</Button>
                    )}
                    {editStep < totalEditSteps && (
                      <Button type="button" className="bg-primary hover:bg-primary/90" onClick={() => setEditStep(editStep + 1)} disabled={saving}>Next</Button>
                    )}
                    {editStep === totalEditSteps && (
                      <Button type="submit" className="bg-primary hover:bg-primary/90" disabled={saving}>{saving ? 'Saving...' : 'Save Changes'}</Button>
                    )}
                  </div>
                </div>
              </form>
            )}
          </DialogContent>
        </Dialog>
      </div>
    </AdminOnlyRoute>
  );
}
