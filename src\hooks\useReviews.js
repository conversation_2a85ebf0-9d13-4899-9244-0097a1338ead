import { useState, useEffect, useCallback } from 'react';
import { ReviewService } from '../services/reviewService';
import { useAuth } from '@/contexts/AuthContext';
import { showToast, toastMessages } from '@/lib/toast';

/**
 * Custom hook for managing reviews
 * Provides state management and API operations for reviews
 */
export const useReviews = (options = {}) => {
  const { user } = useAuth();
  const [reviews, setReviews] = useState([]);
  const [reviewStats, setReviewStats] = useState({
    totalReviews: 0,
    averageRating: 0,
    satisfactionRate: 0,
    recommendationRate: 0,
    ratingDistribution: { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 }
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [nextToken, setNextToken] = useState(null);
  const [hasMore, setHasMore] = useState(true);

  const {
    autoLoad = true,
    limit = 20,
    category = null,
    minRating = null
  } = options;

  /**
   * Load reviews from API
   */
  const loadReviews = useCallback(async (reset = false) => {
    if (loading) return;

    setLoading(true);
    setError(null);

    try {
      let result;
      
      if (category) {
        result = await ReviewService.getReviewsByCategory(category, {
          limit,
          nextToken: reset ? null : nextToken
        });
      } else if (minRating) {
        result = await ReviewService.getReviewsByRating(minRating, {
          limit,
          nextToken: reset ? null : nextToken
        });
      } else {
        // Use platform reviews by default to exclude product-specific reviews
        result = await ReviewService.listPlatformReviews({
          limit,
          nextToken: reset ? null : nextToken
        });
      }

      if (result.success) {
        setReviews(prev => reset ? result.data : [...prev, ...result.data]);
        setNextToken(result.nextToken);
        setHasMore(!!result.nextToken);
      } else {
        setError(result.error);
      }
    } catch (err) {
      setError(err.message || 'Failed to load reviews');
    } finally {
      setLoading(false);
    }
  }, [category, minRating, limit, nextToken, loading]);

  /**
   * Load review statistics
   */
  const loadReviewStats = useCallback(async () => {
    try {
      const result = await ReviewService.getReviewStats();
      if (result.success) {
        setReviewStats(result.data);
      }
    } catch (err) {
      console.error('Failed to load review stats:', err);
    }
  }, []);

  /**
   * Submit a new review
   */
  const submitReview = useCallback(async (reviewData) => {
    if (!user) {
      throw new Error('User must be authenticated to submit a review');
    }

    setLoading(true);
    setError(null);

    try {
      const result = await ReviewService.createReview({
        ...reviewData,
        userId: user.sub || user.id
      });

      if (result.success) {
        // Refresh reviews and stats after successful submission
        await loadReviews(true);
        await loadReviewStats();
        return result;
      } else {
        setError(result.error);
        return result;
      }
    } catch (err) {
      const errorMessage = err.message || 'Failed to submit review';
      setError(errorMessage);
      return {
        success: false,
        error: errorMessage
      };
    } finally {
      setLoading(false);
    }
  }, [user, loadReviews, loadReviewStats]);

  /**
   * Load more reviews (pagination)
   */
  const loadMore = useCallback(() => {
    if (hasMore && !loading) {
      loadReviews(false);
    }
  }, [hasMore, loading, loadReviews]);

  /**
   * Refresh reviews (reload from beginning)
   */
  const refresh = useCallback(() => {
    setNextToken(null);
    setHasMore(true);
    loadReviews(true);
    loadReviewStats();
  }, [loadReviews, loadReviewStats]);

  /**
   * Filter reviews by category
   */
  const filterByCategory = useCallback((newCategory) => {
    setReviews([]);
    setNextToken(null);
    setHasMore(true);
    // The category will be used in the next loadReviews call
  }, []);

  /**
   * Filter reviews by rating
   */
  const filterByRating = useCallback((newMinRating) => {
    setReviews([]);
    setNextToken(null);
    setHasMore(true);
    // The minRating will be used in the next loadReviews call
  }, []);

  /**
   * Get review by ID
   */
  const getReview = useCallback(async (id) => {
    try {
      const result = await ReviewService.getReview(id);
      return result;
    } catch (err) {
      console.error('Failed to get review:', err);
      return {
        success: false,
        error: err.message || 'Failed to get review'
      };
    }
  }, []);

  // Auto-load reviews on mount
  useEffect(() => {
    if (autoLoad) {
      loadReviews(true);
      loadReviewStats();
    }
  }, [autoLoad, category, minRating]); // Re-load when filters change

  return {
    // State
    reviews,
    reviewStats,
    loading,
    error,
    hasMore,
    
    // Actions
    loadReviews,
    loadReviewStats,
    submitReview,
    loadMore,
    refresh,
    filterByCategory,
    filterByRating,
    getReview,
    
    // Utilities
    clearError: () => setError(null)
  };
};

/**
 * Hook for review statistics only
 */
export const useReviewStats = () => {
  const [stats, setStats] = useState({
    totalReviews: 0,
    averageRating: 0,
    satisfactionRate: 0,
    recommendationRate: 0,
    ratingDistribution: { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 }
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const loadStats = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      const result = await ReviewService.getReviewStats();
      if (result.success) {
        setStats(result.data);
      } else {
        setError(result.error);
      }
    } catch (err) {
      setError(err.message || 'Failed to load review statistics');
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    loadStats();
  }, [loadStats]);

  return {
    stats,
    loading,
    error,
    refresh: loadStats
  };
};

/**
 * Hook for submitting reviews
 */
export const useReviewSubmission = () => {
  const { user } = useAuth();
  const [submitting, setSubmitting] = useState(false);
  const [submitError, setSubmitError] = useState(null);
  const [submitSuccess, setSubmitSuccess] = useState(false);

  const submitReview = useCallback(async (reviewData) => {
    console.log('submitReview called with user:', user);

    if (!user) {
      const errorMsg = 'You must be logged in to submit a review';
      setSubmitError(errorMsg);
      showToast.error(toastMessages.auth.authRequired);
      return { success: false, error: 'Authentication required' };
    }

    setSubmitting(true);
    setSubmitError(null);
    setSubmitSuccess(false);

    try {
      // Ensure userId is set
      const userId = user.sub || user.id || user.username;
      console.log('User ID for review:', userId);

      if (!userId) {
        throw new Error('User ID not available. Please log out and log in again.');
      }

      // Validate form data before submission
      const requiredFields = ['name', 'email', 'location', 'weddingDate', 'category', 'rating', 'title', 'review'];
      const missingFields = [];

      for (const field of requiredFields) {
        if (!reviewData[field] || reviewData[field] === '' || reviewData[field] === null || reviewData[field] === undefined) {
          missingFields.push(field);
        }
      }

      if (missingFields.length > 0) {
        throw new Error(`Please fill in all required fields: ${missingFields.join(', ')}`);
      }

      const result = await ReviewService.createReview({
        ...reviewData,
        userId: userId
      });

      if (result.success) {
        setSubmitSuccess(true);
        showToast.success(toastMessages.review.submitSuccess);
        // Clear success message after 5 seconds
        setTimeout(() => setSubmitSuccess(false), 5000);
      } else {
        setSubmitError(result.error);
        showToast.error(result.error || toastMessages.review.submitError);
      }

      return result;
    } catch (err) {
      console.error('Review submission error:', err);
      const errorMessage = err.message || 'Failed to submit review';
      setSubmitError(errorMessage);
      showToast.error(errorMessage);
      return {
        success: false,
        error: errorMessage
      };
    } finally {
      setSubmitting(false);
    }
  }, [user]);

  const clearMessages = useCallback(() => {
    setSubmitError(null);
    setSubmitSuccess(false);
  }, []);

  return {
    submitReview,
    submitting,
    submitError,
    submitSuccess,
    clearMessages
  };
};

export default useReviews;
