import { generateClient } from 'aws-amplify/api';
import { getCurrentUser } from 'aws-amplify/auth';
import {
  createUserProfile,
  updateUserProfile,
  deleteUserProfile
} from '@/src/graphql/mutations';
import {
  getUserProfile,
  listUserProfiles,
  userProfilesByUserId
} from '@/src/graphql/queries';

// Use API key for admin operations to bypass user-level restrictions
const adminClient = generateClient({
  authMode: 'apiKey'
});

const client = generateClient();

export interface AdminUserData {
  userId: string;
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  isAdmin: boolean;
  isSuperAdmin: boolean;
  role: 'CUSTOMER' | 'VENDOR' | 'ADMIN' | 'SUPER_ADMIN';
  permissions: string[];
}

export interface UserProfile {
  id: string;
  userId: string;
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  isAdmin: boolean;
  isSuperAdmin: boolean;
  isVendor: boolean;
  role: string;
  permissions?: string[];
  createdAt: string;
  updatedAt: string;
  lastLoginAt?: string;
  isVerified?: boolean;
  accountType?: string;
  businessInfo?: any;
}

export interface ServiceResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
}

export interface UserSearchFilters {
  role?: string;
  isAdmin?: boolean;
  isVendor?: boolean;
  isVerified?: boolean;
  searchTerm?: string;
  dateFrom?: string;
  dateTo?: string;
}

export class AdminUserService {

  /**
   * Check if current user is admin
   */
  static async isCurrentUserAdmin(): Promise<boolean> {
    try {
      const user = await getCurrentUser();
      const userProfile = await this.getUserProfile(user.userId);
      
      return userProfile?.isAdmin || userProfile?.isSuperAdmin || false;
    } catch (error) {
      console.error('Error checking admin status:', error);
      return false;
    }
  }

  /**
   * Get user profile by user ID
   */
  static async getUserProfile(userId: string): Promise<any> {
    try {
      const result = await client.graphql({
        query: userProfilesByUserId,
        variables: {
          userId: userId,
          limit: 1
        }
      });

      const profiles = result.data?.userProfilesByUserId?.items || [];
      return profiles.length > 0 ? profiles[0] : null;
    } catch (error) {
      console.error('Error getting user profile:', error);
      return null;
    }
  }

  /**
   * Make a user admin
   */
  static async makeUserAdmin(
    userId: string,
    adminLevel: 'ADMIN' | 'SUPER_ADMIN' = 'ADMIN'
  ): Promise<ServiceResponse<any>> {
    try {
      // First, get the existing user profile
      const existingProfile = await this.getUserProfile(userId);

      if (!existingProfile) {
        // If no profile exists, create one with admin privileges
        console.log('No existing profile found, creating new admin profile...');

        const createInput = {
          userId: userId,
          firstName: 'Admin',
          lastName: 'User',
          email: `admin-${userId}@BookmyFestive.com`,
          isAdmin: true,
          isSuperAdmin: adminLevel === 'SUPER_ADMIN',
          role: adminLevel,
          permissions: this.getDefaultAdminPermissions(adminLevel),
          isVerified: true,
          accountType: 'ADMIN_ACCOUNT',
          registrationSource: 'ADMIN_FORM'
        };

        const result = await client.graphql({
          query: createUserProfile,
          variables: {
            input: createInput
          }
        });

        return {
          success: true,
          data: result.data?.createUserProfile
        };
      }

      // Update the existing user profile with admin privileges
      const updateInput = {
        id: existingProfile.id,
        isAdmin: true,
        isSuperAdmin: adminLevel === 'SUPER_ADMIN',
        role: adminLevel,
        permissions: this.getDefaultAdminPermissions(adminLevel)
      };

      const result = await client.graphql({
        query: updateUserProfile,
        variables: {
          input: updateInput
        }
      });

      return {
        success: true,
        data: result.data?.updateUserProfile
      };
    } catch (error) {
      console.error('Error making user admin:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to make user admin'
      };
    }
  }

  /**
   * Remove admin privileges from a user
   */
  static async removeAdminPrivileges(userId: string): Promise<ServiceResponse<any>> {
    try {
      const existingProfile = await this.getUserProfile(userId);
      
      if (!existingProfile) {
        return {
          success: false,
          error: 'User profile not found'
        };
      }

      const updateInput = {
        id: existingProfile.id,
        isAdmin: false,
        isSuperAdmin: false,
        role: 'CUSTOMER',
        permissions: []
      };

      const result = await client.graphql({
        query: updateUserProfile,
        variables: {
          input: updateInput
        }
      });

      return {
        success: true,
        data: result.data?.updateUserProfile
      };
    } catch (error) {
      console.error('Error removing admin privileges:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to remove admin privileges'
      };
    }
  }

  /**
   * Create a new admin user profile
   */
  static async createAdminUser(userData: AdminUserData): Promise<ServiceResponse<any>> {
    try {
      const createInput = {
        userId: userData.userId,
        firstName: userData.firstName,
        lastName: userData.lastName,
        email: userData.email,
        phone: userData.phone,
        isAdmin: userData.isAdmin,
        isSuperAdmin: userData.isSuperAdmin,
        role: userData.role,
        permissions: userData.permissions,
        isVerified: true,
        accountType: 'ADMIN_ACCOUNT'
      };

      const result = await client.graphql({
        query: createUserProfile,
        variables: {
          input: createInput
        }
      });

      return {
        success: true,
        data: result.data?.createUserProfile
      };
    } catch (error) {
      console.error('Error creating admin user:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to create admin user'
      };
    }
  }

  /**
   * List all admin users
   */
  static async listAdminUsers(): Promise<ServiceResponse<any[]>> {
    try {
      const result = await adminClient.graphql({
        query: listUserProfiles,
        variables: {
          filter: {
            or: [
              { isAdmin: { eq: true } },
              { isSuperAdmin: { eq: true } }
            ]
          },
          limit: 100
        }
      });

      const adminUsers = result.data?.listUserProfiles?.items || [];

      return {
        success: true,
        data: adminUsers
      };
    } catch (error) {
      console.error('Error listing admin users:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to list admin users'
      };
    }
  }

  /**
   * Get default permissions for admin level
   */
  private static getDefaultAdminPermissions(adminLevel: 'ADMIN' | 'SUPER_ADMIN'): string[] {
    const basePermissions = [
      'reviews:read',
      'reviews:moderate',
      'users:read',
      'vendors:read',
      'shops:read',
      'venues:read'
    ];

    const adminPermissions = [
      ...basePermissions,
      'reviews:delete',
      'users:update',
      'vendors:update'
    ];

    const superAdminPermissions = [
      ...adminPermissions,
      'users:delete',
      'users:create_admin',
      'system:manage',
      'permissions:manage'
    ];

    return adminLevel === 'SUPER_ADMIN' ? superAdminPermissions : adminPermissions;
  }

  /**
   * Get all users with optional filtering
   */
  static async getAllUsers(filters?: UserSearchFilters, limit: number = 50, nextToken?: string): Promise<ServiceResponse<{users: UserProfile[], nextToken?: string}>> {
    try {
      let filterConditions: any = {};

      if (filters) {
        if (filters.role) {
          filterConditions.role = { eq: filters.role };
        }
        if (filters.isAdmin !== undefined) {
          filterConditions.isAdmin = { eq: filters.isAdmin };
        }
        if (filters.isVendor !== undefined) {
          filterConditions.isVendor = { eq: filters.isVendor };
        }
        if (filters.isVerified !== undefined) {
          filterConditions.isVerified = { eq: filters.isVerified };
        }
        if (filters.dateFrom) {
          filterConditions.createdAt = { ge: filters.dateFrom };
        }
        if (filters.dateTo) {
          if (filterConditions.createdAt) {
            filterConditions.createdAt.le = filters.dateTo;
          } else {
            filterConditions.createdAt = { le: filters.dateTo };
          }
        }
      }

      const result = await adminClient.graphql({
        query: listUserProfiles,
        variables: {
          filter: Object.keys(filterConditions).length > 0 ? filterConditions : undefined,
          limit,
          nextToken
        }
      });

      let users = result.data?.listUserProfiles?.items || [];

      // Apply search term filter on client side if provided
      if (filters?.searchTerm) {
        const searchTerm = filters.searchTerm.toLowerCase();
        users = users.filter((user: any) =>
          user.firstName?.toLowerCase().includes(searchTerm) ||
          user.lastName?.toLowerCase().includes(searchTerm) ||
          user.email?.toLowerCase().includes(searchTerm) ||
          user.userId?.toLowerCase().includes(searchTerm)
        );
      }

      return {
        success: true,
        data: {
          users,
          nextToken: result.data?.listUserProfiles?.nextToken
        }
      };
    } catch (error) {
      console.error('Error fetching all users:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to fetch users'
      };
    }
  }

  /**
   * Get user by ID
   */
  static async getUserById(userId: string): Promise<ServiceResponse<UserProfile>> {
    try {
      const result = await client.graphql({
        query: getUserProfile,
        variables: { id: userId }
      });

      return {
        success: true,
        data: result.data?.getUserProfile
      };
    } catch (error) {
      console.error('Error fetching user by ID:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to fetch user'
      };
    }
  }

  /**
   * Update user profile
   */
  static async updateUser(userId: string, updateData: Partial<UserProfile>): Promise<ServiceResponse<UserProfile>> {
    try {
      const input = {
        id: userId,
        ...updateData
      };

      const result = await client.graphql({
        query: updateUserProfile,
        variables: { input }
      });

      return {
        success: true,
        data: result.data?.updateUserProfile
      };
    } catch (error) {
      console.error('Error updating user:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to update user'
      };
    }
  }

  /**
   * Delete user profile
   */
  static async deleteUser(userId: string): Promise<ServiceResponse<boolean>> {
    try {
      await client.graphql({
        query: deleteUserProfile,
        variables: { input: { id: userId } }
      });

      return {
        success: true,
        data: true
      };
    } catch (error) {
      console.error('Error deleting user:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to delete user'
      };
    }
  }

  /**
   * Get user statistics
   */
  static async getUserStatistics(): Promise<ServiceResponse<{
    totalUsers: number;
    totalCustomers: number;
    totalVendors: number;
    totalAdmins: number;
    recentUsers: number;
  }>> {
    try {
      const result = await adminClient.graphql({
        query: listUserProfiles,
        variables: { limit: 1000 }
      });

      const users = result.data?.listUserProfiles?.items || [];
      const now = new Date();
      const lastWeek = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);

      const stats = {
        totalUsers: users.length,
        totalCustomers: users.filter((u: any) => u.role === 'CUSTOMER').length,
        totalVendors: users.filter((u: any) => u.isVendor).length,
        totalAdmins: users.filter((u: any) => u.isAdmin || u.isSuperAdmin).length,
        recentUsers: users.filter((u: any) => new Date(u.createdAt) > lastWeek).length
      };

      return {
        success: true,
        data: stats
      };
    } catch (error) {
      console.error('Error fetching user statistics:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to fetch statistics'
      };
    }
  }

  /**
   * Check if user has specific permission
   */
  static async hasPermission(userId: string, permission: string): Promise<boolean> {
    try {
      const userProfile = await this.getUserProfile(userId);
      
      if (!userProfile) return false;
      
      // Super admins have all permissions
      if (userProfile.isSuperAdmin) return true;
      
      // Check if user has the specific permission
      return userProfile.permissions?.includes(permission) || false;
    } catch (error) {
      console.error('Error checking permission:', error);
      return false;
    }
  }
}

export default AdminUserService;
