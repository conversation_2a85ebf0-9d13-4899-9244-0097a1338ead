'use client';

import React, { useState, useEffect } from 'react';
import { X, Check, Star, CreditCard, Smartphone, Building, Wallet } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { pricingService, type PricingPlan } from '@/lib/services/pricingService';
import { useAuth } from '@/contexts/AuthContext';
import { showToast } from '@/lib/toast'
interface VendorPricingModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSkip?: () => void;
  vendorId: string;
  showSkipOption?: boolean;
  title?: string;
  subtitle?: string;
}

export default function VendorPricingModal({
  isOpen,
  onClose,
  onSkip,
  vendorId,
  showSkipOption = true,
  title = "Choose Your Plan",
  subtitle = "Select the perfect plan to grow your wedding business"
}: VendorPricingModalProps) {
  const [plans, setPlans] = useState<PricingPlan[]>([]);
  const [selectedPlan, setSelectedPlan] = useState<PricingPlan | null>(null);
  const [loading, setLoading] = useState(true);
  const [processing, setProcessing] = useState(false);
  const [paymentMethod, setPaymentMethod] = useState<'card' | 'upi' | 'netbanking' | 'wallet'>('card');
  const { user } = useAuth();

  useEffect(() => {
    if (isOpen) {
      loadPricingPlans();
    }
  }, [isOpen]);

  const loadPricingPlans = async () => {
    setLoading(true);
    try {
      const pricingPlans = await pricingService.getPricingPlans();
      setPlans(pricingPlans);
    } catch (error) {
      console.error('Error loading pricing plans:', error);
    } finally {
      setLoading(false);
    }
  };

  const handlePlanSelect = (plan: PricingPlan) => {
    setSelectedPlan(plan);
  };

  const handleSubscribe = async () => {
    if (!selectedPlan) return;

    setProcessing(true);
    try {
      const paymentData = {
        vendorId,
        planId: selectedPlan.id,
        amount: selectedPlan.price,
        currency: selectedPlan.currency,
        paymentMethod,
        billingAddress: {
          name: user?.name || '',
          address: '',
          city: '',
          state: '',
          pincode: '',
          country: 'India'
        }
      };

      const result = await pricingService.createSubscription(paymentData);
      
      if (result.success) {
        // Show success message and close modal
        showToast.success('Subscription created successfully!');
        onClose();
      } else {
        showToast.error(`Subscription failed: ${result.error}`);
      }
    } catch (error) {
      console.error('Error creating subscription:', error);
      showToast.error('An error occurred while processing your subscription.');
    } finally {
      setProcessing(false);
    }
  };

  const handleSkip = async () => {
    if (onSkip) {
      setProcessing(true);
      try {
        await pricingService.skipPricingDuringSignup(vendorId);
        onSkip();
      } catch (error) {
        console.error('Error skipping pricing:', error);
      } finally {
        setProcessing(false);
      }
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-6xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex justify-between items-center p-6 border-b">
          <div>
            <h2 className="text-2xl font-bold text-gray-900">{title}</h2>
            <p className="text-gray-600 mt-1">{subtitle}</p>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          {loading ? (
            <div className="flex justify-center items-center py-12">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
            </div>
          ) : (
            <>
              {/* Pricing Plans */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                {plans.map((plan) => (
                  <div
                    key={plan.id}
                    className={`relative border rounded-lg p-6 cursor-pointer transition-all ${
                      selectedPlan?.id === plan.id
                        ? 'border-primary bg-primary/5 shadow-lg'
                        : 'border-gray-200 hover:border-primary/50 hover:shadow-md'
                    } ${plan.isPopular ? 'ring-2 ring-primary' : ''}`}
                    onClick={() => handlePlanSelect(plan)}
                  >
                    {plan.isPopular && (
                      <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                        <span className="bg-primary text-white px-3 py-1 rounded-full text-sm font-medium flex items-center">
                          <Star className="h-3 w-3 mr-1" />
                          Most Popular
                        </span>
                      </div>
                    )}

                    <div className="text-center">
                      <h3 className="text-lg font-semibold text-gray-900 mb-2">{plan.name}</h3>
                      <div className="mb-4">
                        <span className="text-3xl font-bold text-gray-900">₹{plan.price.toLocaleString()}</span>
                        <span className="text-gray-600">/{plan.duration}</span>
                      </div>
                      
                      {plan.discount && (
                        <div className="bg-green-100 text-green-800 px-2 py-1 rounded text-sm mb-4">
                          {plan.discount.percentage}% OFF
                        </div>
                      )}

                      <p className="text-gray-600 text-sm mb-6">{plan.description}</p>

                      <ul className="space-y-2 text-left">
                        {plan.features.map((feature, index) => (
                          <li key={index} className="flex items-start">
                            <Check className="h-4 w-4 text-green-500 mt-0.5 mr-2 flex-shrink-0" />
                            <span className="text-sm text-gray-700">{feature}</span>
                          </li>
                        ))}
                      </ul>
                    </div>

                    {selectedPlan?.id === plan.id && (
                      <div className="absolute inset-0 border-2 border-primary rounded-lg pointer-events-none"></div>
                    )}
                  </div>
                ))}
              </div>

              {/* Payment Method Selection */}
              {selectedPlan && (
                <div className="bg-gray-50 rounded-lg p-6 mb-6">
                  <h3 className="text-lg font-semibold mb-4">Select Payment Method</h3>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    {[
                      { id: 'card', label: 'Credit/Debit Card', icon: CreditCard },
                      { id: 'upi', label: 'UPI', icon: Smartphone },
                      { id: 'netbanking', label: 'Net Banking', icon: Building },
                      { id: 'wallet', label: 'Wallet', icon: Wallet }
                    ].map((method) => (
                      <button
                        key={method.id}
                        onClick={() => setPaymentMethod(method.id as any)}
                        className={`flex flex-col items-center p-4 border rounded-lg transition-all ${
                          paymentMethod === method.id
                            ? 'border-primary bg-primary/5'
                            : 'border-gray-200 hover:border-primary/50'
                        }`}
                      >
                        <method.icon className="h-6 w-6 mb-2" />
                        <span className="text-sm font-medium">{method.label}</span>
                      </button>
                    ))}
                  </div>
                </div>
              )}

              {/* Action Buttons */}
              <div className="flex flex-col sm:flex-row gap-4 justify-between">
                <div className="flex gap-4">
                  {showSkipOption && (
                    <Button
                      variant="outline"
                      onClick={handleSkip}
                      disabled={processing}
                      className="px-8"
                    >
                      Skip for Now
                    </Button>
                  )}
                  <Button
                    variant="outline"
                    onClick={onClose}
                    disabled={processing}
                  >
                    Cancel
                  </Button>
                </div>

                <Button
                  onClick={handleSubscribe}
                  disabled={!selectedPlan || processing}
                  className="px-8"
                >
                  {processing ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Processing...
                    </>
                  ) : (
                    `Subscribe to ${selectedPlan?.name || 'Plan'}`
                  )}
                </Button>
              </div>

              {/* Terms */}
              <div className="mt-6 text-center text-sm text-gray-600">
                <p>
                  By subscribing, you agree to our{' '}
                  <a href="/terms" className="text-primary hover:underline">Terms of Service</a>
                  {' '}and{' '}
                  <a href="/privacy" className="text-primary hover:underline">Privacy Policy</a>
                </p>
              </div>
            </>
          )}
        </div>
      </div>
    </div>
  );
}
